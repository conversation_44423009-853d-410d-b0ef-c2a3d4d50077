#!/usr/bin/env python3
"""
E-UltraEnhanced实验结果分析脚本
对比分析E组超强化实验的效果
"""

import os
import re
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

class EUltraResultsAnalyzer:
    def __init__(self, logs_dir='/home/<USER>/hmt/ACGAN-FG-main/logs/'):
        self.logs_dir = logs_dir
        self.baseline_results = {
            'E_group_baseline': 47.6,  # E组基线准确率
            'D_group_enhanced': 66.0,  # D组Enhanced结果
        }
        
    def find_latest_e_log(self):
        """找到最新的E组日志文件"""
        e_logs = []
        for filename in os.listdir(self.logs_dir):
            if filename.startswith('E_') and filename.endswith('.log'):
                e_logs.append(filename)
        
        if not e_logs:
            print("❌ 未找到E组日志文件")
            return None
            
        # 按时间排序，返回最新的
        e_logs.sort(reverse=True)
        latest_log = e_logs[0]
        print(f"📄 找到最新E组日志: {latest_log}")
        return os.path.join(self.logs_dir, latest_log)
    
    def parse_accuracy_results(self, log_file):
        """解析准确率结果"""
        if not os.path.exists(log_file):
            print(f"❌ 日志文件不存在: {log_file}")
            return None
            
        print(f"🔍 分析日志文件: {log_file}")
        
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找最终准确率结果
        accuracy_pattern = r'LinearSVM:\s*([\d.]+)%.*?RandomForest:\s*([\d.]+)%.*?GaussianNB:\s*([\d.]+)%.*?MLPClassifier:\s*([\d.]+)%'
        
        matches = re.findall(accuracy_pattern, content, re.DOTALL)
        
        if not matches:
            print("❌ 未找到准确率结果")
            return None
        
        # 获取最后一次（最终）结果
        final_result = matches[-1]
        
        results = {
            'LinearSVM': float(final_result[0]),
            'RandomForest': float(final_result[1]), 
            'GaussianNB': float(final_result[2]),
            'MLPClassifier': float(final_result[3])
        }
        
        # 计算平均准确率
        results['Average'] = np.mean(list(results.values()))
        
        return results
    
    def analyze_training_progress(self, log_file):
        """分析训练过程"""
        if not os.path.exists(log_file):
            return None
            
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取训练过程中的准确率变化
        epoch_pattern = r'Epoch\s+(\d+).*?GaussianNB:\s*([\d.]+)%'
        epoch_matches = re.findall(epoch_pattern, content)
        
        if epoch_matches:
            epochs = [int(match[0]) for match in epoch_matches]
            accuracies = [float(match[1]) for match in epoch_matches]
            return epochs, accuracies
        
        return None, None
    
    def compare_with_baseline(self, results):
        """与基线对比"""
        if not results:
            return
            
        print("\n" + "="*60)
        print("📊 E-UltraEnhanced vs 基线对比")
        print("="*60)
        
        baseline = self.baseline_results['E_group_baseline']
        
        print(f"{'分类器':<15} {'基线':<10} {'UltraEnhanced':<15} {'提升':<10} {'状态'}")
        print("-" * 60)
        
        for classifier, accuracy in results.items():
            if classifier == 'Average':
                continue
                
            improvement = accuracy - baseline
            status = "✅ 成功" if improvement > 5 else "⚠️ 有限" if improvement > 0 else "❌ 下降"
            
            print(f"{classifier:<15} {baseline:<10.1f}% {accuracy:<15.1f}% {improvement:<10.1f}% {status}")
        
        # 平均结果
        avg_improvement = results['Average'] - baseline
        avg_status = "🎉 突破!" if results['Average'] > 60 else "✅ 改进" if avg_improvement > 5 else "⚠️ 有限"
        
        print("-" * 60)
        print(f"{'平均准确率':<15} {baseline:<10.1f}% {results['Average']:<15.1f}% {avg_improvement:<10.1f}% {avg_status}")
        
        # 目标达成情况
        print(f"\n🎯 目标达成情况:")
        print(f"   目标准确率: 60%")
        print(f"   实际准确率: {results['Average']:.1f}%")
        if results['Average'] >= 60:
            print(f"   ✅ 目标达成! 超出 {results['Average'] - 60:.1f}%")
        else:
            print(f"   ⚠️ 距离目标还差 {60 - results['Average']:.1f}%")
    
    def analyze_ultra_enhanced_effectiveness(self, results):
        """分析超强化策略的有效性"""
        if not results:
            return
            
        print("\n" + "="*60)
        print("🔬 E-UltraEnhanced策略有效性分析")
        print("="*60)
        
        print("🎯 超强化配置回顾:")
        print("   • Triplet权重: 50 → 100 (2倍)")
        print("   • Center权重: 2.5 → 5.0 (2倍)")
        print("   • 学习率: 0.0002 → 0.0003 (1.5倍)")
        print("   • CRL权重: 0.1 → 0.2 (2倍)")
        
        baseline = self.baseline_results['E_group_baseline']
        improvement = results['Average'] - baseline
        
        print(f"\n📈 整体效果评估:")
        if improvement > 15:
            print(f"   🎉 显著突破! 提升{improvement:.1f}%")
            print("   ✅ 超强化策略非常有效")
        elif improvement > 10:
            print(f"   ✅ 明显改进! 提升{improvement:.1f}%")
            print("   ✅ 超强化策略有效")
        elif improvement > 5:
            print(f"   ⚠️ 适度改进，提升{improvement:.1f}%")
            print("   ⚠️ 超强化策略部分有效")
        else:
            print(f"   ❌ 改进有限，仅提升{improvement:.1f}%")
            print("   ❌ 需要考虑其他策略")
    
    def generate_recommendations(self, results):
        """生成后续建议"""
        if not results:
            return
            
        print("\n" + "="*60)
        print("💡 后续改进建议")
        print("="*60)
        
        baseline = self.baseline_results['E_group_baseline']
        improvement = results['Average'] - baseline
        
        if results['Average'] >= 60:
            print("🎉 恭喜! E组准确率已突破60%目标")
            print("\n📋 下一步行动:")
            print("1. ✅ 在其他组别测试相同配置")
            print("2. ✅ 验证方法的泛化能力") 
            print("3. ✅ 准备论文实验数据")
            print("4. ✅ 分析技术贡献点")
            
        elif improvement > 10:
            print("✅ 显著改进，但未达到60%目标")
            print("\n📋 进一步优化建议:")
            print("1. 🔧 尝试三阶段训练策略")
            print("2. 🔧 考虑独立特征学习架构")
            print("3. 🔧 增加数据增强方法")
            print("4. 🔧 调整网络架构深度")
            
        else:
            print("⚠️ 改进有限，需要更根本的策略")
            print("\n📋 根本性改进建议:")
            print("1. 🔄 重新设计损失函数")
            print("2. 🔄 考虑注意力机制")
            print("3. 🔄 尝试对比学习方法")
            print("4. 🔄 探索元学习策略")
    
    def run_analysis(self):
        """运行完整分析"""
        print("🚀 E-UltraEnhanced结果分析开始...")
        
        # 找到最新日志
        log_file = self.find_latest_e_log()
        if not log_file:
            return
        
        # 解析结果
        results = self.parse_accuracy_results(log_file)
        if not results:
            print("❌ 无法解析准确率结果")
            return
        
        # 显示基本结果
        print(f"\n📊 E-UltraEnhanced最终结果:")
        print("-" * 40)
        for classifier, accuracy in results.items():
            print(f"{classifier:<15}: {accuracy:.2f}%")
        
        # 各种分析
        self.compare_with_baseline(results)
        self.analyze_ultra_enhanced_effectiveness(results)
        self.generate_recommendations(results)
        
        print(f"\n✅ E-UltraEnhanced分析完成!")
        return results

def main():
    """主函数"""
    analyzer = EUltraResultsAnalyzer()
    results = analyzer.run_analysis()
    
    if results and results['Average'] >= 60:
        print(f"\n🎉 重大突破! E组准确率达到{results['Average']:.1f}%")
        print("🏆 E-UltraEnhanced策略成功!")
    elif results:
        print(f"\n📈 有所改进，E组准确率达到{results['Average']:.1f}%")
        print("🔧 建议继续优化策略")

if __name__ == "__main__":
    main()

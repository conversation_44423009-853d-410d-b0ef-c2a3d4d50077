#!/usr/bin/env python3
"""
修复损坏的JSON文件
从CSV文件重新生成JSON文件
"""

import pandas as pd
import json
import ast
from hyperparameter_search import <PERSON>umpyEncoder

def fix_json_file():
    """从CSV文件重新生成JSON文件"""
    print("🔧 修复损坏的JSON文件")
    
    # 读取CSV文件
    csv_path = 'hyperparameter_search_results/lambda_ar_search_split_A.csv'
    json_path = 'hyperparameter_search_results/lambda_ar_search_split_A.json'
    
    try:
        df = pd.read_csv(csv_path)
        print(f"✅ 成功读取CSV文件: {len(df)}条记录")
        
        # 转换为JSON格式
        results = []
        for _, row in df.iterrows():
            # 解析parameters字符串为字典
            parameters = ast.literal_eval(row['parameters'])
            
            result = {
                'experiment_name': row['experiment_name'],
                'stage': int(row['stage']),
                'split_name': row['split_name'],
                'parameters': parameters,
                'best_accuracy': float(row['best_accuracy']),
                'best_epoch': int(row['best_epoch']),
                'final_accuracy': float(row['final_accuracy']),
                'avg_fid': float(row['avg_fid']),
                'avg_mmd': float(row['avg_mmd']),
                'training_time_minutes': float(row['training_time_minutes']),
                'total_epochs': int(row['total_epochs']),
                'early_stopped': bool(row['early_stopped']),
                'timestamp': row['timestamp']
            }
            results.append(result)
        
        # 保存为JSON文件
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)
        
        print(f"✅ 成功重新生成JSON文件: {json_path}")
        
        # 验证JSON文件
        with open(json_path, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        print(f"✅ JSON文件验证成功: {len(loaded_data)}条记录")
        
        # 显示最佳结果
        best_result = max(loaded_data, key=lambda x: x['best_accuracy'])
        print(f"\n🎯 最佳结果:")
        print(f"   Lambda_AR: {best_result['parameters']['lambda_ar']}")
        print(f"   最佳准确率: {best_result['best_accuracy']:.4f}")
        print(f"   实验名称: {best_result['experiment_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

if __name__ == "__main__":
    fix_json_file()

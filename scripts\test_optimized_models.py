#!/usr/bin/env python3
"""
快速测试优化版模型
检查GPU访问、数据流水线和混合精度是否正常工作
"""

import os
import sys
import time
import numpy as np

def test_imports():
    """测试所有必要的模块导入"""
    print("🔍 测试模块导入...")
    try:
        import tensorflow as tf
        import read_data
        from data_pipeline import OptimizedDataPipeline, monitor_gpu, check_docker_gpu_config
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_gpu_access():
    """测试GPU访问"""
    print("\n🔍 测试GPU访问...")
    try:
        import tensorflow as tf
        
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✅ 检测到 {len(gpus)} 个GPU设备")
            for i, gpu in enumerate(gpus):
                print(f"   GPU {i}: {gpu}")
            return True
        else:
            print("⚠️  未检测到GPU设备，将使用CPU运行")
            return False
    except Exception as e:
        print(f"❌ GPU检测失败: {e}")
        return False

def test_data_pipeline():
    """测试数据流水线"""
    print("\n🔍 测试数据流水线...")
    try:
        from data_pipeline import OptimizedDataPipeline
        
        # 创建数据流水线实例
        pipeline = OptimizedDataPipeline(use_mixed_precision=True)
        print("✅ 数据流水线创建成功")
        
        # 测试数据加载
        import read_data
        train_data, test_data = read_data.loadData()
        print("✅ 数据加载成功")
        
        return True
    except Exception as e:
        print(f"❌ 数据流水线测试失败: {e}")
        return False

def test_small_training():
    """测试小规模训练"""
    print("\n🔍 测试小规模训练...")
    try:
        import tensorflow as tf
        from data_pipeline import OptimizedDataPipeline
        import read_data
        
        # 加载数据
        train_data, test_data = read_data.loadData()
        
        # 创建数据流水线
        pipeline = OptimizedDataPipeline(use_mixed_precision=True)
        
        # 准备小批量数据 (Group E)
        test_class_indices = [9, 13, 15]
        data_info = pipeline.prepare_data_triplet(
            train_data, test_data, test_class_indices, 
            batch_size=64, shuffle_buffer=1000
        )
        
        print("✅ 小规模数据准备成功")
        print(f"   训练样本数: {len(data_info['all_train_X'])}")
        print(f"   测试样本数: {len(data_info['test_X'])}")
        print(f"   批处理大小: {data_info['batch_size']}")
        
        # 测试一个训练批次
        train_dataset = data_info['train_dataset']
        sample_batch = next(iter(train_dataset))
        print(f"✅ 数据批次形状: {sample_batch[0].shape}")
        
        return True
    except Exception as e:
        print(f"❌ 小规模训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始优化版模型测试...")
    print("="*50)
    
    results = []
    
    # 测试导入
    results.append(("模块导入", test_imports()))
    
    # 测试GPU
    results.append(("GPU访问", test_gpu_access()))
    
    # 测试数据流水线
    results.append(("数据流水线", test_data_pipeline()))
    
    # 测试小规模训练
    results.append(("小规模训练", test_small_training()))
    
    # 打印结果
    print("\n📊 测试结果汇总:")
    print("="*50)
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if not passed:
            all_passed = False
    
    print("="*50)
    if all_passed:
        print("🎉 所有测试通过！优化版模型可以正常运行")
        print("\n💡 建议执行:")
        print("   docker exec -it acgan-container bash")
        print("   python acgan_triplet_fixed_optimized.py")
    else:
        print("⚠️  部分测试失败，请检查配置")
        print("\n💡 建议:")
        print("   1. 重启容器: ./restart_container_with_gpu.sh")
        print("   2. 检查GPU驱动")
        print("   3. 检查Docker GPU运行时")

if __name__ == "__main__":
    main() 
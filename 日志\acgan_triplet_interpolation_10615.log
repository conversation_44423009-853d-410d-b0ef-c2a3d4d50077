2025-07-10 15:27:23.436839: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:485] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered
2025-07-10 15:27:23.467232: E external/local_xla/xla/stream_executor/cuda/cuda_dnn.cc:8473] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
2025-07-10 15:27:23.478648: E external/local_xla/xla/stream_executor/cuda/cuda_blas.cc:1471] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered
2025-07-10 15:27:23.533632: I tensorflow/core/platform/cpu_feature_guard.cc:211] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX, in other operations, rebuild TensorFlow with the appropriate compiler flags.
/usr/local/lib/python3.12/dist-packages/tensorflow_addons/utils/tfa_eol_msg.py:23: UserWarning: 

TensorFlow Addons (TFA) has ended development and introduction of new features.
TFA has entered a minimal maintenance and release mode until a planned end of life in May 2024.
Please modify downstream libraries to take dependencies from other repositories in our TensorFlow community (e.g. Keras, Keras-CV, and Keras-NLP). 

For more information see: https://github.com/tensorflow/addons/issues/2807 

  warnings.warn(
/usr/local/lib/python3.12/dist-packages/tensorflow_addons/utils/ensure_tf_install.py:53: UserWarning: Tensorflow Addons supports using Python ops for all Tensorflow versions above or equal to 2.12.0 and strictly below 2.15.0 (nightly versions are not supported). 
 The versions of TensorFlow you are currently using is 2.17.0 and is not supported. 
Some things might work, some things might not.
If you were to encounter a bug, do not file an issue.
If you want to make sure you're using a tested and supported configuration, either change the TensorFlow version or the TensorFlow Addons's version. 
You can find the compatibility matrix in TensorFlow Addon's readme:
https://github.com/tensorflow/addons
  warnings.warn(
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1752161245.573127 1728792 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1752161245.686866 1728792 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1752161245.687999 1728792 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1752161245.690239 1728792 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1752161245.691180 1728792 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1752161245.692091 1728792 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1752161245.772304 1728792 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1752161245.773526 1728792 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1752161245.774438 1728792 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
2025-07-10 15:27:25.775315: I tensorflow/core/common_runtime/gpu/gpu_device.cc:2021] Created device /job:localhost/replica:0/task:0/device:GPU:0 with 3731 MB memory:  -> device: 0, name: NVIDIA GeForce RTX 5080, pci bus id: 0000:02:00.0, compute capability: 12.0
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1752161247.256327 1728792 service.cc:146] XLA service 0x4a590370 initialized for platform CUDA (this does not guarantee that XLA will be used). Devices:
I0000 00:00:1752161247.256382 1728792 service.cc:154]   StreamExecutor device (0): NVIDIA GeForce RTX 5080, Compute Capability 12.0
2025-07-10 15:27:27.261044: I tensorflow/compiler/mlir/tensorflow/utils/dump_mlir_util.cc:268] disabling MLIR crash reproducer, set env var `MLIR_CRASH_REPRODUCER_DIRECTORY` to enable.
2025-07-10 15:27:27.301395: I external/local_xla/xla/stream_executor/cuda/cuda_dnn.cc:531] Loaded cuDNN version 90701
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
I0000 00:00:1752161247.343121 1728792 device_compiler.h:188] Compiled cluster using XLA!  This line is logged at most once for the lifetime of the process.
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
WARNING:tensorflow:5 out of the last 5 calls to <function _BaseOptimizer._update_step_xla at 0x764067eacc20> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
WARNING:tensorflow:6 out of the last 6 calls to <function _BaseOptimizer._update_step_xla at 0x764067eacc20> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
loading data...
🔍 验证测试类别: [1, 6, 14]
test classes: [1, 6, 14]
train classes: [0, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13]
1 Physical GPUs, 1 Logical GPU
开始运行 Group A 实验（特征空间插值版本），测试类别: [1, 6, 14]
训练开始，日志将被记录到: 结果/202507102327_triplet_interpolation_GroupA.md
Group A (Easy): 禁用特征插值
[Epoch 0/2000][Batch 0/22][AE+C loss: 0.944755][M loss: 0.111382][D loss: 2.168064][G loss 14.507593 ]time: 0:00:04.353700 
[Epoch 0/2000][Batch 1/22][AE+C loss: 0.915088][M loss: 0.032744][D loss: 2.143824][G loss 12.020856 ]time: 0:00:04.517011 
[Epoch 0/2000][Batch 2/22][AE+C loss: 0.915675][M loss: 0.123743][D loss: 2.053362][G loss 10.102931 ]time: 0:00:04.659363 
[Epoch 0/2000][Batch 3/22][AE+C loss: 0.909134][M loss: 0.139109][D loss: 2.035223][G loss 9.818686 ]time: 0:00:04.808532 
[Epoch 0/2000][Batch 4/22][AE+C loss: 0.902350][M loss: 0.117851][D loss: 2.000382][G loss 9.912823 ]time: 0:00:04.953243 
[Epoch 0/2000][Batch 5/22][AE+C loss: 0.897705][M loss: 0.132937][D loss: 1.983348][G loss 10.083869 ]time: 0:00:05.098581 
[Epoch 0/2000][Batch 6/22][AE+C loss: 0.889093][M loss: 0.157834][D loss: 1.972931][G loss 10.844965 ]time: 0:00:05.240328 
[Epoch 0/2000][Batch 7/22][AE+C loss: 0.892624][M loss: 0.188809][D loss: 1.980721][G loss 12.356085 ]time: 0:00:05.381955 
[Epoch 0/2000][Batch 8/22][AE+C loss: 0.878582][M loss: 0.220330][D loss: 1.998802][G loss 15.176676 ]time: 0:00:05.535508 
[Epoch 0/2000][Batch 9/22][AE+C loss: 0.878358][M loss: 0.302633][D loss: 2.019488][G loss 14.138743 ]time: 0:00:05.691723 
[Epoch 0/2000][Batch 10/22][AE+C loss: 0.894673][M loss: 0.336574][D loss: 2.030182][G loss 13.368603 ]time: 0:00:05.843731 
[Epoch 0/2000][Batch 11/22][AE+C loss: 0.872044][M loss: 0.192163][D loss: 2.009219][G loss 11.004483 ]time: 0:00:05.995777 
[Epoch 0/2000][Batch 12/22][AE+C loss: 0.859786][M loss: 0.136097][D loss: 1.998566][G loss 10.389641 ]time: 0:00:06.149236 
[Epoch 0/2000][Batch 13/22][AE+C loss: 0.846475][M loss: 0.130998][D loss: 2.001000][G loss 10.519874 ]time: 0:00:06.302826 
[Epoch 0/2000][Batch 14/22][AE+C loss: 0.841208][M loss: 0.140028][D loss: 1.999160][G loss 10.841141 ]time: 0:00:06.443110 
[Epoch 0/2000][Batch 15/22][AE+C loss: 0.834114][M loss: 0.147119][D loss: 1.964724][G loss 10.373363 ]time: 0:00:06.593725 
[Epoch 0/2000][Batch 16/22][AE+C loss: 0.828876][M loss: 0.161441][D loss: 1.957500][G loss 10.678611 ]time: 0:00:06.737483 
[Epoch 0/2000][Batch 17/22][AE+C loss: 0.852727][M loss: 0.289516][D loss: 1.932779][G loss 12.627343 ]time: 0:00:06.884257 
[Epoch 0/2000][Batch 18/22][AE+C loss: 0.849072][M loss: 0.270500][D loss: 1.938019][G loss 14.083818 ]time: 0:00:07.036186 
[Epoch 0/2000][Batch 19/22][AE+C loss: 0.865689][M loss: 0.212486][D loss: 1.997609][G loss 18.265720 ]time: 0:00:07.182380 
[Epoch 0/2000][Batch 20/22][AE+C loss: 0.829724][M loss: 0.223719][D loss: 1.981525][G loss 13.999292 ]time: 0:00:07.327016 
[Epoch 0/2000][Batch 21/22][AE+C loss: 0.828210][M loss: 0.133892][D loss: 1.949882][G loss 9.006807 ]time: 0:00:07.474969 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 8s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
37/63 [================>.............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 0/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.164583] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.343056]
[Epoch 1/2000][Batch 0/22][AE+C loss: 0.814905][M loss: 0.091653][D loss: 1.969108][G loss 8.921157 ]time: 0:00:16.465567 
[Epoch 1/2000][Batch 1/22][AE+C loss: 0.803253][M loss: 0.026554][D loss: 1.960184][G loss 9.026594 ]time: 0:00:16.608465 
[Epoch 1/2000][Batch 2/22][AE+C loss: 0.785017][M loss: 0.135534][D loss: 1.927448][G loss 8.936981 ]time: 0:00:16.857643 
[Epoch 1/2000][Batch 3/22][AE+C loss: 0.780626][M loss: 0.131261][D loss: 1.905899][G loss 9.219165 ]time: 0:00:17.011144 
[Epoch 1/2000][Batch 4/22][AE+C loss: 0.776341][M loss: 0.114832][D loss: 1.862535][G loss 9.064679 ]time: 0:00:17.156908 
[Epoch 1/2000][Batch 5/22][AE+C loss: 0.772692][M loss: 0.135951][D loss: 1.862525][G loss 8.933770 ]time: 0:00:17.305570 
[Epoch 1/2000][Batch 6/22][AE+C loss: 0.762652][M loss: 0.147275][D loss: 1.865588][G loss 8.755336 ]time: 0:00:17.458428 
[Epoch 1/2000][Batch 7/22][AE+C loss: 0.777110][M loss: 0.183793][D loss: 1.879657][G loss 10.224986 ]time: 0:00:17.605498 
[Epoch 1/2000][Batch 8/22][AE+C loss: 0.776608][M loss: 0.183173][D loss: 1.899497][G loss 11.572410 ]time: 0:00:17.752431 
[Epoch 1/2000][Batch 9/22][AE+C loss: 0.787499][M loss: 0.233282][D loss: 1.870768][G loss 12.125778 ]time: 0:00:17.893128 
[Epoch 1/2000][Batch 10/22][AE+C loss: 0.813028][M loss: 0.277493][D loss: 1.849937][G loss 12.260902 ]time: 0:00:18.037801 
[Epoch 1/2000][Batch 11/22][AE+C loss: 0.780034][M loss: 0.168853][D loss: 1.872410][G loss 10.414907 ]time: 0:00:18.183786 
[Epoch 1/2000][Batch 12/22][AE+C loss: 0.766751][M loss: 0.125088][D loss: 1.873174][G loss 9.666910 ]time: 0:00:18.327723 
[Epoch 1/2000][Batch 13/22][AE+C loss: 0.755687][M loss: 0.133760][D loss: 1.877747][G loss 9.993882 ]time: 0:00:18.468759 
[Epoch 1/2000][Batch 14/22][AE+C loss: 0.753518][M loss: 0.134241][D loss: 1.867633][G loss 9.741701 ]time: 0:00:18.608080 
[Epoch 1/2000][Batch 15/22][AE+C loss: 0.740477][M loss: 0.169417][D loss: 1.843225][G loss 10.247970 ]time: 0:00:18.751379 
[Epoch 1/2000][Batch 16/22][AE+C loss: 0.738796][M loss: 0.169656][D loss: 1.830656][G loss 9.981666 ]time: 0:00:18.891219 
[Epoch 1/2000][Batch 17/22][AE+C loss: 0.765194][M loss: 0.233288][D loss: 1.795705][G loss 10.477425 ]time: 0:00:19.030433 
[Epoch 1/2000][Batch 18/22][AE+C loss: 0.766546][M loss: 0.254392][D loss: 1.832294][G loss 11.436011 ]time: 0:00:19.178958 
[Epoch 1/2000][Batch 19/22][AE+C loss: 0.795897][M loss: 0.169999][D loss: 1.996454][G loss 12.504114 ]time: 0:00:19.326823 
[Epoch 1/2000][Batch 20/22][AE+C loss: 0.762683][M loss: 0.203717][D loss: 1.950384][G loss 10.597273 ]time: 0:00:19.470831 
[Epoch 1/2000][Batch 21/22][AE+C loss: 0.765003][M loss: 0.129161][D loss: 1.872970][G loss 8.825398 ]time: 0:00:19.618658 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
37/63 [================>.............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
37/63 [================>.............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
42/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 1/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.346875] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.400000]
[Epoch 2/2000][Batch 0/22][AE+C loss: 0.754359][M loss: 0.081912][D loss: 1.807594][G loss 8.928923 ]time: 0:00:28.199630 
[Epoch 2/2000][Batch 1/22][AE+C loss: 0.744928][M loss: 0.026880][D loss: 1.805175][G loss 9.454918 ]time: 0:00:28.343753 
[Epoch 2/2000][Batch 2/22][AE+C loss: 0.714808][M loss: 0.130764][D loss: 1.809857][G loss 9.500122 ]time: 0:00:28.487344 
[Epoch 2/2000][Batch 3/22][AE+C loss: 0.711727][M loss: 0.131431][D loss: 1.779003][G loss 9.645352 ]time: 0:00:28.624001 
[Epoch 2/2000][Batch 4/22][AE+C loss: 0.706374][M loss: 0.120279][D loss: 1.694595][G loss 9.410972 ]time: 0:00:28.769117 
[Epoch 2/2000][Batch 5/22][AE+C loss: 0.701580][M loss: 0.131476][D loss: 1.706313][G loss 8.788713 ]time: 0:00:28.909412 
[Epoch 2/2000][Batch 6/22][AE+C loss: 0.686606][M loss: 0.150438][D loss: 1.720523][G loss 8.824295 ]time: 0:00:29.054640 
[Epoch 2/2000][Batch 7/22][AE+C loss: 0.713208][M loss: 0.163148][D loss: 1.731229][G loss 8.936898 ]time: 0:00:29.195699 
[Epoch 2/2000][Batch 8/22][AE+C loss: 0.723815][M loss: 0.148543][D loss: 1.750493][G loss 9.141376 ]time: 0:00:29.338288 
[Epoch 2/2000][Batch 9/22][AE+C loss: 0.753199][M loss: 0.241448][D loss: 1.688275][G loss 11.193531 ]time: 0:00:29.480701 
[Epoch 2/2000][Batch 10/22][AE+C loss: 0.790780][M loss: 0.276974][D loss: 1.642005][G loss 12.311699 ]time: 0:00:29.623020 
[Epoch 2/2000][Batch 11/22][AE+C loss: 0.742214][M loss: 0.166860][D loss: 1.721415][G loss 10.164822 ]time: 0:00:29.770654 
[Epoch 2/2000][Batch 12/22][AE+C loss: 0.724044][M loss: 0.136464][D loss: 1.739348][G loss 9.583385 ]time: 0:00:29.911377 
[Epoch 2/2000][Batch 13/22][AE+C loss: 0.712602][M loss: 0.139645][D loss: 1.720063][G loss 9.531473 ]time: 0:00:30.057086 
[Epoch 2/2000][Batch 14/22][AE+C loss: 0.709423][M loss: 0.129922][D loss: 1.720762][G loss 9.476712 ]time: 0:00:30.200617 
[Epoch 2/2000][Batch 15/22][AE+C loss: 0.689642][M loss: 0.157867][D loss: 1.667339][G loss 9.326455 ]time: 0:00:30.344898 
[Epoch 2/2000][Batch 16/22][AE+C loss: 0.687767][M loss: 0.164646][D loss: 1.650606][G loss 9.237354 ]time: 0:00:30.484180 
[Epoch 2/2000][Batch 17/22][AE+C loss: 0.706925][M loss: 0.237380][D loss: 1.592770][G loss 9.421010 ]time: 0:00:30.625219 
[Epoch 2/2000][Batch 18/22][AE+C loss: 0.706471][M loss: 0.233079][D loss: 1.672811][G loss 9.764295 ]time: 0:00:30.771485 
[Epoch 2/2000][Batch 19/22][AE+C loss: 0.735443][M loss: 0.159824][D loss: 1.978631][G loss 9.332179 ]time: 0:00:30.916663 
[Epoch 2/2000][Batch 20/22][AE+C loss: 0.714766][M loss: 0.213754][D loss: 1.907257][G loss 8.711448 ]time: 0:00:31.065635 
[Epoch 2/2000][Batch 21/22][AE+C loss: 0.724424][M loss: 0.135990][D loss: 1.781420][G loss 8.598892 ]time: 0:00:31.218052 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
51/63 [=======================>......] - ETA: 0s
63/63 [==============================] - 0s 993us/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
34/63 [===============>..............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 2/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.400000]
[Epoch 3/2000][Batch 0/22][AE+C loss: 0.714733][M loss: 0.095303][D loss: 1.621998][G loss 8.573694 ]time: 0:00:39.923937 
[Epoch 3/2000][Batch 1/22][AE+C loss: 0.707557][M loss: 0.023552][D loss: 1.620270][G loss 8.044268 ]time: 0:00:40.070380 
[Epoch 3/2000][Batch 2/22][AE+C loss: 0.666289][M loss: 0.133864][D loss: 1.661170][G loss 9.002381 ]time: 0:00:40.217811 
[Epoch 3/2000][Batch 3/22][AE+C loss: 0.662086][M loss: 0.125732][D loss: 1.599539][G loss 8.615985 ]time: 0:00:40.366975 
[Epoch 3/2000][Batch 4/22][AE+C loss: 0.652522][M loss: 0.123212][D loss: 1.422942][G loss 8.597521 ]time: 0:00:40.515351 
[Epoch 3/2000][Batch 5/22][AE+C loss: 0.642394][M loss: 0.137362][D loss: 1.443473][G loss 8.406658 ]time: 0:00:40.664401 
[Epoch 3/2000][Batch 6/22][AE+C loss: 0.616956][M loss: 0.169906][D loss: 1.480737][G loss 8.356489 ]time: 0:00:40.811787 
[Epoch 3/2000][Batch 7/22][AE+C loss: 0.654960][M loss: 0.152716][D loss: 1.518659][G loss 8.731290 ]time: 0:00:40.955175 
[Epoch 3/2000][Batch 8/22][AE+C loss: 0.674487][M loss: 0.142029][D loss: 1.558035][G loss 9.220755 ]time: 0:00:41.103141 
[Epoch 3/2000][Batch 9/22][AE+C loss: 0.727704][M loss: 0.214471][D loss: 1.476742][G loss 10.552914 ]time: 0:00:41.251968 
[Epoch 3/2000][Batch 10/22][AE+C loss: 0.776541][M loss: 0.263182][D loss: 1.432733][G loss 12.556621 ]time: 0:00:41.407390 
[Epoch 3/2000][Batch 11/22][AE+C loss: 0.708158][M loss: 0.161928][D loss: 1.559562][G loss 9.609611 ]time: 0:00:41.559142 
[Epoch 3/2000][Batch 12/22][AE+C loss: 0.683322][M loss: 0.134696][D loss: 1.586553][G loss 9.054378 ]time: 0:00:41.705513 
[Epoch 3/2000][Batch 13/22][AE+C loss: 0.669724][M loss: 0.127848][D loss: 1.557314][G loss 9.036795 ]time: 0:00:41.848519 
[Epoch 3/2000][Batch 14/22][AE+C loss: 0.664949][M loss: 0.124063][D loss: 1.555667][G loss 9.072337 ]time: 0:00:41.987995 
[Epoch 3/2000][Batch 15/22][AE+C loss: 0.634906][M loss: 0.160394][D loss: 1.454691][G loss 8.692912 ]time: 0:00:42.139543 
[Epoch 3/2000][Batch 16/22][AE+C loss: 0.631468][M loss: 0.188077][D loss: 1.436208][G loss 8.841000 ]time: 0:00:42.291363 
[Epoch 3/2000][Batch 17/22][AE+C loss: 0.641749][M loss: 0.237642][D loss: 1.327491][G loss 8.876919 ]time: 0:00:42.440637 
[Epoch 3/2000][Batch 18/22][AE+C loss: 0.641008][M loss: 0.250304][D loss: 1.459627][G loss 9.446071 ]time: 0:00:42.589895 
[Epoch 3/2000][Batch 19/22][AE+C loss: 0.671917][M loss: 0.151537][D loss: 1.954359][G loss 9.876368 ]time: 0:00:42.739363 
[Epoch 3/2000][Batch 20/22][AE+C loss: 0.663652][M loss: 0.203078][D loss: 1.834739][G loss 8.909821 ]time: 0:00:42.885344 
[Epoch 3/2000][Batch 21/22][AE+C loss: 0.680139][M loss: 0.122782][D loss: 1.648320][G loss 8.314872 ]time: 0:00:43.025607 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
50/63 [======================>.......] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
46/63 [====================>.........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 3/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.400000]
[Epoch 4/2000][Batch 0/22][AE+C loss: 0.684236][M loss: 0.085540][D loss: 1.399945][G loss 7.718870 ]time: 0:00:51.471088 
[Epoch 4/2000][Batch 1/22][AE+C loss: 0.680840][M loss: 0.019839][D loss: 1.405406][G loss 7.635961 ]time: 0:00:51.616654 
[Epoch 4/2000][Batch 2/22][AE+C loss: 0.608623][M loss: 0.141278][D loss: 1.480469][G loss 8.401745 ]time: 0:00:51.767650 
[Epoch 4/2000][Batch 3/22][AE+C loss: 0.601997][M loss: 0.125094][D loss: 1.357253][G loss 8.322923 ]time: 0:00:51.913299 
[Epoch 4/2000][Batch 4/22][AE+C loss: 0.588623][M loss: 0.107393][D loss: 1.050028][G loss 7.863255 ]time: 0:00:52.058816 
[Epoch 4/2000][Batch 5/22][AE+C loss: 0.572178][M loss: 0.121002][D loss: 1.088305][G loss 7.599102 ]time: 0:00:52.203529 
[Epoch 4/2000][Batch 6/22][AE+C loss: 0.536228][M loss: 0.144091][D loss: 1.147767][G loss 7.503970 ]time: 0:00:52.345974 
[Epoch 4/2000][Batch 7/22][AE+C loss: 0.590504][M loss: 0.147700][D loss: 1.216195][G loss 8.410310 ]time: 0:00:52.488697 
[Epoch 4/2000][Batch 8/22][AE+C loss: 0.626510][M loss: 0.129773][D loss: 1.298825][G loss 9.097748 ]time: 0:00:52.630386 
[Epoch 4/2000][Batch 9/22][AE+C loss: 0.708220][M loss: 0.221507][D loss: 1.199848][G loss 10.694537 ]time: 0:00:52.781412 
[Epoch 4/2000][Batch 10/22][AE+C loss: 0.772929][M loss: 0.267604][D loss: 1.153809][G loss 12.079301 ]time: 0:00:52.920716 
[Epoch 4/2000][Batch 11/22][AE+C loss: 0.676812][M loss: 0.164272][D loss: 1.334161][G loss 9.321786 ]time: 0:00:53.062264 
[Epoch 4/2000][Batch 12/22][AE+C loss: 0.642775][M loss: 0.120231][D loss: 1.387622][G loss 8.829659 ]time: 0:00:53.204091 
[Epoch 4/2000][Batch 13/22][AE+C loss: 0.622625][M loss: 0.139289][D loss: 1.314379][G loss 9.217049 ]time: 0:00:53.346618 
[Epoch 4/2000][Batch 14/22][AE+C loss: 0.615514][M loss: 0.152804][D loss: 1.291241][G loss 9.093922 ]time: 0:00:53.496649 
[Epoch 4/2000][Batch 15/22][AE+C loss: 0.577773][M loss: 0.154133][D loss: 1.159604][G loss 8.718040 ]time: 0:00:53.642088 
[Epoch 4/2000][Batch 16/22][AE+C loss: 0.572463][M loss: 0.165345][D loss: 1.132235][G loss 8.818539 ]time: 0:00:53.786982 
[Epoch 4/2000][Batch 17/22][AE+C loss: 0.575620][M loss: 0.235789][D loss: 0.985581][G loss 8.373872 ]time: 0:00:53.930429 
[Epoch 4/2000][Batch 18/22][AE+C loss: 0.575733][M loss: 0.231037][D loss: 1.201683][G loss 8.584877 ]time: 0:00:54.073995 
[Epoch 4/2000][Batch 19/22][AE+C loss: 0.614254][M loss: 0.132101][D loss: 1.917898][G loss 8.508615 ]time: 0:00:54.209749 
[Epoch 4/2000][Batch 20/22][AE+C loss: 0.611508][M loss: 0.194145][D loss: 1.761026][G loss 8.710218 ]time: 0:00:54.355647 
[Epoch 4/2000][Batch 21/22][AE+C loss: 0.638453][M loss: 0.132929][D loss: 1.483824][G loss 8.365916 ]time: 0:00:54.503057 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
47/63 [=====================>........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
45/63 [====================>.........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 4/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.400000]
[Epoch 5/2000][Batch 0/22][AE+C loss: 0.647961][M loss: 0.101473][D loss: 1.078191][G loss 8.311786 ]time: 0:01:03.497159 
[Epoch 5/2000][Batch 1/22][AE+C loss: 0.642818][M loss: 0.020754][D loss: 1.108232][G loss 7.816670 ]time: 0:01:03.637988 
[Epoch 5/2000][Batch 2/22][AE+C loss: 0.549482][M loss: 0.125508][D loss: 1.266161][G loss 8.171423 ]time: 0:01:03.784896 
[Epoch 5/2000][Batch 3/22][AE+C loss: 0.541380][M loss: 0.129318][D loss: 1.134114][G loss 7.953902 ]time: 0:01:03.927982 
[Epoch 5/2000][Batch 4/22][AE+C loss: 0.521928][M loss: 0.122661][D loss: 0.781798][G loss 7.775075 ]time: 0:01:04.074874 
[Epoch 5/2000][Batch 5/22][AE+C loss: 0.500426][M loss: 0.111522][D loss: 0.801673][G loss 7.511783 ]time: 0:01:04.221017 
[Epoch 5/2000][Batch 6/22][AE+C loss: 0.455188][M loss: 0.133614][D loss: 0.824218][G loss 7.344158 ]time: 0:01:04.368597 
[Epoch 5/2000][Batch 7/22][AE+C loss: 0.521451][M loss: 0.139158][D loss: 0.917917][G loss 7.831546 ]time: 0:01:04.518190 
[Epoch 5/2000][Batch 8/22][AE+C loss: 0.572136][M loss: 0.121925][D loss: 1.038453][G loss 8.981977 ]time: 0:01:04.658890 
[Epoch 5/2000][Batch 9/22][AE+C loss: 0.686151][M loss: 0.203335][D loss: 0.975625][G loss 10.974690 ]time: 0:01:04.806009 
[Epoch 5/2000][Batch 10/22][AE+C loss: 0.772104][M loss: 0.231268][D loss: 0.948957][G loss 12.237779 ]time: 0:01:04.953509 
[Epoch 5/2000][Batch 11/22][AE+C loss: 0.643142][M loss: 0.168407][D loss: 1.143571][G loss 9.318064 ]time: 0:01:05.098775 
[Epoch 5/2000][Batch 12/22][AE+C loss: 0.600949][M loss: 0.139447][D loss: 1.222179][G loss 8.960137 ]time: 0:01:05.242699 
[Epoch 5/2000][Batch 13/22][AE+C loss: 0.570698][M loss: 0.150443][D loss: 1.069541][G loss 8.714715 ]time: 0:01:05.388534 
[Epoch 5/2000][Batch 14/22][AE+C loss: 0.564574][M loss: 0.139001][D loss: 1.028584][G loss 8.613353 ]time: 0:01:05.537279 
[Epoch 5/2000][Batch 15/22][AE+C loss: 0.525298][M loss: 0.143201][D loss: 0.957890][G loss 8.304243 ]time: 0:01:05.684043 
[Epoch 5/2000][Batch 16/22][AE+C loss: 0.521446][M loss: 0.170795][D loss: 0.909999][G loss 8.150861 ]time: 0:01:05.833699 
[Epoch 5/2000][Batch 17/22][AE+C loss: 0.521813][M loss: 0.226242][D loss: 0.788897][G loss 8.220364 ]time: 0:01:05.981233 
[Epoch 5/2000][Batch 18/22][AE+C loss: 0.526426][M loss: 0.224973][D loss: 1.047899][G loss 8.266521 ]time: 0:01:06.126603 
[Epoch 5/2000][Batch 19/22][AE+C loss: 0.576585][M loss: 0.136899][D loss: 1.882704][G loss 8.776020 ]time: 0:01:06.268351 
[Epoch 5/2000][Batch 20/22][AE+C loss: 0.580407][M loss: 0.191376][D loss: 1.703607][G loss 7.691304 ]time: 0:01:06.412377 
[Epoch 5/2000][Batch 21/22][AE+C loss: 0.615278][M loss: 0.126347][D loss: 1.396932][G loss 8.281333 ]time: 0:01:06.559972 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
46/63 [====================>.........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
47/63 [=====================>........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 5/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.400000]
[Epoch 6/2000][Batch 0/22][AE+C loss: 0.639558][M loss: 0.099234][D loss: 0.903461][G loss 7.429074 ]time: 0:01:15.042188 
[Epoch 6/2000][Batch 1/22][AE+C loss: 0.628965][M loss: 0.030000][D loss: 0.931510][G loss 7.713643 ]time: 0:01:15.189752 
[Epoch 6/2000][Batch 2/22][AE+C loss: 0.519782][M loss: 0.123216][D loss: 1.136077][G loss 7.922596 ]time: 0:01:15.334918 
[Epoch 6/2000][Batch 3/22][AE+C loss: 0.510370][M loss: 0.129322][D loss: 1.028762][G loss 7.698991 ]time: 0:01:15.485412 
[Epoch 6/2000][Batch 4/22][AE+C loss: 0.483738][M loss: 0.107569][D loss: 0.604921][G loss 7.297179 ]time: 0:01:15.630272 
[Epoch 6/2000][Batch 5/22][AE+C loss: 0.462610][M loss: 0.117626][D loss: 0.642872][G loss 7.112601 ]time: 0:01:15.779357 
[Epoch 6/2000][Batch 6/22][AE+C loss: 0.416846][M loss: 0.134295][D loss: 0.679656][G loss 7.010515 ]time: 0:01:15.927549 
[Epoch 6/2000][Batch 7/22][AE+C loss: 0.490729][M loss: 0.139789][D loss: 0.783051][G loss 7.709490 ]time: 0:01:16.071842 
[Epoch 6/2000][Batch 8/22][AE+C loss: 0.548501][M loss: 0.090313][D loss: 0.879484][G loss 8.550099 ]time: 0:01:16.214087 
[Epoch 6/2000][Batch 9/22][AE+C loss: 0.683665][M loss: 0.185628][D loss: 0.863240][G loss 10.630557 ]time: 0:01:16.356259 
[Epoch 6/2000][Batch 10/22][AE+C loss: 0.781950][M loss: 0.249819][D loss: 0.819098][G loss 11.794502 ]time: 0:01:16.497913 
[Epoch 6/2000][Batch 11/22][AE+C loss: 0.628921][M loss: 0.163864][D loss: 1.060157][G loss 9.035241 ]time: 0:01:16.641441 
[Epoch 6/2000][Batch 12/22][AE+C loss: 0.578823][M loss: 0.137708][D loss: 1.160055][G loss 8.166705 ]time: 0:01:16.783534 
[Epoch 6/2000][Batch 13/22][AE+C loss: 0.539748][M loss: 0.125151][D loss: 0.935196][G loss 8.304964 ]time: 0:01:16.922907 
[Epoch 6/2000][Batch 14/22][AE+C loss: 0.532517][M loss: 0.123278][D loss: 0.917248][G loss 7.696373 ]time: 0:01:17.069466 
[Epoch 6/2000][Batch 15/22][AE+C loss: 0.493860][M loss: 0.173241][D loss: 0.886383][G loss 7.662420 ]time: 0:01:17.212608 
[Epoch 6/2000][Batch 16/22][AE+C loss: 0.490275][M loss: 0.179135][D loss: 0.878169][G loss 7.765139 ]time: 0:01:17.349373 
[Epoch 6/2000][Batch 17/22][AE+C loss: 0.490088][M loss: 0.236607][D loss: 0.761555][G loss 7.936647 ]time: 0:01:17.488442 
[Epoch 6/2000][Batch 18/22][AE+C loss: 0.497855][M loss: 0.249495][D loss: 1.049843][G loss 7.662451 ]time: 0:01:17.630506 
[Epoch 6/2000][Batch 19/22][AE+C loss: 0.556441][M loss: 0.117001][D loss: 1.870888][G loss 7.974569 ]time: 0:01:17.775779 
[Epoch 6/2000][Batch 20/22][AE+C loss: 0.561382][M loss: 0.194550][D loss: 1.672889][G loss 7.696603 ]time: 0:01:17.920222 
[Epoch 6/2000][Batch 21/22][AE+C loss: 0.600085][M loss: 0.146334][D loss: 1.362389][G loss 8.554495 ]time: 0:01:18.063948 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
46/63 [====================>.........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
49/63 [======================>.......] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
47/63 [=====================>........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 6/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.400000]
[Epoch 7/2000][Batch 0/22][AE+C loss: 0.647074][M loss: 0.105539][D loss: 0.810043][G loss 7.290418 ]time: 0:01:26.448797 
[Epoch 7/2000][Batch 1/22][AE+C loss: 0.632260][M loss: 0.036554][D loss: 0.868909][G loss 7.359774 ]time: 0:01:26.596317 
[Epoch 7/2000][Batch 2/22][AE+C loss: 0.510633][M loss: 0.119966][D loss: 1.181351][G loss 7.727384 ]time: 0:01:26.745390 
[Epoch 7/2000][Batch 3/22][AE+C loss: 0.498334][M loss: 0.122241][D loss: 1.043332][G loss 7.373504 ]time: 0:01:26.886765 
[Epoch 7/2000][Batch 4/22][AE+C loss: 0.463292][M loss: 0.113924][D loss: 0.624783][G loss 7.320679 ]time: 0:01:27.028277 
[Epoch 7/2000][Batch 5/22][AE+C loss: 0.443506][M loss: 0.128511][D loss: 0.648521][G loss 6.725497 ]time: 0:01:27.173196 
[Epoch 7/2000][Batch 6/22][AE+C loss: 0.399554][M loss: 0.143773][D loss: 0.698175][G loss 7.117910 ]time: 0:01:27.322593 
[Epoch 7/2000][Batch 7/22][AE+C loss: 0.476410][M loss: 0.106642][D loss: 0.765343][G loss 7.544409 ]time: 0:01:27.468276 
[Epoch 7/2000][Batch 8/22][AE+C loss: 0.536838][M loss: 0.087580][D loss: 0.860799][G loss 7.828721 ]time: 0:01:27.608941 
[Epoch 7/2000][Batch 9/22][AE+C loss: 0.682118][M loss: 0.177677][D loss: 0.922094][G loss 9.715331 ]time: 0:01:27.756181 
[Epoch 7/2000][Batch 10/22][AE+C loss: 0.786502][M loss: 0.241836][D loss: 0.964662][G loss 10.260504 ]time: 0:01:27.897022 
[Epoch 7/2000][Batch 11/22][AE+C loss: 0.617079][M loss: 0.150658][D loss: 1.217061][G loss 8.508947 ]time: 0:01:28.043311 
[Epoch 7/2000][Batch 12/22][AE+C loss: 0.561849][M loss: 0.131468][D loss: 1.338128][G loss 7.712686 ]time: 0:01:28.188057 
[Epoch 7/2000][Batch 13/22][AE+C loss: 0.514444][M loss: 0.140994][D loss: 0.998098][G loss 7.776529 ]time: 0:01:28.331241 
[Epoch 7/2000][Batch 14/22][AE+C loss: 0.506060][M loss: 0.125707][D loss: 0.979680][G loss 7.131221 ]time: 0:01:28.473393 
[Epoch 7/2000][Batch 15/22][AE+C loss: 0.471657][M loss: 0.172266][D loss: 1.028870][G loss 7.209236 ]time: 0:01:28.616186 
[Epoch 7/2000][Batch 16/22][AE+C loss: 0.468514][M loss: 0.177501][D loss: 1.023146][G loss 7.271872 ]time: 0:01:28.761587 
[Epoch 7/2000][Batch 17/22][AE+C loss: 0.468564][M loss: 0.216923][D loss: 0.973902][G loss 7.562097 ]time: 0:01:28.908246 
[Epoch 7/2000][Batch 18/22][AE+C loss: 0.478852][M loss: 0.213659][D loss: 1.208120][G loss 7.674695 ]time: 0:01:29.055749 
[Epoch 7/2000][Batch 19/22][AE+C loss: 0.543524][M loss: 0.142187][D loss: 1.886978][G loss 7.127844 ]time: 0:01:29.200808 
[Epoch 7/2000][Batch 20/22][AE+C loss: 0.551953][M loss: 0.161973][D loss: 1.677649][G loss 7.461055 ]time: 0:01:29.346979 
[Epoch 7/2000][Batch 21/22][AE+C loss: 0.596080][M loss: 0.122403][D loss: 1.386798][G loss 8.153086 ]time: 0:01:29.498107 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
35/63 [===============>..............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
42/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
38/63 [=================>............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 7/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.400000]
[Epoch 8/2000][Batch 0/22][AE+C loss: 0.656774][M loss: 0.097319][D loss: 0.996888][G loss 7.420949 ]time: 0:01:37.970412 
[Epoch 8/2000][Batch 1/22][AE+C loss: 0.638635][M loss: 0.028675][D loss: 1.074979][G loss 7.486121 ]time: 0:01:38.112214 
[Epoch 8/2000][Batch 2/22][AE+C loss: 0.501625][M loss: 0.131729][D loss: 1.505945][G loss 7.127007 ]time: 0:01:38.259113 
[Epoch 8/2000][Batch 3/22][AE+C loss: 0.489116][M loss: 0.117518][D loss: 1.373272][G loss 7.029352 ]time: 0:01:38.406318 
[Epoch 8/2000][Batch 4/22][AE+C loss: 0.453209][M loss: 0.110149][D loss: 0.910467][G loss 6.503470 ]time: 0:01:38.554063 
[Epoch 8/2000][Batch 5/22][AE+C loss: 0.433846][M loss: 0.096966][D loss: 0.954292][G loss 6.684593 ]time: 0:01:38.698970 
[Epoch 8/2000][Batch 6/22][AE+C loss: 0.391100][M loss: 0.137593][D loss: 0.968264][G loss 6.743814 ]time: 0:01:38.844813 
[Epoch 8/2000][Batch 7/22][AE+C loss: 0.468969][M loss: 0.107629][D loss: 0.980218][G loss 7.078568 ]time: 0:01:38.982970 
[Epoch 8/2000][Batch 8/22][AE+C loss: 0.530891][M loss: 0.072806][D loss: 1.000988][G loss 7.845196 ]time: 0:01:39.126275 
[Epoch 8/2000][Batch 9/22][AE+C loss: 0.680751][M loss: 0.177739][D loss: 1.231579][G loss 9.168104 ]time: 0:01:39.268390 
[Epoch 8/2000][Batch 10/22][AE+C loss: 0.787657][M loss: 0.222497][D loss: 1.343970][G loss 10.214872 ]time: 0:01:39.416531 
[Epoch 8/2000][Batch 11/22][AE+C loss: 0.604116][M loss: 0.159247][D loss: 1.626550][G loss 7.951594 ]time: 0:01:39.560102 
[Epoch 8/2000][Batch 12/22][AE+C loss: 0.544662][M loss: 0.126237][D loss: 1.780339][G loss 6.790665 ]time: 0:01:39.703437 
[Epoch 8/2000][Batch 13/22][AE+C loss: 0.492904][M loss: 0.127133][D loss: 1.363047][G loss 6.602932 ]time: 0:01:39.844649 
[Epoch 8/2000][Batch 14/22][AE+C loss: 0.483734][M loss: 0.151153][D loss: 1.301649][G loss 6.839086 ]time: 0:01:39.984051 
[Epoch 8/2000][Batch 15/22][AE+C loss: 0.455653][M loss: 0.188106][D loss: 1.336821][G loss 6.868781 ]time: 0:01:40.131781 
[Epoch 8/2000][Batch 16/22][AE+C loss: 0.452871][M loss: 0.168975][D loss: 1.352294][G loss 6.855410 ]time: 0:01:40.271541 
[Epoch 8/2000][Batch 17/22][AE+C loss: 0.452680][M loss: 0.210867][D loss: 1.288590][G loss 6.904678 ]time: 0:01:40.421395 
[Epoch 8/2000][Batch 18/22][AE+C loss: 0.465528][M loss: 0.211395][D loss: 1.449909][G loss 6.952911 ]time: 0:01:40.562058 
[Epoch 8/2000][Batch 19/22][AE+C loss: 0.536784][M loss: 0.121664][D loss: 1.883829][G loss 6.738662 ]time: 0:01:40.706558 
[Epoch 8/2000][Batch 20/22][AE+C loss: 0.546259][M loss: 0.174798][D loss: 1.717952][G loss 6.900863 ]time: 0:01:40.850565 
[Epoch 8/2000][Batch 21/22][AE+C loss: 0.592187][M loss: 0.121831][D loss: 1.469256][G loss 7.752330 ]time: 0:01:40.996836 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
38/63 [=================>............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
37/63 [================>.............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
35/63 [===============>..............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 8/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.400000]
[Epoch 9/2000][Batch 0/22][AE+C loss: 0.663933][M loss: 0.088150][D loss: 1.287975][G loss 6.251462 ]time: 0:01:49.382443 
[Epoch 9/2000][Batch 1/22][AE+C loss: 0.643164][M loss: 0.026381][D loss: 1.470859][G loss 6.364512 ]time: 0:01:49.521501 
[Epoch 9/2000][Batch 2/22][AE+C loss: 0.502925][M loss: 0.121333][D loss: 2.027098][G loss 6.653824 ]time: 0:01:49.667558 
[Epoch 9/2000][Batch 3/22][AE+C loss: 0.489510][M loss: 0.119341][D loss: 1.871888][G loss 6.492355 ]time: 0:01:49.811564 
[Epoch 9/2000][Batch 4/22][AE+C loss: 0.449992][M loss: 0.094185][D loss: 1.420362][G loss 6.110083 ]time: 0:01:49.959523 
[Epoch 9/2000][Batch 5/22][AE+C loss: 0.430855][M loss: 0.115135][D loss: 1.405752][G loss 5.789102 ]time: 0:01:50.104739 
[Epoch 9/2000][Batch 6/22][AE+C loss: 0.388724][M loss: 0.131518][D loss: 1.379557][G loss 5.715386 ]time: 0:01:50.251243 
[Epoch 9/2000][Batch 7/22][AE+C loss: 0.465158][M loss: 0.102575][D loss: 1.401584][G loss 6.014890 ]time: 0:01:50.394381 
[Epoch 9/2000][Batch 8/22][AE+C loss: 0.526138][M loss: 0.059066][D loss: 1.383119][G loss 6.810327 ]time: 0:01:50.539621 
[Epoch 9/2000][Batch 9/22][AE+C loss: 0.677002][M loss: 0.158328][D loss: 1.668259][G loss 7.919037 ]time: 0:01:50.682580 
[Epoch 9/2000][Batch 10/22][AE+C loss: 0.784504][M loss: 0.242044][D loss: 1.837297][G loss 8.739568 ]time: 0:01:50.824105 
[Epoch 9/2000][Batch 11/22][AE+C loss: 0.596948][M loss: 0.156510][D loss: 2.029924][G loss 6.710692 ]time: 0:01:50.963436 
[Epoch 9/2000][Batch 12/22][AE+C loss: 0.536217][M loss: 0.127327][D loss: 2.128088][G loss 6.480216 ]time: 0:01:51.100724 
[Epoch 9/2000][Batch 13/22][AE+C loss: 0.476345][M loss: 0.136039][D loss: 1.832216][G loss 5.960756 ]time: 0:01:51.242773 
[Epoch 9/2000][Batch 14/22][AE+C loss: 0.466221][M loss: 0.129987][D loss: 1.842734][G loss 6.452261 ]time: 0:01:51.386754 
[Epoch 9/2000][Batch 15/22][AE+C loss: 0.444255][M loss: 0.162984][D loss: 1.676070][G loss 6.730943 ]time: 0:01:51.528754 
[Epoch 9/2000][Batch 16/22][AE+C loss: 0.441738][M loss: 0.172637][D loss: 1.673938][G loss 6.347190 ]time: 0:01:51.670870 
[Epoch 9/2000][Batch 17/22][AE+C loss: 0.441085][M loss: 0.221740][D loss: 1.641020][G loss 6.540035 ]time: 0:01:51.819591 
[Epoch 9/2000][Batch 18/22][AE+C loss: 0.455818][M loss: 0.206753][D loss: 1.686062][G loss 6.639617 ]time: 0:01:51.965306 
[Epoch 9/2000][Batch 19/22][AE+C loss: 0.532116][M loss: 0.121395][D loss: 1.849422][G loss 6.802584 ]time: 0:01:52.107530 
[Epoch 9/2000][Batch 20/22][AE+C loss: 0.542865][M loss: 0.136596][D loss: 1.755769][G loss 6.506383 ]time: 0:01:52.253143 
[Epoch 9/2000][Batch 21/22][AE+C loss: 0.589980][M loss: 0.113590][D loss: 1.628910][G loss 7.432660 ]time: 0:01:52.399496 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
33/63 [==============>...............] - ETA: 0s
63/63 [==============================] - 0s 2ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
38/63 [=================>............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
46/63 [====================>.........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 9/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.400000]
[Epoch 10/2000][Batch 0/22][AE+C loss: 0.668339][M loss: 0.090106][D loss: 1.664908][G loss 6.209442 ]time: 0:02:00.624499 
[Epoch 10/2000][Batch 1/22][AE+C loss: 0.645468][M loss: 0.024211][D loss: 1.741666][G loss 5.648993 ]time: 0:02:00.769292 
[Epoch 10/2000][Batch 2/22][AE+C loss: 0.503308][M loss: 0.117127][D loss: 2.252626][G loss 6.075353 ]time: 0:02:00.916771 
[Epoch 10/2000][Batch 3/22][AE+C loss: 0.489572][M loss: 0.129543][D loss: 2.138683][G loss 6.165772 ]time: 0:02:01.062236 
[Epoch 10/2000][Batch 4/22][AE+C loss: 0.450062][M loss: 0.100960][D loss: 1.736034][G loss 5.761583 ]time: 0:02:01.206489 
[Epoch 10/2000][Batch 5/22][AE+C loss: 0.430985][M loss: 0.108238][D loss: 1.706415][G loss 5.700012 ]time: 0:02:01.351763 
[Epoch 10/2000][Batch 6/22][AE+C loss: 0.388902][M loss: 0.129807][D loss: 1.630427][G loss 5.442630 ]time: 0:02:01.498407 
[Epoch 10/2000][Batch 7/22][AE+C loss: 0.464365][M loss: 0.091900][D loss: 1.640306][G loss 5.542444 ]time: 0:02:01.644603 
[Epoch 10/2000][Batch 8/22][AE+C loss: 0.524463][M loss: 0.038650][D loss: 1.653140][G loss 6.020818 ]time: 0:02:01.788201 
[Epoch 10/2000][Batch 9/22][AE+C loss: 0.673197][M loss: 0.163357][D loss: 1.859264][G loss 7.261834 ]time: 0:02:01.938909 
[Epoch 10/2000][Batch 10/22][AE+C loss: 0.779429][M loss: 0.213904][D loss: 2.031092][G loss 8.167650 ]time: 0:02:02.089233 
[Epoch 10/2000][Batch 11/22][AE+C loss: 0.588371][M loss: 0.150746][D loss: 2.122043][G loss 6.718284 ]time: 0:02:02.253291 
[Epoch 10/2000][Batch 12/22][AE+C loss: 0.526629][M loss: 0.130424][D loss: 2.165366][G loss 6.678651 ]time: 0:02:02.412894 
[Epoch 10/2000][Batch 13/22][AE+C loss: 0.463721][M loss: 0.137324][D loss: 2.055699][G loss 5.902371 ]time: 0:02:02.573404 
[Epoch 10/2000][Batch 14/22][AE+C loss: 0.453367][M loss: 0.123899][D loss: 2.051044][G loss 5.691202 ]time: 0:02:02.720957 
[Epoch 10/2000][Batch 15/22][AE+C loss: 0.438241][M loss: 0.183979][D loss: 1.851641][G loss 6.310647 ]time: 0:02:02.860790 
[Epoch 10/2000][Batch 16/22][AE+C loss: 0.435614][M loss: 0.177966][D loss: 1.810338][G loss 6.001604 ]time: 0:02:03.016059 
[Epoch 10/2000][Batch 17/22][AE+C loss: 0.433532][M loss: 0.200635][D loss: 1.694678][G loss 6.672732 ]time: 0:02:03.162321 
[Epoch 10/2000][Batch 18/22][AE+C loss: 0.449854][M loss: 0.206362][D loss: 1.728275][G loss 6.864649 ]time: 0:02:03.306410 
[Epoch 10/2000][Batch 19/22][AE+C loss: 0.530613][M loss: 0.106577][D loss: 1.821122][G loss 6.534729 ]time: 0:02:03.450781 
[Epoch 10/2000][Batch 20/22][AE+C loss: 0.541863][M loss: 0.164596][D loss: 1.778239][G loss 6.802416 ]time: 0:02:03.593918 
[Epoch 10/2000][Batch 21/22][AE+C loss: 0.589867][M loss: 0.119904][D loss: 1.719135][G loss 7.346523 ]time: 0:02:03.740156 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
33/63 [==============>...............] - ETA: 0s
63/63 [==============================] - 0s 2ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
49/63 [======================>.......] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 10/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.400000]
[Epoch 11/2000][Batch 0/22][AE+C loss: 0.670139][M loss: 0.091663][D loss: 1.729699][G loss 5.219903 ]time: 0:02:11.587395 
[Epoch 11/2000][Batch 1/22][AE+C loss: 0.645804][M loss: 0.020972][D loss: 1.804962][G loss 5.339176 ]time: 0:02:11.759881 
[Epoch 11/2000][Batch 2/22][AE+C loss: 0.501907][M loss: 0.121989][D loss: 2.079350][G loss 6.212915 ]time: 0:02:11.900603 
[Epoch 11/2000][Batch 3/22][AE+C loss: 0.488991][M loss: 0.119882][D loss: 1.993032][G loss 5.977922 ]time: 0:02:12.045599 
[Epoch 11/2000][Batch 4/22][AE+C loss: 0.452040][M loss: 0.089992][D loss: 1.702096][G loss 5.099593 ]time: 0:02:12.182328 
[Epoch 11/2000][Batch 5/22][AE+C loss: 0.432520][M loss: 0.111402][D loss: 1.685267][G loss 5.004427 ]time: 0:02:12.317188 
[Epoch 11/2000][Batch 6/22][AE+C loss: 0.389985][M loss: 0.124418][D loss: 1.618890][G loss 5.316684 ]time: 0:02:12.454914 
[Epoch 11/2000][Batch 7/22][AE+C loss: 0.464387][M loss: 0.093232][D loss: 1.638481][G loss 5.459849 ]time: 0:02:12.598338 
[Epoch 11/2000][Batch 8/22][AE+C loss: 0.523507][M loss: 0.035804][D loss: 1.707855][G loss 5.131183 ]time: 0:02:12.740884 
[Epoch 11/2000][Batch 9/22][AE+C loss: 0.669624][M loss: 0.167371][D loss: 1.808983][G loss 7.210567 ]time: 0:02:12.886422 
[Epoch 11/2000][Batch 10/22][AE+C loss: 0.774402][M loss: 0.217284][D loss: 1.861554][G loss 8.842663 ]time: 0:02:13.028429 
[Epoch 11/2000][Batch 11/22][AE+C loss: 0.580925][M loss: 0.147946][D loss: 1.903421][G loss 7.417479 ]time: 0:02:13.179588 
[Epoch 11/2000][Batch 12/22][AE+C loss: 0.518847][M loss: 0.118236][D loss: 1.887466][G loss 6.507834 ]time: 0:02:13.334588 
[Epoch 11/2000][Batch 13/22][AE+C loss: 0.455250][M loss: 0.124744][D loss: 1.977305][G loss 5.508364 ]time: 0:02:13.477561 
[Epoch 11/2000][Batch 14/22][AE+C loss: 0.445041][M loss: 0.129682][D loss: 2.003875][G loss 5.621436 ]time: 0:02:13.738562 
[Epoch 11/2000][Batch 15/22][AE+C loss: 0.435255][M loss: 0.172119][D loss: 1.731398][G loss 6.017591 ]time: 0:02:13.885701 
[Epoch 11/2000][Batch 16/22][AE+C loss: 0.432369][M loss: 0.183204][D loss: 1.722591][G loss 6.318180 ]time: 0:02:14.029423 
[Epoch 11/2000][Batch 17/22][AE+C loss: 0.428756][M loss: 0.194061][D loss: 1.596373][G loss 6.615179 ]time: 0:02:14.178294 
[Epoch 11/2000][Batch 18/22][AE+C loss: 0.446009][M loss: 0.217098][D loss: 1.610547][G loss 6.701622 ]time: 0:02:14.326033 
[Epoch 11/2000][Batch 19/22][AE+C loss: 0.528753][M loss: 0.097408][D loss: 1.814978][G loss 6.189468 ]time: 0:02:14.472457 
[Epoch 11/2000][Batch 20/22][AE+C loss: 0.540829][M loss: 0.155280][D loss: 1.808079][G loss 6.626564 ]time: 0:02:14.624738 
[Epoch 11/2000][Batch 21/22][AE+C loss: 0.589992][M loss: 0.119937][D loss: 1.769187][G loss 6.809938 ]time: 0:02:14.763511 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
39/63 [=================>............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
31/63 [=============>................] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 11/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.485069]
[Epoch 12/2000][Batch 0/22][AE+C loss: 0.672351][M loss: 0.089390][D loss: 1.629202][G loss 4.972086 ]time: 0:02:23.094653 
[Epoch 12/2000][Batch 1/22][AE+C loss: 0.647244][M loss: 0.019049][D loss: 1.665900][G loss 5.123393 ]time: 0:02:23.239431 
[Epoch 12/2000][Batch 2/22][AE+C loss: 0.501780][M loss: 0.133427][D loss: 1.772147][G loss 6.461775 ]time: 0:02:23.386338 
[Epoch 12/2000][Batch 3/22][AE+C loss: 0.489345][M loss: 0.120607][D loss: 1.686427][G loss 5.876365 ]time: 0:02:23.533857 
[Epoch 12/2000][Batch 4/22][AE+C loss: 0.453945][M loss: 0.093520][D loss: 1.450375][G loss 4.788193 ]time: 0:02:23.684088 
[Epoch 12/2000][Batch 5/22][AE+C loss: 0.433770][M loss: 0.098197][D loss: 1.427824][G loss 4.763223 ]time: 0:02:23.828559 
[Epoch 12/2000][Batch 6/22][AE+C loss: 0.390561][M loss: 0.136992][D loss: 1.410543][G loss 5.327014 ]time: 0:02:23.970788 
[Epoch 12/2000][Batch 7/22][AE+C loss: 0.464004][M loss: 0.095678][D loss: 1.549521][G loss 5.276289 ]time: 0:02:24.124002 
[Epoch 12/2000][Batch 8/22][AE+C loss: 0.522020][M loss: 0.027153][D loss: 1.733430][G loss 5.209569 ]time: 0:02:24.271765 
[Epoch 12/2000][Batch 9/22][AE+C loss: 0.667228][M loss: 0.152469][D loss: 1.733148][G loss 7.028613 ]time: 0:02:24.422512 
[Epoch 12/2000][Batch 10/22][AE+C loss: 0.771552][M loss: 0.234612][D loss: 1.714266][G loss 8.394074 ]time: 0:02:24.564092 
[Epoch 12/2000][Batch 11/22][AE+C loss: 0.576359][M loss: 0.152059][D loss: 1.627636][G loss 7.159097 ]time: 0:02:24.705863 
[Epoch 12/2000][Batch 12/22][AE+C loss: 0.513911][M loss: 0.134587][D loss: 1.570305][G loss 6.719213 ]time: 0:02:24.849397 
[Epoch 12/2000][Batch 13/22][AE+C loss: 0.449418][M loss: 0.128928][D loss: 1.796860][G loss 5.685286 ]time: 0:02:24.990188 
[Epoch 12/2000][Batch 14/22][AE+C loss: 0.439253][M loss: 0.121683][D loss: 1.810887][G loss 5.721760 ]time: 0:02:25.131456 
[Epoch 12/2000][Batch 15/22][AE+C loss: 0.433219][M loss: 0.175367][D loss: 1.510883][G loss 6.552341 ]time: 0:02:25.273655 
[Epoch 12/2000][Batch 16/22][AE+C loss: 0.430199][M loss: 0.180832][D loss: 1.494128][G loss 6.565564 ]time: 0:02:25.411004 
[Epoch 12/2000][Batch 17/22][AE+C loss: 0.425552][M loss: 0.197788][D loss: 1.386417][G loss 6.406342 ]time: 0:02:25.556543 
[Epoch 12/2000][Batch 18/22][AE+C loss: 0.443299][M loss: 0.196097][D loss: 1.471162][G loss 6.333681 ]time: 0:02:25.695644 
[Epoch 12/2000][Batch 19/22][AE+C loss: 0.527246][M loss: 0.105152][D loss: 1.828684][G loss 5.845077 ]time: 0:02:25.847177 
[Epoch 12/2000][Batch 20/22][AE+C loss: 0.540187][M loss: 0.147817][D loss: 1.798010][G loss 6.109022 ]time: 0:02:25.988652 
[Epoch 12/2000][Batch 21/22][AE+C loss: 0.590207][M loss: 0.120636][D loss: 1.727512][G loss 7.097266 ]time: 0:02:26.128506 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
37/63 [================>.............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
39/63 [=================>............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
42/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 12/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.485069]
[Epoch 13/2000][Batch 0/22][AE+C loss: 0.673038][M loss: 0.076097][D loss: 1.551115][G loss 4.967462 ]time: 0:02:35.008194 
[Epoch 13/2000][Batch 1/22][AE+C loss: 0.647411][M loss: 0.020756][D loss: 1.578736][G loss 5.091642 ]time: 0:02:35.153366 
[Epoch 13/2000][Batch 2/22][AE+C loss: 0.501705][M loss: 0.134938][D loss: 1.484270][G loss 6.471026 ]time: 0:02:35.296146 
[Epoch 13/2000][Batch 3/22][AE+C loss: 0.489567][M loss: 0.112455][D loss: 1.397796][G loss 6.099175 ]time: 0:02:35.442180 
[Epoch 13/2000][Batch 4/22][AE+C loss: 0.455122][M loss: 0.095268][D loss: 1.184112][G loss 5.076672 ]time: 0:02:35.586326 
[Epoch 13/2000][Batch 5/22][AE+C loss: 0.434432][M loss: 0.100669][D loss: 1.206274][G loss 5.031884 ]time: 0:02:35.734250 
[Epoch 13/2000][Batch 6/22][AE+C loss: 0.390689][M loss: 0.118213][D loss: 1.255311][G loss 5.510194 ]time: 0:02:35.882676 
[Epoch 13/2000][Batch 7/22][AE+C loss: 0.463441][M loss: 0.083542][D loss: 1.474956][G loss 5.436981 ]time: 0:02:36.029159 
[Epoch 13/2000][Batch 8/22][AE+C loss: 0.521020][M loss: 0.016616][D loss: 1.733755][G loss 4.968510 ]time: 0:02:36.164104 
[Epoch 13/2000][Batch 9/22][AE+C loss: 0.665796][M loss: 0.125325][D loss: 1.624454][G loss 6.702777 ]time: 0:02:36.311012 
[Epoch 13/2000][Batch 10/22][AE+C loss: 0.770058][M loss: 0.218641][D loss: 1.574800][G loss 8.685097 ]time: 0:02:36.454916 
[Epoch 13/2000][Batch 11/22][AE+C loss: 0.573757][M loss: 0.150656][D loss: 1.393686][G loss 6.862083 ]time: 0:02:36.595694 
[Epoch 13/2000][Batch 12/22][AE+C loss: 0.510720][M loss: 0.120669][D loss: 1.351112][G loss 6.608661 ]time: 0:02:36.741891 
[Epoch 13/2000][Batch 13/22][AE+C loss: 0.445603][M loss: 0.136641][D loss: 1.636369][G loss 5.669880 ]time: 0:02:36.892542 
[Epoch 13/2000][Batch 14/22][AE+C loss: 0.435408][M loss: 0.117927][D loss: 1.700018][G loss 5.845771 ]time: 0:02:37.035016 
[Epoch 13/2000][Batch 15/22][AE+C loss: 0.431213][M loss: 0.162928][D loss: 1.328115][G loss 6.116348 ]time: 0:02:37.174203 
[Epoch 13/2000][Batch 16/22][AE+C loss: 0.428166][M loss: 0.189037][D loss: 1.321065][G loss 6.244742 ]time: 0:02:37.312666 
[Epoch 13/2000][Batch 17/22][AE+C loss: 0.422223][M loss: 0.181953][D loss: 1.344286][G loss 6.495781 ]time: 0:02:37.453759 
[Epoch 13/2000][Batch 18/22][AE+C loss: 0.440795][M loss: 0.205259][D loss: 1.472554][G loss 5.877977 ]time: 0:02:37.596446 
[Epoch 13/2000][Batch 19/22][AE+C loss: 0.526894][M loss: 0.084277][D loss: 1.822418][G loss 5.665005 ]time: 0:02:37.739114 
[Epoch 13/2000][Batch 20/22][AE+C loss: 0.540253][M loss: 0.152234][D loss: 1.792719][G loss 6.164919 ]time: 0:02:37.880354 
[Epoch 13/2000][Batch 21/22][AE+C loss: 0.590139][M loss: 0.118527][D loss: 1.765586][G loss 7.199229 ]time: 0:02:38.026241 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
41/63 [==================>...........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
49/63 [======================>.......] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 13/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.485069]
[Epoch 14/2000][Batch 0/22][AE+C loss: 0.673552][M loss: 0.083746][D loss: 1.553806][G loss 5.084315 ]time: 0:02:46.030123 
[Epoch 14/2000][Batch 1/22][AE+C loss: 0.646785][M loss: 0.013421][D loss: 1.568096][G loss 4.954135 ]time: 0:02:46.168989 
[Epoch 14/2000][Batch 2/22][AE+C loss: 0.499725][M loss: 0.118461][D loss: 1.398535][G loss 6.161123 ]time: 0:02:46.312176 
[Epoch 14/2000][Batch 3/22][AE+C loss: 0.488377][M loss: 0.118387][D loss: 1.302815][G loss 5.719860 ]time: 0:02:46.452803 
[Epoch 14/2000][Batch 4/22][AE+C loss: 0.456390][M loss: 0.086837][D loss: 1.110170][G loss 4.768775 ]time: 0:02:46.598564 
[Epoch 14/2000][Batch 5/22][AE+C loss: 0.435307][M loss: 0.107532][D loss: 1.220307][G loss 5.050258 ]time: 0:02:46.740881 
[Epoch 14/2000][Batch 6/22][AE+C loss: 0.391132][M loss: 0.127418][D loss: 1.381675][G loss 4.958117 ]time: 0:02:46.873914 
[Epoch 14/2000][Batch 7/22][AE+C loss: 0.463334][M loss: 0.063374][D loss: 1.648414][G loss 4.634364 ]time: 0:02:47.034480 
[Epoch 14/2000][Batch 8/22][AE+C loss: 0.520325][M loss: 0.013982][D loss: 1.879858][G loss 4.343034 ]time: 0:02:47.182093 
[Epoch 14/2000][Batch 9/22][AE+C loss: 0.663640][M loss: 0.126076][D loss: 1.665762][G loss 6.718517 ]time: 0:02:47.323677 
[Epoch 14/2000][Batch 10/22][AE+C loss: 0.767359][M loss: 0.210542][D loss: 1.540542][G loss 8.437100 ]time: 0:02:47.471932 
[Epoch 14/2000][Batch 11/22][AE+C loss: 0.570017][M loss: 0.145053][D loss: 1.429913][G loss 7.151217 ]time: 0:02:47.617697 
[Epoch 14/2000][Batch 12/22][AE+C loss: 0.506876][M loss: 0.120371][D loss: 1.389351][G loss 6.366242 ]time: 0:02:47.767666 
[Epoch 14/2000][Batch 13/22][AE+C loss: 0.443346][M loss: 0.118849][D loss: 1.712651][G loss 5.817693 ]time: 0:02:47.919215 
[Epoch 14/2000][Batch 14/22][AE+C loss: 0.433330][M loss: 0.119823][D loss: 1.751533][G loss 5.414917 ]time: 0:02:48.064697 
[Epoch 14/2000][Batch 15/22][AE+C loss: 0.430974][M loss: 0.180422][D loss: 1.439649][G loss 6.065957 ]time: 0:02:48.208457 
[Epoch 14/2000][Batch 16/22][AE+C loss: 0.427796][M loss: 0.172026][D loss: 1.468716][G loss 6.096055 ]time: 0:02:48.356185 
[Epoch 14/2000][Batch 17/22][AE+C loss: 0.420608][M loss: 0.191025][D loss: 1.603781][G loss 6.540206 ]time: 0:02:48.500460 
[Epoch 14/2000][Batch 18/22][AE+C loss: 0.439747][M loss: 0.192943][D loss: 1.654708][G loss 6.718881 ]time: 0:02:48.642832 
[Epoch 14/2000][Batch 19/22][AE+C loss: 0.526599][M loss: 0.091762][D loss: 1.804250][G loss 5.854730 ]time: 0:02:48.793035 
[Epoch 14/2000][Batch 20/22][AE+C loss: 0.539965][M loss: 0.153279][D loss: 1.806054][G loss 6.133222 ]time: 0:02:48.940894 
[Epoch 14/2000][Batch 21/22][AE+C loss: 0.589987][M loss: 0.112805][D loss: 1.882828][G loss 6.922529 ]time: 0:02:49.089413 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
45/63 [====================>.........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
45/63 [====================>.........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
44/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 14/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.494792]
[Epoch 15/2000][Batch 0/22][AE+C loss: 0.673629][M loss: 0.085057][D loss: 1.789405][G loss 4.432082 ]time: 0:02:56.907095 
[Epoch 15/2000][Batch 1/22][AE+C loss: 0.646293][M loss: 0.025033][D loss: 1.765813][G loss 4.570425 ]time: 0:02:57.046269 
[Epoch 15/2000][Batch 2/22][AE+C loss: 0.498820][M loss: 0.129810][D loss: 1.604161][G loss 5.782538 ]time: 0:02:57.193960 
[Epoch 15/2000][Batch 3/22][AE+C loss: 0.487878][M loss: 0.114733][D loss: 1.553371][G loss 5.328269 ]time: 0:02:57.336174 
[Epoch 15/2000][Batch 4/22][AE+C loss: 0.457087][M loss: 0.083707][D loss: 1.479401][G loss 4.304852 ]time: 0:02:57.484299 
[Epoch 15/2000][Batch 5/22][AE+C loss: 0.435597][M loss: 0.106468][D loss: 1.586788][G loss 4.285092 ]time: 0:02:57.630226 
[Epoch 15/2000][Batch 6/22][AE+C loss: 0.391067][M loss: 0.108324][D loss: 1.750152][G loss 4.847105 ]time: 0:02:57.777209 
[Epoch 15/2000][Batch 7/22][AE+C loss: 0.462703][M loss: 0.078733][D loss: 1.901746][G loss 4.534387 ]time: 0:02:57.924392 
[Epoch 15/2000][Batch 8/22][AE+C loss: 0.519283][M loss: 0.017898][D loss: 2.061227][G loss 4.502798 ]time: 0:02:58.073039 
[Epoch 15/2000][Batch 9/22][AE+C loss: 0.662276][M loss: 0.145639][D loss: 1.724016][G loss 6.853368 ]time: 0:02:58.217255 
[Epoch 15/2000][Batch 10/22][AE+C loss: 0.765879][M loss: 0.215206][D loss: 1.543786][G loss 8.262155 ]time: 0:02:58.364307 
[Epoch 15/2000][Batch 11/22][AE+C loss: 0.567768][M loss: 0.145065][D loss: 1.612809][G loss 6.485060 ]time: 0:02:58.514410 
[Epoch 15/2000][Batch 12/22][AE+C loss: 0.504448][M loss: 0.107267][D loss: 1.639546][G loss 6.317816 ]time: 0:02:58.664584 
[Epoch 15/2000][Batch 13/22][AE+C loss: 0.441524][M loss: 0.124103][D loss: 1.882880][G loss 5.754588 ]time: 0:02:58.815099 
[Epoch 15/2000][Batch 14/22][AE+C loss: 0.431671][M loss: 0.146528][D loss: 1.921691][G loss 5.852660 ]time: 0:02:58.965327 
[Epoch 15/2000][Batch 15/22][AE+C loss: 0.430404][M loss: 0.190724][D loss: 1.765215][G loss 6.048156 ]time: 0:02:59.105954 
[Epoch 15/2000][Batch 16/22][AE+C loss: 0.427078][M loss: 0.169592][D loss: 1.801587][G loss 5.656997 ]time: 0:02:59.249488 
[Epoch 15/2000][Batch 17/22][AE+C loss: 0.418956][M loss: 0.188660][D loss: 1.964898][G loss 6.017476 ]time: 0:02:59.392277 
[Epoch 15/2000][Batch 18/22][AE+C loss: 0.438421][M loss: 0.193510][D loss: 1.897123][G loss 6.105196 ]time: 0:02:59.540126 
[Epoch 15/2000][Batch 19/22][AE+C loss: 0.525335][M loss: 0.088561][D loss: 1.759441][G loss 5.718403 ]time: 0:02:59.678253 
[Epoch 15/2000][Batch 20/22][AE+C loss: 0.539220][M loss: 0.146440][D loss: 1.861615][G loss 5.894190 ]time: 0:02:59.818363 
[Epoch 15/2000][Batch 21/22][AE+C loss: 0.589833][M loss: 0.116361][D loss: 2.059162][G loss 7.032113 ]time: 0:02:59.957801 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
33/63 [==============>...............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
39/63 [=================>............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 15/2000] [Accuracy_lsvm: 0.454861] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.494792]
[Epoch 16/2000][Batch 0/22][AE+C loss: 0.673404][M loss: 0.077342][D loss: 1.908085][G loss 4.309525 ]time: 0:03:07.686767 
[Epoch 16/2000][Batch 1/22][AE+C loss: 0.645603][M loss: 0.018421][D loss: 1.877671][G loss 4.583730 ]time: 0:03:07.837773 
[Epoch 16/2000][Batch 2/22][AE+C loss: 0.498708][M loss: 0.123479][D loss: 1.750984][G loss 5.497259 ]time: 0:03:07.984337 
[Epoch 16/2000][Batch 3/22][AE+C loss: 0.487853][M loss: 0.111894][D loss: 1.765264][G loss 5.326564 ]time: 0:03:08.126386 
[Epoch 16/2000][Batch 4/22][AE+C loss: 0.457622][M loss: 0.084297][D loss: 1.802802][G loss 4.072914 ]time: 0:03:08.271557 
[Epoch 16/2000][Batch 5/22][AE+C loss: 0.436066][M loss: 0.091412][D loss: 1.877951][G loss 4.160643 ]time: 0:03:08.413045 
[Epoch 16/2000][Batch 6/22][AE+C loss: 0.391289][M loss: 0.109758][D loss: 1.980138][G loss 4.023558 ]time: 0:03:08.554545 
[Epoch 16/2000][Batch 7/22][AE+C loss: 0.462372][M loss: 0.064988][D loss: 1.945672][G loss 4.342166 ]time: 0:03:08.707396 
[Epoch 16/2000][Batch 8/22][AE+C loss: 0.518330][M loss: 0.014723][D loss: 1.972242][G loss 4.630940 ]time: 0:03:08.851265 
[Epoch 16/2000][Batch 9/22][AE+C loss: 0.660223][M loss: 0.143221][D loss: 1.542716][G loss 7.172028 ]time: 0:03:08.997995 
[Epoch 16/2000][Batch 10/22][AE+C loss: 0.763386][M loss: 0.203382][D loss: 1.350500][G loss 8.047758 ]time: 0:03:09.136703 
[Epoch 16/2000][Batch 11/22][AE+C loss: 0.565776][M loss: 0.135158][D loss: 1.534562][G loss 6.539292 ]time: 0:03:09.282558 
[Epoch 16/2000][Batch 12/22][AE+C loss: 0.502613][M loss: 0.114022][D loss: 1.573723][G loss 5.710466 ]time: 0:03:09.432320 
[Epoch 16/2000][Batch 13/22][AE+C loss: 0.440468][M loss: 0.122534][D loss: 1.771323][G loss 5.527125 ]time: 0:03:09.579156 
[Epoch 16/2000][Batch 14/22][AE+C loss: 0.430729][M loss: 0.107033][D loss: 1.787915][G loss 5.589327 ]time: 0:03:09.725482 
[Epoch 16/2000][Batch 15/22][AE+C loss: 0.430413][M loss: 0.179086][D loss: 1.831180][G loss 5.879640 ]time: 0:03:09.872417 
[Epoch 16/2000][Batch 16/22][AE+C loss: 0.427106][M loss: 0.176427][D loss: 1.811756][G loss 5.682283 ]time: 0:03:10.015792 
[Epoch 16/2000][Batch 17/22][AE+C loss: 0.418328][M loss: 0.181882][D loss: 1.920416][G loss 6.099782 ]time: 0:03:10.156041 
[Epoch 16/2000][Batch 18/22][AE+C loss: 0.438177][M loss: 0.183910][D loss: 1.852575][G loss 5.841699 ]time: 0:03:10.300771 
[Epoch 16/2000][Batch 19/22][AE+C loss: 0.525801][M loss: 0.091870][D loss: 1.679822][G loss 5.579064 ]time: 0:03:10.449339 
[Epoch 16/2000][Batch 20/22][AE+C loss: 0.539880][M loss: 0.132276][D loss: 1.805385][G loss 6.162411 ]time: 0:03:10.596790 
[Epoch 16/2000][Batch 21/22][AE+C loss: 0.590245][M loss: 0.109825][D loss: 2.021869][G loss 7.121274 ]time: 0:03:10.744710 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
39/63 [=================>............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 16/2000] [Accuracy_lsvm: 0.480903] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.494792]
[Epoch 17/2000][Batch 0/22][AE+C loss: 0.671790][M loss: 0.075718][D loss: 1.721624][G loss 4.545982 ]time: 0:03:18.929041 
[Epoch 17/2000][Batch 1/22][AE+C loss: 0.643995][M loss: 0.021275][D loss: 1.662943][G loss 4.823471 ]time: 0:03:19.074347 
[Epoch 17/2000][Batch 2/22][AE+C loss: 0.497257][M loss: 0.114845][D loss: 1.583565][G loss 5.639649 ]time: 0:03:19.217437 
[Epoch 17/2000][Batch 3/22][AE+C loss: 0.486936][M loss: 0.113349][D loss: 1.586966][G loss 5.556501 ]time: 0:03:19.363648 
[Epoch 17/2000][Batch 4/22][AE+C loss: 0.458185][M loss: 0.075892][D loss: 1.665006][G loss 4.653250 ]time: 0:03:19.508484 
[Epoch 17/2000][Batch 5/22][AE+C loss: 0.436146][M loss: 0.090005][D loss: 1.724646][G loss 4.414415 ]time: 0:03:19.651335 
[Epoch 17/2000][Batch 6/22][AE+C loss: 0.391010][M loss: 0.107820][D loss: 1.775893][G loss 4.327151 ]time: 0:03:19.792139 
[Epoch 17/2000][Batch 7/22][AE+C loss: 0.461677][M loss: 0.070368][D loss: 1.689611][G loss 4.796092 ]time: 0:03:19.935935 
[Epoch 17/2000][Batch 8/22][AE+C loss: 0.517542][M loss: 0.010830][D loss: 1.591902][G loss 4.703519 ]time: 0:03:20.120197 
[Epoch 17/2000][Batch 9/22][AE+C loss: 0.659687][M loss: 0.139628][D loss: 1.223910][G loss 7.136525 ]time: 0:03:20.259479 
[Epoch 17/2000][Batch 10/22][AE+C loss: 0.763211][M loss: 0.186183][D loss: 1.025547][G loss 8.236651 ]time: 0:03:20.400857 
[Epoch 17/2000][Batch 11/22][AE+C loss: 0.564026][M loss: 0.144718][D loss: 1.181025][G loss 6.311769 ]time: 0:03:20.546610 
[Epoch 17/2000][Batch 12/22][AE+C loss: 0.500495][M loss: 0.111355][D loss: 1.255640][G loss 6.308980 ]time: 0:03:20.691706 
[Epoch 17/2000][Batch 13/22][AE+C loss: 0.439369][M loss: 0.129859][D loss: 1.488459][G loss 6.289206 ]time: 0:03:20.835593 
[Epoch 17/2000][Batch 14/22][AE+C loss: 0.429797][M loss: 0.122903][D loss: 1.475378][G loss 6.141365 ]time: 0:03:20.980972 
[Epoch 17/2000][Batch 15/22][AE+C loss: 0.429238][M loss: 0.189481][D loss: 1.521423][G loss 6.316272 ]time: 0:03:21.238388 
[Epoch 17/2000][Batch 16/22][AE+C loss: 0.425839][M loss: 0.187537][D loss: 1.535571][G loss 6.275098 ]time: 0:03:21.489847 
[Epoch 17/2000][Batch 17/22][AE+C loss: 0.416351][M loss: 0.200691][D loss: 1.522449][G loss 7.037380 ]time: 0:03:21.637133 
[Epoch 17/2000][Batch 18/22][AE+C loss: 0.436858][M loss: 0.189477][D loss: 1.524089][G loss 6.461556 ]time: 0:03:21.783280 
[Epoch 17/2000][Batch 19/22][AE+C loss: 0.526065][M loss: 0.085056][D loss: 1.604975][G loss 5.680122 ]time: 0:03:21.930992 
[Epoch 17/2000][Batch 20/22][AE+C loss: 0.539637][M loss: 0.153786][D loss: 1.684492][G loss 6.366918 ]time: 0:03:22.082376 
[Epoch 17/2000][Batch 21/22][AE+C loss: 0.589317][M loss: 0.120084][D loss: 1.795461][G loss 7.672521 ]time: 0:03:22.229245 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
31/63 [=============>................] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
37/63 [================>.............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
37/63 [================>.............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 17/2000] [Accuracy_lsvm: 0.480903] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.494792]
[Epoch 18/2000][Batch 0/22][AE+C loss: 0.672630][M loss: 0.071084][D loss: 1.216153][G loss 5.129553 ]time: 0:03:30.468073 
[Epoch 18/2000][Batch 1/22][AE+C loss: 0.643697][M loss: 0.015609][D loss: 1.186710][G loss 5.310769 ]time: 0:03:30.614022 
[Epoch 18/2000][Batch 2/22][AE+C loss: 0.497170][M loss: 0.115971][D loss: 1.216835][G loss 6.252089 ]time: 0:03:30.764028 
[Epoch 18/2000][Batch 3/22][AE+C loss: 0.486815][M loss: 0.109208][D loss: 1.200627][G loss 6.114828 ]time: 0:03:30.911172 
[Epoch 18/2000][Batch 4/22][AE+C loss: 0.458310][M loss: 0.073546][D loss: 1.125099][G loss 5.410638 ]time: 0:03:31.060008 
[Epoch 18/2000][Batch 5/22][AE+C loss: 0.436091][M loss: 0.085361][D loss: 1.202397][G loss 5.111081 ]time: 0:03:31.203111 
[Epoch 18/2000][Batch 6/22][AE+C loss: 0.390796][M loss: 0.104754][D loss: 1.304140][G loss 5.100798 ]time: 0:03:31.350640 
[Epoch 18/2000][Batch 7/22][AE+C loss: 0.460914][M loss: 0.071539][D loss: 1.312925][G loss 4.875264 ]time: 0:03:31.496001 
[Epoch 18/2000][Batch 8/22][AE+C loss: 0.516039][M loss: 0.003798][D loss: 1.254889][G loss 5.155268 ]time: 0:03:31.643316 
[Epoch 18/2000][Batch 9/22][AE+C loss: 0.657706][M loss: 0.122674][D loss: 0.861987][G loss 7.115643 ]time: 0:03:31.781332 
[Epoch 18/2000][Batch 10/22][AE+C loss: 0.761404][M loss: 0.196237][D loss: 0.662645][G loss 8.498003 ]time: 0:03:31.919757 
[Epoch 18/2000][Batch 11/22][AE+C loss: 0.562301][M loss: 0.131595][D loss: 0.886079][G loss 7.157256 ]time: 0:03:32.069050 
[Epoch 18/2000][Batch 12/22][AE+C loss: 0.498890][M loss: 0.125186][D loss: 0.978500][G loss 6.571663 ]time: 0:03:32.216867 
[Epoch 18/2000][Batch 13/22][AE+C loss: 0.438317][M loss: 0.120663][D loss: 1.202198][G loss 6.322192 ]time: 0:03:32.362685 
[Epoch 18/2000][Batch 14/22][AE+C loss: 0.428692][M loss: 0.127688][D loss: 1.207657][G loss 6.428259 ]time: 0:03:32.509574 
[Epoch 18/2000][Batch 15/22][AE+C loss: 0.430355][M loss: 0.200334][D loss: 1.173873][G loss 6.397114 ]time: 0:03:32.658571 
[Epoch 18/2000][Batch 16/22][AE+C loss: 0.426945][M loss: 0.171114][D loss: 1.144886][G loss 6.439883 ]time: 0:03:32.812123 
[Epoch 18/2000][Batch 17/22][AE+C loss: 0.416445][M loss: 0.165521][D loss: 1.076266][G loss 6.883237 ]time: 0:03:32.958731 
[Epoch 18/2000][Batch 18/22][AE+C loss: 0.436742][M loss: 0.177245][D loss: 1.196488][G loss 6.915878 ]time: 0:03:33.114312 
[Epoch 18/2000][Batch 19/22][AE+C loss: 0.524629][M loss: 0.076992][D loss: 1.653518][G loss 5.596277 ]time: 0:03:33.262107 
[Epoch 18/2000][Batch 20/22][AE+C loss: 0.539342][M loss: 0.159390][D loss: 1.614218][G loss 6.365103 ]time: 0:03:33.414796 
[Epoch 18/2000][Batch 21/22][AE+C loss: 0.590220][M loss: 0.111597][D loss: 1.560225][G loss 7.588296 ]time: 0:03:33.561438 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
34/63 [===============>..............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
38/63 [=================>............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
39/63 [=================>............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 18/2000] [Accuracy_lsvm: 0.480903] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.494792]
[Epoch 19/2000][Batch 0/22][AE+C loss: 0.669233][M loss: 0.087075][D loss: 0.843034][G loss 5.458017 ]time: 0:03:41.711949 
[Epoch 19/2000][Batch 1/22][AE+C loss: 0.640164][M loss: 0.017794][D loss: 0.831388][G loss 5.413576 ]time: 0:03:41.861848 
[Epoch 19/2000][Batch 2/22][AE+C loss: 0.496300][M loss: 0.110358][D loss: 1.129629][G loss 6.479312 ]time: 0:03:42.007541 
[Epoch 19/2000][Batch 3/22][AE+C loss: 0.486359][M loss: 0.103249][D loss: 1.081049][G loss 6.294141 ]time: 0:03:42.153241 
[Epoch 19/2000][Batch 4/22][AE+C loss: 0.458491][M loss: 0.081855][D loss: 0.808113][G loss 5.608272 ]time: 0:03:42.296170 
[Epoch 19/2000][Batch 5/22][AE+C loss: 0.435881][M loss: 0.080227][D loss: 0.904198][G loss 5.373828 ]time: 0:03:42.447665 
[Epoch 19/2000][Batch 6/22][AE+C loss: 0.390039][M loss: 0.104480][D loss: 1.047321][G loss 5.002462 ]time: 0:03:42.603271 
[Epoch 19/2000][Batch 7/22][AE+C loss: 0.459865][M loss: 0.061461][D loss: 1.136041][G loss 4.947019 ]time: 0:03:42.763666 
[Epoch 19/2000][Batch 8/22][AE+C loss: 0.515110][M loss: 0.013449][D loss: 1.255245][G loss 4.832914 ]time: 0:03:42.924444 
[Epoch 19/2000][Batch 9/22][AE+C loss: 0.658004][M loss: 0.119928][D loss: 0.790179][G loss 6.964414 ]time: 0:03:43.087744 
[Epoch 19/2000][Batch 10/22][AE+C loss: 0.762699][M loss: 0.191719][D loss: 0.555069][G loss 7.717420 ]time: 0:03:43.246987 
[Epoch 19/2000][Batch 11/22][AE+C loss: 0.562349][M loss: 0.143324][D loss: 0.932573][G loss 6.822156 ]time: 0:03:43.388391 
[Epoch 19/2000][Batch 12/22][AE+C loss: 0.498448][M loss: 0.116568][D loss: 1.089993][G loss 6.538879 ]time: 0:03:43.532492 
[Epoch 19/2000][Batch 13/22][AE+C loss: 0.437749][M loss: 0.115901][D loss: 1.216624][G loss 6.439498 ]time: 0:03:43.679433 
[Epoch 19/2000][Batch 14/22][AE+C loss: 0.428164][M loss: 0.134422][D loss: 1.245197][G loss 6.352037 ]time: 0:03:43.829156 
[Epoch 19/2000][Batch 15/22][AE+C loss: 0.428526][M loss: 0.200121][D loss: 1.016065][G loss 6.730384 ]time: 0:03:43.975978 
[Epoch 19/2000][Batch 16/22][AE+C loss: 0.424993][M loss: 0.200541][D loss: 0.978857][G loss 6.852414 ]time: 0:03:44.125386 
[Epoch 19/2000][Batch 17/22][AE+C loss: 0.413993][M loss: 0.176935][D loss: 0.920871][G loss 7.474518 ]time: 0:03:44.275343 
[Epoch 19/2000][Batch 18/22][AE+C loss: 0.435083][M loss: 0.189053][D loss: 1.109181][G loss 6.476191 ]time: 0:03:44.423013 
[Epoch 19/2000][Batch 19/22][AE+C loss: 0.525192][M loss: 0.063508][D loss: 1.684270][G loss 5.112375 ]time: 0:03:44.578627 
[Epoch 19/2000][Batch 20/22][AE+C loss: 0.539740][M loss: 0.124956][D loss: 1.605413][G loss 6.203842 ]time: 0:03:44.723318 
[Epoch 19/2000][Batch 21/22][AE+C loss: 0.589742][M loss: 0.108289][D loss: 1.543616][G loss 7.509012 ]time: 0:03:44.873610 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
32/63 [==============>...............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
47/63 [=====================>........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 19/2000] [Accuracy_lsvm: 0.527083] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.494792]
[Epoch 20/2000][Batch 0/22][AE+C loss: 0.670324][M loss: 0.076471][D loss: 0.696125][G loss 5.315616 ]time: 0:03:52.879202 
[Epoch 20/2000][Batch 1/22][AE+C loss: 0.640174][M loss: 0.017479][D loss: 0.780721][G loss 5.490914 ]time: 0:03:53.027709 
[Epoch 20/2000][Batch 2/22][AE+C loss: 0.494795][M loss: 0.113634][D loss: 1.438897][G loss 6.212966 ]time: 0:03:53.178825 
[Epoch 20/2000][Batch 3/22][AE+C loss: 0.485014][M loss: 0.096505][D loss: 1.320132][G loss 5.910212 ]time: 0:03:53.330852 
[Epoch 20/2000][Batch 4/22][AE+C loss: 0.458070][M loss: 0.074223][D loss: 0.857288][G loss 5.314554 ]time: 0:03:53.475520 
[Epoch 20/2000][Batch 5/22][AE+C loss: 0.435522][M loss: 0.084676][D loss: 1.008193][G loss 4.933033 ]time: 0:03:53.621779 
[Epoch 20/2000][Batch 6/22][AE+C loss: 0.389844][M loss: 0.127884][D loss: 1.165208][G loss 4.944981 ]time: 0:03:53.770876 
[Epoch 20/2000][Batch 7/22][AE+C loss: 0.459200][M loss: 0.064367][D loss: 1.275864][G loss 4.791330 ]time: 0:03:53.921927 
[Epoch 20/2000][Batch 8/22][AE+C loss: 0.513833][M loss: 0.006699][D loss: 1.355334][G loss 4.564330 ]time: 0:03:54.069967 
[Epoch 20/2000][Batch 9/22][AE+C loss: 0.655403][M loss: 0.133375][D loss: 0.773042][G loss 6.554953 ]time: 0:03:54.222253 
[Epoch 20/2000][Batch 10/22][AE+C loss: 0.759589][M loss: 0.180433][D loss: 0.415814][G loss 8.028179 ]time: 0:03:54.372187 
[Epoch 20/2000][Batch 11/22][AE+C loss: 0.560076][M loss: 0.137968][D loss: 1.058321][G loss 6.677373 ]time: 0:03:54.524018 
[Epoch 20/2000][Batch 12/22][AE+C loss: 0.497005][M loss: 0.110904][D loss: 1.327219][G loss 6.230605 ]time: 0:03:54.668866 
[Epoch 20/2000][Batch 13/22][AE+C loss: 0.437687][M loss: 0.115584][D loss: 1.303284][G loss 6.407652 ]time: 0:03:54.816005 
[Epoch 20/2000][Batch 14/22][AE+C loss: 0.428139][M loss: 0.120357][D loss: 1.319934][G loss 6.429301 ]time: 0:03:54.960833 
[Epoch 20/2000][Batch 15/22][AE+C loss: 0.430803][M loss: 0.169629][D loss: 1.003918][G loss 6.242395 ]time: 0:03:55.100502 
[Epoch 20/2000][Batch 16/22][AE+C loss: 0.427203][M loss: 0.189784][D loss: 0.996043][G loss 6.537648 ]time: 0:03:55.241191 
[Epoch 20/2000][Batch 17/22][AE+C loss: 0.415362][M loss: 0.161171][D loss: 0.920920][G loss 6.883580 ]time: 0:03:55.389935 
[Epoch 20/2000][Batch 18/22][AE+C loss: 0.436250][M loss: 0.191700][D loss: 1.100259][G loss 6.512854 ]time: 0:03:55.538597 
[Epoch 20/2000][Batch 19/22][AE+C loss: 0.525325][M loss: 0.085749][D loss: 1.634044][G loss 5.173861 ]time: 0:03:55.686898 
[Epoch 20/2000][Batch 20/22][AE+C loss: 0.540425][M loss: 0.135401][D loss: 1.635314][G loss 6.093976 ]time: 0:03:55.830436 
[Epoch 20/2000][Batch 21/22][AE+C loss: 0.590942][M loss: 0.113395][D loss: 1.622998][G loss 7.533792 ]time: 0:03:55.973288 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
30/63 [=============>................] - ETA: 0s
63/63 [==============================] - 0s 2ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
61/63 [============================>.] - ETA: 0s
63/63 [==============================] - 0s 843us/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
56/63 [=========================>....] - ETA: 0s
63/63 [==============================] - 0s 921us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 20/2000] [Accuracy_lsvm: 0.527083] [Accuracy_nrf: 0.541319] [Accuracy_pnb: 0.369097][Accuracy_mlp: 0.494792]
[Epoch 21/2000][Batch 0/22][AE+C loss: 0.665323][M loss: 0.074566][D loss: 0.701550][G loss 5.478938 ]time: 0:04:03.575388 
[Epoch 21/2000][Batch 1/22][AE+C loss: 0.636036][M loss: 0.018844][D loss: 0.818933][G loss 5.482685 ]time: 0:04:03.723369 
[Epoch 21/2000][Batch 2/22][AE+C loss: 0.494472][M loss: 0.115893][D loss: 1.715686][G loss 6.035918 ]time: 0:04:03.870395 
[Epoch 21/2000][Batch 3/22][AE+C loss: 0.485072][M loss: 0.102442][D loss: 1.574934][G loss 5.811511 ]time: 0:04:04.017777 
[Epoch 21/2000][Batch 4/22][AE+C loss: 0.458704][M loss: 0.074845][D loss: 1.052740][G loss 5.311205 ]time: 0:04:04.158016 
[Epoch 21/2000][Batch 5/22][AE+C loss: 0.435785][M loss: 0.068989][D loss: 1.163903][G loss 4.909860 ]time: 0:04:04.304247 
[Epoch 21/2000][Batch 6/22][AE+C loss: 0.389896][M loss: 0.107737][D loss: 1.317287][G loss 4.824612 ]time: 0:04:04.449439 
[Epoch 21/2000][Batch 7/22][AE+C loss: 0.458493][M loss: 0.059303][D loss: 1.361598][G loss 4.578129 ]time: 0:04:04.718690 
[Epoch 21/2000][Batch 8/22][AE+C loss: 0.512773][M loss: 0.005244][D loss: 1.359134][G loss 4.604731 ]time: 0:04:04.873688 
[Epoch 21/2000][Batch 9/22][AE+C loss: 0.654979][M loss: 0.141512][D loss: 0.646659][G loss 6.816943 ]time: 0:04:05.021302 
[Epoch 21/2000][Batch 10/22][AE+C loss: 0.760014][M loss: 0.186943][D loss: 0.256330][G loss 8.202478 ]time: 0:04:05.169343 
[Epoch 21/2000][Batch 11/22][AE+C loss: 0.559684][M loss: 0.134761][D loss: 1.050409][G loss 6.725696 ]time: 0:04:05.314717 
[Epoch 21/2000][Batch 12/22][AE+C loss: 0.496299][M loss: 0.116083][D loss: 1.312682][G loss 6.387071 ]time: 0:04:05.464602 
[Epoch 21/2000][Batch 13/22][AE+C loss: 0.436579][M loss: 0.113013][D loss: 1.279958][G loss 6.206418 ]time: 0:04:05.612752 
[Epoch 21/2000][Batch 14/22][AE+C loss: 0.426976][M loss: 0.117738][D loss: 1.283961][G loss 6.054984 ]time: 0:04:05.756859 
[Epoch 21/2000][Batch 15/22][AE+C loss: 0.428937][M loss: 0.192365][D loss: 1.102769][G loss 6.091155 ]time: 0:04:05.903887 
[Epoch 21/2000][Batch 16/22][AE+C loss: 0.425325][M loss: 0.188267][D loss: 1.089270][G loss 6.426117 ]time: 0:04:06.051228 
[Epoch 21/2000][Batch 17/22][AE+C loss: 0.413298][M loss: 0.196462][D loss: 0.990747][G loss 6.486882 ]time: 0:04:06.195142 
[Epoch 21/2000][Batch 18/22][AE+C loss: 0.434571][M loss: 0.191378][D loss: 1.147281][G loss 6.334770 ]time: 0:04:06.343618 
[Epoch 21/2000][Batch 19/22][AE+C loss: 0.524321][M loss: 0.070460][D loss: 1.632032][G loss 5.218394 ]time: 0:04:06.491642 
[Epoch 21/2000][Batch 20/22][AE+C loss: 0.539806][M loss: 0.137716][D loss: 1.648221][G loss 5.921694 ]time: 0:04:06.636694 
[Epoch 21/2000][Batch 21/22][AE+C loss: 0.590290][M loss: 0.111248][D loss: 1.686864][G loss 7.648818 ]time: 0:04:06.779254 
loading data...
test classes: [1, 6, 14]
train classes: [ 1  2  3  4  6  7  8  9 10 11 12 14]
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
37/63 [================>.............] - ETA: 0s
63/63 [==============================] - 0s 2ms/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
37/63 [================>.............] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
43/63 [===================>..........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)

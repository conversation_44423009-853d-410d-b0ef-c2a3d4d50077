import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,LayerNormalization,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
#import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler


# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    # 限制TensorFlow只使用第一个GPU
    tf.config.set_visible_devices(gpus[0], 'GPU')
    # 设置显存按需增长
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    # 显存增长必须在GPU初始化之前设置
    print(e)


def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = LayerNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = LayerNormalization()(y)
    
    # 如果输入和输出维度不同，需要一个线性投影
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        # input_shape is (batch_size, feature_dim)
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        # Reshape for matrix multiplication
        # Temporarily add a "sequence length" of 1
        # x_reshaped shape: (batch_size, 1, feature_dim)
        x_reshaped = K.expand_dims(x, axis=1)

        # Q, K, V projections
        q = self.query(x_reshaped)  # (batch_size, 1, feature_dim/8)
        k = self.key(x_reshaped)    # (batch_size, 1, feature_dim/8)
        v = self.value(x_reshaped)  # (batch_size, 1, feature_dim)

        # Attention scores
        attention_scores = K.batch_dot(q, k, axes=[2, 2]) # (batch_size, 1, 1)
        attention_probs = K.softmax(attention_scores)

        # Apply attention
        context = K.batch_dot(attention_probs, v) # (batch_size, 1, feature_dim)
        
        # Remove the temporary dimension
        context = K.squeeze(context, axis=1)

        # Add back to original input (residual connection)
        return x + self.gamma * context
        
class Zero_shot():
    def __init__(self):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        self.lambda_cla = 10 
        self.lambda_triplet = 20  # 只改这个：从10增加到20
        self.lambda_cms = 10  # 比较损失权重
        self.lambda_crl = 0.01 
        
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 1.0  # 只改这个：从0.4增加到1.0
        
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For triplet loss
        self.comparator_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For comparator
        self.prototype_mapper_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For prototype mapper
        
        self.autoencoder= self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c= self.build_classifier()
        self.prototype_mapper = self.build_prototype_mapper()  # 原型映射网络
        
        # 存储seen classes的原型
        self.seen_prototypes = {}  # {class_id: prototype_vector}
        self.prototype_update_frequency = 10  # 每10个epoch更新一次原型
        
    def build_autoencoder(self):
      
      sample = Input(shape=self.sample_shape)     
     
      a0=sample

      # Encoder
      a1=Dense(100)(a0)
      a1=LeakyReLU(alpha=0.2)(a1)
      a1=BatchNormalization()(a1)

      a2=Dense(200)(a1)
      a2=LeakyReLU(alpha=0.2)(a2)
      a2=BatchNormalization()(a2)

      a3=Dense(256)(a2)
      a3=LeakyReLU(alpha=0.2)(a3)
      a3=BatchNormalization()(a3)
      feature=a3

      # Decoder
      a4=Dense(200)(feature)
      a4=LeakyReLU(alpha=0.2)(a4)
      a4=BatchNormalization()(a4)

      a5=Dense(100)(a4)
      a5=LeakyReLU(alpha=0.2)(a5)
      a5=BatchNormalization()(a5)

      a6=Dense(52)(a5)
      a6=LeakyReLU(alpha=0.2)(a6)
      a6=BatchNormalization()(a6)
      output_sample=a6

      # Autoencoder Model
      autoencoder = Model(sample,[feature, output_sample])
      # We only need the encoder part for triplet loss feature extraction
      self.encoder = Model(sample, feature)
      return autoencoder    
        
    def build_discriminator(self):
        
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input,attribute],validity)

    def build_generator(self):
      
      noise = Input(shape=self.noise_shape)
      attribute = Input(shape=(20,), dtype='float32')
      
      noise_embedding = Flatten()(noise)
      attribute_embedding = Dense(self.latent_dim)(attribute)
      
      g_input = concatenate([noise_embedding, attribute_embedding])

      g1 = Dense(128)(g_input)
      g1 = LeakyReLU(alpha=0.2)(g1)
      g1 = BatchNormalization()(g1)

      g2 = residual_block(g1, 256) 
      g3 = residual_block(g2, 256) 
      
      g3_attention = SelfAttention()(g3)
      
      generated_feature = Dense(256)(g3_attention)
      generated_feature = BatchNormalization()(generated_feature)

      return Model([noise,attribute],generated_feature)
    
    def build_classifier(self):
        
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        
        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        hidden_ouput=c2
               
        c3 = Dense(20,activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])
    
    def build_prototype_mapper(self):
        """
        原型映射网络：从属性空间(20维)映射到特征空间原型(256维)
        学习seen class属性到其真实原型的映射关系
        """
        attribute_input = Input(shape=(20,), dtype='float32')
        
        # 使用深度网络来学习复杂的映射关系
        pm1 = Dense(128, activation='relu')(attribute_input)
        pm1 = BatchNormalization()(pm1)
        pm1 = Dropout(0.3)(pm1)
        
        pm2 = Dense(256, activation='relu')(pm1)
        pm2 = BatchNormalization()(pm2)
        pm2 = Dropout(0.3)(pm2)
        
        pm3 = Dense(512, activation='relu')(pm2)
        pm3 = BatchNormalization()(pm3)
        pm3 = Dropout(0.3)(pm3)
        
        # 输出层：映射到特征空间原型(256维)
        prototype_output = Dense(256, activation='linear')(pm3)
        
        return Model(attribute_input, prototype_output)
    
    def compute_class_prototypes(self, train_X_by_class, seen_class_indices):
        """
        计算seen classes的真实原型(类中心)
        """
        prototypes = {}
        for class_id in seen_class_indices:
            class_samples = train_X_by_class[class_id]
            # 通过encoder提取特征，然后计算均值作为原型
            class_features = self.encoder(class_samples)
            prototype = tf.reduce_mean(class_features, axis=0, keepdims=True)
            prototypes[class_id] = prototype
        return prototypes
    
    def update_seen_prototypes(self, train_X_by_class, seen_class_indices):
        """
        更新seen classes的原型
        """
        self.seen_prototypes = self.compute_class_prototypes(train_X_by_class, seen_class_indices)
    
    def prototypical_triplet_loss(self, anchor_features, unseen_attributes, seen_class_id, margin=0.4):
        """
        原型三元组损失
        anchor_features: 生成器产生的特征 G(z, c_u)
        unseen_attributes: unseen class的属性 c_u  
        seen_class_id: 作为negative的seen class ID
        """
        # Positive: unseen class的虚拟原型 P(c_u) = f_map(c_u)
        virtual_prototype = self.prototype_mapper(unseen_attributes)
        
        # Negative: seen class的真实原型 P(c_s)
        real_prototype = self.seen_prototypes[seen_class_id]
        
        # 计算距离
        pos_dist = tf.reduce_sum(tf.square(anchor_features - virtual_prototype), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor_features - real_prototype), axis=-1)
        
        # Triplet loss: 希望anchor与positive距离近，与negative距离远
        basic_loss = pos_dist - neg_dist + margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss
    
    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
            return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self,current_batch_features,y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(
                y_true, pred_attribute)
        
        mi_penalty=0    
        if self.bound == True:    
          mi_penalty = self.mi_penalty_loss(
              current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def train(self, epochs, batch_size, log_file=None, test_class_indices=None):
        
        start_time = datetime.datetime.now()
        
        accuracy_list_1=[]
        accuracy_list_2=[]
        accuracy_list_3=[]
        accuracy_list_4=[]
        
        valid = -np.ones((batch_size,1) )
        fake = np.ones((batch_size,1) )
        
        # 比较器训练标签
        # similar_truth = np.ones((batch_size,1))
        # unsimilar_truth = np.zeros((batch_size,1))
        
        PATH_train='/app/data/dataset_train_case1.npz'
        PATH_test='/app/data/dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        # 使用传入的测试类别，如果没有传入则默认使用E组
        if test_class_indices is None:
            test_class_indices = [9, 13, 15]  # 默认E组 (1-based)
        
        # 根据测试类别，确定训练类别（Seen classes）
        all_class_indices = list(range(1, 16)) # 1-15
        seen_class_indices = [i for i in all_class_indices if i not in test_class_indices]
        
        # This part remains mostly the same, but we will use the labels to find pos/neg samples
        train_X_by_class = {i: train_data[f'training_samples_{i}'] for i in seen_class_indices}
        train_Y_by_class = {i: train_data[f'training_attribute_{i}'] for i in seen_class_indices}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        test_X_by_class = {i: test_data[f'testing_samples_{i}'] for i in test_class_indices}
        test_Y_by_class = {i: test_data[f'testing_attribute_{i}'] for i in test_class_indices}

        test_X = np.concatenate([v for k, v in test_X_by_class.items()])
        test_Y = np.concatenate([v for k, v in test_Y_by_class.items()])
        test_classlabel = np.concatenate([np.full(len(v), k) for k, v in test_X_by_class.items()])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # Re-organize scaled data back into class dictionaries
        current_pos = 0
        for i in seen_class_indices:
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata=all_train_X
        train_attributelabel=all_train_Y
        train_classlabel = all_train_labels
        
        testdata=test_X
        test_attributelabel=test_Y
       
        num_batches=int(traindata.shape[0]/batch_size)
        
        # 初始化seen classes的原型
        print("初始化seen classes的原型...")
        self.update_seen_prototypes(train_X_by_class, seen_class_indices)
        print(f"已初始化 {len(self.seen_prototypes)} 个seen class的原型")
               
        for epoch in range(epochs):
            
            for batch_i in range(num_batches):
                
                start_i =batch_i * batch_size
                end_i=(batch_i + 1) * batch_size
                
                train_x=traindata[start_i:end_i]
                train_y=train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # Autoencoder and Classifier Training (Same as before)
                self.autoencoder.trainable = True
                self.c.trainable = True # Train C together with AE now
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                  feature, output_sample=self.autoencoder(train_x)
                  autoencoder_loss=mean_squared_error(train_x,output_sample)      

                  hidden_ouput_c,predict_attribute_c=self.c(feature)
                  c_loss=self.classification_loss(feature,train_y, hidden_ouput_c, predict_attribute_c)

                  total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c

                # Prototypical Triplet Loss 和 Prototype Mapper Training
                self.autoencoder.trainable = True # Encoder is part of metric learning
                self.c.trainable = True
                self.prototype_mapper.trainable = True
                
                # 1. 训练原型映射网络：学习seen class属性到原型的映射
                seen_attributes = []
                seen_prototypes_list = []
                for class_id in seen_class_indices:
                    # 获取该类的属性
                    class_attribute = train_Y_by_class[class_id][0]  # 同一类的属性都相同，取第一个
                    seen_attributes.append(class_attribute)
                    seen_prototypes_list.append(self.seen_prototypes[class_id][0])  # 去掉keepdims维度
                
                seen_attributes = np.array(seen_attributes)
                seen_prototypes_list = np.array(seen_prototypes_list)
                
                with tf.GradientTape() as tape_prototype_mapper:
                    predicted_prototypes = self.prototype_mapper(seen_attributes)
                    prototype_mapper_loss = tf.reduce_mean(tf.square(predicted_prototypes - seen_prototypes_list))
                
                grads_prototype_mapper = tape_prototype_mapper.gradient(prototype_mapper_loss, self.prototype_mapper.trainable_weights)
                self.prototype_mapper_optimizer.apply_gradients(zip(grads_prototype_mapper, self.prototype_mapper.trainable_weights))
                
                # 2. 使用生成器生成特征作为anchor，应用原型三元组损失
                # 我们需要为每个样本准备unseen class属性（这里我们用test class的属性作为unseen）
                # 为简化，我们随机选择test class的属性作为unseen class属性
                unseen_attributes_for_triplet = []
                negative_seen_classes = []
                for _ in range(batch_size):
                    # 随机选择一个test class的属性作为unseen
                    test_class_id = np.random.choice(test_class_indices)
                    unseen_attr = test_Y_by_class[test_class_id][0]
                    unseen_attributes_for_triplet.append(unseen_attr)
                    
                    # 随机选择一个seen class作为negative
                    negative_seen_class = np.random.choice(seen_class_indices)
                    negative_seen_classes.append(negative_seen_class)
                
                unseen_attributes_for_triplet = np.array(unseen_attributes_for_triplet)
                
                with tf.GradientTape() as tape_prototypical_triplet:
                    # 生成特征作为anchor
                    noise_triplet = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    anchor_features = self.g([noise_triplet, unseen_attributes_for_triplet])
                    
                    # 计算原型三元组损失
                    triplet_losses = []
                    for i in range(batch_size):
                        single_anchor = tf.expand_dims(anchor_features[i], 0)
                        single_unseen_attr = tf.expand_dims(unseen_attributes_for_triplet[i], 0)
                        negative_class_id = negative_seen_classes[i]
                        
                        triplet_loss_single = self.prototypical_triplet_loss(
                            single_anchor, single_unseen_attr, negative_class_id, margin=self.triplet_margin)
                        triplet_losses.append(triplet_loss_single)
                    
                    prototypical_triplet_loss = tf.reduce_mean(triplet_losses)
                
                grads_prototypical_triplet = tape_prototypical_triplet.gradient(
                    prototypical_triplet_loss, 
                    self.encoder.trainable_weights + self.prototype_mapper.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_prototypical_triplet, 
                    self.encoder.trainable_weights + self.prototype_mapper.trainable_weights))

                # Discriminator Training (Same as before)
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False

                for _ in range(self.n_critic):
                  with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature = self.g([noise,train_y])
                    real_feature = self.encoder(train_x)
        
                    real_validity = self.d([real_feature,train_y])
                    fake_validity = self.d([fake_feature,train_y])  
                                           
                    d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                    d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                    d_loss = d_loss_real + d_loss_fake
                  
                  grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                  self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # Generator Training
                self.d.trainable = False               
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                  noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                  Fake_feature_g = self.g([noise_g,train_y])
                  Fake_validity_g = self.d([Fake_feature_g,train_y])
                  adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
            
                  fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                  classification_loss = self.classification_loss(Fake_feature_g,train_y, fake_hidden_ouput_g, Fake_classification_g)
                  
                  # Prototypical Triplet loss for Generator
                  g_anchor_features = Fake_feature_g
                  
                  # 为生成器计算原型三元组损失
                  g_triplet_losses = []
                  for i in range(batch_size):
                      single_g_anchor = tf.expand_dims(g_anchor_features[i], 0)
                      single_g_unseen_attr = tf.expand_dims(unseen_attributes_for_triplet[i], 0)
                      g_negative_class_id = negative_seen_classes[i]
                      
                      g_triplet_loss_single = self.prototypical_triplet_loss(
                          single_g_anchor, single_g_unseen_attr, g_negative_class_id, margin=self.triplet_margin)
                      g_triplet_losses.append(g_triplet_loss_single)
                  
                  triplet_loss_g = tf.reduce_mean(g_triplet_losses)
                  
                  # 比较损失计算
                  # real_features_for_comparison = self.encoder(train_x)
                  # comparison_loss_similar = tf.keras.losses.binary_crossentropy(similar_truth, self.m([Fake_feature_g, real_features_for_comparison]))
                  # comparison_loss_unsimilar = tf.keras.losses.binary_crossentropy(unsimilar_truth, self.m([Fake_feature_g, g_negative_features]))
                  # cycle_rank_loss = 0
                  # if self.crl == True:
                  #     ...
                  #     cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature, similar_truth)
                  total_loss = adversarial_loss + self.lambda_cla * classification_loss + self.lambda_triplet * triplet_loss_g
                          
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print ("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][PrototypeMapper loss: %f][PrototypicalTriplet loss: %f][D loss: %f][G loss %05f ]time: %s " \
                 % (epoch, epochs,
                   batch_i, num_batches,
                   tf.reduce_mean(total_ac_loss), 
                     prototype_mapper_loss,
                     prototypical_triplet_loss,
                     d_loss,
                     tf.reduce_mean(total_loss),                                                                                                              
                     elapsed_time))
        
            # 定期更新原型
            if epoch % self.prototype_update_frequency == 0 and epoch > 0:
                print(f"更新原型 (Epoch {epoch})...")
                self.update_seen_prototypes(train_X_by_class, seen_class_indices)
                
            if epoch % 1 == 0:
                accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp = feature_generation_and_diagnosis(2000,testdata,test_attributelabel,self.autoencoder,self.g, self.c, test_class_indices)  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                  %(epoch, epochs,max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)))
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
            
        best_accuracy = max([max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)])
        print('finished! best_acc:{:.4f}'.format(best_accuracy))
        if log_file:
            log_file.write(f'finished! best_acc:{best_accuracy:.4f}\n')
            log_file.flush()
                
if __name__ == '__main__':
    # =============================================================
    # 配置区域：只需要修改这里的TARGET_GROUP即可切换实验组别
    # =============================================================
    TARGET_GROUP = 'B'#可选: 'A', 'B', 'C', 'D', 'E'
    # =============================================================
    
    # 分组配置
    GROUP_CONFIGS = {
        'A': [1, 6, 14],   # 测试类别: [1, 6, 14] 
        'B': [4, 7, 10],   # 测试类别: [4, 7, 10]
        'C': [8, 11, 12],  # 测试类别: [8, 11, 12]
        'D': [2, 3, 5],    # 测试类别: [2, 3, 5]
        'E': [9, 13, 15],  # 测试类别: [9, 13, 15]
    }
    
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + f"_triplet_simple_fix_Group{TARGET_GROUP}.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"开始运行 Group {TARGET_GROUP} 实验，测试类别: {GROUP_CONFIGS[TARGET_GROUP]}")
    print(f"训练开始，日志将被记录到: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 训练日志 (Triplet架构 - Group {TARGET_GROUP})\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**实验组别**: Group {TARGET_GROUP}\n")
        log_file.write(f"**测试类别**: {GROUP_CONFIGS[TARGET_GROUP]}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = Zero_shot()
        gan.train(epochs=2000, batch_size=256, log_file=log_file, test_class_indices=GROUP_CONFIGS[TARGET_GROUP])

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"训练完成，日志已保存至: {log_filename}") 
    
#!/usr/bin/env python3
"""
🚀 自动化训练脚本 - D、E组连续训练

按顺序执行：
1. D组 2000轮次
2. E组 2000轮次

每组训练完成后自动开始下一组
"""

import os
import sys
import subprocess
import time
from datetime import datetime, timedelta

def run_training(group, epochs=2000):
    """运行指定组的训练"""
    print(f"""
🚀 开始训练组别: {group}
=====================================
📊 组别: {group}
🔄 轮次: {epochs}
⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📍 当前目录: {os.getcwd()}
=====================================
    """)
    
    start_time = time.time()
    
    try:
        # 构建命令
        cmd = [
            'python', 'test_all_improvements.py',
            '--group', group,
            '--epochs', str(epochs)
        ]
        
        print(f"🔧 执行命令: {' '.join(cmd)}")
        print("=" * 80)
        
        # 运行训练
        result = subprocess.run(cmd, check=True)
        
        # 计算训练时间
        end_time = time.time()
        duration = end_time - start_time
        duration_str = str(timedelta(seconds=int(duration)))
        
        print("=" * 80)
        print(f"✅ {group}组训练完成！")
        print(f"⏱️ 训练时间: {duration_str}")
        print(f"🏁 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return True, duration
        
    except subprocess.CalledProcessError as e:
        print("=" * 80)
        print(f"❌ {group}组训练失败！")
        print(f"错误码: {e.returncode}")
        return False, 0
    except KeyboardInterrupt:
        print("=" * 80)
        print(f"⚠️ {group}组训练被用户中断！")
        return False, 0
    except Exception as e:
        print("=" * 80)
        print(f"❌ {group}组训练出现异常: {str(e)}")
        return False, 0


def main():
    """主函数 - 按顺序训练D、E组"""
    
    print(f"""
🎯 自动化训练脚本启动 - D、E组
=====================================
📋 训练计划:
1. D组: 2000 epochs (~2-3小时)
2. E组: 2000 epochs (~2-3小时)

📊 预计总时间: 4-6小时
⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💡 文献基准对比:
- D组目标: 87.24% (文献第二高)
- E组目标: 89.06% (文献最高)

🎯 提示:
- 可以随时按 Ctrl+C 中断
- 每组完成后会自动开始下一组
- 结果保存在 experiments/group_X/ 目录
- D组和E组在文献中表现最好
=====================================
    """)
    
    # 确认是否继续
    try:
        response = input("🤔 确认开始D、E组自动化训练？(y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ 训练已取消")
            return
    except KeyboardInterrupt:
        print("\n❌ 训练已取消")
        return
    
    # 切换到正确目录
    script_dir = '/home/<USER>/hmt/ACGAN-FG-main/innovations'
    if not os.path.exists(script_dir):
        print(f"❌ 目录不存在: {script_dir}")
        return
    
    original_dir = os.getcwd()
    os.chdir(script_dir)
    
    # 训练组别列表
    groups = ['D', 'E']
    results = {}
    total_start_time = time.time()
    
    try:
        for i, group in enumerate(groups, 1):
            print(f"\n{'='*60}")
            print(f"📊 进度: {i}/{len(groups)} - 开始训练{group}组")
            
            # 显示组别特点
            if group == 'D':
                print("🎯 D组特点: 测试类别[2,3,5], 文献基准87.24%")
            elif group == 'E':
                print("🎯 E组特点: 测试类别[9,13,15], 文献基准89.06% (最高)")
            
            print(f"{'='*60}")
            
            # 运行训练
            success, duration = run_training(group, epochs=2000)
            results[group] = {'success': success, 'duration': duration}
            
            if not success:
                print(f"⚠️ {group}组训练失败，是否继续下一组？")
                try:
                    response = input("继续？(y/N): ").strip().lower()
                    if response not in ['y', 'yes']:
                        print("❌ 自动化训练已停止")
                        break
                except KeyboardInterrupt:
                    print("\n❌ 自动化训练已中断")
                    break
            
            # 如果不是最后一组，显示间隔信息
            if i < len(groups):
                next_group = groups[i]
                print(f"\n⏳ 准备开始{next_group}组训练...")
                print("💡 如需中断，请按 Ctrl+C")
                time.sleep(3)  # 短暂暂停
    
    except KeyboardInterrupt:
        print("\n⚠️ 自动化训练被用户中断")
    
    finally:
        os.chdir(original_dir)
    
    # 显示最终结果
    total_duration = time.time() - total_start_time
    total_duration_str = str(timedelta(seconds=int(total_duration)))
    
    print(f"""
🎉 D、E组自动化训练完成！
=====================================
📊 训练结果汇总:
""")
    
    # 文献基准对比
    literature_benchmarks = {
        'D': 87.24,
        'E': 89.06
    }
    
    for group in groups:
        if group in results:
            result = results[group]
            status = "✅ 成功" if result['success'] else "❌ 失败"
            duration = str(timedelta(seconds=int(result['duration']))) if result['duration'] > 0 else "N/A"
            benchmark = literature_benchmarks.get(group, 0)
            print(f"   {group}组: {status} (用时: {duration}) [文献基准: {benchmark}%]")
        else:
            benchmark = literature_benchmarks.get(group, 0)
            print(f"   {group}组: ⏸️ 未执行 [文献基准: {benchmark}%]")
    
    print(f"""
⏱️ 总用时: {total_duration_str}
🏁 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📁 查看结果:
- D组: experiments/group_D/
- E组: experiments/group_E/

📊 启动TensorBoard查看训练曲线:
tensorboard --logdir=tensorboard

🎯 期望结果:
- D组: 目标达到87%+ (文献87.24%)
- E组: 目标达到89%+ (文献89.06%)
=====================================
    """)


if __name__ == "__main__":
    main()

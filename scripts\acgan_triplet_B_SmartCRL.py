#!/usr/bin/env python3
"""
实验B-SmartCRL: 强化语义自洽性约束
目标: 通过更强的语义约束生成更有区分度的特征
基于: acgan_triplet.py (最成功的基线模型)
针对: B组困难类别 [4, 7, 10]

关键改进:
- lambda_crl: 0.01 -> 0.1 (10倍增强)
- 保持triplet权重不变，专注于语义一致性
- 增强循环排序损失以提高语义区分度
"""

import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, Reshape, Flatten, Dropout, multiply, GaussianNoise
from tensorflow.keras.layers import BatchNormalization, Activation, Embedding, ZeroPadding2D
from tensorflow.keras.layers import LeakyReLU
from tensorflow.keras.layers import UpSampling2D, Conv2D
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.utils import to_categorical
import tensorflow.keras.backend as K

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import accuracy_score
from sklearn.svm import LinearSVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
import sys
import os
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/scripts')
from read_data import creat_dataset

# 添加scripts目录到路径，避免与系统test模块冲突
sys.path.insert(0, '/app/scripts')
import test as test_module

class Zero_shot():
    def __init__(self):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        # 🔥 实验B-SmartCRL: 强化语义自洽性约束
        self.lambda_cla = 10          # 保持不变
        self.lambda_triplet = 10      # 保持不变
        self.lambda_cms = 10          # 保持不变
        self.lambda_crl = 0.1         # 0.01 -> 0.1 (10倍增强)
        
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 0.4
        
        # Center Loss 权重保持不变
        self.lambda_center = 0.5      # 保持不变
        self.centers = None
        self.seen_class_map = None
        self.center_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        
        # 🔥 新增：语义一致性增强参数
        self.lambda_semantic_consistency = 0.5  # 语义一致性权重
        self.semantic_margin = 0.3               # 语义边际

        # 最优准确率跟踪
        self.best_accuracy = {
            'lsvm': 0.0,
            'nrf': 0.0,
            'pnb': 0.0,
            'mlp': 0.0
        }
        
        # 构建模型
        self.build_autoencoder()
        self.discriminator = self.build_discriminator()
        self.generator = self.build_generator()
        self.classifier = self.build_classifier()

        # 为了兼容feature_generation_and_diagnosis函数，添加别名
        self.d = self.discriminator
        self.g = self.generator
        self.c = self.classifier
        
        # 优化器
        self.optimizer_G = Adam(0.0001, 0.5)
        self.optimizer_D = Adam(0.0001, 0.5)
        self.optimizer_C = Adam(0.0001, 0.5)
        self.optimizer_M = Adam(0.0001, 0.5)
        
        # 编译判别器
        self.discriminator.compile(loss=self.wasserstein_loss,
            optimizer=self.optimizer_D,
            metrics=['accuracy'])
        
        # 编译分类器
        self.classifier.compile(loss='binary_crossentropy',
            optimizer=self.optimizer_C,
            metrics=['accuracy'])
        
        # 编译生成器
        self.discriminator.trainable = False
        self.classifier.trainable = False
        
        noise = Input(shape=self.noise_shape)
        label = Input(shape=(20,))
        feature = self.generator([noise, label])
        
        fake = self.discriminator([feature, label])
        hidden_ouput, pred_attribute = self.classifier(feature)
        
        self.combined = Model([noise, label], [fake, pred_attribute])
        self.combined.compile(loss=[self.wasserstein_loss, 'binary_crossentropy'],
            optimizer=self.optimizer_G)
        
        # 编译度量学习模型
        self.encoder.compile(optimizer=self.optimizer_M)
        
    def build_autoencoder(self):
        """构建自编码器"""
        # 编码器
        sample = Input(shape=self.sample_shape)
        
        e1 = Dense(256)(sample)
        e1 = LeakyReLU(alpha=0.2)(e1)
        e1 = BatchNormalization(momentum=0.8)(e1)
        
        e2 = Dense(128)(e1)
        e2 = LeakyReLU(alpha=0.2)(e2)
        e2 = BatchNormalization(momentum=0.8)(e2)
        
        encoded = Dense(self.feature_dim)(e2)
        encoded = LeakyReLU(alpha=0.2)(encoded)
        
        self.encoder = Model(sample, encoded)
        
        # 解码器
        feature = Input(shape=self.feature_shape)
        
        d1 = Dense(128)(feature)
        d1 = LeakyReLU(alpha=0.2)(d1)
        d1 = BatchNormalization(momentum=0.8)(d1)
        
        d2 = Dense(256)(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)
        d2 = BatchNormalization(momentum=0.8)(d2)
        
        decoded = Dense(self.data_lenth, activation='tanh')(d2)
        
        self.decoder = Model(feature, decoded)
        
        # 完整自编码器 - 返回特征和重构输出（兼容feature_generation_and_diagnosis）
        sample_input = Input(shape=self.sample_shape)
        encoded_repr = self.encoder(sample_input)
        reconstructed = self.decoder(encoded_repr)

        self.autoencoder = Model(sample_input, [encoded_repr, reconstructed])
        self.autoencoder.compile(optimizer=Adam(0.0001), loss='mse')
        
    def build_generator(self):
        """构建生成器"""
        noise = Input(shape=self.noise_shape)
        label = Input(shape=(20,))
        
        label_embedding = Flatten()(Embedding(20, self.latent_dim)(label))
        label_embedding = Dense(self.latent_dim)(label)
        
        model_input = multiply([noise, label_embedding])
        model_input = Flatten()(model_input)
        
        x = Dense(128)(model_input)
        x = LeakyReLU(alpha=0.2)(x)
        x = BatchNormalization(momentum=0.8)(x)
        
        x = Dense(256)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = BatchNormalization(momentum=0.8)(x)
        
        feature = Dense(self.feature_dim, activation='tanh')(x)
        
        return Model([noise, label], feature)
    
    def build_discriminator(self):
        """构建判别器"""
        feature = Input(shape=self.feature_shape)
        label = Input(shape=(20,))
        
        label_embedding = Dense(self.feature_dim)(label)
        
        model_input = multiply([feature, label_embedding])
        
        x = Dense(256)(model_input)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        
        x = Dense(128)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        
        validity = Dense(1)(x)
        
        return Model([feature, label], validity)
    
    def build_classifier(self):
        """构建分类器"""
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        
        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        hidden_ouput=c2
               
        c3 = Dense(20,activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])
    
    def triplet_loss(self, anchor, positive, negative, margin=None):
        """Triplet损失函数"""
        if margin is None:
            margin = self.triplet_margin
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        """Wasserstein损失"""
        return K.mean(y_true * y_pred)
    
    def center_loss(self, features, labels):
        """使用通用安全的Center Loss"""
        return safe_center_loss(features, labels, self.centers, self.seen_class_map)
    
    def center_loss_old(self, features, labels):
        """Center Loss实现"""
        if self.centers is None:
            return tf.constant(0.0)
        
        # 获取当前批次的中心
        batch_centers = tf.gather(self.centers, labels)
        
        # 计算特征与对应中心的距离
        center_loss = tf.reduce_mean(tf.reduce_sum(tf.square(features - batch_centers), axis=1))
        
        return center_loss
    
    def cycle_rank_loss(self, anchor, positive, negative):
        """增强的循环排序损失"""
        # 基础triplet损失
        basic_loss = self.triplet_loss(anchor, positive, negative)
        
        # 🔥 新增：语义一致性约束
        # 确保重构特征与原始特征在语义空间中保持一致
        semantic_consistency_loss = tf.reduce_mean(tf.square(anchor - positive))
        
        # 🔥 新增：语义边际约束
        # 确保不同类别的特征在语义空间中有足够的边际
        semantic_margin_loss = tf.maximum(0.0, 
            self.semantic_margin - tf.reduce_mean(tf.square(anchor - negative)))
        
        # 组合损失
        total_crl_loss = (basic_loss + 
                         self.lambda_semantic_consistency * semantic_consistency_loss +
                         self.lambda_semantic_consistency * semantic_margin_loss)
        
        return total_crl_loss
    
    def enhanced_semantic_loss(self, generated_features, target_attributes, predicted_attributes):
        """增强的语义损失"""
        # 确保数据类型一致
        generated_features = tf.cast(generated_features, tf.float32)
        target_attributes = tf.cast(target_attributes, tf.float32)
        predicted_attributes = tf.cast(predicted_attributes, tf.float32)

        # 基础属性预测损失
        attribute_loss = tf.reduce_mean(tf.square(predicted_attributes - target_attributes))

        # 🔥 语义区分度损失
        # 鼓励不同类别的特征在语义空间中更加分离
        batch_size = tf.shape(generated_features)[0]

        # 计算特征间的相似度矩阵
        normalized_features = tf.nn.l2_normalize(generated_features, axis=1)
        similarity_matrix = tf.matmul(normalized_features, normalized_features, transpose_b=True)

        # 计算属性间的相似度矩阵
        normalized_attributes = tf.nn.l2_normalize(target_attributes, axis=1)
        attribute_similarity = tf.matmul(normalized_attributes, normalized_attributes, transpose_b=True)

        # 语义一致性损失：特征相似度应该与属性相似度一致
        semantic_consistency = tf.reduce_mean(tf.square(similarity_matrix - attribute_similarity))

        return attribute_loss + tf.cast(self.lambda_semantic_consistency, tf.float32) * semantic_consistency

    def mi_penalty_loss(self, x, z):
        """互信息惩罚损失"""
        # 简化的互信息估计
        x_flat = tf.reshape(x, [tf.shape(x)[0], -1])
        z_flat = tf.reshape(z, [tf.shape(z)[0], -1])

        # 确保维度匹配
        min_dim = tf.minimum(tf.shape(x_flat)[1], tf.shape(z_flat)[1])
        x_flat = x_flat[:, :min_dim]
        z_flat = z_flat[:, :min_dim]

        # 计算相关性
        x_mean = tf.reduce_mean(x_flat, axis=0, keepdims=True)
        z_mean = tf.reduce_mean(z_flat, axis=0, keepdims=True)

        x_centered = x_flat - x_mean
        z_centered = z_flat - z_mean

        correlation = tf.reduce_mean(x_centered * z_centered)
        return tf.abs(correlation)

    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        """分类损失"""
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)

        mi_penalty = 0
        if self.bound == True:
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)

        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss

    def print_experiment_info(self):
        """打印实验信息"""
        print("="*80)
        print("🔥 实验B-SmartCRL: 强化语义自洽性约束")
        print("="*80)
        print("📊 目标: 通过更强的语义约束生成更有区分度的特征")
        print("🎯 针对: B组困难类别 [4, 7, 10]")
        print("")
        print("⚡ 关键权重调整:")
        print(f"   - lambda_crl: 0.01 -> {self.lambda_crl} (10倍增强)")
        print(f"   - lambda_triplet: {self.lambda_triplet} (保持不变)")
        print(f"   - lambda_center: {self.lambda_center} (保持不变)")
        print(f"   - lambda_cla: {self.lambda_cla} (保持不变)")
        print("")
        print("🔬 新增语义增强:")
        print(f"   - lambda_semantic_consistency: {self.lambda_semantic_consistency}")
        print(f"   - semantic_margin: {self.semantic_margin}")
        print("")
        print("🔬 实验假设:")
        print("   - 更强的CRL约束能够提高语义自洽性")
        print("   - 语义一致性约束能够增强特征区分度")
        print("   - 语义边际约束能够防止类别混淆")
        print("="*80)

    def train(self, epochs=2000, batch_size=256, log_file=None):
        """B-SmartCRL实验训练函数"""
        import datetime
from universal_group_config import UniversalGroupConfig, safe_center_loss, safe_update_centers
        from read_data import creat_dataset
        from tensorflow.keras.losses import mean_squared_error

        start_time = datetime.datetime.now()

        print("🔥 开始B-SmartCRL实验训练...")
        print(f"📅 开始时间: {start_time}")
        print(f"🎯 目标类别: [4, 7, 10] (B组)")
        print(f"⚡ 强化权重: lambda_crl={self.lambda_crl}, semantic_consistency={self.lambda_semantic_consistency}")

        # 加载B组数据
        traindata, train_classlabel, train_attributelabel, \
        testdata, test_classlabel, test_attributelabel, \
        test_attribute_matrix, train_attribute_matrix = creat_dataset([4, 7, 10])

        print(f"📊 训练数据: {traindata.shape}")
        print(f"📊 测试数据: {testdata.shape}")

        # 初始化类中心
        num_seen_classes = len(np.unique(train_classlabel))
        self.centers = tf.Variable(tf.random.normal([num_seen_classes, self.feature_dim]), trainable=True)
        self.seen_class_map = {class_id: idx for idx, class_id in enumerate(np.unique(train_classlabel))}

        num_batches = len(traindata) // batch_size

        valid = -np.ones((batch_size, 1))
        fake = np.ones((batch_size, 1))

        for epoch in range(epochs):
            epoch_start = datetime.datetime.now()

            # 初始化损失值变量
            epoch_crl_loss = 0.0
            epoch_semantic_loss = 0.0
            epoch_total_loss = 0.0

            for batch_i in range(num_batches):
                batch_start_time = datetime.datetime.now()
                start_i = batch_i * batch_size
                end_i = (batch_i + 1) * batch_size

                train_x = traindata[start_i:end_i]
                train_y = train_attributelabel[start_i:end_i]
                train_labels = train_classlabel[start_i:end_i]

                # 1. Autoencoder and Classifier Training
                self.autoencoder.trainable = True
                self.classifier.trainable = True

                with tf.GradientTape(persistent=True) as tape_auto_c:
                    feature = self.encoder(train_x)
                    output_sample = self.decoder(feature)
                    autoencoder_loss = mean_squared_error(train_x, output_sample)

                    hidden_output_c, predict_attribute_c = self.classifier(feature)
                    c_loss = self.classification_loss(feature, train_y, hidden_output_c, predict_attribute_c)

                    # 🔥 新增：语义一致性损失
                    semantic_loss = self.enhanced_semantic_loss(feature, train_y, predict_attribute_c)

                    # 保存语义损失到外部作用域
                    epoch_semantic_loss = semantic_loss

                    total_ac_loss = autoencoder_loss + c_loss + self.lambda_semantic_consistency * semantic_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss,
                                                   self.autoencoder.trainable_weights + self.classifier.trainable_weights)
                self.optimizer_C.apply_gradients(zip(grads_auto_c,
                                                   self.autoencoder.trainable_weights + self.classifier.trainable_weights))
                del tape_auto_c

                # 2. Metric Learning (Triplet + Center Loss)
                # 准备triplet样本
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []

                for i, label in enumerate(train_labels):
                    # 正样本：同类别的其他样本
                    same_class_indices = np.where(train_classlabel == label)[0]
                    if len(same_class_indices) > 1:
                        pos_idx = np.random.choice([idx for idx in same_class_indices if idx != start_i + i])
                        positive_samples.append(traindata[pos_idx])
                    else:
                        positive_samples.append(train_x[i])  # 如果没有其他同类样本，使用自己

                    # 负样本：不同类别的样本
                    diff_class_indices = np.where(train_classlabel != label)[0]
                    neg_idx = np.random.choice(diff_class_indices)
                    negative_samples.append(traindata[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape(persistent=True) as tape_m:
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)

                    # 标准Triplet Loss
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                    # 标准Center Loss
                    c_loss = self.center_loss(anchor_features, train_labels)

                    # 组合损失
                    total_metric_loss = m_loss + self.lambda_center * c_loss

                # 更新encoder
                grads_encoder = tape_m.gradient(total_metric_loss, self.encoder.trainable_weights)
                self.optimizer_M.apply_gradients(zip(grads_encoder, self.encoder.trainable_weights))

                # 更新centers
                grads_centers = tape_m.gradient(c_loss, [self.centers])
                self.center_optimizer.apply_gradients(zip(grads_centers, [self.centers]))
                del tape_m

                # 3. Discriminator Training
                self.autoencoder.trainable = False
                self.classifier.trainable = False
                self.discriminator.trainable = True
                self.generator.trainable = False

                for _ in range(self.n_critic):
                    with tf.GradientTape() as tape_d:
                        noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                        fake_feature = self.generator([noise, train_y])
                        real_feature = self.encoder(train_x)

                        real_validity = self.discriminator([real_feature, train_y])
                        fake_validity = self.discriminator([fake_feature, train_y])

                        d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                        d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                        d_loss = d_loss_real + d_loss_fake

                    grads_d = tape_d.gradient(d_loss, self.discriminator.trainable_weights)
                    self.optimizer_D.apply_gradients(zip(grads_d, self.discriminator.trainable_weights))

                # 4. Generator Training
                self.discriminator.trainable = False
                self.generator.trainable = True

                with tf.GradientTape() as tape_g:
                    noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    Fake_feature_g = self.generator([noise_g, train_y])
                    Fake_validity_g = self.discriminator([Fake_feature_g, train_y])
                    adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)

                    fake_hidden_output_g, Fake_classification_g = self.classifier(Fake_feature_g)
                    classification_loss = self.classification_loss(Fake_feature_g, train_y, fake_hidden_output_g, Fake_classification_g)

                    # Generator的Triplet loss
                    g_anchor_features = Fake_feature_g
                    g_positive_features = self.encoder(positive_samples)
                    g_negative_features = self.encoder(negative_samples)
                    triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)

                    # Generator的Center loss
                    center_loss_g = self.center_loss(g_anchor_features, train_labels)

                    # 🔥 强化的CRL loss
                    cycle_rank_loss_val = 0.0
                    if self.crl:
                        reconstructed_feature = self.generator([noise_g, Fake_classification_g])
                        shuffled_indices = tf.random.shuffle(tf.range(batch_size))
                        unsimilar_attributes = tf.gather(train_y, shuffled_indices)
                        unsimilar_fake_feature = self.generator([noise_g, unsimilar_attributes])

                        # 使用增强的CRL损失
                        cycle_rank_loss_val = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_fake_feature)

                    # 保存CRL损失到外部作用域
                    epoch_crl_loss = cycle_rank_loss_val

                    # 🔥 语义增强损失
                    semantic_gen_loss = self.enhanced_semantic_loss(Fake_feature_g, train_y, Fake_classification_g)

                    # 🔥 使用强化CRL权重的总损失
                    total_loss = (adversarial_loss +
                                 self.lambda_cla * classification_loss +
                                 self.lambda_triplet * triplet_loss_g +
                                 self.lambda_center * center_loss_g +
                                 self.lambda_crl * cycle_rank_loss_val +
                                 self.lambda_semantic_consistency * semantic_gen_loss)

                # 保存总损失到外部作用域
                epoch_total_loss = total_loss

                grads_g = tape_g.gradient(total_loss, self.generator.trainable_weights)
                self.optimizer_G.apply_gradients(zip(grads_g, self.generator.trainable_weights))

                # 每个batch输出损失值（E组格式）
                batch_time = datetime.datetime.now() - batch_start_time
                try:
                    # 转换损失值
                    ae_c_val = float(total_ac_loss.numpy().mean()) if hasattr(total_ac_loss, 'numpy') else float(total_ac_loss)
                    crl_val = float(cycle_rank_loss_val.numpy().mean()) if hasattr(cycle_rank_loss_val, 'numpy') else float(cycle_rank_loss_val)
                    semantic_val = float(semantic_gen_loss.numpy().mean()) if hasattr(semantic_gen_loss, 'numpy') else float(semantic_gen_loss)
                    d_val = float(d_loss.numpy().mean()) if hasattr(d_loss, 'numpy') else float(d_loss)
                    g_val = float(total_loss.numpy().mean()) if hasattr(total_loss, 'numpy') else float(total_loss)

                    print(f"[Epoch {epoch+1}/{epochs}][Batch {batch_i}/{num_batches}][AE+C loss: {ae_c_val:.6f}][强化CRL: {crl_val:.6f}][语义增强: {semantic_val:.6f}][D loss: {d_val:.6f}][G loss {g_val:.6f}]time: {batch_time}")

                    if log_file:
                        log_file.write(f"[Epoch {epoch+1}/{epochs}][Batch {batch_i}/{num_batches}][AE+C loss: {ae_c_val:.6f}][强化CRL: {crl_val:.6f}][语义增强: {semantic_val:.6f}][D loss: {d_val:.6f}][G loss {g_val:.6f}]time: {batch_time}\n")
                        log_file.flush()
                except:
                    print(f"[Epoch {epoch+1}/{epochs}][Batch {batch_i}/{num_batches}] Loss recording error")

                # 保存损失到epoch级别
                epoch_crl_loss = cycle_rank_loss_val
                epoch_semantic_loss = semantic_gen_loss
                epoch_total_loss = total_loss

            # 每个epoch都输出进度和损失
            epoch_time = datetime.datetime.now() - epoch_start

            # 安全地转换张量为标量
            try:
                # 正确的TensorFlow张量转换方式
                if hasattr(epoch_crl_loss, 'numpy'):
                    crl_array = epoch_crl_loss.numpy()
                    crl_val = crl_array.item() if crl_array.size == 1 else float(crl_array.mean())
                else:
                    crl_val = float(epoch_crl_loss)

                if hasattr(epoch_semantic_loss, 'numpy'):
                    semantic_array = epoch_semantic_loss.numpy()
                    semantic_val = semantic_array.item() if semantic_array.size == 1 else float(semantic_array.mean())
                else:
                    semantic_val = float(epoch_semantic_loss)

                if hasattr(epoch_total_loss, 'numpy'):
                    total_array = epoch_total_loss.numpy()
                    total_val = total_array.item() if total_array.size == 1 else float(total_array.mean())
                else:
                    total_val = float(epoch_total_loss)

                # 每个epoch结束时使用feature_generation_and_diagnosis评估（原版格式）
                if epoch % 1 == 0:
                    accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = test_module.feature_generation_and_diagnosis(
                        2000, testdata, test_attributelabel, self.autoencoder, self.g, self.c, [4, 7, 10])

                    # 更新最优准确率
                    if accuracy_lsvm > self.best_accuracy['lsvm']:
                        self.best_accuracy['lsvm'] = accuracy_lsvm
                    if accuracy_nrf > self.best_accuracy['nrf']:
                        self.best_accuracy['nrf'] = accuracy_nrf
                    if accuracy_pnb > self.best_accuracy['pnb']:
                        self.best_accuracy['pnb'] = accuracy_pnb
                    if accuracy_mlp > self.best_accuracy['mlp']:
                        self.best_accuracy['mlp'] = accuracy_mlp

                    # E组格式输出
                    print(f"[Accuracy_lsvm: {accuracy_lsvm:.6f}] [Accuracy_nrf: {accuracy_nrf:.6f}] [Accuracy_pnb: {accuracy_pnb:.6f}][Accuracy_mlp: {accuracy_mlp:.6f}]time: {epoch_time}")
                    print(f"[Best_lsvm: {self.best_accuracy['lsvm']:.6f}] [Best_nrf: {self.best_accuracy['nrf']:.6f}] [Best_pnb: {self.best_accuracy['pnb']:.6f}][Best_mlp: {self.best_accuracy['mlp']:.6f}]")

                if log_file:
                    log_file.write(f"Epoch {epoch+1}: CRL={crl_val:.4f}, Semantic={semantic_val:.4f}, Total={total_val:.4f}, Accuracy={accuracy:.2f}%\n")
                    log_file.flush()

            except Exception as e:
                print(f"Epoch {epoch+1}/{epochs} - 耗时: {epoch_time}")
                print(f"  📊 损失值记录错误: {e}")
                if log_file:
                    log_file.write(f"Epoch {epoch+1}: 损失值记录错误 - {e}\n")
                    log_file.flush()

        end_time = datetime.datetime.now()
        total_time = end_time - start_time

        print(f"✅ B-SmartCRL实验训练完成!")
        print(f"📅 结束时间: {end_time}")
        print(f"⏱️  总耗时: {total_time}")

        return True



# 使用示例
if __name__ == "__main__":
    model = Zero_shot()
    model.print_experiment_info()
    print("✅ 实验B-SmartCRL模型初始化完成!")
    print("🚀 准备开始B组困难类别的语义增强训练...")

    # 开始训练
    model.train(epochs=800, batch_size=256)

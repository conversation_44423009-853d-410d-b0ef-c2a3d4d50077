#!/usr/bin/env python3
"""
🎯 平衡版本 - 解决判别器过强问题

修复策略：
1. 降低判别器学习率
2. 增加判别器训练频率控制
3. 添加标签平滑
4. 优化网络架构平衡

目标：让判别器和生成器平衡对抗
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from datetime import datetime
from sklearn.svm import LinearSVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
from load_npz_data import load_tep_data_from_npz


class BalancedGenerator(nn.Module):
    """平衡的生成器 - 稍微增强"""
    def __init__(self, noise_dim=50, attr_dim=20, feature_dim=52):
        super().__init__()
        
        self.model = nn.Sequential(
            nn.Linear(noise_dim + attr_dim, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(128, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            
            nn.Linear(128, feature_dim),
            nn.Tanh()
        )
    
    def forward(self, noise, attributes):
        x = torch.cat([noise, attributes], dim=1)
        return self.model(x)


class BalancedDiscriminator(nn.Module):
    """平衡的判别器 - 稍微削弱"""
    def __init__(self, feature_dim=52, attr_dim=20):
        super().__init__()
        
        self.model = nn.Sequential(
            nn.Linear(feature_dim + attr_dim, 128),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            
            nn.Linear(128, 64),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            
            nn.Linear(64, 1)
        )
    
    def forward(self, features, attributes):
        x = torch.cat([features, attributes], dim=1)
        return self.model(x)


class BalancedTrainer:
    """平衡训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
        # 初始化模型
        self.generator = BalancedGenerator().to(device)
        self.discriminator = BalancedDiscriminator().to(device)
        
        # 🔥 平衡的优化器 - 降低判别器学习率
        self.optimizer_g = optim.Adam(self.generator.parameters(), lr=0.0002, betas=(0.5, 0.999))
        self.optimizer_d = optim.Adam(self.discriminator.parameters(), lr=0.0001, betas=(0.5, 0.999))  # 降低判别器学习率
        
        # 损失函数
        self.criterion = nn.BCEWithLogitsLoss()
        
        # 🔥 标签平滑参数
        self.real_label_smooth = 0.9  # 真实标签平滑
        self.fake_label_smooth = 0.1  # 假标签平滑
        
        # 训练历史
        self.history = {'g_loss': [], 'd_loss': [], 'accuracy': []}
        
        print("✅ 平衡训练器初始化完成")
        print(f"   生成器参数: {sum(p.numel() for p in self.generator.parameters()):,}")
        print(f"   判别器参数: {sum(p.numel() for p in self.discriminator.parameters()):,}")
        print(f"   生成器学习率: 0.0002")
        print(f"   判别器学习率: 0.0001 (降低)")
        print(f"   标签平滑: 真实={self.real_label_smooth}, 假={self.fake_label_smooth}")
    
    def load_data(self, group='A'):
        """加载数据"""
        print(f"📊 加载TEP数据集 (分组 {group})...")
        
        test_classes_map = {
            'A': [1, 6, 14],
            'B': [4, 7, 10], 
            'C': [8, 11, 12],
            'D': [2, 3, 5],
            'E': [9, 13, 15]
        }
        
        test_classes = test_classes_map[group]
        
        original_cwd = os.getcwd()
        os.chdir('/home/<USER>/hmt/ACGAN-FG-main')
        
        try:
            (traindata, trainlabel, train_attributelabel,
             testdata, testlabel, test_attributelabel,
             test_attribute_matrix, train_attribute_matrix) = load_tep_data_from_npz(test_classes)
        finally:
            os.chdir(original_cwd)
        
        # 转换为tensor
        self.train_features = torch.FloatTensor(traindata).to(self.device)
        self.train_attributes = torch.FloatTensor(train_attributelabel).to(self.device)
        self.test_features = torch.FloatTensor(testdata).to(self.device)
        self.test_attributes = torch.FloatTensor(test_attributelabel).to(self.device)
        self.test_labels = testlabel
        
        print(f"✅ 数据加载完成: 训练{self.train_features.shape[0]}, 测试{self.test_features.shape[0]}")
    
    def train_step(self, batch_size=32):
        """平衡的训练步骤"""
        # 随机采样
        indices = torch.randperm(self.train_features.shape[0])[:batch_size]
        real_features = self.train_features[indices]
        real_attributes = self.train_attributes[indices]
        
        # ==================== 训练判别器 ====================
        self.optimizer_d.zero_grad()
        
        # 🔥 标签平滑 - 真实样本
        real_output = self.discriminator(real_features, real_attributes)
        real_labels = torch.full_like(real_output, self.real_label_smooth)  # 0.9而不是1.0
        d_loss_real = self.criterion(real_output, real_labels)
        
        # 生成假样本
        noise = torch.randn(batch_size, 50).to(self.device)
        fake_features = self.generator(noise, real_attributes)
        
        # 🔥 标签平滑 - 假样本
        fake_output = self.discriminator(fake_features.detach(), real_attributes)
        fake_labels = torch.full_like(fake_output, self.fake_label_smooth)  # 0.1而不是0.0
        d_loss_fake = self.criterion(fake_output, fake_labels)
        
        d_loss = (d_loss_real + d_loss_fake) / 2
        d_loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), 0.5)
        
        self.optimizer_d.step()
        
        # ==================== 训练生成器 ====================
        self.optimizer_g.zero_grad()
        
        # 生成器希望判别器认为假样本是真的
        fake_output = self.discriminator(fake_features, real_attributes)
        g_loss = self.criterion(fake_output, torch.ones_like(fake_output))  # 生成器目标是1.0
        
        g_loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.generator.parameters(), 0.5)
        
        self.optimizer_g.step()
        
        return g_loss.item(), d_loss.item()
    
    def evaluate(self):
        """评估模型"""
        self.generator.eval()
        
        unique_labels = np.unique(self.test_labels)
        generated_features = []
        generated_labels = []
        
        with torch.no_grad():
            for label in unique_labels:
                mask = self.test_labels == label
                if np.sum(mask) > 0:
                    attr = self.test_attributes[mask][0:1]
                    
                    # 生成1000个样本
                    for _ in range(10):
                        noise = torch.randn(100, 50).to(self.device)
                        attr_batch = attr.repeat(100, 1)
                        fake_features = self.generator(noise, attr_batch)
                        
                        generated_features.append(fake_features.cpu().numpy())
                        generated_labels.extend([label] * 100)
        
        if not generated_features:
            return 0.0
        
        generated_features = np.vstack(generated_features)
        generated_labels = np.array(generated_labels)
        
        # 评估
        classifiers = {
            'LinearSVM': LinearSVC(random_state=42, max_iter=1000),
            'RandomForest': RandomForestClassifier(random_state=42, n_estimators=100),
            'GaussianNB': GaussianNB(),
            'MLPClassifier': MLPClassifier(random_state=42, max_iter=500)
        }
        
        accuracies = {}
        for name, clf in classifiers.items():
            try:
                clf.fit(generated_features, generated_labels)
                pred = clf.predict(self.test_features.cpu().numpy())
                acc = accuracy_score(self.test_labels, pred)
                accuracies[name] = acc * 100
            except:
                accuracies[name] = 0.0
        
        self.generator.train()
        return max(accuracies.values())
    
    def train(self, epochs=100, batch_size=32):
        """训练模型"""
        print(f"🚀 开始平衡训练 ({epochs} epochs)")
        
        best_accuracy = 0.0
        d_train_freq = 1  # 判别器训练频率
        
        for epoch in range(epochs):
            epoch_g_loss = 0.0
            epoch_d_loss = 0.0
            num_batches = len(self.train_features) // batch_size
            
            for batch_idx in range(num_batches):
                # 🔥 动态调整判别器训练频率
                if epoch_d_loss > epoch_g_loss * 2:  # 如果判别器太强
                    d_train_freq = 2  # 减少判别器训练
                else:
                    d_train_freq = 1
                
                # 训练生成器
                g_loss, _ = self.train_step(batch_size)
                epoch_g_loss += g_loss
                
                # 根据频率训练判别器
                if batch_idx % d_train_freq == 0:
                    _, d_loss = self.train_step(batch_size)
                    epoch_d_loss += d_loss
            
            epoch_g_loss /= num_batches
            epoch_d_loss /= (num_batches // d_train_freq)
            
            # 每10个epoch评估一次
            if (epoch + 1) % 10 == 0:
                accuracy = self.evaluate()
                best_accuracy = max(best_accuracy, accuracy)
                
                print(f"Epoch {epoch+1}/{epochs}: G_loss={epoch_g_loss:.4f}, D_loss={epoch_d_loss:.4f}, Acc={accuracy:.2f}%, Best={best_accuracy:.2f}%, D_freq={d_train_freq}")
                
                self.history['g_loss'].append(epoch_g_loss)
                self.history['d_loss'].append(epoch_d_loss)
                self.history['accuracy'].append(accuracy)
        
        return best_accuracy


def main():
    print(f"""
🎯 平衡版本测试
=====================================
⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 平衡策略:
✅ 降低判别器学习率 (0.0002 → 0.0001)
✅ 添加标签平滑 (0.9/0.1)
✅ 动态训练频率控制
✅ 网络架构平衡
🎯 目标: 解决判别器过强问题
=====================================
    """)
    
    try:
        trainer = BalancedTrainer(device='cuda')
        trainer.load_data(group='A')
        best_accuracy = trainer.train(epochs=100, batch_size=32)
        
        print(f"""
🎉 平衡版本训练完成！
=====================================
📊 最终结果:
- 最佳准确率: {best_accuracy:.2f}%
- 与最小版本对比: 33.33% → {best_accuracy:.2f}%
- 与目标对比: 60.97% → {best_accuracy:.2f}%
- 状态: {'✅ 显著改善' if best_accuracy > 50 else '🔧 继续优化'}

💡 分析:
{'- 平衡策略有效，可以进一步优化' if best_accuracy > 40 else '- 需要重新审视方法'}
=====================================
        """)
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

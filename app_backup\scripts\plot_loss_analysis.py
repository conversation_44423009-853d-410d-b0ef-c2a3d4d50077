import re
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def parse_log_file(log_file_path):
    """解析日志文件，提取损失函数数据"""
    
    # 存储数据的字典
    data = {
        'epochs': [],
        'ae_c_loss': [],
        'triplet_loss': [],
        'center_loss': [],
        'combined_loss': [],
        'crl_loss': [],
        'semantic_loss': [],
        'd_loss': [],
        'g_loss': [],
        'lsvm_acc': [],
        'rf_acc': [],
        'nb_acc': [],
        'mlp_acc': []
    }
    
    # 正则表达式模式
    epoch_pattern = r'Epoch\s+(\d+)/2000.*?Time: ([\d:\.]+)'
    loss_pattern = r'AE\+C loss: (\d+(?:\.\d+)?) \| 强化Triplet: ([\d\.]+) \| 强化Center: (\d+(?:\.\d+)?) \| 组合强化: (\d+(?:\.\d+)?) \| 适度CRL: ([\d\.]+) \| 语义增强: ([\d\.]+) \| D loss: ([-\d\.]+) \| G loss: (\d+(?:\.\d+)?)'
    acc_pattern = r'LinearSVM: ([\d\.]+)% \| RandomForest: ([\d\.]+)% \| GaussianNB: ([\d\.]+)% \| MLPClassifier: ([\d\.]+)%'
    
    with open(log_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 按epoch分割内容
    epoch_blocks = re.split(r'Epoch\s+\d+/2000.*?Time:', content)
    
    for i, block in enumerate(epoch_blocks[1:]):  # 跳过第一个空块
        # 提取epoch信息
        epoch_match = re.search(r'^.*?(\d+:\d+:\d+\.\d+)', block)
        if not epoch_match:
            continue
            
        # 提取损失函数信息
        loss_match = re.search(loss_pattern, block)
        if loss_match:
            data['epochs'].append(i)
            data['ae_c_loss'].append(float(loss_match.group(1)))
            data['triplet_loss'].append(float(loss_match.group(2)))
            data['center_loss'].append(float(loss_match.group(3)))
            data['combined_loss'].append(float(loss_match.group(4)))
            data['crl_loss'].append(float(loss_match.group(5)))
            data['semantic_loss'].append(float(loss_match.group(6)))
            data['d_loss'].append(float(loss_match.group(7)))
            data['g_loss'].append(float(loss_match.group(8)))
            
            # 提取准确率信息
            acc_match = re.search(acc_pattern, block)
            if acc_match:
                data['lsvm_acc'].append(float(acc_match.group(1)))
                data['rf_acc'].append(float(acc_match.group(2)))
                data['nb_acc'].append(float(acc_match.group(3)))
                data['mlp_acc'].append(float(acc_match.group(4)))
            else:
                # 如果没有准确率数据，填充NaN
                data['lsvm_acc'].append(np.nan)
                data['rf_acc'].append(np.nan)
                data['nb_acc'].append(np.nan)
                data['mlp_acc'].append(np.nan)
    
    return data

def plot_loss_curves(data, save_path='loss_analysis.png'):
    """绘制损失函数曲线图"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('B-Hybrid实验损失函数分析 (Epochs 0-4)', fontsize=16, fontweight='bold')
    
    epochs = data['epochs']
    
    # 1. AE+C Loss
    axes[0,0].plot(epochs, data['ae_c_loss'], 'b-', linewidth=2, marker='o', markersize=4)
    axes[0,0].set_title('AE+C Loss', fontsize=12, fontweight='bold')
    axes[0,0].set_xlabel('Epoch')
    axes[0,0].set_ylabel('Loss Value')
    axes[0,0].grid(True, alpha=0.3)
    axes[0,0].ticklabel_format(style='scientific', axis='y', scilimits=(0,0))
    
    # 2. 强化损失 (Triplet + Center)
    axes[0,1].plot(epochs, data['triplet_loss'], 'r-', linewidth=2, marker='s', markersize=4, label='强化Triplet')
    axes[0,1].plot(epochs, data['center_loss'], 'g-', linewidth=2, marker='^', markersize=4, label='强化Center')
    axes[0,1].plot(epochs, data['combined_loss'], 'purple', linewidth=2, marker='d', markersize=4, label='组合强化')
    axes[0,1].set_title('强化损失函数', fontsize=12, fontweight='bold')
    axes[0,1].set_xlabel('Epoch')
    axes[0,1].set_ylabel('Loss Value')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    axes[0,1].ticklabel_format(style='scientific', axis='y', scilimits=(0,0))
    
    # 3. 正则化损失 (CRL + Semantic)
    axes[0,2].plot(epochs, data['crl_loss'], 'orange', linewidth=2, marker='v', markersize=4, label='适度CRL')
    axes[0,2].plot(epochs, data['semantic_loss'], 'brown', linewidth=2, marker='<', markersize=4, label='语义增强')
    axes[0,2].set_title('正则化损失函数', fontsize=12, fontweight='bold')
    axes[0,2].set_xlabel('Epoch')
    axes[0,2].set_ylabel('Loss Value')
    axes[0,2].legend()
    axes[0,2].grid(True, alpha=0.3)
    
    # 4. 判别器损失
    axes[1,0].plot(epochs, data['d_loss'], 'cyan', linewidth=2, marker='h', markersize=4)
    axes[1,0].set_title('判别器损失 (D Loss)', fontsize=12, fontweight='bold')
    axes[1,0].set_xlabel('Epoch')
    axes[1,0].set_ylabel('Loss Value')
    axes[1,0].grid(True, alpha=0.3)
    axes[1,0].axhline(y=0, color='k', linestyle='--', alpha=0.5)
    
    # 5. 生成器损失
    axes[1,1].plot(epochs, data['g_loss'], 'magenta', linewidth=2, marker='p', markersize=4)
    axes[1,1].set_title('生成器损失 (G Loss)', fontsize=12, fontweight='bold')
    axes[1,1].set_xlabel('Epoch')
    axes[1,1].set_ylabel('Loss Value')
    axes[1,1].grid(True, alpha=0.3)
    axes[1,1].ticklabel_format(style='scientific', axis='y', scilimits=(0,0))
    
    # 6. 分类准确率
    valid_epochs = [e for e, acc in zip(epochs, data['lsvm_acc']) if not np.isnan(acc)]
    valid_lsvm = [acc for acc in data['lsvm_acc'] if not np.isnan(acc)]
    valid_rf = [acc for acc in data['rf_acc'] if not np.isnan(acc)]
    valid_nb = [acc for acc in data['nb_acc'] if not np.isnan(acc)]
    valid_mlp = [acc for acc in data['mlp_acc'] if not np.isnan(acc)]
    
    if valid_epochs:
        axes[1,2].plot(valid_epochs, valid_lsvm, 'b-', linewidth=2, marker='o', markersize=4, label='LinearSVM')
        axes[1,2].plot(valid_epochs, valid_rf, 'r-', linewidth=2, marker='s', markersize=4, label='RandomForest')
        axes[1,2].plot(valid_epochs, valid_nb, 'g-', linewidth=2, marker='^', markersize=4, label='GaussianNB')
        axes[1,2].plot(valid_epochs, valid_mlp, 'orange', linewidth=2, marker='d', markersize=4, label='MLPClassifier')
    
    axes[1,2].set_title('分类准确率 (%)', fontsize=12, fontweight='bold')
    axes[1,2].set_xlabel('Epoch')
    axes[1,2].set_ylabel('Accuracy (%)')
    axes[1,2].legend()
    axes[1,2].grid(True, alpha=0.3)
    axes[1,2].set_ylim([30, 40])  # 基于数据范围设置
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig

def print_analysis_summary(data):
    """打印分析摘要"""
    print("=" * 60)
    print("B-Hybrid实验损失函数分析摘要")
    print("=" * 60)
    
    if len(data['epochs']) > 0:
        print(f"📊 训练轮次: Epoch 0-{max(data['epochs'])}")
        print(f"📈 数据点数: {len(data['epochs'])} 个epoch")
        
        print("\n🔥 损失函数变化趋势:")
        print(f"  • AE+C Loss: {data['ae_c_loss'][0]:,.0f} → {data['ae_c_loss'][-1]:,.0f} ({((data['ae_c_loss'][-1]/data['ae_c_loss'][0]-1)*100):+.1f}%)")
        print(f"  • 强化Triplet: {data['triplet_loss'][0]:,.1f} → {data['triplet_loss'][-1]:,.1f} ({((data['triplet_loss'][-1]/data['triplet_loss'][0]-1)*100):+.1f}%)")
        print(f"  • 强化Center: {data['center_loss'][0]:,.0f} → {data['center_loss'][-1]:,.0f} ({((data['center_loss'][-1]/data['center_loss'][0]-1)*100):+.1f}%)")
        print(f"  • G Loss: {data['g_loss'][0]:,.0f} → {data['g_loss'][-1]:,.0f} ({((data['g_loss'][-1]/data['g_loss'][0]-1)*100):+.1f}%)")
        print(f"  • D Loss: {data['d_loss'][0]:+.1f} → {data['d_loss'][-1]:+.1f}")
        
        # 分析准确率
        valid_accs = [acc for acc in data['lsvm_acc'] if not np.isnan(acc)]
        if valid_accs:
            print(f"\n🎯 分类性能:")
            print(f"  • 所有分类器准确率: {valid_accs[0]:.2f}% (持续稳定)")
            print(f"  • 性能状态: 基准水平 (33.33% = 随机猜测)")
        
        print(f"\n⚠️  关键观察:")
        print(f"  • 权重平衡策略有效 - 避免了数值爆炸")
        print(f"  • Center Loss大幅下降 - 聚类效果增强")
        print(f"  • D Loss转负 - 判别器训练正常")
        print(f"  • 分类性能未提升 - 需要更多训练轮次")

def main():
    """主函数"""
    log_file_path = "/home/<USER>/hmt/ACGAN-FG-main/logs/B_Hybrid_20250715_1354.log"
    
    print("🔍 开始解析日志文件...")
    data = parse_log_file(log_file_path)
    
    if len(data['epochs']) == 0:
        print("❌ 未找到有效的训练数据！")
        return
    
    print(f"✅ 成功解析 {len(data['epochs'])} 个epoch的数据")
    
    # 打印分析摘要
    print_analysis_summary(data)
    
    # 绘制图表
    print("\n📊 正在生成损失函数图表...")
    fig = plot_loss_curves(data, 'B_Hybrid_loss_analysis.png')
    print("✅ 图表已保存为 'B_Hybrid_loss_analysis.png'")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
ACGAN-FG性能验证脚本

目标: 验证ACGAN-FG是否能达到文献中声称的89%准确率
方法: 
1. 运行原始ACGAN-FG代码
2. 运行增强版ACGAN-FG代码  
3. 对比结果并分析差异
4. 提供改进建议
"""

import os
import sys
import datetime
import numpy as np
import subprocess

def run_original_acgan_fg():
    """运行原始ACGAN-FG代码"""
    print("🔄 运行原始ACGAN-FG代码...")
    print("=" * 50)
    
    try:
        # 运行原始代码
        result = subprocess.run([
            sys.executable, "ACGAN_FG.py"
        ], capture_output=True, text=True, timeout=3600)  # 1小时超时
        
        print("✅ 原始ACGAN-FG运行完成")
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout[-2000:])  # 显示最后2000字符
            
        if result.stderr:
            print("错误输出:")
            print(result.stderr[-1000:])  # 显示最后1000字符
            
        return result.returncode == 0, result.stdout, result.stderr
        
    except subprocess.TimeoutExpired:
        print("❌ 原始ACGAN-FG运行超时")
        return False, "", "运行超时"
    except Exception as e:
        print(f"❌ 原始ACGAN-FG运行失败: {e}")
        return False, "", str(e)

def analyze_results(stdout, stderr):
    """分析运行结果"""
    print("\n📊 结果分析:")
    print("=" * 50)
    
    # 提取准确率信息
    accuracies = {
        'LinearSVM': [],
        'RandomForest': [],
        'GaussianNB': [],
        'MLP': []
    }
    
    lines = stdout.split('\n')
    for line in lines:
        if 'Accuracy_lsvm:' in line:
            try:
                # 解析准确率
                parts = line.split()
                for i, part in enumerate(parts):
                    if 'Accuracy_lsvm:' in part:
                        acc_lsvm = float(parts[i+1].rstrip(']'))
                        accuracies['LinearSVM'].append(acc_lsvm)
                    elif 'Accuracy_nrf:' in part:
                        acc_nrf = float(parts[i+1].rstrip(']'))
                        accuracies['RandomForest'].append(acc_nrf)
                    elif 'Accuracy_pnb:' in part:
                        acc_pnb = float(parts[i+1].rstrip(']'))
                        accuracies['GaussianNB'].append(acc_pnb)
                    elif 'Accuracy_mlp:' in part:
                        acc_mlp = float(parts[i+1].rstrip(']'))
                        accuracies['MLP'].append(acc_mlp)
            except (ValueError, IndexError):
                continue
    
    # 分析最终结果
    print("📈 准确率变化趋势:")
    for classifier, acc_list in accuracies.items():
        if acc_list:
            max_acc = max(acc_list)
            final_acc = acc_list[-1] if acc_list else 0
            print(f"{classifier:<15}: 最高{max_acc:.4f} ({max_acc*100:.2f}%), 最终{final_acc:.4f} ({final_acc*100:.2f}%)")
        else:
            print(f"{classifier:<15}: 无数据")
    
    # 与文献对比
    print(f"\n🎯 与文献结果对比 (E组):")
    literature_results = {
        'LinearSVM': 0.6867,
        'RandomForest': 0.8023, 
        'GaussianNB': 0.7144,
        'MLP': 0.8906  # 文献中的89.06%
    }
    
    print(f"{'分类器':<15} {'文献结果':<10} {'我们结果':<10} {'差距':<10} {'状态'}")
    print("-" * 55)
    
    for classifier, lit_acc in literature_results.items():
        if accuracies[classifier]:
            our_acc = max(accuracies[classifier])
            diff = our_acc - lit_acc
            status = "✅" if diff > -0.1 else "⚠️" if diff > -0.2 else "❌"
            print(f"{classifier:<15} {lit_acc*100:>8.2f}% {our_acc*100:>8.2f}% {diff*100:>+8.1f}% {status}")
        else:
            print(f"{classifier:<15} {lit_acc*100:>8.2f}% {'N/A':>8} {'N/A':>8} ❌")
    
    # 检查是否达到文献水平
    mlp_max = max(accuracies['MLP']) if accuracies['MLP'] else 0
    if mlp_max >= 0.85:
        print(f"\n🎉 成功! MLP达到{mlp_max*100:.2f}%, 接近文献的89.06%")
        return True
    elif mlp_max >= 0.70:
        print(f"\n✅ 良好! MLP达到{mlp_max*100:.2f}%, 但仍低于文献结果")
        return False
    else:
        print(f"\n❌ 较差! MLP仅达到{mlp_max*100:.2f}%, 远低于文献结果")
        return False

def provide_improvement_suggestions():
    """提供改进建议"""
    print(f"\n💡 改进建议:")
    print("=" * 50)
    
    suggestions = [
        "1. 检查数据预处理: 确保与文献中的预处理方式一致",
        "2. 验证属性定义: 确认20个属性的定义与文献完全一致", 
        "3. 调整网络参数: 尝试更深的网络和更多的训练轮次",
        "4. 优化损失函数: 调整各损失项的权重系数",
        "5. 改进特征生成: 使用更好的噪声分布和生成策略",
        "6. 增强分类器: 使用更强的分类器配置",
        "7. 数据增强: 尝试更多的数据增强技术",
        "8. 超参数调优: 系统性地调优所有超参数",
        "9. 集成方法: 尝试多模型集成",
        "10. 联系作者: 直接联系论文作者获取详细实现细节"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")

def main():
    """主函数"""
    print("🚀 ACGAN-FG性能验证开始")
    print(f"⏰ 开始时间: {datetime.datetime.now()}")
    print("🎯 目标: 验证是否能复现文献中E组89%的准确率")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        "ACGAN_FG.py",
        "scripts/test.py", 
        "scripts/read_data.py",
        "test_enhanced_acgan_fg.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return
    
    print("✅ 所有必要文件存在")
    
    # 运行原始ACGAN-FG
    success, stdout, stderr = run_original_acgan_fg()
    
    if not success:
        print("❌ ACGAN-FG运行失败")
        print(f"错误信息: {stderr}")
        return
    
    # 分析结果
    reached_target = analyze_results(stdout, stderr)
    
    # 提供改进建议
    provide_improvement_suggestions()
    
    # 总结
    print(f"\n📋 验证总结:")
    print("=" * 50)
    if reached_target:
        print("🎉 验证成功! ACGAN-FG达到了接近文献的性能")
        print("✅ 可以继续基于此方法进行改进研究")
    else:
        print("⚠️ 验证部分成功! ACGAN-FG未完全达到文献性能")
        print("🔧 建议按照上述建议进行改进")
        print("📚 考虑重新审视论文实现细节")
    
    print(f"⏰ 结束时间: {datetime.datetime.now()}")

if __name__ == "__main__":
    main()

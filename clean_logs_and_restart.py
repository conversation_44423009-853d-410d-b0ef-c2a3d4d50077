#!/usr/bin/env python3
"""
清理旧日志并重新开始训练脚本
按照用户要求删除tensorboard_logs和logs文件夹下的相关文件
"""

import os
import shutil
import glob
import datetime

def clean_tensorboard_logs():
    """清理TensorBoard日志"""
    tensorboard_dir = "tensorboard_logs"
    if os.path.exists(tensorboard_dir):
        print(f"🧹 清理TensorBoard日志目录: {tensorboard_dir}")
        try:
            # 删除所有子目录和文件
            for item in os.listdir(tensorboard_dir):
                item_path = os.path.join(tensorboard_dir, item)
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                    print(f"   ✅ 删除目录: {item}")
                else:
                    os.remove(item_path)
                    print(f"   ✅ 删除文件: {item}")
        except Exception as e:
            print(f"   ❌ 清理TensorBoard日志失败: {e}")
    else:
        print(f"📁 TensorBoard日志目录不存在: {tensorboard_dir}")

def clean_logs_directory():
    """清理logs目录"""
    logs_dir = "logs"
    if os.path.exists(logs_dir):
        print(f"🧹 清理logs目录: {logs_dir}")
        try:
            # 删除所有.log文件
            log_files = glob.glob(os.path.join(logs_dir, "*.log"))
            for log_file in log_files:
                os.remove(log_file)
                print(f"   ✅ 删除日志文件: {os.path.basename(log_file)}")
            
            # 删除其他相关文件
            other_files = glob.glob(os.path.join(logs_dir, "*"))
            for file_path in other_files:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    print(f"   ✅ 删除文件: {os.path.basename(file_path)}")
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"   ✅ 删除目录: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"   ❌ 清理logs目录失败: {e}")
    else:
        print(f"📁 logs目录不存在: {logs_dir}")

def clean_root_log_files():
    """清理根目录下的日志文件"""
    print(f"🧹 清理根目录下的日志文件...")
    
    # 清理.log文件
    log_patterns = ["*.log", "*_*.log", "*Enhanced*.log", "*Hybrid*.log"]
    for pattern in log_patterns:
        log_files = glob.glob(pattern)
        for log_file in log_files:
            try:
                os.remove(log_file)
                print(f"   ✅ 删除日志文件: {log_file}")
            except Exception as e:
                print(f"   ❌ 删除失败 {log_file}: {e}")

def clean_model_checkpoints():
    """清理模型检查点（可选）"""
    print(f"🧹 检查模型检查点...")
    
    model_patterns = ["models/*.h5", "models/*.keras", "*.h5", "*.keras"]
    found_models = False
    
    for pattern in model_patterns:
        model_files = glob.glob(pattern)
        for model_file in model_files:
            found_models = True
            print(f"   📁 发现模型文件: {model_file}")
    
    if found_models:
        response = input("❓ 是否也删除模型文件？(y/N): ").strip().lower()
        if response == 'y':
            for pattern in model_patterns:
                model_files = glob.glob(pattern)
                for model_file in model_files:
                    try:
                        os.remove(model_file)
                        print(f"   ✅ 删除模型文件: {model_file}")
                    except Exception as e:
                        print(f"   ❌ 删除失败 {model_file}: {e}")
        else:
            print("   ⏭️ 保留模型文件")
    else:
        print("   📁 未发现模型文件")

def create_fresh_directories():
    """创建新的干净目录"""
    print(f"📁 创建新的日志目录...")
    
    directories = ["tensorboard_logs", "logs"]
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"   ✅ 创建目录: {directory}")
        else:
            print(f"   📁 目录已存在: {directory}")

def main():
    """主函数"""
    print("🚀 清理旧日志并准备重新训练")
    print(f"⏰ 开始时间: {datetime.datetime.now()}")
    print("=" * 60)
    
    # 确认操作
    print("⚠️ 此操作将删除以下内容:")
    print("   - tensorboard_logs/ 目录下的所有文件")
    print("   - logs/ 目录下的所有文件")
    print("   - 根目录下的所有.log文件")
    print("   - 可选：模型检查点文件")
    
    response = input("\n❓ 确认继续？(y/N): ").strip().lower()
    if response != 'y':
        print("❌ 操作已取消")
        return
    
    print("\n🧹 开始清理...")
    
    # 执行清理
    clean_tensorboard_logs()
    clean_logs_directory()
    clean_root_log_files()
    clean_model_checkpoints()
    
    # 创建新目录
    create_fresh_directories()
    
    print(f"\n✅ 清理完成!")
    print("🎯 现在可以从第0个epoch开始重新训练")
    print("💡 建议运行命令:")
    print("   docker exec -it acgan-container python scripts/acgan_triplet_Hybrid.py")
    
    print(f"⏰ 完成时间: {datetime.datetime.now()}")

if __name__ == "__main__":
    main()

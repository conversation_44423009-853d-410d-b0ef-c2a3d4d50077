import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,LayerNormalization,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from test_fusion import feature_generation_and_diagnosis_with_fusion
from sklearn.preprocessing import MinMaxScaler
import shutil
import json


# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    # 限制TensorFlow只使用第一个GPU
    tf.config.set_visible_devices(gpus[0], 'GPU')
    # 设置显存按需增长
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    # 显存增长必须在GPU初始化之前设置
    print(e)


def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = LayerNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = LayerNormalization()(y)
    
    # 如果输入和输出维度不同，需要一个线性投影
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        # input_shape is (batch_size, feature_dim)
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        # Reshape for matrix multiplication
        # Temporarily add a "sequence length" of 1
        # x_reshaped shape: (batch_size, 1, feature_dim)
        x_reshaped = K.expand_dims(x, axis=1)

        # Q, K, V projections
        q = self.query(x_reshaped)  # (batch_size, 1, feature_dim/8)
        k = self.key(x_reshaped)    # (batch_size, 1, feature_dim/8)
        v = self.value(x_reshaped)  # (batch_size, 1, feature_dim)

        # Attention scores
        attention_scores = K.batch_dot(q, k, axes=[2, 2]) # (batch_size, 1, 1)
        attention_probs = K.softmax(attention_scores)

        # Apply attention
        context = K.batch_dot(attention_probs, v) # (batch_size, 1, feature_dim)
        
        # Remove the temporary dimension
        context = K.squeeze(context, axis=1)

        # Add back to original input (residual connection)
        return x + self.gamma * context

class CrossAttentionFusion(Layer):
    """交叉注意力融合层 - 第三个创新点的核心实现"""
    def __init__(self, units, **kwargs):
        super(CrossAttentionFusion, self).__init__(**kwargs)
        self.units = units

    def build(self, input_shape):
        data_feature_dim = input_shape[0][-1]
        semantic_feature_dim = input_shape[1][-1]
        
        self.query = Dense(self.units, use_bias=False)
        self.key = Dense(self.units, use_bias=False)
        self.value = Dense(self.units, use_bias=False)

        self.out_dense = Dense(data_feature_dim)
        super(CrossAttentionFusion, self).build(input_shape)

    def call(self, inputs):
        data_features, semantic_features = inputs
        
        # 扩展维度以进行注意力计算
        q = self.query(K.expand_dims(data_features, axis=1))
        k = self.key(K.expand_dims(semantic_features, axis=1))
        v = self.value(K.expand_dims(semantic_features, axis=1))
        
        # 计算注意力权重
        attention_scores = tf.matmul(q, k, transpose_b=True)
        attention_probs = tf.nn.softmax(attention_scores)
        
        # 应用注意力权重
        context = tf.matmul(attention_probs, v)
        context = K.squeeze(context, axis=1)
        
        # 将注意力上下文与原始数据特征结合
        fused_output = self.out_dense(concatenate([data_features, context]))
        return fused_output

class ModelCheckpointManager:
    """智能模型检查点管理器"""
    def __init__(self, save_path="best_dr_net_stable_checkpoints/"):
        self.save_path = save_path
        self.best_accuracy = 0.0
        self.best_epoch = 0
        self.consecutive_decline = 0
        self.patience = 800 # 早停的耐心轮数 - 已根据用户要求调整为800
        self.best_model_path = None # 唯一最佳模型的路径
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
        
    def should_save_and_update(self, current_accuracy, epoch):
        """判断是否应该保存，并更新统计"""
        is_new_best = current_accuracy > self.best_accuracy
        
        if is_new_best:
            # 清理旧的最佳模型
            if self.best_model_path and os.path.exists(self.best_model_path):
                print(f"🧹 清理旧的最佳模型: {self.best_model_path}")
                shutil.rmtree(self.best_model_path)

            self.best_accuracy = current_accuracy
            self.best_epoch = epoch
            self.consecutive_decline = 0
            print(f"🏆 新最佳准确率: {current_accuracy:.4f} (Epoch {epoch})")
            return True, "best"
        else:
            self.consecutive_decline += 1
            return False, "normal"
    
    def should_early_stop(self):
        """判断是否应该早停"""
        return self.consecutive_decline >= self.patience

class DR_ZeroShot():
    """判别器-回归器对偶网络 (Discriminator-Regressor Dual Network)"""
    def __init__(self):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.attribute_dim=20
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        # 原有损失权重
        self.lambda_cla = 10 
        self.lambda_triplet = 10 
        self.lambda_crl = 0.01 
        self.lambda_fusion = 1.0
        
        # 🔥 DR-Net新增损失权重
        self.lambda_d_reg = 0.1  # 判别器回归损失权重 - 保守起步
        self.lambda_g_reg = 0.5  # 生成器回归损失权重 - 稍高以引导生成器
        
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        # DR-Net训练阶段控制
        self.dr_phase = 1  # 1: 基础DR, 2: 生成器约束, 3: 动态权重
        
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For triplet loss
        self.fusion_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For fusion
        
        # 智能检查点管理器
        self.checkpoint_manager = ModelCheckpointManager(save_path="best_dr_net_stable_checkpoints/")
        self.accuracy_history = []
        
        # 🔥 构建DR-Net架构
        self.autoencoder= self.build_autoencoder()
        self.dr_discriminator = self.build_dr_discriminator()  # 新的双输出判别器
        self.g = self.build_generator()
        self.c= self.build_classifier()
        self.fusion_net = self.build_fusion_network()
        
    def build_autoencoder(self):
        """构建自编码器 - 与原版保持一致"""
        sample = Input(shape=self.sample_shape)     
        a0=sample

        # Encoder
        a1=Dense(100)(a0)
        a1=LeakyReLU(alpha=0.2)(a1)
        a1=LayerNormalization()(a1)

        a2=Dense(200)(a1)
        a2=LeakyReLU(alpha=0.2)(a2)
        a2=LayerNormalization()(a2)

        a3=Dense(256)(a2)
        a3=LeakyReLU(alpha=0.2)(a3)
        a3=LayerNormalization()(a3)
        feature=a3

        # Decoder
        a4=Dense(200)(feature)
        a4=LeakyReLU(alpha=0.2)(a4)
        a4=LayerNormalization()(a4)

        a5=Dense(100)(a4)
        a5=LeakyReLU(alpha=0.2)(a5)
        a5=LayerNormalization()(a5)

        a6=Dense(52)(a5)
        a6=LeakyReLU(alpha=0.2)(a6)
        a6=LayerNormalization()(a6)
        output_sample=a6

        # Autoencoder Model
        autoencoder = Model(sample,[feature, output_sample])
        # We only need the encoder part for triplet loss feature extraction
        self.encoder = Model(sample, feature)
        return autoencoder    
        
    def build_dr_discriminator(self):
        """🔥 构建DR双输出判别器 - 核心创新点"""
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        # 共享特征提取层
        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        d1 = Dropout(0.1)(d1)  # 增加泛化能力
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)
        d2 = Dropout(0.1)(d2)

        # 🔥 输出头1：传统的真假判断
        validity = SpectralNormalization(Dense(1, name='validity_head'))(d2)
        
        # 🔥 输出头2：属性回归 - DR-Net的核心创新
        # 使用独立的处理层来增强回归能力
        reg_hidden = SpectralNormalization(Dense(64))(d2)
        reg_hidden = LeakyReLU(alpha=0.2)(reg_hidden)
        reconstructed_attr = SpectralNormalization(Dense(20, activation='sigmoid', name='regression_head'))(reg_hidden)

        return Model([sample_input, attribute], [validity, reconstructed_attr], name="DR_Discriminator")

    def build_generator(self):
        """构建生成器 - 与原版保持一致"""
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        noise_embedding = Flatten()(noise)
        attribute_embedding = Dense(self.latent_dim)(attribute)
        
        g_input = concatenate([noise_embedding, attribute_embedding])

        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = LayerNormalization()(g1)

        g2 = residual_block(g1, 256) 
        g3 = residual_block(g2, 256) 
        
        g3_attention = SelfAttention()(g3)
        
        generated_feature = Dense(256)(g3_attention)
        generated_feature = BatchNormalization()(generated_feature)

        return Model([noise,attribute],generated_feature)
    
    def build_classifier(self):
        """构建分类器 - 与原版保持一致"""
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        
        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        hidden_ouput=c2
               
        c3 = Dense(20,activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])

    def build_fusion_network(self):
        """构建交叉注意力融合网络"""
        data_input = Input(shape=self.feature_shape, name="data_input")
        semantic_input = Input(shape=(self.attribute_dim,), name="semantic_input")
        
        # 交叉注意力融合
        fused_output = CrossAttentionFusion(units=128)([data_input, semantic_input])
        
        # 额外的处理层
        fused_output = Dense(256)(fused_output)
        fused_output = LeakyReLU(alpha=0.2)(fused_output)
        fused_output = LayerNormalization()(fused_output)
        
        return Model([data_input, semantic_input], fused_output, name="FusionNetwork")

    def save_model_checkpoint(self, epoch, accuracy, checkpoint_type="best"):
        """保存模型检查点 - 使用权重保存"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if checkpoint_type == "best":
            checkpoint_path = os.path.join(self.checkpoint_manager.save_path, "checkpoint_best")
            if os.path.exists(checkpoint_path):
                shutil.rmtree(checkpoint_path)
        else:
            checkpoint_path = os.path.join(self.checkpoint_manager.save_path, f"checkpoint_{checkpoint_type}_{epoch}_{timestamp}")

        os.makedirs(checkpoint_path, exist_ok=True)
        
        try:
            self.autoencoder.save_weights(os.path.join(checkpoint_path, "autoencoder.weights.h5"))
            self.g.save_weights(os.path.join(checkpoint_path, "generator.weights.h5"))
            self.dr_discriminator.save_weights(os.path.join(checkpoint_path, "dr_discriminator.weights.h5"))
            self.c.save_weights(os.path.join(checkpoint_path, "classifier.weights.h5"))
            self.fusion_net.save_weights(os.path.join(checkpoint_path, "fusion_net.weights.h5"))
            
            checkpoint_info = {
                "epoch": epoch,
                "accuracy": float(accuracy),
                "model_type": 'dr_net_stable'
            }
            
            with open(os.path.join(checkpoint_path, "checkpoint_info.json"), 'w') as f:
                json.dump(checkpoint_info, f, indent=2)
            
            print(f"💾 模型权重检查点已保存: {checkpoint_path}")
            
            if checkpoint_type == "best":
                self.checkpoint_manager.best_model_path = checkpoint_path
            
            return checkpoint_path
            
        except Exception as e:
            print(f"❌ 模型保存失败: {e}")
            return None

    def load_best_model(self):
        """加载最优模型 - 使用权重加载"""
        checkpoint_path = self.checkpoint_manager.best_model_path
        
        if not checkpoint_path or not os.path.exists(checkpoint_path):
             print(f"❌ 无法找到最佳模型路径: {checkpoint_path}")
             return False
        
        try:
            print(f"📂 从唯一最佳路径加载模型权重: {checkpoint_path}")
            self.autoencoder.load_weights(os.path.join(checkpoint_path, "autoencoder.weights.h5"))
            self.g.load_weights(os.path.join(checkpoint_path, "generator.weights.h5"))
            self.dr_discriminator.load_weights(os.path.join(checkpoint_path, "dr_discriminator.weights.h5"))
            self.c.load_weights(os.path.join(checkpoint_path, "classifier.weights.h5"))
            self.fusion_net.load_weights(os.path.join(checkpoint_path, "fusion_net.weights.h5"))
            print("✅ 权重加载成功")
            return True
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False

    def test_with_best_model(self, testdata, test_attributelabel):
        """使用最优模型进行测试"""
        if self.load_best_model():
            print("🧪 使用最优模型进行测试...")
            accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis_with_fusion(
                self, 2000, testdata, test_attributelabel, self.fusion_net)
            
            current_best = max(accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp)
            print(f"🎯 最优模型复现准确率: {current_best:.4f}")
            return current_best
        else:
            print("❌ 无法加载最优模型进行测试")
            return 0.0

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)) + 1e-8)
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self,current_batch_features,y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(
                y_true, pred_attribute)
        
        mi_penalty=0    
        if self.bound == True:    
          mi_penalty = self.mi_penalty_loss(
              current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def cycle_rank_loss(self, anchor, positive, negative):
        # We can reuse the triplet loss logic for cycle rank consistency
        # The goal is that the reconstructed feature (positive) is closer to the original fake feature (anchor)
        # than any other generated feature from a different class (negative).
        return self.triplet_loss(anchor, positive, negative)
    
    # 🔥 DR-Net 核心损失函数
    def discriminator_regression_loss(self, true_attributes, predicted_attributes):
        """判别器属性回归损失"""
        return tf.keras.losses.binary_crossentropy(true_attributes, predicted_attributes)
    
    def generator_regression_loss(self, target_attributes, reconstructed_attributes):
        """生成器回归一致性损失"""
        return tf.keras.losses.binary_crossentropy(target_attributes, reconstructed_attributes)
    
    def train(self, epochs, batch_size, traindata, train_attributelabel, train_classlabel, train_X_by_class, testdata, test_attributelabel, log_file=None):
        
        start_time = datetime.datetime.now()
        
        accuracy_list_1=[]
        accuracy_list_2=[]
        accuracy_list_3=[]
        accuracy_list_4=[]
        
        valid = -np.ones((batch_size,1) )
        fake = np.ones((batch_size,1) )
        
        num_batches=int(traindata.shape[0]/batch_size)

        print(f"🔥 开始DR-Net训练 - 阶段 {self.dr_phase}")
        print(f"📊 DR权重设置: lambda_d_reg={self.lambda_d_reg}, lambda_g_reg={self.lambda_g_reg}")
               
        for epoch in range(epochs):
            
            for batch_i in range(num_batches):
                
                start_i =batch_i * batch_size
                end_i=(batch_i + 1) * batch_size
                
                train_x=traindata[start_i:end_i]
                train_y=train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # Autoencoder and Classifier Training (Same as before)
                self.autoencoder.trainable = True
                self.c.trainable = True # Train C together with AE now
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                  feature, output_sample=self.autoencoder(train_x)
                  autoencoder_loss=mean_squared_error(train_x,output_sample)      

                  hidden_ouput_c,predict_attribute_c=self.c(feature)
                  c_loss=self.classification_loss(feature,train_y, hidden_ouput_c, predict_attribute_c)

                  total_ac_loss = autoencoder_loss + c_loss
                  # 软检查：记录但不中断训练
                  if tf.reduce_any(tf.math.is_nan(total_ac_loss)) or tf.reduce_any(tf.math.is_inf(total_ac_loss)):
                      print(f"⚠️ Warning: AE+C loss contains NaN/Inf at epoch {epoch}, batch {batch_i}")
                      total_ac_loss = tf.where(tf.math.is_finite(total_ac_loss), total_ac_loss, 0.0)

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c

                # Triplet Loss Metric Learning
                self.autoencoder.trainable = True # Encoder is part of metric learning
                self.c.trainable = True
                
                # Sample triplets
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []
                for label in train_labels:
                    pos_class_samples = train_X_by_class[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    
                    neg_class = np.random.choice([c for c in range(15) if c != label])
                    neg_class_samples = train_X_by_class[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)
                    # 软检查：记录但不中断训练
                    if tf.reduce_any(tf.math.is_nan(m_loss)) or tf.reduce_any(tf.math.is_inf(m_loss)):
                        print(f"⚠️ Warning: M loss contains NaN/Inf at epoch {epoch}, batch {batch_i}")
                        m_loss = tf.where(tf.math.is_finite(m_loss), m_loss, 0.0)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                # 交叉注意力融合网络训练
                self.fusion_net.trainable = True
                
                with tf.GradientTape() as tape_fusion:
                    # 获取特征和语义表示
                    batch_features = self.encoder(train_x)
                    _, semantic_features = self.c(batch_features)
                    
                    # 通过融合网络
                    fused_features = self.fusion_net([batch_features, semantic_features])
                    
                    # 融合特征应该能够重构原始特征
                    fusion_loss = tf.reduce_mean(tf.square(fused_features - batch_features))
                    # 软检查：记录但不中断训练
                    if tf.reduce_any(tf.math.is_nan(fusion_loss)) or tf.reduce_any(tf.math.is_inf(fusion_loss)):
                        print(f"⚠️ Warning: Fusion loss contains NaN/Inf at epoch {epoch}, batch {batch_i}")
                        fusion_loss = tf.where(tf.math.is_finite(fusion_loss), fusion_loss, 0.0)

                grads_fusion = tape_fusion.gradient(fusion_loss, self.fusion_net.trainable_weights)
                self.fusion_optimizer.apply_gradients(zip(grads_fusion, self.fusion_net.trainable_weights))

                # 🔥 DR判别器训练 - 核心创新点
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.dr_discriminator.trainable = True
                self.g.trainable = False
                self.fusion_net.trainable = False

                for _ in range(self.n_critic):
                  with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature = self.g([noise,train_y])
                    real_feature = self.encoder(train_x)
        
                    # 🔥 DR判别器双输出
                    real_validity, real_reconstructed_attr = self.dr_discriminator([real_feature, train_y])
                    fake_validity, fake_reconstructed_attr = self.dr_discriminator([fake_feature, train_y])
                                           
                    # 原有对抗损失
                    d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                    d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                    d_loss_adv = d_loss_real + d_loss_fake
                    
                    # 🔥 DR新增：属性回归损失 (只在真实样本上计算)
                    d_loss_regression = tf.reduce_mean(
                        self.discriminator_regression_loss(train_y, real_reconstructed_attr)
                    )
                    
                    # DR总损失
                    d_loss = d_loss_adv + self.lambda_d_reg * d_loss_regression
                    # 软检查：记录但不中断训练
                    if tf.reduce_any(tf.math.is_nan(d_loss)) or tf.reduce_any(tf.math.is_inf(d_loss)):
                        print(f"⚠️ Warning: DR D loss contains NaN/Inf at epoch {epoch}, batch {batch_i}")
                        d_loss = tf.where(tf.math.is_finite(d_loss), d_loss, 0.0)
                  
                  grads_d = tape_d.gradient(d_loss, self.dr_discriminator.trainable_weights)
                  self.d_optimizer.apply_gradients(zip(grads_d, self.dr_discriminator.trainable_weights))

                # 🔥 生成器训练 with DR增强
                self.dr_discriminator.trainable = False               
                self.g.trainable = True
                self.fusion_net.trainable = False  # 在G训练时冻结fusion
                
                with tf.GradientTape() as tape_g:
                  noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                  Fake_feature_g = self.g([noise_g,train_y])
                  Fake_validity_g, Fake_reconstructed_attr_g = self.dr_discriminator([Fake_feature_g, train_y])  # 🔥 双输出
                  adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
            
                  fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                  classification_loss = self.classification_loss(Fake_feature_g,train_y, fake_hidden_ouput_g, Fake_classification_g)
                  
                  # Triplet loss for Generator
                  g_anchor_features = Fake_feature_g
                  g_positive_features = self.encoder(positive_samples) # Use same positive samples
                  g_negative_features = self.encoder(negative_samples) # Use same negative samples
                  triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                  
                  # 融合增强：生成的特征通过融合网络应该能产生更好的表示
                  fused_fake_features = self.fusion_net([Fake_feature_g, Fake_classification_g])
                  fusion_enhancement_loss = tf.reduce_mean(tf.square(fused_fake_features - Fake_feature_g))
                  
                  # 🔥 DR新增：生成器回归一致性损失
                  g_regression_loss = tf.reduce_mean(
                      self.generator_regression_loss(train_y, Fake_reconstructed_attr_g)
                  )
                  
                  cycle_rank_loss = 0
                  if self.crl == True:
                    reconstructed_feature = self.g([noise_g, Fake_classification_g])
                    
                    # For cycle rank, the "negative" is a feature from a different class
                    # We can reuse the negative samples from the batch for simplicity
                    negative_attributes = np.array([train_Y_by_class[np.random.choice([c for c in range(15) if c != label])][0] for label in train_labels])
                    unsimilar_generated_feature = self.g([noise_g, negative_attributes])

                    cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                           
                  # 🔥 DR增强的生成器总损失
                  total_loss = (adversarial_loss + 
                               self.lambda_cla * classification_loss + 
                               self.lambda_triplet * triplet_loss_g + 
                               self.lambda_crl * cycle_rank_loss +
                               self.lambda_fusion * fusion_enhancement_loss +
                               self.lambda_g_reg * g_regression_loss)  # 🔥 新增DR损失
                  # 软检查：记录但不中断训练
                  if tf.reduce_any(tf.math.is_nan(total_loss)) or tf.reduce_any(tf.math.is_inf(total_loss)):
                      print(f"⚠️ Warning: DR G loss contains NaN/Inf at epoch {epoch}, batch {batch_i}")
                      total_loss = tf.where(tf.math.is_finite(total_loss), total_loss, 0.0)
                          
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print ("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][M loss: %f][Fusion loss: %f][DR D loss: %f][DR D_reg: %f][DR G loss %05f ][DR G_reg: %f]time: %s " \
                 % (epoch, epochs,
                   batch_i, num_batches,
                   tf.reduce_mean(total_ac_loss), 
                     m_loss,
                     fusion_loss,
                     d_loss,
                     d_loss_regression,  # 🔥 新增监控
                     tf.reduce_mean(total_loss),
                     g_regression_loss,  # 🔥 新增监控                                                                                                              
                     elapsed_time))
        
            if epoch % 1 == 0:
                # 在测试时使用融合增强的特征生成
                accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp = feature_generation_and_diagnosis_with_fusion(self, 2000, testdata, test_attributelabel, self.fusion_net)  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                # 计算当前最佳准确率
                current_best = max(accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp)
                self.accuracy_history.append(current_best)
                
                print("[Epoch %d/%d] [DR-Net Phase %d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f] [Best: %f]"\
                  %(epoch, epochs, self.dr_phase, max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4), current_best))
                
                # 智能检查点保存
                should_save, save_type = self.checkpoint_manager.should_save_and_update(current_best, epoch)
                if should_save:
                    self.save_model_checkpoint(epoch, current_best, save_type)
                    
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] [DR-Net Phase {self.dr_phase}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]"
                                   f"[Current_best: {current_best:f}]\n")
                    log_file.write(log_message)
                    log_file.flush()

                # 早停检查
                if self.checkpoint_manager.should_early_stop():
                    print(f"🛑 早停触发！在Epoch {epoch}停止训练 (连续{self.checkpoint_manager.patience}轮无提升)")
                    break
            
        best_accuracy = max([max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)])
        print('🎉 DR-Net训练完成! 训练期间最佳准确率:{:.4f}'.format(best_accuracy))
        print(f'💾 历史最佳DR-Net模型已保存至: {self.checkpoint_manager.save_path}/')
        print(f'🏆 历史最佳准确率: {self.checkpoint_manager.best_accuracy:.4f}')
        if log_file:
            log_file.write(f'DR-Net训练完成! 训练期间最佳准确率:{best_accuracy:.4f}\n')
            log_file.write(f'历史最佳准确率: {self.checkpoint_manager.best_accuracy:.4f}\n')
            log_file.write(f'最佳模型保存路径: {self.checkpoint_manager.save_path}/\n')
            log_file.flush()


                
if __name__ == '__main__':
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + "_dr_net_stable.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"🔥 DR-Net Stable训练开始，日志将被记录到: {log_filename}")

    # 数据加载
    PATH_train='./dataset_train_case1.npz'
    PATH_test='./dataset_test_case1.npz'
    
    train_data = np.load(PATH_train)
    test_data = np.load(PATH_test)
    
    train_X_by_class = {i: train_data[f'training_samples_{i+1}'] for i in range(15)}
    train_Y_by_class = {i: train_data[f'training_attribute_{i+1}'] for i in range(15)}

    all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
    all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
    all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

    test_X = np.concatenate([test_data[f'testing_samples_{i+1}'] for i in [1, 6, 14]])
    test_Y = np.concatenate([test_data[f'testing_attribute_{i+1}'] for i in [1, 6, 14]])

    scaler = MinMaxScaler()
    all_train_X = scaler.fit_transform(all_train_X)
    test_X = scaler.transform(test_X)

    current_pos = 0
    for i in range(15):
        class_len = len(train_X_by_class[i])
        train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
        current_pos += class_len

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# DR-Net训练日志 (判别器-回归器对偶网络 - Stable版)\n\n")
        log_file.write(f"## 核心创新点\n")
        log_file.write(f"- 🔥 双输出判别器：同时进行真假判断和属性回归\n")
        log_file.write(f"- 🔥 生成器回归约束：强制生成特征包含完整属性信息\n")
        log_file.write(f"- 🔥 智能检查点与早停机制\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = DR_ZeroShot()
        gan.train(epochs=2000, batch_size=120, 
                  traindata=all_train_X, train_attributelabel=all_train_Y, train_classlabel=all_train_labels,
                  train_X_by_class=train_X_by_class,
                  testdata=test_X, test_attributelabel=test_Y,
                  log_file=log_file)

        # 训练结束后自动测试最优模型
        print("\n" + "="*50)
        print("🎯 训练完成，使用最优模型进行最终测试...")
        gan.test_with_best_model(testdata, test_Y)
        
        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"🎉 DR-Net Stable训练完成，日志已保存至: {log_filename}") 
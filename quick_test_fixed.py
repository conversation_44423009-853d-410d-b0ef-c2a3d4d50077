#!/usr/bin/env python3
"""
🔥 快速测试修复效果 - 只运行50个epoch验证
"""

import sys
import os
sys.path.append('scripts')

def main():
    print("⚡ 快速测试修复效果...")
    
    try:
        from acgan_triplet_B_Hybrid import Zero_shot
        from tensorflow.keras.optimizers import Adam
        
        model = Zero_shot()
        
        # 应用修复配置
        model.lambda_cla = 10.0
        model.lambda_triplet = 30.0
        model.lambda_center = 1.5
        model.lambda_crl = 0.1
        model.lambda_semantic_consistency = 0.05
        
        model.optimizer_G = Adam(0.0001, 0.5)
        model.optimizer_D = Adam(0.0001, 0.5)
        model.optimizer_C = Adam(0.0001, 0.5)
        model.optimizer_M = Adam(0.0001, 0.5)
        
        print("🎯 快速测试 - 50个epoch")
        model.train(epochs=50, batch_size=256)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
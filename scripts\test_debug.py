import tensorflow as tf
import numpy as np
from sklearn import preprocessing
from sklearn.metrics import accuracy_score
from sklearn.naive_bayes import GaussianNB
import time

def scalar_stand(Train_X, Test_X):
    scalar_train = preprocessing.StandardScaler().fit(Train_X)
    Train_X = scalar_train.transform(Train_X)
    Test_X = scalar_train.transform(Test_X)
    return Train_X, Test_X

def feature_generation_and_diagnosis_debug(add_quantity, test_x, test_y, autoencoder, generator, classifier, test_class_indices):
    print("=== 调试模式：追踪性能瓶颈 ===")
    
    print(f"Step 1: 初始化...")
    start_time = time.time()
    
    all_classes = np.arange(1, 16)
    seen_classes = np.setdiff1d(all_classes, test_class_indices)
    print(f"test classes: {test_class_indices}")
    print(f"train classes: {seen_classes}")
    
    Labels_train = []
    Labels_test = []
    Generated_feature = []
    samples_per_class = len(test_x) // len(test_class_indices)
    
    print(f"Step 2: 生成特征 (样本数: {add_quantity})...")
    for i, class_idx in enumerate(test_class_indices):
        print(f"  处理类别 {class_idx}...")
        step_start = time.time()
        
        attribute_index = i * samples_per_class
        attribute_vector = test_y[attribute_index]
        
        attribute = np.array([attribute_vector for _ in range(add_quantity)])
        print(f"    属性形状: {attribute.shape}")
        
        noise_shape = (add_quantity, 50, 1)
        noise = tf.random.normal(shape=noise_shape)
        print(f"    噪声形状: {noise.shape}")
        
        print(f"    开始生成器预测...")
        generated_feature = generator.predict([noise, attribute], verbose=0)  # 关闭详细输出
        print(f"    生成特征形状: {generated_feature.shape}")
        
        Generated_feature.append(generated_feature)
        
        labels_train = np.full((add_quantity, 1), class_idx)
        labels_test = np.full((samples_per_class, 1), class_idx)
        
        Labels_train.append(labels_train)
        Labels_test.append(labels_test)
        
        print(f"    类别 {class_idx} 完成，耗时: {time.time() - step_start:.2f}秒")
    
    print(f"Step 3: 整理数据...")
    step_start = time.time()
    
    Generated_feature = np.array(Generated_feature).reshape(-1, 256)
    Labels_train = np.array(Labels_train).reshape(-1, 1)
    Labels_test = np.array(Labels_test).reshape(-1, 1)
    
    print(f"    数据整理完成，耗时: {time.time() - step_start:.2f}秒")
    
    print(f"Step 4: 自编码器处理测试数据...")
    step_start = time.time()
    
    test_feature, decoded_test = autoencoder(test_x)
    
    print(f"    自编码器完成，耗时: {time.time() - step_start:.2f}秒")
    
    print(f"Step 5: 分类器处理...")
    step_start = time.time()
    
    hidden_ouput_train, predict_attribute_train = classifier(Generated_feature)
    new_feature_train = np.concatenate((Generated_feature, hidden_ouput_train), axis=1)
    
    hidden_ouput_test, predict_attribute_test = classifier(test_feature)
    new_feature_test = np.concatenate((test_feature, hidden_ouput_test), axis=1)
    
    print(f"    分类器处理完成，耗时: {time.time() - step_start:.2f}秒")
    
    print(f"Step 6: 标准化...")
    step_start = time.time()
    
    train_X = new_feature_train
    train_Y = Labels_train
    test_X = new_feature_test
    test_Y = Labels_test
    
    train_X, test_X = scalar_stand(train_X, test_X)
    
    print(f"    标准化完成，耗时: {time.time() - step_start:.2f}秒")
    
    print(f"Step 7: 最终分类器训练...")
    step_start = time.time()
    
    # 只使用最快的朴素贝叶斯
    classifier_pnb = GaussianNB()
    classifier_pnb.fit(train_X, train_Y.ravel())
    Y_pred_pnb = classifier_pnb.predict(test_X)
    accuracy_pnb = accuracy_score(test_Y, Y_pred_pnb)
    
    print(f"    最终分类器完成，耗时: {time.time() - step_start:.2f}秒")
    print(f"    准确率: {accuracy_pnb:.4f}")
    
    total_time = time.time() - start_time
    print(f"=== 总耗时: {total_time:.2f}秒 ===")
    
    return accuracy_pnb, accuracy_pnb, accuracy_pnb, accuracy_pnb
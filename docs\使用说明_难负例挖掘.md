# 难负例挖掘 Triplet ACGAN 使用说明

## 📁 文件说明
- **主脚本**: `acgan_triplet_hard_negative.py`
- **技术文档**: `难负例挖掘改进方案.md` 
- **使用说明**: `使用说明_难负例挖掘.md` (本文件)

## 🚀 快速开始

### 方法1: 默认运行（推荐）
```bash
python acgan_triplet_hard_negative.py
```
- 默认使用 **Group E** [6, 9, 13]（当前表现最差的组别）
- 自动生成日志文件到 `结果/` 目录

### 方法2: 指定组别运行
```bash
# 运行 Group A
python acgan_triplet_hard_negative.py A

# 运行 Group B  
python acgan_triplet_hard_negative.py B

# 运行 Group C
python acgan_triplet_hard_negative.py C

# 运行 Group D
python acgan_triplet_hard_negative.py D

# 运行 Group E
python acgan_triplet_hard_negative.py E
```

## 📊 组别配置

| 组别 | 测试类别 | 说明 |
|------|---------|------|
| Group A | [1, 5, 14] | 标准测试组 |
| Group B | [4, 7, 10] | 中等难度测试组 |
| Group C | [8, 11, 12] | 挑战性测试组 |
| Group D | [2, 3, 15] | 复杂故障测试组 |
| Group E | [6, 9, 13] | 最困难测试组（推荐） |

## ⚙️ 核心改进特性

### 1. 难负例挖掘策略
- **智能负采样**: 选择属性空间中最相似但类别不同的负样本
- **混合采样**: 70% 难负例 + 30% 随机负例
- **动态调整**: 训练前期更多随机样本，后期更多难负例

### 2. 属性相似度计算
- **相似度度量**: 余弦相似度
- **候选池大小**: Top-5 最相似属性
- **排除机制**: 自动排除自身类别

### 3. 灵活配置
```python
# 可调整的核心参数
self.hard_negative_ratio = 0.7    # 难负例占比
self.top_k_candidates = 5         # 候选负例数量
self.use_hard_negative = True     # 启用/禁用难负例挖掘
```

## 📈 预期改进效果

### 针对 Group E 的优化
- **当前基线**: 各分类器准确率偏低
- **预期提升**: 5-10% 准确率提升
- **收敛速度**: 更快达到最优性能
- **稳定性**: 减少训练波动

### 改进机制
1. **细粒度学习**: 强迫模型区分相似故障类型
2. **属性利用**: 充分利用故障特征的语义信息  
3. **对比增强**: 更有挑战性的对比学习

## 📝 输出文件

### 训练日志格式
```
结果/{timestamp}_triplet_hard_negative_Group{X}.md
```

### 日志内容示例
```
# 训练日志 (Triplet架构 + 难负例挖掘 - Group E)

**开始时间**: 2025-01-29 14:30:25
**实验组别**: Group E  
**测试类别**: [6, 9, 13]
**改进策略**: 难负例挖掘 (Hard Negative Mining)
**难负例比例**: 70% (动态调整)
**属性相似度**: 余弦相似度
**候选负例数**: 5

---

[Epoch 0/2000] [D_loss: 0.245631] [G_loss: 1.234567] ...
```

## 🔧 技术细节

### 训练参数
- **训练轮数**: 2000 epochs
- **批量大小**: 64
- **学习率**: 0.0001 (Adam优化器)
- **评估频率**: 每50个epoch

### 损失函数组合
```python
g_loss = (
    lambda_adversarial * g_loss_adv +      # 对抗损失
    lambda_triplet * triplet_loss +        # Triplet损失
    lambda_cycle_rank * cycle_loss +       # 循环排序损失  
    lambda_mi * mi_loss                    # 互信息损失
)
```

### 模型架构
- **生成器**: 带自注意力机制的全连接网络
- **判别器**: 多层感知机 + LeakyReLU + Dropout
- **分类器**: 属性预测网络
- **自编码器**: 特征重构网络

## 🎯 使用建议

### 1. 首次运行
```bash
# 推荐先在表现最差的组别上验证效果
python acgan_triplet_hard_negative.py E
```

### 2. 对比实验
```bash
# 运行原始版本（作为对照组）
python acgan_triplet.py

# 运行改进版本
python acgan_triplet_hard_negative.py E
```

### 3. 全面测试
```bash
# 在所有组别上测试改进效果
for group in A B C D E; do
    python acgan_triplet_hard_negative.py $group
done
```

## 📊 性能监控

### 关键指标
- **LSVM准确率**: 线性支持向量机分类准确率
- **NRF准确率**: 最近邻随机森林准确率  
- **PNB准确率**: 朴素贝叶斯分类准确率
- **MLP准确率**: 多层感知机分类准确率

### 训练监控
- **D_loss**: 判别器损失（应逐渐稳定）
- **G_loss**: 生成器损失（应逐渐下降）
- **C_loss**: 分类器损失（应逐渐下降）
- **AE_loss**: 自编码器损失（应逐渐下降）

## ⚠️ 注意事项

1. **硬件要求**: 建议使用GPU加速训练
2. **内存占用**: 大约需要4-8GB GPU显存
3. **训练时间**: 约2-3小时（2000 epochs）
4. **日志空间**: 确保 `结果/` 目录有足够空间

## 🔄 故障排除

### 常见问题
1. **GPU内存不足**: 减小batch_size
2. **训练不收敛**: 调整学习率或损失权重
3. **准确率波动**: 增加训练轮数或调整难负例比例

### 联系方式
如有问题，请查看技术文档 `难负例挖掘改进方案.md` 或检查代码注释。

---

**最后更新**: 2025-01-29  
**版本**: v1.0  
**兼容性**: TensorFlow 2.x + CUDA 12.0+ 
import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,LayerNormalization,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from test_fusion import feature_generation_and_diagnosis_with_fusion
from sklearn.preprocessing import MinMaxScaler


# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    # 限制TensorFlow只使用第一个GPU
    tf.config.set_visible_devices(gpus[0], 'GPU')
    # 设置显存按需增长
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    # 显存增长必须在GPU初始化之前设置
    print(e)


def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = LayerNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = LayerNormalization()(y)
    
    # 如果输入和输出维度不同，需要一个线性投影
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        # input_shape is (batch_size, feature_dim)
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        # Reshape for matrix multiplication
        # Temporarily add a "sequence length" of 1
        # x_reshaped shape: (batch_size, 1, feature_dim)
        x_reshaped = K.expand_dims(x, axis=1)

        # Q, K, V projections
        q = self.query(x_reshaped)  # (batch_size, 1, feature_dim/8)
        k = self.key(x_reshaped)    # (batch_size, 1, feature_dim/8)
        v = self.value(x_reshaped)  # (batch_size, 1, feature_dim)

        # Attention scores
        attention_scores = K.batch_dot(q, k, axes=[2, 2]) # (batch_size, 1, 1)
        attention_probs = K.softmax(attention_scores)

        # Apply attention
        context = K.batch_dot(attention_probs, v) # (batch_size, 1, feature_dim)
        
        # Remove the temporary dimension
        context = K.squeeze(context, axis=1)

        # Add back to original input (residual connection)
        return x + self.gamma * context

class CrossAttentionFusion(Layer):
    """交叉注意力融合层 - 第三个创新点的核心实现"""
    def __init__(self, units, **kwargs):
        super(CrossAttentionFusion, self).__init__(**kwargs)
        self.units = units

    def build(self, input_shape):
        data_feature_dim = input_shape[0][-1]
        semantic_feature_dim = input_shape[1][-1]
        
        self.query = Dense(self.units, use_bias=False)
        self.key = Dense(self.units, use_bias=False)
        self.value = Dense(self.units, use_bias=False)

        self.out_dense = Dense(data_feature_dim)
        super(CrossAttentionFusion, self).build(input_shape)

    def call(self, inputs):
        data_features, semantic_features = inputs
        
        # 扩展维度以进行注意力计算
        q = self.query(K.expand_dims(data_features, axis=1))
        k = self.key(K.expand_dims(semantic_features, axis=1))
        v = self.value(K.expand_dims(semantic_features, axis=1))
        
        # 计算注意力权重
        attention_scores = tf.matmul(q, k, transpose_b=True)
        attention_probs = tf.nn.softmax(attention_scores)
        
        # 应用注意力权重
        context = tf.matmul(attention_probs, v)
        context = K.squeeze(context, axis=1)
        
        # 将注意力上下文与原始数据特征结合
        fused_output = self.out_dense(concatenate([data_features, context]))
        return fused_output
        
class Zero_shot():
    def __init__(self, lambda_cla=10.0, lambda_triplet=10.0): # 调整默认值以匹配实验
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256
        self.feature_shape=(self.feature_dim,)
        self.num_classes=15
        self.attribute_dim=20
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim, 1)
        self.n_critic = 1
        self.crl = True

        self.lambda_cla = lambda_cla
        self.lambda_triplet = lambda_triplet
        self.lambda_crl = 0.01 
        self.lambda_fusion = 1.0  # 新增：融合损失的权重
        
        self.bound = False
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For triplet loss
        self.fusion_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For fusion
        
        # 移除模型保存功能
        
        self.autoencoder= self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c= self.build_classifier()
        self.fusion_net = self.build_fusion_network() # 新增：交叉注意力融合网络

    def build_autoencoder(self):
      
      sample = Input(shape=self.sample_shape)     
      
      a0=sample

      # Encoder
      a1=Dense(100)(a0)
      a1=LeakyReLU(alpha=0.2)(a1)
      a1=LayerNormalization()(a1)

      a2=Dense(200)(a1)
      a2=LeakyReLU(alpha=0.2)(a2)
      a2=LayerNormalization()(a2)

      a3=Dense(256)(a2)
      a3=LeakyReLU(alpha=0.2)(a3)
      a3=LayerNormalization()(a3)
      feature=a3

      # Decoder
      a4=Dense(200)(feature)
      a4=LeakyReLU(alpha=0.2)(a4)
      a4=LayerNormalization()(a4)

      a5=Dense(100)(a4)
      a5=LeakyReLU(alpha=0.2)(a5)
      a5=LayerNormalization()(a5)

      a6=Dense(52)(a5)
      a6=LeakyReLU(alpha=0.2)(a6)
      a6=LayerNormalization()(a6)
      output_sample=a6

      # Autoencoder Model
      autoencoder = Model(sample,[feature, output_sample])
      # We only need the encoder part for triplet loss feature extraction
      self.encoder = Model(sample, feature)
      return autoencoder    
        
    def build_discriminator(self):
        
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input,attribute],validity)

    def build_generator(self):
      
      noise = Input(shape=self.noise_shape)
      attribute = Input(shape=(20,), dtype='float32')
      
      noise_embedding = Flatten()(noise)
      attribute_embedding = Dense(self.latent_dim)(attribute)
      
      g_input = concatenate([noise_embedding, attribute_embedding])

      g1 = Dense(128)(g_input)
      g1 = LeakyReLU(alpha=0.2)(g1)
      g1 = LayerNormalization()(g1)

      g2 = residual_block(g1, 256) 
      g3 = residual_block(g2, 256) 
      
      g3_attention = SelfAttention()(g3)
      
      generated_feature = Dense(256)(g3_attention)
      generated_feature = BatchNormalization()(generated_feature)

      return Model([noise,attribute],generated_feature)
    
    def build_classifier(self):
        
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        
        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        hidden_ouput=c2
               
        c3 = Dense(20,activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])

    def build_fusion_network(self):
        """构建交叉注意力融合网络"""
        data_input = Input(shape=self.feature_shape, name="data_input")
        semantic_input = Input(shape=(self.attribute_dim,), name="semantic_input")
        
        # 交叉注意力融合
        fused_output = CrossAttentionFusion(units=128)([data_input, semantic_input])
        
        # 额外的处理层
        fused_output = Dense(256)(fused_output)
        fused_output = LeakyReLU(alpha=0.2)(fused_output)
        fused_output = LayerNormalization()(fused_output)
        
        return Model([data_input, semantic_input], fused_output, name="FusionNetwork")



    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
            return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)) + 1e-8)
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self,current_batch_features,y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(
                y_true, pred_attribute)
        
        mi_penalty=0    
        if self.bound == True:    
          mi_penalty = self.mi_penalty_loss(
              current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def cycle_rank_loss(self, anchor, positive, negative):
        # We can reuse the triplet loss logic for cycle rank consistency
        # The goal is that the reconstructed feature (positive) is closer to the original fake feature (anchor)
        # than any other generated feature from a different class (negative).
        return self.triplet_loss(anchor, positive, negative)
    
    def train(self, epochs, batch_size, log_file=None, test_classes=None):
        
        start_time = datetime.datetime.now()
        
        accuracy_list_1=[]
        accuracy_list_2=[]
        accuracy_list_3=[]
        accuracy_list_4=[]
        
        valid = -np.ones((batch_size,1) )
        fake = np.ones((batch_size,1) )
        
        PATH_train='./dataset_train_case1.npz'
        PATH_test='./dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        if test_classes is None:
            test_classes = [9, 13, 15] # 默认E组

        # E组配置：训练集排除 9, 13, 15，测试集只用 9, 13, 15
        train_classes = [i for i in range(1, 16) if i not in test_classes]

        # This part remains mostly the same, but we will use the labels to find pos/neg samples
        train_X_by_class = {i-1: train_data[f'training_samples_{i}'] for i in train_classes}
        train_Y_by_class = {i-1: train_data[f'training_attribute_{i}'] for i in train_classes}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        test_X = np.concatenate([test_data[f'testing_samples_{i}'] for i in test_classes])
        test_Y = np.concatenate([test_data[f'testing_attribute_{i}'] for i in test_classes])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # Re-organize scaled data back into class dictionaries
        current_pos = 0
        for i in sorted(train_X_by_class.keys()):
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata=all_train_X
        train_attributelabel=all_train_Y
        train_classlabel = all_train_labels
        
        testdata=test_X
        test_attributelabel=test_Y
       
        num_batches=int(traindata.shape[0]/batch_size)
               
        for epoch in range(epochs):
            
            for batch_i in range(num_batches):
                
                start_i =batch_i * batch_size
                end_i=(batch_i + 1) * batch_size
                
                train_x=traindata[start_i:end_i]
                train_y=train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # Autoencoder and Classifier Training (Same as before)
                self.autoencoder.trainable = True
                self.c.trainable = True # Train C together with AE now
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                  feature, output_sample=self.autoencoder(train_x)
                  autoencoder_loss=mean_squared_error(train_x,output_sample)      

                  hidden_ouput_c,predict_attribute_c=self.c(feature)
                  c_loss=self.classification_loss(feature,train_y, hidden_ouput_c, predict_attribute_c)

                  total_ac_loss = autoencoder_loss + c_loss
                  tf.debugging.check_numerics(total_ac_loss, "AE+C loss is not finite")

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c

                # Triplet Loss Metric Learning
                self.autoencoder.trainable = True # Encoder is part of metric learning
                self.c.trainable = True
                
                # Sample triplets
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []
                for label in train_labels:
                    pos_class_samples = train_X_by_class[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    
                    available_neg_classes = [c for c in train_X_by_class.keys() if c != label]
                    neg_class = np.random.choice(available_neg_classes)
                    neg_class_samples = train_X_by_class[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)
                    tf.debugging.check_numerics(m_loss, "M loss is not finite")

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                # 交叉注意力融合网络训练
                self.fusion_net.trainable = True
                
                with tf.GradientTape() as tape_fusion:
                    # 获取特征和语义表示
                    batch_features = self.encoder(train_x)
                    _, semantic_features = self.c(batch_features)
                    
                    # 通过融合网络
                    fused_features = self.fusion_net([batch_features, semantic_features])
                    
                    # 融合特征应该能够重构原始特征
                    fusion_loss = tf.reduce_mean(tf.square(fused_features - batch_features))
                    tf.debugging.check_numerics(fusion_loss, "Fusion loss is not finite")

                grads_fusion = tape_fusion.gradient(fusion_loss, self.fusion_net.trainable_weights)
                self.fusion_optimizer.apply_gradients(zip(grads_fusion, self.fusion_net.trainable_weights))

                # Discriminator Training (Same as before)
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False
                self.fusion_net.trainable = False

                for _ in range(self.n_critic):
                  with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature = self.g([noise,train_y])
                    real_feature = self.encoder(train_x)
        
                    real_validity = self.d([real_feature,train_y])
                    fake_validity = self.d([fake_feature,train_y])  
                                           
                    d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                    d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                    d_loss = d_loss_real + d_loss_fake
                    tf.debugging.check_numerics(d_loss, "D loss is not finite")
                  
                  grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                  self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # Generator Training with Fusion Enhancement
                self.d.trainable = False               
                self.g.trainable = True
                self.fusion_net.trainable = False  # 在G训练时冻结fusion
                
                with tf.GradientTape() as tape_g:
                  noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                  Fake_feature_g = self.g([noise_g,train_y])
                  Fake_validity_g = self.d([Fake_feature_g,train_y])
                  adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
            
                  fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                  classification_loss = self.classification_loss(Fake_feature_g,train_y, fake_hidden_ouput_g, Fake_classification_g)
                  
                  # Triplet loss for Generator
                  g_anchor_features = Fake_feature_g
                  g_positive_features = self.encoder(positive_samples) # Use same positive samples
                  g_negative_features = self.encoder(negative_samples) # Use same negative samples
                  triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                  
                  # 融合增强：生成的特征通过融合网络应该能产生更好的表示
                  fused_fake_features = self.fusion_net([Fake_feature_g, Fake_classification_g])
                  fusion_enhancement_loss = tf.reduce_mean(tf.square(fused_fake_features - Fake_feature_g))
                  
                  cycle_rank_loss = 0
                  if self.crl == True:
                    reconstructed_feature = self.g([noise_g, Fake_classification_g])
                    
                    # For cycle rank, the "negative" is a feature from a different class
                    # We can reuse the negative samples from the batch for simplicity
                    
                    # Correctly sample negative attributes only from training classes
                    negative_attributes_list = []
                    train_class_indices = list(train_Y_by_class.keys())
                    for label in train_labels:
                        available_neg_classes = [c for c in train_class_indices if c != label]
                        neg_class = np.random.choice(available_neg_classes)
                        negative_attributes_list.append(train_Y_by_class[neg_class][0])
                    negative_attributes = np.array(negative_attributes_list)

                    unsimilar_generated_feature = self.g([noise_g, negative_attributes])

                    cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                           
                  total_loss = (adversarial_loss + 
                               self.lambda_cla * classification_loss + 
                               self.lambda_triplet * triplet_loss_g + 
                               self.lambda_crl * cycle_rank_loss +
                               self.lambda_fusion * fusion_enhancement_loss)  # 新增融合损失
                  tf.debugging.check_numerics(total_loss, "G loss is not finite")
                          
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print ("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][M loss: %f][Fusion loss: %f][D loss: %f][G loss %05f ]time: %s " \
                 % (epoch, epochs,
                   batch_i, num_batches,
                   tf.reduce_mean(total_ac_loss), 
                     m_loss,
                     fusion_loss,
                     d_loss,
                     tf.reduce_mean(total_loss),                                                                                                              
                     elapsed_time))
        
            if epoch % 1 == 0:
                # 在测试时使用融合增强的特征生成
                test_class_indices = [c - 1 for c in test_classes]
                accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp = feature_generation_and_diagnosis_with_fusion(self, 2000, testdata, test_attributelabel, self.fusion_net, test_class_indices)  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                # 计算当前最佳准确率
                current_best = max(accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp)
                
                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f] [Best: %f]"\
                  %(epoch, epochs,max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4), current_best))
                    
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]"
                                   f"[Current_best: {current_best:f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
            
        best_accuracy = max([max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)])
        print('🎉 训练完成! 总体最佳准确率:{:.4f}'.format(best_accuracy))
        if log_file:
            log_file.write(f'训练完成! 总体最佳准确率:{best_accuracy:.4f}\n')
            log_file.flush()


                
if __name__ == '__main__':
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    # --- 中心化实验配置 ---
    GROUP_CONFIG = {
        'A': {'test_classes': [1, 6, 14], 'lambda_cla': 10, 'lambda_triplet': 10, 'epochs': 1000},
        'B': {'test_classes': [4, 7, 10], 'lambda_cla': 10, 'lambda_triplet': 10, 'epochs': 1500},
        'C': {'test_classes': [8, 11, 12], 'lambda_cla': 10, 'lambda_triplet': 10, 'epochs': 500},
        'D': {'test_classes': [2, 3, 5], 'lambda_cla': 10, 'lambda_triplet': 10, 'epochs': 500},
        'E': {'test_classes': [9, 13, 15], 'lambda_cla': 1.0, 'lambda_triplet': 1.0, 'epochs': 2000},
    }
    EXPERIMENT_GROUP = 'C'  # <--- 在这里切换实验组别 ('A', 'B', 'C', 'D', 'E')
    
    config = GROUP_CONFIG[EXPERIMENT_GROUP]
    test_classes = config['test_classes']
    lambda_cla_val = config['lambda_cla']
    lambda_triplet_val = config['lambda_triplet']
    epochs_val = config['epochs']
    # -------------------------

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + f"_cross_attention_Group_{EXPERIMENT_GROUP}.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"开始训练，实验组: {EXPERIMENT_GROUP}, 测试类别: {test_classes}")
    print(f"日志将被记录到: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 训练日志 (交叉注意力融合架构 - {EXPERIMENT_GROUP}组)\n\n")
        log_file.write(f"**测试类别**: {test_classes}\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = Zero_shot(lambda_cla=lambda_cla_val, lambda_triplet=lambda_triplet_val)
        gan.train(epochs=epochs_val, batch_size=120, log_file=log_file, test_classes=test_classes)

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"训练完成，日志已保存至: {log_filename}") 
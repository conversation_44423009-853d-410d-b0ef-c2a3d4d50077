{"timestamp": "2025-07-28T22:30:13.948487", "total_groups": 5, "successful_groups": 0, "failed_groups": 5, "results": [{"group": "A", "status": "failed", "duration_hours": 0.004920754962497287, "return_code": 3221226505, "stdout": "6] Created device /job:localhost/replica:0/task:0/device:GPU:0 with 21458 MB memory:  -> device: 0, name: NVIDIA GeForce RTX 4090, pci bus id: 0000:01:00.0, compute capability: 8.9\n2025-07-28 22:28:58,420 - INFO - 开始为组别 A 进行训练，共 2000 轮。\n2025-07-28 22:28:58,420 - INFO - 为组别 A 定义数据，看不见的类别: [1, 6, 14]\n2025-07-28 22:28:58,421 - INFO - 看得见的类别: [2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 15]\n2025-07-28 22:28:58,430 - INFO - 数据加载完成:\n2025-07-28 22:28:58,430 - INFO -   - 训练数据形状: (5760, 52)\n2025-07-28 22:28:58,430 - INFO -   - 训练属性形状: (5760, 20)\n2025-07-28 22:28:58,430 - INFO -   - 测试数据形状: (2880, 52)\n2025-07-28 22:28:58,430 - INFO -   - 测试属性形状: (2880, 20)\n2025-07-28 22:28:58.462725: I tensorflow/stream_executor/cuda/cuda_blas.cc:1614] TensorFloat-32 will be used for the matrix multiplication. This will only be logged once.\n2025-07-28 22:28:58.818064: I tensorflow/stream_executor/cuda/cuda_dnn.cc:384] Loaded cuDNN version 8600\nCould not locate zlibwapi.dll. Please make sure it is in your library path!\n", "stderr": ""}, {"group": "B", "status": "failed", "duration_hours": 0.004366611838340759, "return_code": 3221226505, "stdout": "6] Created device /job:localhost/replica:0/task:0/device:GPU:0 with 21458 MB memory:  -> device: 0, name: NVIDIA GeForce RTX 4090, pci bus id: 0000:01:00.0, compute capability: 8.9\n2025-07-28 22:29:15,929 - INFO - 开始为组别 B 进行训练，共 2000 轮。\n2025-07-28 22:29:15,929 - INFO - 为组别 B 定义数据，看不见的类别: [4, 7, 10]\n2025-07-28 22:29:15,930 - INFO - 看得见的类别: [1, 2, 3, 5, 6, 8, 9, 11, 12, 13, 14, 15]\n2025-07-28 22:29:15,938 - INFO - 数据加载完成:\n2025-07-28 22:29:15,938 - INFO -   - 训练数据形状: (5760, 52)\n2025-07-28 22:29:15,938 - INFO -   - 训练属性形状: (5760, 20)\n2025-07-28 22:29:15,939 - INFO -   - 测试数据形状: (2880, 52)\n2025-07-28 22:29:15,939 - INFO -   - 测试属性形状: (2880, 20)\n2025-07-28 22:29:15.966539: I tensorflow/stream_executor/cuda/cuda_blas.cc:1614] TensorFloat-32 will be used for the matrix multiplication. This will only be logged once.\n2025-07-28 22:29:15.988975: I tensorflow/stream_executor/cuda/cuda_dnn.cc:384] Loaded cuDNN version 8600\nCould not locate zlibwapi.dll. Please make sure it is in your library path!\n", "stderr": ""}, {"group": "C", "status": "failed", "duration_hours": 0.004167303575409783, "return_code": 3221226505, "stdout": "6] Created device /job:localhost/replica:0/task:0/device:GPU:0 with 21458 MB memory:  -> device: 0, name: NVIDIA GeForce RTX 4090, pci bus id: 0000:01:00.0, compute capability: 8.9\n2025-07-28 22:29:31,588 - INFO - 开始为组别 C 进行训练，共 2000 轮。\n2025-07-28 22:29:31,589 - INFO - 为组别 C 定义数据，看不见的类别: [8, 11, 12]\n2025-07-28 22:29:31,590 - INFO - 看得见的类别: [1, 2, 3, 4, 5, 6, 7, 9, 10, 13, 14, 15]\n2025-07-28 22:29:31,600 - INFO - 数据加载完成:\n2025-07-28 22:29:31,600 - INFO -   - 训练数据形状: (5760, 52)\n2025-07-28 22:29:31,600 - INFO -   - 训练属性形状: (5760, 20)\n2025-07-28 22:29:31,600 - INFO -   - 测试数据形状: (2880, 52)\n2025-07-28 22:29:31,600 - INFO -   - 测试属性形状: (2880, 20)\n2025-07-28 22:29:31.621422: I tensorflow/stream_executor/cuda/cuda_blas.cc:1614] TensorFloat-32 will be used for the matrix multiplication. This will only be logged once.\n2025-07-28 22:29:31.643705: I tensorflow/stream_executor/cuda/cuda_dnn.cc:384] Loaded cuDNN version 8600\nCould not locate zlibwapi.dll. Please make sure it is in your library path!\n", "stderr": ""}, {"group": "D", "status": "failed", "duration_hours": 0.004211273259586758, "return_code": 3221226505, "stdout": "6] Created device /job:localhost/replica:0/task:0/device:GPU:0 with 21458 MB memory:  -> device: 0, name: NVIDIA GeForce RTX 4090, pci bus id: 0000:01:00.0, compute capability: 8.9\n2025-07-28 22:29:46,588 - INFO - 开始为组别 D 进行训练，共 2000 轮。\n2025-07-28 22:29:46,588 - INFO - 为组别 D 定义数据，看不见的类别: [2, 3, 5]\n2025-07-28 22:29:46,589 - INFO - 看得见的类别: [1, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]\n2025-07-28 22:29:46,596 - INFO - 数据加载完成:\n2025-07-28 22:29:46,597 - INFO -   - 训练数据形状: (5760, 52)\n2025-07-28 22:29:46,597 - INFO -   - 训练属性形状: (5760, 20)\n2025-07-28 22:29:46,597 - INFO -   - 测试数据形状: (2880, 52)\n2025-07-28 22:29:46,597 - INFO -   - 测试属性形状: (2880, 20)\n2025-07-28 22:29:46.617475: I tensorflow/stream_executor/cuda/cuda_blas.cc:1614] TensorFloat-32 will be used for the matrix multiplication. This will only be logged once.\n2025-07-28 22:29:46.638762: I tensorflow/stream_executor/cuda/cuda_dnn.cc:384] Loaded cuDNN version 8600\nCould not locate zlibwapi.dll. Please make sure it is in your library path!\n", "stderr": ""}, {"group": "E", "status": "failed", "duration_hours": 0.004236905044979519, "return_code": 3221226505, "stdout": "6] Created device /job:localhost/replica:0/task:0/device:GPU:0 with 21458 MB memory:  -> device: 0, name: NVIDIA GeForce RTX 4090, pci bus id: 0000:01:00.0, compute capability: 8.9\n2025-07-28 22:30:01,769 - INFO - 开始为组别 E 进行训练，共 2000 轮。\n2025-07-28 22:30:01,769 - INFO - 为组别 E 定义数据，看不见的类别: [9, 13, 15]\n2025-07-28 22:30:01,770 - INFO - 看得见的类别: [1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 14]\n2025-07-28 22:30:01,779 - INFO - 数据加载完成:\n2025-07-28 22:30:01,779 - INFO -   - 训练数据形状: (5760, 52)\n2025-07-28 22:30:01,779 - INFO -   - 训练属性形状: (5760, 20)\n2025-07-28 22:30:01,779 - INFO -   - 测试数据形状: (2880, 52)\n2025-07-28 22:30:01,779 - INFO -   - 测试属性形状: (2880, 20)\n2025-07-28 22:30:01.799190: I tensorflow/stream_executor/cuda/cuda_blas.cc:1614] TensorFloat-32 will be used for the matrix multiplication. This will only be logged once.\n2025-07-28 22:30:01.821224: I tensorflow/stream_executor/cuda/cuda_dnn.cc:384] Loaded cuDNN version 8600\nCould not locate zlibwapi.dll. Please make sure it is in your library path!\n", "stderr": ""}]}
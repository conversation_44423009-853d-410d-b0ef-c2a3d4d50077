import tensorflow as tf
from tensorflow.summary import create_file_writer, scalar, histogram, image
import numpy as np
import matplotlib.pyplot as plt
import io
from PIL import Image

class AdvancedTensorBoardMonitor:
    """高级TensorBoard监控器，支持实时可视化和分析"""
    
    def __init__(self, log_dir="./tensorboard_logs/advanced"):
        self.log_dir = log_dir
        self.writer = create_file_writer(log_dir)
        
    def log_loss_analysis(self, data):
        """记录损失函数分析"""
        with self.writer.as_default():
            epochs = data['epochs']
            
            for i, epoch in enumerate(epochs):
                # 基础损失
                scalar('Losses/AE_C_Loss', data['ae_c_loss'][i], step=epoch)
                scalar('Losses/Generator_Loss', data['g_loss'][i], step=epoch)
                scalar('Losses/Discriminator_Loss', data['d_loss'][i], step=epoch)
                
                # 强化学习损失
                scalar('Reinforcement/Triplet', data['triplet_loss'][i], step=epoch)
                scalar('Reinforcement/Center', data['center_loss'][i], step=epoch)
                scalar('Reinforcement/Combined', data['combined_loss'][i], step=epoch)
                
                # 正则化损失
                scalar('Regularization/CRL', data['crl_loss'][i], step=epoch)
                scalar('Regularization/Semantic', data['semantic_loss'][i], step=epoch)
                
                # 准确率
                if not np.isnan(data['lsvm_acc'][i]):
                    scalar('Accuracy/LinearSVM', data['lsvm_acc'][i], step=epoch)
                    scalar('Accuracy/RandomForest', data['rf_acc'][i], step=epoch)
                    scalar('Accuracy/GaussianNB', data['nb_acc'][i], step=epoch)
                    scalar('Accuracy/MLPClassifier', data['mlp_acc'][i], step=epoch)
    
    def create_loss_distribution_plot(self, data, epoch):
        """创建损失分布图"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle(f'Loss Distribution Analysis - Epoch {epoch}', fontsize=14)
        
        # 损失组件分布
        loss_components = [
            data['ae_c_loss'][epoch],
            data['triplet_loss'][epoch],
            data['center_loss'][epoch],
            data['crl_loss'][epoch]
        ]
        loss_labels = ['AE+C', 'Triplet', 'Center', 'CRL']
        
        axes[0,0].pie(loss_components, labels=loss_labels, autopct='%1.1f%%')
        axes[0,0].set_title('Loss Components Distribution')
        
        # GAN损失趋势
        gan_epochs = data['epochs'][:epoch+1]
        axes[0,1].plot(gan_epochs, data['g_loss'][:epoch+1], 'b-', label='Generator')
        axes[0,1].plot(gan_epochs, data['d_loss'][:epoch+1], 'r-', label='Discriminator')
        axes[0,1].set_title('GAN Training Progress')
        axes[0,1].legend()
        axes[0,1].grid(True)
        
        # 准确率对比
        if not np.isnan(data['lsvm_acc'][epoch]):
            acc_values = [
                data['lsvm_acc'][epoch],
                data['rf_acc'][epoch],
                data['nb_acc'][epoch],
                data['mlp_acc'][epoch]
            ]
            acc_labels = ['LinearSVM', 'RandomForest', 'GaussianNB', 'MLP']
            axes[1,0].bar(acc_labels, acc_values)
            axes[1,0].set_title('Classification Accuracy')
            axes[1,0].set_ylabel('Accuracy (%)')
        
        # 损失收敛分析
        center_trend = data['center_loss'][:epoch+1]
        axes[1,1].plot(gan_epochs, center_trend, 'g-', linewidth=2)
        axes[1,1].set_title('Center Loss Convergence')
        axes[1,1].set_xlabel('Epoch')
        axes[1,1].set_ylabel('Center Loss')
        axes[1,1].grid(True)
        
        plt.tight_layout()
        
        # 转换为图像
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=150, bbox_inches='tight')
        buf.seek(0)
        img = Image.open(buf)
        img_array = np.array(img)
        plt.close()
        
        return img_array
    
    def log_custom_visualizations(self, data):
        """记录自定义可视化"""
        with self.writer.as_default():
            for i, epoch in enumerate(data['epochs']):
                # 创建损失分布图
                if i % 5 == 0:  # 每5个epoch记录一次图像
                    img_array = self.create_loss_distribution_plot(data, i)
                    # 添加batch维度并转换为float32
                    img_tensor = tf.expand_dims(tf.cast(img_array, tf.float32), 0)
                    image('Loss_Analysis/Distribution_Plot', img_tensor, step=epoch)
                
                # 记录损失比率
                total_loss = data['ae_c_loss'][i] + data['center_loss'][i]
                if total_loss > 0:
                    triplet_ratio = data['triplet_loss'][i] / total_loss
                    center_ratio = data['center_loss'][i] / total_loss
                    scalar('Loss_Ratios/Triplet_Ratio', triplet_ratio, step=epoch)
                    scalar('Loss_Ratios/Center_Ratio', center_ratio, step=epoch)
                
                # GAN平衡指标
                if abs(data['g_loss'][i]) > 0:
                    gan_balance = abs(data['d_loss'][i]) / abs(data['g_loss'][i])
                    scalar('GAN_Metrics/Balance_Ratio', gan_balance, step=epoch)
    
    def close(self):
        """关闭写入器"""
        self.writer.close()

def run_advanced_monitoring():
    """运行高级监控"""
    from tensorboard_loss_visualizer import TensorBoardLossVisualizer
    
    log_file_path = "/home/<USER>/hmt/ACGAN-FG-main/logs/B_Hybrid_20250715_1354.log"
    
    # 基础可视化器
    basic_visualizer = TensorBoardLossVisualizer("./tensorboard_logs/basic")
    
    # 高级监控器
    advanced_monitor = AdvancedTensorBoardMonitor("./tensorboard_logs/advanced")
    
    try:
        print("🔍 解析日志文件...")
        data = basic_visualizer.parse_log_file(log_file_path)
        
        if len(data['epochs']) == 0:
            print("❌ 未找到有效数据！")
            return
        
        print(f"✅ 解析完成: {len(data['epochs'])} 个epoch")
        
        # 基础TensorBoard记录
        print("📊 写入基础TensorBoard数据...")
        basic_visualizer.write_to_tensorboard(data)
        
        # 高级可视化
        print("🚀 创建高级可视化...")
        advanced_monitor.log_loss_analysis(data)
        advanced_monitor.log_custom_visualizations(data)
        
        print("\n" + "="*60)
        print("🎉 TensorBoard可视化完成！")
        print("="*60)
        print("📊 启动命令:")
        print("  tensorboard --logdir=./tensorboard_logs")
        print("🌐 访问地址:")
        print("  http://localhost:6006")
        print("\n📈 可查看内容:")
        print("  • 基础损失曲线 (basic)")
        print("  • 高级分析图表 (advanced)")
        print("  • 实时损失分布")
        print("  • GAN训练平衡分析")
        
    finally:
        basic_visualizer.close()
        advanced_monitor.close()

if __name__ == "__main__":
    run_advanced_monitoring()
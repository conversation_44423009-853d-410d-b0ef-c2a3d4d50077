2025-07-27 14:36:02,402 - INFO - 批量训练开始
2025-07-27 14:36:02,402 - INFO - 
========================================
2025-07-27 14:36:02,402 - INFO - 📍 进度: 1/5 - 开始训练 Group A
2025-07-27 14:36:02,402 - INFO - ========================================
2025-07-27 14:36:02,402 - INFO - 🚀 开始训练 Group A - 2000 epochs
2025-07-27 14:36:02,402 - INFO - 执行命令: /usr/bin/python run_enhanced_baseline.py --group A --epochs 2000 --batch_size 120
2025-07-27 14:36:02,531 - INFO - [Group A] 2025-07-27 14:36:02.531088: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-07-27 14:36:02,538 - INFO - [Group A] 2025-07-27 14:36:02.538308: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:485] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered
2025-07-27 14:36:02,546 - INFO - [Group A] 2025-07-27 14:36:02.546588: E external/local_xla/xla/stream_executor/cuda/cuda_dnn.cc:8473] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
2025-07-27 14:36:02,549 - INFO - [Group A] 2025-07-27 14:36:02.549289: E external/local_xla/xla/stream_executor/cuda/cuda_blas.cc:1471] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered
2025-07-27 14:36:02,555 - INFO - [Group A] 2025-07-27 14:36:02.555508: I tensorflow/core/platform/cpu_feature_guard.cc:211] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
2025-07-27 14:36:02,555 - INFO - [Group A] To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-07-27 14:36:03,760 - INFO - [Group A] 2025-07-27 14:36:03,759 - INFO - 初始化训练器，组别: A, 日志目录: logs/baseline/Group-A_20250727-143603
2025-07-27 14:36:03,762 - INFO - [Group A] 2025-07-27 14:36:03.762010: E external/local_xla/xla/stream_executor/cuda/cuda_driver.cc:266] failed call to cuInit: CUDA_ERROR_NO_DEVICE: no CUDA-capable device is detected
2025-07-27 14:36:03,762 - INFO - [Group A] 2025-07-27 14:36:03.762028: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:135] retrieving CUDA diagnostic information for host: d354318c6ed3
2025-07-27 14:36:03,762 - INFO - [Group A] 2025-07-27 14:36:03.762031: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:142] hostname: d354318c6ed3
2025-07-27 14:36:03,762 - INFO - [Group A] 2025-07-27 14:36:03.762051: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:166] libcuda reported version is: 570.172.8
2025-07-27 14:36:03,762 - INFO - [Group A] 2025-07-27 14:36:03.762061: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:170] kernel reported version is: NOT_FOUND: could not find kernel module information in driver version file contents: "NVRM version: NVIDIA UNIX Open Kernel Module for x86_64  570.172.08  Release Build  (dvs-builder@U22-I3-AF01-21-3)  Tue Jul  8 18:08:21 UTC 2025
2025-07-27 14:36:03,762 - INFO - [Group A] GCC version:  gcc version 13.3.0 (Ubuntu 13.3.0-6ubuntu2~24.04)
2025-07-27 14:36:03,762 - INFO - [Group A] "
2025-07-27 14:36:04,186 - INFO - [Group A] 2025-07-27 14:36:04,186 - INFO - 开始为组别 A 进行训练，共 2000 轮。
2025-07-27 14:36:04,186 - INFO - [Group A] 2025-07-27 14:36:04,186 - INFO - 为组别 A 定义数据，看不见的类别: [1, 6, 14]
2025-07-27 14:36:04,187 - INFO - [Group A] 2025-07-27 14:36:04,187 - INFO - 看得见的类别: [2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 15]
2025-07-27 14:36:04,193 - INFO - [Group A] 2025-07-27 14:36:04,193 - INFO - 数据加载完成:
2025-07-27 14:36:04,193 - INFO - [Group A] 2025-07-27 14:36:04,193 - INFO -   - 训练数据形状: (5760, 52)
2025-07-27 14:36:04,193 - INFO - [Group A] 2025-07-27 14:36:04,193 - INFO -   - 训练属性形状: (5760, 20)
2025-07-27 14:36:04,193 - INFO - [Group A] 2025-07-27 14:36:04,193 - INFO -   - 测试数据形状: (2880, 52)
2025-07-27 14:36:04,193 - INFO - [Group A] 2025-07-27 14:36:04,193 - INFO -   - 测试属性形状: (2880, 20)
2025-07-27 14:36:05,767 - INFO - [Group A] WARNING:tensorflow:Calling GradientTape.gradient on a persistent tape inside its context is significantly less efficient than calling it outside the context (it causes the gradient ops to be recorded on the tape, leading to increased CPU and memory usage). Only call GradientTape.gradient inside the context if you actually want to trace the gradient in order to compute higher order derivatives.
2025-07-27 14:36:05,767 - INFO - [Group A] 2025-07-27 14:36:05,767 - WARNING - Calling GradientTape.gradient on a persistent tape inside its context is significantly less efficient than calling it outside the context (it causes the gradient ops to be recorded on the tape, leading to increased CPU and memory usage). Only call GradientTape.gradient inside the context if you actually want to trace the gradient in order to compute higher order derivatives.
2025-07-27 14:36:06,311 - INFO - [Group A] 2025-07-27 14:36:06,311 - INFO - [Epoch 1/2000][Batch 1/48] D Loss: 15.6246, G Loss: 786.8222, C Loss: 0.6810, M Loss: 5.2888, AE Loss: 1145061.3750
2025-07-27 14:36:06,827 - INFO - [Group A] 2025-07-27 14:36:06,827 - INFO - [Epoch 1/2000][Batch 2/48] D Loss: 4.2032, G Loss: 501.6859, C Loss: 0.6839, M Loss: 3.1728, AE Loss: 1147991.5000
2025-07-27 14:36:07,338 - INFO - [Group A] 2025-07-27 14:36:07,338 - INFO - [Epoch 1/2000][Batch 3/48] D Loss: -0.7237, G Loss: 181.1373, C Loss: 0.6841, M Loss: 1.7640, AE Loss: 1139465.8750
2025-07-27 14:36:07,845 - INFO - [Group A] 2025-07-27 14:36:07,844 - INFO - [Epoch 1/2000][Batch 4/48] D Loss: -3.1204, G Loss: 48.1058, C Loss: 0.6838, M Loss: 0.5378, AE Loss: 1139586.1250
2025-07-27 14:36:08,344 - INFO - [Group A] 2025-07-27 14:36:08,344 - INFO - [Epoch 1/2000][Batch 5/48] D Loss: -2.3545, G Loss: 44.6975, C Loss: 0.8970, M Loss: 0.8259, AE Loss: 1113096.1250
2025-07-27 14:36:08,850 - INFO - [Group A] 2025-07-27 14:36:08,850 - INFO - [Epoch 1/2000][Batch 6/48] D Loss: -4.2648, G Loss: 46.3919, C Loss: 0.8972, M Loss: 1.1545, AE Loss: 1113217.0000
2025-07-27 14:36:09,355 - INFO - [Group A] 2025-07-27 14:36:09,355 - INFO - [Epoch 1/2000][Batch 7/48] D Loss: -5.0691, G Loss: 57.7591, C Loss: 0.8972, M Loss: 1.0593, AE Loss: 1115415.6250
2025-07-27 14:36:09,876 - INFO - [Group A] 2025-07-27 14:36:09,876 - INFO - [Epoch 1/2000][Batch 8/48] D Loss: -4.8670, G Loss: 82.3979, C Loss: 0.8971, M Loss: 0.8700, AE Loss: 1112952.3750
2025-07-27 14:36:10,400 - INFO - [Group A] 2025-07-27 14:36:10,400 - INFO - [Epoch 1/2000][Batch 9/48] D Loss: -6.6869, G Loss: 110.1325, C Loss: 0.9714, M Loss: 0.7978, AE Loss: 1114336.7500
2025-07-27 14:36:10,904 - INFO - [Group A] 2025-07-27 14:36:10,904 - INFO - [Epoch 1/2000][Batch 10/48] D Loss: -7.4482, G Loss: 128.3593, C Loss: 0.9713, M Loss: 0.7973, AE Loss: 1114307.3750
2025-07-27 14:36:11,407 - INFO - [Group A] 2025-07-27 14:36:11,406 - INFO - [Epoch 1/2000][Batch 11/48] D Loss: -8.3723, G Loss: 129.6192, C Loss: 0.9712, M Loss: 0.7969, AE Loss: 1112799.2500
2025-07-27 14:36:11,902 - INFO - [Group A] 2025-07-27 14:36:11,902 - INFO - [Epoch 1/2000][Batch 12/48] D Loss: -9.0850, G Loss: 118.7060, C Loss: 0.9714, M Loss: 0.7677, AE Loss: 1113432.3750
2025-07-27 14:36:12,423 - INFO - [Group A] 2025-07-27 14:36:12,423 - INFO - [Epoch 1/2000][Batch 13/48] D Loss: -9.3653, G Loss: 102.9861, C Loss: 0.9308, M Loss: 0.7216, AE Loss: 1109893.8750
2025-07-27 14:36:12,919 - INFO - [Group A] 2025-07-27 14:36:12,918 - INFO - [Epoch 1/2000][Batch 14/48] D Loss: -9.8770, G Loss: 87.9059, C Loss: 0.9291, M Loss: 0.6859, AE Loss: 1109165.2500
2025-07-27 14:36:13,431 - INFO - [Group A] 2025-07-27 14:36:13,431 - INFO - [Epoch 1/2000][Batch 15/48] D Loss: -10.4351, G Loss: 76.8176, C Loss: 0.9306, M Loss: 0.6756, AE Loss: 1114056.0000
2025-07-27 14:36:13,943 - INFO - [Group A] 2025-07-27 14:36:13,943 - INFO - [Epoch 1/2000][Batch 16/48] D Loss: -11.3415, G Loss: 70.4427, C Loss: 0.9305, M Loss: 0.6893, AE Loss: 1110383.2500
2025-07-27 14:36:14,442 - INFO - [Group A] 2025-07-27 14:36:14,442 - INFO - [Epoch 1/2000][Batch 17/48] D Loss: -12.7831, G Loss: 69.1510, C Loss: 0.8864, M Loss: 0.7110, AE Loss: 1111376.6250
2025-07-27 14:36:14,950 - INFO - [Group A] 2025-07-27 14:36:14,950 - INFO - [Epoch 1/2000][Batch 18/48] D Loss: -13.0929, G Loss: 69.7721, C Loss: 0.8842, M Loss: 0.7272, AE Loss: 1111309.0000
2025-07-27 14:36:15,450 - INFO - [Group A] 2025-07-27 14:36:15,450 - INFO - [Epoch 1/2000][Batch 19/48] D Loss: -13.5218, G Loss: 72.6147, C Loss: 0.8859, M Loss: 0.7325, AE Loss: 1118000.1250
2025-07-27 14:36:15,957 - INFO - [Group A] 2025-07-27 14:36:15,956 - INFO - [Epoch 1/2000][Batch 20/48] D Loss: -13.8176, G Loss: 76.7287, C Loss: 0.8857, M Loss: 0.7305, AE Loss: 1112657.2500
2025-07-27 14:36:16,472 - INFO - [Group A] 2025-07-27 14:36:16,472 - INFO - [Epoch 1/2000][Batch 21/48] D Loss: -14.3132, G Loss: 83.2795, C Loss: 0.7377, M Loss: 0.7261, AE Loss: 1118772.5000
2025-07-27 14:36:16,967 - INFO - [Group A] 2025-07-27 14:36:16,967 - INFO - [Epoch 1/2000][Batch 22/48] D Loss: -15.0530, G Loss: 87.4005, C Loss: 0.7377, M Loss: 0.7227, AE Loss: 1093092.7500
2025-07-27 14:36:17,475 - INFO - [Group A] 2025-07-27 14:36:17,475 - INFO - [Epoch 1/2000][Batch 23/48] D Loss: -15.5640, G Loss: 89.6515, C Loss: 0.7379, M Loss: 0.7191, AE Loss: 1121364.6250

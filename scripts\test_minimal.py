import tensorflow as tf
import numpy as np
from sklearn import preprocessing
from sklearn.metrics import accuracy_score
from sklearn.naive_bayes import GaussianNB

def scalar_stand(Train_X, Test_X):
    scalar_train = preprocessing.StandardScaler().fit(Train_X)
    Train_X = scalar_train.transform(Train_X)
    Test_X = scalar_train.transform(Test_X)
    return Train_X, Test_X

def feature_generation_and_diagnosis_minimal(add_quantity, test_x, test_y, autoencoder, generator, classifier, test_class_indices):
    """
    极简GAN测试函数 - 只生成少量样本，只用最快分类器
    适合每轮训练监控，不影响GAN训练特性
    """
    
    # 大幅减少样本数量 - 从2000降到200
    quick_quantity = 200
    
    all_classes = np.arange(1, 16)
    seen_classes = np.setdiff1d(all_classes, test_class_indices)
    
    Labels_train = []
    Labels_test = []
    Generated_feature = []
    samples_per_class = len(test_x) // len(test_class_indices)
    
    # 快速特征生成
    for i, class_idx in enumerate(test_class_indices):
        attribute_index = i * samples_per_class
        attribute_vector = test_y[attribute_index]
        
        attribute = np.array([attribute_vector for _ in range(quick_quantity)])
        noise_shape = (quick_quantity, 50, 1)
        noise = tf.random.normal(shape=noise_shape)

        # 关闭verbose，加快预测
        generated_feature = generator.predict([noise, attribute], verbose=0)
        Generated_feature.append(generated_feature)

        labels_train = np.full((quick_quantity, 1), class_idx)
        labels_test = np.full((samples_per_class, 1), class_idx)

        Labels_train.append(labels_train)
        Labels_test.append(labels_test)
    
    Generated_feature = np.array(Generated_feature).reshape(-1, 256)
    Labels_train = np.array(Labels_train).reshape(-1, 1)
    Labels_test = np.array(Labels_test).reshape(-1, 1)
    
    # 快速处理测试数据
    test_feature, decoded_test = autoencoder(test_x)
    
    hidden_ouput_train, predict_attribute_train = classifier(Generated_feature)
    new_feature_train = np.concatenate((Generated_feature, hidden_ouput_train), axis=1)

    hidden_ouput_test, predict_attribute_test = classifier(test_feature)
    new_feature_test = np.concatenate((test_feature, hidden_ouput_test), axis=1)

    train_X = new_feature_train
    train_Y = Labels_train
    test_X = new_feature_test
    test_Y = Labels_test

    train_X, test_X = scalar_stand(train_X, test_X)

    # 只使用最快的朴素贝叶斯分类器
    classifier_pnb = GaussianNB()
    classifier_pnb.fit(train_X, train_Y.ravel())
    Y_pred_pnb = classifier_pnb.predict(test_X)
    accuracy_pnb = accuracy_score(test_Y, Y_pred_pnb)
    
    # 返回相同格式但只有一个有效值，保持兼容性
    return accuracy_pnb, accuracy_pnb, accuracy_pnb, accuracy_pnb
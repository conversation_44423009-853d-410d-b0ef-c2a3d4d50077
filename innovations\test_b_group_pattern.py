#!/usr/bin/env python3
"""
🎯 B组成功模式测试

基于B组61.04%成功经验，测试A组和D组：
1. 使用B组的成功损失权重配置
2. 复制B组的训练动态
3. 验证是否能提升A组和D组的表现
"""

import os
import sys
import argparse
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')

from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer


def test_b_group_pattern(group='A', epochs=500):
    """使用B组成功模式测试其他组"""
    
    print(f"""
🎯 B组成功模式测试
=====================================
📊 目标组别: {group}
🔄 训练轮次: {epochs}
⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🏆 B组成功经验:
- 最佳准确率: 61.04%
- 关键突破: Epoch 90 (46.22% → 58.06%)
- 损失特征: 平滑下降，无剧烈波动
- 循环损失: 281.7 (适中且稳定)

🎯 应用策略:
✅ 循环损失权重: 0.1 → 0.3 (B组模式)
✅ 保持其他优化的权重设置
✅ 训练500轮次观察突破点
✅ 重点监控Epoch 90附近的变化
=====================================
    """)
    
    try:
        # 初始化训练器
        trainer = EnhancedASDCGANTrainer(
            device='cuda',
            batch_size=32,
            learning_rate_g=0.0002,
            learning_rate_d=0.0004
        )
        
        # 加载数据
        trainer.load_data(split_group=group)
        
        # 开始训练
        print(f"🚀 开始{group}组B模式训练...")
        best_accuracy = trainer.train_enhanced(epochs=epochs)
        
        # 结果分析
        print(f"""
🎉 B组模式测试完成！
=====================================
📊 {group}组结果:
- 最佳准确率: {best_accuracy:.2f}%
- 与B组对比: 61.04% vs {best_accuracy:.2f}%
- 改进效果: {'✅ 显著提升' if best_accuracy > 55 else '🔧 需要进一步优化'}

📈 与原始结果对比:
""")
        
        # 显示原始结果对比
        original_results = {
            'A': 60.76,
            'B': 61.04,
            'C': 44.72,
            'D': 59.76,
            'E': 47.67
        }
        
        original = original_results.get(group, 0)
        improvement = best_accuracy - original
        
        print(f"- 原始{group}组: {original:.2f}%")
        print(f"- B模式{group}组: {best_accuracy:.2f}%")
        print(f"- 改进幅度: {improvement:+.2f}%")
        print(f"- 改进状态: {'✅ 成功' if improvement > 2 else '⚠️ 微弱' if improvement > 0 else '❌ 退化'}")
        
        print(f"""
💡 分析建议:
{'- B组模式有效，可以应用到其他组' if improvement > 2 else '- 需要进一步分析B组的其他成功因素'}
{'- 建议增加训练轮次到1000+' if best_accuracy > 50 else '- 可能需要调整其他超参数'}
=====================================
        """)
        
        return best_accuracy
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0.0


def main():
    parser = argparse.ArgumentParser(description='🎯 B组成功模式测试')
    parser.add_argument('--group', type=str, choices=['A', 'C', 'D', 'E'], 
                       default='A', help='测试组别 (B组已经成功)')
    parser.add_argument('--epochs', type=int, default=500, 
                       help='训练轮次 (默认500)')
    
    args = parser.parse_args()
    
    print(f"""
🎯 B组成功模式复制测试
=====================================
📋 测试计划:
- 目标组别: {args.group}
- 训练轮次: {args.epochs}
- 基准模式: B组61.04%成功经验

🔬 实验假设:
B组的成功主要来自于：
1. 适中的循环损失权重 (0.3)
2. 平滑的训练动态
3. 特定的损失平衡策略

📊 预期结果:
- A组: 60.76% → 65%+ (目标)
- D组: 59.76% → 65%+ (目标)
- C组: 44.72% → 55%+ (目标)
- E组: 47.67% → 55%+ (目标)
=====================================
    """)
    
    # 确认是否继续
    try:
        response = input(f"🤔 确认开始{args.group}组B模式测试？(y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ 测试已取消")
            return
    except KeyboardInterrupt:
        print("\n❌ 测试已取消")
        return
    
    # 运行测试
    best_accuracy = test_b_group_pattern(args.group, args.epochs)
    
    # 最终总结
    print(f"""
🏁 B组模式测试总结
=====================================
🎯 测试组别: {args.group}
📊 最终准确率: {best_accuracy:.2f}%
⏱️ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔄 下一步建议:
1. 如果效果好：应用到其他组别
2. 如果效果一般：分析B组的其他成功因素
3. 如果效果差：重新审视B组成功的根本原因

📁 查看详细结果:
- 实验目录: experiments/group_{args.group}/
- 训练曲线: experiments/group_{args.group}/*/training_curves.png
=====================================
    """)


if __name__ == "__main__":
    main()

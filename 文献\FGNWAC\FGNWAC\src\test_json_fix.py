#!/usr/bin/env python3
"""
测试JSON序列化修复
"""

import json
import numpy as np
from hyperparameter_search import NumpyEncoder, convert_numpy_types

def test_json_serialization():
    """测试JSON序列化修复"""
    print("🧪 测试JSON序列化修复")
    print("-" * 40)
    
    # 创建包含numpy类型的测试数据
    test_data = {
        'experiment_name': 'test_experiment',
        'best_accuracy': np.float32(0.7958),  # numpy float32
        'best_epoch': np.int64(25),           # numpy int64
        'total_epochs': np.int32(50),         # numpy int32
        'early_stopped': np.bool_(True),      # numpy bool
        'parameters': {
            'lambda_ar': np.float64(0.8),     # numpy float64
            'lr': np.float32(0.0001)          # numpy float32
        },
        'metrics': np.array([0.1, 0.2, 0.3]) # numpy array
    }
    
    print("原始数据类型:")
    for key, value in test_data.items():
        print(f"  {key}: {type(value)} = {value}")
    
    try:
        # 测试原始json.dump（应该失败）
        print("\n❌ 测试原始json.dump:")
        json.dumps(test_data)
        print("  意外成功！")
    except TypeError as e:
        print(f"  预期失败: {e}")
    
    try:
        # 测试使用NumpyEncoder
        print("\n✅ 测试使用NumpyEncoder:")
        json_str = json.dumps(test_data, cls=NumpyEncoder, indent=2)
        print("  成功序列化!")
        
        # 反序列化验证
        loaded_data = json.loads(json_str)
        print("  反序列化成功!")
        print("  反序列化后的数据:")
        for key, value in loaded_data.items():
            print(f"    {key}: {type(value)} = {value}")
            
    except Exception as e:
        print(f"  失败: {e}")
    
    try:
        # 测试convert_numpy_types函数
        print("\n✅ 测试convert_numpy_types函数:")
        converted_data = convert_numpy_types(test_data)
        print("  类型转换成功!")
        
        # 检查转换后的类型
        print("  转换后的数据类型:")
        for key, value in converted_data.items():
            print(f"    {key}: {type(value)} = {value}")
        
        # 测试JSON序列化
        json_str = json.dumps(converted_data, indent=2)
        print("  JSON序列化成功!")
        
    except Exception as e:
        print(f"  失败: {e}")
    
    print("\n🎉 JSON序列化修复测试完成!")

if __name__ == "__main__":
    test_json_serialization()

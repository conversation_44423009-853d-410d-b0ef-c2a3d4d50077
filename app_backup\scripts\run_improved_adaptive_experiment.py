#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行改进后的自适应权重实验
对比原始、改进自适应和固定权重三种策略
"""

import subprocess
import datetime
import os
import json

def run_experiment(group='C', epochs=2000, strategy='improved_adaptive'):
    """运行单个实验"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M")
    log_filename = f"结果/{timestamp}_{strategy}_Group{group}.md"
    
    print(f"🚀 开始实验: {strategy} - Group {group}")
    print(f"📝 日志文件: {log_filename}")
    
    if strategy == 'improved_adaptive':
        # 使用改进后的自适应权重
        cmd = f"python acgan_triplet_adaptive.py"
        env_vars = {
            'TARGET_GROUP': group,
            'USE_ADAPTIVE': 'True',
            'EPOCHS': str(epochs),
            'LOG_FILE': log_filename
        }
    elif strategy == 'original_adaptive':
        # 需要先恢复原始的激进策略...
        print("⚠️  需要手动恢复原始策略进行对比")
        return None
    else:  # fixed_weights
        cmd = f"python acgan_triplet_adaptive.py"
        env_vars = {
            'TARGET_GROUP': group,
            'USE_ADAPTIVE': 'False',
            'EPOCHS': str(epochs),
            'LOG_FILE': log_filename
        }
    
    # 在这里您需要根据实际情况运行实验
    print(f"💡 命令: {cmd}")
    print(f"📋 环境变量: {env_vars}")
    
    return log_filename

def compare_strategies():
    """对比不同策略的效果"""
    print("=" * 60)
    print("🔬 自适应权重策略对比实验")
    print("=" * 60)
    
    strategies = [
        ('fixed_weights', '固定权重基准'),
        ('improved_adaptive', '改进自适应权重'),
    ]
    
    group = 'C'  # 使用Group C进行测试
    epochs = 500  # 快速测试用较少epoch
    
    results = {}
    
    for strategy, description in strategies:
        print(f"\n📊 开始策略: {description}")
        log_file = run_experiment(group, epochs, strategy)
        if log_file:
            results[strategy] = {
                'description': description,
                'log_file': log_file,
                'status': 'ready'
            }
    
    # 保存实验配置
    experiment_config = {
        'timestamp': datetime.datetime.now().isoformat(),
        'group': group,
        'epochs': epochs,
        'strategies': results,
        'improvements': {
            'weight_adjustment': '使用tanh函数平滑映射，限制调整幅度',
            'max_triplet_change': '±30%',
            'max_cla_change': '±20%',
            'weight_range': '[0.7x, 1.5x]',
            'expected_benefit': '更稳定的训练过程，更好的收敛性'
        }
    }
    
    config_filename = f"experiment_config_{datetime.datetime.now().strftime('%Y%m%d%H%M')}.json"
    with open(config_filename, 'w', encoding='utf-8') as f:
        json.dump(experiment_config, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 实验配置已保存: {config_filename}")
    return experiment_config

def analyze_weight_changes():
    """分析权重变化的理论效果"""
    print("\n🔍 权重调整策略分析")
    print("-" * 40)
    
    difficulty_score = 1.7941107824805875  # Group C的实际难度分数
    
    # 原始激进策略
    original_triplet = 10 * (1 + 1.0 * difficulty_score)
    original_cla = 10 / (1 + 0.5 * difficulty_score)
    
    # 改进温和策略
    import math
    normalized_difficulty = math.tanh((difficulty_score - 1.0) * 0.5)
    improved_triplet_adj = max(0.7, min(1.5, 1.0 + normalized_difficulty * 0.3))
    improved_cla_adj = max(0.7, min(1.5, 1.0 - normalized_difficulty * 0.2))
    improved_triplet = 10 * improved_triplet_adj
    improved_cla = 10 * improved_cla_adj
    
    print(f"📈 难度分数: {difficulty_score:.4f}")
    print(f"📈 标准化难度: {normalized_difficulty:.4f}")
    print()
    
    print("🔴 原始激进策略:")
    print(f"  Triplet: 10.0 → {original_triplet:.2f} ({(original_triplet/10-1)*100:+.1f}%)")
    print(f"  分类:    10.0 → {original_cla:.2f} ({(original_cla/10-1)*100:+.1f}%)")
    print(f"  比例:    {original_triplet/original_cla:.2f}:1")
    print()
    
    print("🟢 改进温和策略:")
    print(f"  Triplet: 10.0 → {improved_triplet:.2f} ({(improved_triplet/10-1)*100:+.1f}%)")
    print(f"  分类:    10.0 → {improved_cla:.2f} ({(improved_cla/10-1)*100:+.1f}%)")
    print(f"  比例:    {improved_triplet/improved_cla:.2f}:1")
    print()
    
    print("💡 改进优势:")
    print("  ✓ 权重调整更温和，训练更稳定")
    print("  ✓ 保持损失函数的平衡性")
    print("  ✓ 避免过拟合风险")
    print("  ✓ 更好的泛化能力")

def main():
    """主函数"""
    print("🎯 自适应权重改进实验")
    print("=" * 50)
    
    # 分析权重变化
    analyze_weight_changes()
    
    # 设置对比实验
    print("\n" + "=" * 50)
    experiment_config = compare_strategies()
    
    print(f"\n📋 实验总结:")
    print(f"  - 已修改权重调整策略，使用温和的tanh映射")
    print(f"  - 限制权重调整范围在[0.7x, 1.5x]")
    print(f"  - 可运行实验对比改进效果")
    print(f"  - 预期得到更稳定的训练和更好的泛化性能")
    
    print(f"\n🚀 下一步操作:")
    print(f"  1. 在Docker环境中运行: python acgan_triplet_adaptive.py")
    print(f"  2. 对比改进前后的结果")
    print(f"  3. 验证权重调整的合理性")

if __name__ == "__main__":
    main() 
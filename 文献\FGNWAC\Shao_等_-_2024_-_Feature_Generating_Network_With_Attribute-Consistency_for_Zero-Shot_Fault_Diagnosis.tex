\documentclass[10pt]{article}
\usepackage[verbose, a4paper, hmargin=2.5cm, vmargin=2.5cm]{geometry}

\usepackage{fontspec}
\usepackage{ctex}
\usepackage{paratype}


\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{esint}

\usepackage{graphicx}
\usepackage[export]{adjustbox}
\usepackage{mdframed}
\usepackage{booktabs,array,multirow}
\usepackage{adjustbox}
\usepackage{tabularx}
\usepackage{hyperref}
\hypersetup{colorlinks=true, linkcolor=blue, filecolor=magenta, urlcolor=cyan,}
\urlstyle{same}
\usepackage[most]{tcolorbox}
\definecolor{mygray}{RGB}{240,240,240}
\tcbset{
  colback=mygray,
  boxrule=0pt,
}
\graphicspath{ {./images/} }
\newcommand{\HRule}{\begin{center}\rule{0.9\linewidth}{0.2mm}\end{center}}
\newcommand{\customfootnote}[1]{
  \let\thefootnote\relax\footnotetext{#1}
}

\begin{document}
\section*{Feature Generating Network With Attribute-Consistency for Zero-Shot Fault Diagnosis}

Lexuan Shao \({}^{\circledR }\) , Ningyun Lu \({}^{\circledR }\) , Member, IEEE, Bin Jiang \({}^{\circledR }\) , Fellow, IEEE, and Silvio Simani \({}^{ \circ  }\) , Senior Member, IEEE

Abstract-The absence of fault data in certain categories presents a significant challenge in data-driven fault diagnosis, as obtaining a complete fault dataset is often unfeasible. Zero-shot learning has emerged as a viable solution to this problem. Nonetheless, it often encounters problem of unreliable diagnosis results due to domain shift. In this article, a feature generating network with attribute-consistency is developed for zero-shot fault diagnosis, which introduces the attribute consistency constraint and feature transformation with attribute information. The implementation process comprises two parts, unseen fault class generation and discriminative feature transformation. The attribute consistency constraint adopted in data generation can make the generated data represent their attribute well. For feature transformation, a concatenation operation is used to transforming the generated samples into more discriminative representations. The effectiveness of the proposed method is verified using a public dataset for fault diagnosis purpose. Results indicate that the proposed method outperforms the state-of-art zero-shot diagnosis method.

Index Terms-Attribute consistency, feature transformation, generating network, zero-shot fault diagnosis.

\section*{NOMENCLATURE}

Notations Used in the Work

Symbol Description.

\({D}_{s}\;\) Seen class dataset.

\({D}_{u}\;\) Unseen class dataset.

\({N}_{s}\;\) Number of training samples.

\(S\) Number of seen classes.

\(U\) Number of unseen classes.

Attribute space.

Sample feature.

Label space.

Output of hidden layer.

Discriminator iterations.

Factor of VAE Loss.

\({\lambda }_{1}\) Factor of AR Loss.

\section*{I. INTRODUCTION}

TAULT diagnosis for critical equipment is crucial, and cur- rently data-driven based methods are gaining increasing attention in fault diagnosis [1], [2], [3], [4], [5]. Data-driven based fault diagnosis cannot be separated from historical data of equipment under different fault conditions, which are often derived fault injection experiments or the analysis of large amounts of supervisory control and data acquisition data. However, some faults may require the design of destructive experiments, which is economically unfeasible for expensive equipment, making it difficult to obtain fault data under these conditions. Therefore, there is a practical interest in studying zero-shot fault diagnosis, whose definition will be presented in Section II.

The scenario mentioned earlier exists not just in fault diagnosis framework but also in the field of computer vision, which is referred to as zero-shot learning (ZSL) [6]. The fundamental idea of this learning mode is to introduce the attributes of every category and use them as a linkage between seen and unseen classes. This methodology has been very successful in image recognition, and several models with superior performance have emerged with great zeal [7], [8], [9], [10], [11], [12]. For example, Zhang et al. [13] solved the problem of unavailable videos about certain activity classes in temporal activity detection using ZSL. Currently, some experts and researchers in fault diagnosis have investigated the idea of ZSL. Feng and Zhao [14] adapted the classical learning model of ZSL, known as direct attribute prediction (DAP), by introducing a fault attribute prediction layer into the middle of the fault classifier, for the purpose of classifying unseen faults. Chen et al. [15] developed a deep attention relation network to extract fault knowledge from unseen class data using only seen class data, thereby accomplishing cross-conditions fault diagnosis. Moreover, certain researchers have improved diagnostic performance by aligning the consistency of fault feature distribution and attribute distribution. Hu et al. [16] developed a consistent loss function for feature and attribute distribution using the Barlow matrix to enhance classification performance. Li et al. [17] narrowed the disparity between feature and attribute domains by utilizing a bidirectional alignment network to enhance the consistency of features and their corresponding attributes in a hidden space. Other studies have developed fault attribute learning models to address the challenge of defining difficult fault attributes, which reduces labor costs and enhances the automation of diagnostic models as described in [18], [19], and [20]. For example, recently, generative adversarial networks (GAN) have been employed in ZSL models for their superior performance in generating data [21]. The main concept behind this method is to generate samples of unseen fault classes by attributes. For instance, Lv et al. [22] employed a conditional adversarial denoising autoencoder to generate unseen fault classes samples using mixed attributes as conditions. Zhuo and Ge [23] designed GAN combined with triplet loss to generate discriminative fault data. Yan et al. [24] proposed a differentiable GAN architecture search method to achieve good performance across diversified datasets or scenarios.

\customfootnote{

Manuscript received 12 August 2023; revised 25 December 2023; accepted 2 February 2024. Date of publication 26 February 2024; date of current version 6 May 2024. This work was supported in part by the National Key R\&D Program of China under Grant 2021YFB3301300 and in part by the National Natural Science Foundation of China under Grant 62273176. Paper no. TII-23-3093. (Corresponding author: Ningyun Lu.)

Lexuan Shao, Ningyun Lu, and Bin Jiang are with the College of Automation Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing 210016, China (e-mail: <EMAIL>; luningyun@ nuaa.edu.cn; <EMAIL>).

Silvio Simani is with the Department of Engineering, University of Ferrara, 44122 Ferrara, Italy (e-mail: <EMAIL>).

Color versions of one or more figures in this article are available at https://doi.org/10.1109/TII.2024.3363078.

Digital Object Identifier 10.1109/TII.2024.3363078

}

Overall, the current ZSL models have made progress in addressing the challenge of data-driven fault diagnosis when fault data specific to a class are unavailable. Nevertheless, some crucial concerns still require attention. There is always a bias present when transferring models trained on seen class samples to the unseen class. This is demonstrated by the feature-attribute embedding based zero-shot diagnostic model, which predicts the attributes of unseen class samples with bias toward the attributes observed in the seen class samples [9]. In the generative-based zero-shot diagnostic model, this phenomenon results in the generated samples showing little match to attribute features, leading to significant differences between the generated and real sample features. This effect is commonly known as domain shift [25].

To tackle the aforementioned issues, this article suggests a zero-shot diagnosis model with attribute-consistency. More specifically, a GAN-based model projects fault attribute representation into a feature representation, similar to that of a conventional generative-based ZSL model. Nevertheless, the generated sample is also taken as input to a regressor that reconstructs the original fault attribute representation. The supplementary reconstruction task imposes a new constraint on learning the attribute \(\rightarrow\) feature projection function. The projection should also retain all the information from the original attribute space to ensure that they can be recovered by the regressor. An intuitive explanation is provided in the following. Although the feature representation of attributes may differ between seen and unseen class sets, it is widely applicable to improve the accurate reconstruction of attributes in both domains. As a result, the learned project function will perform optimally in both domains. In addition, the attribute regressor's output from the hidden layer is extracted and combined with the generated samples to augment attribute features present in the samples. The entire model aims to produce samples with attribute-consistent features. This study presumes that the domain shift issue can be effectively mitigated if the generated sample features are highly consistent with their attributes.

It is worth noting that there exist some prior generative-based ZSL models created for solving the domain shift problem, including models semantic refinement Wasserstein generative adversarial network (SRWGAN) [26] and feature refinement for generalized zero-shot learning (FREE) [27]. Feng et al. [26] developed SRWGAN, a fault attribute model for improved fault information transfer through attribute refinement. The idea of this model is perpendicular to the model proposed in this article. Chen et al. [27] proposed the FREE model with the same idea as the model proposed in this article. It is from the perspective of designing model regularization terms, expecting to generate attribute consistency of samples through structural constraints from attributes to samples to attributes. The difference lies in the design of attribute regressors, and this article considers more practical constraints on attribute regressors, such as rank loss hinge and mutual information, which improves the effect of the model more obviously.

Lastly, we confirm the efficacy of the model on a public dataset and conduct a visualization analysis. The contributions of this article can be summarized as follows.

1) A generative model is proposed that can be easily trained and perform the data-driven fault diagnosis tasks in the absence of data for certain fault categories.

2) By designing the attribute-consistent loss and transformation process of the generated samples, the issue that the features of the generated samples of unseen class faults may deviate from the real faults due to domain shift is, thus, alleviated.

3) Attribute regressors designed with hinge rank loss and mutual information constraint have a strong ability to predict attribute information, improving model performance above other state-of-the-art methods.

The rest of this article is organized as follows. Section II introduces the necessary preliminary details for the method. Section III describes the suggested model and its implementation process. Section IV presents the experiments. Finally, Section V concludes this article, including some final remarks and unresolved issues for future research.

\section*{II. PRELIMINARY}

\section*{A. Problem Definition}

For clarity, we first describe the zero-shot fault diagnosis task as follows. There are some labeled fault samples available during training, which are denoted as \({D}_{s} = \left\{  {\left( {{x}_{i}{}^{s},{y}_{i}{}^{s}}\right) ,i = }\right.\)  \(\left. {1,\ldots ,{N}_{s}}\right\}\) , where \({x}_{i}{}^{s}\) is a fault sample and \({y}_{i}{}^{s} \in  {\mathrm{Y}}_{s} =\)  \(\{ 1,\ldots ,S\}\) is the corresponding label called seen classes. There are also some unavailable samples \({D}_{u} = \left\{  {{x}_{i}{}^{u},i = 1,\ldots ,{N}_{s}}\right\}\) during training, which belong to unseen classes \({\mathrm{Y}}_{u} = \{ S +\)  \(1,\ldots ,S + U\}\) . Note that \({Y}_{s} \cap  {Y}_{u} = \varnothing\) , i.e., there is no overlap between seen class and unseen class. But they are associated in a common semantic space \(A\) , which is the knowledge bridge between the seen classes and unseen classes. This semantic space is usually provided by expert knowledge, so it is also called the fault attribute space. Each fault class \(y \in  {Y}_{s} \cup  {Y}_{u}\) can be described by a fault attribute \({a}_{y} \in  A\) , and the training set \({D}_{s}\) can be represented as \(\left\{  {\left( {{x}_{i}{}^{s},{a}_{{y}_{i}^{s}}}\right) ,i = 1,\ldots ,{N}_{s}}\right\}\) . The goal of zero-shot fault diagnosis is to predict the label \({y}_{i}{}^{u} \in  {\mathrm{Y}}_{u}\) given \({x}_{i}{}^{u}\) based on fault attribute. Specifically, in generative based zero-shot fault diagnosis, we need to generate the sample of unseen class \({x}^{\text{ syn }}\) by utilizing the attribute \({a}_{{y}^{u}}\) .

\begin{center}
\includegraphics[max width=0.5\textwidth]{images/bo_d20576n7aajc73bfkneg_2_108_183_738_341_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 1. Comparison between traditional and zero-shot fault diagnosis approaches. (a) Traditional fault diagnosis. (b) Zero-shot fault diagnosis.

\begin{center}
\includegraphics[max width=0.5\textwidth]{images/bo_d20576n7aajc73bfkneg_2_146_658_668_335_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 2. Example of domain shift problem.

\section*{B. Domain Shift}

Since the \({D}_{s}\) and \({D}_{u}\) have unrelated classes, the underlying attribute vector of classes also differ, as do the optimal projection functions between the sample space and the attribute spaces. A projection function mapping sample space into the attribute space is learned from \({D}_{s}\) . Such a projection being then applied directly to map \({D}_{u}\) into A without adaptation will introduce an unknown shift. Specifically, in generative-based zero-shot fault diagnosis, the generator is trained with seen class samples, and when generating unseen class samples, there is a large gap between the generated samples and the actual sample features, as depicted in Fig. 2.

\section*{III. METHOD}

This section introduces the solution proposed in this study. In particular, Fig. 3 illustrates the overview of this methodology, whose details are described in the following four steps.

1) Data Processing. The collected seen class fault data are first input to a pretrained feature extraction network to extract the corresponding features for each sample. Then, a fault attribute analysis is performed on all possible fault classes to define the attributes of each class of faults.

2) Unseen Class Fault Data Generation. A generative model is trained with the seen class samples after extracting features in Step 1, so that the model has the ability to generate unseen class samples. Then, the model is used to generate the unseen class fault samples.

3) Feature Transformation. The generated unseen class samples and seen class samples are put into the attribute regression model, and the hidden layer output of the regression model is extracted and combined with the samples so that the samples have new discriminative information.

4) Diagnosis Model Training. The transformed samples of seen and unseen classes are used as the training set to train a classification model. The model has the ability to identify the unseen class samples.

Steps 2 and 3 are the core of this method, are thoroughly described below, and the remaining steps are demonstrated in the next section.

\section*{A. Data Feature Generation}

Since only fault samples of the seen class are available, auxiliary information is needed to generate unseen fault samples, i.e., fault attributes \(a = \left\lbrack  {{a}_{1},{a}_{2},\ldots ,{a}_{k}}\right\rbrack  ,{a}_{i}\) is 1 when it indicates the presence of the \(i\) th attribute, whereas 0 if the attribute is missing. GAN has the ability to map a random distribution to a target distribution. To improve the stability of the generative model, variational autoencoder generative adversarial network (VAEGAN) [28], which is easy to train, is chosen. The key task in this step is mapping the attribute distribution of each class to the corresponding feature distribution. Set the training set \(D = \left\{  {\left( {{x}_{i}{}^{s},{a}_{i}^{s}}\right) ,i = 1,\ldots ,{N}_{s}}\right\}\) , where \({x}_{i}^{s}\) represents the fault sample and \({a}_{i}^{s}\) represents the fault attribute.

In variational autoencoder (VAE) part, as illustrated in Fig. 4, the real samples \({x}_{i}^{s}\) and \({a}_{i}^{s}\) are input into the encoder block, compressed to a hidden variable \(z\) . Subsequently, \(z\) and \({a}_{i}^{s}\) are fed together into the generator block to reconstruct/generate synthetic samples, and the VAE part of the optimization function is as follows:

\[
{\mathcal{L}}_{\mathrm{{VAE}}} = \mathrm{{KL}}\left( {\mathrm{E}\left( {{x}_{i},{a}_{{y}_{i}}}\right) \parallel p\left( {z \mid  {a}_{{y}_{i}}}\right) }\right)
\]

\[
- {\mathbb{E}}_{\mathrm{E}\left( {{x}_{i},{a}_{{y}_{i}}}\right) }\left\lbrack  {\log G\left( {z,{a}_{{y}_{i}}}\right) }\right\rbrack   \tag{1}
\]

where KL represents Kullback-Leibler divergence, and \(p\left( {z \mid  a}\right)\) is the prior distribution, set as a standard normal distribution. The first term of \({L}_{\mathrm{{VAE}}}\) indicates that the encoder prefers to compress the joint distribution to the prior distribution \(p\left( {z \mid  a}\right)\) , whilst the second term guarantees that the reconstruction error of \(\mathrm{G}\left( {z,a}\right)\) should be as small as possible, making the inputs and outputs as similar as possible. This is the key to ensuring stable model training. While this alone does not provide the diversity of the generated samples, there is a need to introduce a discriminator, which works with the generator to form a GAN structure.

For the GAN part, the reconstructed sample \(\mathrm{G}\left( {z,a}\right)\) is fed into the discriminator together with the real sample to output a real value. Moreover, the Wasserstein distance [29] is utilized to reduce the risk of gradient disappearance, and the gradient penalty strategy is adopted to satisfy the Lipschitz restriction. The optimization function for the GAN part has the following

\begin{center}
\includegraphics[max width=0.8\textwidth]{images/bo_d20576n7aajc73bfkneg_3_325_182_1101_953_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 3. Diagram of the proposed method.

\begin{center}
\includegraphics[max width=0.5\textwidth]{images/bo_d20576n7aajc73bfkneg_3_161_1240_661_274_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 4. Block scheme of the proposed architecture.

form:

\[
{\mathcal{L}}_{\text{ WGAN }} = D\left( {{x}_{i},{a}_{{y}_{i}}}\right)  - D\left( {G\left( {z,{a}_{{y}_{i}}}\right) ,{a}_{{y}_{i}}}\right)  \tag{2}
\]

\[
- \lambda \mathbb{E}\left\lbrack  {\left( \left| \right| {\nabla }_{{x}^{ * }}D\left( {x}^{ * },{a}_{{y}_{i}}\right) {\left| \right| }_{2} - 1\right) }^{2}\right\rbrack
\]

where \({x}^{ * } = \alpha {x}_{i} + \left( {1 - \alpha {x}_{i}}\right) ,\alpha  \sim  U\left( {0,1}\right)\) , and \(\lambda\) is the penalty factor.

The samples generated by relying only on the previous model will exhibit unpredictable shift, mainly because VAEGAN has not be trained with samples of unseen classes. To alleviate the domain shift problem, an attribute regressor is added after the generator. For each generated sample \(\widetilde{x}\) , their attributes will be predicted, and the predicted attributes are compared with their true attributes, forcing the generator to generate features with attributes matching the true attributes.

The attribute predictor is defined as \(R\left( \right)\) , which is a fully connected neural network containing three hidden layers. We define the loss function as follows:

\[
{\mathcal{L}}_{\mathrm{{AR}}} = \frac{1}{N}\mathop{\sum }\limits_{{i = 0}}^{N}\Delta \left( {R\left( {{x}_{i};{\theta }_{\mathrm{{AR}}}}\right) ,{a}_{{y}_{i}}}\right)  \tag{3}
\]

where \(\Delta\) represents the loss incurred from the difference between the predicted attributes \(R\left( {{x}_{i};{\theta }_{\mathrm{{AR}}}}\right)\) and the true values \({a}_{{y}_{i}}\) , and \({\theta }_{\mathrm{{AR}}}\) indicates the parameters of the neural networks. For clarity, we denote \({\begin{Vmatrix}R\left( {x}_{i};{\theta }_{\mathrm{{AR}}}\right)  - {a}_{{y}_{i}}\end{Vmatrix}}_{2}\) as \(F\left( {{x}_{i},{a}_{{y}_{i}};{\theta }_{\mathrm{{AR}}}}\right)\) . Hence, the most common form of \({\mathcal{L}}_{\mathrm{{AR}}}\) is

\[
{\mathcal{L}}_{\mathrm{{AR}}} = \frac{1}{N}\mathop{\sum }\limits_{{i = 0}}^{N}F\left( {{x}_{i},{a}_{{y}_{i}};{\theta }_{\mathrm{{AR}}}}\right) . \tag{4}
\]

However, it is not suitable in attribute prediction, mainly because many attribute vectors from different classes are close to each other, easily making the samples map to other similar attribute labels when predicting. Therefore, a hinge rank loss is chosen here, which calculates the rank of the predicted attributes among all attributes, making the model can also account for the effect of other similar attribute vectors. Let \(\mathbb{I}\left( u\right)  = 1\) if \(u\) is true and 0 otherwise. Let \(l\left( {{x}_{i},{a}_{{y}_{i}},{a}_{j}}\right)  = 1 - F\left( {{x}_{i},{a}_{{y}_{i}};{\theta }_{\mathrm{{AR}}}}\right)  + F\left( {{x}_{i},{a}_{j};{\theta }_{\mathrm{{AR}}}}\right)\) and let \({r}_{\Delta }\left( {{x}_{i},{a}_{{y}_{i}}}\right)  = \mathop{\sum }\limits_{{{a}_{j} \neq  {a}_{{y}_{i}}}}\mathbb{I}\left( {l\left( {{x}_{i},{a}_{{y}_{i}},{a}_{j}}\right)  > 0}\right)\) , we consider the following objective:

\[
{\mathcal{L}}_{\mathrm{{AR}}} = \frac{1}{N}\mathop{\sum }\limits_{{i = 0}}^{N}{\gamma }_{{r}_{\Delta }\left( {{x}_{i},{y}_{{a}_{i}}}\right) }\mathop{\sum }\limits_{{{a}_{j} \neq  {a}_{{y}_{i}}}}\max \left\{  {0,l\left( {{x}_{i},{a}_{{y}_{i}},{a}_{j}}\right) }\right\}   \tag{5}
\]

where \({\gamma }_{k}\) is a decreasing function of \(k\) , ensuring that more importance is given to the top of the ranking list. \({a}_{{y}_{i}}\) represents the attribute vector corresponding to \({x}_{i}\) , and \({a}_{j}\) represents the attribute vector other than \({a}_{{y}_{i}}\) .

In order to make the information in the hidden layer \(T\) less redundant feature information and to facilitate feature transformation in the next step, we constrain dependence on \(X\) and \(T\) from an information theory perspective. More specifically, we aim to keep the mutual information between \(X\) and \(T\) below an upper bound \(b\) , as defined in (6). This ensures that the redundancy information from \(X\) will be eliminated in \(T\) [30]

\[
\mathrm{I}\left( {T;X}\right)  < b\text{ . } \tag{6}
\]

For more convenience in computing the mutual information, We follow the method suggested by Alemi et al. [31] and use a variational upper bound of \(\mathrm{I}\left( {T;X}\right)\) as an alternative

\[
\mathrm{I}\left( {T;X}\right)  \leq  {\mathbb{E}}_{p\left( x\right) }\left\lbrack  {{D}_{\mathrm{{KL}}}\left\lbrack  {{p}_{M}\left( {t \mid  x}\right) \parallel r\left( t\right) }\right\rbrack  }\right\rbrack   < b \tag{7}
\]

where \(r\left( t\right)\) is the variational approximation to the marginal distribution of \(t,{p}_{M}\left( {t \mid  x}\right)\) is the conditional distribution of \(t\) conditioned on the features \(x\) . The objection of attribute regressor is

\[
\mathop{\min }\limits_{R}\frac{1}{N}\mathop{\sum }\limits_{{i = 0}}^{N}{\gamma }_{{r}_{\Delta }\left( {{x}_{i},{y}_{{a}_{i}}}\right) }\mathop{\sum }\limits_{{{a}_{j} \neq  {a}_{{y}_{i}}}}\max \left\{  {0,l\left( {{x}_{i},{a}_{{y}_{i}},{a}_{j}}\right) }\right\}   \tag{8}
\]

\[
\text{ s.t. }{\mathbb{E}}_{p\left( x\right) }\left\lbrack  {{D}_{\mathrm{{KL}}}\left\lbrack  {{p}_{M}\left( {t \mid  x}\right) \parallel r\left( t\right) }\right\rbrack  }\right\rbrack   < b\text{ . }
\]

By using the Lagrange multiplier method to transform (8) into an unconstrained optimization form

\[
{\mathcal{L}}_{\mathrm{{AR}}} = \frac{1}{N}\mathop{\sum }\limits_{{i = 0}}^{N}{\gamma }_{{r}_{\Delta }\left( {{x}_{i},{y}_{{a}_{i}}}\right) }\mathop{\sum }\limits_{{{a}_{j} \neq  {a}_{{y}_{i}}}}\max \left\{  {0,l\left( {{x}_{i},{a}_{{y}_{i}},{a}_{j}}\right) }\right\}
\]

\[
+ \lambda \left\{  {{\mathbb{E}}_{p\left( x\right) }\left\lbrack  {{D}_{\mathrm{{KL}}}\left\lbrack  {{p}_{M}\left( {t \mid  x}\right) \parallel r\left( t\right) }\right\rbrack  }\right\rbrack   - \mathrm{b}}\right\}  . \tag{9}
\]

The loss function of the proposed model is expressed in the following form:

\[
{\mathcal{L}}_{\text{ total }} = {\mathcal{L}}_{\mathrm{{WGAN}}} + {\lambda }_{1}{\mathcal{L}}_{\mathrm{{VAE}}} + {\lambda }_{2}{\mathcal{L}}_{\mathrm{{AR}}}. \tag{10}
\]

As depicted in Fig. 4, the model is an encoding, and then, decoding process where the model inputs attributes to map them to features, and then, maps the features back to the attributes. It is noteworthy that the encoder is equivalent to the generator G, whereas the decoder corresponds to the attribute regressor R. The Table I displays the structure of each component in the model that employs the Adam optimizer with a learning rate of 0.0001. "Fc" represents the fully connected layer and "LN" represents layer normalization.

The training strategy of the proposed model is represented in Algorithm 1.

\section*{B. Feature Transformation}

Domain shift occurs fundamentally due to the emergence of features that do not correspond to attributes when creating unseen classes while attribute information is mostly used in training stage and will be discarded during classification stage in previous studies, which leads to the value of attribute information not being fully utilized. In Step 2, an attribute regressor was developed whose hidden layer output \(T\) contains less redundant feature information and sufficient attribute information, so \(T\) is complementary to feature \(x\) . In this article, we transform the seen and unseen class samples into a discriminative feature space by utilizing the attribute information contained in the well-trained attribute regressor to give the generated samples more discriminative information, as represented in Fig. 5. This is accomplished as follows.

TABLE I

NETWORK PARAMETERS OF PROPOSED METHOD

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|}
\hline
Layer name & parameter \\
\cline{1-2}
\multicolumn{2}{|c|}{Generator} \\
\cline{1-2}
Concatenate(   ) & attribute\_dim+noise\_dim \\
\cline{1-2}
Fc1+LeakyReLU(0.2)+LN & output\_dim = 50 \\
\cline{1-2}
Fc2+LeakyReLU(0.2)+LN & output\_dim = 100 \\
\cline{1-2}
Fc3+LeakyReLU(0.2)+LN & output\_dim = 150 \\
\cline{1-2}
Fc4+ReLU(0.2) & output\_dim = feature\_dim \\
\cline{1-2}
\multicolumn{2}{|c|}{Discriminator} \\
\cline{1-2}
Fc1+LeakyReLU(0.2)+LN & output\_dim = 200 \\
\cline{1-2}
Fc2+LeakyReLU(0.2)+LN & output\_dim = 100 \\
\cline{1-2}
Fc3 & output\_dim = 1 \\
\cline{1-2}
\multicolumn{2}{|c|}{Attribute regressor} \\
\cline{1-2}
Fc1+LeakyReLU(0.2) & output\_dim = 48 \\
\cline{1-2}
Fc2+LeakyReLU(0.2) & output\_dim = 24 \\
\cline{1-2}
Fc3+Sigmoid(   ) & output\_dim = attribute\_dim \\
\cline{1-2}
\hline
\end{tabular}
}
\end{center}

\begin{center}
\includegraphics[max width=0.5\textwidth]{images/bo_d20576n7aajc73bfkneg_4_891_771_723_259_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 5. Discriminative feature transformation.

First, the attribute regressor of the whole trained model is extracted, and the generated unseen class samples combined with seen class samples are used to feed it. Subsequently, the output of the second hidden layer \(T\) in the attribute regressor is taken out, where the output of the unseen class samples is \({T}^{u}\) , as well as the output of the seen class samples is \({T}^{s}\) . Finally, the transformation is carried out on each sample as follows:

\[
{x}^{u} = {x}^{u} \oplus  {T}^{u}
\]

\[
{x}^{s} = {x}^{s} \oplus  {T}^{s} \tag{11}
\]

where \(\oplus\) is the concatenation operation. In this way, the transformed \({x}^{s}\) and \({x}^{s}\) are used to train the fault classifier. Notably, these steps are performed after training the generative model and before training the classification model. At test time, the data in the test set are also transformed in the same way before being predicted.

\section*{IV. EXPERIMENTS}

This section validates the proposed method by selecting the Tennessee-Eastman Process (TEP) dataset and comparing it with multiple current state-of-the-art algorithms. Moreover, ablation experiments are designed to verify the effect of AR on the model.

Algorithm 1: VAEGAN-AR Optimization.

\HRule

Require:Input data \(\left\{  {\left( {{x}_{i}{}^{s},{a}_{{y}_{i}^{s}}}\right) ,i = 1,\ldots ,{N}_{s}}\right\}\) .

\({\theta }_{E},{\theta }_{G},{\theta }_{D},{\theta }_{\mathrm{{AR}}} \leftarrow\) initialize network parameters

repeat

for \(i = 1\) to \(n\) do

\hspace*{1em} Pick a random data \(\left( {{x}_{i}{}^{s},{a}_{{y}_{i}^{s}}}\right)\)

\hspace*{1em} Randomly sample a noise \({z}_{p} \sim  N\left( {0,1}\right)\)

\hspace*{1em} \({\widehat{a}}_{{y}_{i}} = \operatorname{AR}\left( {x}_{i}\right)\)

\hspace*{1em} \({\mathcal{L}}_{\mathrm{{AR}}} = {\gamma }_{{r}_{\Delta }\left( {{x}_{i},{y}_{{a}_{i}}}\right) }\mathop{\sum }\limits_{{{a}_{j} \neq  {a}_{{y}_{i}}}}\max \left\{  {0,l\left( {{x}_{i},{a}_{{y}_{i}},{a}_{j}}\right) }\right\}\)

\hspace*{5em} \(+ \lambda \left\{  {{\mathbb{E}}_{p\left( x\right) }\left\lbrack  {{D}_{\mathrm{{KL}}}\left\lbrack  {{p}_{M}\left( {t \mid  x}\right) \parallel r\left( t\right) }\right\rbrack  }\right\rbrack   - \mathrm{b}}\right\}\)

\hspace*{1em} \({\theta }_{\mathrm{{AR}}} \leftarrow   - {\nabla }_{{\theta }_{\mathrm{{AR}}}}{\mathcal{L}}_{\mathrm{{AR}}}\)

\hspace*{1em} \({\mathcal{L}}_{\text{ WGAN }} = D\left( {{x}_{i},{a}_{{y}_{i}}}\right)  - D\left( {G\left( {{z}_{p},{a}_{{y}_{i}}}\right) ,{a}_{{y}_{i}}}\right)\)

\hspace*{5em} \(- {\lambda E}\left\lbrack  {\left( {\begin{Vmatrix}{\nabla }_{{x}^{ * }}D\left( {x}^{ * },{a}_{{y}_{i}}\right) \end{Vmatrix}}_{2} - 1\right) }^{2}\right\rbrack\)

\hspace*{1em} \({\theta }_{D} \leftarrow   - {\nabla }_{{\theta }_{D}}{\mathcal{L}}_{\text{ WGAN }}\)

end for

\(z = \mathrm{E}\left( {{x}_{i},{a}_{{y}_{i}}}\right)\)

\(\widehat{x} = G\left( {z,{a}_{{y}_{i}}}\right)\)

\({\mathcal{L}}_{\mathrm{{VAE}}} = \mathrm{{KL}}\left( {\mathrm{E}\left( {{x}_{i},{a}_{{y}_{i}}}\right) \parallel p\left( {z \mid  {a}_{{y}_{i}}}\right) }\right)\)

\hspace*{4em} \(- {\mathbb{E}}_{\mathrm{E}\left( {{x}_{i},{a}_{{y}_{i}}}\right) }\left\lbrack  {\log G\left( {z,{a}_{{y}_{i}}}\right) }\right\rbrack\)

Randomly sample a noise \({z}_{p} \sim  N\left( {0,1}\right)\)

\(\widetilde{x} = G\left( {{z}_{p},{a}_{{y}_{i}}}\right)\)

\(\widetilde{a} = \operatorname{AR}\left( \widetilde{x}\right)\)

\({\mathcal{L}}_{\mathrm{{AR}}} = {\gamma }_{{r}_{\Delta }\left( {{\widetilde{x}}_{i},{y}_{{a}_{i}}}\right) }\mathop{\sum }\limits_{{{a}_{j} \neq  {a}_{{y}_{i}}}}\max \left\{  {0,l\left( {{\widetilde{x}}_{i},{a}_{{y}_{i}},{a}_{j}}\right) }\right\}\)

\hspace*{4em} \(+ \lambda \left\{  {{\mathbb{E}}_{p\left( \widetilde{x}\right) }\left\lbrack  {{D}_{\mathrm{{KL}}}\left\lbrack  {{p}_{M}\left( {t \mid  \widetilde{x}}\right) \parallel r\left( t\right) }\right\rbrack  }\right\rbrack   - \mathrm{b}}\right\}\)

\({\mathcal{L}}_{E,G} = D\left( {\widetilde{x},{a}_{{y}_{i}}}\right)  + {\mathcal{L}}_{\mathrm{{AR}}} + {\mathcal{L}}_{\text{ VAE }}\)

\({\theta }_{G} \leftarrow   - {\nabla }_{{\theta }_{G}}{\mathcal{L}}_{E,G}\)

\({\theta }_{E} \leftarrow   - {\nabla }_{{\theta }_{E}}{\mathcal{L}}_{E,G}\)

until Deadline

\HRule

1) Data Description: The TEP dataset is developed by Eastman Chemical Company to simulate actual chemical processes [32] and is often used for research in fault diagnosis, and it has been frequently chosen for previous zero-shot fault diagnosis studies to verify the validity of the method. The TEP consists of five operating units: Reactor, condenser, gas-liquid separator, recirculation compressor, and product stripper tower. The data in the TEP dataset consist of 22 different simulation data, one for normal conditions and 21 for different fault conditions. There are 480 samples for each fault type in the TEP dataset, and each sample has 52 observed variables.

2) Model Implementation: we follow the data partition according to [14], which selected 15 different fault conditions as the data for this experiment. More details are given in [14].

In the first step, the attributes of 15 fault classes are defined. The set of these attributes is specified in Table II. Each fault class corresponds to an attribute that is a 0/1 vector of dimension 20, and the specific attribute definition of each fault is depicted in Fig. 6. Then, the fault classes are divided into seen and unseen classes. The training set is composed of samples from the seen groups, whereas the test set consists of samples from the unseen groups. The specific partition is displayed in Table IV. Ultimately, features must be extracted for every sample. A network for feature extraction is trained on the seen class samples. The output data of the network is used as its feature. The data in the training set corresponds to the sample's features. The training set has a shape of \({12} \times  {480} \times  {24}\) .

TABLE II

ATTRIBUTES OF THE TEP BENCHMARK

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|}
\hline
No. & Attributes \\
\cline{1-2}
Att\#1 & Input A is changed \\
\cline{1-2}
Att\#2 & Input \(\mathrm{C}\) is changed \\
\cline{1-2}
Att\#3 & A/C ratio is changed \\
\cline{1-2}
Att\#4 & Input B is changed \\
\cline{1-2}
Att\#5 & Related with pipe4 \\
\cline{1-2}
Att\#6 & Temperature of input \(\mathrm{D}\) is changed \\
\cline{1-2}
Att\#7 & Related with pipe2 \\
\cline{1-2}
Att\#8 & Disturbance is step changing \\
\cline{1-2}
Att\#9 & Input is changed \\
\cline{1-2}
Att\#10 & Temperature of input is changed \\
\cline{1-2}
Att\#11 & Occurred at reactor \\
\cline{1-2}
Att\#12 & Temperature of cooling water is changed \\
\cline{1-2}
Att\#13 & Occurred at condenser \\
\cline{1-2}
Att\#14 & Related with pipe 1 \\
\cline{1-2}
Att\#15 & Disturbance is random varying \\
\cline{1-2}
Att\#16 & Model parameters are changed \\
\cline{1-2}
Att\#17 & Disturbance is slow drift \\
\cline{1-2}
Att\#18 & Related with cooling water \\
\cline{1-2}
Att\#19 & Related with valve \\
\cline{1-2}
Att\#20 & Disturbance is sticking \\
\cline{1-2}
\hline
\end{tabular}
}
\end{center}

TABLE III

FIVE GROUPS OF SEEN/UNSEEN SPLIT FOR TEP

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|}
\hline
No. & Seen classes & Unseen classes \\
\cline{1-3}
A & \(2,3,4,7 - {13},{15}\) & 1, 6, 14 \\
\cline{1-3}
B & \(1,2,3,5,6,8,9,{11} - {15}\) & 4, 7, 10 \\
\cline{1-3}
C & \(1 - 7,9,{10},{13},{14},{15}\) & 8, 11, 12 \\
\cline{1-3}
D & \(1,4,6 - {15}\) & 2,3,5 \\
\cline{1-3}
E & \(1 - 8,{10},{11},{12},{14}\) & 9,13,15 \\
\cline{1-3}
\hline
\end{tabular}
}
\end{center}

Best value in each group.

TABLE IV

COMPARISON OF DIAGNOSIS ACCURACY USING THE PROPOSED METHOD AND RELATED METHODS TESTED ON THE TEP DATASET

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|c|c|c|c|}
\hline
\multirow{2}{*}{Method} & \multicolumn{5}{|c|}{Accuracy (\%)} & \multirow{2}{*}{Average (\%)} \\
\cline{2-6}
 & Group A & Group & Group & Group D & Group E &  \\
\cline{1-7}
FDAT [14] & 80.3 & 62.6 & 59.0 & 72.4 & 67.4 & 68.3 \\
\cline{1-7}
SCE [16] & 89.5 & 78.1 & 62.4 & 76.0 & 82.1 & 77.6 \\
\cline{1-7}
FAGAN [23] & 84.5 & 76.9 & 62.5 & 74.6 & 76.5 & 75.0 \\
\cline{1-7}
FREE [27] & 81.0 & 75.6 & 71.5 & 78.8 & 74.4 & 76.3 \\
\cline{1-7}
SRWGAN [26] & 79.2 & 77.6 & 69.4 & 77.4 & 72.3 & 75.2 \\
\cline{1-7}
Ours & 85.3 & 76.7 & 70.7 & 95.7 & 82.3 & 82.1 \\
\cline{1-7}
Normal AR loss & 69.1 & 70.1 & 70.0 & 69.8 & 68.4 & 69.5 \\
\cline{1-7}
Without AR loss & 54.3 & 56.7 & 51.9 & 59.1 & 61.7 & 56.7 \\
\cline{1-7}
\hline
\end{tabular}
}
\end{center}

Best value in each group.

In the second step, the training dataset is the input of the generative model that is designed, and the training is completed after the loss function of the model converges. After that, the fault attributes of the unseen classes feed the well-trained generative model to generate the samples of the unseen classes, and 480 samples are generated for each unseen class of faults. Next, the seen class samples and the generated unseen class samples are combined to form a new dataset \({D}_{\mathrm{{cls}}}\) to train the fault classifier.

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|}
\hline
\multirow{17}{*}{Fault classes} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} \\
\cline{2-22}
 & Fault\#1 & 1 & 1 & 1 & 0 & 1 & 0 & 0 & 1 & 1 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 0 & 0 \\
\cline{2-22}
 & Fault\#2 & 1 & 1 & 0 & 1 & 1 & 0 & 0 & 1 & 1 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 0 & 0 \\
\cline{2-22}
 & Fault\#3 & 0 & 0 & 0 & 0 & 0 & 1 & 1 & 1 & 0 & 1 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 \\
\cline{2-22}
 & Fault\#4 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 1 & 1 & 1 & 0 & 0 & 0 & 0 & 1 & 0 & 0 \\
\cline{2-22}
 & Fault\#5 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 1 & 1 & 0 & 0 & 0 & 0 & 1 & 0 & 0 \\
\cline{2-22}
 & Fault\#6 & 1 & 0 & 1 & 0 & 0 & 0 & 0 & 1 & 1 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 0 & 0 \\
\cline{2-22}
 & Fault\#7 & 0 & 1 & 1 & 0 & 1 & 0 & 0 & 1 & 1 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 \\
\cline{2-22}
 & Fault\#8 & 1 & 1 & 1 & 1 & 1 & 0 & 1 & 0 & 1 & 0 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 0 \\
\cline{2-22}
 & Fault\#9 & 0 & 0 & 0 & 0 & 0 & 1 & 1 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 0 \\
\cline{2-22}
 & Fault\#10 & 0 & 1 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 0 \\
\cline{2-22}
 & Fault\#11 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 1 & 1 & 0 & 0 & 1 & 0 & 0 & 1 & 0 & 0 \\
\cline{2-22}
 & Fault\#12 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 1 & 1 & 0 & 1 & 0 & 0 & 1 & 0 & 0 \\
\cline{2-22}
 & Fault\#13 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 1 & 1 & 0 & 0 & 0 \\
\cline{2-22}
 & Fault\#14 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 0 & 0 & 1 & 1 & 1 \\
\cline{2-22}
 & Fault\#15 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 1 & 1 & 1 \\
\cline{2-22}
 & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} & \phantom{X} \\
\cline{1-22}
\hline
\end{tabular}
}
\end{center}

\(\left\lbrack  \begin{matrix} \text{ Att\#1 } & \text{ Att\#2 } & \text{ Att\#3 } & \text{ Att\#5 } & \text{ Att\#5 } & \text{ Att\#7 } & \text{ Att\#7 } & \text{ Att\#8 } & \text{ Att\#9 } & \text{ Att\#10 } & \\  \text{ Att\#11 } & \text{ Att\#12 } & \text{ Att\#13 } & \text{ Att\#15 } & \text{ Att\#15 } & \text{ Att\#17 } & \text{ Att\#19 } & \text{ Att\#19 } & \text{ Att\#19 } & \text{ Att\# } & {19} \end{matrix}\right\rbrack\) Attributes

Fig. 6. Fault description matrix for TEP dataset.

\begin{center}
\includegraphics[max width=0.5\textwidth]{images/bo_d20576n7aajc73bfkneg_6_152_746_655_485_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 7. Effect of attribute regressors on diagnosis accuracy.

In the third step, the dataset \({D}_{\mathrm{{cls}}}\) is used as input for the AR module. Then, the features of the dataset are transformed according to the method described in Section III, and we obtain the transformed dataset \({D}_{\text{ trans }}\) .

In the fourth step, the classifier is trained with \({D}_{\text{ trans }}\) , and the well-trained classifier is used for fault classification.

3) Results: We evaluate the model from multiple perspectives as suggested in [33], including accuracy analysis, comparative experiments, ablation experiments, and visualization of generated samples. Table IV shows the diagnosis results under the five experimental designs of groups \(\mathrm{A},\mathrm{B},\mathrm{C},\mathrm{D}\) , and \(\mathrm{E}\) . It can be seen that group A achieves the highest accuracy rate with \({96.1}\%\) , and the lowest is group \(\mathrm{C}\) with \({72.6}\%\) . The average accuracy of the five groups of experiments reached 82.7\%, which is much higher than the random guess accuracy of \({33.3}\%\) , thus proving the effectiveness of the method for the zero-shot fault diagnosis problem. The confusion matrix for the five groups of experiments is represented in Fig. 8.

We validated the effect of the attribute regressor in the model by comparing the loss function of the attribute regressor \({\mathcal{L}}_{\mathrm{{AR}}}\) , in the form of (2), with our design. In addition, we deleted the module of the attribute regressor to verify its necessity. Fig. 7 displays the result. Using our proposed loss function, the accuracy of the model obtained is across all groups higher than that achieved using (2), and significantly greater than the accuracy of the model without an attribute regressor. Therefore, a generalizable attribute regression model is needed.

In addition, other current state-of-the-art zero-shot fault diagnosis methods are chosen for comparison, such as fault direct attributes prediction (FDAT) [14], semantic-consistent embedding (SCE) [16], fault auxiliary generative adversarial network (FAGAN) [23], FREE [27], and SRWGAN [26] where FDAT is based on attribute prediction, SCE is based on feature-attribute embedding, FAGAN, FREE, and SRWGAN are based on generative methods. The experimental results of these methods are also summarized in Table V. FDAT records the lowest average accuracy of \({68.3}\%\) , suggesting that DAP based methods do not achieve superior results in overall zero-shot diagnosis. SCE performed significantly better than FDAT, with an improved accuracy rate of \({77.6}\%\) . Generative-based models, such as FAGAN, FREE, and SRWGAN obtain an accuracy rate of roughly \({76}\%\) , which is a relatively great result. Meanwhile, the method proposed in this article achieves an accuracy rate of 82.7\%. The overall improvement in accuracy indicates the superiority of our method among existing methods. Upon comparing the accuracy rates between groups, Groups A and D consistently record the highest accuracy rate, followed by Groups B and E, whereas Group C had a low accuracy rate across all methods. Notably, FREE has a similar structure to our model, as well as both include a feature transformation step. However, our approach gains a competitive advantage through the design of attribute regressors. The use of a hinge rank loss and mutual information constraints reduces redundant feature information while preserving more realistic attribute information.

Moreover, to quantitatively analyze the quality of generated samples from various generative models, we calculated the Fréchet inception distance (FID) between the generated and real samples. The average FID and maximum mean discrepancy (MMD) values between the three types of generated samples and the real samples in each experiment group were used as reference and are presented in Table V. The model proposed in this article generates samples that are the closest to real samples, resulting in the best performance in four out of the five groups of experiments. The performance of the remaining three generated models is similar. These findings confirm the superior performance of the models in this article, which is comparable to the accuracy comparison experiments.

\begin{center}
\includegraphics[max width=0.9\textwidth]{images/bo_d20576n7aajc73bfkneg_7_294_180_1174_770_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 8. Confusion matrix of five groups.

TABLE V

ANALYSIS OF QUANTITATIVE METRICS FOR THE GENERATIVE-BASED ZERO-SHOT MODEL FOR THE TEP DATASET

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|c|c|c|c|c|c|c|c|}
\hline
\multirow{2}{*}{Method} & \multicolumn{5}{|c|}{FID} & \multicolumn{5}{|c|}{MMD} \\
\cline{2-11}
 & A & B & C & D & E & A & B & C & D & E \\
\cline{1-11}
Ours & 1.24 & 1.39 & 2.17 & 1.21 & 1.43 & 0.34 & 0.31 & 0.57 & 0.27 & 0.42 \\
\cline{1-11}
FAGAN & 1.76 & 2.42 & 2.01 & 1.95 & 1.79 & 0.44 & 0.52 & 0.71 & 0.46 & 0.49 \\
\cline{1-11}
FREE & 1.36 & 1.46 & 2.64 & 1.37 & 1.51 & 0.41 & 0.27 & 0.61 & 0.39 & 0.55 \\
\cline{1-11}
SRWGAN & 2.14 & 1.74 & 2.36 & 1.65 & 1.67 & 0.39 & 0.34 & 0.77 & 0.42 & 0.41 \\
\cline{1-11}
\hline
\end{tabular}
}
\end{center}

\begin{center}
\includegraphics[max width=1.0\textwidth]{images/bo_d20576n7aajc73bfkneg_7_257_1367_1245_457_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 9. Visual analysis of model results. (a) Complete model. (b) Model without feature transformation. (c) Model without attribute regressor.

Subsequently, we perform visual analysis to demonstrate the conceptual effectiveness of the proposed method. For instance, using Group A's experiment as an example, we generate samples for classes1,6, and 14 using the proposed method and compare these samples with the actual ones. Fig. 9(a) illustrates the results. Afterward, we remove the feature transformation process and contrast the newly generated samples with the original ones. Fig. 9(b) illustrates a reduction in divisibility between samples, with samples from different classes appearing to overlap. Furthermore, the attribute regressor has been removed. Afterward, the model is retrained and the generated samples are compared with the true samples, as depicted in Fig. 9(c). The significant shift between the generated samples and real samples infers that the generative model failed to learn the correspondence between the attribute distribution and feature distribution of the unseen classes. This failure is attributed to the domain shift described in the previous section.

\begin{center}
\includegraphics[max width=1.0\textwidth]{images/bo_d20576n7aajc73bfkneg_8_206_188_1324_802_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 10. Comparison of attribute prediction results. (a)-(c) Normal attribute regressor. (d)-(f) Attribute regressor proposed in this article.

Finally, to further explore the effects of hinge rank loss and mutual information constraints on the performance of attribute regressors, we take the Group A experiment as an example and present the two trained attribute regressors (normal attribute regressor and attribute regressor proposed in this article) to predict the attribute labels of the three unseen classes (Classes 1, 6, and 14) in Group A. We analyze the performance of the attribute regressors by comparing the attribute prediction results of the unseen classes to analyze the capability of the attribute regressor. The attribute prediction results are shown in Fig. 10, and generations (a)-(c) are the normal attribute regressor for the three unseen classes, and (d)-(f) are the predictions of the attribute regressor proposed in this article for the three unseen classes. After the hinge rank loss and mutual information constraints are added, the prediction performance of the attribute regressor improves more significantly, and the prediction of the attribute information of the unseen classes is more accurate, which justifies the better diagnostic results of the generated model using the attribute regressor proposed in this article.

\section*{V. CONCLUSION}

Domain shift is a very important issue with applications to many zero-shot fault diagnosis tasks. This article formally analyzes the domain shift problem and proposes a generative framework with attribute-consistency to solve it. The framework considered in this work is flexible to generate various samples, which are unavailable before training. Experiment results on a fault diagnosis task show that while other state-of-art methods can be applied to zero-shot fault diagnosis, they do not perform as well as our new method, which has less shift between generated samples and real samples. The achieved results also highlight that the transformation of features using attribute information can make the sample more consistent with attributes and more discriminative. The framework opens up many interesting future research directions, especially those related to consideration of the input data characteristics and theoretical guarantees for ZSL. REFERENCES

[1] C. Wang, N. Lu, Y. Cheng, and B. Jiang, "A data-driven aero-engine degradation prognostic strategy," IEEE Trans. Cybern., vol. 51, no. 3, pp. 1531-1541, Mar. 2021.

[2] X. Wang, B. Jiang, S. X. Ding, N. Lu, and Y. Li, "Extended relevance vector machine-based remaining useful life prediction for DC-link capacitor in high-speed train," IEEE Trans. Cybern., vol. 52, no. 9, pp. 9746-9755, Sep. 2022.

[3] C. Chen et al., "Pseudo-label guided sparse deep belief network learning method for fault diagnosis of radar critical components," IEEE Trans. Instrum. Meas., vol. 72, 2023, Art. no. 3510212.

[4] Y. Li, N. Lu, X. Wang, and B. Jiang, "Islanding fault detection based on data-driven approach with active developed reactive power variation," Neurocomputing, vol. 337, pp. 97-109, 2019.

[5] H. Chen, B. Jiang, T. Zhang, and N. Lu, "Data-driven and deep learning-based detection and diagnosis of incipient faults with application to electrical traction systems," Neurocomputing, vol. 396, pp. 429-437, 2020.

[6] C. H. Lampert, H. Nickisch, and S. Harmeling, "Learning to detect unseen object classes by between-class attribute transfer," in Proc. IEEE Conf. Comput. Vis. Pattern Recognit., 2009, pp. 951-958.

[7] Z. Akata, F. Perronnin, Z. Harchaoui, and C. Schmid, "Label-embedding for attribute-based classification," in Proc. IEEE Conf. Comput. Vis. Pattern Recognit., 2013, pp. 819-826.

[8] B. R.-Paredes and P. Torr, "An embarrassingly simple approach to zero-shot learning," in Proc. Int. Conf. Mach. Learn., 2015, pp. 2152-2161.

[9] E. Kodirov, T. Xiang, and S. Gong, "Semantic autoencoder for zero-shot learning," in Proc. IEEE Conf. Comput. Vis. Pattern Recognit., 2017, pp. 3174-3183.

[10] Y. Xian, T. Lorenz, B. Schiele, and Z. Akata, "Feature generating networks for zero-shot learning," in Proc. IEEE Conf. Comput. Vis. Pattern Recognit., 2018, pp. 5542-5551.

[11] E. Schonfeld, S. Ebrahimi, S. Sinha, T. Darrell, and Z. Akata, "Generalized zero-and few-shot learning via aligned variational autoencoders," in Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit., 2019, pp. 8247-8255.

[12] Y. Li, Z. Liu, L. Yao, and X. Chang, "Attribute-modulated generative meta learning for zero-shot learning," IEEE Trans. Multimedia, vol. 25, pp. 1600-1610, 2021.

[13] L. Zhang et al., "TN-ZSTAD: Transferable network for zero-shot temporal activity detection," IEEE Trans. Pattern Anal. Mach. Intell., vol. 45, no. 3, pp. 3848-3861, Mar. 2023.

[14] L. Feng and C. Zhao, "Fault description based attribute transfer for zero-sample industrial fault diagnosis," IEEE Trans. Ind. Inform., vol. 17, no. 3, pp. 1852-1862, Mar., 2021.

[15] Z. Chen, J. Wu, C. Deng, X. Wang, and Y. Wang, "Deep attention relation network: A zero-shot learning method for bearing fault diagnosis under unknown domains," IEEE Trans. Rel., vol. 72, no. 1, pp. 79-89, Mar. 2023.

[16] Z. Hu, H. Zhao, L. Yao, and J. Peng, "Semantic-consistent embedding for zero-shot fault diagnosis," IEEE Trans. Ind. Inform., vol. 19, no. 5, pp. 7022-7031, May, 2023.

[17] B. Li and C. Zhao, "Federated zero-shot industrial fault diagnosis with cloud-shared semantic knowledge base," IEEE Internet Things J., vol. 10, no. 13, pp. 116 19-11 630, Jul. 2023.

[18] A. Gupta, H. P. Gupta, B. Biswas, and T. Dutta, "An unseen fault classification approach for smart appliances using ongoing multivariate time series," IEEE Trans. Ind. Inform., vol. 17, no. 6, pp. 3731-3738, Jun., 2021.

[19] S. Xing, Y. Lei, S. Wang, N. Lu, and N. Li, "A label description space embedded model for zero-shot intelligent diagnosis of mechanical compound faults," Mech. Syst. Signal Process., vol. 162, 2022, Art. no. 108036.

[20] J. Xu, L. Zhou, W. Zhao, Y. Fan, X. Ding, and X. Yuan, "Zero-shot learning for compound fault diagnosis of bearings," Expert Syst. Appl., vol. 190, 2022, Art. no. 116197.

[21] W. Wang, V. W. Zheng, H. Yu, and C. Miao, "A survey of zero-shot learning: Settings, methods, and applications," ACM Trans. Intell. Syst. Technol., vol. 10, no. 2, pp. 1-37, 2019.

[22] H. Lv, J. Chen, T. Pan, and Z. Zhou, "Hybrid attribute conditional adversarial denoising autoencoder for zero-shot classification of mechanical intelligent fault diagnosis," Appl. Soft Comput., vol. 95, 2020, Art. no. 106577.

[23] Y. Zhuo and Z. Ge, "Auxiliary information-guided industrial data augmentation for any-shot fault learning and diagnosis," IEEE Trans. Ind. Inform., vol. 17, no. 11, pp. 7535-7545, Nov., 2021.

[24] C. Yan et al., "ZeroNAS: Differentiable generative adversarial networks search for zero-shot learning," IEEE Trans. Pattern Anal. Mach. Intell., vol. 44, no. 12, pp. 9733-9740, Dec. 2022.

[25] Y. Fu, T. M. Hospedales, T. Xiang, Z. Fu, and S. Gong, "Transductive multi-view embedding for zero-shot recognition and annotation," in Proc. 13th Eur. Conf. Comput. Vis., , 2014, pp. 584-599.

[26] L. Feng, C. Zhao, and X. Li, "Bias-eliminated semantic refinement for any-shot learning," IEEE Trans. Image Process., vol. 31, pp. 2229-2244, 2022.

[27] S. Chen et al., "Free: Feature refinement for generalized zero-shot learning," in Proc. IEEE/CVF Int. Conf. Comput. Vis., 2021, pp. 122-131.

[28] A. B. L. Larsen, S. K. Sønderby, H. Larochelle, and O. Winther, "Autoen-coding beyond pixels using a learned similarity metric," in Proc. Int. Conf. Mach. Learn., 2016, pp. 1558-1566.

[29] M. Arjovsky, S. Chintala, and L. Bottou, "Wasserstein generative adversarial networks," in Proc. Int. Conf. Mach. Learn., 2017, pp. 214-223.

[30] N. Tishby and N. Zaslavsky, "Deep learning and the information bottleneck principle," in Proc. IEEE Inf. Theory Workshop, 2015, pp. 1-5.

[31] A. A. Alemi et al., "Deep variational information bottleneck," in Proc. Int. Conf. Learn. Representations, 2016.

[32] J. J. Downs and E. F. Vogel, "A plant-wide industrial process control problem," Comput. Chem. Eng., vol. 17, no. 3, pp. 245-255, 1993.

[33] L. Shao, N. Lu, B. Jiang, S. Simani, L. Song, and Z. Liu, "Improved generative adversarial networks with filtering mechanism for fault data augmentation," IEEE Sensors J., vol. 23, no. 13, pp. 15176-15187, Jul. 2023.

\begin{center}
\includegraphics[max width=0.2\textwidth]{images/bo_d20576n7aajc73bfkneg_9_894_190_221_278_0.jpg}
\end{center}
\hspace*{3em} 

Lexuan Shao received the B.E. degree in electrical engineering and automation from Lanzhou Jiaotong University, Lanzhou, China, in 2021. He is currently working toward the M.Sc. degree in control theory and control engineering with the College of Automation Engineering, Nan-jing University of Aeronautics and Astronautics, Nanjing, China.

His current research interests include fault diagnosis and deep learning.

\begin{center}
\includegraphics[max width=0.2\textwidth]{images/bo_d20576n7aajc73bfkneg_9_892_622_224_276_0.jpg}
\end{center}
\hspace*{3em} 

Ningyun Lu (Member, IEEE) received the Ph.D. degree in control science and engineering from Northeastern University, Shenyang, China, in 2004.

From 2002 to 2005, she worked as a Research Associate and Postdoctoral Fellow with the Hong Kong University of Science and Technology, Guangzhou, China. She is currently a Full Professor with the College of Automation Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China. Her research interests include data-driven fault prognosis and diagnosis and their applications to various industrial processes.

\begin{center}
\includegraphics[max width=0.2\textwidth]{images/bo_d20576n7aajc73bfkneg_9_894_1129_224_274_0.jpg}
\end{center}
\hspace*{3em} 

Bin Jiang (Fellow, IEEE) received the Ph.D. degree in automatic control from Northeastern University, Shenyang, China, in 1995.

He is currently the President with the Nan-jing University of Aeronautics and Astronautics, Nanjing, China. His current research interests include fault diagnosis and fault tolerant control and their applications in aircrafts, satellites, and high-speed trains.

Dr. Jiang is the Chair with Control Systems Chapter in IEEE Nanjing Section, and a Member of IFAC Technical Committee on fault detection, supervision, and safety of technical processes. He currently is an Associate Editor or Editorial Board Member for several journals, such as the IEEE TRANSACTIONS ON NEURAL NETWORKS AND LEARNING SYSTEMS, IEEE TRANSACTIONS ON CYBERNETICS, IEEE TRANSACTIONS ON INDUSTRIAL INFORMATICS, Journal of the Franklin Institute, Neurocomputing, International Journal of Control, Automation and Systems, Acta Automatica Sinica, Journal of Control and Decision, and Journal of Systems Engineering and Electronics.

\begin{center}
\includegraphics[max width=0.2\textwidth]{images/bo_d20576n7aajc73bfkneg_9_894_1792_225_277_0.jpg}
\end{center}
\hspace*{3em} 

Silvio Simani (Senior Member, IEEE) received the M.Sc. degree in electronic engineering from the University of Ferrara, Ferrara, Italy, in 1996, and the Ph.D. degree in information sciences (automatic control area) from the University of Modena and Reggio Emilia, Modena, Italy, in 2000.

Since 2000, he has been a Member with the SAFEPROCESS Technical Committee. In 2002, he was an Assistant Professor with the Department of Engineering, University of Ferrara, where he has been an Associate Professor since 2014. He has authored or coauthored about 160 refereed journals and conference papers, as well as three books and chapters. His research interests include fault diagnosis, fault-tolerant control, and system identification.
\end{document}
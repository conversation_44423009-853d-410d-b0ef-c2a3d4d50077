# 训练日志 (Triplet+语义指导融合架构)

## 核心创新点
1. 基于成功的triplet方法（88.16%准确率）
2. 添加语义指导机制，避免对抗训练的不稳定性
3. 在特征空间和语义空间同时进行triplet学习
4. 保持triplet方法的稳定性，增强语义迁移能力

**开始时间**: 2025-07-02 16:59:50

---

[Epoch 0/50] [Accuracy_lsvm: 0.333333] [Accuracy_nrf: 0.360764] [Accuracy_pnb: 0.331597][Accuracy_mlp: 0.517014]
[Epoch 1/50] [Accuracy_lsvm: 0.333333] [Accuracy_nrf: 0.360764] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.517014]
[Epoch 2/50] [Accuracy_lsvm: 0.333333] [Accuracy_nrf: 0.360764] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.517014]
[Epoch 3/50] [Accuracy_lsvm: 0.333333] [Accuracy_nrf: 0.360764] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.517014]
[Epoch 4/50] [Accuracy_lsvm: 0.333333] [Accuracy_nrf: 0.360764] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 5/50] [Accuracy_lsvm: 0.333333] [Accuracy_nrf: 0.360764] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 6/50] [Accuracy_lsvm: 0.333333] [Accuracy_nrf: 0.444097] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 7/50] [Accuracy_lsvm: 0.333333] [Accuracy_nrf: 0.444097] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 8/50] [Accuracy_lsvm: 0.333333] [Accuracy_nrf: 0.444097] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 9/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.444097] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 10/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.444097] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 11/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 12/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 13/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 14/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 15/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 16/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 17/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 18/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 19/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 20/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 21/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 22/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.486806] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 23/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 24/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 25/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 26/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 27/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 28/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.376389][Accuracy_mlp: 0.517014]
[Epoch 29/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 30/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 31/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 32/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 33/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 34/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 35/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 36/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.546528] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 37/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 38/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 39/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 40/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 41/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 42/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 43/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 44/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.517014]
[Epoch 45/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.592361]
[Epoch 46/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.592361]
[Epoch 47/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.592361]
[Epoch 48/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.592361]
[Epoch 49/50] [Accuracy_lsvm: 0.452431] [Accuracy_nrf: 0.557639] [Accuracy_pnb: 0.512153][Accuracy_mlp: 0.592361]

finished! best_acc:0.5924

## 最终结果
- LSVM最佳: 0.452431
- RF最佳: 0.557639
- NB最佳: 0.512153
- MLP最佳: 0.592361

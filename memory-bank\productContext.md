# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-07-25 11:10:54 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

*   本项目旨在实现 ACGAN-FG（带有特征生成的辅助分类器生成对抗网络），一种用于智能故障诊断的零样本学习方法。
*   该实现基于 <PERSON><PERSON>, L. <PERSON>, S. <PERSON> 和 S. Fujimura 在 IEEE Transactions on Industrial Informatics 上发表的论文。

## Key Features

*

## Overall Architecture

*   该项目使用 Python 3.9 和 TensorFlow 2.10.0。
*   它利用田纳西-伊斯曼过程 (TEP) 数据集进行训练和评估。
*   核心逻辑似乎在 `ACGAN-FG.py` 文件中，可以通过 `python ACGAN-FG.py` 命令直接运行。
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 ACGAN-FG 组别运行脚本
用于在tmux中运行不同组别的实验
"""

import sys
import os

# 添加scripts目录到路径
sys.path.append('scripts')

# 确保在正确的工作目录
if not os.path.exists('./data/dataset_train_case1.npz'):
    print("❌ 数据文件未找到！")
    print("📁 请确保在项目根目录运行此脚本")
    print("📁 当前目录:", os.getcwd())
    print("📁 需要的文件: ./data/dataset_train_case1.npz")
    sys.exit(1)

from ACGAN_FG_refactored import Zero_shot

def main():
    """主函数 - 支持命令行参数选择组别"""
    
    # 默认参数
    group_name = 'E'  # 默认E组（与原版一致）
    epochs = 2000
    batch_size = 120
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        group_name = sys.argv[1].upper()
    
    if len(sys.argv) > 2:
        epochs = int(sys.argv[2])
    
    if len(sys.argv) > 3:
        batch_size = int(sys.argv[3])
    
    print("🔥 ACGAN-FG 重构版本 - 组别运行脚本")
    print("=" * 60)
    print(f"📋 运行参数:")
    print(f"   组别: {group_name}")
    print(f"   训练轮数: {epochs}")
    print(f"   批次大小: {batch_size}")
    print("=" * 60)
    
    # 验证组别
    valid_groups = ['A', 'B', 'C', 'D', 'E']
    if group_name not in valid_groups:
        print(f"❌ 无效组别 '{group_name}'")
        print(f"✅ 可用组别: {valid_groups}")
        print("\n📖 使用方法:")
        print("   python3 run_acgan_group.py [组别] [轮数] [批次大小]")
        print("\n📝 示例:")
        print("   python3 run_acgan_group.py E 2000 120    # E组，2000轮，批次120")
        print("   python3 run_acgan_group.py A 1000 64     # A组，1000轮，批次64")
        print("   python3 run_acgan_group.py C             # C组，默认参数")
        return
    
    # 创建模型并开始训练
    try:
        print("🚀 正在初始化ACGAN-FG模型...")
        gan = Zero_shot()
        
        print(f"🎯 开始{group_name}组训练...")
        gan.train(epochs=epochs, batch_size=batch_size, group_name=group_name)
        
        print("🎉 训练完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断训练")
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

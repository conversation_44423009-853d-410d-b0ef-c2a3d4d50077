#!/usr/bin/env python3
"""
Data-Centric Diagnosis (English Version)
Phase 1: Deep Diagnosis of "Data Difficulties"
Goal: Explain why Group A is "easy" and Group B is "hard"

Analysis Content:
1. Semantic Space Analysis
2. Feature Space Visualization (t-SNE/UMAP)
3. Class Difficulty Quantification Analysis
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import umap
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Set English fonts to avoid Chinese character issues
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

class DataDiagnosis:
    def __init__(self, data_dir='/home/<USER>/hmt/ACGAN-FG-main/data/'):
        self.data_dir = data_dir
        self.attribute_matrix = None
        self.class_data = {}
        self.test_data = {}
        
        # Define experimental groups
        self.groups = {
            'A': [1, 6, 14],   # Easiest group
            'B': [4, 7, 10],   # Hardest group
            'C': [8, 11, 12],  # Medium group
            'D': [2, 3, 5],    # Medium-high group
            'E': [9, 13, 15]   # Medium-low group
        }
        
        # Experimental results accuracy (based on previous analysis)
        self.group_accuracy = {
            'A': {'LinearSVM': 82.40, 'RandomForest': 83.65, 'GaussianNB': 77.88, 'MLPClassifier': 67.22},
            'B': {'LinearSVM': 43.06, 'RandomForest': 44.72, 'GaussianNB': 47.33, 'MLPClassifier': 42.40},
            'C': {'LinearSVM': 41.91, 'RandomForest': 49.51, 'GaussianNB': 52.26, 'MLPClassifier': 47.71},
            'D': {'LinearSVM': 63.54, 'RandomForest': 63.37, 'GaussianNB': 64.48, 'MLPClassifier': 64.20},
            'E': {'LinearSVM': 44.69, 'RandomForest': 46.08, 'GaussianNB': 55.63, 'MLPClassifier': 43.92}
        }
        
    def load_attribute_matrix(self):
        """Load attribute matrix"""
        try:
            # Try to read Excel file
            df = pd.read_excel(f'{self.data_dir}/attribute_matrix.xlsx', header=None)
            # Remove header row and class ID column, keep only attribute values
            self.attribute_matrix = df.iloc[1:, 1:].astype(float)  # Skip first row and first column
            print(f"✅ Successfully loaded attribute matrix: {self.attribute_matrix.shape}")
            print(f"   Attributes for {self.attribute_matrix.shape[0]} classes with {self.attribute_matrix.shape[1]} features")
        except Exception as e:
            print(f"❌ Cannot load attribute matrix: {e}")
            # Create simulated attribute matrix for demonstration
            np.random.seed(42)
            self.attribute_matrix = pd.DataFrame(np.random.randn(15, 20))
            print("🔄 Using simulated attribute matrix for demonstration")
            
    def load_class_data(self):
        """Load training and test data for all classes"""
        for class_id in range(1, 16):
            try:
                # Load training data
                train_file = f'{self.data_dir}/d{class_id:02d}.dat'
                train_data = np.loadtxt(train_file)
                self.class_data[class_id] = train_data
                
                # Load test data
                test_file = f'{self.data_dir}/d{class_id:02d}_te.dat'
                test_data = np.loadtxt(test_file)
                self.test_data[class_id] = test_data
                
                print(f"✅ Class {class_id:2d}: Train samples {train_data.shape[0]:3d}, Test samples {test_data.shape[0]:3d}")
                
            except Exception as e:
                print(f"❌ Cannot load data for class {class_id}: {e}")
                
    def analyze_semantic_similarity(self):
        """Analyze semantic space similarity"""
        if self.attribute_matrix is None:
            print("❌ Attribute matrix not loaded")
            return
            
        print("\n" + "="*60)
        print("📊 Semantic Space Analysis")
        print("="*60)
        
        # Calculate cosine similarity matrix
        similarity_matrix = cosine_similarity(self.attribute_matrix.values)
        
        # Create visualization
        plt.figure(figsize=(15, 12))
        
        # 1. Global similarity heatmap
        plt.subplot(2, 2, 1)
        sns.heatmap(similarity_matrix, 
                   annot=True, 
                   fmt='.2f', 
                   cmap='RdYlBu_r',
                   xticklabels=range(1, 16),
                   yticklabels=range(1, 16),
                   cbar_kws={'label': 'Cosine Similarity'})
        plt.title('Semantic Similarity Matrix of All Classes', fontsize=14, fontweight='bold')
        plt.xlabel('Class ID')
        plt.ylabel('Class ID')
        
        # 2. Intra-group similarity analysis
        plt.subplot(2, 2, 2)
        group_similarities = {}
        group_colors = ['red', 'blue', 'green', 'orange', 'purple']
        
        for i, (group_name, classes) in enumerate(self.groups.items()):
            # Calculate average intra-group similarity
            group_sim = []
            for j, class1 in enumerate(classes):
                for k, class2 in enumerate(classes):
                    if j < k:  # Avoid duplicate calculations
                        sim = similarity_matrix[class1-1, class2-1]
                        group_sim.append(sim)
            
            avg_sim = np.mean(group_sim) if group_sim else 0
            group_similarities[group_name] = avg_sim
            
            # Plot intra-group similarity
            plt.bar(group_name, avg_sim, color=group_colors[i], alpha=0.7)
            plt.text(group_name, avg_sim + 0.01, f'{avg_sim:.3f}', 
                    ha='center', va='bottom', fontweight='bold')
        
        plt.title('Average Intra-Group Semantic Similarity', fontsize=14, fontweight='bold')
        plt.ylabel('Average Cosine Similarity')
        plt.ylim(0, 1)
        
        # 3. Inter-group similarity analysis
        plt.subplot(2, 2, 3)
        inter_group_sim = np.zeros((5, 5))
        group_names = list(self.groups.keys())
        
        for i, group1 in enumerate(group_names):
            for j, group2 in enumerate(group_names):
                if i != j:
                    sims = []
                    for class1 in self.groups[group1]:
                        for class2 in self.groups[group2]:
                            sims.append(similarity_matrix[class1-1, class2-1])
                    inter_group_sim[i, j] = np.mean(sims)
                else:
                    inter_group_sim[i, j] = group_similarities[group1]
        
        sns.heatmap(inter_group_sim,
                   annot=True,
                   fmt='.3f',
                   cmap='RdYlBu_r',
                   xticklabels=group_names,
                   yticklabels=group_names,
                   cbar_kws={'label': 'Average Similarity'})
        plt.title('Inter-Group Semantic Similarity Matrix', fontsize=14, fontweight='bold')
        
        # 4. Difficulty vs similarity relationship
        plt.subplot(2, 2, 4)
        avg_accuracies = []
        similarities = []
        
        for group_name, classes in self.groups.items():
            # Calculate average accuracy
            group_acc = self.group_accuracy[group_name]
            avg_acc = np.mean(list(group_acc.values()))
            avg_accuracies.append(avg_acc)
            similarities.append(group_similarities[group_name])
            
            plt.scatter(group_similarities[group_name], avg_acc, 
                       s=100, alpha=0.7, label=f'Group {group_name}')
            plt.text(group_similarities[group_name], avg_acc + 1, 
                    group_name, ha='center', va='bottom', fontweight='bold')
        
        # Fit trend line
        z = np.polyfit(similarities, avg_accuracies, 1)
        p = np.poly1d(z)
        x_trend = np.linspace(min(similarities), max(similarities), 100)
        plt.plot(x_trend, p(x_trend), "r--", alpha=0.8, linewidth=2)
        
        plt.xlabel('Average Intra-Group Semantic Similarity')
        plt.ylabel('Average Classification Accuracy (%)')
        plt.title('Semantic Similarity vs Classification Difficulty', fontsize=14, fontweight='bold')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('/home/<USER>/hmt/ACGAN-FG-main/semantic_analysis_en.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Print analysis results
        print("\n📈 Semantic Similarity Analysis Results:")
        print("-" * 40)
        for group_name, sim in group_similarities.items():
            classes = self.groups[group_name]
            avg_acc = np.mean(list(self.group_accuracy[group_name].values()))
            print(f"Group {group_name} {classes}: Similarity={sim:.3f}, Avg Accuracy={avg_acc:.1f}%")
        
        return similarity_matrix, group_similarities
        
    def visualize_feature_space(self, focus_groups=['A', 'B']):
        """Feature space visualization analysis"""
        print("\n" + "="*60)
        print("🎯 Feature Space Visualization")
        print("="*60)
        
        if not self.class_data:
            print("❌ Class data not loaded")
            return
            
        # Create visualization for each focus group
        for group_name in focus_groups:
            classes = self.groups[group_name]
            print(f"\n🔍 Analyzing Group {group_name}: {classes}")
            
            self._visualize_group_features(group_name, classes)
            
    def _visualize_group_features(self, group_name, classes):
        """Create feature space visualization for specific group"""
        # Collect data
        all_features = []
        all_labels = []
        data_types = []
        
        # Training set data (other classes)
        train_classes = [i for i in range(1, 16) if i not in classes]
        for class_id in train_classes:
            if class_id in self.class_data:
                features = self.class_data[class_id]
                all_features.append(features)
                all_labels.extend([class_id] * len(features))
                data_types.extend(['train'] * len(features))
        
        # Test set real features
        for class_id in classes:
            if class_id in self.test_data:
                features = self.test_data[class_id]
                all_features.append(features)
                all_labels.extend([class_id] * len(features))
                data_types.extend(['test_real'] * len(features))
        
        # Merge all features
        if not all_features:
            print(f"❌ No available data for Group {group_name}")
            return
            
        X = np.vstack(all_features)
        y = np.array(all_labels)
        types = np.array(data_types)
        
        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Dimensionality reduction visualization
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'Group {group_name} {classes} Feature Space Analysis', fontsize=16, fontweight='bold')
        
        # t-SNE
        print(f"  🔄 Performing t-SNE dimensionality reduction...")
        tsne = TSNE(n_components=2, random_state=42, perplexity=30)
        X_tsne = tsne.fit_transform(X_scaled)
        
        # UMAP
        print(f"  🔄 Performing UMAP dimensionality reduction...")
        umap_reducer = umap.UMAP(n_components=2, random_state=42)
        X_umap = umap_reducer.fit_transform(X_scaled)
        
        # PCA
        print(f"  🔄 Performing PCA dimensionality reduction...")
        pca = PCA(n_components=2, random_state=42)
        X_pca = pca.fit_transform(X_scaled)
        
        # Plot visualizations
        methods = [('t-SNE', X_tsne), ('UMAP', X_umap), ('PCA', X_pca)]
        
        for idx, (method_name, X_reduced) in enumerate(methods):
            if idx < 3:  # First three subplots
                ax = axes[idx//2, idx%2]
                
                # Plot training set data (gray background)
                train_mask = types == 'train'
                ax.scatter(X_reduced[train_mask, 0], X_reduced[train_mask, 1], 
                          c='lightgray', alpha=0.3, s=10, label='Training Set')
                
                # Plot test set real features (colored)
                colors = ['red', 'blue', 'green']
                for i, class_id in enumerate(classes):
                    class_mask = (y == class_id) & (types == 'test_real')
                    if np.any(class_mask):
                        ax.scatter(X_reduced[class_mask, 0], X_reduced[class_mask, 1],
                                  c=colors[i], alpha=0.8, s=50, 
                                  label=f'Class {class_id} (Real)', marker='o')
                
                ax.set_title(f'{method_name} Dimensionality Reduction', fontsize=12, fontweight='bold')
                ax.legend()
                ax.grid(True, alpha=0.3)
        
        # Fourth subplot: inter-class distance analysis
        ax = axes[1, 1]
        self._plot_class_distances(ax, classes, X_scaled, y, types)
        
        plt.tight_layout()
        plt.savefig(f'/home/<USER>/hmt/ACGAN-FG-main/feature_space_group_{group_name}_en.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
    def _plot_class_distances(self, ax, classes, X_scaled, y, types):
        """Plot inter-class distance analysis"""
        from sklearn.metrics.pairwise import euclidean_distances
        
        # Calculate class centers
        class_centers = {}
        for class_id in classes:
            class_mask = (y == class_id) & (types == 'test_real')
            if np.any(class_mask):
                class_centers[class_id] = np.mean(X_scaled[class_mask], axis=0)
        
        if len(class_centers) < 2:
            ax.text(0.5, 0.5, 'Insufficient Data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Calculate inter-class distances
        center_ids = list(class_centers.keys())
        center_features = np.array([class_centers[cid] for cid in center_ids])
        distances = euclidean_distances(center_features)
        
        # Plot distance matrix
        im = ax.imshow(distances, cmap='RdYlBu_r')
        ax.set_xticks(range(len(center_ids)))
        ax.set_yticks(range(len(center_ids)))
        ax.set_xticklabels([f'Class {cid}' for cid in center_ids])
        ax.set_yticklabels([f'Class {cid}' for cid in center_ids])
        
        # Add numerical annotations
        for i in range(len(center_ids)):
            for j in range(len(center_ids)):
                ax.text(j, i, f'{distances[i, j]:.2f}', 
                       ha='center', va='center', color='white', fontweight='bold')
        
        ax.set_title('Inter-Class Euclidean Distance', fontsize=12, fontweight='bold')
        plt.colorbar(im, ax=ax, label='Euclidean Distance')

def main():
    """Main function"""
    print("🚀 Starting data diagnosis analysis...")
    
    # Create diagnosis instance
    diagnosis = DataDiagnosis()
    
    # Load data
    print("\n📂 Loading data...")
    diagnosis.load_attribute_matrix()
    diagnosis.load_class_data()
    
    # Semantic space analysis
    similarity_matrix, group_similarities = diagnosis.analyze_semantic_similarity()
    
    # Feature space visualization
    diagnosis.visualize_feature_space(focus_groups=['A', 'B'])
    
    print("\n✅ Data diagnosis analysis completed!")
    print("📊 Generated files:")
    print("  - semantic_analysis_en.png: Semantic space analysis results")
    print("  - feature_space_group_A_en.png: Group A feature space analysis")
    print("  - feature_space_group_B_en.png: Group B feature space analysis")

if __name__ == "__main__":
    main()

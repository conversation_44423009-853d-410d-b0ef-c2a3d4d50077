import numpy as np
import pandas as pd
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test_hard_negative import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler
from data_pipeline import OptimizedDataPipeline, monitor_gpu, check_docker_gpu_config

# 配置GPU显存按需增长和混合精度
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print("GPU显存增长已启用")
    except RuntimeError as e:
        print(f"GPU配置错误: {e}")

# 启用超级优化混合精度
tf.keras.mixed_precision.set_global_policy('mixed_float16')
print("已启用超级优化混合精度训练 (float16)")

class MultiHeadSelfAttention(Layer):
    """多头自注意力机制 - 增加计算强度"""
    def __init__(self, num_heads=8, key_dim=64, **kwargs):
        super(MultiHeadSelfAttention, self).__init__(**kwargs)
        self.num_heads = num_heads
        self.key_dim = key_dim
        self.attention = tf.keras.layers.MultiHeadAttention(
            num_heads=num_heads, 
            key_dim=key_dim,
            dropout=0.1
        )
        self.norm = tf.keras.layers.LayerNormalization()

    def call(self, inputs):
        # 多头自注意力
        attention_output = self.attention(inputs, inputs)
        # 残差连接和归一化
        return self.norm(inputs + attention_output)

class TransformerBlock(Layer):
    """Transformer块 - 大幅增加计算复杂度"""
    def __init__(self, embed_dim=256, num_heads=8, ff_dim=512, dropout=0.1, **kwargs):
        super(TransformerBlock, self).__init__(**kwargs)
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.ff_dim = ff_dim
        self.dropout_rate = dropout
        
        self.attention = tf.keras.layers.MultiHeadAttention(
            num_heads=num_heads, 
            key_dim=embed_dim // num_heads,
            dropout=dropout
        )
        self.ffn = tf.keras.Sequential([
            tf.keras.layers.Dense(ff_dim, activation='gelu'),
            tf.keras.layers.Dropout(dropout),
            tf.keras.layers.Dense(embed_dim)
        ])
        self.norm1 = tf.keras.layers.LayerNormalization()
        self.norm2 = tf.keras.layers.LayerNormalization()
        self.dropout1 = tf.keras.layers.Dropout(dropout)
        self.dropout2 = tf.keras.layers.Dropout(dropout)

    def call(self, inputs, training=False):
        # 多头注意力子层
        attention_output = self.attention(inputs, inputs, training=training)
        attention_output = self.dropout1(attention_output, training=training)
        out1 = self.norm1(inputs + attention_output)
        
        # 前馈网络子层
        ffn_output = self.ffn(out1, training=training)
        ffn_output = self.dropout2(ffn_output, training=training)
        return self.norm2(out1 + ffn_output)

def ultra_residual_block(x, units, num_layers=3):
    """超级残差块 - 增加网络深度和计算强度"""
    shortcut = x
    
    for i in range(num_layers):
        x = Dense(units)(x)
        x = BatchNormalization()(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.1)(x)
    
    # 投影shortcut到正确维度
    if int(shortcut.shape[-1]) != units:
        shortcut = Dense(units)(shortcut)
        shortcut = BatchNormalization()(shortcut)
    
    x = tf.keras.layers.add([x, shortcut])
    x = LeakyReLU(alpha=0.2)(x)
    return x

class Zero_shot_Adaptive_Ultra_Optimized:
    def __init__(self, group='B', use_adaptive=True, alpha=1.0, beta=0.5):
        self.group = group
        self.use_adaptive = use_adaptive
        
        # 基础参数
        self.data_lenth = 52
        self.sample_shape = (self.data_lenth,)
        self.feature_dim = 512  # 增加特征维度
        self.feature_shape = (512,)
        self.num_classes = 15
        self.latent_dim = 128   # 超级优化版使用128维噪声
        self.noise_shape = (self.latent_dim, 1)
        self.n_critic = 1
        self.crl = True
        
        # 基础损失权重
        self.base_lambda_cla = 10
        self.base_lambda_triplet = 10
        self.lambda_crl = 0.01
        
        # 当前使用的权重
        self.lambda_cla = self.base_lambda_cla
        self.lambda_triplet = self.base_lambda_triplet
        
        # 其他参数
        self.bound = True
        self.mi_weight = 0.001
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        # 初始化超级优化数据流水线
        self.data_pipeline = OptimizedDataPipeline(use_mixed_precision=True)
        print("已启用超级优化数据流水线和混合精度训练")
        
        # 优化器 - 使用学习率调度
        self.autoencoder_optimizer = tf.keras.optimizers.AdamW(learning_rate=0.0002, weight_decay=0.0001)
        self.d_optimizer = tf.keras.optimizers.AdamW(learning_rate=0.0001, weight_decay=0.0001)
        self.g_optimizer = tf.keras.optimizers.AdamW(learning_rate=0.0002, weight_decay=0.0001)
        self.c_optimizer = tf.keras.optimizers.AdamW(learning_rate=0.0001, weight_decay=0.0001)
        self.m_optimizer = tf.keras.optimizers.AdamW(learning_rate=0.0001, weight_decay=0.0001)
        
        # 构建超级复杂模型
        self.autoencoder = self.build_ultra_autoencoder()
        self.d = self.build_ultra_discriminator()
        self.g = self.build_ultra_generator()
        self.c = self.build_ultra_classifier()
        
        print(f"超级优化版Zero-shot模型初始化完成 (Group {self.group})")
        print(f"模型复杂度: 自编码器参数 ~{self.autoencoder.count_params()}, 生成器参数 ~{self.g.count_params()}")

    def build_ultra_autoencoder(self):
        """超级复杂的自编码器"""
        sample = Input(shape=self.sample_shape)
        
        # 编码器部分 - 深度网络
        e1 = Dense(512)(sample)
        e1 = BatchNormalization()(e1)
        e1 = LeakyReLU(alpha=0.2)(e1)
        
        # 多个超级残差块
        e2 = ultra_residual_block(e1, 512, num_layers=4)
        e3 = ultra_residual_block(e2, 512, num_layers=4)
        e4 = ultra_residual_block(e3, 512, num_layers=4)
        
        # 多头注意力层
        e4_reshaped = tf.expand_dims(e4, axis=1)  # 添加序列维度
        attention_output = MultiHeadSelfAttention(num_heads=16, key_dim=32)(e4_reshaped)
        attention_output = tf.squeeze(attention_output, axis=1)
        
        # Transformer块
        transformer_input = tf.expand_dims(attention_output, axis=1)
        for i in range(3):  # 3个Transformer块
            transformer_input = TransformerBlock(embed_dim=512, num_heads=16, ff_dim=1024)(transformer_input)
        transformer_output = tf.squeeze(transformer_input, axis=1)
        
        # 特征输出
        feature = Dense(512, dtype='float32')(transformer_output)  # 输出层用float32
        feature = BatchNormalization()(feature)
        
        # 解码器部分 - 对称深度网络
        d1 = Dense(512, activation='gelu')(feature)
        d1 = BatchNormalization()(d1)
        d1 = Dropout(0.1)(d1)
        
        d2 = ultra_residual_block(d1, 512, num_layers=3)
        d3 = ultra_residual_block(d2, 256, num_layers=3)
        d4 = ultra_residual_block(d3, 128, num_layers=2)
        
        output_sample = Dense(self.data_lenth, activation='linear', dtype='float32')(d4)
        
        encoder = Model(sample, feature)
        self.encoder = encoder
        
        return Model(sample, [feature, output_sample])

    def build_ultra_discriminator(self):
        """超级复杂的判别器"""
        feature = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        # 特征嵌入 - 深度网络
        feature_embedding = Dense(512)(feature)
        feature_embedding = BatchNormalization()(feature_embedding)
        feature_embedding = LeakyReLU(alpha=0.2)(feature_embedding)
        feature_embedding = ultra_residual_block(feature_embedding, 512, num_layers=3)
        
        # 属性嵌入 - 深度网络
        attribute_embedding = Dense(256)(attribute)
        attribute_embedding = BatchNormalization()(attribute_embedding)
        attribute_embedding = LeakyReLU(alpha=0.2)(attribute_embedding)
        attribute_embedding = ultra_residual_block(attribute_embedding, 256, num_layers=2)
        
        # 融合
        d_input = concatenate([feature_embedding, attribute_embedding])
        
        # 超级深度判别网络
        d1 = ultra_residual_block(d_input, 768, num_layers=4)
        d2 = ultra_residual_block(d1, 512, num_layers=3)
        d3 = ultra_residual_block(d2, 256, num_layers=3)
        d4 = ultra_residual_block(d3, 128, num_layers=2)
        
        # 多头注意力
        d4_reshaped = tf.expand_dims(d4, axis=1)
        attention_output = MultiHeadSelfAttention(num_heads=8, key_dim=16)(d4_reshaped)
        attention_output = tf.squeeze(attention_output, axis=1)
        
        validity = Dense(1, dtype='float32')(attention_output)
        
        return Model([feature, attribute], validity)

    def build_ultra_generator(self):
        """超级复杂的生成器"""
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        # 噪声嵌入
        noise_embedding = Flatten()(noise)
        noise_embedding = Dense(512)(noise_embedding)
        noise_embedding = BatchNormalization()(noise_embedding)
        noise_embedding = LeakyReLU(alpha=0.2)(noise_embedding)
        
        # 属性嵌入 - 深度网络
        attribute_embedding = Dense(256)(attribute)
        attribute_embedding = BatchNormalization()(attribute_embedding)
        attribute_embedding = LeakyReLU(alpha=0.2)(attribute_embedding)
        attribute_embedding = ultra_residual_block(attribute_embedding, 256, num_layers=2)
        
        # 融合输入
        g_input = concatenate([noise_embedding, attribute_embedding])

        # 超级深度生成网络
        g1 = ultra_residual_block(g_input, 768, num_layers=4)
        g2 = ultra_residual_block(g1, 512, num_layers=4)
        g3 = ultra_residual_block(g2, 512, num_layers=4)
        
        # 多个Transformer块
        g3_reshaped = tf.expand_dims(g3, axis=1)
        for i in range(2):  # 2个Transformer块
            g3_reshaped = TransformerBlock(embed_dim=512, num_heads=16, ff_dim=1024)(g3_reshaped)
        transformer_output = tf.squeeze(g3_reshaped, axis=1)
        
        # 最终特征生成
        generated_feature = Dense(512, dtype='float32')(transformer_output)
        generated_feature = BatchNormalization()(generated_feature)

        return Model([noise, attribute], generated_feature)

    def build_ultra_classifier(self):
        """超级复杂的分类器"""
        sample = Input(shape=self.feature_shape)

        # 深度分类网络
        c1 = ultra_residual_block(sample, 512, num_layers=3)
        c2 = ultra_residual_block(c1, 256, num_layers=3)
        c3 = ultra_residual_block(c2, 128, num_layers=2)
        
        # 注意力机制
        c3_reshaped = tf.expand_dims(c3, axis=1)
        attention_output = MultiHeadSelfAttention(num_heads=8, key_dim=16)(c3_reshaped)
        attention_output = tf.squeeze(attention_output, axis=1)
        
        hidden_output = Dense(64)(attention_output)
        hidden_output = LeakyReLU(alpha=0.2)(hidden_output)
        hidden_output = Dropout(0.1)(hidden_output)
               
        predict_attribute = Dense(20, activation="sigmoid", dtype='float32')(hidden_output)
        
        return Model(sample, [hidden_output, predict_attribute])

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        """混合精度优化的Wasserstein损失"""
        y_true = tf.cast(y_true, y_pred.dtype)
        return K.mean(y_true * y_pred)

    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(128, activation='gelu')(samples)
            h2 = Dense(64, activation='gelu')(h1)
            return Dense(1, dtype='float32')(h2)  # 输出用float32避免数值不稳定
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        # 确保数值稳定性，转换为float32进行计算
        t_joint = tf.cast(t_joint, tf.float32)
        t_marginal = tf.cast(t_marginal, tf.float32)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est

    def mi_penalty_loss(self, x, z):
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)

    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)
        
        mi_penalty = 0
        if self.bound == True:
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)
            # 确保类型一致性 - 修复混合精度问题
            mi_penalty = tf.cast(mi_penalty, classification_loss.dtype)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss

    def cycle_rank_loss(self, anchor, positive, negative):
        return self.triplet_loss(anchor, positive, negative)

    def train_ultra_optimized(self, epochs, batch_size=1024, log_file=None):
        """超级优化训练方法 - 设计来充分利用RTX 5080"""
        start_time = datetime.datetime.now()
        
        accuracy_list_1 = []
        accuracy_list_2 = []
        accuracy_list_3 = []
        accuracy_list_4 = []
        
        PATH_train = './dataset_train_case1.npz'
        PATH_test = './dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        print(f"开始为Group {self.group}准备超级优化数据流水线，批处理大小: {batch_size}")
        
        # 使用超级优化数据流水线准备数据
        data_info = self.data_pipeline.prepare_data_adaptive(
            train_data, test_data, self.group, batch_size=batch_size, shuffle_buffer=30000
        )
        
        train_dataset = data_info['train_dataset']
        train_X_by_class = data_info['train_X_by_class'] 
        train_Y_by_class = data_info['train_Y_by_class']
        seen_classes = data_info['seen_classes']
        unseen_classes = data_info['unseen_classes']
        testdata = data_info['test_X']
        test_attributelabel = data_info['test_Y']

        print(f"开始超级优化训练 Group {self.group}")
        print(f"批处理大小: {batch_size} (专为RTX 5080优化)")
        print(f"模型复杂度: 超高 (设计来充分利用GPU)")

        for epoch in range(epochs):
            epoch_losses = {'ae_c': [], 'm': [], 'd': [], 'g': []}
            batch_count = 0
            
            for batch_data in train_dataset:
                train_x, train_y, train_labels = batch_data
                current_batch_size = tf.shape(train_x)[0]
                batch_count += 1
                
                # 每50个批次监控GPU
                if batch_count % 50 == 0:
                    print(f"Epoch {epoch}, Batch {batch_count} - 超级训练GPU监控:")
                    monitor_gpu()
                
                # 超级优化的Autoencoder和Classifier训练
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                    feature, output_sample = self.autoencoder(train_x, training=True)
                    autoencoder_loss = mean_squared_error(train_x, output_sample)

                    hidden_ouput_c, predict_attribute_c = self.c(feature, training=True)
                    c_loss = self.classification_loss(feature, train_y, hidden_ouput_c, predict_attribute_c)

                    total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c
                epoch_losses['ae_c'].append(float(tf.reduce_mean(total_ac_loss)))

                # 超级优化的Triplet Loss训练
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                # 超级采样策略
                train_labels_np = train_labels.numpy()
                positive_samples = []
                negative_samples = []
                
                for label in train_labels_np:
                    if label in train_X_by_class and len(train_X_by_class[label]) > 0:
                        pos_class_samples = train_X_by_class[label]
                        pos_idx = np.random.choice(len(pos_class_samples))
                        positive_samples.append(pos_class_samples[pos_idx])
                    else:
                        positive_samples.append(train_x[0].numpy())
                    
                    available_classes = [c for c in seen_classes if c != label and c in train_X_by_class]
                    if available_classes:
                        neg_class = np.random.choice(available_classes)
                        neg_class_samples = train_X_by_class[neg_class]
                        neg_idx = np.random.choice(len(neg_class_samples))
                        negative_samples.append(neg_class_samples[neg_idx])
                    else:
                        negative_samples.append(train_x[0].numpy())

                positive_samples = tf.constant(np.array(positive_samples), dtype=tf.float16)
                negative_samples = tf.constant(np.array(negative_samples), dtype=tf.float16)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(train_x, training=True)
                    positive_features = self.encoder(positive_samples, training=True)
                    negative_features = self.encoder(negative_samples, training=True)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))
                epoch_losses['m'].append(float(m_loss))

                # 超级判别器训练
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False
                
                with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(current_batch_size, self.latent_dim, 1), dtype=tf.float16)
                    Fake_feature = self.g([noise, train_y], training=True)
                    Real_validity = self.d([feature, train_y], training=True)
                    Fake_validity = self.d([Fake_feature, train_y], training=True)
                    
                    valid = -tf.ones((current_batch_size, 1), dtype=tf.float16)
                    fake = tf.ones((current_batch_size, 1), dtype=tf.float16)
                    
                    d_loss_real = self.wasserstein_loss(valid, Real_validity)
                    d_loss_fake = self.wasserstein_loss(fake, Fake_validity)
                    d_loss = (d_loss_real + d_loss_fake) / 2

                grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))
                epoch_losses['d'].append(float(d_loss))

                # 超级生成器训练
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = False
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                    noise_g = tf.random.normal(shape=(current_batch_size, self.latent_dim, 1), dtype=tf.float16)
                    Fake_feature_g = self.g([noise_g, train_y], training=True)
                    Fake_validity_g = self.d([Fake_feature_g, train_y], training=True)
                    valid = -tf.ones((current_batch_size, 1), dtype=tf.float16)
                    adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
              
                    fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g, training=True)
                    classification_loss = self.classification_loss(Fake_feature_g, train_y, fake_hidden_ouput_g, Fake_classification_g)
                    
                    # 超级Triplet loss
                    g_anchor_features = Fake_feature_g
                    g_positive_features = self.encoder(positive_samples, training=True)
                    g_negative_features = self.encoder(negative_samples, training=True)
                    triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                    
                    cycle_rank_loss = 0
                    if self.crl == True:
                        reconstructed_feature = self.g([noise_g, Fake_classification_g], training=True)
                        
                        batch_size_np = train_labels_np.shape[0]
                        negative_attributes = []
                        for label in train_labels_np:
                            available_classes = [c for c in seen_classes if c != label and c in train_Y_by_class]
                            if available_classes:
                                neg_class = np.random.choice(available_classes)
                                negative_attributes.append(train_Y_by_class[neg_class][0])
                            else:
                                negative_attributes.append(train_y[0].numpy())
                        
                        negative_attributes = tf.constant(np.array(negative_attributes), dtype=tf.float16)
                        unsimilar_generated_feature = self.g([noise_g, negative_attributes], training=True)

                        cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                               
                    # 确保所有损失的类型一致性 - 修复混合精度问题
                    adversarial_loss = tf.cast(adversarial_loss, tf.float32)
                    classification_loss = tf.cast(classification_loss, tf.float32) 
                    triplet_loss_g = tf.cast(triplet_loss_g, tf.float32)
                    cycle_rank_loss = tf.cast(cycle_rank_loss, tf.float32)
                    
                    total_loss = adversarial_loss + self.lambda_cla * classification_loss + self.lambda_triplet * triplet_loss_g + self.lambda_crl * cycle_rank_loss
                              
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                epoch_losses['g'].append(float(tf.reduce_mean(total_loss)))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                if batch_count % 5 == 0:  # 每5个批次打印一次
                    print("[Epoch %d/%d][Batch %d][AE+C loss: %f][M loss: %f][D loss: %f][G loss %05f] GPU利用率应该很高! time: %s" \
                         % (epoch, epochs,
                           batch_count,
                           np.mean(epoch_losses['ae_c'][-5:]), 
                           np.mean(epoch_losses['m'][-5:]),
                           np.mean(epoch_losses['d'][-5:]),
                           np.mean(epoch_losses['g'][-5:]),
                           elapsed_time))
        
            # 每个epoch结束后进行评估
            if epoch % 1 == 0:
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis(
                    2000, testdata, test_attributelabel, self.autoencoder, self.g, self.c, unseen_classes)

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                      %(epoch, epochs, max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)))
                      
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
        
        return {
            'best_accuracy': max(accuracy_list_4) if accuracy_list_4 else 0.0,
            'accuracy_lsvm': max(accuracy_list_1) if accuracy_list_1 else 0.0,
            'accuracy_nrf': max(accuracy_list_2) if accuracy_list_2 else 0.0,
            'accuracy_pnb': max(accuracy_list_3) if accuracy_list_3 else 0.0,
            'accuracy_mlp': max(accuracy_list_4) if accuracy_list_4 else 0.0,
        }

def run_ultra_optimized_experiments(target_group='B', epochs=500, batch_size=1024):
    """运行超级优化版实验 - 专为RTX 5080设计"""
    
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    
    print("=== 开始超级优化版实验 ===")
    print("🚀 专为RTX 5080设计的超高GPU利用率版本")
    log_filename = os.path.join(results_dir, start_run_time.strftime("%Y%m%d%H%M") + f"_ultra_optimized_Group{target_group}.md")
    
    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 超级优化版实验 - Group {target_group}\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**优化特性**: 超深网络 + 多头注意力 + Transformer + 混合精度 + 大批处理\n")
        log_file.write(f"**批处理大小**: {batch_size} (专为RTX 5080优化)\n")
        log_file.write(f"**目标**: 达到>70% GPU利用率\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan_ultra = Zero_shot_Adaptive_Ultra_Optimized(group=target_group, use_adaptive=True)
        results = gan_ultra.train_ultra_optimized(epochs=epochs, batch_size=batch_size, log_file=log_file)
        
        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")
        log_file.write(f"**最佳准确率**: {results['best_accuracy']:.4f}\n")

    print(f"=== 超级优化版实验完成 ===")
    print(f"最佳准确率: {results['best_accuracy']:.4f}")
    print(f"详细结果已保存至: {log_filename}")
    
    return results

if __name__ == '__main__':
    TARGET_GROUP = 'B'  # 可选: 'A', 'B', 'C', 'D', 'E'
    
    print(f"🚀 开始运行 Group {TARGET_GROUP} 的超级优化版实验")
    print("💪 专为RTX 5080设计，目标GPU利用率 > 70%")
    print("⚡ 特性: 超深网络 + 多头注意力 + Transformer + 混合精度")
    results = run_ultra_optimized_experiments(target_group=TARGET_GROUP, epochs=500, batch_size=1024) 
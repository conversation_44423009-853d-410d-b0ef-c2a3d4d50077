#!/usr/bin/env python3
"""
D-Enhanced实验脚本
基于B-HardTriplet成功配置，针对D组相似类别问题的强化版本

关键改进：
1. 恢复B-HardTriplet成功权重：triplet=50, center=2.5
2. 大幅提升学习率：0.00001 -> 0.0002
3. 强化CRL和语义权重
4. 针对D组[2,3,5]相似类别优化
"""

import os
import sys
import datetime
import subprocess

def run_d_enhanced_experiment():
    """运行D-Enhanced强化实验"""
    
    print("🚀 D-Enhanced实验启动")
    print("=" * 60)
    print("📋 实验配置:")
    print("   - Triplet权重: 50.0 (恢复B-HardTriplet成功配置)")
    print("   - Center权重: 2.5 (恢复B-HardTriplet成功配置)")
    print("   - CRL权重: 0.1 (大幅提升)")
    print("   - 学习率: 0.0002 (提升20倍)")
    print("   - 目标: 解决D组相似类别分离问题")
    print("=" * 60)
    
    # 确认用户要继续
    response = input("🤔 确认开始D-Enhanced实验? (y/n): ")
    if response.lower() != 'y':
        print("❌ 实验取消")
        return
    
    # 记录开始时间
    start_time = datetime.datetime.now()
    print(f"⏰ 实验开始时间: {start_time}")
    
    # 构建Docker命令
    docker_cmd = [
        "docker", "exec", "-it", "acgan-container",
        "python", "/app/scripts/acgan_triplet_Hybrid.py",
        "--group", "D",
        "--epochs", "2000",  # 长时间训练
        "--experiment_name", "D-Enhanced-Recovery"
    ]
    
    print("🐳 Docker命令:")
    print(" ".join(docker_cmd))
    print()
    
    try:
        # 运行实验
        print("🔥 开始训练...")
        result = subprocess.run(docker_cmd, check=True, capture_output=False)
        
        # 计算总时间
        end_time = datetime.datetime.now()
        total_time = end_time - start_time
        
        print("=" * 60)
        print("✅ D-Enhanced实验完成!")
        print(f"⏰ 总耗时: {total_time}")
        print(f"📊 结果文件应该保存在: /app/results/")
        print("=" * 60)
        
        # 提供后续建议
        print("🔍 后续分析建议:")
        print("1. 检查准确率是否有显著提升 (目标: >45%)")
        print("2. 观察损失函数是否更稳定")
        print("3. 验证GAN平衡比例是否改善")
        print("4. 如果效果仍不理想，考虑架构改进")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 实验失败: {e}")
        print("🔧 可能的解决方案:")
        print("1. 检查Docker容器是否运行: docker start acgan-container")
        print("2. 检查脚本路径是否正确")
        print("3. 检查数据文件是否存在")
    
    except KeyboardInterrupt:
        print("\n⚠️ 实验被用户中断")
        print("💡 可以稍后重新运行此脚本继续实验")

def show_comparison():
    """显示配置对比"""
    print("\n📊 配置对比分析:")
    print("=" * 80)
    print(f"{'参数':<20} {'原C-Hybrid':<15} {'新D-Enhanced':<15} {'变化':<20}")
    print("-" * 80)
    print(f"{'Triplet权重':<20} {'5.0':<15} {'50.0':<15} {'提升10倍 ⬆️':<20}")
    print(f"{'Center权重':<20} {'0.1':<15} {'2.5':<15} {'提升25倍 ⬆️':<20}")
    print(f"{'CRL权重':<20} {'0.01':<15} {'0.1':<15} {'提升10倍 ⬆️':<20}")
    print(f"{'语义权重':<20} {'0.01':<15} {'0.05':<15} {'提升5倍 ⬆️':<20}")
    print(f"{'学习率':<20} {'0.00001':<15} {'0.0002':<15} {'提升20倍 ⬆️':<20}")
    print("=" * 80)
    print("🎯 预期改进:")
    print("   ✅ Triplet Loss不再过早饱和")
    print("   ✅ Center Loss更有效的类别分离")
    print("   ✅ 更强的优化动力")
    print("   ✅ 更稳定的GAN训练")
    print("   ✅ 针对D组相似类别的强化分离")

if __name__ == "__main__":
    print("🎯 D-Enhanced实验管理器")
    show_comparison()
    print()
    run_d_enhanced_experiment()

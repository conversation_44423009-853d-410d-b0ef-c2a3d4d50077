# 🔍 VAEGAN-AR 超参数搜索系统

## 📋 系统概述

这是一个专为VAEGAN-AR模型设计的系统性超参数搜索工具，通过两阶段网格搜索优化关键超参数：
1. **阶段1**: 搜索最佳 `lambda_ar` (属性回归器损失权重)
2. **阶段2**: 搜索最佳 `lr` (学习率)

### 🎯 核心特性
- ✅ **两阶段搜索策略**: 先优化lambda_ar，再优化学习率
- ✅ **智能早停机制**: 避免过度训练，节省时间
- ✅ **完整结果记录**: CSV + JSON格式，支持后续分析
- ✅ **可视化分析**: 自动生成参数敏感性图表
- ✅ **断点续传**: 支持分阶段执行和错误恢复
- ✅ **资源管理**: 自动GPU内存清理

## 🚀 快速开始

### 1. 环境准备
确保已安装所需依赖：
```bash
pip install torch pandas matplotlib seaborn tqdm scikit-learn
```

### 2. 基本使用

#### 完整搜索 (推荐)
```bash
# 对A组数据进行完整的两阶段搜索
python run_hyperparameter_search.py --split A --stage all

# 自定义训练轮数
python run_hyperparameter_search.py --split A --stage all --epochs 200 --patience 25
```

#### 分阶段搜索
```bash
# 只运行阶段1 (lambda_ar搜索)
python run_hyperparameter_search.py --split A --stage 1

# 只运行阶段2 (学习率搜索)
python run_hyperparameter_search.py --split A --stage 2
```

#### 结果分析
```bash
# 生成详细分析报告
python analyze_search_results.py --split A

# 或在搜索时自动生成
python run_hyperparameter_search.py --split A --stage all --generate-report
```

## ⚙️ 配置参数

### 搜索范围配置
默认搜索范围在 `search_config.py` 中定义：
- **Lambda_AR**: [0.1, 0.3, 0.5, 0.8, 1.0, 1.5]
- **学习率**: [0.00005, 0.0001, 0.0002]

### 自定义搜索范围
```bash
# 自定义lambda_ar范围
python run_hyperparameter_search.py --split A --stage 1 --lambda-ar-range 0.1 0.5 1.0 2.0

# 自定义学习率范围
python run_hyperparameter_search.py --split A --stage 2 --lr-range 0.00001 0.0001 0.001
```

### 训练参数配置
```bash
python run_hyperparameter_search.py --split A --stage all \
    --epochs 150 \          # 每个实验的最大训练轮数
    --patience 20 \         # 早停耐心值
    --min-epochs 50 \       # 最少训练轮数
    --display-freq 1        # 准确率显示频率 (1=每轮, 5=每5轮)
```

## 📊 结果文件结构

搜索完成后，会在 `hyperparameter_search_results/` 目录下生成：

```
hyperparameter_search_results/
├── lambda_ar_search_split_A.csv          # 阶段1结果 (CSV格式)
├── lambda_ar_search_split_A.json         # 阶段1结果 (JSON格式)
├── lr_search_split_A.csv                 # 阶段2结果 (CSV格式)
├── lr_search_split_A.json                # 阶段2结果 (JSON格式)
├── best_config_split_A.json              # 最佳配置
├── search_summary_split_A.txt            # 搜索摘要报告
└── analysis_split_A/                     # 分析图表目录
    ├── comprehensive_analysis.png        # 综合分析图
    ├── parameter_sensitivity.png         # 参数敏感性分析
    ├── training_dynamics.png             # 训练动态分析
    ├── performance_distribution.png      # 性能分布分析
    ├── efficiency_analysis.png           # 效率分析
    └── statistical_summary.txt           # 统计摘要
```

## 📈 结果解读

### 1. 最佳配置文件 (`best_config_split_A.json`)
```json
{
  "split_name": "A",
  "best_lambda_ar": 0.8,
  "best_lr": 0.0001,
  "search_completed": true,
  "total_search_time_hours": 18.5
}
```

### 2. CSV结果文件
包含每个实验的详细信息：
- `experiment_name`: 实验名称
- `parameters`: 参数配置
- `best_accuracy`: 最佳准确率
- `best_epoch`: 最佳轮数
- `training_time_minutes`: 训练时间
- `early_stopped`: 是否早停

### 3. 分析图表
- **参数敏感性**: 显示参数值与性能的关系
- **训练动态**: 分析收敛速度和早停情况
- **性能分布**: 展示准确率的统计分布
- **效率分析**: 评估时间效率和资源利用

## 🔧 高级用法

### 1. 断点续传
如果搜索中断，可以分阶段继续：
```bash
# 如果阶段1已完成，直接运行阶段2
python run_hyperparameter_search.py --split A --stage 2

# 或指定特定的lambda_ar值
python run_hyperparameter_search.py --split A --stage 2 --best-lambda-ar 0.8
```

### 2. 批量搜索
```bash
# 对所有分组进行搜索
for split in A B C D E; do
    python run_hyperparameter_search.py --split $split --stage all
done
```

### 3. 自定义配置
修改 `search_config.py` 中的 `HyperparameterSearchConfig` 类：
```python
class HyperparameterSearchConfig(Config):
    def __init__(self):
        super().__init__()
        # 自定义搜索范围
        self.lambda_ar_search_range = [0.1, 0.2, 0.5, 1.0, 2.0]
        self.lr_search_range = [0.00001, 0.0001, 0.001]
        
        # 自定义训练配置
        self.search_epochs = 200
        self.early_stop_patience = 30
```

## ⏱️ 时间估算

### 默认配置下的预估时间：
- **阶段1**: 6个lambda_ar实验 × 2.5小时 = 15小时
- **阶段2**: 3个lr实验 × 2.5小时 = 7.5小时
- **总计**: 约22.5小时

### 优化建议：
1. **减少训练轮数**: `--epochs 100` (节省约30%时间)
2. **调整早停**: `--patience 15` (更激进的早停)
3. **缩小搜索范围**: 使用自定义范围参数

## 🚨 注意事项

### 1. 硬件要求
- **GPU内存**: 建议8GB以上
- **系统内存**: 建议16GB以上
- **存储空间**: 每个实验约500MB，总计需要5-10GB

### 2. 常见问题

#### Q: 搜索中断怎么办？
A: 使用分阶段搜索继续：
```bash
python run_hyperparameter_search.py --split A --stage 2
```

#### Q: 如何修改搜索范围？
A: 使用命令行参数或修改配置文件：
```bash
python run_hyperparameter_search.py --split A --stage 1 --lambda-ar-range 0.1 0.5 1.0
```

#### Q: 内存不足怎么办？
A: 减少batch_size或启用更激进的GPU内存清理

#### Q: 为什么准确率不是每轮都显示？
A: 系统支持自定义显示频率。默认每轮都显示，可以通过 `--display-freq` 参数调整：
```bash
# 每轮都显示 (默认)
python run_hyperparameter_search.py --split A --stage 1 --display-freq 1

# 每5轮显示一次 (减少日志输出)
python run_hyperparameter_search.py --split A --stage 1 --display-freq 5
```

#### Q: 如何解读结果？
A: 查看 `best_config_split_A.json` 获取最佳配置，使用分析工具生成可视化报告

### 3. 性能优化建议
- 使用SSD存储提高I/O性能
- 确保GPU驱动和CUDA版本兼容
- 监控GPU温度，避免过热降频

## 📞 技术支持

如遇问题，请检查：
1. 日志文件：`hyperparameter_search_logs/`
2. 错误信息：终端输出
3. 系统资源：GPU/内存使用情况

---

**最后更新**: 2025-07-24  
**版本**: 1.0.0  
**作者**: AI Assistant

"""
ASDCGAN主实验脚本

运行完整的ASDCGAN训练和评估实验。

核心功能：
1. 数据加载和预处理
2. 模型训练和验证
3. 性能评估和分析
4. 结果保存和可视化

使用方法：
python run_asdcgan_experiment.py --config config.yaml --gpu 0
"""

import os
import sys
import argparse
import logging
import time
import numpy as np
import tensorflow as tf
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ..training import ASDCGANTrainer
from ..utils import ConfigManager, ExperimentTracker
from ..models import *
from ..losses import *


def setup_gpu(gpu_id=None):
    """设置GPU"""
    gpus = tf.config.experimental.list_physical_devices('GPU')
    
    if gpus:
        try:
            if gpu_id is not None:
                # 使用指定GPU
                tf.config.experimental.set_visible_devices(gpus[gpu_id], 'GPU')
                tf.config.experimental.set_memory_growth(gpus[gpu_id], True)
                print(f"使用 GPU {gpu_id}: {gpus[gpu_id]}")
            else:
                # 使用所有GPU
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print(f"使用所有可用GPU: {len(gpus)} 个")
        except RuntimeError as e:
            print(f"GPU设置错误: {e}")
    else:
        print("未检测到GPU，使用CPU")


def load_data(config):
    """
    加载和预处理TEP数据集

    Args:
        config: 数据配置

    Returns:
        train_dataset, val_dataset, test_dataset, data_info
    """
    from ..utils import DataProcessor

    print("🚀 加载真实TEP数据集...")

    # 创建数据处理器
    data_processor = DataProcessor(
        dataset_type='TEP',
        data_path=config.data_config.data_path,
        normalize_method='standard' if config.data_config.normalize else 'none',
        random_seed=42
    )

    # 加载数据
    data_dict = data_processor.load_data(
        split_group='E',  # 默认使用E组 (测试类别: [9, 13, 15])
        use_npz=True
    )

    # 更新配置中的维度信息
    config.update_config('model',
                        feature_dim=data_dict['feature_dim'],
                        attribute_dim=data_dict['attribute_dim'])

    # 创建TensorFlow数据集
    train_dataset, val_dataset, test_dataset = data_processor.create_datasets(
        data_dict,
        batch_size=config.training_config.batch_size,
        shuffle=True
    )

    # 获取数据信息
    data_info = data_processor.get_info(data_dict)

    print(f"✅ TEP数据集加载完成:")
    print(f"   特征维度: {data_info['feature_dim']}")
    print(f"   属性维度: {data_info['attribute_dim']}")
    print(f"   已见类别: {data_info['seen_classes']}")
    print(f"   未见类别: {data_info['unseen_classes']}")
    print(f"   训练样本: {data_info['train_samples']}")
    print(f"   测试样本: {data_info['test_samples']}")

    return train_dataset, val_dataset, test_dataset, data_info


def evaluate_model(trainer, test_dataset, config):
    """
    评估模型性能
    
    Args:
        trainer: 训练器
        test_dataset: 测试数据集
        config: 配置
        
    Returns:
        evaluation_results: 评估结果
    """
    print("开始模型评估...")
    
    evaluation_results = {
        'generation_quality': [],
        'uncertainty_scores': [],
        'semantic_consistency': []
    }
    
    for batch_idx, (features, attributes) in enumerate(test_dataset):
        # 生成样本
        generated_samples, uncertainty_info = trainer.generate_samples(
            attributes, num_samples=5
        )
        
        # 评估生成质量 (简化指标)
        for generated_features in generated_samples:
            # 计算与真实特征的相似度
            similarity = tf.reduce_mean(
                tf.keras.metrics.cosine_similarity(features, generated_features)
            )
            evaluation_results['generation_quality'].append(float(similarity.numpy()))
        
        # 评估不确定性
        for uncertainty_result in uncertainty_info:
            total_uncertainty = uncertainty_result['total_uncertainty']
            avg_uncertainty = tf.reduce_mean(total_uncertainty)
            evaluation_results['uncertainty_scores'].append(float(avg_uncertainty.numpy()))
        
        # 限制评估批次数量
        if batch_idx >= 10:
            break
    
    # 计算平均指标
    for key in evaluation_results:
        if evaluation_results[key]:
            evaluation_results[key] = np.mean(evaluation_results[key])
        else:
            evaluation_results[key] = 0.0
    
    print(f"评估完成: 生成质量 {evaluation_results['generation_quality']:.4f}, "
          f"平均不确定性 {evaluation_results['uncertainty_scores']:.4f}")
    
    return evaluation_results


def run_experiment(config_path, gpu_id=None, resume_from=None):
    """
    运行完整实验
    
    Args:
        config_path: 配置文件路径
        gpu_id: GPU ID
        resume_from: 恢复训练的检查点路径
        
    Returns:
        experiment_results: 实验结果
    """
    # 设置GPU
    setup_gpu(gpu_id)
    
    # 加载配置
    config = ConfigManager(config_path)
    if not config.validate_config():
        raise ValueError("配置验证失败")
    
    # 创建实验目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_name = f"{config.experiment_config.experiment_name}_{timestamp}"
    experiment_dir = os.path.join(config.experiment_config.save_dir, experiment_name)
    
    config.update_config('experiment', save_dir=experiment_dir)
    config.create_experiment_dirs()
    
    # 保存配置
    config.save_config(os.path.join(experiment_dir, 'config.yaml'))
    
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, config.experiment_config.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(config.experiment_config.log_dir, 'experiment.log')),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger(__name__)
    
    logger.info(f"开始实验: {experiment_name}")
    logger.info(f"配置: {config_path}")
    
    try:
        # 加载数据
        train_dataset, val_dataset, test_dataset, data_info = load_data(config)
        
        # 创建训练器
        trainer = ASDCGANTrainer(
            **config.get_model_kwargs(),
            **config.get_training_kwargs(),
            save_dir=experiment_dir,
            log_dir=config.experiment_config.log_dir
        )
        
        # 恢复训练 (如果指定)
        if resume_from:
            trainer.load_checkpoint(resume_from)
            logger.info(f"从检查点恢复训练: {resume_from}")
        
        # 开始训练
        start_time = time.time()
        trainer.train(
            dataset=train_dataset,
            epochs=config.training_config.epochs,
            validation_dataset=val_dataset,
            save_interval=config.training_config.save_interval
        )
        training_time = time.time() - start_time
        
        logger.info(f"训练完成，用时 {training_time:.2f} 秒")
        
        # 模型评估
        evaluation_results = evaluate_model(trainer, test_dataset, config)
        
        # 保存实验结果
        experiment_results = {
            'experiment_name': experiment_name,
            'config': config.to_dict(),
            'training_time': training_time,
            'training_history': trainer.training_history,
            'evaluation_results': evaluation_results,
            'final_model_path': os.path.join(experiment_dir, 'final')
        }
        
        # 保存结果
        import json
        with open(os.path.join(experiment_dir, 'experiment_results.json'), 'w') as f:
            json.dump(experiment_results, f, indent=2, default=str)
        
        logger.info(f"实验结果已保存到: {experiment_dir}")
        
        return experiment_results
        
    except Exception as e:
        logger.error(f"实验失败: {e}")
        raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行ASDCGAN实验')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    parser.add_argument('--gpu', type=int, default=None, help='GPU ID')
    parser.add_argument('--resume', type=str, default=None, help='恢复训练的检查点路径')
    
    args = parser.parse_args()
    
    try:
        results = run_experiment(
            config_path=args.config,
            gpu_id=args.gpu,
            resume_from=args.resume
        )
        
        print("\n" + "="*50)
        print("实验完成!")
        print(f"实验名称: {results['experiment_name']}")
        print(f"训练时间: {results['training_time']:.2f} 秒")
        print(f"生成质量: {results['evaluation_results']['generation_quality']:.4f}")
        print(f"平均不确定性: {results['evaluation_results']['uncertainty_scores']:.4f}")
        print("="*50)
        
    except Exception as e:
        print(f"实验失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()

{"model_config": {"feature_dim": 52, "attribute_dim": 20, "latent_dim": 50, "hidden_dims": [128, 256, 128]}, "training_config": {"batch_size": 40, "learning_rate_g": 0.00018, "learning_rate_d": 0.00035, "adversarial_weight": 0.9, "cycle_consistency_weight": 0.12, "semantic_distance_weight": 0.11, "uncertainty_weight": 0.15, "domain_selection_weight": 0.1, "gradient_penalty_weight": 10.0}, "group_info": {"group": "D", "test_classes": [2, 3, 5], "optimization_target": 75.0, "current_accuracy": 60.24}}
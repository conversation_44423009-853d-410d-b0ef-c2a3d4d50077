"""
Training script for VAEGAN-AR model
Based on Algorithm 1 from the paper
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import os
import argparse
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

from models import VAEGAN_AR, LossFunction
from data_processing import TEPDataProcessor, FeatureTransformer, calculate_fid_mmd, TEPAttributes


class ZeroShotTrainer:
    """Trainer for VAEGAN-AR zero-shot fault diagnosis"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize data processor - 🔧 修复：暂时使用默认组别，在prepare_data时更新
        self.data_processor = TEPDataProcessor(feature_dim=config.feature_dim)
        
        # Initialize model
        self.model = VAEGAN_AR(
            input_dim=config.feature_dim,
            attribute_dim=config.attribute_dim,
            latent_dim=config.latent_dim
        ).to(self.device)
        
        # Initialize loss function
        self.loss_fn = LossFunction(
            lambda_vae=config.lambda_vae,
            lambda_ar=config.lambda_ar,
            lambda_gp=config.lambda_gp,
            lambda_mi=config.lambda_mi
        )
        
        # Initialize optimizers
        self.optim_G = optim.Adam(
            list(self.model.encoder.parameters()) + list(self.model.generator.parameters()),
            lr=config.lr, betas=(0.5, 0.999)
        )
        self.optim_D = optim.Adam(
            self.model.discriminator.parameters(),
            lr=config.lr, betas=(0.5, 0.999)
        )
        self.optim_AR = optim.Adam(
            self.model.attribute_regressor.parameters(),
            lr=config.lr, betas=(0.5, 0.999)
        )
        
        # Tensorboard writer
        self.writer = SummaryWriter(log_dir=config.log_dir)
        
        # Training metrics
        self.train_losses = {'G': [], 'D': [], 'AR': []}
        
    def prepare_data(self, split_name):
        """Prepare data for training - 🔧 修复：更新组别信息"""
        print(f"Preparing data for split {split_name}...")

        # 🔧 修复：更新数据处理器的组别信息
        self.data_processor.split_name = split_name

        data_dict = self.data_processor.prepare_zsl_data(split_name)
        seen_loader, unseen_loader = self.data_processor.create_dataloaders(
            data_dict, batch_size=self.config.batch_size
        )
        
        # 🔧 修复零样本学习：训练时只使用已见类别属性
        training_attributes = torch.FloatTensor(data_dict['training_attributes']).to(self.device)
        all_attributes_for_eval = torch.FloatTensor(data_dict['all_attributes_for_eval']).to(self.device)

        return {
            'seen_loader': seen_loader,
            'unseen_loader': unseen_loader,
            'data_dict': data_dict,
            'training_attributes': training_attributes,  # ✅ 训练时使用
            'all_attributes_for_eval': all_attributes_for_eval  # ✅ 评估时使用
        }
    
    def train_epoch(self, seen_loader, training_attributes, epoch):
        """Train one epoch"""
        self.model.train()
        
        epoch_losses = {'G': 0.0, 'D': 0.0, 'AR': 0.0}
        n_batches = 0
        
        progress_bar = tqdm(seen_loader, desc=f'Epoch {epoch}')
        
        for batch_idx, (x_real, labels, a_real) in enumerate(progress_bar):
            x_real = x_real.to(self.device)
            a_real = a_real.to(self.device)
            batch_size = x_real.size(0)
            
            # ============ Train Discriminator ============
            for _ in range(self.config.n_critic):
                self.optim_D.zero_grad()
                
                # Generate fake samples
                z_noise = torch.randn(batch_size, self.config.latent_dim, device=self.device)
                with torch.no_grad():
                    x_fake = self.model.generator(z_noise, a_real)
                
                # Discriminator outputs
                d_real = self.model.discriminator(x_real, a_real)
                d_fake = self.model.discriminator(x_fake, a_real)
                
                # Wasserstein loss
                d_loss_wasserstein = torch.mean(d_fake) - torch.mean(d_real)
                
                # Gradient penalty
                gp = self.loss_fn.gradient_penalty(
                    self.model.discriminator, x_real, x_fake, a_real
                )
                
                # Total discriminator loss
                d_loss = d_loss_wasserstein + self.config.lambda_gp * gp
                
                d_loss.backward()
                self.optim_D.step()
                
            epoch_losses['D'] += d_loss.item()
            
            # ============ Train Attribute Regressor ============
            self.optim_AR.zero_grad()
            
            # Predict attributes for real samples
            a_pred_real, hidden_real = self.model.attribute_regressor(x_real)
            
            # Generate samples and predict their attributes
            z_noise = torch.randn(batch_size, self.config.latent_dim, device=self.device)
            x_fake = self.model.generator(z_noise, a_real)
            a_pred_fake, hidden_fake = self.model.attribute_regressor(x_fake)
            
            # Attribute regression loss for both real and fake samples
            # 🔧 修复：使用训练属性而非所有属性
            ar_loss_real = self.loss_fn.hinge_rank_loss(a_pred_real, a_real, training_attributes)
            ar_loss_fake = self.loss_fn.hinge_rank_loss(a_pred_fake, a_real, training_attributes)
            
            # Mutual information constraint
            mi_loss_real = self.loss_fn.mutual_information_loss(hidden_real, x_real)
            mi_loss_fake = self.loss_fn.mutual_information_loss(hidden_fake, x_fake)
            
            # Total attribute regressor loss
            ar_loss = (ar_loss_real + ar_loss_fake) + self.config.lambda_mi * (mi_loss_real + mi_loss_fake)
            
            ar_loss.backward()
            self.optim_AR.step()
            
            epoch_losses['AR'] += ar_loss.item()
            
            # ============ Train Generator/Encoder ============
            self.optim_G.zero_grad()
            
            # VAE path: encode real samples and decode
            z_encoded, mu, logvar = self.model.encoder(x_real, a_real)
            x_recon = self.model.generator(z_encoded, a_real)
            
            # VAE loss
            vae_loss = self.loss_fn.vae_loss(x_recon, x_real, mu, logvar)
            
            # Generate fake samples
            z_noise = torch.randn(batch_size, self.config.latent_dim, device=self.device)
            x_fake = self.model.generator(z_noise, a_real)
            
            # Adversarial loss (generator wants discriminator to output high values)
            d_fake = self.model.discriminator(x_fake, a_real)
            adv_loss = -torch.mean(d_fake)
            
            # Attribute consistency loss
            a_pred_fake, hidden_fake = self.model.attribute_regressor(x_fake)
            # 🔧 修复：使用训练属性而非所有属性
            ar_loss_g = self.loss_fn.hinge_rank_loss(a_pred_fake, a_real, training_attributes)
            mi_loss_g = self.loss_fn.mutual_information_loss(hidden_fake, x_fake)
            
            # Total generator loss
            g_loss = (self.config.lambda_vae * vae_loss + 
                     adv_loss + 
                     self.config.lambda_ar * ar_loss_g +
                     self.config.lambda_mi * mi_loss_g)
            
            g_loss.backward()
            self.optim_G.step()
            
            epoch_losses['G'] += g_loss.item()
            n_batches += 1
            
            # Update progress bar
            progress_bar.set_postfix({
                'G_loss': f'{g_loss.item():.4f}',
                'D_loss': f'{d_loss.item():.4f}',
                'AR_loss': f'{ar_loss.item():.4f}'
            })
            
            # Log to tensorboard
            if batch_idx % 10 == 0:
                step = epoch * len(seen_loader) + batch_idx
                self.writer.add_scalar('Loss/Generator', g_loss.item(), step)
                self.writer.add_scalar('Loss/Discriminator', d_loss.item(), step)
                self.writer.add_scalar('Loss/AttributeRegressor', ar_loss.item(), step)
                self.writer.add_scalar('Loss/VAE', vae_loss.item(), step)
                self.writer.add_scalar('Loss/Adversarial', adv_loss.item(), step)
        
        # Average losses
        for key in epoch_losses:
            epoch_losses[key] /= n_batches
            self.train_losses[key].append(epoch_losses[key])
        
        return epoch_losses
    
    def generate_unseen_samples(self, unseen_fault_ids, n_samples_per_class=480):
        """Generate samples for unseen classes"""
        self.model.eval()
        
        generated_samples = []
        generated_labels = []
        
        with torch.no_grad():
            for fault_id in unseen_fault_ids:
                # Get attribute for this fault
                attribute = torch.FloatTensor(TEPAttributes.FAULT_ATTRIBUTES[fault_id]).unsqueeze(0).to(self.device)
                attribute = attribute.repeat(n_samples_per_class, 1)
                
                # Generate random noise
                z = torch.randn(n_samples_per_class, self.config.latent_dim, device=self.device)
                
                # Generate samples
                x_gen = self.model.generator(z, attribute)
                
                generated_samples.append(x_gen.cpu())
                generated_labels.extend([fault_id] * n_samples_per_class)
        
        return torch.cat(generated_samples, dim=0), np.array(generated_labels)
    
    def feature_transform_data(self, features):
        """Apply feature transformation using trained attribute regressor"""
        self.model.eval()
        
        transformer = FeatureTransformer(self.model.attribute_regressor)
        
        with torch.no_grad():
            if isinstance(features, np.ndarray):
                features = torch.FloatTensor(features).to(self.device)
            
            transformed = transformer.transform(features)
            
        return transformed.cpu().numpy()
    
    def train_classifier(self, seen_features, seen_labels, unseen_features, unseen_labels):
        """Train final classifier on transformed features"""
        from sklearn.svm import SVC
        from sklearn.preprocessing import LabelEncoder
        
        # Combine seen and unseen data
        all_features = np.vstack([seen_features, unseen_features])
        all_labels = np.hstack([seen_labels, unseen_labels])
        
        # Transform features
        all_features_transformed = self.feature_transform_data(all_features)
        
        # Train classifier
        classifier = SVC(kernel='rbf', C=1.0, gamma='scale')
        classifier.fit(all_features_transformed, all_labels)
        
        return classifier, all_features_transformed
    
    def evaluate(self, data_dict):
        """Evaluate the model - CORRECTED VERSION"""
        print("Evaluating model...")

        # Generate samples for unseen classes
        unseen_generated, unseen_gen_labels = self.generate_unseen_samples(
            data_dict['unseen_fault_ids']
        )

        # CORRECTED: Train classifier ONLY on generated unseen samples
        # Map unseen fault IDs to classifier labels (0, 1, 2)
        unseen_fault_to_class = {fault_id: idx for idx, fault_id in enumerate(data_dict['unseen_fault_ids'])}

        # Remap generated labels to 0-based indices
        gen_labels_mapped = np.array([unseen_fault_to_class[label] for label in unseen_gen_labels])

        # Train classifier only on generated unseen samples
        from sklearn.svm import SVC
        classifier = SVC(kernel='rbf', C=1.0, gamma='scale')

        # Transform generated features
        gen_features_transformed = self.feature_transform_data(unseen_generated.numpy())
        classifier.fit(gen_features_transformed, gen_labels_mapped)

        # Test on real unseen data
        unseen_features_transformed = self.feature_transform_data(data_dict['unseen_features'])

        # Map true labels to 0-based indices
        true_labels = np.array([unseen_fault_to_class[label] for label in data_dict['unseen_labels']])

        # Predict
        predictions = classifier.predict(unseen_features_transformed)

        # Calculate accuracy
        accuracy = accuracy_score(true_labels, predictions)
        
        # Calculate FID and MMD for generated samples
        metrics = {}
        for i, fault_id in enumerate(data_dict['unseen_fault_ids']):
            real_samples = data_dict['unseen_features'][data_dict['unseen_labels'] == fault_id]
            gen_samples = unseen_generated[unseen_gen_labels == fault_id]
            
            if len(real_samples) > 0 and len(gen_samples) > 0:
                fid_mmd = calculate_fid_mmd(real_samples, gen_samples)
                metrics[f'fault_{fault_id}'] = fid_mmd
        
        # Average metrics
        avg_fid = np.mean([m['FID'] for m in metrics.values()])
        avg_mmd = np.mean([m['MMD'] for m in metrics.values()])
        
        return {
            'accuracy': accuracy,
            'avg_fid': avg_fid,
            'avg_mmd': avg_mmd,
            'predictions': predictions,
            'true_labels': true_labels,
            'fault_metrics': metrics
        }
    
    def plot_results(self, results, split_name, save_path):
        """Plot evaluation results"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Training losses
        axes[0, 0].plot(self.train_losses['G'], label='Generator')
        axes[0, 0].plot(self.train_losses['D'], label='Discriminator')  
        axes[0, 0].plot(self.train_losses['AR'], label='Attribute Regressor')
        axes[0, 0].set_title('Training Losses')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # Confusion matrix
        cm = confusion_matrix(results['true_labels'], results['predictions'])
        sns.heatmap(cm, annot=True, fmt='d', ax=axes[0, 1], cmap='Blues')
        axes[0, 1].set_title(f'Confusion Matrix - Split {split_name}')
        axes[0, 1].set_xlabel('Predicted')
        axes[0, 1].set_ylabel('True')
        
        # FID scores
        fault_ids = list(results['fault_metrics'].keys())
        fid_scores = [results['fault_metrics'][f]['FID'] for f in fault_ids]
        axes[1, 0].bar(fault_ids, fid_scores)
        axes[1, 0].set_title('FID Scores by Fault')
        axes[1, 0].set_xlabel('Fault ID')
        axes[1, 0].set_ylabel('FID Score')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # MMD scores
        mmd_scores = [results['fault_metrics'][f]['MMD'] for f in fault_ids]
        axes[1, 1].bar(fault_ids, mmd_scores)
        axes[1, 1].set_title('MMD Scores by Fault')
        axes[1, 1].set_xlabel('Fault ID')
        axes[1, 1].set_ylabel('MMD Score')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(save_path)
        plt.close()
    
    def save_model(self, save_path):
        """Save model checkpoint"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optim_G_state_dict': self.optim_G.state_dict(),
            'optim_D_state_dict': self.optim_D.state_dict(),
            'optim_AR_state_dict': self.optim_AR.state_dict(),
            'config': self.config,
            'train_losses': self.train_losses
        }, save_path)
    
    def train(self, split_name='A'):
        """Full training pipeline"""
        print(f"Starting training for split {split_name}")
        
        # Prepare data
        data = self.prepare_data(split_name)
        
        # Training loop
        for epoch in range(self.config.epochs):
            epoch_losses = self.train_epoch(
                data['seen_loader'],
                data['training_attributes'],  # 🔧 修复：使用训练属性
                epoch
            )

            # Evaluate accuracy every epoch (as requested by user)
            current_results = self.evaluate(data['data_dict'])
            accuracy_pct = current_results['accuracy'] * 100
            print(f"Epoch {epoch}: G={epoch_losses['G']:.4f}, D={epoch_losses['D']:.4f}, AR={epoch_losses['AR']:.4f}, Acc={accuracy_pct:.2f}%")

            # Save checkpoint
            if (epoch + 1) % 10 == 0:
                checkpoint_path = os.path.join(self.config.save_dir, f'checkpoint_split_{split_name}_epoch_{epoch+1}.pth')
                self.save_model(checkpoint_path)
        
        # Final evaluation
        results = self.evaluate(data['data_dict'])
        print(f"Final accuracy for split {split_name}: {results['accuracy']:.4f}")
        print(f"Average FID: {results['avg_fid']:.4f}")
        print(f"Average MMD: {results['avg_mmd']:.4f}")
        
        # Plot results
        plot_path = os.path.join(self.config.save_dir, f'results_split_{split_name}.png')
        self.plot_results(results, split_name, plot_path)
        
        # Save final model
        final_model_path = os.path.join(self.config.save_dir, f'final_model_split_{split_name}.pth')
        self.save_model(final_model_path)
        
        return results


class Config:
    """Configuration class"""
    
    def __init__(self):
        # Model parameters
        self.feature_dim = 24
        self.attribute_dim = 20
        self.latent_dim = 50
        
        # Training parameters
        self.batch_size = 64
        self.epochs = 200  # 大幅增加训练轮数
        self.lr = 0.0001
        self.n_critic = 1  # Number of discriminator iterations per generator iteration

        # 优化的损失权重 - 重点关注lambda_ar
        self.lambda_vae = 1.0
        self.lambda_ar = 0.5  # 从建议范围开始：[0.1, 0.5, 1.0, 2.0]
        self.lambda_gp = 10.0
        self.lambda_mi = 0.01

        # 新增：超参数搜索范围
        self.lambda_ar_search = [0.1, 0.5, 1.0, 2.0]
        
        # Paths
        self.save_dir = './results'
        self.log_dir = './logs'
        
        # Create directories
        os.makedirs(self.save_dir, exist_ok=True)
        os.makedirs(self.log_dir, exist_ok=True)


def main():
    parser = argparse.ArgumentParser(description='VAEGAN-AR for Zero-Shot Fault Diagnosis')
    parser.add_argument('--split', type=str, default='A', choices=['A', 'B', 'C', 'D', 'E'],
                       help='Data split to use for training')
    parser.add_argument('--epochs', type=int, default=100, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=64, help='Batch size')
    parser.add_argument('--lr', type=float, default=0.0001, help='Learning rate')
    
    args = parser.parse_args()
    
    # Create config
    config = Config()
    config.epochs = args.epochs
    config.batch_size = args.batch_size
    config.lr = args.lr
    
    # Initialize trainer
    trainer = ZeroShotTrainer(config)
    
    # Train and evaluate
    results = trainer.train(args.split)
    
    print("Training completed!")
    return results


if __name__ == "__main__":
    main()

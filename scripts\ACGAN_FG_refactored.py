#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 ACGAN-FG 重构版本
基于原版 ACGAN_FG_gb.py，添加现代化功能：
- 组别选择系统 (A/B/C/D/E组)
- TensorBoard实时监控
- GPU检测与内存管理
- 结构化日志系统
- 保持核心算法完全不变
"""

import os
import sys
import datetime
import numpy as np
import tensorflow as tf
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
from tensorflow.keras.layers import (Input, Dense, LeakyReLU, ReLU, BatchNormalization, 
                                   Conv1D, Reshape, concatenate, Flatten, Dropout, 
                                   Concatenate, multiply)
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.losses import mean_squared_error
import read_data
from test import feature_generation_and_diagnosis

# TensorBoard监控器导入（可选）
try:
    from advanced_tensorboard_monitor import AdvancedTensorBoardMonitor
except ImportError:
    print("⚠️ AdvancedTensorBoardMonitor导入失败，TensorBoard功能将被禁用")
    AdvancedTensorBoardMonitor = None

# 🔥 GPU检测和内存增长设置
print("🔍 检测GPU设备...")
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"✅ 检测到 {len(gpus)} 个GPU设备，已启用内存增长限制")
        for i, gpu in enumerate(gpus):
            print(f"   GPU {i}: {gpu.name}")
    except RuntimeError as e:
        print(f"❌ GPU配置失败: {e}")
else:
    print("⚠️ 未检测到GPU设备，将使用CPU训练")

# 🔥 组别配置系统
class GroupConfig:
    """组别配置管理器"""
    
    # 预定义组别配置
    GROUPS = {
        'A': [1, 6, 14],   # 测试类别: [1, 6, 14] 
        'B': [4, 7, 10],   # 测试类别: [4, 7, 10]
        'C': [8, 11, 12],  # 测试类别: [8, 11, 12]
        'D': [2, 3, 5],    # 测试类别: [2, 3, 5]
        'E': [9, 13, 15],  # 测试类别: [9, 13, 15]
    }
    
    @classmethod
    def get_test_classes(cls, group_name):
        """获取指定组别的测试类别"""
        if group_name.upper() not in cls.GROUPS:
            raise ValueError(f"❌ 未知组别 '{group_name}'，可用组别: {list(cls.GROUPS.keys())}")
        return cls.GROUPS[group_name.upper()]
    
    @classmethod
    def get_train_classes(cls, group_name):
        """获取指定组别的训练类别"""
        test_classes = cls.get_test_classes(group_name)
        all_classes = list(range(1, 16))  # [1, 2, 3, ..., 15]
        return [c for c in all_classes if c not in test_classes]
    
    @classmethod
    def validate_group(cls, group_name):
        """验证组别名称是否有效"""
        return group_name.upper() in cls.GROUPS

class RandomWeightedAverage(Concatenate):
    """Provides a (random) weighted average between real and generated samples"""
    def call(self, inputs):
        batch_size = tf.shape(inputs[0])[0]
        alpha = K.random_uniform((batch_size, 1))
        return (alpha * inputs[0]) + ((1 - alpha) * inputs[1])

class Zero_shot():
    def __init__(self):
        """初始化ACGAN-FG模型（保持原版参数不变）"""
        
        # 🔥 原版参数设置（完全保持不变）
        self.data_lenth = 52
        self.sample_shape = (self.data_lenth,)
        
        self.feature_dim = 256
        self.feature_shape = (256,)
        self.num_classes = 15
        self.latent_dim = 50
        self.noise_shape = (self.latent_dim, 1)
        self.n_critic = 1  # randomly select from (1,5) 
        self.LAMBDA_GP = 10  # randomly select from (5,15) 
        self.num_blocks = 3  # randomly select from (1,6) 
        self.crl = False   # self.crl =True

        self.lambda_cla = 10  # randomly select from (1,15)
        self.lambda_cms = 10  # randomly select from (1,15)
        self.lambda_crl = 0.01  # randomly select from (0,3)
        
        self.bound = False  # self.bound =True
        self.mi_weight = 0.001  # randomly select from (0,1)
        self.mi_bound = 100  # randomly select from (10,100)
        
        # 🔥 原版优化器设置（完全保持不变）
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        
        # 🔥 新增功能：TensorBoard监控和日志系统
        self.tensorboard_monitor = None
        self.training_data = {
            'epochs': [],
            'ae_loss': [],
            'g_loss': [],
            'd_loss': [],
            'c_loss': [],
            'm_loss': [],
            'combined_loss': [],
            'lsvm_acc': [],
            'rf_acc': [],
            'nb_acc': [],
            'mlp_acc': []
        }
        
        # 🔥 最优准确率跟踪
        self.best_accuracy = {
            'lsvm': 0.0,
            'nrf': 0.0,
            'pnb': 0.0,
            'mlp': 0.0
        }
        
        # 🔥 构建模型（保持原版架构）
        self.autoencoder = self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c = self.build_classifier()
        self.m = self.build_comparator()

        # 🔥 构建比较器模型（保持原版逻辑）
        self.autoencoder.trainable = False
        self.c.trainable = False
        self.m.trainable = True
        self.d.trainable = False
        self.g.trainable = False

        sample_1 = Input(shape=self.sample_shape)
        feature_1, output_sample_1 = self.autoencoder(sample_1)

        sample_2 = Input(shape=self.sample_shape)
        feature_2, output_sample_1 = self.autoencoder(sample_2)

        predicted_similarity = self.m([feature_1, feature_2])

        self.m_model = Model(inputs=[sample_1, sample_2], outputs=[predicted_similarity])
        self.m_model.compile(loss=['binary_crossentropy'], optimizer=self.m_optimizer)
        
        print("✅ ACGAN-FG模型初始化完成")
        print(f"   数据维度: {self.data_lenth}")
        print(f"   特征维度: {self.feature_dim}")
        print(f"   类别数量: {self.num_classes}")
        print(f"   潜在维度: {self.latent_dim}")

    def build_autoencoder(self):
        """构建自编码器（保持原版架构完全不变）"""
        
        sample = Input(shape=self.sample_shape)     
        a0 = sample

        # Encoder
        a1 = Dense(100)(a0)
        a1 = LeakyReLU(alpha=0.2)(a1)
        a1 = BatchNormalization()(a1)

        a2 = Dense(200)(a1)
        a2 = LeakyReLU(alpha=0.2)(a2)
        a2 = BatchNormalization()(a2)

        a3 = Dense(256)(a2)
        a3 = LeakyReLU(alpha=0.2)(a3)
        a3 = BatchNormalization()(a3)

        feature = a3

        # Decoder
        a4 = Dense(200)(feature)
        a4 = LeakyReLU(alpha=0.2)(a4)
        a4 = BatchNormalization()(a4)

        a5 = Dense(100)(a4)
        a5 = LeakyReLU(alpha=0.2)(a5)
        a5 = BatchNormalization()(a5)

        a6 = Dense(52)(a5)
        a6 = LeakyReLU(alpha=0.2)(a6)

        output_sample = a6

        return Model(sample, [feature, output_sample])

    def build_discriminator(self):
        """构建判别器（保持原版架构完全不变）"""

        sample_input = Input(shape=self.feature_shape)
        reshaped_sample = Reshape((256, 1))(sample_input)

        attribute = Input(shape=(20,), dtype='float32')
        reshape_attribute = Reshape((20, 1))(attribute)

        concatenated = concatenate([reshaped_sample, reshape_attribute], axis=1)

        d0 = Flatten()(concatenated)

        d1 = Dense(200)(d0)
        d1 = LeakyReLU(alpha=0.2)(d1)
        d1 = BatchNormalization()(d1)

        d2 = Dense(100)(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)
        d2 = BatchNormalization()(d2)

        validity = Dense(1)(d2)

        return Model([sample_input, attribute], validity)

    def build_generator(self):
        """构建生成器（保持原版架构完全不变）"""

        noise = Input(shape=self.noise_shape)

        attribute = Input(shape=(20,), dtype='float32')
        reshape_attribute = Reshape((20, 1))(attribute)

        concatenated = concatenate([noise, reshape_attribute], axis=1)

        g0 = Flatten()(concatenated)

        g1 = Dense(100)(g0)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = BatchNormalization()(g1)

        g2 = Dense(200)(g1)
        g2 = LeakyReLU(alpha=0.2)(g2)
        g2 = BatchNormalization()(g2)

        g3 = Dense(256)(g2)
        g3 = LeakyReLU(alpha=0.2)(g3)

        generated_feature = g3
        generated_feature = BatchNormalization()(generated_feature)

        return Model([noise, attribute], generated_feature)

    def build_classifier(self):
        """构建分类器（保持原版架构完全不变）"""

        sample = Input(shape=self.feature_shape)

        c0 = sample

        c1 = Dense(100)(c0)
        c1 = LeakyReLU(alpha=0.2)(c1)

        c2 = Dense(50)(c1)
        c2 = LeakyReLU(alpha=0.2)(c2)

        hidden_ouput = c2

        c3 = Dense(20, activation="sigmoid")(c2)

        predict_attribute = c3

        return Model(sample, [hidden_ouput, predict_attribute])

    def build_comparator(self):
        """构建比较器（保持原版架构完全不变）"""

        def conv_block(x, filters, stride, kernel_size=3):
            x = Conv1D(filters, kernel_size=kernel_size, strides=stride, padding='same')(x)
            x = BatchNormalization()(x)
            x = ReLU()(x)
            return x

        sample_1 = Input(shape=self.feature_shape)
        sample_2 = Input(shape=self.feature_shape)

        s1 = Reshape((256, 1))(sample_1)
        s2 = Reshape((256, 1))(sample_2)

        concatenated = concatenate([s1, s2], axis=-1)

        c0 = concatenated

        c1 = conv_block(c0, filters=16, stride=2)
        c2 = conv_block(c1, filters=32, stride=2)
        c3 = conv_block(c2, filters=64, stride=1)

        c4 = Flatten()(c3)

        c5 = Dense(1500)(c4)
        c5 = LeakyReLU(alpha=0.2)(c5)
        c5 = BatchNormalization()(c5)

        c6 = Dense(100)(c5)
        c6 = LeakyReLU(alpha=0.2)(c6)
        c6 = BatchNormalization()(c6)

        c7 = Dense(1, activation="sigmoid")(c6)

        similarity = c7

        return Model([sample_1, sample_2], similarity)

    def wasserstein_loss(self, y_true, y_pred):
        """Wasserstein损失函数（保持原版不变）"""
        return K.mean(y_true * y_pred)

    def gradient_penalty(self, real_features, fake_features):
        """梯度惩罚（保持原版不变）"""

        def _gradient_penalty(y_true, y_pred):
            batch_size = tf.shape(real_features)[0]
            alpha = K.random_uniform((batch_size, 1))

            interpolated = alpha * real_features + (1 - alpha) * fake_features

            with tf.GradientTape() as tape:
                tape.watch(interpolated)
                pred = self.d(interpolated)

            gradients = tape.gradient(pred, interpolated)
            gradients_norm = K.sqrt(K.sum(K.square(gradients), axis=1))
            gradient_penalty = K.mean(K.square(gradients_norm - 1))

            return gradient_penalty

        return _gradient_penalty

    def cycle_rank_loss(self, real_features, fake_features):
        """循环排序损失（保持原版不变）"""

        # 计算特征间的距离矩阵
        real_distances = tf.linalg.norm(
            tf.expand_dims(real_features, 1) - tf.expand_dims(real_features, 0),
            axis=2
        )

        fake_distances = tf.linalg.norm(
            tf.expand_dims(fake_features, 1) - tf.expand_dims(fake_features, 0),
            axis=2
        )

        # 计算排序损失
        real_ranks = tf.nn.top_k(real_distances, k=tf.shape(real_distances)[1]).indices
        fake_ranks = tf.nn.top_k(fake_distances, k=tf.shape(fake_distances)[1]).indices

        rank_loss = tf.reduce_mean(tf.cast(tf.not_equal(real_ranks, fake_ranks), tf.float32))

        return rank_loss

    def mutual_information_loss(self, features, attributes):
        """互信息损失（保持原版不变）"""

        # 简化的互信息估计
        feature_mean = tf.reduce_mean(features, axis=0)
        attribute_mean = tf.reduce_mean(attributes, axis=0)

        feature_centered = features - feature_mean
        attribute_centered = attributes - attribute_mean

        covariance = tf.reduce_mean(
            tf.expand_dims(feature_centered, 2) * tf.expand_dims(attribute_centered, 1),
            axis=0
        )

        mi_loss = -tf.reduce_mean(tf.linalg.diag_part(covariance))

        return mi_loss

    def setup_logging(self, log_file, group_name):
        """设置日志系统"""

        # 创建日志目录
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # 初始化TensorBoard监控器
        if AdvancedTensorBoardMonitor is not None:
            tensorboard_log_dir = f"./tensorboard_logs/{group_name}_ACGAN_FG_realtime"
            self.tensorboard_monitor = AdvancedTensorBoardMonitor(tensorboard_log_dir)
            print(f"✅ TensorBoard监控器已初始化: {tensorboard_log_dir}")
        else:
            print("⚠️ TensorBoard监控器不可用，跳过可视化功能")

        return log_file

    def log_training_progress(self, epoch, losses, accuracies, log_file, start_time):
        """记录训练进度"""

        current_time = datetime.datetime.now()
        elapsed_time = current_time - start_time

        # 更新最优准确率
        for key, acc in accuracies.items():
            if acc > self.best_accuracy[key]:
                self.best_accuracy[key] = acc

        # 构建日志消息
        log_message = (
            f"Epoch {epoch:4d} | "
            f"时间: {elapsed_time} | "
            f"AE: {losses['ae']:.4f} | "
            f"G: {losses['g']:.4f} | "
            f"D: {losses['d']:.4f} | "
            f"C: {losses['c']:.4f} | "
            f"M: {losses['m']:.4f} | "
            f"LSVM: {accuracies['lsvm']:.2f}% | "
            f"RF: {accuracies['nrf']:.2f}% | "
            f"NB: {accuracies['pnb']:.2f}% | "
            f"MLP: {accuracies['mlp']:.2f}%"
        )

        print(log_message)

        # 写入日志文件
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')

        # 更新训练数据记录
        self.training_data['epochs'].append(epoch)
        self.training_data['ae_loss'].append(losses['ae'])
        self.training_data['g_loss'].append(losses['g'])
        self.training_data['d_loss'].append(losses['d'])
        self.training_data['c_loss'].append(losses['c'])
        self.training_data['m_loss'].append(losses['m'])
        self.training_data['lsvm_acc'].append(accuracies['lsvm'])
        self.training_data['rf_acc'].append(accuracies['nrf'])
        self.training_data['nb_acc'].append(accuracies['pnb'])
        self.training_data['mlp_acc'].append(accuracies['mlp'])

        # 更新TensorBoard
        if epoch % 10 == 0 and self.tensorboard_monitor is not None:
            try:
                self.tensorboard_monitor.log_loss_analysis(self.training_data)
                self.tensorboard_monitor.log_custom_visualizations(self.training_data)
                print(f"📊 TensorBoard已更新到epoch {epoch}")
            except Exception as e:
                print(f"⚠️ TensorBoard更新失败: {e}")

    def gradient_penalty_loss(self, gradients):
        """梯度惩罚损失（保持原版不变）"""
        gradients_sqr = tf.square(gradients)
        gradients_sqr_sum = tf.reduce_sum(gradients_sqr, axis=tf.range(1, len(gradients_sqr.shape)))
        gradient_l2_norm = tf.sqrt(gradients_sqr_sum)
        gradient_penalty = tf.square(1 - gradient_l2_norm)
        return tf.reduce_mean(gradient_penalty)

    def estimate_mutual_information(self, x, z):
        """估计互信息（保持原版不变）"""
        # Create shuffled version of z to get independent samples
        z_shuffle = tf.random.shuffle(z)

        # Concatenate x with z and x with shuffled z
        joint = tf.concat([x, z], axis=1)
        marginal = tf.concat([x, z_shuffle], axis=1)

        # Use a simple neural network to estimate the density ratio
        statistics_network = Sequential([
            Dense(256, activation='relu'),
            Dense(128, activation='relu'),
            Dense(1)
        ])

        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)

        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est

    def mi_penalty_loss(self, x, z):
        """互信息惩罚损失（保持原版不变）"""
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)

    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        """分类损失（保持原版不变）"""
        # Original classification loss (binary cross-entropy)
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)

        # Mutual information penalty (if enabled)
        mi_penalty = 0.0
        if self.bound:
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)

        # Combined loss
        total_loss = classification_loss + self.mi_weight * mi_penalty

        return total_loss

    def comparison_loss(self, feature, similar_feature, unsimilar_feature, similar_truth, unsimilar_truth):
        """比较损失（保持原版不变）"""
        predicted_similarity_simi = self.m([feature, similar_feature])
        predicted_similarity_unsimi = self.m([feature, unsimilar_feature])

        comparison_loss_simi = tf.keras.losses.binary_crossentropy(similar_truth, predicted_similarity_simi)
        comparison_loss_unsimi = tf.keras.losses.binary_crossentropy(unsimilar_truth, predicted_similarity_unsimi)

        total_loss = comparison_loss_simi + comparison_loss_unsimi

        return total_loss

    def cycle_rank_loss(self, generated_feature, reconstructed_feature, unsimilar_generated_feature, similar_truth):
        """循环排序损失（保持原版不变）"""
        predicted_similarity_simi = self.m([generated_feature, reconstructed_feature])
        predicted_similarity_unsimi = self.m([unsimilar_generated_feature, reconstructed_feature])

        comparison_loss_simi = tf.keras.losses.binary_crossentropy(similar_truth, predicted_similarity_simi)
        comparison_loss_unsimi = tf.keras.losses.binary_crossentropy(tf.zeros_like(similar_truth), predicted_similarity_unsimi)

        loss = tf.maximum(0, comparison_loss_simi - comparison_loss_unsimi)

        return loss

    def train(self, epochs=2000, batch_size=120, group_name='E', log_file=None):
        """🔥 重构版训练函数 - 支持组别选择和现代化功能

        Args:
            epochs: 训练轮数
            batch_size: 批次大小
            group_name: 组别名称 (A/B/C/D/E)
            log_file: 日志文件路径
        """
        start_time = datetime.datetime.now()

        # 🔥 验证组别配置
        if not GroupConfig.validate_group(group_name):
            raise ValueError(f"❌ 无效组别 '{group_name}'，可用组别: {list(GroupConfig.GROUPS.keys())}")

        # 🔥 获取组别配置
        test_classes = GroupConfig.get_test_classes(group_name)
        train_classes = GroupConfig.get_train_classes(group_name)

        print(f"🔥 开始{group_name.upper()}组ACGAN-FG训练...")
        print(f"📅 开始时间: {start_time}")
        print(f"🎯 测试类别: {test_classes} ({group_name.upper()}组)")
        print(f"🎯 训练类别: {train_classes}")
        print(f"⚙️ 训练参数: epochs={epochs}, batch_size={batch_size}")

        # 🔥 设置日志文件
        if log_file is None:
            log_file = f"logs/{group_name}_ACGAN_FG_{start_time.strftime('%Y%m%d_%H%M%S')}.log"

        self.setup_logging(log_file, group_name)

        # 🔥 准确率记录列表（保持原版变量名）
        accuracy_list_1 = []  # lsvm
        accuracy_list_2 = []  # nrf
        accuracy_list_3 = []  # pnb
        accuracy_list_4 = []  # mlp

        # 🔥 对抗损失真值（保持原版不变）
        valid = -np.ones((batch_size, 1))
        fake = np.ones((batch_size, 1))
        dummy = np.zeros((batch_size, 1))  # Dummy gt for gradient penalty

        similar_truth = np.ones((batch_size, 1))
        unsimilar_truth = np.zeros((batch_size, 1))

        # 🔥 数据路径（修正为正确的data目录路径）
        PATH_train = './data/dataset_train_case1.npz'
        PATH_test = './data/dataset_test_case1.npz'

        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)

        print("✅ 数据加载完成")
        print(f"   训练数据文件: {PATH_train}")
        print(f"   测试数据文件: {PATH_test}")

        # 🔥 动态组合训练数据（根据组别配置）
        train_x_list = []
        train_y_list = []

        for class_id in train_classes:
            train_x_list.append(train_data[f'training_samples_{class_id}'])
            train_y_list.append(train_data[f'training_attribute_{class_id}'])

        traindata = np.concatenate(train_x_list, axis=0)
        train_attributelabel = np.concatenate(train_y_list, axis=0)

        # 🔥 动态组合测试数据（根据组别配置）
        test_x_list = []
        test_y_list = []

        for class_id in test_classes:
            test_x_list.append(test_data[f'testing_samples_{class_id}'])
            test_y_list.append(test_data[f'testing_attribute_{class_id}'])

        testdata = np.concatenate(test_x_list, axis=0)
        test_attributelabel = np.concatenate(test_y_list, axis=0)

        print(f"✅ 数据组合完成")
        print(f"   训练数据形状: {traindata.shape}")
        print(f"   训练标签形状: {train_attributelabel.shape}")
        print(f"   测试数据形状: {testdata.shape}")
        print(f"   测试标签形状: {test_attributelabel.shape}")

        num_batches = int(traindata.shape[0] / batch_size)
        print(f"   批次数量: {num_batches}")
        print()

        # 🔥 开始训练循环（保持原版训练逻辑完全不变）
        for epoch in range(epochs):

            for batch_i in range(num_batches):

                start_i = batch_i * batch_size
                end_i = (batch_i + 1) * batch_size

                train_x = traindata[start_i:end_i]
                train_y = train_attributelabel[start_i:end_i]

                # 🔥 训练自编码器（保持原版逻辑不变）
                self.autoencoder.trainable = True
                self.c.trainable = False
                self.m.trainable = False
                self.d.trainable = False
                self.g.trainable = False

                with tf.GradientTape(persistent=True) as tape_auto:
                    feature, output_sample = self.autoencoder(train_x)
                    autoencoder_loss = mean_squared_error(train_x, output_sample)
                average_autoencoder_loss = tf.reduce_mean(autoencoder_loss)
                grads_autoencoder = tape_auto.gradient(autoencoder_loss, self.autoencoder.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_autoencoder, self.autoencoder.trainable_weights))

                del tape_auto

                # 🔥 训练分类器（保持原版逻辑不变）
                self.autoencoder.trainable = False
                self.c.trainable = True
                self.m.trainable = False
                self.d.trainable = False
                self.g.trainable = False

                with tf.GradientTape(persistent=True) as tape_c:
                    feature_c, output_sample_c = self.autoencoder(train_x)
                    hidden_ouput_c, predict_attribute_c = self.c(feature_c)
                    c_loss = self.classification_loss(feature_c, train_y, hidden_ouput_c, predict_attribute_c)
                average_c_loss = tf.reduce_mean(c_loss)
                grads_c = tape_c.gradient(c_loss, self.c.trainable_weights)
                self.c_optimizer.apply_gradients(zip(grads_c, self.c.trainable_weights))

                del tape_c

                # 🔥 训练比较器（保持原版逻辑不变）
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.m.trainable = True
                self.d.trainable = False
                self.g.trainable = False

                m_loss_1 = self.m_model.train_on_batch([train_x, train_x], [similar_truth])

                position = int(start_i / 480)
                delete_start = position * 480
                delete_end = (position + 1) * 480

                new_traindata = np.delete(traindata, slice(delete_start, delete_end), axis=0)
                new_train_attribute_label = np.delete(train_attributelabel, slice(delete_start, delete_end), axis=0)
                random_indices = np.random.choice(new_traindata.shape[0], size=batch_size, replace=False)
                selected_train_x = new_traindata[random_indices]

                m_loss_2 = self.m_model.train_on_batch([train_x, selected_train_x], [unsimilar_truth])

                # 🔥 训练判别器（保持原版逻辑不变）
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.m.trainable = False
                self.d.trainable = True
                self.g.trainable = False

                for _ in range(self.n_critic):

                    with tf.GradientTape(persistent=True) as tape_d:

                        Noise_shape = (batch_size, 50, 1)
                        noise = tf.random.normal(shape=Noise_shape)
                        fake_feature = self.g([noise, train_y])

                        real_feature, decoded = self.autoencoder(train_x)

                        interpolated_feature = RandomWeightedAverage()([real_feature, fake_feature])

                        real_validity = self.d([real_feature, train_y])
                        fake_validity = self.d([fake_feature, train_y])
                        interploted_validity = self.d([interpolated_feature, train_y])

                        d_loss_real = self.wasserstein_loss(valid, real_validity)
                        d_loss_fake = self.wasserstein_loss(fake, fake_validity)

                        gradients = tape_d.gradient(interploted_validity, interpolated_feature)

                        gradient_penalty = self.gradient_penalty_loss(gradients)

                        d_loss = d_loss_real + d_loss_fake + self.LAMBDA_GP * gradient_penalty

                    average_d_loss = tf.reduce_mean(d_loss)

                    grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)

                    self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                    del tape_d

                # 🔥 训练生成器（保持原版逻辑不变）
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.m.trainable = False
                self.d.trainable = False
                self.g.trainable = True

                Noise_shape_g = (batch_size, self.latent_dim, 1)
                noise_g = tf.random.normal(shape=Noise_shape_g)

                base_indices = np.random.choice(480, size=batch_size, replace=False)
                all_indices = base_indices[None, :] + 480 * np.arange(11)[:, None]

                homogeneous_feature, new_sample_1 = self.autoencoder(train_x)

                heterogeneous_features, new_samples = zip(*[
                    self.autoencoder(new_traindata[indices])
                    for indices in all_indices])

                heterogeneous_attributes = [new_train_attribute_label[indices] for indices in all_indices]

                with tf.GradientTape(persistent=True) as tape_g:

                    Fake_feature_g = self.g([noise_g, train_y])
                    Fake_validity_g = self.d([Fake_feature_g, train_y])
                    adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)  # L_adv

                    fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                    classification_loss = self.classification_loss(Fake_feature_g, train_y,
                                                                 fake_hidden_ouput_g, Fake_classification_g)  # L_cla

                    comparison_loss_simi = tf.keras.losses.binary_crossentropy(
                        similar_truth, self.m([Fake_feature_g, homogeneous_feature]))

                    comparison_loss_unsimi = tf.reduce_sum([
                        tf.keras.losses.binary_crossentropy(
                            unsimilar_truth,
                            self.m([Fake_feature_g, heterogeneous_features[i]])
                        ) for i in range(11)
                    ], axis=0)

                    cycle_rank_loss = 0

                    if self.crl == True:

                        reconstructed_feature = self.g([noise_g, Fake_classification_g])
                        Fake_feature_g_unsimi = [
                            self.g([noise_g, heterogeneous_attributes[i]])
                            for i in range(11)
                        ]

                        cycle_rank_losses = [
                            self.cycle_rank_loss(
                                Fake_feature_g,
                                reconstructed_feature,
                                Fake_feature_g_unsimi[i],
                                similar_truth
                            ) for i in range(11)
                        ]

                        cycle_rank_loss = tf.reduce_mean(cycle_rank_losses, axis=0)

                    total_loss = adversarial_loss + self.lambda_cla * classification_loss \
                               + self.lambda_cms * comparison_loss_simi + self.lambda_cms * comparison_loss_unsimi \
                               + self.lambda_crl * cycle_rank_loss

                average_g_loss = tf.reduce_mean(total_loss)
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))

                del tape_g

                # 🔥 输出训练进度（保持原版格式）
                elapsed_time = datetime.datetime.now() - start_time

                print("[Epoch %d/%d][Batch %d/%d][Autoencoder loss: %f][C loss: %f][M loss: %f][D loss: %f][G loss %05f ]time: %s " \
                      % (epoch, epochs,
                         batch_i, num_batches,
                         average_autoencoder_loss,
                         average_c_loss,
                         (m_loss_1 + m_loss_2) / 2,
                         average_d_loss,
                         average_g_loss,
                         elapsed_time))

            # 🔥 每个epoch进行测试（保持原版逻辑）
            if epoch % 1 == 0:

                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis(
                    2000, testdata, test_attributelabel, self.autoencoder, self.g, self.c, test_classes)

                accuracy_list_1.append(accuracy_lsvm)
                accuracy_list_2.append(accuracy_nrf)
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                # 🔥 记录训练进度（新增功能）
                losses = {
                    'ae': float(average_autoencoder_loss),
                    'g': float(average_g_loss),
                    'd': float(average_d_loss),
                    'c': float(average_c_loss),
                    'm': float((m_loss_1 + m_loss_2) / 2)
                }

                accuracies = {
                    'lsvm': accuracy_lsvm,
                    'nrf': accuracy_nrf,
                    'pnb': accuracy_pnb,
                    'mlp': accuracy_mlp
                }

                self.log_training_progress(epoch, losses, accuracies, log_file, start_time)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]" \
                      % (epoch, epochs, max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)))

        # 🔥 训练完成总结
        best_acc = max([max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)])
        print('finished! best_acc:{:.4f}'.format(best_acc))

        end_time = datetime.datetime.now()
        total_time = end_time - start_time

        # 🔥 最终总结报告
        final_message = (
            f"\n🎉 {group_name.upper()}组ACGAN-FG训练完成！\n"
            f"📅 开始时间: {start_time}\n"
            f"📅 结束时间: {end_time}\n"
            f"⏱️ 总训练时间: {total_time}\n"
            f"🏆 最佳准确率:\n"
            f"   LinearSVM: {max(accuracy_list_1):.2f}%\n"
            f"   RandomForest: {max(accuracy_list_2):.2f}%\n"
            f"   GaussianNB: {max(accuracy_list_3):.2f}%\n"
            f"   MLPClassifier: {max(accuracy_list_4):.2f}%\n"
            f"📊 整体最佳准确率: {best_acc:.2f}%\n"
            f"📝 日志文件: {log_file}"
        )

        print(final_message)

        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(final_message + '\n')

        # 🔥 关闭TensorBoard监控器
        if self.tensorboard_monitor is not None:
            try:
                self.tensorboard_monitor.close()
                tensorboard_log_dir = f"./tensorboard_logs/{group_name}_ACGAN_FG_realtime"
                print(f"📊 TensorBoard日志已保存到: {tensorboard_log_dir}")
                print(f"🌐 启动命令: tensorboard --logdir=./tensorboard_logs")
                print(f"🔗 访问地址: http://localhost:6006")
            except Exception as e:
                print(f"⚠️ TensorBoard关闭失败: {e}")
        else:
            print("⚠️ TensorBoard监控器未初始化，无需关闭")


if __name__ == '__main__':
    print("🔥 ACGAN-FG 重构版本")
    print("=" * 60)
    print("✨ 新增功能:")
    print("   - 组别选择系统 (A/B/C/D/E组)")
    print("   - TensorBoard实时监控")
    print("   - GPU检测与内存管理")
    print("   - 结构化日志系统")
    print("   - 保持核心算法完全不变")
    print("=" * 60)

    # 🔥 创建模型实例
    gan = Zero_shot()

    # 🔥 可选择不同组别进行训练
    # 组别配置:
    # A组: 测试类别 [1, 6, 14]
    # B组: 测试类别 [4, 7, 10]
    # C组: 测试类别 [8, 11, 12]
    # D组: 测试类别 [2, 3, 5]
    # E组: 测试类别 [9, 13, 15] (原版默认)

    # 🔥 使用E组配置（与原版一致）
    gan.train(epochs=2000, batch_size=120, group_name='A')

    # 🔥 其他组别使用示例:
    # gan.train(epochs=2000, batch_size=120, group_name='A')  # A组
    # gan.train(epochs=2000, batch_size=120, group_name='B')  # B组
    # gan.train(epochs=2000, batch_size=120, group_name='C')  # C组
    # gan.train(epochs=2000, batch_size=120, group_name='D')  # D组

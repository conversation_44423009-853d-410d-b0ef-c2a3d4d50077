import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler


# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    # 限制TensorFlow只使用第一个GPU
    tf.config.set_visible_devices(gpus[0], 'GPU')
    # 设置显存按需增长
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    # 显存增长必须在GPU初始化之前设置
    print(e)


def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = BatchNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = BatchNormalization()(y)
    
    # 如果输入和输出维度不同，需要一个线性投影
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        # input_shape is (batch_size, feature_dim)
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        # Reshape for matrix multiplication
        # Temporarily add a "sequence length" of 1
        # x_reshaped shape: (batch_size, 1, feature_dim)
        x_reshaped = K.expand_dims(x, axis=1)

        # Q, K, V projections
        q = self.query(x_reshaped)  # (batch_size, 1, feature_dim/8)
        k = self.key(x_reshaped)    # (batch_size, 1, feature_dim/8)
        v = self.value(x_reshaped)  # (batch_size, 1, feature_dim)

        # Attention scores
        attention_scores = K.batch_dot(q, k, axes=[2, 2]) # (batch_size, 1, 1)
        attention_probs = K.softmax(attention_scores)

        # Apply attention
        context = K.batch_dot(attention_probs, v) # (batch_size, 1, feature_dim)
        
        # Remove the temporary dimension
        context = K.squeeze(context, axis=1)

        # Add back to original input (residual connection)
        return x + self.gamma * context
        
class Zero_shot():
    def __init__(self):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        self.lambda_cla = 10 
        self.lambda_triplet = 10 
        self.lambda_crl = 0.01 
        
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        # 改进的特征空间插值参数
        self.use_interpolation = True
        self.adaptive_interpolation = True  # 启用自适应插值策略
        
        # 基础插值参数
        self.base_interpolation_ratio = 0.15  # 降低基础插值比例
        self.max_interpolation_ratio = 0.4    # 最大插值比例
        self.alpha_range = (0.3, 0.7)         # 更保守的插值系数范围
        
        # 自适应插值参数
        self.interpolation_warmup_epochs = 50  # 插值预热期
        self.performance_threshold = 0.6      # 性能阈值，低于此值才使用强插值
        self.interpolation_decay = 0.95       # 插值比例衰减因子
        
        # 组别难度配置（基于历史性能）
        self.group_difficulty = {
            'A': 'easy',    # A组性能好，插值可能有害
            'B': 'medium',  # B组中等难度
            'C': 'medium',  # C组中等难度  
            'D': 'easy',    # D组性能较好
            'E': 'hard'     # E组最困难，需要更多插值
        }
        
        # 当前组别和动态参数
        self.current_group = None
        self.current_interpolation_ratio = self.base_interpolation_ratio
        self.recent_performance = []
        
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For triplet loss
        
        self.autoencoder= self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c= self.build_classifier()
    
    def set_group_config(self, group_name):
        """设置当前组别配置"""
        self.current_group = group_name
        difficulty = self.group_difficulty.get(group_name, 'medium')
        
        if difficulty == 'easy':
            # 对于容易的组别，使用更保守的插值策略
            self.use_interpolation = False  # 直接禁用插值
            print(f"Group {group_name} (Easy): 禁用特征插值")
            
        elif difficulty == 'hard':
            # 对于困难的组别，使用更激进的插值策略
            self.current_interpolation_ratio = self.max_interpolation_ratio * 0.8
            self.alpha_range = (0.2, 0.8)  # 更大的插值范围
            print(f"Group {group_name} (Hard): 启用强化插值策略")
            
        else:  # medium
            # 对于中等难度的组别，使用标准策略
            self.current_interpolation_ratio = self.base_interpolation_ratio
            print(f"Group {group_name} (Medium): 使用标准插值策略")
    
    def update_interpolation_strategy(self, current_epoch, recent_accuracy):
        """根据训练进展和性能动态更新插值策略"""
        self.recent_performance.append(recent_accuracy)
        
        # 只保留最近10个epoch的性能
        if len(self.recent_performance) > 10:
            self.recent_performance.pop(0)
        
        # 在预热期内，逐渐增加插值比例
        if current_epoch < self.interpolation_warmup_epochs:
            warmup_factor = current_epoch / self.interpolation_warmup_epochs
            self.current_interpolation_ratio = self.base_interpolation_ratio * warmup_factor
        
        # 根据最近性能调整插值策略
        if len(self.recent_performance) >= 5:
            avg_performance = np.mean(self.recent_performance[-5:])
            
            if avg_performance < self.performance_threshold:
                # 性能较差，增加插值比例
                self.current_interpolation_ratio = min(
                    self.current_interpolation_ratio * 1.1, 
                    self.max_interpolation_ratio
                )
            else:
                # 性能较好，减少插值比例
                self.current_interpolation_ratio *= self.interpolation_decay
                self.current_interpolation_ratio = max(
                    self.current_interpolation_ratio,
                    self.base_interpolation_ratio * 0.5
                )
    
    def generate_smart_interpolated_features(self, train_X_by_class, train_Y_by_class, 
                                           seen_class_indices, num_interpolations, epoch):
        """
        智能特征空间插值生成
        根据类别相似性和训练进展生成更合理的插值样本
        """
        if not self.use_interpolation or num_interpolations <= 0:
            return np.array([]), np.array([]), np.array([])
        
        interpolated_samples = []
        interpolated_attributes = []
        interpolated_labels = []
        
        # 计算类别间的属性相似性矩阵
        class_similarities = {}
        for i, class1 in enumerate(seen_class_indices):
            for j, class2 in enumerate(seen_class_indices):
                if i < j:  # 避免重复计算
                    attr1 = np.mean(train_Y_by_class[class1], axis=0)
                    attr2 = np.mean(train_Y_by_class[class2], axis=0)
                    similarity = np.dot(attr1, attr2) / (np.linalg.norm(attr1) * np.linalg.norm(attr2))
                    class_similarities[(class1, class2)] = similarity
        
        # 根据相似性选择插值对
        similarity_threshold = 0.3 if epoch < 100 else 0.1  # 早期选择相似类别，后期放宽限制
        
        for _ in range(num_interpolations):
            if class_similarities and np.random.random() < 0.7:  # 70%概率选择相似类别
                # 选择相似度适中的类别对进行插值
                valid_pairs = [(pair, sim) for pair, sim in class_similarities.items() 
                             if sim > similarity_threshold]
                
                if valid_pairs:
                    # 按相似度加权选择
                    pairs, similarities = zip(*valid_pairs)
                    similarities = np.array(similarities)
                    probs = similarities / np.sum(similarities)
                    chosen_pair = pairs[np.random.choice(len(pairs), p=probs)]
                    class1, class2 = chosen_pair
                else:
                    # 如果没有合适的相似类别，随机选择
                    class1, class2 = np.random.choice(seen_class_indices, 2, replace=False)
            else:
                # 随机选择两个不同的seen class
                class1, class2 = np.random.choice(seen_class_indices, 2, replace=False)
            
            # 从每个类别中选择质量较好的样本（避免选择边界样本）
            class1_samples = train_X_by_class[class1]
            class2_samples = train_X_by_class[class2]
            
            # 选择距离类别中心较近的样本
            if len(class1_samples) > 5:
                class1_center = np.mean(class1_samples, axis=0)
                distances1 = np.linalg.norm(class1_samples - class1_center, axis=1)
                idx1 = np.argsort(distances1)[:len(class1_samples)//2]  # 选择距离中心较近的一半
                idx1 = np.random.choice(idx1)
            else:
                idx1 = np.random.choice(len(class1_samples))
            
            if len(class2_samples) > 5:
                class2_center = np.mean(class2_samples, axis=0)
                distances2 = np.linalg.norm(class2_samples - class2_center, axis=1)
                idx2 = np.argsort(distances2)[:len(class2_samples)//2]
                idx2 = np.random.choice(idx2)
            else:
                idx2 = np.random.choice(len(class2_samples))
            
            sample1 = class1_samples[idx1]
            sample2 = class2_samples[idx2]
            
            attr1 = train_Y_by_class[class1][idx1]
            attr2 = train_Y_by_class[class2][idx2]
            
            # 动态调整插值系数，早期使用更保守的插值
            if epoch < 50:
                alpha = np.random.uniform(0.4, 0.6)  # 早期更保守
            else:
                alpha = np.random.uniform(self.alpha_range[0], self.alpha_range[1])
            
            # 样本插值
            interpolated_sample = alpha * sample1 + (1 - alpha) * sample2
            
            # 属性插值策略改进
            attr_similarity = np.dot(attr1, attr2) / (np.linalg.norm(attr1) * np.linalg.norm(attr2))
            
            if attr_similarity > 0.5:
                # 属性相似时使用插值
                interpolated_attr = alpha * attr1 + (1 - alpha) * attr2
            else:
                # 属性差异大时选择其中一个，但偏向alpha值
                interpolated_attr = attr1 if np.random.random() < alpha else attr2
            
            # 标签选择策略
            interpolated_label = class1 if np.random.random() < alpha else class2
            
            interpolated_samples.append(interpolated_sample)
            interpolated_attributes.append(interpolated_attr)
            interpolated_labels.append(interpolated_label)
        
        return (np.array(interpolated_samples), 
                np.array(interpolated_attributes), 
                np.array(interpolated_labels))
        
    def build_autoencoder(self):
      
      sample = Input(shape=self.sample_shape)     
     
      a0=sample

      # Encoder
      a1=Dense(100)(a0)
      a1=LeakyReLU(alpha=0.2)(a1)
      a1=BatchNormalization()(a1)

      a2=Dense(200)(a1)
      a2=LeakyReLU(alpha=0.2)(a2)
      a2=BatchNormalization()(a2)

      a3=Dense(256)(a2)
      a3=LeakyReLU(alpha=0.2)(a3)
      a3=BatchNormalization()(a3)
      feature=a3

      # Decoder
      a4=Dense(200)(feature)
      a4=LeakyReLU(alpha=0.2)(a4)
      a4=BatchNormalization()(a4)

      a5=Dense(100)(a4)
      a5=LeakyReLU(alpha=0.2)(a5)
      a5=BatchNormalization()(a5)

      a6=Dense(52)(a5)
      a6=LeakyReLU(alpha=0.2)(a6)
      a6=BatchNormalization()(a6)
      output_sample=a6

      # Autoencoder Model
      autoencoder = Model(sample,[feature, output_sample])
      # We only need the encoder part for triplet loss feature extraction
      self.encoder = Model(sample, feature)
      return autoencoder    
        
    def build_discriminator(self):
        
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input,attribute],validity)

    def build_generator(self):
      
      noise = Input(shape=self.noise_shape)
      attribute = Input(shape=(20,), dtype='float32')
      
      noise_embedding = Flatten()(noise)
      attribute_embedding = Dense(self.latent_dim)(attribute)
      
      g_input = concatenate([noise_embedding, attribute_embedding])

      g1 = Dense(128)(g_input)
      g1 = LeakyReLU(alpha=0.2)(g1)
      g1 = BatchNormalization()(g1)

      g2 = residual_block(g1, 256) 
      g3 = residual_block(g2, 256) 
      
      g3_attention = SelfAttention()(g3)
      
      generated_feature = Dense(256)(g3_attention)
      generated_feature = BatchNormalization()(generated_feature)

      return Model([noise,attribute],generated_feature)
    
    def build_classifier(self):
        
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        
        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        hidden_ouput=c2
               
        c3 = Dense(20,activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
            return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self,current_batch_features,y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(
                y_true, pred_attribute)
        
        mi_penalty=0    
        if self.bound == True:    
          mi_penalty = self.mi_penalty_loss(
              current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def cycle_rank_loss(self, anchor, positive, negative):
        # We can reuse the triplet loss logic for cycle rank consistency
        # The goal is that the reconstructed feature (positive) is closer to the original fake feature (anchor)
        # than any other generated feature from a different class (negative).
        return self.triplet_loss(anchor, positive, negative)
    
    def train(self, epochs, batch_size, log_file=None, test_class_indices=None):
        
        start_time = datetime.datetime.now()
        
        accuracy_list_1=[]
        accuracy_list_2=[]
        accuracy_list_3=[]
        accuracy_list_4=[]
        
        valid = -np.ones((batch_size,1) )
        fake = np.ones((batch_size,1) )
        
        PATH_train='./dataset_train_case1.npz'
        PATH_test='./dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        # 使用传入的测试类别，如果没有传入则默认使用E组
        if test_class_indices is None:
            test_class_indices = [9, 13, 15]  # 默认E组 (1-based)
        
        # 根据测试类别，确定训练类别（Seen classes）
        all_class_indices = list(range(1, 16)) # 1-15
        seen_class_indices = [i for i in all_class_indices if i not in test_class_indices]
        
        # This part remains mostly the same, but we will use the labels to find pos/neg samples
        train_X_by_class = {i: train_data[f'training_samples_{i}'] for i in seen_class_indices}
        train_Y_by_class = {i: train_data[f'training_attribute_{i}'] for i in seen_class_indices}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        test_X_by_class = {i: test_data[f'testing_samples_{i}'] for i in test_class_indices}
        test_Y_by_class = {i: test_data[f'testing_attribute_{i}'] for i in test_class_indices}

        test_X = np.concatenate([v for k, v in test_X_by_class.items()])
        test_Y = np.concatenate([v for k, v in test_Y_by_class.items()])
        test_classlabel = np.concatenate([np.full(len(v), k) for k, v in test_X_by_class.items()])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # Re-organize scaled data back into class dictionaries
        current_pos = 0
        for i in seen_class_indices:
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata=all_train_X
        train_attributelabel=all_train_Y
        train_classlabel = all_train_labels
        
        testdata=test_X
        test_attributelabel=test_Y
       
        num_batches=int(traindata.shape[0]/batch_size)
               
        for epoch in range(epochs):
            
            for batch_i in range(num_batches):
                
                start_i =batch_i * batch_size
                end_i=(batch_i + 1) * batch_size
                
                train_x=traindata[start_i:end_i]
                train_y=train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                
                # 特征空间插值增强
                if self.use_interpolation and epoch > 0:  # 从第二个epoch开始使用插值
                    num_interpolations = int(batch_size * self.current_interpolation_ratio)
                    interp_samples, interp_attrs, interp_labels = self.generate_smart_interpolated_features(
                        train_X_by_class, train_Y_by_class, seen_class_indices, num_interpolations, epoch
                    )
                    
                    # 将插值样本添加到当前批次
                    if len(interp_samples) > 0:
                        # 如果插值样本数量足够，随机选择部分替换原始样本
                        replace_indices = np.random.choice(batch_size, min(num_interpolations, batch_size), replace=False)
                        train_x[replace_indices[:len(interp_samples)]] = interp_samples[:len(replace_indices)]
                        train_y[replace_indices[:len(interp_samples)]] = interp_attrs[:len(replace_indices)]
                        train_labels[replace_indices[:len(interp_samples)]] = interp_labels[:len(replace_indices)]
                                                                               
                # Autoencoder and Classifier Training (Same as before)
                self.autoencoder.trainable = True
                self.c.trainable = True # Train C together with AE now
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                  feature, output_sample=self.autoencoder(train_x)
                  autoencoder_loss=mean_squared_error(train_x,output_sample)      

                  hidden_ouput_c,predict_attribute_c=self.c(feature)
                  c_loss=self.classification_loss(feature,train_y, hidden_ouput_c, predict_attribute_c)

                  total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c

                # Triplet Loss Metric Learning
                self.autoencoder.trainable = True # Encoder is part of metric learning
                self.c.trainable = True
                
                # Sample triplets
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []
                for label in train_labels:
                    pos_class_samples = train_X_by_class[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    
                    neg_class = np.random.choice([c for c in seen_class_indices if c != label])
                    neg_class_samples = train_X_by_class[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                # Discriminator Training (Same as before)
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False

                for _ in range(self.n_critic):
                  with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature = self.g([noise,train_y])
                    real_feature = self.encoder(train_x)
        
                    real_validity = self.d([real_feature,train_y])
                    fake_validity = self.d([fake_feature,train_y])  
                                           
                    d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                    d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                    d_loss = d_loss_real + d_loss_fake
                  
                  grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                  self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # Generator Training
                self.d.trainable = False               
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                  noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                  Fake_feature_g = self.g([noise_g,train_y])
                  Fake_validity_g = self.d([Fake_feature_g,train_y])
                  adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
            
                  fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                  classification_loss = self.classification_loss(Fake_feature_g,train_y, fake_hidden_ouput_g, Fake_classification_g)
                  
                  # Triplet loss for Generator
                  g_anchor_features = Fake_feature_g
                  g_positive_features = self.encoder(positive_samples) # Use same positive samples
                  g_negative_features = self.encoder(negative_samples) # Use same negative samples
                  triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                  
                  cycle_rank_loss = 0
                  if self.crl == True:
                    reconstructed_feature = self.g([noise_g, Fake_classification_g])
                    
                    # For cycle rank, the "negative" is a feature from a different class
                    # We can reuse the negative samples from the batch for simplicity
                    negative_attributes = np.array([train_Y_by_class[np.random.choice([c for c in seen_class_indices if c != label])][0] for label in train_labels])
                    unsimilar_generated_feature = self.g([noise_g, negative_attributes])

                    cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                           
                  total_loss = adversarial_loss + self.lambda_cla * classification_loss + self.lambda_triplet * triplet_loss_g + self.lambda_crl*cycle_rank_loss  
                          
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print ("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][M loss: %f][D loss: %f][G loss %05f ]time: %s " \
                 % (epoch, epochs,
                   batch_i, num_batches,
                   tf.reduce_mean(total_ac_loss), 
                     m_loss,
                     d_loss,
                     tf.reduce_mean(total_loss),                                                                                                              
                     elapsed_time))
        
            if epoch % 1 == 0:
                accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp = feature_generation_and_diagnosis(2000,testdata,test_attributelabel,self.autoencoder,self.g, self.c, test_class_indices)  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                # 计算当前最佳准确率用于自适应插值策略
                current_best_accuracy = max(accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp)
                
                # 更新插值策略
                if self.adaptive_interpolation:
                    self.update_interpolation_strategy(epoch, current_best_accuracy)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                  %(epoch, epochs,max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)))
                
                # 增强日志输出，显示插值策略信息
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]")
                    
                    # 添加插值策略信息
                    if self.use_interpolation:
                        log_message += f"[Interp_ratio: {self.current_interpolation_ratio:.3f}]"
                    else:
                        log_message += "[Interp: Disabled]"
                    
                    log_message += "\n"
                    log_file.write(log_message)
                    log_file.flush()
            
        best_accuracy = max([max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)])
        print('finished! best_acc:{:.4f}'.format(best_accuracy))
        if log_file:
            log_file.write(f'finished! best_acc:{best_accuracy:.4f}\n')
            log_file.flush()
                
if __name__ == '__main__':
    # =============================================================
    # 配置区域：只需要修改这里的TARGET_GROUP即可切换实验组别
    # =============================================================
    TARGET_GROUP = 'A'  # 可选: 'A', 'B', 'C', 'D', 'E'
    # =============================================================
    
    # 分组配置
    GROUP_CONFIGS = {
        'A': [1, 6, 14],   # 测试类别: [1, 6, 14] 
        'B': [4, 7, 10],   # 测试类别: [4, 7, 10]
        'C': [8, 11, 12],  # 测试类别: [8, 11, 12]
        'D': [2, 3, 5],    # 测试类别: [2, 3, 5]
        'E': [9, 13, 15],  # 测试类别: [9, 13, 15]
    }
    
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + f"_triplet_interpolation_Group{TARGET_GROUP}.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"开始运行 Group {TARGET_GROUP} 实验（特征空间插值版本），测试类别: {GROUP_CONFIGS[TARGET_GROUP]}")
    print(f"训练开始，日志将被记录到: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 训练日志 (Triplet架构 + 特征空间插值 - Group {TARGET_GROUP})\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**实验组别**: Group {TARGET_GROUP}\n")
        log_file.write(f"**测试类别**: {GROUP_CONFIGS[TARGET_GROUP]}\n")
        log_file.write(f"**改进方法**: 特征空间插值 (Feature Space Interpolation)\n")
        log_file.write(f"**插值比例**: 30%\n")
        log_file.write(f"**插值系数范围**: [0.2, 0.8]\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = Zero_shot()
        gan.set_group_config(TARGET_GROUP) # 设置当前组别
        gan.train(epochs=2000, batch_size=256, log_file=log_file, test_class_indices=GROUP_CONFIGS[TARGET_GROUP])

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"训练完成，日志已保存至: {log_filename}") 
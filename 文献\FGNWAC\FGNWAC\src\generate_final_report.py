#!/usr/bin/env python3
"""
生成最终的超参数搜索报告
"""

import pandas as pd
import json
import os
from datetime import datetime

def generate_final_report():
    """生成最终搜索报告"""
    print("📊 生成最终超参数搜索报告")
    print("=" * 60)
    
    # 读取阶段1结果
    stage1_df = pd.read_csv('hyperparameter_search_results/lambda_ar_search_split_A.csv')
    stage2_df = pd.read_csv('hyperparameter_search_results/lr_search_split_A.csv')
    
    print("\n🎯 阶段1结果 (Lambda_AR搜索)")
    print("-" * 40)
    print("Lambda_AR | 最佳准确率 | 最佳轮数 | 训练时间(分钟)")
    print("-" * 40)
    
    stage1_results = []
    for _, row in stage1_df.iterrows():
        params = eval(row['parameters'])
        lambda_ar = params['lambda_ar']
        accuracy = row['best_accuracy']
        epoch = row['best_epoch']
        time_min = row['training_time_minutes']
        print(f"{lambda_ar:8.1f} | {accuracy:10.4f} | {epoch:8d} | {time_min:13.1f}")
        stage1_results.append((lambda_ar, accuracy))
    
    # 找到阶段1最佳
    best_stage1 = max(stage1_results, key=lambda x: x[1])
    print(f"\n✅ 阶段1最佳: Lambda_AR={best_stage1[0]}, 准确率={best_stage1[1]:.4f}")
    
    print("\n🎯 阶段2结果 (学习率搜索)")
    print("-" * 40)
    print("学习率    | 最佳准确率 | 最佳轮数 | 训练时间(分钟)")
    print("-" * 40)
    
    stage2_results = []
    for _, row in stage2_df.iterrows():
        params = eval(row['parameters'])
        lr = params['lr']
        accuracy = row['best_accuracy']
        epoch = row['best_epoch']
        time_min = row['training_time_minutes']
        print(f"{lr:9.5f} | {accuracy:10.4f} | {epoch:8d} | {time_min:13.1f}")
        stage2_results.append((lr, accuracy))
    
    # 找到阶段2最佳
    best_stage2 = max(stage2_results, key=lambda x: x[1])
    print(f"\n✅ 阶段2最佳: 学习率={best_stage2[0]:.5f}, 准确率={best_stage2[1]:.4f}")
    
    # 生成最终推荐
    print("\n🏆 最终推荐配置")
    print("=" * 30)
    print(f"Lambda_AR: {best_stage1[0]}")
    print(f"学习率:    {best_stage2[0]:.5f}")
    print(f"预期准确率: {best_stage2[1]:.4f}")
    
    # 与原始配置对比
    print(f"\n📈 性能提升分析")
    print("-" * 30)
    original_lambda_ar = 0.5  # 原始默认值
    original_lr = 0.0001      # 原始默认值
    
    # 从阶段1找原始lambda_ar的性能
    original_stage1_acc = None
    for lambda_ar, acc in stage1_results:
        if lambda_ar == original_lambda_ar:
            original_stage1_acc = acc
            break
    
    if original_stage1_acc:
        improvement_stage1 = ((best_stage1[1] - original_stage1_acc) / original_stage1_acc) * 100
        print(f"Lambda_AR优化: {original_lambda_ar} → {best_stage1[0]}")
        print(f"准确率提升: {original_stage1_acc:.4f} → {best_stage1[1]:.4f} (+{improvement_stage1:.2f}%)")
    
    # 学习率分析
    print(f"\n学习率分析:")
    for lr, acc in stage2_results:
        if lr == original_lr:
            print(f"原始lr={lr:.5f}: 准确率={acc:.4f}")
        else:
            print(f"测试lr={lr:.5f}: 准确率={acc:.4f}")
    
    # 保存最终配置
    final_config = {
        'split_name': 'A',
        'best_lambda_ar': float(best_stage1[0]),
        'best_lr': float(best_stage2[0]),
        'stage1_best_accuracy': float(best_stage1[1]),
        'stage2_best_accuracy': float(best_stage2[1]),
        'search_completed': True,
        'total_experiments': len(stage1_results) + len(stage2_results),
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'stage1_experiments': len(stage1_results),
            'stage2_experiments': len(stage2_results),
            'best_overall_accuracy': float(max(best_stage1[1], best_stage2[1])),
            'recommended_config': {
                'lambda_ar': float(best_stage1[0]),
                'lr': float(best_stage2[0])
            }
        }
    }
    
    # 保存最终配置
    with open('hyperparameter_search_results/final_best_config_split_A.json', 'w', encoding='utf-8') as f:
        json.dump(final_config, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 最终配置已保存: final_best_config_split_A.json")
    
    # 生成文本报告
    report_path = 'hyperparameter_search_results/final_search_report_split_A.txt'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("VAEGAN-AR 超参数搜索最终报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"搜索完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"目标分组: A\n\n")
        
        f.write("最终推荐配置:\n")
        f.write("-" * 20 + "\n")
        f.write(f"Lambda_AR: {best_stage1[0]}\n")
        f.write(f"学习率: {best_stage2[0]:.5f}\n")
        f.write(f"预期准确率: {best_stage2[1]:.4f}\n\n")
        
        f.write("搜索摘要:\n")
        f.write("-" * 20 + "\n")
        f.write(f"阶段1实验数: {len(stage1_results)}\n")
        f.write(f"阶段2实验数: {len(stage2_results)}\n")
        f.write(f"总实验数: {len(stage1_results) + len(stage2_results)}\n")
        f.write(f"最佳整体准确率: {max(best_stage1[1], best_stage2[1]):.4f}\n")
    
    print(f"📄 文本报告已保存: {report_path}")
    
    print(f"\n🎉 超参数搜索完成！")
    print(f"   推荐使用: lambda_ar={best_stage1[0]}, lr={best_stage2[0]:.5f}")
    print(f"   预期性能: 准确率 {best_stage2[1]:.4f}")

if __name__ == "__main__":
    generate_final_report()

from keras.models import load_model

import tensorflow as tf
import numpy as np
import random
from sklearn import preprocessing
from sklearn.svm import SVR
from sklearn.svm import SVC
from sklearn.metrics import confusion_matrix
from sklearn.metrics import accuracy_score
from tensorflow.keras import regularizers
from tensorflow.keras.layers import Input, Dense, Reshape, Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.layers import UpSampling2D, Conv2D, Conv1D,MaxPooling1D,UpSampling1D
from tensorflow.keras.layers import BatchNormalization, Activation, ZeroPadding2D,Embedding,concatenate
from tensorflow.keras.regularizers import l2
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LeakyReLU
from sklearn.svm import SVC,LinearSVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier

def scalar_stand(Train_X, Test_X):
    # 用训练集标准差标准化训练集以及测试集
    scalar_train = preprocessing.StandardScaler().fit(Train_X)
    #scalar_test = preprocessing.StandardScaler().fit(Test_X)
    Train_X = scalar_train.transform(Train_X)
    Test_X = scalar_train.transform(Test_X)
    return Train_X, Test_X


def sample_generation_and_diagnosis(add_quantity,test_x,test_y,generator):
    """
    专门为改进Dense架构设计的测试函数
    使用随机噪声输入而非源样本输入
    """
    
    Labels_train=[]
    Labels_test=[]
    Generated_samples=[]

    for j in range(3):

      i=960*j

      attribute_vector=test_y[i]

      attribute=[attribute_vector for _ in range(add_quantity)]
      attribute=np.array(attribute)
     
      print(attribute.shape)
           
      # 生成随机噪声作为生成器输入（适配改进的Dense架构）
      noise = np.random.normal(0, 1, (add_quantity, 100))

      generated_sample=generator.predict([noise,attribute])
      
      Generated_samples.append(generated_sample)

      labels_train = np.full((add_quantity, 1), j)
      labels_test = np.full((960, 1), j)

      Labels_train.append(labels_train)
      Labels_test.append(labels_test)
    
    Generated_samples=np.array(Generated_samples).reshape(-1, 52)
    Labels_train=np.array(Labels_train).reshape(-1, 1)

    Labels_test=np.array(Labels_test).reshape(-1, 1)  
    
    train_X=Generated_samples
    train_Y=Labels_train.ravel()
    
    test_X=test_x
    test_Y=Labels_test.ravel()

    train_X,test_X=scalar_stand(train_X, test_X)

    classifier_lsvm = LinearSVC(dual="auto", random_state=42)

    classifier_lsvm.fit(train_X, train_Y)
    
    Y_pred_lsvm = classifier_lsvm.predict(test_X)

    accuracy_lsvm = accuracy_score(test_Y, Y_pred_lsvm)
    
    
    
    classifier_nrf = RandomForestClassifier(n_estimators=100, random_state=42)
    
    classifier_nrf.fit(train_X, train_Y)
    
    Y_pred_nrf = classifier_nrf.predict(test_X)

    accuracy_nrf = accuracy_score(test_Y, Y_pred_nrf)
    
    
    classifier_pnb = GaussianNB()
    
    classifier_pnb.fit(train_X, train_Y)
    
    Y_pred_pnb = classifier_pnb.predict(test_X)

    accuracy_pnb = accuracy_score(test_Y, Y_pred_pnb)
    
    
    classifier_mlp = MLPClassifier(hidden_layer_sizes=(100, 50),
                               activation='relu',
                               solver='adam',
                               alpha=0.0001,
                               batch_size='auto',
                               learning_rate='constant',
                               max_iter=200,
                               tol=0.0001,
                               random_state=42)
    
    classifier_mlp.fit(train_X, train_Y)
    
    Y_pred_mlp = classifier_mlp.predict(test_X)

    accuracy_mlp = accuracy_score(test_Y, Y_pred_mlp)
   
    
    return accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp 
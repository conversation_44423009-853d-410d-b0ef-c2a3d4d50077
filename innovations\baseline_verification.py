#!/usr/bin/env python3
"""
基线验证脚本 - 快速测试原版ACGAN-FG性能
目标: 确认原版方法能达到的准确率基准
"""

import os
import sys
import time
import subprocess
import json
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')

def run_baseline_test():
    """运行基线测试"""
    print("🔍 ASDCGAN基线验证测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 目标: 验证原版ACGAN-FG的实际性能")
    print("=" * 60)
    
    results = {}
    
    # 1. 检查VAEGAN-AR的已知结果
    print("\n📊 1. VAEGAN-AR基准性能 (已知结果)")
    vaegan_results = {
        'A': 83.75,
        'B': 55.07, 
        'C': 57.78,
        'D': 63.54,
        'E': 65.21
    }
    
    for group, acc in vaegan_results.items():
        print(f"   Group {group}: {acc:.2f}%")
    
    results['vaegan_ar'] = vaegan_results
    
    # 2. 检查我们的ASDCGAN结果
    print("\n📊 2. ASDCGAN当前性能")
    asdcgan_results = {}
    
    # 从实验配置文件读取结果
    exp_dirs = [
        'experiments/group_A',
        'experiments/group_B', 
        'experiments/group_C',
        'experiments/group_D',
        'experiments/group_E'
    ]
    
    for exp_dir in exp_dirs:
        config_file = f'{exp_dir}/experiment_config.json'
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    group = exp_dir.split('_')[-1].upper()
                    
                    # 从训练日志获取真实最佳准确率
                    best_acc = get_best_accuracy_from_logs(exp_dir)
                    if best_acc is None:
                        best_acc = config.get('final_metrics', {}).get('final_accuracy', 0) * 100
                    
                    asdcgan_results[group] = best_acc
                    print(f"   Group {group}: {best_acc:.2f}%")
            except Exception as e:
                print(f"   Group {exp_dir.split('_')[-1].upper()}: 读取失败 ({e})")
    
    results['asdcgan'] = asdcgan_results
    
    # 3. 性能差距分析
    print("\n📈 3. 性能差距分析")
    print("   Group | VAEGAN-AR | ASDCGAN | 差距")
    print("   ------|-----------|---------|------")
    
    total_gap = 0
    valid_groups = 0
    
    for group in ['A', 'B', 'C', 'D', 'E']:
        if group in vaegan_results and group in asdcgan_results:
            vaegan_acc = vaegan_results[group]
            asdcgan_acc = asdcgan_results[group]
            gap = vaegan_acc - asdcgan_acc
            total_gap += gap
            valid_groups += 1
            
            print(f"     {group}   |   {vaegan_acc:5.2f}%  |  {asdcgan_acc:5.2f}% | {gap:+5.2f}%")
    
    if valid_groups > 0:
        avg_gap = total_gap / valid_groups
        print(f"   ------|-----------|---------|------")
        print(f"   平均  |     -     |    -    | {avg_gap:+5.2f}%")
    
    results['performance_gap'] = {
        'average_gap': avg_gap if valid_groups > 0 else 0,
        'valid_groups': valid_groups
    }
    
    # 4. 问题诊断
    print("\n🔍 4. 初步问题诊断")
    
    if avg_gap > 20:
        print("   ❌ 严重性能差距 (>20%)")
        print("   🔧 建议: 全面检查架构和训练策略")
    elif avg_gap > 10:
        print("   ⚠️  显著性能差距 (10-20%)")
        print("   🔧 建议: 优化超参数和损失函数")
    elif avg_gap > 5:
        print("   ⚡ 中等性能差距 (5-10%)")
        print("   🔧 建议: 微调训练策略")
    else:
        print("   ✅ 性能差距较小 (<5%)")
        print("   🔧 建议: 继续优化创新组件")
    
    # 5. 下一步行动建议
    print("\n🚀 5. 立即行动建议")
    
    if avg_gap > 15:
        print("   1. 立即运行原版ACGAN-FG，确认基准")
        print("   2. 移除所有创新组件，测试基础性能")
        print("   3. 检查数据预处理和评估方法")
        print("   4. 进行超参数网格搜索")
    else:
        print("   1. 进行消融实验，识别有效组件")
        print("   2. 优化超参数设置")
        print("   3. 改进训练策略")
    
    # 6. 保存结果
    results['timestamp'] = datetime.now().isoformat()
    results['summary'] = {
        'average_gap': avg_gap if valid_groups > 0 else 0,
        'max_asdcgan_acc': max(asdcgan_results.values()) if asdcgan_results else 0,
        'target_accuracy': 80.0,
        'publication_ready': max(asdcgan_results.values()) >= 75.0 if asdcgan_results else False
    }
    
    # 保存到文件
    with open('baseline_verification_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 结果已保存到: baseline_verification_results.json")
    
    return results

def get_best_accuracy_from_logs(exp_dir):
    """从训练日志中提取真实的最佳准确率"""
    log_files = []
    
    # 查找日志文件
    for root, dirs, files in os.walk(exp_dir):
        for file in files:
            if file.endswith('.log') and 'training' in file:
                log_files.append(os.path.join(root, file))
    
    if not log_files:
        return None
    
    # 读取最新的日志文件
    latest_log = max(log_files, key=os.path.getmtime)
    
    try:
        with open(latest_log, 'r') as f:
            lines = f.readlines()
        
        best_acc = 0
        for line in lines:
            if 'Best_Accuracy=' in line:
                # 提取准确率 (格式: Best_Accuracy=60.69%)
                acc_str = line.split('Best_Accuracy=')[1].split('%')[0]
                acc = float(acc_str)
                best_acc = max(best_acc, acc)
        
        return best_acc if best_acc > 0 else None
        
    except Exception as e:
        print(f"   警告: 无法读取日志文件 {latest_log}: {e}")
        return None

def main():
    """主函数"""
    try:
        results = run_baseline_test()
        
        # 输出关键结论
        print("\n" + "=" * 60)
        print("🎯 关键结论")
        print("=" * 60)
        
        summary = results.get('summary', {})
        avg_gap = summary.get('average_gap', 0)
        max_acc = summary.get('max_asdcgan_acc', 0)
        target = summary.get('target_accuracy', 80)
        
        print(f"📊 当前最佳准确率: {max_acc:.2f}%")
        print(f"🎯 目标准确率: {target:.2f}%")
        print(f"📈 需要提升: {target - max_acc:.2f}%")
        print(f"📉 与基准差距: {avg_gap:.2f}%")
        
        if summary.get('publication_ready', False):
            print("✅ 已达到发表标准 (75%+)")
        else:
            print("❌ 未达到发表标准，需要改进")
            
        print("\n🚀 下一步: 执行性能提升计划")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()

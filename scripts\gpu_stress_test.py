#!/usr/bin/env python3
"""
GPU压力测试 - 找出GPU利用率低的原因
"""

import tensorflow as tf
import numpy as np
import time
import datetime

def test_gpu_utilization():
    """测试不同配置下的GPU利用率"""
    
    print("=== GPU压力测试开始 ===")
    print(f"时间: {datetime.datetime.now()}")
    
    # 检查GPU
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if not gpus:
        print("❌ 未检测到GPU")
        return
        
    print(f"✅ 检测到GPU: {gpus[0]}")
    
    # 配置GPU
    try:
        tf.config.experimental.set_memory_growth(gpus[0], True)
        print("✅ GPU内存增长已启用")
    except:
        print("⚠️ 无法设置GPU内存增长")
    
    print("\n=== 测试1: 大矩阵乘法 (应该有高GPU利用率) ===")
    with tf.device('/GPU:0'):
        # 大矩阵运算
        size = 4096
        print(f"创建 {size}x{size} 矩阵...")
        
        start_time = time.time()
        a = tf.random.normal([size, size], dtype=tf.float32)
        b = tf.random.normal([size, size], dtype=tf.float32)
        
        print("开始大矩阵乘法运算...")
        for i in range(10):
            c = tf.matmul(a, b)
            if i % 2 == 0:
                print(f"  运算 {i+1}/10 完成...")
                
        end_time = time.time()
        print(f"大矩阵运算完成，耗时: {end_time - start_time:.2f}秒")
    
    print("\n=== 测试2: 模拟训练批处理 ===")
    with tf.device('/GPU:0'):
        # 模拟不同批处理大小
        for batch_size in [64, 128, 256, 512, 1024]:
            print(f"\n测试批处理大小: {batch_size}")
            
            # 创建模拟数据
            x = tf.random.normal([batch_size, 52], dtype=tf.float32)
            y = tf.random.normal([batch_size, 20], dtype=tf.float32)
            
            # 简单的神经网络
            model = tf.keras.Sequential([
                tf.keras.layers.Dense(256, activation='relu'),
                tf.keras.layers.Dense(256, activation='relu'),
                tf.keras.layers.Dense(256, activation='relu'),
                tf.keras.layers.Dense(20, activation='sigmoid')
            ])
            
            # 编译模型
            model.compile(optimizer='adam', loss='mse')
            
            # 训练几个步骤
            start_time = time.time()
            for step in range(5):
                with tf.GradientTape() as tape:
                    predictions = model(x, training=True)
                    loss = tf.keras.losses.mse(y, predictions)
                
                gradients = tape.gradient(loss, model.trainable_variables)
                model.optimizer.apply_gradients(zip(gradients, model.trainable_variables))
                
            end_time = time.time()
            print(f"  批处理 {batch_size}: {end_time - start_time:.3f}秒")
    
    print("\n=== 测试3: 混合精度训练 ===")
    # 启用混合精度
    tf.keras.mixed_precision.set_global_policy('mixed_float16')
    print("已启用混合精度 (float16)")
    
    with tf.device('/GPU:0'):
        batch_size = 512
        x = tf.random.normal([batch_size, 52], dtype=tf.float16)
        y = tf.random.normal([batch_size, 20], dtype=tf.float16)
        
        # 混合精度模型
        model_fp16 = tf.keras.Sequential([
            tf.keras.layers.Dense(512, activation='relu'),
            tf.keras.layers.Dense(512, activation='relu'),
            tf.keras.layers.Dense(512, activation='relu'),
            tf.keras.layers.Dense(20, activation='sigmoid', dtype='float32')  # 输出层用float32
        ])
        
        model_fp16.compile(optimizer='adam', loss='mse')
        
        start_time = time.time()
        for step in range(10):
            with tf.GradientTape() as tape:
                predictions = model_fp16(x, training=True)
                loss = tf.keras.losses.mse(tf.cast(y, tf.float32), predictions)
            
            gradients = tape.gradient(loss, model_fp16.trainable_variables)
            model_fp16.optimizer.apply_gradients(zip(gradients, model_fp16.trainable_variables))
            
            if step % 2 == 0:
                print(f"  混合精度步骤 {step+1}/10")
                
        end_time = time.time()
        print(f"混合精度训练完成，耗时: {end_time - start_time:.3f}秒")
    
    print("\n=== 测试4: 数据流水线效率 ===")
    # 测试数据加载效率
    def create_dataset(batch_size, buffer_size):
        # 创建大数据集
        data_size = 10000
        x_data = np.random.normal(0, 1, (data_size, 52)).astype(np.float32)
        y_data = np.random.normal(0, 1, (data_size, 20)).astype(np.float32)
        
        dataset = tf.data.Dataset.from_tensor_slices((x_data, y_data))
        dataset = dataset.shuffle(buffer_size)
        dataset = dataset.batch(batch_size)
        dataset = dataset.prefetch(tf.data.AUTOTUNE)
        
        return dataset
    
    # 测试不同配置的数据流水线
    configs = [
        (256, 1000),   # 小批处理，小缓冲
        (512, 5000),   # 中批处理，中缓冲  
        (1024, 10000), # 大批处理，大缓冲
    ]
    
    for batch_size, buffer_size in configs:
        print(f"\n数据流水线测试: batch_size={batch_size}, buffer_size={buffer_size}")
        
        dataset = create_dataset(batch_size, buffer_size)
        
        start_time = time.time()
        count = 0
        for batch_x, batch_y in dataset.take(20):  # 只取20个批次测试
            count += 1
            # 模拟简单计算
            with tf.device('/GPU:0'):
                result = tf.matmul(batch_x, tf.transpose(batch_x))
                
        end_time = time.time()
        print(f"  处理20个批次耗时: {end_time - start_time:.3f}秒")
        print(f"  平均每批次: {(end_time - start_time)/20:.3f}秒")
    
    print("\n=== GPU压力测试完成 ===")
    print("请在另一个终端运行 'watch -n 1 nvidia-smi' 观察GPU利用率变化")

if __name__ == "__main__":
    test_gpu_utilization() 
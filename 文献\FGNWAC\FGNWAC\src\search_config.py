"""
超参数搜索配置模块
基于原Config类扩展，支持系统性的超参数搜索
"""

import os
from train import Config


class HyperparameterSearchConfig(Config):
    """超参数搜索配置类"""
    
    def __init__(self):
        super().__init__()
        
        # ============ 搜索范围定义 ============
        # 阶段1: lambda_ar搜索范围（固定lr=0.0001）
        self.lambda_ar_search_range = [0.1, 0.3, 0.5, 0.8, 1.0, 1.5]
        
        # 阶段2: lr搜索范围（固定最佳lambda_ar）
        self.lr_search_range = [0.00005, 0.0001, 0.0002]
        
        # ============ 搜索策略配置 ============
        # 每个配置的训练轮数
        self.search_epochs = 150
        
        # 早停配置
        self.early_stop_patience = 20  # 连续20轮无改善则停止
        self.min_epochs = 50  # 最少训练轮数
        
        # 评估频率
        self.eval_frequency = 1  # 每轮都评估

        # 显示频率
        self.display_frequency = 1  # 每轮都显示准确率 (1=每轮, 5=每5轮, 10=每10轮)
        
        # ============ 结果记录配置 ============
        # 搜索结果保存目录
        self.search_results_dir = './hyperparameter_search_results'
        os.makedirs(self.search_results_dir, exist_ok=True)
        
        # 搜索日志目录
        self.search_log_dir = './hyperparameter_search_logs'
        os.makedirs(self.search_log_dir, exist_ok=True)
        
        # ============ 实验管理配置 ============
        # 是否保存所有模型（False=只保存最佳）
        self.save_all_models = False
        
        # 是否启用详细日志
        self.verbose_logging = True
        
        # GPU内存管理
        self.clear_gpu_cache = True
        
        # ============ 搜索阶段配置 ============
        self.search_stages = {
            1: {
                'name': 'lambda_ar_search',
                'description': '搜索最佳lambda_ar（固定lr=0.0001）',
                'fixed_params': {'lr': 0.0001},
                'search_params': {'lambda_ar': self.lambda_ar_search_range}
            },
            2: {
                'name': 'lr_search', 
                'description': '搜索最佳lr（固定最佳lambda_ar）',
                'fixed_params': {},  # lambda_ar将在运行时设置
                'search_params': {'lr': self.lr_search_range}
            }
        }
    
    def get_experiment_config(self, stage, param_values, best_params=None):
        """
        获取特定实验的配置
        
        Args:
            stage: 搜索阶段 (1 or 2)
            param_values: 当前参数值字典
            best_params: 之前阶段的最佳参数（用于阶段2）
        
        Returns:
            配置好的Config对象
        """
        # 创建新的配置实例
        config = Config()
        
        # 复制基础配置
        config.feature_dim = self.feature_dim
        config.attribute_dim = self.attribute_dim
        config.latent_dim = self.latent_dim
        config.batch_size = self.batch_size
        config.epochs = self.search_epochs
        
        # 复制损失权重
        config.lambda_vae = self.lambda_vae
        config.lambda_gp = self.lambda_gp
        config.lambda_mi = self.lambda_mi
        
        # 设置搜索阶段的固定参数
        stage_config = self.search_stages[stage]
        for param, value in stage_config['fixed_params'].items():
            setattr(config, param, value)
        
        # 设置当前搜索的参数值
        for param, value in param_values.items():
            setattr(config, param, value)
        
        # 如果是阶段2，设置阶段1的最佳参数
        if stage == 2 and best_params:
            for param, value in best_params.items():
                if param != 'lr':  # lr是阶段2要搜索的参数
                    setattr(config, param, value)
        
        return config
    
    def get_experiment_name(self, stage, param_values, split_name):
        """
        生成实验名称
        
        Args:
            stage: 搜索阶段
            param_values: 参数值字典
            split_name: 数据分组名称
        
        Returns:
            实验名称字符串
        """
        stage_name = self.search_stages[stage]['name']
        param_str = '_'.join([f"{k}_{v}" for k, v in param_values.items()])
        return f"{stage_name}_split_{split_name}_{param_str}"
    
    def get_search_summary(self):
        """获取搜索配置摘要"""
        summary = {
            'total_experiments': len(self.lambda_ar_search_range) + len(self.lr_search_range),
            'stage1_experiments': len(self.lambda_ar_search_range),
            'stage2_experiments': len(self.lr_search_range),
            'epochs_per_experiment': self.search_epochs,
            'early_stop_patience': self.early_stop_patience,
            'estimated_total_time_hours': (len(self.lambda_ar_search_range) + len(self.lr_search_range)) * 2.5,
            'lambda_ar_range': self.lambda_ar_search_range,
            'lr_range': self.lr_search_range
        }
        return summary


class SearchResultsManager:
    """搜索结果管理器"""
    
    def __init__(self, config: HyperparameterSearchConfig):
        self.config = config
        self.results_dir = config.search_results_dir
        
    def get_results_file_path(self, stage, split_name, file_type='csv'):
        """获取结果文件路径"""
        stage_name = self.config.search_stages[stage]['name']
        filename = f"{stage_name}_split_{split_name}.{file_type}"
        return os.path.join(self.results_dir, filename)
    
    def get_detailed_results_path(self, split_name):
        """获取详细结果文件路径"""
        filename = f"detailed_results_split_{split_name}.json"
        return os.path.join(self.results_dir, filename)
    
    def get_best_config_path(self, split_name):
        """获取最佳配置文件路径"""
        filename = f"best_config_split_{split_name}.json"
        return os.path.join(self.results_dir, filename)
    
    def get_analysis_plots_dir(self, split_name):
        """获取分析图表目录"""
        plots_dir = os.path.join(self.results_dir, f"plots_split_{split_name}")
        os.makedirs(plots_dir, exist_ok=True)
        return plots_dir


# 使用示例和测试
if __name__ == "__main__":
    # 创建搜索配置
    search_config = HyperparameterSearchConfig()
    
    # 打印搜索摘要
    summary = search_config.get_search_summary()
    print("🔍 超参数搜索配置摘要:")
    print(f"   总实验数: {summary['total_experiments']}")
    print(f"   阶段1实验数: {summary['stage1_experiments']}")
    print(f"   阶段2实验数: {summary['stage2_experiments']}")
    print(f"   每个实验轮数: {summary['epochs_per_experiment']}")
    print(f"   预估总时间: {summary['estimated_total_time_hours']:.1f}小时")
    print(f"   lambda_ar范围: {summary['lambda_ar_range']}")
    print(f"   lr范围: {summary['lr_range']}")
    
    # 测试实验配置生成
    print("\n🧪 测试实验配置生成:")
    
    # 阶段1测试
    stage1_params = {'lambda_ar': 0.5}
    config1 = search_config.get_experiment_config(1, stage1_params)
    exp_name1 = search_config.get_experiment_name(1, stage1_params, 'A')
    print(f"   阶段1实验: {exp_name1}")
    print(f"   配置: lr={config1.lr}, lambda_ar={config1.lambda_ar}")
    
    # 阶段2测试
    stage2_params = {'lr': 0.0002}
    best_params = {'lambda_ar': 0.8}
    config2 = search_config.get_experiment_config(2, stage2_params, best_params)
    exp_name2 = search_config.get_experiment_name(2, stage2_params, 'A')
    print(f"   阶段2实验: {exp_name2}")
    print(f"   配置: lr={config2.lr}, lambda_ar={config2.lambda_ar}")
    
    print("\n✅ 搜索配置系统测试完成!")

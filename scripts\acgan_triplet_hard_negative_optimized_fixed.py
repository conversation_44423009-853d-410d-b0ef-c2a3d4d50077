import numpy as np
import pandas as pd
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test_hard_negative import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler
from data_pipeline import OptimizedDataPipeline, monitor_gpu, check_docker_gpu_config

# 配置GPU显存按需增长
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(e)

# 在训练开始前检查GPU配置
print("=" * 60)
print("GPU配置检查和优化")
print("=" * 60)
check_docker_gpu_config()
monitor_gpu()
print("=" * 60)

if __name__ == "__main__":
    print("优化版难负例挖掘模型 - 简化版本")
    print("由于缩进问题，暂时使用原始版本运行")
    print("请使用: python acgan_triplet_hard_negative.py E") 
import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics.pairwise import cosine_distances, euclidean_distances
import json
from data_pipeline import OptimizedDataPipeline, monitor_gpu, check_docker_gpu_config

# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    tf.config.set_visible_devices(gpus[0], 'GPU')
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    print(e)

# 在训练开始前检查GPU配置
print("=" * 60)
print("GPU配置检查和优化")
print("=" * 60)
check_docker_gpu_config()
monitor_gpu()
print("=" * 60)

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        self.W_q = self.add_weight(shape=(input_shape[-1], input_shape[-1]),
                                   initializer='glorot_uniform',
                                   trainable=True)
        self.W_k = self.add_weight(shape=(input_shape[-1], input_shape[-1]),
                                   initializer='glorot_uniform',
                                   trainable=True)
        self.W_v = self.add_weight(shape=(input_shape[-1], input_shape[-1]),
                                   initializer='glorot_uniform',
                                   trainable=True)
        super(SelfAttention, self).build(input_shape)

    def call(self, inputs):
        q = tf.matmul(inputs, self.W_q)
        k = tf.matmul(inputs, self.W_k)
        v = tf.matmul(inputs, self.W_v)
        
        # 修复混合精度类型问题
        scale = tf.sqrt(tf.cast(tf.shape(q)[-1], inputs.dtype))
        attention_weights = tf.nn.softmax(tf.matmul(q, k, transpose_b=True) / scale)
        attention_output = tf.matmul(attention_weights, v)
        return attention_output

def residual_block(x, units):
    shortcut = x
    
    y = Dense(units)(x)
    y = BatchNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = BatchNormalization()(y)
    
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class TaskDifficultyAnalyzer:
    def __init__(self, distance_metric='euclidean'):
        self.distance_metric = distance_metric
        
    def compute_task_difficulty(self, seen_attributes, unseen_attributes, group_name):
        if self.distance_metric == 'euclidean':
            distance_matrix = euclidean_distances(seen_attributes, unseen_attributes)
        else:
            distance_matrix = cosine_distances(seen_attributes, unseen_attributes)
            
        min_distances = np.min(distance_matrix, axis=0)
        difficulty_score = np.mean(min_distances)
        
        print(f"任务难度分析 ({group_name}):")
        print(f"  距离度量: {self.distance_metric}")
        print(f"  平均最小距离: {difficulty_score:.6f}")
        print(f"  属性相似度范围: [{np.min(min_distances):.6f}, {np.max(min_distances):.6f}]")
        
        return difficulty_score

class AdaptiveWeightScheduler:
    def __init__(self, base_lambda_triplet=10, base_lambda_cla=10, alpha=1.0, beta=0.5):
        self.base_lambda_triplet = base_lambda_triplet
        self.base_lambda_cla = base_lambda_cla
        self.alpha = alpha
        self.beta = beta
    
    def compute_adaptive_weights(self, difficulty_score):
        
        
        normalized_difficulty = np.tanh((difficulty_score - 1.0) * 0.5)
        
        triplet_adjustment = 1.0 + normalized_difficulty * 0.3
        cla_adjustment = 1.0 - normalized_difficulty * 0.2
        
        triplet_adjustment = np.clip(triplet_adjustment, 0.7, 1.5)
        cla_adjustment = np.clip(cla_adjustment, 0.7, 1.5)
        
        lambda_triplet_adaptive = self.base_lambda_triplet * triplet_adjustment
        lambda_cla_adaptive = self.base_lambda_cla * cla_adjustment
        
        return {
            'lambda_triplet': lambda_triplet_adaptive,
            'lambda_cla': lambda_cla_adaptive,
            'difficulty_score': difficulty_score,
            'adjustment_ratio_triplet': triplet_adjustment,
            'adjustment_ratio_cla': cla_adjustment
        }

class ExperimentManager:
    def __init__(self, target_group='E'):
        self.target_group = target_group
        self.group_configs = {
            'A': [1, 6, 14],
            'B': [4, 7, 10], 
            'C': [8, 11, 12],
            'D': [2, 3, 5],
            'E': [9, 13, 15],
        }
    
    def get_group_data(self, train_data, test_data):
        test_classes = self.group_configs[self.target_group]
        all_classes = list(range(1, 16))
        seen_classes = [i for i in all_classes if i not in test_classes]
        
        train_X_by_class = {i: train_data[f'training_samples_{i}'] for i in seen_classes}
        train_Y_by_class = {i: train_data[f'training_attribute_{i}'] for i in seen_classes}
        
        test_X_by_class = {i: test_data[f'testing_samples_{i}'] for i in test_classes}
        test_Y_by_class = {i: test_data[f'testing_attribute_{i}'] for i in test_classes}
        
        return {
            'train_X_by_class': train_X_by_class,
            'train_Y_by_class': train_Y_by_class,
            'test_X_by_class': test_X_by_class,
            'test_Y_by_class': test_Y_by_class,
            'seen_classes': seen_classes,
            'unseen_classes': test_classes
        }

class Zero_shot_Adaptive_Optimized:
    def __init__(self, group='E', use_adaptive=True, alpha=1.0, beta=0.5, use_optimized_pipeline=True):
        self.group = group
        self.use_adaptive = use_adaptive
        self.use_optimized_pipeline = use_optimized_pipeline
        
        # 基础参数
        self.data_lenth = 52
        self.sample_shape = (self.data_lenth,)
        self.feature_dim = 256
        self.feature_shape = (256,)
        self.num_classes = 15
        self.latent_dim = 50
        self.noise_shape = (self.latent_dim, 1)
        self.n_critic = 1
        self.crl = True
        
        # 基础损失权重
        self.base_lambda_cla = 10
        self.base_lambda_triplet = 10
        self.lambda_crl = 0.01
        
        # 当前使用的权重（可能是自适应的）
        self.lambda_cla = self.base_lambda_cla
        self.lambda_triplet = self.base_lambda_triplet
        
        # 其他参数
        self.bound = True
        self.mi_weight = 0.001
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        # 自适应组件
        self.difficulty_analyzer = TaskDifficultyAnalyzer(distance_metric='euclidean')
        self.weight_scheduler = AdaptiveWeightScheduler(
            base_lambda_triplet=self.base_lambda_triplet,
            base_lambda_cla=self.base_lambda_cla,
            alpha=alpha,
            beta=beta
        )
        self.experiment_manager = ExperimentManager(target_group=group)
        
        # 初始化优化数据流水线
        if self.use_optimized_pipeline:
            self.data_pipeline = OptimizedDataPipeline(use_mixed_precision=True)
            print("已启用优化数据流水线和混合精度训练")
        
        # 优化器
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        
        # 构建模型
        self.autoencoder = self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c = self.build_classifier()
        
        print(f"优化版自适应Zero-shot模型初始化完成 (Group {self.group})")

    def build_autoencoder(self):
        sample = Input(shape=self.sample_shape)
        
        e1 = Dense(256)(sample)
        e1 = LeakyReLU(alpha=0.2)(e1)
        e1 = BatchNormalization()(e1)
        
        e2 = residual_block(e1, 256)
        e3 = residual_block(e2, 256)
        
        e3_attention = SelfAttention()(e3)
        
        feature = Dense(256)(e3_attention)
        feature = BatchNormalization()(feature)
        
        d1 = Dense(256, activation='relu')(feature)
        d2 = Dense(128, activation='relu')(d1)
        d3 = Dense(64, activation='relu')(d2)
        output_sample = Dense(self.data_lenth, activation='linear')(d3)
        
        encoder = Model(sample, feature)
        self.encoder = encoder
        
        return Model(sample, [feature, output_sample])

    def build_discriminator(self):
        feature = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        feature_embedding = Dense(128)(feature)
        feature_embedding = LeakyReLU(alpha=0.2)(feature_embedding)
        
        attribute_embedding = Dense(128)(attribute)
        attribute_embedding = LeakyReLU(alpha=0.2)(attribute_embedding)
        
        d_input = concatenate([feature_embedding, attribute_embedding])
        
        d1 = Dense(128)(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        d1 = BatchNormalization()(d1)
        
        d2 = residual_block(d1, 128)
        d3 = residual_block(d2, 64)
        
        validity = Dense(1)(d3)
        
        return Model([feature, attribute], validity)

    def build_generator(self):
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        noise_embedding = Flatten()(noise)
        attribute_embedding = Dense(self.latent_dim)(attribute)
        
        g_input = concatenate([noise_embedding, attribute_embedding])

        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = BatchNormalization()(g1)

        g2 = residual_block(g1, 256)
        g3 = residual_block(g2, 256)
        
        g3_attention = SelfAttention()(g3)
        
        generated_feature = Dense(256)(g3_attention)
        generated_feature = BatchNormalization()(generated_feature)

        return Model([noise, attribute], generated_feature)

    def build_classifier(self):
        sample = Input(shape=self.feature_shape)

        c0 = sample
        c1 = Dense(100)(c0)
        c1 = LeakyReLU(alpha=0.2)(c1)
        
        c2 = Dense(50)(c1)
        c2 = LeakyReLU(alpha=0.2)(c2)
        hidden_ouput = c2
               
        c3 = Dense(20, activation="sigmoid")(c2)
        predict_attribute = c3
        
        return Model(sample, [hidden_ouput, predict_attribute])

    def compute_task_difficulty(self, seen_attributes, unseen_attributes):
        difficulty_score = self.difficulty_analyzer.compute_task_difficulty(
            seen_attributes, unseen_attributes, f"Group_{self.group}"
        )
        
        if self.use_adaptive:
            adaptive_weights = self.weight_scheduler.compute_adaptive_weights(difficulty_score)
            self.lambda_triplet = adaptive_weights['lambda_triplet']
            self.lambda_cla = adaptive_weights['lambda_cla']
            return difficulty_score, adaptive_weights
        else:
            return difficulty_score, {
                'lambda_triplet': self.base_lambda_triplet,
                'lambda_cla': self.base_lambda_cla,
                'difficulty_score': difficulty_score,
                'adjustment_ratio_triplet': 1.0,
                'adjustment_ratio_cla': 1.0
            }

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        """Wasserstein loss function with mixed precision support"""
        # 确保类型一致性，处理混合精度
        y_true = tf.cast(y_true, y_pred.dtype)
        return K.mean(y_true * y_pred)

    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est

    def mi_penalty_loss(self, x, z):
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)

    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)
        
        mi_penalty = 0
        if self.bound == True:
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss

    def cycle_rank_loss(self, anchor, positive, negative):
        return self.triplet_loss(anchor, positive, negative)

    def train_optimized(self, epochs, batch_size=512, log_file=None):
        """使用优化数据流水线的训练方法"""
        start_time = datetime.datetime.now()
        
        accuracy_list_1 = []
        accuracy_list_2 = []
        accuracy_list_3 = []
        accuracy_list_4 = []
        
        PATH_train = './dataset_train_case1.npz'
        PATH_test = './dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        print(f"开始为Group {self.group}准备优化数据流水线，批处理大小: {batch_size}")
        
        # 使用优化数据流水线准备数据
        data_info = self.data_pipeline.prepare_data_adaptive(
            train_data, test_data, self.group, batch_size=batch_size, shuffle_buffer=20000
        )
        
        train_dataset = data_info['train_dataset']
        train_X_by_class = data_info['train_X_by_class'] 
        train_Y_by_class = data_info['train_Y_by_class']
        seen_classes = data_info['seen_classes']
        unseen_classes = data_info['unseen_classes']
        testdata = data_info['test_X']
        test_attributelabel = data_info['test_Y']

        # 计算任务难度和自适应权重
        seen_attributes = np.array([train_Y_by_class[i][0] for i in seen_classes])
        unseen_attributes = np.array([test_attributelabel[i*len(testdata)//3] for i in range(3)])
        
        difficulty_score, adaptive_weights = self.compute_task_difficulty(seen_attributes, unseen_attributes)
        
        # 记录任务难度信息
        if log_file:
            log_file.write(f"## 优化版自适应任务难度调整分析\n\n")
            log_file.write(f"**目标分组**: Group {self.group}\n")
            log_file.write(f"**优化数据流水线**: 启用\n")
            log_file.write(f"**批处理大小**: {batch_size}\n")
            log_file.write(f"**混合精度训练**: 启用\n")
            log_file.write(f"**任务难度得分**: {difficulty_score:.6f}\n")
            log_file.write(f"**自适应模式**: {'启用' if self.use_adaptive else '禁用'}\n")
            log_file.write(f"**seen类别**: {seen_classes}\n")
            log_file.write(f"**unseen类别**: {unseen_classes}\n\n")
            
            log_file.write(f"### 损失权重配置\n")
            log_file.write(f"- **Triplet Loss权重**: {adaptive_weights['lambda_triplet']:.6f} (调整比例: {adaptive_weights['adjustment_ratio_triplet']:.3f})\n")
            log_file.write(f"- **分类Loss权重**: {adaptive_weights['lambda_cla']:.6f} (调整比例: {adaptive_weights['adjustment_ratio_cla']:.3f})\n")
            log_file.write(f"- **基础Triplet权重**: {self.base_lambda_triplet}\n")
            log_file.write(f"- **基础分类权重**: {self.base_lambda_cla}\n\n")
            log_file.write("---\n\n")
            log_file.flush()
        
        print(f"开始优化版训练 Group {self.group} (自适应模式: {self.use_adaptive})")
        print(f"任务难度: {difficulty_score:.6f}")
        print(f"Triplet权重: {self.lambda_triplet:.6f}, 分类权重: {self.lambda_cla:.6f}")
        print(f"批处理大小: {batch_size}")

        for epoch in range(epochs):
            epoch_losses = {'ae_c': [], 'm': [], 'd': [], 'g': []}
            batch_count = 0
            
            for batch_data in train_dataset:
                train_x, train_y, train_labels = batch_data
                current_batch_size = tf.shape(train_x)[0]
                batch_count += 1
                
                # 监控GPU使用情况（每100个批次）
                if batch_count % 100 == 0:
                    print(f"Epoch {epoch}, Batch {batch_count} - 监控GPU状态:")
                    monitor_gpu()
                
                # Autoencoder和Classifier训练
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                    feature, output_sample = self.autoencoder(train_x)
                    autoencoder_loss = mean_squared_error(train_x, output_sample)

                    hidden_ouput_c, predict_attribute_c = self.c(feature)
                    c_loss = self.classification_loss(feature, train_y, hidden_ouput_c, predict_attribute_c)

                    total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c
                epoch_losses['ae_c'].append(float(tf.reduce_mean(total_ac_loss)))

                # Triplet Loss度量学习 - 优化版采样
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                # 从当前批次生成triplet样本
                train_labels_np = train_labels.numpy()
                positive_samples = []
                negative_samples = []
                
                for label in train_labels_np:
                    # 正样本采样
                    if label in train_X_by_class and len(train_X_by_class[label]) > 0:
                        pos_class_samples = train_X_by_class[label]
                        pos_idx = np.random.choice(len(pos_class_samples))
                        positive_samples.append(pos_class_samples[pos_idx])
                    else:
                        positive_samples.append(train_x[0].numpy())  # 备用方案
                    
                    # 负样本采样
                    available_classes = [c for c in seen_classes if c != label and c in train_X_by_class]
                    if available_classes:
                        neg_class = np.random.choice(available_classes)
                        neg_class_samples = train_X_by_class[neg_class]
                        neg_idx = np.random.choice(len(neg_class_samples))
                        negative_samples.append(neg_class_samples[neg_idx])
                    else:
                        negative_samples.append(train_x[0].numpy())  # 备用方案

                positive_samples = tf.constant(np.array(positive_samples), dtype=tf.float32)
                negative_samples = tf.constant(np.array(negative_samples), dtype=tf.float32)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(train_x)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))
                epoch_losses['m'].append(float(m_loss))

                # 判别器训练
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False
                
                with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(current_batch_size, self.latent_dim, 1))
                    Fake_feature = self.g([noise, train_y])
                    Real_validity = self.d([feature, train_y])
                    Fake_validity = self.d([Fake_feature, train_y])
                    
                    valid = -tf.ones((current_batch_size, 1))
                    fake = tf.ones((current_batch_size, 1))
                    
                    d_loss_real = self.wasserstein_loss(valid, Real_validity)
                    d_loss_fake = self.wasserstein_loss(fake, Fake_validity)
                    d_loss = (d_loss_real + d_loss_fake) / 2

                grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))
                epoch_losses['d'].append(float(d_loss))

                # 生成器训练
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = False
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                    noise_g = tf.random.normal(shape=(current_batch_size, self.latent_dim, 1))
                    Fake_feature_g = self.g([noise_g, train_y])
                    Fake_validity_g = self.d([Fake_feature_g, train_y])
                    valid = -tf.ones((current_batch_size, 1))
                    adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
              
                    fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                    classification_loss = self.classification_loss(Fake_feature_g, train_y, fake_hidden_ouput_g, Fake_classification_g)
                    
                    # 使用自适应权重的Triplet loss
                    g_anchor_features = Fake_feature_g
                    g_positive_features = self.encoder(positive_samples)
                    g_negative_features = self.encoder(negative_samples)
                    triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                    
                    cycle_rank_loss = 0
                    if self.crl == True:
                        reconstructed_feature = self.g([noise_g, Fake_classification_g])
                        
                        # 创建不相似的属性
                        batch_size_np = train_labels_np.shape[0]
                        negative_attributes = []
                        for label in train_labels_np:
                            available_classes = [c for c in seen_classes if c != label and c in train_Y_by_class]
                            if available_classes:
                                neg_class = np.random.choice(available_classes)
                                negative_attributes.append(train_Y_by_class[neg_class][0])
                            else:
                                negative_attributes.append(train_y[0].numpy())
                        
                        negative_attributes = tf.constant(np.array(negative_attributes), dtype=tf.float32)
                        unsimilar_generated_feature = self.g([noise_g, negative_attributes])

                        cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                               
                    # 使用自适应权重
                    total_loss = adversarial_loss + self.lambda_cla * classification_loss + self.lambda_triplet * triplet_loss_g + self.lambda_crl * cycle_rank_loss
                              
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                epoch_losses['g'].append(float(tf.reduce_mean(total_loss)))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                if batch_count % 10 == 0:  # 每10个批次打印一次
                    print("[Epoch %d/%d][Batch %d][AE+C loss: %f][M loss: %f][D loss: %f][G loss %05f] λ_triplet: %.3f λ_cla: %.3f time: %s" \
                         % (epoch, epochs,
                           batch_count,
                           np.mean(epoch_losses['ae_c'][-10:]), 
                           np.mean(epoch_losses['m'][-10:]),
                           np.mean(epoch_losses['d'][-10:]),
                           np.mean(epoch_losses['g'][-10:]),
                           self.lambda_triplet,
                           self.lambda_cla,
                           elapsed_time))
        
            # 每个epoch结束后进行评估
            if epoch % 1 == 0:
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis(
                    2000, testdata, test_attributelabel, self.autoencoder, self.g, self.c, unseen_classes)

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                      %(epoch, epochs, max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)))
                      
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
        
        return {
            'best_accuracy': max(accuracy_list_4) if accuracy_list_4 else 0.0,
            'accuracy_lsvm': max(accuracy_list_1) if accuracy_list_1 else 0.0,
            'accuracy_nrf': max(accuracy_list_2) if accuracy_list_2 else 0.0,
            'accuracy_pnb': max(accuracy_list_3) if accuracy_list_3 else 0.0,
            'accuracy_mlp': max(accuracy_list_4) if accuracy_list_4 else 0.0,
        }

def run_optimized_experiments(target_group='E', epochs=500, batch_size=512):
    """运行优化版实验"""
    
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    
    print("=== 开始优化版自适应权重实验 ===")
    log_filename_optimized = os.path.join(results_dir, start_run_time.strftime("%Y%m%d%H%M") + f"_optimized_adaptive_Group{target_group}.md")
    
    with open(log_filename_optimized, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 优化版自适应任务难度调整实验 - Group {target_group}\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**优化特性**: 数据流水线优化 + 混合精度 + 增大批处理\n")
        log_file.write(f"**批处理大小**: {batch_size}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan_optimized = Zero_shot_Adaptive_Optimized(group=target_group, use_adaptive=True, use_optimized_pipeline=True)
        results_optimized = gan_optimized.train_optimized(epochs=epochs, batch_size=batch_size, log_file=log_file)
        
        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")
        log_file.write(f"**最佳准确率**: {results_optimized['best_accuracy']:.4f}\n")

    print(f"=== 优化版实验完成 ===")
    print(f"最佳准确率: {results_optimized['best_accuracy']:.4f}")
    print(f"详细结果已保存至: {log_filename_optimized}")
    
    return results_optimized

if __name__ == '__main__':
    TARGET_GROUP = 'B'  # 可选: 'A', 'B', 'C', 'D', 'E'
    
    print(f"开始运行 Group {TARGET_GROUP} 的优化版自适应实验")
    print("优化特性: 数据流水线 + 混合精度训练 + 大批处理 + GPU监控")
    results = run_optimized_experiments(target_group=TARGET_GROUP, epochs=500, batch_size=512) 
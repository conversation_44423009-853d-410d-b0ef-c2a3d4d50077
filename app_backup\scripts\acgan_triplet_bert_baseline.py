import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.regularizers import l2 # 导入l2正则化
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import sys
sys.path.append('..')
import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from transformers import BertTokenizer, TFBertModel

# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    tf.config.set_visible_devices(gpus[0], 'GPU')
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    print(e)

def generate_bert_attributes():
    """生成TEP故障的BERT属性嵌入"""
    fault_descriptions = {
        1: "Step change in A/C feed ratio, input B constant.",
        2: "Step change in input B composition, A/C ratio constant.", 
        3: "Step change in D feed temperature.",
        4: "Step change in Reactor cooling water inlet temperature.",
        5: "Step change in Condenser cooling water inlet temperature.",
        6: "Step change, A feed loss.",
        7: "Step change, C header pressure loss.",
        8: "Random variation in A, B, and C feed composition.",
        9: "Random variation in D feed temperature.",
        10: "Random variation in C feed temperature.",
        11: "Random variation in Reactor cooling water inlet temperature.",
        12: "Random variation in Condenser cooling water inlet temperature.",
        13: "Slow drift in Reaction kinetics.",
        14: "Sticking of Reactor cooling water valve.",
        15: "Sticking of Condenser cooling water valve."
    }
    
    local_model_path = "../models/bert/bert-base-uncased"
    print(f"正从本地路径加载BERT模型: {local_model_path}")

    try:
        tokenizer = BertTokenizer.from_pretrained(local_model_path)
        bert_model = TFBertModel.from_pretrained(local_model_path)
    except Exception as e:
        print(f"❌ 加载本地BERT模型失败: {e}")
        print("请确认以下几点:")
        print(f"1. 模型已下载到 '{local_model_path}' 文件夹。")
        print("2. 文件夹中包含 'tf_model.h5', 'config.json', 'vocab.txt' 等文件。")
        raise # 抛出异常，中断程序

    new_attribute_embeddings = {}
    for i in range(1, 16):
        description = fault_descriptions[i]
        inputs = tokenizer(description, return_tensors="tf", padding=True, truncation=True, max_length=64)
        outputs = bert_model(inputs)
        cls_embedding = outputs.last_hidden_state[:, 0, :]
        new_attribute_embeddings[i] = cls_embedding.numpy()
        print(f"为故障 {i} 生成嵌入，形状: {cls_embedding.shape}")
    
    # 整理成数组并标准化
    all_embeddings_list = [new_attribute_embeddings[i] for i in range(1, 16)]
    all_embeddings_array = np.vstack(all_embeddings_list)
    
    # 标准化处理 -> 改为MinMaxScaler以保证数据尺度一致
    scaler = MinMaxScaler()
    all_embeddings_normalized = scaler.fit_transform(all_embeddings_array)
    
    # 保存到项目根目录
    np.save('../tep_bert_attributes.npy', all_embeddings_normalized)
    np.save('../bert_scaler.npy', scaler) # 保存整个scaler对象
    print("BERT属性已使用MinMaxScaler进行归一化，并保存到 ../tep_bert_attributes.npy")
    
    return all_embeddings_normalized

def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = BatchNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = BatchNormalization()(y)
    
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        x_reshaped = K.expand_dims(x, axis=1)
        q = self.query(x_reshaped)
        k = self.key(x_reshaped)
        v = self.value(x_reshaped)
        attention_scores = K.batch_dot(q, k, axes=[2, 2])
        attention_probs = K.softmax(attention_scores)
        context = K.batch_dot(attention_probs, v)
        context = K.squeeze(context, axis=1)
        return x + self.gamma * context
        
class Zero_shot_BERT():
    def __init__(self):
        # --- 基本参数 ---
        self.data_lenth = 52
        self.sample_shape = (self.data_lenth,)
        self.feature_dim = 256
        self.feature_shape = (self.feature_dim,)
        self.latent_dim = 50
        self.noise_shape = (self.latent_dim, 1)
        self.n_critic = 5 # 原始GAN的经典设置
        
        # --- BERT属性参数 ---
        self.attribute_dim = 768
        self.attribute_shape = (self.attribute_dim,)

        # --- 损失权重 (参考原始ACGAN_FG.py的成功经验) ---
        self.lambda_cla = 10.0 # 分类损失
        self.lambda_comp = 10.0 # 比较损失 (代替三元组损失)
        self.lambda_gp = 10.0  # 梯度惩罚
        self.lambda_triplet = 1.0 # 三元组损失权重
        self.triplet_margin = 1.0 # 三元组损失的边际值

        # --- 优化器 ---
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(0.0001, 0.5, 0.9)
        self.d_optimizer = tf.keras.optimizers.Adam(0.0001, 0.5, 0.9)
        self.g_optimizer = tf.keras.optimizers.Adam(0.0001, 0.5, 0.9)
        self.c_optimizer = tf.keras.optimizers.Adam(0.0001, 0.5, 0.9)
        self.m_optimizer = tf.keras.optimizers.Adam(0.0001, 0.5, 0.9) # M for Matcher/Comparator
        
        # --- 构建模型 ---
        self.g = self.build_generator()
        self.d = self.build_discriminator()
        self.c = self.build_classifier()
        self.m = self.build_comparator() # 引入比较器
        self.encoder = self.build_encoder()

        # 加载BERT属性
        try:
            self.bert_attributes = np.load('../tep_bert_attributes.npy')
            print(f"✅ 已加载BERT属性，形状: {self.bert_attributes.shape}")
        except FileNotFoundError:
            print("📥 BERT属性文件不存在，正在生成...")
            self.bert_attributes = generate_bert_attributes()

    def build_encoder(self):
        sample = Input(shape=self.sample_shape)
        a1 = Dense(100, kernel_regularizer=l2(0.001))(sample)
        a1 = LeakyReLU(alpha=0.2)(a1)
        a1 = BatchNormalization()(a1)
        a2 = Dense(200, kernel_regularizer=l2(0.001))(a1)
        a2 = LeakyReLU(alpha=0.2)(a2)
        a2 = BatchNormalization()(a2)
        feature = Dense(256, kernel_regularizer=l2(0.001))(a2)
        return Model(sample, feature)

    def build_discriminator(self):
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=self.attribute_shape, dtype='float32')
        label_embedding = Dense(self.feature_dim, use_bias=False)(attribute)
        d_input = multiply([sample_input, label_embedding])
        d1 = Dense(256)(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        d2 = Dense(128)(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)
        validity = Dense(1)(d2)
        return Model([sample_input, attribute], validity)

    def build_generator(self):
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=self.attribute_shape, dtype='float32')
        noise_embedding = Flatten()(noise)
        attribute_embedding = Dense(128)(attribute)
        attribute_embedding = Dense(self.latent_dim)(attribute_embedding)
        g_input = concatenate([noise_embedding, attribute_embedding])
        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = BatchNormalization()(g1)
        g2 = residual_block(g1, 256)
        g3 = SelfAttention()(g2)
        generated_feature = Dense(self.feature_dim)(g3)
        return Model([noise, attribute], generated_feature)

    def build_classifier(self):
        sample = Input(shape=self.feature_shape)
        c1 = Dense(512)(sample)
        c1 = LeakyReLU(alpha=0.2)(c1)
        hidden_output = c1
        c2 = Dense(1024)(hidden_output)
        c2 = LeakyReLU(alpha=0.2)(c2)
        predict_attribute = Dense(self.attribute_dim, activation="linear")(c2)
        return Model(sample, [hidden_output, predict_attribute])
    
    def build_comparator(self):
        # 新增：复刻原始代码的比较器
        def conv_block(x, filters, stride, kernel_size=3):
            x = Conv1D(filters, kernel_size=kernel_size, strides=stride, padding='same')(x)
            x = BatchNormalization()(x)
            x = ReLU()(x)
            return x
        s1_input = Input(shape=self.feature_shape)
        s2_input = Input(shape=self.feature_shape)
        s1 = Reshape((self.feature_dim, 1))(s1_input)
        s2 = Reshape((self.feature_dim, 1))(s2_input)
        concatenated = concatenate([s1, s2], axis=-1)
        c1 = conv_block(concatenated, filters=16, stride=2)
        c2 = conv_block(c1, filters=32, stride=2)
        c3 = conv_block(c2, filters=64, stride=1)
        c4 = Flatten()(c3)
        c5 = Dense(1500)(c4)
        c5 = LeakyReLU(alpha=0.2)(c5)
        c5 = BatchNormalization()(c5)
        c6 = Dense(100)(c5)
        c6 = LeakyReLU(alpha=0.2)(c6)
        c6 = BatchNormalization()(c6)
        similarity = Dense(1, activation="sigmoid")(c6)
        return Model([s1_input, s2_input], similarity)

    def wasserstein_loss(self, y_true, y_pred):
        return tf.reduce_mean(y_true * y_pred)

    def classification_loss(self, y_true, y_pred):
        # 使用余弦相似度
        return tf.reduce_mean(1.0 - tf.keras.losses.cosine_similarity(y_true, y_pred, axis=-1))

    def comparison_loss(self, y_true, y_pred):
        # 比较器使用经典的二元交叉熵
        return tf.keras.losses.binary_crossentropy(y_true, y_pred)

    def train(self, epochs, batch_size, log_file=None, test_class_indices=None):
        start_time = datetime.datetime.now()
        
        accuracy_list_1 = []
        accuracy_list_2 = []
        accuracy_list_3 = []
        accuracy_list_4 = []
        
        valid = -np.ones((batch_size, 1))
        fake = np.ones((batch_size, 1))
        
        PATH_train = '../data/dataset_train_case1.npz'
        PATH_test = '../data/dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        # 使用传入的测试类别，如果没有传入则默认使用E组
        if test_class_indices is None:
            test_class_indices = [9, 13, 15]  # 默认E组
        
        # 根据测试类别，确定训练类别（Seen classes）
        all_class_indices = list(range(1, 16)) # 1-15
        seen_class_indices = [i for i in all_class_indices if i not in test_class_indices]
        
        # 加载训练数据
        train_X_by_class = {i: train_data[f'training_samples_{i}'] for i in seen_class_indices}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        # 🔑 使用BERT属性替代原始属性
        all_train_Y = self.bert_attributes[all_train_labels - 1]

        # 加载测试数据
        test_X_by_class = {i: test_data[f'testing_samples_{i}'] for i in test_class_indices}
        test_X = np.concatenate([v for k, v in test_X_by_class.items()])
        test_classlabel = np.concatenate([np.full(len(v), k) for k, v in test_X_by_class.items()])
        test_Y = self.bert_attributes[test_classlabel - 1]  # 🔑 测试集也使用BERT属性

        # 数据标准化
        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # 重新组织缩放后的数据
        current_pos = 0
        for i in seen_class_indices:
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata = all_train_X
        train_attributelabel = all_train_Y
        train_classlabel = all_train_labels
        
        testdata = test_X
        test_attributelabel = test_Y
       
        num_batches = int(traindata.shape[0] / batch_size)
        
        print(f"🔥 方案A训练开始：使用{self.attribute_dim}维BERT属性")
        print(f"训练样本: {len(traindata)}, 测试类别: {test_class_indices}")
               
        for epoch in range(epochs):
            
            for batch_i in range(num_batches):
                
                start_i = batch_i * batch_size
                end_i = (batch_i + 1) * batch_size
                
                train_x = traindata[start_i:end_i]
                train_y = train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # 编码器和分类器训练
                with tf.GradientTape(persistent=True) as tape_auto_c:
                    feature = self.encoder(train_x)
                    
                    hidden_output_c, predict_attribute_c = self.c(feature)
                    c_loss = self.classification_loss(train_y, predict_attribute_c)

                grads_c = tape_auto_c.gradient(c_loss, self.encoder.trainable_weights + self.c.trainable_weights)
                self.c_optimizer.apply_gradients(zip(grads_c, self.encoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c

                # 三元组损失学习
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []
                for label in train_labels:
                    pos_class_samples = train_X_by_class[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    
                    neg_class = np.random.choice([c for c in seen_class_indices if c != label])
                    neg_class_samples = train_X_by_class[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                # 判别器训练
                for _ in range(self.n_critic):
                    with tf.GradientTape() as tape_d:
                        noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                        fake_feature = self.g([noise, train_y])
                        real_feature = self.encoder(train_x)
            
                        real_validity = self.d([real_feature, train_y])
                        fake_validity = self.d([fake_feature, train_y])  
                                               
                        d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                        d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                        d_loss = d_loss_real + d_loss_fake
                      
                    grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                    self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # 生成器训练
                with tf.GradientTape() as tape_g:
                    noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature_g = self.g([noise_g, train_y])
                    fake_validity_g = self.d([fake_feature_g, train_y])
                    adversarial_loss = self.wasserstein_loss(valid, fake_validity_g)
              
                    fake_hidden_output_g, fake_classification_g = self.c(fake_feature_g)
                    classification_loss_g = self.classification_loss(train_y, fake_classification_g)
                    
                    # 生成器的三元组损失
                    g_anchor_features = fake_feature_g
                    g_positive_features = self.encoder(positive_samples)
                    g_negative_features = self.encoder(negative_samples)
                    triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                    
                    g_loss = adversarial_loss + self.lambda_cla * classification_loss_g + self.lambda_triplet * triplet_loss_g
                              
                grads_g = tape_g.gradient(g_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print ("[Epoch %d/%d][Batch %d/%d][C loss: %f][M loss: %f][D loss: %f][G loss %05f ]time: %s " \
                 % (epoch, epochs,
                   batch_i, num_batches,
                   c_loss, 
                   m_loss,
                   d_loss,
                   g_loss,                                                                                                              
                   elapsed_time))
        
            if epoch % 10 == 0:  # 每10个epoch测试一次
                # 创建一个兼容的autoencoder包装器，因为test.py期望(feature, decoded)两个返回值
                class AutoencoderWrapper:
                    def __init__(self, encoder):
                        self.encoder = encoder
                    
                    def __call__(self, x):
                        features = self.encoder(x)
                        # 返回特征和虚拟的解码结果（实际不使用）
                        return features, features
                
                autoencoder_wrapper = AutoencoderWrapper(self.encoder)
                
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis(
                    2000, testdata, test_attributelabel, autoencoder_wrapper, self.g, self.c, test_class_indices)  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                  %(epoch, epochs, max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)))
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
            
        best_accuracy = max([max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)])
        print('🎯 方案A完成! best_acc:{:.4f}'.format(best_accuracy))
        if log_file:
            log_file.write(f'🎯 方案A完成! best_acc:{best_accuracy:.4f}\n')
            log_file.flush()
        
        return best_accuracy

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

if __name__ == '__main__':
    TARGET_GROUP = 'E'  # 先用E组验证方案A
    
    GROUP_CONFIGS = {
        'A': [1, 6, 14],   
        'B': [4, 7, 10],   
        'C': [8, 11, 12],  
        'D': [2, 3, 5],    
        'E': [9, 13, 15],  
    }
    
    results_dir = "../结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + f"_BERT_baseline_Group{TARGET_GROUP}.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"🚀 阶段一：BERT基线验证 - Group {TARGET_GROUP}")
    print(f"测试类别: {GROUP_CONFIGS[TARGET_GROUP]}")
    print(f"日志文件: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 🚀 阶段一：BERT基线验证 (Group {TARGET_GROUP})\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**实验组别**: Group {TARGET_GROUP}\n")
        log_file.write(f"**测试类别**: {GROUP_CONFIGS[TARGET_GROUP]}\n")
        log_file.write(f"**方案**: 方案A - 纯BERT语义属性 (768维)\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = Zero_shot_BERT()
        best_acc = gan.train(epochs=2000, batch_size=64, log_file=log_file, test_class_indices=GROUP_CONFIGS[TARGET_GROUP])

        # 评估结果
        if best_acc > 0.6:  # 假设当前baseline在50%左右，60%就是显著提升
            result_status = "🟢 显著成功"
            next_step = "直接推进方案C"
        elif best_acc > 0.55:
            result_status = "🟡 中等成功"  
            next_step = "可推进方案C，建议实施方案B作为对比"
        else:
            result_status = "🔴 效果有限"
            next_step = "必须先实施方案B验证混合信息价值"

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"## 🎯 阶段一结果评估\n\n")
        log_file.write(f"**最佳准确率**: {best_acc:.4f}\n")
        log_file.write(f"**结果状态**: {result_status}\n")
        log_file.write(f"**下步建议**: {next_step}\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"\n🎯 阶段一完成!")
    print(f"结果状态: {result_status}")
    print(f"下步建议: {next_step}")
    print(f"详细日志: {log_filename}") 
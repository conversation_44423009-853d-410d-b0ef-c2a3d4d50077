#!/usr/bin/env python3
"""
深度分析ASDCGAN实验中的训练问题
重点关注损失函数行为、梯度问题和模式崩塌
"""

import re
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

def analyze_loss_patterns(log_file, group_name):
    """深度分析损失函数模式"""
    data = {'epochs': [], 'g_loss': [], 'd_loss': [], 'cycle_loss': [], 'accuracy': []}
    
    if not Path(log_file).exists():
        return data
    
    with open(log_file, 'r') as f:
        for line in f:
            match = re.search(r'Epoch (\d+): G_loss=([\d.]+), D_loss=([\d.]+), Cycle=([\d.]+), Best_Accuracy=([\d.]+)%', line)
            if match:
                epoch, g_loss, d_loss, cycle_loss, accuracy = match.groups()
                data['epochs'].append(int(epoch))
                data['g_loss'].append(float(g_loss))
                data['d_loss'].append(float(d_loss))
                data['cycle_loss'].append(float(cycle_loss))
                data['accuracy'].append(float(accuracy))
    
    if not data['epochs']:
        return data
    
    # 分析损失函数行为
    g_loss = np.array(data['g_loss'])
    d_loss = np.array(data['d_loss'])
    
    print(f"\n=== {group_name}组详细分析 ===")
    print(f"训练轮次: {len(data['epochs'])}")
    print(f"最终准确率: {data['accuracy'][-1]:.2f}%")
    print(f"最高准确率: {max(data['accuracy']):.2f}%")
    
    # 损失函数统计
    print(f"\n生成器损失统计:")
    print(f"  - 最小值: {np.min(g_loss):.2f}")
    print(f"  - 最大值: {np.max(g_loss):.2f}")
    print(f"  - 平均值: {np.mean(g_loss):.2f}")
    print(f"  - 标准差: {np.std(g_loss):.2f}")
    print(f"  - 最终值: {g_loss[-1]:.2f}")
    
    print(f"\n判别器损失统计:")
    print(f"  - 最小值: {np.min(d_loss):.6f}")
    print(f"  - 最大值: {np.max(d_loss):.6f}")
    print(f"  - 平均值: {np.mean(d_loss):.6f}")
    print(f"  - 标准差: {np.std(d_loss):.6f}")
    print(f"  - 最终值: {d_loss[-1]:.6f}")
    
    # 检测异常模式
    print(f"\n异常检测:")
    
    # 损失爆炸
    g_explosion_count = np.sum(g_loss > 1000)
    d_explosion_count = np.sum(d_loss > 0.5)
    print(f"  - 生成器损失爆炸次数 (>1000): {g_explosion_count}")
    print(f"  - 判别器损失爆炸次数 (>0.5): {d_explosion_count}")
    
    # 损失振荡
    g_changes = np.abs(np.diff(g_loss))
    d_changes = np.abs(np.diff(d_loss))
    large_g_changes = np.sum(g_changes > 200)
    large_d_changes = np.sum(d_changes > 0.1)
    print(f"  - 生成器大幅振荡次数 (变化>200): {large_g_changes}")
    print(f"  - 判别器大幅振荡次数 (变化>0.1): {large_d_changes}")
    
    # 判别器过强/过弱
    very_low_d = np.sum(d_loss < 0.001)
    very_high_d = np.sum(d_loss > 0.1)
    print(f"  - 判别器过强情况 (D_loss<0.001): {very_low_d} 次")
    print(f"  - 判别器过弱情况 (D_loss>0.1): {very_high_d} 次")
    
    # 准确率停滞
    accuracy_arr = np.array(data['accuracy'])
    if len(accuracy_arr) > 50:
        recent_acc_std = np.std(accuracy_arr[-50:])
        print(f"  - 近50轮准确率标准差: {recent_acc_std:.4f} (越小越停滞)")
    
    # 寻找准确率提升的关键轮次
    acc_improvements = []
    for i in range(1, len(accuracy_arr)):
        if accuracy_arr[i] > accuracy_arr[i-1]:
            acc_improvements.append((data['epochs'][i], accuracy_arr[i]))
    
    if acc_improvements:
        print(f"  - 准确率提升关键轮次: {len(acc_improvements)} 次")
        if len(acc_improvements) <= 10:
            for epoch, acc in acc_improvements:
                print(f"    Epoch {epoch}: {acc:.2f}%")
    
    # 分析训练阶段
    print(f"\n训练阶段分析:")
    total_epochs = len(data['epochs'])
    
    # 早期阶段 (前20%)
    early_end = max(1, total_epochs // 5)
    early_g = np.mean(g_loss[:early_end])
    early_d = np.mean(d_loss[:early_end])
    early_acc = np.mean(accuracy_arr[:early_end])
    
    # 中期阶段 (中间40%)
    mid_start = early_end
    mid_end = total_epochs * 3 // 5
    mid_g = np.mean(g_loss[mid_start:mid_end])
    mid_d = np.mean(d_loss[mid_start:mid_end])
    mid_acc = np.mean(accuracy_arr[mid_start:mid_end])
    
    # 后期阶段 (后40%)
    late_g = np.mean(g_loss[mid_end:])
    late_d = np.mean(d_loss[mid_end:])
    late_acc = np.mean(accuracy_arr[mid_end:])
    
    print(f"  - 早期 (前20%): G={early_g:.2f}, D={early_d:.4f}, Acc={early_acc:.2f}%")
    print(f"  - 中期 (中40%): G={mid_g:.2f}, D={mid_d:.4f}, Acc={mid_acc:.2f}%")
    print(f"  - 后期 (后40%): G={late_g:.2f}, D={late_d:.4f}, Acc={late_acc:.2f}%")
    
    return data

def analyze_mode_collapse(data, group_name):
    """分析模式崩塌迹象"""
    if not data['epochs']:
        return
    
    g_loss = np.array(data['g_loss'])
    d_loss = np.array(data['d_loss'])
    accuracy = np.array(data['accuracy'])
    
    print(f"\n=== {group_name}组模式崩塌分析 ===")
    
    # 检测典型的模式崩塌模式
    # 1. 判别器损失突然下降且保持很低
    d_very_low = d_loss < 0.01
    consecutive_low_d = 0
    max_consecutive_low_d = 0
    for low in d_very_low:
        if low:
            consecutive_low_d += 1
            max_consecutive_low_d = max(max_consecutive_low_d, consecutive_low_d)
        else:
            consecutive_low_d = 0
    
    print(f"判别器损失过低分析:")
    print(f"  - 判别器损失<0.01的轮次: {np.sum(d_very_low)}/{len(d_loss)}")
    print(f"  - 最长连续低损失轮次: {max_consecutive_low_d}")
    
    # 2. 生成器损失不正常上升
    if len(g_loss) > 10:
        g_trend = np.polyfit(range(len(g_loss)), g_loss, 1)[0]
        print(f"生成器损失趋势: {g_trend:.2f} (正值表示上升趋势)")
    
    # 3. 准确率突然下降或停滞
    if len(accuracy) > 20:
        max_acc_idx = np.argmax(accuracy)
        if max_acc_idx < len(accuracy) - 10:  # 最高准确率不在最后10轮
            final_acc_drop = max(accuracy) - accuracy[-1]
            print(f"准确率退化: 从最高{max(accuracy):.2f}%下降到{accuracy[-1]:.2f}% (下降{final_acc_drop:.2f}%)")
    
    # 4. 质量指标分析
    if 'quality' in data and data['quality']:
        quality = np.array(data['quality'])
        quality_trend = np.polyfit(range(len(quality)), quality, 1)[0]
        print(f"生成质量趋势: {quality_trend:.2f} (负值表示质量下降)")

def generate_detailed_diagnosis():
    """生成详细的问题诊断报告"""
    print("\n" + "="*80)
    print("🔬 ASDCGAN 深度诊断报告")
    print("="*80)
    
    # 分析A组
    log_a = "/home/<USER>/hmt/ACGAN-FG-main/innovations/experiments/group_A/run_20250724_202822/training.log"
    data_a = analyze_loss_patterns(log_a, "A")
    analyze_mode_collapse(data_a, "A")
    
    # 分析B组
    log_b = "/home/<USER>/hmt/ACGAN-FG-main/innovations/experiments/group_B/run_20250724_203307/training.log"
    data_b = analyze_loss_patterns(log_b, "B")
    analyze_mode_collapse(data_b, "B")
    
    # 对比分析
    print(f"\n=== 对比分析 ===")
    if data_a['epochs'] and data_b['epochs']:
        a_final_acc = data_a['accuracy'][-1]
        b_final_acc = data_b['accuracy'][-1]
        
        print(f"关键差异:")
        print(f"  - 准确率差距: A组比B组高 {a_final_acc - b_final_acc:.2f}%")
        print(f"  - 训练轮次: A组{len(data_a['epochs'])}轮 vs B组{len(data_b['epochs'])}轮")
        
        a_g_final = data_a['g_loss'][-1]
        b_g_final = data_b['g_loss'][-1]
        print(f"  - 最终生成器损失: A组{a_g_final:.2f} vs B组{b_g_final:.2f}")
        
        a_d_final = data_a['d_loss'][-1]
        b_d_final = data_b['d_loss'][-1]
        print(f"  - 最终判别器损失: A组{a_d_final:.4f} vs B组{b_d_final:.4f}")
    
    # 提供改进建议
    print(f"\n=== 改进建议 ===")
    print("针对发现的问题，建议:")
    print("1. 降低学习率，特别是生成器学习率")
    print("2. 调整损失权重，减少cycle_consistency_weight")
    print("3. 增加梯度裁剪，防止梯度爆炸")
    print("4. 使用谱归一化稳定训练")
    print("5. 调整判别器更新频率，平衡G和D的训练")
    print("6. 检查数据预处理，确保数据分布合理")
    print("7. B组可能需要重新划分测试类别或增加训练数据")

if __name__ == "__main__":
    generate_detailed_diagnosis()
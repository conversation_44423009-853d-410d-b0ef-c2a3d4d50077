"""
ASDCGAN数据处理器

基于现有TEP数据集的数据加载和预处理，兼容原版ACGAN-FG的数据格式。

核心功能：
1. 加载TEP数据集 (.npz文件)
2. 数据标准化和预处理
3. 零样本学习数据分割
4. TensorFlow数据集创建
5. 属性矩阵加载

技术特点：
- 兼容现有数据格式
- 支持多种分组策略
- 高效的数据流水线
- 完整的数据验证
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from typing import Dict, List, Tuple, Optional
import logging

# 添加项目路径以导入现有模块
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
from load_npz_data import load_tep_data_from_npz, scalar_stand


class TEPDataset(Dataset):
    """PyTorch数据集类"""

    def __init__(self, features, attributes, labels=None):
        """
        初始化数据集

        Args:
            features: 特征数据 [num_samples, feature_dim]
            attributes: 属性数据 [num_samples, attribute_dim]
            labels: 标签数据 [num_samples] (可选)
        """
        self.features = torch.FloatTensor(features)
        self.attributes = torch.FloatTensor(attributes)
        self.labels = torch.LongTensor(labels) if labels is not None else None

    def __len__(self):
        return len(self.features)

    def __getitem__(self, idx):
        if self.labels is not None:
            return self.features[idx], self.attributes[idx], self.labels[idx]
        else:
            return self.features[idx], self.attributes[idx]


class TEPDataProcessor:
    """
    TEP数据处理器
    
    基于现有TEP数据集的数据加载和预处理。
    """
    
    def __init__(self, 
                 data_path='/home/<USER>/hmt/ACGAN-FG-main/data',
                 normalize_method='standard',
                 random_seed=42):
        """
        初始化数据处理器
        
        Args:
            data_path: 数据路径
            normalize_method: 标准化方法 ('standard', 'minmax', 'none')
            random_seed: 随机种子
        """
        self.data_path = data_path
        self.normalize_method = normalize_method
        self.random_seed = random_seed
        
        # 设置随机种子
        np.random.seed(random_seed)
        tf.random.set_seed(random_seed)
        
        # 初始化标准化器
        if normalize_method == 'standard':
            self.scaler = StandardScaler()
        elif normalize_method == 'minmax':
            self.scaler = MinMaxScaler()
        else:
            self.scaler = None
        
        self.logger = logging.getLogger(__name__)
        
        # TEP数据集分组配置 (基于原版ACGAN-FG)
        self.split_configs = {
            'A': {'seen': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], 'unseen': [13, 14, 15]},
            'B': {'seen': [1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15], 'unseen': [9]},
            'C': {'seen': [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15], 'unseen': [10]},
            'D': {'seen': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15], 'unseen': [11]},
            'E': {'seen': [1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 14], 'unseen': [9, 13, 15]}
        }
    
    def load_tep_data(self, split_group='E', use_npz=True):
        """
        加载TEP数据集
        
        Args:
            split_group: 分组名称 ('A', 'B', 'C', 'D', 'E')
            use_npz: 是否使用.npz文件 (推荐)
            
        Returns:
            data_dict: 包含训练和测试数据的字典
        """
        self.logger.info(f"加载TEP数据集 - 分组 {split_group}")
        
        if split_group not in self.split_configs:
            raise ValueError(f"未知分组: {split_group}，支持的分组: {list(self.split_configs.keys())}")
        
        split_config = self.split_configs[split_group]
        seen_classes = split_config['seen']
        unseen_classes = split_config['unseen']
        
        self.logger.info(f"已见类别: {seen_classes}")
        self.logger.info(f"未见类别: {unseen_classes}")
        
        if use_npz:
            return self._load_from_npz(unseen_classes)
        else:
            return self._load_from_dat_files(seen_classes, unseen_classes)
    
    def _load_from_npz(self, test_class_indices):
        """
        从.npz文件加载数据 (推荐方式)
        
        Args:
            test_class_indices: 测试类别索引
            
        Returns:
            data_dict: 数据字典
        """
        self.logger.info("从.npz文件加载TEP数据...")
        
        # 使用现有的加载函数
        (traindata, trainlabel, train_attributelabel, 
         testdata, testlabel, test_attributelabel, 
         test_attribute_matrix, train_attribute_matrix) = load_tep_data_from_npz(test_class_indices)
        
        # 数据标准化
        if self.scaler is not None:
            self.logger.info(f"使用{self.normalize_method}标准化数据...")
            traindata, testdata = scalar_stand(traindata, testdata)
        
        # 构建数据字典
        data_dict = {
            # 训练数据 (已见类别)
            'train_features': traindata.astype(np.float32),
            'train_labels': trainlabel.astype(np.int32),
            'train_attributes': train_attributelabel.astype(np.float32),
            'train_attribute_matrix': train_attribute_matrix.astype(np.float32),
            
            # 测试数据 (未见类别)
            'test_features': testdata.astype(np.float32),
            'test_labels': testlabel.astype(np.int32),
            'test_attributes': test_attributelabel.astype(np.float32),
            'test_attribute_matrix': test_attribute_matrix.astype(np.float32),
            
            # 元信息
            'seen_classes': [c for c in range(1, 16) if c not in test_class_indices],
            'unseen_classes': test_class_indices,
            'feature_dim': traindata.shape[1],
            'attribute_dim': train_attributelabel.shape[1],
            'num_seen_classes': len([c for c in range(1, 16) if c not in test_class_indices]),
            'num_unseen_classes': len(test_class_indices)
        }
        
        self.logger.info(f"数据加载完成:")
        self.logger.info(f"  训练样本: {data_dict['train_features'].shape}")
        self.logger.info(f"  测试样本: {data_dict['test_features'].shape}")
        self.logger.info(f"  特征维度: {data_dict['feature_dim']}")
        self.logger.info(f"  属性维度: {data_dict['attribute_dim']}")
        
        return data_dict
    
    def _load_from_dat_files(self, seen_classes, unseen_classes):
        """
        从.dat文件加载数据 (备用方式)
        
        Args:
            seen_classes: 已见类别列表
            unseen_classes: 未见类别列表
            
        Returns:
            data_dict: 数据字典
        """
        self.logger.info("从.dat文件加载TEP数据...")
        
        train_features_list = []
        train_labels_list = []
        test_features_list = []
        test_labels_list = []
        
        # 加载训练数据 (已见类别)
        for class_id in seen_classes:
            try:
                # 训练文件
                train_file = os.path.join(self.data_path, f'd{class_id:02d}.dat')
                if os.path.exists(train_file):
                    data = np.loadtxt(train_file)
                    train_features_list.append(data)
                    train_labels_list.append(np.full(len(data), class_id))
                    self.logger.info(f"  加载训练类别 {class_id}: {len(data)} 样本")
                else:
                    self.logger.warning(f"  训练文件不存在: {train_file}")
            except Exception as e:
                self.logger.error(f"  加载训练类别 {class_id} 失败: {e}")
        
        # 加载测试数据 (未见类别)
        for class_id in unseen_classes:
            try:
                # 测试文件
                test_file = os.path.join(self.data_path, f'd{class_id:02d}_te.dat')
                if os.path.exists(test_file):
                    data = np.loadtxt(test_file)
                    test_features_list.append(data)
                    test_labels_list.append(np.full(len(data), class_id))
                    self.logger.info(f"  加载测试类别 {class_id}: {len(data)} 样本")
                else:
                    self.logger.warning(f"  测试文件不存在: {test_file}")
            except Exception as e:
                self.logger.error(f"  加载测试类别 {class_id} 失败: {e}")
        
        if not train_features_list or not test_features_list:
            raise ValueError("数据加载失败，请检查数据文件")
        
        # 合并数据
        train_features = np.vstack(train_features_list).astype(np.float32)
        train_labels = np.hstack(train_labels_list).astype(np.int32)
        test_features = np.vstack(test_features_list).astype(np.float32)
        test_labels = np.hstack(test_labels_list).astype(np.int32)
        
        # 数据标准化
        if self.scaler is not None:
            self.logger.info(f"使用{self.normalize_method}标准化数据...")
            train_features = self.scaler.fit_transform(train_features)
            test_features = self.scaler.transform(test_features)
        
        # 加载属性矩阵
        attribute_matrix = self._load_attribute_matrix()
        
        # 生成属性标签 (简化版本，实际应该从属性矩阵获取)
        train_attributes = self._generate_attributes_from_labels(train_labels, attribute_matrix)
        test_attributes = self._generate_attributes_from_labels(test_labels, attribute_matrix)
        
        # 构建数据字典
        data_dict = {
            'train_features': train_features,
            'train_labels': train_labels,
            'train_attributes': train_attributes,
            'test_features': test_features,
            'test_labels': test_labels,
            'test_attributes': test_attributes,
            'seen_classes': seen_classes,
            'unseen_classes': unseen_classes,
            'feature_dim': train_features.shape[1],
            'attribute_dim': train_attributes.shape[1] if train_attributes is not None else 20,
            'num_seen_classes': len(seen_classes),
            'num_unseen_classes': len(unseen_classes)
        }
        
        return data_dict
    
    def _load_attribute_matrix(self):
        """加载属性矩阵"""
        try:
            excel_path = os.path.join(self.data_path, 'attribute_matrix.xlsx')
            if os.path.exists(excel_path):
                df = pd.read_excel(excel_path, index_col=0)
                attribute_matrix = df.values.astype(np.float32)
                self.logger.info(f"成功加载属性矩阵: {attribute_matrix.shape}")
                return attribute_matrix
            else:
                self.logger.warning(f"属性矩阵文件不存在: {excel_path}")
                return None
        except Exception as e:
            self.logger.error(f"加载属性矩阵失败: {e}")
            return None
    
    def _generate_attributes_from_labels(self, labels, attribute_matrix):
        """从标签生成属性"""
        if attribute_matrix is None:
            # 生成随机属性作为备用
            unique_labels = np.unique(labels)
            num_attributes = 20
            attributes = np.random.randn(len(labels), num_attributes).astype(np.float32)
            return attributes
        
        # 从属性矩阵获取对应的属性向量
        attributes_list = []
        for label in labels:
            if label - 1 < len(attribute_matrix):  # 标签从1开始，索引从0开始
                attr_vector = attribute_matrix[label - 1]
            else:
                attr_vector = np.random.randn(attribute_matrix.shape[1])
            attributes_list.append(attr_vector)
        
        return np.array(attributes_list).astype(np.float32)
    
    def create_pytorch_datasets(self, data_dict, batch_size=32, shuffle=True, num_workers=4):
        """
        创建PyTorch数据集

        Args:
            data_dict: 数据字典
            batch_size: 批次大小
            shuffle: 是否打乱数据
            num_workers: 数据加载器工作进程数

        Returns:
            train_loader, val_loader, test_loader
        """
        self.logger.info(f"创建PyTorch数据集，批次大小: {batch_size}")

        # 训练数据分割 (80% 训练, 20% 验证)
        train_size = int(0.8 * len(data_dict['train_features']))

        # 打乱训练数据
        if shuffle:
            indices = np.random.permutation(len(data_dict['train_features']))
            train_features = data_dict['train_features'][indices]
            train_attributes = data_dict['train_attributes'][indices]
            train_labels = data_dict['train_labels'][indices] if 'train_labels' in data_dict else None
        else:
            train_features = data_dict['train_features']
            train_attributes = data_dict['train_attributes']
            train_labels = data_dict.get('train_labels', None)

        # 分割训练和验证数据
        train_features_split = train_features[:train_size]
        train_attributes_split = train_attributes[:train_size]
        train_labels_split = train_labels[:train_size] if train_labels is not None else None

        val_features = train_features[train_size:]
        val_attributes = train_attributes[train_size:]
        val_labels = train_labels[train_size:] if train_labels is not None else None

        # 创建数据集
        train_dataset = TEPDataset(train_features_split, train_attributes_split, train_labels_split)
        val_dataset = TEPDataset(val_features, val_attributes, val_labels)
        test_dataset = TEPDataset(
            data_dict['test_features'],
            data_dict['test_attributes'],
            data_dict.get('test_labels', None)
        )

        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            pin_memory=torch.cuda.is_available()
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=torch.cuda.is_available()
        )

        test_loader = DataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=torch.cuda.is_available()
        )

        self.logger.info(f"数据集创建完成:")
        self.logger.info(f"  训练集: {len(train_features_split)} 样本")
        self.logger.info(f"  验证集: {len(val_features)} 样本")
        self.logger.info(f"  测试集: {len(data_dict['test_features'])} 样本")

        return train_loader, val_loader, test_loader
    
    def get_data_info(self, data_dict):
        """获取数据信息"""
        info = {
            'feature_dim': data_dict['feature_dim'],
            'attribute_dim': data_dict['attribute_dim'],
            'num_seen_classes': data_dict['num_seen_classes'],
            'num_unseen_classes': data_dict['num_unseen_classes'],
            'seen_classes': data_dict['seen_classes'],
            'unseen_classes': data_dict['unseen_classes'],
            'train_samples': len(data_dict['train_features']),
            'test_samples': len(data_dict['test_features'])
        }
        return info


class DataProcessor:
    """数据处理器的统一接口"""
    
    def __init__(self, dataset_type='TEP', **kwargs):
        """
        初始化数据处理器
        
        Args:
            dataset_type: 数据集类型 ('TEP', 'synthetic')
            **kwargs: 其他参数
        """
        if dataset_type == 'TEP':
            self.processor = TEPDataProcessor(**kwargs)
        else:
            raise ValueError(f"不支持的数据集类型: {dataset_type}")
    
    def load_data(self, **kwargs):
        """加载数据"""
        return self.processor.load_tep_data(**kwargs)
    
    def create_datasets(self, data_dict, **kwargs):
        """创建数据集"""
        return self.processor.create_pytorch_datasets(data_dict, **kwargs)
    
    def get_info(self, data_dict):
        """获取数据信息"""
        return self.processor.get_data_info(data_dict)

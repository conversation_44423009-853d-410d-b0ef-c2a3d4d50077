import numpy as np
import pandas as pd
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test_hard_negative import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler

# 强制使用CPU
tf.config.experimental.set_visible_devices([], 'GPU')

# 配置CPU优化
tf.config.threading.set_intra_op_parallelism_threads(0)  # 使用所有可用CPU核心
tf.config.threading.set_inter_op_parallelism_threads(0)

print("=== CPU优化版ACGAN ===")
print("已禁用GPU，使用CPU训练")

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        self.W_q = self.add_weight(name='query',
                                   shape=(input_shape[-1], input_shape[-1]),
                                   initializer='random_normal',
                                   trainable=True)
        self.W_k = self.add_weight(name='key',
                                   shape=(input_shape[-1], input_shape[-1]),
                                   initializer='random_normal',
                                   trainable=True)
        self.W_v = self.add_weight(name='value',
                                   shape=(input_shape[-1], input_shape[-1]),
                                   initializer='random_normal',
                                   trainable=True)
        super(SelfAttention, self).build(input_shape)

    def call(self, inputs):
        q = tf.matmul(inputs, self.W_q)
        k = tf.matmul(inputs, self.W_k)
        v = tf.matmul(inputs, self.W_v)
        
        # CPU版本使用float32，避免混合精度问题
        scale = tf.sqrt(tf.cast(tf.shape(q)[-1], tf.float32))
        attention_weights = tf.nn.softmax(tf.matmul(q, k, transpose_b=True) / scale)
        attention_output = tf.matmul(attention_weights, v)
        return attention_output

def residual_block(x, units):
    """残差块"""
    shortcut = x
    
    x = Dense(units)(x)
    x = LeakyReLU(alpha=0.2)(x)
    x = BatchNormalization()(x)
    
    x = Dense(units)(x)
    x = LeakyReLU(alpha=0.2)(x)
    x = BatchNormalization()(x)
    
    # 如果维度不匹配，调整shortcut
    if int(shortcut.shape[-1]) != units:
        shortcut = Dense(units)(shortcut)
    
    return tf.keras.layers.add([x, shortcut])

class OptimizedDataPipelineCPU:
    """CPU优化的数据流水线"""
    def __init__(self):
        self.scaler = MinMaxScaler()

    def prepare_data_adaptive(self, train_data, test_data, group, batch_size=256, shuffle_buffer=10000):
        """为CPU优化的自适应训练准备数据"""
        print(f"为目标组别 {group} 准备CPU优化数据流水线...")
        start_time = datetime.datetime.now()
        
        # 加载数据
        train_samples = train_data['samples']
        train_attributes = train_data['attributes']
        test_samples = test_data['samples']
        test_attributes = test_data['attributes']
        
        # 数据归一化
        print("正在进行数据标准化...")
        train_samples = self.scaler.fit_transform(train_samples)
        test_samples = self.scaler.transform(test_samples)
        
        # 定义组别映射
        group_mapping = {
            'A': ([0, 2, 4, 6, 8, 10, 12, 14], [1, 3, 5]),
            'B': ([0, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13], [1, 6, 14]),
            'C': ([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], [12, 13, 14]),
            'D': ([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 13], [10, 11, 14]),
            'E': ([0, 1, 2, 3, 4, 5, 6, 7, 10, 11, 12, 13], [8, 9, 14])
        }
        
        seen_classes, unseen_classes = group_mapping[group]
        
        # 筛选训练数据
        seen_indices = np.isin(train_data['labels'], seen_classes)
        train_X = train_samples[seen_indices]
        train_Y = train_attributes[seen_indices]
        train_labels = train_data['labels'][seen_indices]
        
        # 按类别组织数据
        train_X_by_class = {}
        train_Y_by_class = {}
        for label in seen_classes:
            indices = train_labels == label
            train_X_by_class[label] = train_X[indices]
            train_Y_by_class[label] = train_Y[indices]
        
        # 创建CPU优化的数据集
        print(f"创建CPU优化的训练数据流水线，批处理大小: {batch_size}")
        dataset = tf.data.Dataset.from_tensor_slices((
            train_X.astype(np.float32),
            train_Y.astype(np.float32),
            train_labels.astype(np.int32)
        ))
        
        # CPU优化配置
        dataset = dataset.shuffle(shuffle_buffer)
        dataset = dataset.batch(batch_size)
        dataset = dataset.prefetch(2)  # 预取2个批次
        
        # 筛选测试数据
        unseen_indices = np.isin(test_data['labels'], unseen_classes)
        test_X = test_samples[unseen_indices]
        test_Y = test_attributes[unseen_indices]
        
        end_time = datetime.datetime.now()
        print(f"数据流水线准备完成! 用时: {(end_time - start_time).total_seconds():.2f}秒")
        print(f"训练样本数: {len(train_X)}, 测试样本数: {len(test_X)}")
        print(f"训练批次数: {len(train_X) // batch_size}")
        
        return {
            'train_dataset': dataset,
            'train_X_by_class': train_X_by_class,
            'train_Y_by_class': train_Y_by_class,
            'seen_classes': seen_classes,
            'unseen_classes': unseen_classes,
            'test_X': test_X,
            'test_Y': test_Y
        }

class Zero_shot_Adaptive_CPU:
    def __init__(self, group='B'):
        self.group = group
        
        # 基础参数
        self.data_lenth = 52
        self.sample_shape = (self.data_lenth,)
        self.feature_dim = 256
        self.feature_shape = (256,)
        self.num_classes = 15
        self.latent_dim = 50
        self.noise_shape = (self.latent_dim, 1)
        self.n_critic = 1
        
        # 损失权重
        self.lambda_cla = 10
        self.lambda_triplet = 10
        self.lambda_crl = 0.01
        self.triplet_margin = 0.2
        
        # 互信息相关
        self.bound = True
        self.mi_weight = 0.001
        self.mi_bound = 100
        self.crl = True
        
        # 初始化数据流水线
        self.data_pipeline = OptimizedDataPipelineCPU()
        
        # 优化器 - CPU版本学习率稍低
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        
        # 构建模型
        self.autoencoder = self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c = self.build_classifier()
        
        print(f"CPU优化版Zero-shot模型初始化完成 (Group {self.group})")

    def build_autoencoder(self):
        sample = Input(shape=self.sample_shape)
        
        e1 = Dense(256)(sample)
        e1 = LeakyReLU(alpha=0.2)(e1)
        e1 = BatchNormalization()(e1)
        
        e2 = residual_block(e1, 256)
        e3 = residual_block(e2, 256)
        
        e3_attention = SelfAttention()(e3)
        
        feature = Dense(256)(e3_attention)
        feature = BatchNormalization()(feature)
        
        d1 = Dense(256, activation='relu')(feature)
        d2 = Dense(128, activation='relu')(d1)
        d3 = Dense(64, activation='relu')(d2)
        output_sample = Dense(self.data_lenth, activation='linear')(d3)
        
        encoder = Model(sample, feature)
        self.encoder = encoder
        
        return Model(sample, [feature, output_sample])

    def build_discriminator(self):
        feature = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        feature_embedding = Dense(128)(feature)
        feature_embedding = LeakyReLU(alpha=0.2)(feature_embedding)
        
        attribute_embedding = Dense(128)(attribute)
        attribute_embedding = LeakyReLU(alpha=0.2)(attribute_embedding)
        
        d_input = concatenate([feature_embedding, attribute_embedding])
        
        d1 = Dense(128)(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        d1 = BatchNormalization()(d1)
        
        d2 = residual_block(d1, 128)
        d3 = residual_block(d2, 64)
        
        validity = Dense(1)(d3)
        
        return Model([feature, attribute], validity)

    def build_generator(self):
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        noise_embedding = Flatten()(noise)
        attribute_embedding = Dense(self.latent_dim)(attribute)
        
        g_input = concatenate([noise_embedding, attribute_embedding])

        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = BatchNormalization()(g1)

        g2 = residual_block(g1, 256)
        g3 = residual_block(g2, 256)
        
        g3_attention = SelfAttention()(g3)
        
        generated_feature = Dense(256)(g3_attention)
        generated_feature = BatchNormalization()(generated_feature)

        return Model([noise, attribute], generated_feature)

    def build_classifier(self):
        sample = Input(shape=self.feature_shape)

        c0 = sample
        c1 = Dense(100)(c0)
        c1 = LeakyReLU(alpha=0.2)(c1)
        
        c2 = Dense(50)(c1)
        c2 = LeakyReLU(alpha=0.2)(c2)
        hidden_ouput = c2
               
        c3 = Dense(20, activation="sigmoid")(c2)
        predict_attribute = c3
        
        return Model(sample, [hidden_ouput, predict_attribute])

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        """CPU版本的Wasserstein损失，强制使用float32"""
        y_true = tf.cast(y_true, tf.float32)
        y_pred = tf.cast(y_pred, tf.float32)
        return K.mean(y_true * y_pred)

    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est

    def mi_penalty_loss(self, x, z):
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)

    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)
        
        mi_penalty = 0
        if self.bound == True:
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss

    def cycle_rank_loss(self, anchor, positive, negative):
        return self.triplet_loss(anchor, positive, negative)

    def train_cpu(self, epochs, batch_size=256, log_file=None):
        """CPU优化训练方法"""
        start_time = datetime.datetime.now()
        
        accuracy_list_1 = []
        accuracy_list_2 = []
        accuracy_list_3 = []
        accuracy_list_4 = []
        
        PATH_train = './dataset_train_case1.npz'
        PATH_test = './dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        print(f"开始为Group {self.group}准备CPU优化数据流水线，批处理大小: {batch_size}")
        
        # 使用CPU优化数据流水线准备数据
        data_info = self.data_pipeline.prepare_data_adaptive(
            train_data, test_data, self.group, batch_size=batch_size, shuffle_buffer=10000
        )
        
        train_dataset = data_info['train_dataset']
        train_X_by_class = data_info['train_X_by_class'] 
        train_Y_by_class = data_info['train_Y_by_class']
        seen_classes = data_info['seen_classes']
        unseen_classes = data_info['unseen_classes']
        testdata = data_info['test_X']
        test_attributelabel = data_info['test_Y']

        print(f"开始CPU训练 Group {self.group}")
        print(f"批处理大小: {batch_size}")
        print(f"训练数据: {len(seen_classes)}个已见类别")
        print(f"测试数据: {len(unseen_classes)}个未见类别")

        for epoch in range(epochs):
            epoch_losses = {'ae_c': [], 'm': [], 'd': [], 'g': []}
            batch_count = 0
            
            for batch_data in train_dataset:
                train_x, train_y, train_labels = batch_data
                current_batch_size = tf.shape(train_x)[0]
                batch_count += 1
                
                # Autoencoder和Classifier训练
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                    feature, output_sample = self.autoencoder(train_x)
                    autoencoder_loss = mean_squared_error(train_x, output_sample)

                    hidden_ouput_c, predict_attribute_c = self.c(feature)
                    c_loss = self.classification_loss(feature, train_y, hidden_ouput_c, predict_attribute_c)

                    total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c
                epoch_losses['ae_c'].append(float(tf.reduce_mean(total_ac_loss)))

                # Triplet Loss度量学习
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                # 从当前批次生成triplet样本
                train_labels_np = train_labels.numpy()
                positive_samples = []
                negative_samples = []
                
                for label in train_labels_np:
                    # 正样本采样
                    if label in train_X_by_class and len(train_X_by_class[label]) > 0:
                        pos_class_samples = train_X_by_class[label]
                        pos_idx = np.random.choice(len(pos_class_samples))
                        positive_samples.append(pos_class_samples[pos_idx])
                    else:
                        positive_samples.append(train_x[0].numpy())
                    
                    # 负样本采样
                    available_classes = [c for c in seen_classes if c != label and c in train_X_by_class]
                    if available_classes:
                        neg_class = np.random.choice(available_classes)
                        neg_class_samples = train_X_by_class[neg_class]
                        neg_idx = np.random.choice(len(neg_class_samples))
                        negative_samples.append(neg_class_samples[neg_idx])
                    else:
                        negative_samples.append(train_x[0].numpy())

                positive_samples = tf.constant(np.array(positive_samples), dtype=tf.float32)
                negative_samples = tf.constant(np.array(negative_samples), dtype=tf.float32)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(train_x)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))
                epoch_losses['m'].append(float(m_loss))

                # 判别器训练
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False
                
                with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(current_batch_size, self.latent_dim, 1))
                    Fake_feature = self.g([noise, train_y])
                    Real_validity = self.d([feature, train_y])
                    Fake_validity = self.d([Fake_feature, train_y])
                    
                    valid = -tf.ones((current_batch_size, 1))
                    fake = tf.ones((current_batch_size, 1))
                    
                    d_loss_real = self.wasserstein_loss(valid, Real_validity)
                    d_loss_fake = self.wasserstein_loss(fake, Fake_validity)
                    d_loss = (d_loss_real + d_loss_fake) / 2

                grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))
                epoch_losses['d'].append(float(d_loss))

                # 生成器训练
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = False
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                    noise_g = tf.random.normal(shape=(current_batch_size, self.latent_dim, 1))
                    Fake_feature_g = self.g([noise_g, train_y])
                    Fake_validity_g = self.d([Fake_feature_g, train_y])
                    valid = -tf.ones((current_batch_size, 1))
                    adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
              
                    fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                    classification_loss = self.classification_loss(Fake_feature_g, train_y, fake_hidden_ouput_g, Fake_classification_g)
                    
                    # Triplet loss
                    g_anchor_features = Fake_feature_g
                    g_positive_features = self.encoder(positive_samples)
                    g_negative_features = self.encoder(negative_samples)
                    triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                    
                    cycle_rank_loss = 0
                    if self.crl == True:
                        reconstructed_feature = self.g([noise_g, Fake_classification_g])
                        
                        # 创建不相似的属性
                        batch_size_np = train_labels_np.shape[0]
                        negative_attributes = []
                        for label in train_labels_np:
                            available_classes = [c for c in seen_classes if c != label and c in train_Y_by_class]
                            if available_classes:
                                neg_class = np.random.choice(available_classes)
                                negative_attributes.append(train_Y_by_class[neg_class][0])
                            else:
                                negative_attributes.append(train_y[0].numpy())
                        
                        negative_attributes = tf.constant(np.array(negative_attributes), dtype=tf.float32)
                        unsimilar_generated_feature = self.g([noise_g, negative_attributes])

                        cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                               
                    total_loss = adversarial_loss + self.lambda_cla * classification_loss + self.lambda_triplet * triplet_loss_g + self.lambda_crl * cycle_rank_loss
                              
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                epoch_losses['g'].append(float(tf.reduce_mean(total_loss)))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                if batch_count % 5 == 0:  # CPU版本每5个批次打印一次
                    print("[Epoch %d/%d][Batch %d][AE+C loss: %f][M loss: %f][D loss: %f][G loss %05f] time: %s" \
                         % (epoch, epochs,
                           batch_count,
                           np.mean(epoch_losses['ae_c'][-5:]), 
                           np.mean(epoch_losses['m'][-5:]),
                           np.mean(epoch_losses['d'][-5:]),
                           np.mean(epoch_losses['g'][-5:]),
                           elapsed_time))
        
            # 每个epoch结束后进行评估
            if epoch % 1 == 0:
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis(
                    2000, testdata, test_attributelabel, self.autoencoder, self.g, self.c, unseen_classes)

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                      %(epoch, epochs, max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)))
                      
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
        
        return {
            'best_accuracy': max(accuracy_list_4) if accuracy_list_4 else 0.0,
            'accuracy_lsvm': max(accuracy_list_1) if accuracy_list_1 else 0.0,
            'accuracy_nrf': max(accuracy_list_2) if accuracy_list_2 else 0.0,
            'accuracy_pnb': max(accuracy_list_3) if accuracy_list_3 else 0.0,
            'accuracy_mlp': max(accuracy_list_4) if accuracy_list_4 else 0.0,
        }

def run_cpu_experiments(target_group='B', epochs=500, batch_size=256):
    """运行CPU优化版实验"""
    
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    
    print("=== 开始CPU优化版实验 ===")
    log_filename = os.path.join(results_dir, start_run_time.strftime("%Y%m%d%H%M") + f"_cpu_optimized_Group{target_group}.md")
    
    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# CPU优化版实验 - Group {target_group}\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**优化特性**: CPU多线程 + 数据流水线优化 + 避免混合精度\n")
        log_file.write(f"**批处理大小**: {batch_size}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan_cpu = Zero_shot_Adaptive_CPU(group=target_group)
        results = gan_cpu.train_cpu(epochs=epochs, batch_size=batch_size, log_file=log_file)
        
        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")
        log_file.write(f"**最佳准确率**: {results['best_accuracy']:.4f}\n")

    print(f"=== CPU优化版实验完成 ===")
    print(f"最佳准确率: {results['best_accuracy']:.4f}")
    print(f"详细结果已保存至: {log_filename}")
    
    return results

if __name__ == '__main__':
    TARGET_GROUP = 'B'  # 可选: 'A', 'B', 'C', 'D', 'E'
    
    print(f"开始运行 Group {TARGET_GROUP} 的CPU优化版实验")
    print("优化特性: CPU多线程 + 数据流水线优化 + 避免混合精度问题")
    results = run_cpu_experiments(target_group=TARGET_GROUP, epochs=500, batch_size=256) 
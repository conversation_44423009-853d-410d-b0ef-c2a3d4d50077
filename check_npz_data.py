#!/usr/bin/env python3
"""
检查.npz数据文件的内容和结构
"""

import numpy as np
import os

def check_npz_file(filepath):
    """检查npz文件内容"""
    print(f"\n📁 检查文件: {filepath}")
    print("=" * 50)
    
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return
    
    try:
        data = np.load(filepath)
        print(f"✅ 文件加载成功")
        print(f"📊 包含的数组:")
        
        for key in data.files:
            array = data[key]
            print(f"   {key}: 形状{array.shape}, 类型{array.dtype}")
            print(f"      范围: [{array.min():.4f}, {array.max():.4f}]")
            if len(array.shape) <= 2 and array.shape[0] <= 10:
                print(f"      前几个值: {array.flatten()[:5]}")
        
        data.close()
        
    except Exception as e:
        print(f"❌ 文件加载失败: {e}")

def main():
    """主函数"""
    print("🔍 检查TEP数据集的.npz文件")
    
    # 检查训练数据
    check_npz_file("data/dataset_train_case1.npz")
    
    # 检查测试数据  
    check_npz_file("data/dataset_test_case1.npz")
    
    # 尝试加载并分析E组数据
    print(f"\n🎯 分析E组数据 (测试类别: [9, 13, 15])")
    print("=" * 50)
    
    try:
        # 加载测试数据
        test_data = np.load("data/dataset_test_case1.npz")
        
        if 'X_test' in test_data.files and 'y_test' in test_data.files:
            X_test = test_data['X_test']
            y_test = test_data['y_test']
            
            print(f"测试数据形状: {X_test.shape}")
            print(f"测试标签形状: {y_test.shape}")
            print(f"测试标签唯一值: {np.unique(y_test)}")
            
            # 检查E组类别
            e_group_classes = [9, 13, 15]
            for class_id in e_group_classes:
                mask = y_test == class_id
                count = np.sum(mask)
                print(f"类别 {class_id}: {count} 个样本")
                
                if count > 0:
                    class_data = X_test[mask]
                    print(f"   数据范围: [{class_data.min():.4f}, {class_data.max():.4f}]")
                    print(f"   数据均值: {class_data.mean():.4f}, 标准差: {class_data.std():.4f}")
        
        test_data.close()
        
    except Exception as e:
        print(f"❌ E组数据分析失败: {e}")

if __name__ == "__main__":
    main()

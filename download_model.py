import os
from modelscope.hub.snapshot_download import snapshot_download

# 模型ID
model_id = 'damo/nlp_structbert_base_en'

# 本地保存路径 (与acgan_triplet_bert_modelscope.py中的路径一致)
output_dir = '/home/<USER>/hmt/ACGAN-FG-main/models/bert/structbert_base_en'

print(f"准备下载模型 '{model_id}' 到: {output_dir}")

# 确保目标目录存在
os.makedirs(output_dir, exist_ok=True)

try:
    # 使用 local_dir 参数指定下载位置
    snapshot_download(model_id, local_dir=output_dir, local_dir_use_symlinks=False)
    print(f"✅ 模型成功下载到: {output_dir}")
    print("请验证该文件夹中是否包含 'configuration.json', 'pytorch_model.bin' 等文件。")

except Exception as e:
    print(f"❌ 下载失败: {e}")
    print("请检查网络连接或ModelScope状态。") 
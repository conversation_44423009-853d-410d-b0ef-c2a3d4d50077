"""
配置管理器

统一管理ASDCGAN的所有配置参数，支持多种配置格式和动态配置更新。

核心功能：
1. 配置文件加载和保存
2. 参数验证和类型检查
3. 配置继承和覆盖
4. 动态配置更新
5. 配置版本管理

技术特点：
- 支持YAML、JSON配置格式
- 参数类型验证
- 配置模板和继承
- 环境变量支持
"""

import yaml
import json
import os
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
import logging


@dataclass
class ModelConfig:
    """模型配置"""
    feature_dim: int = 256
    attribute_dim: int = 20
    latent_dim: int = 50
    num_domains: int = 5
    
    # 生成器配置
    generator_hidden_dims: list = None
    generator_dropout_rate: float = 0.1
    
    # 判别器配置
    discriminator_hidden_dims: list = None
    discriminator_dropout_rate: float = 0.1
    use_spectral_norm: bool = True
    
    # 语义距离配置
    semantic_attention_dim: int = 64
    semantic_num_heads: int = 4
    semantic_distance_type: str = 'weighted_euclidean'
    
    # 域选择配置
    domain_selection_mode: str = 'soft'
    domain_temperature: float = 1.0
    
    # 不确定性配置
    uncertainty_num_samples: int = 50
    uncertainty_confidence_threshold: float = 0.8
    
    def __post_init__(self):
        if self.generator_hidden_dims is None:
            self.generator_hidden_dims = [128, 64]
        if self.discriminator_hidden_dims is None:
            self.discriminator_hidden_dims = [256, 128, 64]


@dataclass
class TrainingConfig:
    """训练配置"""
    batch_size: int = 32
    epochs: int = 100
    learning_rate_g: float = 0.0001
    learning_rate_d: float = 0.0004
    beta1: float = 0.5
    beta2: float = 0.999
    
    # 训练策略
    n_critic: int = 5
    warmup_epochs: int = 5
    save_interval: int = 10
    validation_interval: int = 5
    
    # 损失权重
    adversarial_weight: float = 1.0
    cycle_consistency_weight: float = 10.0
    semantic_distance_weight: float = 5.0
    uncertainty_weight: float = 1.0
    lambda_gp: float = 10.0
    
    # 自适应配置
    adaptive_weights: bool = True
    adaptation_rate: float = 0.01
    
    # 早停配置
    early_stopping: bool = True
    patience: int = 20
    min_delta: float = 0.001


@dataclass
class DataConfig:
    """数据配置"""
    dataset_name: str = 'TEP'
    data_path: str = './data'
    train_split: float = 0.8
    validation_split: float = 0.1
    test_split: float = 0.1
    
    # 数据预处理
    normalize: bool = True
    standardize: bool = True
    feature_selection: bool = False
    
    # 数据增强
    data_augmentation: bool = False
    noise_level: float = 0.01


@dataclass
class ExperimentConfig:
    """实验配置"""
    experiment_name: str = 'asdcgan_experiment'
    save_dir: str = './experiments'
    log_dir: str = './logs'
    tensorboard_dir: str = './tensorboard'
    
    # 日志配置
    log_level: str = 'INFO'
    log_to_file: bool = True
    log_to_console: bool = True
    
    # 保存配置
    save_model: bool = True
    save_history: bool = True
    save_samples: bool = True
    
    # 可视化配置
    plot_losses: bool = True
    plot_samples: bool = True
    plot_uncertainty: bool = True


class ConfigManager:
    """
    配置管理器
    
    统一管理ASDCGAN的所有配置参数。
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        
        # 初始化默认配置
        self.model_config = ModelConfig()
        self.training_config = TrainingConfig()
        self.data_config = DataConfig()
        self.experiment_config = ExperimentConfig()
        
        # 如果提供了配置文件，加载配置
        if config_path and os.path.exists(config_path):
            self.load_config(config_path)
    
    def load_config(self, config_path: str):
        """
        从文件加载配置
        
        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    config_data = yaml.safe_load(f)
                elif config_path.endswith('.json'):
                    config_data = json.load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {config_path}")
            
            # 更新配置
            self._update_configs(config_data)
            self.config_path = config_path
            
            self.logger.info(f"配置已从 {config_path} 加载")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise
    
    def save_config(self, save_path: str, format: str = 'yaml'):
        """
        保存配置到文件
        
        Args:
            save_path: 保存路径
            format: 保存格式 ('yaml' 或 'json')
        """
        try:
            config_data = self.to_dict()
            
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                if format == 'yaml':
                    yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
                elif format == 'json':
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的保存格式: {format}")
            
            self.logger.info(f"配置已保存到 {save_path}")
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            raise
    
    def _update_configs(self, config_data: Dict[str, Any]):
        """更新配置对象"""
        
        # 更新模型配置
        if 'model' in config_data:
            self._update_dataclass(self.model_config, config_data['model'])
        
        # 更新训练配置
        if 'training' in config_data:
            self._update_dataclass(self.training_config, config_data['training'])
        
        # 更新数据配置
        if 'data' in config_data:
            self._update_dataclass(self.data_config, config_data['data'])
        
        # 更新实验配置
        if 'experiment' in config_data:
            self._update_dataclass(self.experiment_config, config_data['experiment'])
    
    def _update_dataclass(self, config_obj, update_dict: Dict[str, Any]):
        """更新dataclass对象"""
        for key, value in update_dict.items():
            if hasattr(config_obj, key):
                setattr(config_obj, key, value)
            else:
                self.logger.warning(f"未知配置参数: {key}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'model': asdict(self.model_config),
            'training': asdict(self.training_config),
            'data': asdict(self.data_config),
            'experiment': asdict(self.experiment_config)
        }
    
    def update_config(self, section: str, **kwargs):
        """
        动态更新配置
        
        Args:
            section: 配置节 ('model', 'training', 'data', 'experiment')
            **kwargs: 要更新的参数
        """
        if section == 'model':
            config_obj = self.model_config
        elif section == 'training':
            config_obj = self.training_config
        elif section == 'data':
            config_obj = self.data_config
        elif section == 'experiment':
            config_obj = self.experiment_config
        else:
            raise ValueError(f"未知配置节: {section}")
        
        for key, value in kwargs.items():
            if hasattr(config_obj, key):
                setattr(config_obj, key, value)
                self.logger.info(f"更新配置 {section}.{key} = {value}")
            else:
                self.logger.warning(f"未知配置参数: {section}.{key}")
    
    def get_config(self, section: str, key: str = None):
        """
        获取配置值
        
        Args:
            section: 配置节
            key: 配置键 (可选)
            
        Returns:
            配置值或配置对象
        """
        if section == 'model':
            config_obj = self.model_config
        elif section == 'training':
            config_obj = self.training_config
        elif section == 'data':
            config_obj = self.data_config
        elif section == 'experiment':
            config_obj = self.experiment_config
        else:
            raise ValueError(f"未知配置节: {section}")
        
        if key is None:
            return config_obj
        else:
            return getattr(config_obj, key, None)
    
    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            是否有效
        """
        try:
            # 验证模型配置
            assert self.model_config.feature_dim > 0, "feature_dim 必须大于 0"
            assert self.model_config.attribute_dim > 0, "attribute_dim 必须大于 0"
            assert self.model_config.latent_dim > 0, "latent_dim 必须大于 0"
            
            # 验证训练配置
            assert self.training_config.batch_size > 0, "batch_size 必须大于 0"
            assert self.training_config.epochs > 0, "epochs 必须大于 0"
            assert 0 < self.training_config.learning_rate_g < 1, "learning_rate_g 必须在 (0, 1) 范围内"
            assert 0 < self.training_config.learning_rate_d < 1, "learning_rate_d 必须在 (0, 1) 范围内"
            
            # 验证数据配置
            splits_sum = (self.data_config.train_split + 
                         self.data_config.validation_split + 
                         self.data_config.test_split)
            assert abs(splits_sum - 1.0) < 1e-6, "数据分割比例之和必须等于 1.0"
            
            self.logger.info("配置验证通过")
            return True
            
        except AssertionError as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def create_experiment_dirs(self):
        """创建实验目录"""
        dirs_to_create = [
            self.experiment_config.save_dir,
            self.experiment_config.log_dir,
            self.experiment_config.tensorboard_dir
        ]
        
        for dir_path in dirs_to_create:
            os.makedirs(dir_path, exist_ok=True)
            self.logger.info(f"创建目录: {dir_path}")
    
    def get_model_kwargs(self) -> Dict[str, Any]:
        """获取模型初始化参数"""
        return asdict(self.model_config)
    
    def get_training_kwargs(self) -> Dict[str, Any]:
        """获取训练参数"""
        return asdict(self.training_config)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ConfigManager(config_path={self.config_path})"
    
    def __repr__(self) -> str:
        return self.__str__()

#!/usr/bin/env python3
"""
运行改进后的Hybrid训练脚本
使用强化版判别器训练 (n_critic=5)
"""

import subprocess
import sys
import datetime
import os

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查必要文件
    required_files = [
        "scripts/acgan_triplet_Hybrid.py",
        "data/dataset_train_case1.npz",
        "data/dataset_test_case1.npz"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 所有必要文件存在")
    return True

def run_training_in_docker():
    """在Docker环境中运行训练"""
    print("🚀 在Docker环境中启动改进后的Hybrid训练...")
    print("🎯 使用强化版判别器 (n_critic=5)")
    print("=" * 60)
    
    try:
        # 构建Docker命令
        docker_cmd = [
            "docker", "exec", "-it", "acgan-container",
            "python", "scripts/acgan_triplet_Hybrid.py"
        ]
        
        print(f"📋 执行命令: {' '.join(docker_cmd)}")
        print("⏰ 训练开始...")
        print("💡 提示: 使用Ctrl+C可以安全停止训练")
        print("-" * 60)
        
        # 运行训练
        result = subprocess.run(docker_cmd, check=False)
        
        if result.returncode == 0:
            print("\n✅ 训练完成!")
        else:
            print(f"\n⚠️ 训练结束，返回码: {result.returncode}")
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断训练")
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")

def run_training_local():
    """在本地环境中运行训练"""
    print("🚀 在本地环境中启动改进后的Hybrid训练...")
    print("🎯 使用强化版判别器 (n_critic=5)")
    print("=" * 60)
    
    try:
        # 构建本地命令
        local_cmd = [sys.executable, "scripts/acgan_triplet_Hybrid.py"]
        
        print(f"📋 执行命令: {' '.join(local_cmd)}")
        print("⏰ 训练开始...")
        print("💡 提示: 使用Ctrl+C可以安全停止训练")
        print("-" * 60)
        
        # 运行训练
        result = subprocess.run(local_cmd, check=False)
        
        if result.returncode == 0:
            print("\n✅ 训练完成!")
        else:
            print(f"\n⚠️ 训练结束，返回码: {result.returncode}")
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断训练")
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")

def show_training_info():
    """显示训练信息"""
    print("📊 改进后的训练配置:")
    print("=" * 50)
    print("🔧 主要改进:")
    print("   ✅ n_critic = 5 (强化判别器训练)")
    print("   ✅ 每次判别器更新使用独立数据采样")
    print("   ✅ 防止判别器过拟合到单个批次")
    print("   ✅ 提高训练稳定性")
    
    print("\n🎯 预期效果:")
    print("   📈 更稳定的对抗训练")
    print("   📈 更好的生成质量")
    print("   📈 更高的分类准确率")
    print("   📈 减少模式坍塌")
    
    print("\n⏰ 预计训练时间:")
    print("   🕐 完整训练: 2-4小时 (2000 epochs)")
    print("   🕐 初步结果: 30-60分钟 (前200 epochs)")
    
    print("\n📋 监控指标:")
    print("   📊 判别器损失 (D loss)")
    print("   📊 生成器损失 (G loss)")
    print("   📊 分类准确率 (LinearSVM, RandomForest, NaiveBayes, MLP)")
    print("   📊 Triplet损失, Center损失, CRL损失")

def main():
    """主函数"""
    print("🚀 改进后的Hybrid训练启动器")
    print(f"⏰ 开始时间: {datetime.datetime.now()}")
    print("🎯 目标: 使用强化版判别器提高训练效果")
    print("=" * 70)
    
    # 显示训练信息
    show_training_info()
    
    # 检查环境
    if not check_environment():
        return
    
    # 选择运行环境
    print(f"\n🔧 选择运行环境:")
    print("1. Docker环境 (推荐)")
    print("2. 本地环境")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        # 检查Docker是否可用
        try:
            subprocess.run(["docker", "ps"], capture_output=True, check=True)
            run_training_in_docker()
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Docker不可用，切换到本地环境")
            run_training_local()
    elif choice == "2":
        run_training_local()
    else:
        print("❌ 无效选择，使用默认Docker环境")
        run_training_in_docker()
    
    print(f"\n⏰ 结束时间: {datetime.datetime.now()}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应权重机制问题分析和改进建议
"""

import numpy as np
import matplotlib.pyplot as plt
import json

class AdaptiveWeightAnalyzer:
    """自适应权重分析器"""
    
    def __init__(self):
        self.problems_identified = []
        self.improvement_suggestions = []
        
    def analyze_weight_distribution(self, adaptive_weights, base_weights):
        """分析权重分布问题"""
        print("=== 权重分布分析 ===")
        
        triplet_ratio = adaptive_weights['lambda_triplet'] / base_weights['lambda_triplet']
        cla_ratio = adaptive_weights['lambda_cla'] / base_weights['lambda_cla']
        
        print(f"Triplet权重变化倍数: {triplet_ratio:.2f}")
        print(f"Classification权重变化倍数: {cla_ratio:.2f}")
        print(f"权重比例差异: {triplet_ratio/cla_ratio:.2f}")
        
        # 问题1: 权重变化过于极端
        if triplet_ratio > 2.5 or cla_ratio < 0.6:
            self.problems_identified.append("权重调整过于极端，可能导致训练不平衡")
            
        # 问题2: 权重比例失衡
        if abs(triplet_ratio - cla_ratio) > 2.0:
            self.problems_identified.append("两个损失权重变化方向相反且幅度大，可能破坏平衡")
            
    def analyze_improvement_magnitude(self, improvement_data):
        """分析改进幅度"""
        print(f"\n=== 改进幅度分析 ===")
        
        abs_improvement = improvement_data['accuracy_improvement']
        rel_improvement = improvement_data['improvement_percentage']
        
        print(f"绝对改进: {abs_improvement:.4f}")
        print(f"相对改进: {rel_improvement:.2f}%")
        
        # 问题3: 改进幅度较小
        if rel_improvement < 5.0:
            self.problems_identified.append("改进幅度较小，可能不够显著")
            
        # 问题4: 改进不稳定
        if abs_improvement < 0.02:
            self.problems_identified.append("绝对改进很小，可能存在随机性影响")
            
    def analyze_difficulty_correlation(self, difficulty_score, weight_adjustment):
        """分析难度与权重调整的相关性"""
        print(f"\n=== 难度-权重相关性分析 ===")
        print(f"任务难度分数: {difficulty_score:.4f}")
        print(f"Triplet权重调整系数: {weight_adjustment['adjustment_ratio_triplet']:.4f}")
        print(f"分类权重调整系数: {weight_adjustment['adjustment_ratio_cla']:.4f}")
        
        # 问题5: 调整策略可能不合理
        if difficulty_score > 1.5 and weight_adjustment['adjustment_ratio_triplet'] > 2.5:
            self.problems_identified.append("高难度任务过度增强Triplet损失，可能导致过拟合")
            
    def generate_improvement_suggestions(self):
        """生成改进建议"""
        print(f"\n=== 改进建议 ===")
        
        suggestions = [
            "1. 权重调整策略优化：",
            "   - 限制权重变化范围: [0.5x, 2.0x]",
            "   - 使用平滑调整函数而非线性调整",
            "   - 考虑权重之间的平衡约束",
            "",
            "2. 难度评估机制改进：",
            "   - 结合更多维度：语义相似度、类间距离、数据分布",
            "   - 引入历史训练性能作为难度指标",
            "   - 考虑动态难度评估",
            "",
            "3. 自适应策略改进：",
            "   - 渐进式权重调整",
            "   - 基于验证集性能的反馈调整",
            "   - 多阶段自适应策略",
            "",
            "4. 实验设计改进：",
            "   - 增加更多分组的对比实验",
            "   - 进行多次独立实验取平均",
            "   - 分析不同epoch的权重变化轨迹"
        ]
        
        for suggestion in suggestions:
            print(suggestion)

def improved_adaptive_weight_strategy():
    """改进的自适应权重策略"""
    
    strategy_code = '''
class ImprovedAdaptiveWeightScheduler:
    """改进的自适应权重调度器"""
    
    def __init__(self, base_lambda_triplet=10, base_lambda_cla=10, 
                 max_ratio=2.0, min_ratio=0.5, smoothness=0.5):
        self.base_lambda_triplet = base_lambda_triplet
        self.base_lambda_cla = base_lambda_cla
        self.max_ratio = max_ratio  # 最大调整比例
        self.min_ratio = min_ratio  # 最小调整比例
        self.smoothness = smoothness  # 平滑度参数
        
    def compute_adaptive_weights(self, difficulty_score, epoch=None, total_epochs=None):
        """计算自适应权重"""
        
        # 1. 平滑的难度映射函数
        normalized_difficulty = np.tanh(difficulty_score - 1.0)  # 将难度映射到[-1,1]
        
        # 2. 限制调整范围的权重计算
        triplet_ratio = 1.0 + normalized_difficulty * (self.max_ratio - 1.0) * self.smoothness
        cla_ratio = 1.0 - normalized_difficulty * (1.0 - self.min_ratio) * self.smoothness
        
        # 3. 确保权重在合理范围内
        triplet_ratio = np.clip(triplet_ratio, self.min_ratio, self.max_ratio)
        cla_ratio = np.clip(cla_ratio, self.min_ratio, self.max_ratio)
        
        # 4. 可选：基于训练进度的动态调整
        if epoch is not None and total_epochs is not None:
            progress = epoch / total_epochs
            # 训练后期减少权重调整幅度
            decay_factor = 1.0 - 0.5 * progress
            triplet_ratio = 1.0 + (triplet_ratio - 1.0) * decay_factor
            cla_ratio = 1.0 + (cla_ratio - 1.0) * decay_factor
        
        return {
            'lambda_triplet': self.base_lambda_triplet * triplet_ratio,
            'lambda_cla': self.base_lambda_cla * cla_ratio,
            'triplet_ratio': triplet_ratio,
            'cla_ratio': cla_ratio
        }
'''
    
    print("=== 改进的自适应权重策略代码 ===")
    print(strategy_code)

def main():
    """主分析函数"""
    
    # 模拟实验数据
    adaptive_weights = {
        'lambda_triplet': 27.94,
        'lambda_cla': 5.27,
        'adjustment_ratio_triplet': 2.794,
        'adjustment_ratio_cla': 0.527
    }
    
    base_weights = {
        'lambda_triplet': 10,
        'lambda_cla': 10
    }
    
    improvement_data = {
        'accuracy_improvement': 0.017013888888888884,
        'improvement_percentage': 3.3108108108108105
    }
    
    difficulty_score = 1.7941107824805875
    
    # 创建分析器
    analyzer = AdaptiveWeightAnalyzer()
    
    # 执行分析
    analyzer.analyze_weight_distribution(adaptive_weights, base_weights)
    analyzer.analyze_improvement_magnitude(improvement_data)
    analyzer.analyze_difficulty_correlation(difficulty_score, adaptive_weights)
    
    # 输出发现的问题
    print(f"\n=== 发现的问题 ({len(analyzer.problems_identified)}个) ===")
    for i, problem in enumerate(analyzer.problems_identified, 1):
        print(f"{i}. {problem}")
    
    # 生成改进建议
    analyzer.generate_improvement_suggestions()
    
    # 展示改进策略
    print(f"\n")
    improved_adaptive_weight_strategy()
    
    print(f"\n=== 总结 ===")
    print("虽然自适应权重在数值上有提升，但存在以下核心问题：")
    print("1. 权重调整过于激进，破坏了损失函数的平衡")
    print("2. 改进幅度较小，实际意义有限")
    print("3. 需要更温和、更智能的自适应策略")
    print("4. 建议采用改进的平滑调整策略和更全面的评估机制")

if __name__ == "__main__":
    main() 
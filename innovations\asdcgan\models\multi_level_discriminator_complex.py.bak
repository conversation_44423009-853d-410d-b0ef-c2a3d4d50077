"""
多层次判别器模块 (PyTorch版本)

集成特征级、属性级和语义级的多层次判别机制。

核心功能：
1. 特征级判别 - 判断特征的真实性
2. 属性级判别 - 验证属性的一致性
3. 语义级判别 - 评估语义距离的合理性
4. 自适应融合 - 动态权重平衡各级别损失

技术特点：
- 多层次判别架构
- 谱归一化稳定训练
- 梯度惩罚机制
- 自适应权重融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.utils import spectral_norm
import numpy as np
    """谱归一化层"""
    def __init__(self, module, name='weight', power_iterations=1):
        super(SpectralNorm, self).__init__()
        self.module = module
        self.name = name
        self.power_iterations = power_iterations
        if not self._made_params():
            self._make_params()

    def _update_u_v(self):
        u = getattr(self.module, self.name + "_u")
        v = getattr(self.module, self.name + "_v")
        w = getattr(self.module, self.name + "_bar")

        height = w.data.shape[0]
        for _ in range(self.power_iterations):
            v.data = F.normalize(torch.mv(torch.t(w.view(height,-1).data), u.data))
            u.data = F.normalize(torch.mv(w.view(height,-1).data, v.data))

        sigma = u.dot(w.view(height, -1).mv(v))
        setattr(self.module, self.name, w / sigma.expand_as(w))

    def _made_params(self):
        try:
            u = getattr(self.module, self.name + "_u")
            v = getattr(self.module, self.name + "_v")
            w = getattr(self.module, self.name + "_bar")
            return True
        except AttributeError:
            return False

    def _make_params(self):
        w = getattr(self.module, self.name)

        height = w.data.shape[0]
        width = w.view(height, -1).data.shape[1]

        u = nn.Parameter(w.data.new(height).normal_(0, 1), requires_grad=False)
        v = nn.Parameter(w.data.new(width).normal_(0, 1), requires_grad=False)
        u.data = F.normalize(u.data)
        v.data = F.normalize(v.data)
        w_bar = nn.Parameter(w.data)

        del self.module._parameters[self.name]

        self.module.register_parameter(self.name + "_u", u)
        self.module.register_parameter(self.name + "_v", v)
        self.module.register_parameter(self.name + "_bar", w_bar)

    def forward(self, *args):
        self._update_u_v()
        return self.module.forward(*args)


def spectral_norm(module, name='weight', power_iterations=1):
    """应用谱归一化"""
    return SpectralNorm(module, name, power_iterations)


class FeatureDiscriminator(nn.Module):
    """特征级判别器"""
    
    def __init__(self, feature_dim, hidden_dims=None, use_spectral_norm=True):
        super(FeatureDiscriminator, self).__init__()
        
        if hidden_dims is None:
            hidden_dims = [256, 128, 64]
        
        layers = []
        input_dim = feature_dim
        
        for h_dim in hidden_dims:
            linear = nn.Linear(input_dim, h_dim)
            if use_spectral_norm:
                linear = spectral_norm(linear)
            layers.append(linear)
            layers.append(nn.LeakyReLU(0.2))
            layers.append(nn.Dropout(0.1))
            input_dim = h_dim
        
        # 输出层
        output_layer = nn.Linear(input_dim, 1)
        if use_spectral_norm:
            output_layer = spectral_norm(output_layer)
        layers.append(output_layer)
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, features):
        return self.network(features)


class AttributeDiscriminator(nn.Module):
    """属性级判别器"""
    
    def __init__(self, feature_dim, attribute_dim, hidden_dims=None, use_spectral_norm=True):
        super(AttributeDiscriminator, self).__init__()
        
        if hidden_dims is None:
            hidden_dims = [128, 64]
        
        layers = []
        input_dim = feature_dim + attribute_dim
        
        for h_dim in hidden_dims:
            linear = nn.Linear(input_dim, h_dim)
            if use_spectral_norm:
                linear = spectral_norm(linear)
            layers.append(linear)
            layers.append(nn.LeakyReLU(0.2))
            layers.append(nn.Dropout(0.1))
            input_dim = h_dim
        
        # 输出层
        output_layer = nn.Linear(input_dim, 1)
        if use_spectral_norm:
            output_layer = spectral_norm(output_layer)
        layers.append(output_layer)
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, features, attributes):
        x = torch.cat([features, attributes], dim=1)
        return self.network(x)


class SemanticDiscriminator(nn.Module):
    """语义级判别器"""
    
    def __init__(self, feature_dim, hidden_dims=None, use_spectral_norm=True):
        super(SemanticDiscriminator, self).__init__()
        
        if hidden_dims is None:
            hidden_dims = [128, 64]
        
        layers = []
        input_dim = feature_dim * 2 + 1  # 两个特征 + 语义距离
        
        for h_dim in hidden_dims:
            linear = nn.Linear(input_dim, h_dim)
            if use_spectral_norm:
                linear = spectral_norm(linear)
            layers.append(linear)
            layers.append(nn.LeakyReLU(0.2))
            layers.append(nn.Dropout(0.1))
            input_dim = h_dim
        
        # 输出层
        output_layer = nn.Linear(input_dim, 1)
        if use_spectral_norm:
            output_layer = spectral_norm(output_layer)
        layers.append(output_layer)
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, features, ref_features, semantic_distance):
        x = torch.cat([features, ref_features, semantic_distance], dim=1)
        return self.network(x)


class MultiLevelDiscriminator(nn.Module):
    """
    多层次判别器
    
    集成特征级、属性级和语义级判别。
    """
    
    def __init__(self, 
                 feature_dim=52,
                 attribute_dim=20,
                 hidden_dims=None,
                 use_spectral_norm=True,
                 fusion_mode='weighted'):
        """
        初始化多层次判别器
        
        Args:
            feature_dim: 特征维度
            attribute_dim: 属性维度
            hidden_dims: 隐藏层维度
            use_spectral_norm: 是否使用谱归一化
            fusion_mode: 融合模式 ('weighted', 'attention', 'concat')
        """
        super(MultiLevelDiscriminator, self).__init__()
        
        self.feature_dim = feature_dim
        self.attribute_dim = attribute_dim
        self.fusion_mode = fusion_mode
        
        # 特征级判别器
        self.feature_discriminator = FeatureDiscriminator(
            feature_dim, hidden_dims, use_spectral_norm
        )
        
        # 属性级判别器
        self.attribute_discriminator = AttributeDiscriminator(
            feature_dim, attribute_dim, hidden_dims, use_spectral_norm
        )
        
        # 语义级判别器
        self.semantic_discriminator = SemanticDiscriminator(
            feature_dim, hidden_dims, use_spectral_norm
        )
        
        # 融合网络
        if fusion_mode == 'weighted':
            self.fusion_weights = nn.Parameter(torch.ones(3) / 3)
        elif fusion_mode == 'attention':
            self.attention_network = nn.Sequential(
                nn.Linear(3, 16),
                nn.ReLU(),
                nn.Linear(16, 3),
                nn.Softmax(dim=-1)
            )
        elif fusion_mode == 'concat':
            fusion_layer = nn.Linear(3, 1)
            if use_spectral_norm:
                fusion_layer = spectral_norm(fusion_layer)
            self.fusion_network = fusion_layer
    
    def forward(self, inputs):
        """
        前向传播
        
        Args:
            inputs: 输入字典，包含:
                - features: 特征 [batch_size, feature_dim]
                - attributes: 属性 [batch_size, attribute_dim]
                - ref_features: 参考特征 (可选)
                - semantic_distance: 语义距离 (可选)
                
        Returns:
            result_dict: 判别结果字典
        """
        features = inputs['features']
        attributes = inputs['attributes']
        
        # 1. 特征级判别
        feature_validity = self.feature_discriminator(features)
        
        # 2. 属性级判别
        attribute_validity = self.attribute_discriminator(features, attributes)
        
        # 3. 语义级判别
        if 'ref_features' in inputs and 'semantic_distance' in inputs:
            ref_features = inputs['ref_features']
            semantic_distance = inputs['semantic_distance']
            semantic_validity = self.semantic_discriminator(features, ref_features, semantic_distance)
        else:
            # 如果没有提供参考特征，使用零特征和零距离
            ref_features = torch.zeros_like(features)
            semantic_distance = torch.zeros(features.shape[0], 1, device=features.device)
            semantic_validity = self.semantic_discriminator(features, ref_features, semantic_distance)
        
        # 4. 融合多层次结果
        if self.fusion_mode == 'weighted':
            weights = F.softmax(self.fusion_weights, dim=0)
            final_validity = (weights[0] * feature_validity + 
                            weights[1] * attribute_validity + 
                            weights[2] * semantic_validity)
        elif self.fusion_mode == 'attention':
            # 计算注意力权重
            validity_stack = torch.stack([
                feature_validity.squeeze(-1),
                attribute_validity.squeeze(-1),
                semantic_validity.squeeze(-1)
            ], dim=-1)  # [batch_size, 3]
            
            attention_weights = self.attention_network(validity_stack)  # [batch_size, 3]
            final_validity = torch.sum(validity_stack * attention_weights, dim=-1, keepdim=True)
        elif self.fusion_mode == 'concat':
            validity_concat = torch.cat([
                feature_validity, attribute_validity, semantic_validity
            ], dim=-1)
            final_validity = self.fusion_network(validity_concat)
        else:
            # 默认简单平均
            final_validity = (feature_validity + attribute_validity + semantic_validity) / 3
        
        return {
            'final_validity': final_validity,
            'feature_validity': feature_validity,
            'attribute_validity': attribute_validity,
            'semantic_validity': semantic_validity
        }
    
    def compute_gradient_penalty(self, real_samples, fake_samples, attributes, lambda_gp=10.0):
        """
        计算梯度惩罚
        
        Args:
            real_samples: 真实样本
            fake_samples: 生成样本
            attributes: 属性
            lambda_gp: 梯度惩罚权重
            
        Returns:
            gradient_penalty: 梯度惩罚
        """
        batch_size = real_samples.shape[0]
        device = real_samples.device
        
        # 随机插值
        alpha = torch.rand(batch_size, 1, device=device)
        interpolated = alpha * real_samples + (1 - alpha) * fake_samples
        interpolated.requires_grad_(True)
        
        # 计算插值样本的判别结果
        inputs = {
            'features': interpolated,
            'attributes': attributes
        }
        d_interpolated = self.forward(inputs)['final_validity']
        
        # 计算梯度
        gradients = torch.autograd.grad(
            outputs=d_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(d_interpolated),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]
        
        # 计算梯度惩罚
        gradient_penalty = lambda_gp * ((gradients.norm(2, dim=1) - 1) ** 2).mean()
        
        return gradient_penalty

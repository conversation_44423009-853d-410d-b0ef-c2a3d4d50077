# Improved Generative Adversarial Networks With Filtering Mechanism for Fault Data Augmentation

Lexuan <PERSON> \( {}^{ \oplus  } \) , <PERSON><PERSON><PERSON> \( {}^{ \oplus  } \) , Member, IEEE, Bin Jiang \( {}^{ \oplus  } \) , Fellow, IEEE, <PERSON><PERSON><PERSON> \( {}^{ \circ  } \) , Senior Member, IEEE, Le Song, and <PERSON><PERSON> Liu

Abstract-Few-shot fault diagnosis (i.e., fault diagnosis with few samples) is a challenging issue in practice because fault samples are scarce and difficult to obtain. Data augmentation based on generative adversarial networks (GANs) has proven to be an effective solution. However, it often encounters problems such as difficult model training and low quality of generated samples. In this article, an improved GAN with filtering mechanism is developed for fault data augmentation, which introduces the self-attention mechanism and instance normalization (IN) into the GAN structure and utilizes a filtering mechanism. The implementation process comprises two parts, sample generation and abnormal sample filtering. The self-attention mechanism and IN adopted in sample generation can make the generative model easy to train and have better quality of the generated samples. For abnormal sample filtering, the isolated forest (IF) algorithm is used for detecting low-quality generated samples. The effectiveness of the proposed fault data augmentation method is verified using two public datasets for fault diagnosis purposes, and the results show that the proposed method can have better performance over the state-of-art GAN-based data augmentation methods.

![bo_d2311hjef24c73anl120_0_936_694_717_358_0.jpg](images/bo_d2311hjef24c73anl120_0_936_694_717_358_0.jpg)

Index Terms-Data augmentation, few-shot fault diagnosis, filtering mechanism, generative adversarial network (GAN).

## I. INTRODUCTION

FAULT diagnosis is critically important to improving the safety of modern engineering systems by promptly detecting and isolating abnormal parts, preventing fault propagation and evolution, hence reducing the loss after unexpected failures occur [1]. Benefiting from advanced sensing and information technology, data-driven fault diagnosis techniques have been deeply studied and widely adopted in many areas [2], [3], [4], [5]. However, most existing data-driven fault diagnosis methods require enough fault samples to develop fault diagnosis models, while fault samples are indeed scarce and difficult to obtain in real-world applications. It poses great challenges to implement the state-of-the-art data-driven fault diagnosis techniques. In recent years, fault diagnosis with limited fault samples has been attracting increasing attention in both academia and industries [6], [7], [8].

Few-shot learning is a promising solution to solve the problem of fault diagnosis when there is a lack of enough fault samples. A new direction called few-shot fault diagnosis has arisen, wherein generative adversarial networks (GANs)-based data augmentation is a hotspot [9], [10]. Shao et al. [11] used the auxiliary classifier GAN (ACGAN) to generate samples for improving the accuracy of diagnosis. Wang et al. [12] addressed the problems of noise contamination and few fault samples in planetary gearbox vibration signals, and then a stacked noise-reducing autoencoder was introduced into GAN. Yang et al. [13] transformed 1-D fault signals into 2-D gray images and then a conditional GAN (CGAN) was used to generate gray image samples for fault diagnosis in presence of sample imbalance. Liu et al. [14] transformed normal wind turbine signals into rough fault signals using prior knowledge and then used GAN to purify the rough fault signals to obtain synthetic signals similar to the real fault signals. Moreover, to improve the structure of GAN for better learning the temporal signals, Yu et al. [15] combined convolutional neural network (CNN) and long short-term memory (LSTM) in a GAN model to better capture the temporal features from telemetry data of space vehicles. Furthermore, Gao et al. [16] enlarged fault samples by a combination of numerical simulation and GAN. Lou et al. [17] used GAN-based domain adaptation network to make original simulation fault samples similar to the measured samples.

---

Manuscript received 7 May 2023; accepted 20 May 2023. Date of publication 30 May 2023; date of current version 29 June 2023. This work was supported in part by the National Key Research and Development Program of China under Grant 2021YFB3301300. The associate editor coordinating the review of this article and approving it for publication was Dr. Pedro Oliveira Conceição Junior. (Corresponding author: Ningyun Lu.)

Lexuan Shao, Ningyun Lu, and Bin Jiang are with the College of Automation Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing 210016, China (e-mail: <EMAIL>; <EMAIL>; <EMAIL>).

Silvio Simani is with the Department of Engineering, University of Ferrara, 44122 Ferrara, Italy (e-mail: <EMAIL>).

Le Song and Zhengyuan Liu are with AVIC Xi'an Flight Automatic Control Research Institute, Xi'an 710076, China (e-mail: <EMAIL>; <EMAIL>).

Digital Object Identifier 10.1109/JSEN.2023.3279436

---

In general, GAN-based fault data augmentation has made some preliminary progress for few-shot fault diagnosis [18]. Nevertheless, there are some fundamental problems unsolved such as unstable network training [19], low high-dimensional sample generation quality, and the lack of quality evaluation of the model [20], [21]. The unequal capabilities of the generator and discriminator of GAN can easily lead to the disappearance of the loss gradient of one party and the sharp increase of the loss value of the other party. When the dimension of the generated sample is high enough, GAN is often difficult to capture the details, which leads to the generated sample being rough and some anomalies.

Aiming to solve the above problems, an improved GAN embedded with a filtering mechanism is proposed for fault data augmentation in this article. First, the self-attention (SA) mechanism is introduced to complement the convolutional operation, which can only focus on local delivery information. After adding the SA mechanism, the model will be able to focus on global delivery information [22], [23], enabling the model to better learn the distribution of long-term series. Second, the instance normalization (IN) layer is added after each layer of convolution. IN not only accelerates the convergence of the model [24] but also maintains the independence between each fault sample instance, which is useful to improve the generalization performance of the generated samples. Third, after using the trained GAN to generate fault samples, an isolated forest (IF)-based filtering mechanism is added to filter out the samples with poor quality. The remaining generated samples are used as the training set for the subsequent classifier together with the real samples. Finally, to address the difficulty of model performance assessment, this article considers assessing the quality of the generated samples from multiple perspectives. We compare the spectrum of real and generated samples, and reduce both to two dimensions for visual analysis. Some quantitative metrics such as maximum mean discrepancy (MMD) and Fréchet inception distance (FID) are selected to quantify the distance between the distribution of the generated samples and the true distribution. The fault diagnosis accuracy after data augmentation will be compared with other methods. We remove each module one by one and analyze the difference in model performance to verify the role of each module.

The contributions of this article can be summarized as follows.

1) A generative model called improved GAN is proposed, which has a more stable training process and better ability to generate fault samples due to the addition of SA and IN.

![bo_d2311hjef24c73anl120_1_924_153_726_235_0.jpg](images/bo_d2311hjef24c73anl120_1_924_153_726_235_0.jpg)

Fig. 1. Architecture of the GAN.

2) An IF-based filtering mechanism is added after the generative model to ensure that low-quality generated samples are excluded, and high-quality training samples are provided to the downstream classification tasks.

3) Multiperspective assessment experiments and ablation experiments are designed to evaluate the performance of the model and contributions of each module.

The remainder of the article is organized as follows. Section II briefly introduces some preliminary details required for the method. Section III presents the proposed model and its implementation process. Section IV verifies the validity of the developed model. Section V concludes the article with some final comments and open problem for further research.

## II. PRELIMINARY

## A. Generative Adversarial Network

A typical GAN consists of a generator and a discriminator, which belongs to a type of generative model. The basic structure of GAN is shown in Fig. 1. Inspired by the zero-sum game theory, its training process gradually improves their respective capabilities through the confrontation between the generator and the discriminator and finally achieves Nash equilibrium to complete the training [25]. The adversarial process of these two components can be described as follows: Generator tries to capture the distribution of real data as much as possible and learns the mapping from noisy \( z \) to real samples, to generate as many real samples as possible to deceive the discriminator. Moreover, discriminator is trained to distinguish real data from generated data as much as possible by maximizing the difference between them.

For simplicity, the nonlinear function mappings of the generator and discriminator are denoted as \( G\left( \cdot \right) \) and \( D\left( \cdot \right) \) , respectively. \( x \sim  {p}_{\text{data }} \) indicates the real data \( x \) sampled from the probability distribution \( {p}_{z} \) as well as \( z \sim  {p}_{z} \) indicates the noise vector \( z \) sampled from the prior distribution \( {p}_{z} \) . The generator and discriminator are jointly trained utilizing alternating optimization, and the objective function of the model can be written as follows:

\[
\mathop{\min }\limits_{G}\mathop{\max }\limits_{D}V\left( {D, G}\right)  = {E}_{x \sim  {p}_{\text{data }}}\left\lbrack  {\log D\left( x\right) }\right\rbrack
\]

\[
+ {E}_{z \sim  {p}_{z}}\left\lbrack  {\log \left( {1 - D\left( {G\left( z\right) }\right) }\right) }\right\rbrack   \tag{1}
\]

The objective function indicates that discriminator is maximized first and afterward generator is minimized. The optimal case is highlighted as

\[
{D}^{ * }\left( x\right)  = \frac{{p}_{\text{data }}\left( x\right) }{{p}_{\text{data }}\left( x\right)  + {p}_{g}\left( x\right) }. \tag{2}
\]

The model reaches Nash equilibrium when the distribution of the generated data \( {p}_{g} \) is gradually transformed into the distribution of the real data \( {p}_{\text{data }} \) through training, and the output of the optimal discriminator \( {D}^{ * }\left( x\right) \) is 0.5 .

## B. Isolation Forest

To efficiently identify anomalous samples, the IF algorithm is proposed, which solves the problem of high-computational complexity for handling high-dimensional data and too many false alarms in previous anomaly detection algorithms [26]. The premise of using IF is that the percentage of anomalous points is relatively small and distinct with respect to the normal points. From a statistical point of view, in a data space, if there are only sparsely distributed points in a region, it means that the probability of data points falling in this region is low, and therefore the points in these regions can be considered as anomalous [27]. The core of the IF algorithm is the construction of a forest (iForest) consisting of isolated trees (iTrees). To facilitate the description and calculation, the IF algorithm introduces the definition of iTrees and path lengths.

Definition 1 (iTree): Let \( T \) be a binary tree and \( N \) be a node of \( T \) . If \( N \) is a leaf node, it is said to be an external node, and if \( N \) is a node with two children, it is said to be an internal node.

Definition 2 (Path Length): In an iTree, the number of edges experienced from the root node to the external node is called the path length, denoted as \( h\left( \cdot \right) \) .

An iTree is constructed as follows: an attribute \( A \) and a split value \( p \) are randomly selected from the dataset \( X = \) \( \left\{  {{x}_{1},{x}_{2},\ldots ,{x}_{n}}\right\} \) of \( n \) instances, and then each data \( {x}_{i} \) are partitioned according to the value of \( A \) [denoted as \( {x}_{i}\left( A\right) \) ]. If \( {x}_{i}\left( A\right)  < p \) , then the data \( {x}_{i} \) are placed in the left subtree, and vice versa in the right subtree. In this way, the left and right subtrees are constructed recursively until one of the following conditions is satisfied: 1) there is only one data or multiple identical data left in \( X \) and 2) the tree reaches its maximum height. In this way, the IF algorithm constructs a certain number of iTrees to compose the iForest. Specifically, a subset of \( X \) is extracted by random sampling to construct each iTree to ensure the diversity of iTrees. By traversing each iTree in the iForest, the path length of data \( {x}_{i} \) in each tree is calculated, denoted as \( h\left( {x}_{i}\right) \) , and then the anomaly score of \( {x}_{i} \) is calculated based on \( h\left( {x}_{i}\right) \) to determine whether \( {x}_{i} \) is anomalous. The anomaly score \( \mathrm{s}\left( {{x}_{i}, n}\right) \) of data \( {x}_{i} \) is defined as (3). \( c\left( n\right) \) is normalization factor, which is calculated as shown in (4)

\[
s\left( {{x}_{i}, n}\right)  = {2}^{-\frac{E\left( {h\left( {x}_{i}\right) }\right) }{c\left( n\right) }} \tag{3}
\]

\[
c\left( n\right)  = {2H}\left( {n - 1}\right)  - \left( {2\left( {n - 1}\right) /n}\right)  \tag{4}
\]

where \( H\left( n\right) \) is a harmonic number and can be estimated by the Euler constant.

If the sample returns score close to 1 , then there is a high probability that it is an outlier; if the sample returns score close to 0 , then it is usually a normal point; if all samples return score around 0.5 , there is usually no outlier in this dataset.

## III. Proposed Method

For the purpose of addressing the issues of data augmentation described in Section I, an improved GAN with filtering mechanism is proposed. This section thoroughly describes the solution proposed in this study. Fig. 2 illustrates the overview of it, which mainly consists of four steps.

1) The sensor is installed in the appropriate position of mechanical equipment to measure the original signals. In addition, the collected data are preprocessed to fit the model preferably.

2) The improved GAN is trained separately for each type of real fault data. Subsequently, this article employs the well-trained generator to generate sufficient fault samples.

3) The generated samples of each type of fault are put into the filtering module, and the top \( {10}\% \) samples with the highest anomaly scores are deleted to obtain the filtered generated data.

4) The filtered generated data are combined with real samples to form a complete training set, which are used as inputs of the classifier for training, and eventually gains a fault classifier with favorable performance.

Among them, steps 2 and 3 are model-related, which will be introduced in this section, while step 4 represents the assessment of the method that will be presented in Section IV.

## A. Fault Data Generation

For this part, the generation of fault samples is the main task. Aiming at the deficiency of the prior GAN model, an improved GAN is designed to complete this task.

The first improvement of the GAN model is the introduction of the SA mechanism. The specific internal structure of the model is sketched in Table I. 1-D CNN is selected as the primary structural blocks of model generator and discriminator because it has the advantages of fast training and parallel computing [28]. Nonetheless, convolution only increases the convolutional kernel size to improve the receptive field, when dealing with long-range sequences [29]. Excessive convolution kernel size can lead to a burst in terms of computation. By adding a SA layer, this article increases the global feature-capturing capability of both the generator and the discriminator. The size of the convolutional kernel does not to be too large. Herein, the size of the convolutional kernel is set to 5 .

As shown in Fig. 3, the input of the SA layer is denoted as \( x \in  {\mathrm{R}}^{N \times  C} \) , where \( N \) and \( C \) represent the length of the sequences and the number of channels of the sample, respectively. Then, \( x \) is transformed into three different feature spaces that are \( f, g \) , and \( h \) , which represent query, key, and value, respectively. The calculation formulas are illustrated in the following equation:

\[
f\left( x\right)  = x{W}_{f}
\]

\[
g\left( x\right)  = x{W}_{g}
\]

\[
h\left( x\right)  = x{W}_{h} \tag{5}
\]

where \( {W}_{f} \in  {R}^{C \times  \bar{C}},{W}_{g} \in  {R}^{C \times  \bar{C}} \) , and \( {W}_{h} \in  {R}^{C \times  \bar{C}} \) are all weight matrices that are gradually updated with the training process. \( \bar{C} \) represents the number of output channels. The value of \( \bar{C} \) is set to be the same as that of \( C \) .

![bo_d2311hjef24c73anl120_3_161_169_1478_1120_0.jpg](images/bo_d2311hjef24c73anl120_3_161_169_1478_1120_0.jpg)

Fig. 2. Overall framework of the method.

Next, the attention score \( {s}_{i, j} \) , obtained from the product of \( f \) and \( g \) , is calculated as shown in (6). It is regularized latter using the softmax function, denoted as \( {a}_{i, j} \) . The specific calculation formula is shown in (7), where the subscripts \( i \) , \( j \) denote the attention score of the model at the \( i \) th point when synthesizing the \( j \) th region. After that, the obtained \( {a}_{i, j} \) and \( h \) are adopted to calculate the output vector of the SA layer corresponding to each input vector \( x \) , as defined in (9). Similarly, the weight matrix \( {W}_{v} \) is also updated with the training process

\[
{s}_{i, j} = {f}^{T}g \tag{6}
\]

\[
{\alpha }_{i, j} = \frac{\exp \left( {s}_{i, j}\right) }{\mathop{\sum }\limits_{{i = 1}}^{N}\exp \left( {s}_{i, j}\right) } \tag{7}
\]

\[
v\left( {x}_{i}\right)  = {W}_{v}{x}_{i} \tag{8}
\]

\[
{\mathrm{o}}_{j} = v\left( {\mathop{\sum }\limits_{{i = 1}}^{N}{\alpha }_{j, i}h\left( {x}_{i}\right) }\right) . \tag{9}
\]

Finally, to make the model progressively learnable from simple to complex, the output of the SA layer is further multiplied by a constant \( \gamma \) and added to the input. The constant \( \gamma \) is learnable with an initial value of 0 . Thus, the final output is implied in the following equation:

\[
{y}_{i} = \gamma {o}_{i} + {x}_{i} \tag{10}
\]

To explore the effect of the location of SA layers on the model, this study constructs multiple models, adds SA layers at different layers in each model, and measures the effect of model generation with FID and MMD metrics, as shown in Table II. All the models aforementioned have been trained for 20000 iterations. The SA layer is finally determined to be placed after the Deconv2 layer reported in Table II.

The second improvement is to add IN layer after each layer of convolution. The majority of the normalization layer used in the previous GAN is batch normalization (BN), whereas this is not suitable for fault sample augmentation. In fact, the batch size is excessively small when the learnable samples are quite scarce. It is impossible to estimate the mean and variance of the whole dataset. Therefore, this article proposes to use IN instead of BN. The former calculates the mean and standard deviation of the sample features only within the sample channel, preserving the independence of each generated sample and accelerating the model convergence when the batch size is very small. The specific operational process is as follows.

![bo_d2311hjef24c73anl120_4_159_171_707_313_0.jpg](images/bo_d2311hjef24c73anl120_4_159_171_707_313_0.jpg)

Fig. 3. Self-attention mechanism. The \( \otimes \) denotes matrix multiplication.

TABLE I

NETWORK PARAMETERS OF PROPOSED METHOD

<table><tr><td>Layer name</td><td>SAGAN-IN</td></tr><tr><td colspan="2">Generator</td></tr><tr><td>Input_size</td><td>\( {G}_{ - } \) in \( = \) Input(noise_dim \( = {512} \) )</td></tr><tr><td>Fc1</td><td>out_features \( = 7 * {256} \)</td></tr><tr><td>Reshape</td><td>dim = (7,256)</td></tr><tr><td>Deconv1</td><td>Filters \( = {128} \) , kernel_size \( = 5,\mathrm{\;s} = 2 \)</td></tr><tr><td>IN</td><td>Activation \( = \) relu(   )</td></tr><tr><td>Deconv2</td><td>Filters \( = {64} \) , kernel_size \( = 5,\mathrm{\;s} = 4 \)</td></tr><tr><td>IN</td><td>Activation \( = \) relu(   )</td></tr><tr><td>Self-adttention</td><td/></tr><tr><td>Deconv3</td><td>Filters \( = {32} \) , kernel_size \( = 5 \) , s=4</td></tr><tr><td>IN</td><td>Activation \( = \) relu(   )</td></tr><tr><td>Deconv4</td><td>Filters \( = 1 \) , kernel_size \( = 5,\mathrm{\;s} = 4 \) , Tanh(   )</td></tr><tr><td colspan="2">Discriminator</td></tr><tr><td>Conv1</td><td>Filters \( = {32} \) , kernel_size \( = 5,\mathrm{\;s} = 4 \)</td></tr><tr><td>Self-attention</td><td/></tr><tr><td>Conv2</td><td>Filters \( = {64} \) , kernel_size \( = 5,\mathrm{\;s} = 4 \)</td></tr><tr><td>IN</td><td>Activation \( = \) LeakyReLu(0.2)</td></tr><tr><td>Conv3</td><td>Filters \( = {128} \) , kernel_size \( = 5,\mathrm{\;s} = 4 \)</td></tr><tr><td>IN</td><td>Activation \( = \) LeakyReLu(0.2)</td></tr><tr><td>Conv4</td><td>Filters \( = {256} \) , kernel_size \( = 5,\mathrm{\;s} = 4 \)</td></tr><tr><td>IN</td><td>Activation \( = \) LeakyReLu(0.2)</td></tr><tr><td>Flatten</td><td/></tr><tr><td>FC1</td><td>out_features \( = 1 \)</td></tr></table>

TABLE II

EFFECT OF THE LOCATION OF THE SELF-ATTENTION MECHANISM ON MODEL

<table><tr><td rowspan="2">Model</td><td colspan="4">SAWGAN-IN</td></tr><tr><td>Deconv1</td><td>Deconv2</td><td>Decon3</td><td>Deconv4</td></tr><tr><td>FID</td><td>31.86</td><td>26.31</td><td>33.86</td><td>53.14</td></tr><tr><td>MMD</td><td>0.131</td><td>0.119</td><td>0.323</td><td>0.296</td></tr></table>

First of all, for the input data \( x \in  {R}^{M \times  N \times  C},(M \) represents the number of samples in one batch, \( C \) represents the number of channels, and \( N \) represents the length of the sequences), IN only finds the mean value \( \mu \) and variance \( \sigma \) of the data of

\( N \) dimensions of each sample, maintaining the dimensions of \( M \) and \( C \) , and the calculations are as shown in (11) and (12), where \( t \) represents the index of the sequences and \( i \) indicates the index of the channel. Next, the samples are normalized and calculated, as shown in (13)

\[
{\mu }_{\mathrm{{ti}}} = \frac{1}{N}\mathop{\sum }\limits_{{l = 1}}^{N}{x}_{\mathrm{{til}}} \tag{11}
\]

\[
{\sigma }_{\mathrm{{ti}}}{}^{2} = \frac{1}{N}\mathop{\sum }\limits_{{l = 1}}^{N}{\left( {x}_{\mathrm{{til}}} - {\mu }_{\mathrm{{ti}}}\right) }^{2} \tag{12}
\]

\[
{\widehat{x}}_{\mathrm{{til}}} = \frac{{x}_{\mathrm{{til}}} - {\mu }_{\mathrm{{ti}}}}{\sqrt{{\sigma }_{\mathrm{{ti}}}{}^{2} + \varepsilon }}. \tag{13}
\]

Then, the final output is obtained by adding the scaling \( \gamma \) and translation \( \beta \) learnable variables.

When defining the loss function of the improved GAN, Wasserstein distance [30] is selected to describe the distance between these two distributions, and the formula is

\[
W\left( {{P}_{\text{data }},{P}_{g}}\right)  = \frac{1}{k}\mathop{\sup }\limits_{{{\left| f\right| }_{L} \leq  K}}{E}_{x \sim  {P}_{\text{data }}}\left\lbrack  {f\left( x\right) }\right\rbrack   - {E}_{x \sim  {P}_{g}}\left\lbrack  {f\left( x\right) }\right\rbrack
\]

(14)

where \( {\left| f\right| }_{L} \) is the Lipschitz constant of \( f\left( x\right) \) . The gradient penalty strategy is used to satisfy the Lipschitz restriction on the Wasserstein distance. The loss function of model is

\[
L = \underset{x \sim  {P}_{g}}{E}\left\lbrack  {D\left( x\right) }\right\rbrack   - \underset{x \sim  {P}_{\text{data }}}{E}\left\lbrack  {D\left( x\right) }\right\rbrack
\]

\[
+ \lambda \underset{{x}^{ * } \sim  {P}_{{x}^{ * }}}{E}\left\lbrack  {\left( {\begin{Vmatrix}{\nabla }_{{x}^{ * }}D\left( {x}^{ * }\right) \end{Vmatrix}}_{2} - 1\right) }^{2}\right\rbrack  . \tag{15}
\]

Both generator and discriminator are trained using Adam optimizer, and the learning rate is set to 0.0001 . The generator is updated once after every five times' updates of the discriminator.

## B. Filtering of Generated Data

For the second part, the main goal is deleting these low-quality samples mixed in the generated samples. The IF algorithm is selected as the filtering mechanism. After obtaining a well-trained GAN, a large number of samples will be acquired, which ought to be close to the distribution of the real samples. Nonetheless, through experiments, it is found that some generated samples deviate from the distribution of real samples. These abnormal samples are few and distinct from normal samples, which is in line with the premise of the IF algorithm. These abnormal samples mixed in the generated samples reduce the overall quality of the generated samples, directly reducing the effect of data augmentation. Therefore, these abnormal samples are of low quality that require be deleted. This study calculates the anomaly score of each sample through the IF algorithm and deletes the samples with the highest score. The specific calculation formula is shown in (4).

The most important parameter in IF is contamination, which determines the proportion of samples to be removed. Different values are set for the parameter contamination, as shown in Table III. The results illustrate that the model performs best at 0.1 , thus the recommended parameter is set to 0.1 . The generated samples are used as the inputs for IF to obtain the anomaly scores of each generated sample, remove the top 10% of samples with the highest scores, and keep 90% samples.

TABLE III

Effect of Parameter Values of IFs on Model Performance

<table><tr><td rowspan="2">Parameter</td><td colspan="4">Value of contamination</td></tr><tr><td>0.05</td><td>0.1</td><td>0.15</td><td>0.2</td></tr><tr><td>FID</td><td>25.37</td><td>23.44</td><td>27.82</td><td>34.2</td></tr><tr><td>MMD</td><td>0.117</td><td>0.121</td><td>0.134</td><td>0.21</td></tr></table>

## IV. CASE STUDY

This article has considered two different datasets to evaluate the proposed method for fault data augmentation. One is the popular Case Western Reserve University (CWRU) bearing dataset [31], which is employed to verify the model's ability to generate vibration signals. Another is a real wing beam dataset [32]. Notably, it is used to verify the model's ability to generate signal details, which is a harder task since there is little difference between the guided wave signals of different faults. In addition, the ablation experiments are designed to validate the contributions of each module.

## A. Vibration Data Generation Experiment

1) Data Description: The first case study uses the bearing dataset from CWRU, USA. The bearing faults are induced by machining single-point damage on inner ring, outer ring, and rolling body by electric spark, respectively, with three types of damage diameters of 0.1778,0.3556, and \( {0.5334}\mathrm{\;{mm}} \) . An acceleration sensor is placed above the bearing housing at the fan end and the drive end of the motor, with the vibration acceleration signals of the faulty bearing under different motor loads being collected with \( {12}\mathrm{k} \) and \( {48}\mathrm{k} \) sampling frequencies, respectively. In this article, the vibration data of the driving end bearing with the rotation speed of \( {1797}\mathrm{r}/\mathrm{{min}}\left( {0\mathrm{{hp}}}\right) \) and the sampling frequency of \( {12}\mathrm{{kHz}} \) are selected as the experimental object. The specific experimental data are shown in Table IV. Additionally, more detailed description can be referred in the literature [31]. The experimental platform is depicted in Fig. 4.

2) Experimental Verification and Analysis: To make the experimental verification more in line with the actual situation, a scenario often encountered in data-driven fault diagnosis is designed: class imbalance, where normal samples are sufficient and various types of fault samples are scarce [33], [34]. There are ten categories in total, including nine fault conditions and one normal condition. For each type of fault, their sample size is relatively small compared to the sample size under normal condition.

Five groups of experiments with different imbalance rates are designed, which are called Groups A, B, C, D, and E. In the experiment of Group A, the imbalance rate is set to 1:20, where five real samples for each fault condition and 100 samples for the normal condition. Five samples of each type of fault are used as training samples for GAN separately. In the experiment of Group B, the imbalance rate is set to 1:10. Groups C and D are similar to groups A and B, except that real samples are added during the training of the classifiers. The experiment with Groups E and F does not use the data augmentation method. In each group, only fault samples need to be augmented. We only train GANs for nine types of fault samples to alleviate the class imbalance situation. About 100 samples are generated using the corresponding GAN for each type of fault, then filter out \( {10}\% \) of the generated samples. A total of 90 samples for each type of fault are used as the training set for the classifier in Groups A and B. For Groups \( \mathrm{C} \) and \( \mathrm{D},{90} \) generated samples plus the original five and ten real samples, totaling 95 and 100 samples for each type of fault are used as the training set for the classifier. Normal class samples directly enter the training set of the classifier without augmentation. The detailed sample configurations are shown in Table V. Notably, the learning ability of the classifier doesn't have to be extremely strong, since it is aimed at better verifying the role of data augmentation for diagnosis purpose. The structure of the classifier is shown in Table VI.

TABLE IV

DATA USED IN EXPERIMENT

<table><tr><td>Label</td><td>State</td><td>Fault diameter</td><td>Data length</td></tr><tr><td>0</td><td>Ball fault</td><td>0.1778mm</td><td>896</td></tr><tr><td>1</td><td>Ball fault</td><td>0.3556mm</td><td>896</td></tr><tr><td>2</td><td>Ball fault</td><td>0.5334mm</td><td>896</td></tr><tr><td>3</td><td>Inner race fault</td><td>0.1778mm</td><td>896</td></tr><tr><td>4</td><td>Inner race fault</td><td>0.3556mm</td><td>896</td></tr><tr><td>5</td><td>Inner race fault</td><td>0.5334mm</td><td>896</td></tr><tr><td>6</td><td>Outer race fault</td><td>0.1778mm</td><td>896</td></tr><tr><td>7</td><td>Outer race fault</td><td>0.3556mm</td><td>896</td></tr><tr><td>8</td><td>Outer race fault</td><td>0.5334mm</td><td>896</td></tr><tr><td>9</td><td>Normal</td><td>-</td><td>896</td></tr></table>

![bo_d2311hjef24c73anl120_5_944_718_684_410_0.jpg](images/bo_d2311hjef24c73anl120_5_944_718_684_410_0.jpg)

Fig. 4. Bearing fault test bench of CWRU dataset.

For the purpose of indicating the training process of the model, the loss curves of the model are illustrated in Fig. 5. The model converges after approximately 10000 iterations, and the losses of both the discriminator and generator are close to 0 . The overall training process is stable. Figs. 6 and 7 compare the distributions of the generated samples and the real samples in terms of time and frequency domains, respectively. It can be seen that the overall characteristics of the generated samples generally match those of the real samples, which also retain the diversity. In addition, this article uses the \( T \) -distributed stochastic neighbor embedding (t-SNE) dimensionality reduction in the frequency domain space and makes the visualization analysis, and the results are shown in Fig. 8.

TABLE V

DETAILED SAMPLE CONFIGURATIONS IN EXPERIMENTS

<table><tr><td rowspan="2">(per fault class)</td><td colspan="2">GAN</td><td colspan="2">Classifier</td></tr><tr><td>Training set</td><td>Generated data</td><td>Training set</td><td>Test set</td></tr><tr><td>A(1:20)</td><td>5</td><td>100</td><td>90</td><td>50</td></tr><tr><td>\( \mathrm{B}\left( {1 : {10}}\right) \)</td><td>10</td><td>100</td><td>90</td><td>50</td></tr><tr><td>C(1:20)</td><td>5</td><td>100</td><td>95</td><td>50</td></tr><tr><td>D(1:10)</td><td>10</td><td>100</td><td>100</td><td>50</td></tr><tr><td>\( \mathrm{E}\left( {1 : {20}}\right) \)</td><td>0</td><td>0</td><td>5</td><td>50</td></tr><tr><td>F(1:10)</td><td>0</td><td>0</td><td>10</td><td>50</td></tr></table>

TABLE VI

NETWORK PARAMETERS OF CLASSIFIER

<table><tr><td>Layer name</td><td>CNN</td></tr><tr><td colspan="2">Classifier</td></tr><tr><td>Conv1d</td><td>filters \( = {16} \) , kernel_size \( = {125} \) , strides \( = 4 \)</td></tr><tr><td>BN</td><td>default</td></tr><tr><td>ReLU</td><td>default</td></tr><tr><td>MaxPooling1D</td><td>pool_size \( = 2 \) , strides \( = 4 \)</td></tr><tr><td>Conv1d</td><td>filters \( = {32} \) , kernel_size \( = {25} \) , strides \( = 4 \)</td></tr><tr><td>BN</td><td>default</td></tr><tr><td>ReLU</td><td>default</td></tr><tr><td>MaxPooling1D</td><td>pool_size \( = 2 \) , strides \( = 4 \)</td></tr><tr><td>Flatten</td><td/></tr><tr><td>Dense</td><td>output_features \( = {256} \)</td></tr><tr><td>Dense</td><td>output_features \( = 9 \) , activation \( = \) ’softmax’</td></tr></table>

![bo_d2311hjef24c73anl120_6_151_1272_720_387_0.jpg](images/bo_d2311hjef24c73anl120_6_151_1272_720_387_0.jpg)

Fig. 5. Loss curves of the model. (a) Five samples. (b) Ten samples.

TABLE VII

ACCURACY OF FIVE GROUPS FOR CWRU BEARING DATASET

<table><tr><td>Group</td><td>A</td><td>B</td><td>C</td><td>D</td><td>E</td><td>F</td></tr><tr><td>Accuracy</td><td>56.13%</td><td>67.78%</td><td>73.51%</td><td>80.62%</td><td>17.35%</td><td>21.45%</td></tr></table>

Next, the proposed method in terms of classification accuracy in groups \( \mathrm{A},\mathrm{B},\mathrm{C},\mathrm{D},\mathrm{E} \) , and \( \mathrm{F} \) is assessed. Each group of experiments is conducted ten times separately, and the accuracy is averaged. Firstly, the experiments are carried out for Groups E and F. When the class imbalance rates are 1:20 and 1:10, the accuracy rates are of 17.45% and 21.35%, respectively. The next experiments are conducted for Groups A and B, with the obtained accuracy rates of \( {56.13}\% \) and 67.78%. It can be seen that the class imbalance problem caused by the lack of fault samples has been greatly alleviated by our proposed data augmentation method. In addition, the distribution that the model can learn is closer to the real distribution of the whole sample and the quality of the generated samples is significantly better when more samples can be learned, so the accuracy rate is also obviously improved. Finally, for \( \mathrm{C} \) and D experiments, the accuracy of the classifier is \( {73.51}\% \) and \( {80.62}\% \) , respectively, when a small number of real samples are added to the training set. It can be seen that the accuracy also improves with the addition of real samples. The diagnosis results are summarized in Table VII. Overall, the proposed method alleviates the problem of low fault diagnosis accuracy in the case of extreme lack of samples.

![bo_d2311hjef24c73anl120_6_915_150_742_765_0.jpg](images/bo_d2311hjef24c73anl120_6_915_150_742_765_0.jpg)

Fig. 6. Comparison of true samples and generated samples in the time domain.

![bo_d2311hjef24c73anl120_6_922_1020_728_981_0.jpg](images/bo_d2311hjef24c73anl120_6_922_1020_728_981_0.jpg)

Fig. 7. Comparison of true samples and generated samples in the frequency domain. (a) B007. (b) IR007. (c) OR007.

![bo_d2311hjef24c73anl120_7_136_149_742_645_0.jpg](images/bo_d2311hjef24c73anl120_7_136_149_742_645_0.jpg)

Fig. 8. 2-D visualization of real and generated samples for CWRU bearing dataset.

To verify the superiority of the proposed method, several variants of GAN are selected for comparative experiments, including DCGAN [35], LSGAN [36], and FAWGAN-GN [37]. The configuration of Groups C and D is adopted in the experiment. To allow a fair comparison, the classifiers adopt the same structure to ensure that all conditions are the same except for the differences of the generative models themselves. Additional experiments using only a single CNN are added as a reference, and the structure of CNN is the same as that in Table VI. As shown in Fig. 9, under the experimental conditions of Group C, the lowest accuracy of \( {17.45}\% \) is obtained only using CNN, which indicates that the traditional neural network cannot obtain great diagnosis accuracy when the training set has few samples. The accuracy of DCGAN, LSGAN, and FAWGAN-GN are all around 35%, which certainly represents an improvement of the diagnosis task, while the proposed method in this article achieves an accuracy of \( {73.51}\% \) . This is a significant improvement, proving that the proposed method has a better performance than the currently popular methods. With the Group D, the accuracy of each model has been further facilitated due to the larger set of training samples. The accuracy of CNN remains at the lowest value of 21.35%. The accuracy of DCGAN, LSGAN, and FAWGAN-GN is improved to about \( {53}\% \) , while the accuracy of the proposed method is as high as \( {80.62}\% \) .

![bo_d2311hjef24c73anl120_7_922_150_723_498_0.jpg](images/bo_d2311hjef24c73anl120_7_922_150_723_498_0.jpg)

Fig. 9. Diagnosis results of different methods for CWRU bearing dataset.

TABLE VIII

DETAIL OF THE WING BEAM DATASET

<table><tr><td>Label</td><td>Fault diameter</td><td>Data length</td></tr><tr><td>0</td><td>4mm</td><td>1000</td></tr><tr><td>1</td><td>8mm</td><td>1000</td></tr><tr><td>2</td><td>12mm</td><td>1000</td></tr><tr><td>3</td><td>16mm</td><td>1000</td></tr><tr><td>4</td><td>No damage</td><td>1000</td></tr></table>

![bo_d2311hjef24c73anl120_7_926_1104_719_458_0.jpg](images/bo_d2311hjef24c73anl120_7_926_1104_719_458_0.jpg)

Fig. 10. Sensor layout and simulated damage location setting [32].

## B. Guided Wave Data Generation Experiment

1) Data Description: To verify the generalization performance of the model, the raw guided wave data of the real wing beam are chosen as the validation dataset. The allocation of the beam and piezoelectric sensor is shown in Fig. 10. In the experiment, three piezoelectric sheet sensors are set up. The piezoelectric sheet 1 is used as the excitation, the piezoelectric sheet 3 is used as the sensing, and the distance between the two sensors is \( {120}\mathrm{\;{mm}} \) . The simulated damage is located on position 3 . We use the piezoelectric sheet 1 to send an excitation signal and then obtain the signal collected by the piezoelectric sheet 3 . The purpose of this experimental platform is to study the difference signal changes caused by simulated damage of different sizes. Specific details of the experiment are provided in [32].

![bo_d2311hjef24c73anl120_8_156_154_715_374_0.jpg](images/bo_d2311hjef24c73anl120_8_156_154_715_374_0.jpg)

Fig. 11. Comparison of true samples and generated samples in the time domain for wing beam Lamb wave.

![bo_d2311hjef24c73anl120_8_149_638_725_282_0.jpg](images/bo_d2311hjef24c73anl120_8_149_638_725_282_0.jpg)

Fig. 12. Comparison of true samples and generated samples in the frequency domain for wing beam Lamb wave.

To obtain the signal changes caused by small simulated damage of different sizes, the experimental procedure is designed as follows.

1) A 4 mm simulated damage is placed at position 3 and 60 sets of signals are acquired.

2) The previous simulated damage is removed, an \( 8\mathrm{\;{mm}} \) simulated damage is placed at position 3 , and 60 sets of signals are acquired.

3) The previous simulated damage is removed, \( {12}\mathrm{\;{mm}} \) simulated damage is placed at position 3 , and 60 sets of signals are acquired.

4) The previous simulated damage is removed, \( {16}\mathrm{\;{mm}} \) simulated damage is placed at position 3 , and 60 sets of signals are acquired.

The whole data is a matrix of \( {60} \times  4 \times  {10000} \) samples, where 10000 indicates the sampling length of signals, 60 indicates 60 sets of signals for each type of fault, and 4 represents the data collection under four different impairments. Subsequently, the signal data are subsampled, one for every ten points, to get a matrix of \( {60} \times  4 \times  {1000} \) .

2) Experimental Verification: As with the previous CWRU bearing diagnostics, class imbalance scenarios are designed. Four different groups of experiments are set up to validate the model with respect to the imbalance rate from 1:20 to 1:5, as shown in Table IX. There are few samples of each type of fault, ranging from 5 to 20, and sufficient samples of 100 for normal condition. These four sets of experiments are performed regarding the diagnostic steps described in Section III.

![bo_d2311hjef24c73anl120_8_950_158_689_487_0.jpg](images/bo_d2311hjef24c73anl120_8_950_158_689_487_0.jpg)

Fig. 13. Comparison of diagnosis accuracy using the proposed method and related methods tested on the wing beam dataset.

TABLE IX

DETAILED SAMPLE CONFIGURATION IN GUIDED WAVE GENERATION EXPERIMENT

<table><tr><td rowspan="2">(per class)</td><td colspan="2">GAN</td><td colspan="2">Classifier</td></tr><tr><td>Training set</td><td>Generated data</td><td>Training set</td><td>Test set</td></tr><tr><td>A(1:20)</td><td>5</td><td>100</td><td>95</td><td>50</td></tr><tr><td>B(1:10)</td><td>10</td><td>100</td><td>100</td><td>50</td></tr><tr><td>C(3:20)</td><td>15</td><td>100</td><td>105</td><td>50</td></tr><tr><td>D(1:5)</td><td>20</td><td>100</td><td>110</td><td>50</td></tr></table>

TABLE X

COMPARISON OF DIAGNOSIS ACCURACY USING THE PROPOSED METHOD AND RELATED METHODS TESTED ON THE WING BEAM DATASET

<table><tr><td rowspan="2">Accuracy</td><td colspan="4">NO. of training samples per class</td></tr><tr><td>5 samples</td><td>10 samples</td><td>15 samples</td><td>20 samples</td></tr><tr><td>Proposed method</td><td>0.7621</td><td>0.8944</td><td>0.9771</td><td>0.9921</td></tr><tr><td>DCGAN</td><td>0.6968</td><td>0.8120</td><td>0.9781</td><td>0.9901</td></tr><tr><td>LSGAN</td><td>0.6568</td><td>0.8176</td><td>0.9737</td><td>0.9812</td></tr><tr><td>WGAN</td><td>0.6293</td><td>0.7932</td><td>0.9641</td><td>0.9883</td></tr></table>

Similarly, different state-of-the-art GAN-based methods are selected for comparison, such as DCGAN [35], LSGAN [36], and WGAN [38]. They are all trained in the same configuration. Each group of experiments is done ten times and the average accuracy is taken. The diagnosis results are summarized in Fig. 13 and Table X. When the imbalance rate is 1:20 and 1:10, the method proposed in this article significantly outperforms several other methods, and the diagnosis accuracy reaches \( {76.21}\% \) and \( {89.4}\% \) , respectively. When the imbalance rate is \( 3 : {20} \) and \( 1 : 5 \) , our proposed method also performs better. In addition, this article has also considered other quantitative indicators to measure the quality of the generated samples. Under the condition of ten training samples, the MMD and FID are calculated between the generated and the real samples as shown in Table XI. Comparison of true samples and generated samples in the time and frequency domain for wing beam Lamb wave are shown in Figs. 11 and 12. The aforementioned experiments prove that this proposed method has a better performance in terms of generating details of signal.

TABLE XI

QUALITY ASSESSMENT OF WING BEAM GENERATED SAMPLES BASED ON FID AND MMD

<table><tr><td rowspan="2">Method</td><td colspan="4">FID</td><td colspan="4">MMD</td></tr><tr><td>5 samples</td><td>10 samples</td><td>15 samples</td><td>20 samples</td><td>5 samples</td><td>10 samples</td><td>15 samples</td><td>20 samples</td></tr><tr><td>Proposed method</td><td>0.2452</td><td>0.0924</td><td>0.0511</td><td>0.0277</td><td>0.00032</td><td>0.00035</td><td>0.00035</td><td>0.00033</td></tr><tr><td>DCGAN</td><td>0.3781</td><td>0.1235</td><td>0.1051</td><td>0.1241</td><td>0.00175</td><td>0.00094</td><td>0.00095</td><td>0.00094</td></tr><tr><td>LSGAN</td><td>0.3465</td><td>0.1665</td><td>0.1289</td><td>0.0984</td><td>0.00283</td><td>0.00286</td><td>0.00217</td><td>0.00201</td></tr><tr><td>WGAN</td><td>0.2784</td><td>0.1374</td><td>0.1242</td><td>0.0756</td><td>0.00056</td><td>0.00031</td><td>0.00031</td><td>0.00034</td></tr></table>

![bo_d2311hjef24c73anl120_9_142_540_738_397_0.jpg](images/bo_d2311hjef24c73anl120_9_142_540_738_397_0.jpg)

Fig. 14. Loss curves of model. (a) Model without IN layers. (b) Model without SA layers.

![bo_d2311hjef24c73anl120_9_144_1037_736_400_0.jpg](images/bo_d2311hjef24c73anl120_9_144_1037_736_400_0.jpg)

Fig. 15. Comparison of the distribution of samples. (a) Model without IN layers. (b) Model without SA layers.

## C. Ablation Experiment

In this part, the ablation experiments are designed to verify the role of each module in the model. This study removes SA layers, IN layers, and filtering mechanism one by one in the proposed model, and analyze the variation of the experimental results. The experiments are based on CWRU data.

The IN layers is removed first. In this case, the model has problem to converge, and generated samples are too similar to the real ones, as shown in Figs. 14(a) and 15(a), suggesting that IN layers are crucial for the stability of the model training and the diversity of the generated samples.

The second experiment removes the SA layers. The training process of the model remains to be basically stable, and the loss values of both the generator and the discriminator converge to about -5 , despite a larger vibration. It is more difficult for the model to fit the distribution of the real samples. The results demonstrate that SA layers improve the learning ability of GAN.

![bo_d2311hjef24c73anl120_9_912_542_741_401_0.jpg](images/bo_d2311hjef24c73anl120_9_912_542_741_401_0.jpg)

Fig. 16. Visualization of the process before and after the sample enters the IF module. (a) Sample before entering the module. (b) Sample after entering the module.

![bo_d2311hjef24c73anl120_9_926_1066_713_491_0.jpg](images/bo_d2311hjef24c73anl120_9_926_1066_713_491_0.jpg)

Fig. 17. Comparison of model accuracy after removing each module.

Then, this article compares the differences before and after the removal of the filtering mechanism. A total of 200 generated samples are put into filtering module, then ten percent of them are deleted, and finally 180 samples are obtained. The prefiltered, postfiltered, and real samples are assumed as sample sets, that are \( {X}_{\text{pre }},{X}_{\text{post }} \) , and \( {X}_{r} \) . The distributions of \( {X}_{\text{pre }},{X}_{\text{post }} \) , and \( {X}_{r} \) in 2-D space are depicted in Fig. 16. The MMD between \( {X}_{\text{pre }} \) and \( {X}_{r} \) is greater than that between \( {X}_{\text{post }} \) and \( {X}_{r} \) , which quantifies the change of the distribution distance.

Fig. 17 illustrates the changes of the classification accuracy of the model for each ablation experiment. It can be seen that the diagnosis accuracy of the model decreases the most when the IN layers are removed. The next is the IF module, which is more effective when the number of learnable samples is small, while its effect gradually decreases when the number of learnable samples increases. This is due to the fact that the model training effect is poor when the number of learnable samples is small and the generated samples have more outliers. When the number of learnable samples increases, the generated samples have a closer distribution to the real ones, thus there is no need to filter generated samples. The SA layer produces the least effect.

Extensive results present that each module has its own role, which improves the effect of GAN-based fault data augmentation to some extent.

## V. CONCLUSION AND FUTURE WORK

Data augmentation is a very important method with applications to few-shot fault diagnosis tasks. In this article, we formally analyze the data augmentation problem and propose a method called improved GAN with a filtering mechanism. The framework is flexible to support many different strategies for data augmentation. In particular, it can generate long series of time signals. Experiment results on two fault diagnosis tasks show that while other state-of-the-art GANs can be applied to data augmentation with less complexity, they do not perform as well as our proposed method, which accurately captures the distribution of the real samples. The achieved results also highlight that a more stable training process can be obtained by adding IN layers. The method opens up many interesting future research directions, especially those related to how to accelerate model convergence and infer the distribution of the whole dataset from a few samples. REFERENCES

[1] N. Lu, B. Jiang, X. Meng, and H. Zhao, "Diagnosis, diagnosticability analysis, and test point design for multiple faults based on multisignal modeling and blind source separation," IEEE Trans. Syst. Man, Cybern. Syst., vol. 50, no. 1, pp. 137-148, Jan. 2020.

[2] Y. Lei, F. Jia, J. Lin, S. Xing, and S. X. Ding, "An intelligent fault diagnosis method using unsupervised feature learning towards mechanical big data," IEEE Trans. Ind. Electron., vol. 63, no. 5, pp. 3137-3147, May 2016.

[3] C. Wang, N. Lu, Y. Cheng, and B. Jiang, "A data-driven aero-engine degradation prognostic strategy," IEEE Trans. Cybern., vol. 51, no. 3, pp. 1531-1541, Mar. 2021.

[4] S. Liu, J. Chen, C. Qu, R. Hou, H. Lv, and T. Pan, "LOSGAN: Latent optimized stable GAN for intelligent fault diagnosis with limited data in rotating machinery," Meas. Sci. Technol., vol. 32, no. 4, Apr. 2021, Art. no. 045101.

[5] K. Zhou, N. Lu, and B. Jiang, "Information fusion-based fault diagnosis method using synthetic indicator," IEEE Sensors J., vol. 23, no. 5, pp. 5124-5133, Mar. 2023.

[6] S. Dixit and N. K. Verma, "Intelligent condition-based monitoring of rotary machines with few samples," IEEE Sensors J., vol. 20, no. 23, pp. 14337-14346, Dec. 2020.

[7] Y. Hu, R. Liu, X. Li, D. Chen, and Q. Hu, "Task-sequencing meta learning for intelligent few-shot fault diagnosis with limited data," IEEE Trans. Ind. Informat., vol. 18, no. 6, pp. 3894-3904, Jun. 2022.

[8] W. Luo et al., "Fault diagnosis method based on two-stage GAN for data imbalance," IEEE Sensors J., vol. 22, no. 22, pp. 21961-21973, Nov. 2022.

[9] L. Wen, L. Gao, and X. Li, "A new deep transfer learning based on sparse auto-encoder for fault diagnosis," IEEE Trans. Syst. Man, Cybern. Syst., vol. 49, no. 1, pp. 136-144, Jan. 2019.

[10] T. Pan, J. Chen, T. Zhang, S. Liu, S. He, and H. Lv, "Generative adversarial network in mechanical fault diagnosis under small sample: A systematic review on applications and future perspectives," ISA Trans., vol. 128, pp. 1-10, Sep. 2022.

[11] S. Shao, P. Wang, and R. Yan, "Generative adversarial networks for data augmentation in machine fault diagnosis," Comput. Ind., vol. 106, pp. 85-93, Apr. 2019.

[12] Z. Wang, J. Wang, and Y. Wang, "An intelligent diagnosis scheme based on generative adversarial learning deep neural networks and its application to planetary gearbox fault pattern recognition," Neurocomputing, vol. 310, pp. 213-222, Oct. 2018.

[13] J. Yang, J. Liu, J. Xie, C. Wang, and T. Ding, "Conditional GAN and 2-D CNN for bearing fault diagnosis with small samples," IEEE Trans. Instrum. Meas., vol. 70, pp. 1-12, 2021.

[14] J. Liu, F. Qu, X. Hong, and H. Zhang, "A small-sample wind turbine fault detection method with synthetic fault data using generative adversarial nets," IEEE Trans. Ind. Informat., vol. 15, no. 7, pp. 3877-3888, Jul. 2019.

[15] J. Yu, Y. Song, D. Tang, D. Han, and J. Dai, "Telemetry data-based spacecraft anomaly detection with spatial-temporal generative adversarial networks," IEEE Trans. Instrum. Meas., vol. 70, pp. 1-9, 2021.

[16] Y. Gao, X. Liu, and J. Xiang, "Fault detection in gears using fault samples enlarged by a combination of numerical simulation and a generative adversarial network," IEEE/ASME Trans. Mechatronics, vol. 27, no. 5, pp. 3798-3805, Oct. 2022.

[17] Y. Lou, A. Kumar, and J. Xiang, "Machinery fault diagnosis based on domain adaptation to bridge the gap between simulation and measured signals," IEEE Trans. Instrum. Meas., vol. 71, pp. 1-9, 2022.

[18] A. Creswell, T. White, V. Dumoulin, K. Arulkumaran, B. Sengupta, and A. A. Bharath, "Generative adversarial networks: An overview," IEEE Signal Process. Mag., vol. 35, no. 1, pp. 53-65, Jan. 2018.

[19] W. Wan, S. He, J. Chen, A. Li, and Y. Feng, "QSCGAN: An unsupervised quick self-attention convolutional GAN for LRE bearing fault diagnosis under limited label-lacked data," IEEE Trans. Instrum. Meas., vol. 70, pp. 1-16, 2021.

[20] C. Esteban, S. L. Hyland, and G. Rätsch, "Real-valued (medical) time series generation with recurrent conditional GANs," 2017, arXiv:1706.02633.

[21] Y. Wu, Y. Burda, R. Salakhutdinov, and R. Grosse, "On the quantitative analysis of decoder-based generative models," 2016, arXiv:1611.04273.

[22] H. Zhang, I. Goodfellow, D. Metaxas, and A. Odena, "Self-attention generative adversarial networks," in Proc. Int. Conf. Mach. Learn., 2019, pp. 7354-7363.

[23] R. Wang, Z. Chen, S. Zhang, and W. Li, "Dual-attention generative adversarial networks for fault diagnosis under the class-imbalanced conditions," IEEE Sensors J., vol. 22, no. 2, pp. 1474-1485, Jan. 2022.

[24] D. Ulyanov, A. Vedaldi, and V. Lempitsky, "Instance normalization: The missing ingredient for fast stylization," 2016, arXiv:1607.08022.

[25] I. Goodfellow et al., "Generative adversarial networks," Commun. ACM, vol. 63, no. 11, pp. 139-144, 2020.

[26] F. T. Liu, K. M. Ting, and Z.-H. Zhou, "Isolation forest," in Proc. 8th IEEE Int. Conf. Data Mining, Dec. 2008, pp. 413-422.

[27] Y. Shi, D. Wang, Z. Ni, H. Liu, B. Liu, and M. Deng, "A sequential pattern mining based approach to adaptively detect anomalous paths in floating vehicle trajectories," IEEE Trans. Intell. Transp. Syst., vol. 23, no. 10, pp. 18186-18199, Oct. 2022.

[28] Q. Guo, Y. Li, Y. Song, D. Wang, and W. Chen, "Intelligent fault diagnosis method based on full 1-D convolutional generative adversarial network," IEEE Trans. Ind. Informat., vol. 16, no. 3, pp. 2044-2053, Mar. 2020.

[29] J.-B. Cordonnier, A. Loukas, and M. Jaggi, "On the relationship between self-attention and convolutional layers," 2019, arXiv:1911.03584.

[30] M. Arjovsky, S. Chintala, and L. Bottou, "Wasserstein generative adversarial networks," in Proc. Int. Conf. Mach. Learn., 2017, pp. 214-223.

[31] W. A. Smith and R. B. Randall, "Rolling element bearing diagnostics using the case western reserve university data: A benchmark study," Mech. Syst. Signal Process., vols. 64-65, pp. 100-131, Dec. 2015.

[32] F. Guo, S. Yuan, and Q. Bao, "Research on corrosion damage monitoring of aircraft structures based on guided waves," Aeronaut. Manuf. Technol., vol. 61, no. 7, pp. 70-76, 2018.

[33] Y. Gao, X. Liu, and J. Xiang, "FEM simulation-based generative adversarial networks to detect bearing faults," IEEE Trans. Ind. Informat., vol. 16, no. 7, pp. 4961-4971, Jul. 2020.

[34] X. Liu, H. Huang, and J. Xiang, "A personalized diagnosis method to detect faults in gears using numerical simulation and extreme learning machine," Knowl.-Based Syst., vol. 195, May 2020, Art. no. 105653.

[35] R. Wang, S. Zhang, Z. Chen, and W. Li, "Enhanced generative adversarial network for extremely imbalanced fault diagnosis of rotating machine," Measurement, vol. 180, Aug. 2021, Art. no. 109467.
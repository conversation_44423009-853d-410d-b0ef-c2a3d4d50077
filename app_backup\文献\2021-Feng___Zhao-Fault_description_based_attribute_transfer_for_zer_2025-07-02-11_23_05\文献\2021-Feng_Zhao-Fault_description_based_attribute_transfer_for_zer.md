# Fault Description Based Attribute Transfer for Zero-Sample Industrial Fault Diagnosis

<PERSON><PERSON>, and <PERSON><PERSON>, Senior Member, IEEE

Abstract-In this paper, a challenging fault diagnosis task is studied, in which no samples of the target faults are available for the model training. This scenario has hardly been studied in industrial research. But it is a common problem that massive fault samples are not available for the target faults, which limits the successes of conventional data-driven approaches in practical application. Here, we introduce the idea of zero-shot learning into the industry field, and tackle the zero-sample fault diagnosis task by proposing the fault description based attribute transfer method. Specifically, the method learns to determine the fault categories using the human-defined fault descriptions instead of the collected fault samples. The defined description consists of arbitrary attributes of the faults, including the fault positions, the consequences of the fault, and even the cause of the fault, etc. For the attribute knowledge of target faults, they can be pre-learned and transferred from some readily available faults occurred in the same process. Afterwards, the target faults can be diagnosed based on the defined fault descriptions without the need for any additional data based training. Besides, the supervised principle component analysis is adopted in our method to extract the attribute related features to offer an effective attribute learning. We analyze and interpret the feasibility of the fault description based method theoretically. Also, the zero-sample fault diagnosis experiments are designed and conducted on the benchmark Tennessee-Eastman process and the real thermal power plant process to validate the effectiveness. The results show that it is indeed possible to diagnose target faults without their samples.

Index Terms-Fault diagnosis, fault description, machine learning, zero-shot learning, industry process, transfer learning.

## I. INTRODUCTION

FAULT diagnosis and classification are essential tasks for large-scale industrial maintenance [1], [2], and massive data-driven approaches have been proposed to improve the diagnosis ability for various faults [3], [4]. Traditionally, classification based fault diagnosis consists of three stages: data acquisition, feature extraction, and fault classification [5]. For the first data acquisition step, numerous sensors are installed to collect the fault signals during the daily operation. The feature extraction is usually done by matrix decomposition methods [6], time-frequency analysis [7], and the recent convolutional neural networks [8], [9] to realize the dimensionality reduction and de-nosing. In the last classification step, the extracted features are used as the input to get machine learning models [10] for fault diagnosis and classification. However, the three-step process is more like the rule in laboratory rather than the expectation in practice, since it is a common industrial scenario where no or few data are available for the target faults which appear during the daily process operation. Considering many faults can be destructive and cause huge losses, few factories would be allowed to run to failure and collect samples to train a fault diagnosis system [11]. Machine usually experiences a gradual degradation process from health to failure, which also reveals that it is time-consuming and expensive to obtain sufficient fault samples for the data-driven methods [12].

To overcome the trouble in collecting samples for certain faults, an acceptable way is to apply the knowledge learned from some easily obtained or historical faults (training faults) to those faults which are difficult or expensive to collect (target faults) [13], [14]. Hence, Lu et al. [15] integrated the deep neural network with the maximum mean discrepancy item to present a deep transfer learning for fault diagnosis, in which none of samples of the target faults were necessary for highly accurate performance. The sparse autoencoder was also utilized by Long et al. [16] for the deep domain adaptation of training and target faults. However, for deep transfer learning [17], the same kinds of faults have already been prepared well in the training domain. Although there is no demand for the fault samples in target domain, the deep transfer learning actually tackles the domain shift (between the training and target domain) problem instead of the concerned zero-sample problem. Besides, the fault tree analysis [18], [19] was also a classic method for industry scenario, which diagnosed the system failure based on some basic faults. It was successfully applied to understand the causes of system failure, and found the best way to reduce the risk, or confirmed the occurrence rate of a fault [20], [21]. However, when the fault tree analysis was applied to the quantitative analysis, the frequencies of basic faults were usually required by the expert system. It means that the records (samples) for some target faults had been prepared, which did not meet the requirement of zero-sample setting.

Recently, the zero-shot learning proposed by Lampert at al. [22] presents promising performance for the zero-sample animal recognition task, in which no training images are available for the animals appeared at test stage. Instead of directly training the classification algorithms, Lampert utilized the nonlinear support vector machine to learn the attributes of animals, i.e., the "colors" and "shapes", and exploited the well-trained machines to predict the attributes of test images of other animals. Based on the transfer of learned attributes, the new animals could be detected. The method was termed as direct attribute prediction (DAP). Lampert et al. [23] also learned the animal attributes from the labels to present the indirect attribute prediction (IAP) method. Besides, the compatibility functions and square loss were utilized in zero-shot learning to learn the mapping between the images and attributes. Akata et al. [24] conducted the zero-shot classification of an image proceeds by finding the label yielding the highest joint compatibility score. And Romera-Paredes, et al. [25] proposed an embarrassingly simple approach for zero-shot learning, which modeled the relationships between features, attributes, and classes as a two linear layers network based on the square loss. The idea of zero-shot learning that learns an object from its attributes [26] provides a possible solution to the zero-sample fault diagnosis problem. However, when it turns to the fault diagnosis task, there are no images available to obtain the "colors" or "shapes" attributes for various faults. The visual attributes are inapplicable for the industrial sensor signals, and more effective auxiliary information is required for the zero-sample fault diagnosis task.

---

Manuscript received Aug. 6, 2019; revised Dec. 3, 2019; accepted Apr. 12, 2020. This work is supported by NSFC-Zhejiang Joint Fund for the Integration of Industrialization and Informatization (No. U1709211) and Zhejiang Key Research and Development Project (2019C03100 and 2019C01048). (The corresponding author is Chunhui Zhao).

The authors are with the state Key Laboratory of Industrial Control Technology, Institute of Industrial Process Control, College of Control Science and Engineering, Zhejiang University, Hangzhou 310027, China. (E-mail: <EMAIL>, <EMAIL>).

---

Intuitively, when we learn a new kind of fault in practice, the characteristics and semantic description of the fault will be noticed first rather than the samples. For examples, from the description "a device that is connected with a tube, a coupling and a valve to convey gas, liquid, or fluid with solid particles", we can learn the object "pipe" without seeing it at all. Also, humans know there is a "pipe blockage" fault, when they are told that "the fluid flow in pipe is stopped or abnormally slow". Hence, it is feasible to diagnose and recognize various faults based on the well-concluded descriptions instead of the samples. The human-defined fault descriptions are convincing and intuitive evidence for the identification of fault categories. Each description of industrial faults usually consists of several attributes, including the effects of the fault, the position of the fault, and the cause of the fault, etc. Obviously, there are numerous possible attributes, and the fact that the human-defined attributes which transcend class boundaries can be exploited for an efficient cross-class learning [22], [23]. For examples, both of the fault "abnormal vibration of pulverizer bearing" and the fault "temperature rise of pulverizer coil" occur at the "pulverizer". To learn and recognize the "pulverizer" attribute, we can therefore make use of existing training data by merging samples of several other fault categories which also occur at the "pulverizer", and apply the knowledge to the target faults. The fact that the human-defined attributes can be shared by various faults is this possibility to obtain attribute knowledge from the descriptions of different fault categories.

Besides, the high-dimension and low-density information is another essential characteristic of the industrial sensor data [27], [28]. Statistical feature extraction algorithms [29], [30] are usually required for an effective data mining and property revealing. The most popular and classic method is the principle component analysis [31], [32], which conducts the feature extraction in an unsupervised manner. Numerous case studies and former works have validated its effectiveness. However, for the fault diagnosis task [33], which is generally defined in the supervised learning paradigm, the supervised principle component analysis [34] can be more helpful for the extraction of target related features and presenting an effective diagnosis.

In this paper, the fault diagnosis scenario in which no training samples are provided for the target fault categories is studied by the fault description based attribute transfer method. Instead of the conventional diagnosis paradigm, the fault description which consists of arbitrary attributes is provided for each fault as the auxiliary information in our method. The fault description layer is embedded between the fault sample layer and the fault category layer. Based on the fine-grained and class-shared attributes in the fault description layer, a cascade diagnosis system can be built to transfer the attribute knowledge from training faults to target faults to conduct the zero-sample fault diagnosis. We also adopt the supervised principle component analysis as the feature extractor in the fault description based method to provide the attribute related features for a more efficient learning process.

The main contributions of this paper are summarized as below:

1) It is the first time that the zero-sample fault diagnosis task is concluded and solved, which attempts to diagnose target faults without their samples

2) The fault description based method which sets the fault descriptions as the auxiliary knowledge source is proposed to achieve the attribute transfer from training faults to target faults for the zero-sample diagnosis.

The remainder of this paper is organized as follows. In Section II, we formulize the concerned zero-sample diagnosis problem. In Section III, the fault description based attribute transfer method and feasibility analysis are presented. In Section IV, the zero-sample fault diagnosis task is conducted on the benchmark Tennessee-Eastman process and the real thermal power plant process. Last, we give the conclusions and future works.

![bo_d1iabrk601uc738hi9sg_1_975_1463_614_395_0.jpg](images/bo_d1iabrk601uc738hi9sg_1_975_1463_614_395_0.jpg)

Fig. 1. The dilemma of the ordinary machine learning models for the zero-sample fault diagnosis task, in which none of the parameter vectors \( \alpha \) can be obtained for target faults during the training stage.

## II. Problem Formulation

### A.The Configuration of Descriptions in Vector Space

As mentioned above, we conclude the fault description for each kind of fault to offer the fine-grained class-level information. The description consists of arbitrary attributes, including the effects of the fault, the specific fault position, and the cause of the fault, etc. Each attribute is a dimension in vector space, and the description of a fault is denoted as \( {\mathbf{a}}^{\prime } \in  {\mathbb{R}}^{{C}^{\prime }} \) , where \( {C}^{\prime } \) is the number of attributes. For \( L \) kinds of faults, the description matrix can be denoted as \( {\mathbf{A}}^{\prime } \in  {\mathbb{R}}^{L \times  {C}^{\prime }} \) . The one-hot coding technique is adopted here to make \( {\mathbf{A}}^{\prime } \) a sparse matrix \( \mathbf{A} \in  {\mathbb{R}}^{L \times  C} = \) one-hot \( \left( {\mathbf{A}}^{\prime }\right) \) , where \( C \) is the dimension of one-hot coding. All the elements in \( \mathbf{A} \) are either 1 or 0 , which denotes the attribute exists or not in the description of a certain fault category.

### B.The Formulation of Zero-Sample Fault Diagnosis

We aim to diagnose and recognize \( p \) kinds of faults. The set of target faults is denoted as \( T = \left\{  {{t}_{1},\ldots ,{t}_{p}}\right\} \) . However, there are no training samples available for \( T \) . Some accessible fault categories may be utilized here, which are denoted as \( S = \left\{  {{s}_{1},\ldots ,{s}_{q}}\right\} \) , where \( q \) is the number of accessible faults. But \( T \) and \( S \) are disjoint from each other, i.e., \( T \cap  S = \varnothing \) . The samples of \( S \) are denoted as \( \mathfrak{I} = \left\{  {{\mathbf{X}}_{S} \in  {\mathbb{R}}^{{N}_{S} \times  D},\mathbf{Y} \in  {\mathbb{R}}^{{N}_{S}}}\right\} \) , where \( {N}_{S} \) is the number of samples, and \( D \) is the feature dimension. The zero-sample fault diagnosis learns a mapping \( f \) from \( S \) to \( T \) , which can be formulated as below

\[
\text{Min CLoss}\left( {{\mathbf{Y}}_{T},{\widehat{\mathbf{Y}}}_{T}}\right) \text{, and}{\widehat{\mathbf{Y}}}_{T} = f\left( {{\mathbf{X}}_{S},{\mathbf{Y}}_{S} \mid  {\mathbf{X}}_{T}}\right) \text{,} \tag{1}
\]

where \( {\mathbf{X}}_{T} \) and \( {\mathbf{Y}}_{T} \) denotes the samples and labels of the target faults at test stage, and CLoss denotes arbitrary classification loss.

After introducing the fault descriptions for both of \( T \) and \( S \) , the objective function in (1) can be rewritten as

\[
\text{Min CLoss}\left( {{\mathbf{Y}}_{T},{\widehat{\mathbf{Y}}}_{T}}\right) \text{, and}{\widehat{\mathbf{Y}}}_{T} = f\left( {{\mathbf{X}}_{S},{\mathbf{Y}}_{S},\mathbf{A} \mid  {\mathbf{X}}_{T}}\right) \text{,} \tag{2}
\]

where \( \mathbf{A} = \left\lbrack  {{\mathbf{A}}_{S},{\mathbf{A}}_{T}}\right\rbrack   \in  {\mathbb{R}}^{L \times  C} \) is the attribute description matrix, and \( L = p + q \) . It is worth mentioning that both of the attribute description matrix \( {\mathbf{A}}_{S} \) of \( S \) and \( {\mathbf{A}}_{T} \) of \( T \) are available for the model training, since the attribute descriptions are class-level rather than sample-level and common knowledge rather than specialized expert knowledge which can readily be obtained. Compared with the conventional data-driven fault diagnosis problem, the proposed task is a more meaningful and challenging task, which aims to overcome the trouble in collecting numerous samples for the target fault categories.

## III. METHODOLOGY

As defined in Section II, our interest is to diagnose and classify the faults which have no samples during the training stage. Here, we begin with the proposed fault description based method, and then provide the feasibility analysis.

## A. Fault Description Based Attribute Transfer

For the ordinary machine learning models, such as support vector machine or decision tree, they usually learn one parameter vector (or other representation) \( {\alpha }_{i} \) for each kind of fault category based on the fault samples or extracted features. Clearly, it is infeasible for them to directly realize the learning from \( S \) to \( T \) for the zero-sample fault diagnosis task. Because the target classes \( T = \left\{  {{t}_{1},\ldots ,{t}_{p}}\right\} \) were not presented during the training stage, and there are no parameter vectors obtained for \( T \) . This dilemma is described in Fig. 1.

In other words, to identify the disjoint target faults, the learning system needs to extract target faults related information from training faults. Since no training data are available for the target faults, this coupling cannot be extracted from samples directly. Hence, the fault descriptions \( \mathbf{A} \) are utilized to offer additional auxiliary information and achieve the attribute transfer from training faults to target faults.

The basic idea of the fault description based method for zero-sample fault diagnosis task is presented in Fig. 2. The first step of the cascade model is feature extraction. We merge the training label \( {\mathbf{Y}}_{S} \) with the training fault descriptions \( {\mathbf{A}}_{S} \) to obtain the training attribute label \( {\mathbf{Z}}_{S} = {\left\lbrack  {\mathbf{z}}_{1}^{S},\ldots ,{\mathbf{z}}_{C}^{\bar{S}}\right\rbrack  }^{T} \in  {\mathbb{R}}^{N \times  C} \) ,

![bo_d1iabrk601uc738hi9sg_2_458_1454_871_631_0.jpg](images/bo_d1iabrk601uc738hi9sg_2_458_1454_871_631_0.jpg)

Fig. 2. The basic idea of the fault description based method. \( N \) is the number of samples of training faults, \( {N}^{\prime } \) is the number of samples of target faults at test stage, \( \varphi \) is the transform mapping of supervised principle component analysis, \( b \) is the feature of \( x,{\alpha }_{i}\left( {i = 1,\ldots , C}\right) \) is the attribute learners (regressors or classifiers) for each attribute, \( {a}_{i}\left( {i = 1,\ldots , C}\right) \) is the attributes in \( A \in  {\mathbb{R}}^{L \times  C} \) , and \( \beta \) is the inference rule from the attribute descriptions to target fault categories.

1551-3203 (c) 2020 IEEE. Personal use is permitted, but republication/redistribution requires IEEE permission. See http://www.ieee.org/publications_standards/publications/rights/index.html for more information. Authorized licensed use limited to: University of Exeter. Downloaded on May 03,2020 at 09:38:40 UTC from IEEE Xplore. Restrictions apply. which denotes each fault sample is described by a \( C \) -dimension attribute vector. The supervised principle component analysis is applied on the pair \( \left\{  {\mathbf{X},{\mathbf{z}}_{i}^{S}}\right\}  \left( {i = 1,\ldots , C}\right) \) to extract the attribute related features. The features of training sample \( \mathbf{x} \) is denoted as \( b \) , and the transform is denoted as \( \varphi \) . The second step is the attribute learning and transfer stage based on the extracted features. Instead of directly learning a mapping between features and labels, it trains an attribute learner \( {\alpha }_{i}\left( {i = 1,\ldots , C}\right) \) for each kind of attribute \( {a}_{i} \) under the supervised manner during the training stage. At test time, these attribute learners allow the predictions of attribute values (either 1 or 0 ) for each test sample of target fault. Note that as long as the attributes are described in the same dimensions for both of the training and target faults, none of additional training is needed for more attribute learners for target faults. Based on the predicted attribute vectors of test samples, the third step can be conducted, in which the final categories of the test samples are inferred. Because the fault descriptions of target faults \( {\mathbf{A}}_{T} \in  {\mathbb{R}}^{p \times  C} \) are known, the inference rules \( \beta \) from fault descriptions to fault categories are ready for test samples, such as the nearest neighbor searching, etc.

Here, the cascade model in Fig. 2 is also described in the probabilistic manner for an accurate presentation. The feature \( \mathbf{b} \) is extracted from the sample \( \mathbf{x} \) by the transform mapping \( \varphi \) . The second step which makes inference from features to fault descriptions can be presented as \( p\left( {\mathbf{a} \mid  \mathbf{b}}\right)  = \mathop{\prod }\limits_{{i = 1}}^{C}p\left( {{a}_{i} \mid  \mathbf{b}}\right) \) , where \( \mathbf{a} \in  {\mathbb{R}}^{C} \) is the description of a fault in \( \mathbf{A} \) . For the third step, it obtains the final category \( t \) from \( \mathbf{a} \) . Here, the attribute vector \( \mathbf{a} \) for the \( t \) -th class is denoted as \( {\mathbf{a}}^{t} = \left\lbrack  {{a}_{1}^{t},\ldots ,{a}_{C}^{t}}\right\rbrack \) . Based on the Bayes' rule, we denote the inference as

\[
p\left( {t \mid  \mathbf{a}}\right)  = \mathop{\sum }\limits_{{t = 1}}^{p}\frac{p\left( {{\mathbf{a}}^{t} \mid  t}\right) p\left( t\right) }{p\left( {\mathbf{a}}^{t}\right) }\left\lbrack  {\mathbf{a} = {\mathbf{a}}^{t}}\right\rbrack  , \tag{3}
\]

where \( \left\lbrack  {\mathbf{a} = {\mathbf{a}}^{t}}\right\rbrack   = 1 \) if \( \mathbf{a} = {\mathbf{a}}^{t} \) and otherwise it is 0 . Since the attribute matrix of target faults \( {\mathbf{A}}_{T} \) is known, we have \( p\left( {{\mathbf{a}}^{t} \mid  t}\right)  = 1 \) . In the absence of more specific knowledge, the class prior \( p\left( t\right) \) is assumed identical. For the factor \( p\left( {\mathbf{a}}^{t}\right)  = \) \( \mathop{\prod }\limits_{{i = 1}}^{C}p\left( {a}_{i}^{t}\right) \) , the empirical means \( p\left( {a}_{i}^{t}\right)  = \frac{1}{q}\mathop{\sum }\limits_{{j = 1}}^{q}{a}_{i}^{{s}_{j}} \) over the training fault categories can be assumed as attribute priors. Combining the three steps, the posterior of a test fault category can be obtained from a test sample as below

\[
p\left( {t \mid  \mathbf{x}}\right)  = p\left( {t \mid  \mathbf{a}}\right) p\left( {\mathbf{a} \mid  \mathbf{b}}\right)  = \frac{p\left( t\right) }{p\left( {\mathbf{a}}^{t}\right) }\mathop{\prod }\limits_{{i = 1}}^{C}p\left( {{a}_{i}^{t} \mid  \mathbf{b}}\right) , \tag{4}
\]

where \( \mathbf{b} = \varphi \left( \mathbf{x}\right) \) . To assign the best output class from all target fault categories \( {t}_{1},\ldots ,{t}_{p} \) to a test sample \( \mathbf{x} \) , the maximum posterior estimation can be used

\[
f\left( \mathbf{x}\right)  = \underset{j = 1,\ldots , p}{\arg \max }\mathop{\prod }\limits_{{i = 1}}^{C}\frac{p\left( {{a}_{i}^{{t}_{j}} \mid  \varphi \left( \mathbf{x}\right) }\right) }{p\left( {a}_{i}^{{t}_{j}}\right) } = \underset{j = 1,\ldots , p}{\arg \max }\frac{p\left( {{\mathbf{a}}^{{t}_{j}} \mid  \mathbf{b}}\right) }{p\left( {\mathbf{a}}^{{t}_{j}}\right) }, \tag{5}
\]

where \( {\mathbf{a}}^{{t}_{j}} \) denotes the fault description vector of the \( j \) -th target fault category and \( {\mathbf{a}}_{i}^{{t}_{j}} \) is the \( i \) -th element of \( {\mathbf{a}}^{{t}_{j}} \) . The equation (5) reveals that our description based method actually conducts the fault diagnosis between the features \( \mathbf{b} \) and the fault description \( \mathbf{a} \) . And hence, the fault features and descriptions are essential for the zero-sample task.

## B. Feasibility Analysis

Let \( {\mathbf{X}}_{S} = \left\lbrack  {{\mathbf{x}}_{1}^{S},\ldots ,{\mathbf{x}}_{N}^{S}}\right\rbrack \) and \( {\mathbf{X}}_{T} = \left\lbrack  {{\mathbf{x}}_{1}^{T},\ldots ,{\mathbf{x}}_{{N}^{\prime }}^{T}}\right\rbrack \) denote the samples of training faults and target faults. \( {\mathbf{B}}_{S} = \left\lbrack  {{\mathbf{b}}_{1}^{S},\ldots ,{\mathbf{b}}_{N}^{S}}\right\rbrack \) and \( {\mathbf{B}}_{T} = \left\lbrack  {{\mathbf{b}}_{1}^{T},\ldots ,{\mathbf{b}}_{{N}^{\prime }}^{T}}\right\rbrack \) are the features, \( {\mathbf{Y}}_{S} \in  {\mathbb{R}}^{N} \) and \( {\mathbf{Y}}_{T} \in \) \( {\mathbb{R}}^{{N}^{\prime }} \) are their labels. \( \mathbf{A} = \left\lbrack  {{\mathbf{A}}_{S},{\mathbf{A}}_{T}}\right\rbrack   \in  {\mathbb{R}}^{L \times  C} \) denotes that each fault category is described by a \( C \) -dimension attribute vector, and hence the attribute label \( {\mathbf{Z}}_{S} = \left\lbrack  {{\mathbf{z}}_{1}^{S},\ldots ,{\mathbf{z}}_{N}^{S}}\right\rbrack   \in  {\mathbb{R}}^{N \times  C} \) and \( {\mathbf{Z}}_{T} = \left\lbrack  {{\mathbf{z}}_{1}^{T},\ldots ,{\mathbf{z}}_{{N}^{\prime }}^{T}}\right\rbrack   \in  {\mathbb{R}}^{{N}^{\prime } \times  C} \) can be obtained by merging of \( \mathbf{A} \) with \( {\mathbf{Y}}_{S} \) and \( {\mathbf{Y}}_{T} \) . Without loss of generality, the attribute learners \( {g}_{S} = \left\{  {{\alpha }_{1}^{S},\ldots ,{\alpha }_{C}^{S}}\right\} \) and \( {g}_{T} = \left\{  {{\alpha }_{1}^{T},\ldots ,{\alpha }_{C}^{T}}\right\} \) for training and target faults are assumed as the linear mapping from the features \( {\mathbf{B}}_{S} \) and \( {\mathbf{B}}_{T} \) to the attribute labels \( {\mathbf{Z}}_{S} \) and \( {\mathbf{Z}}_{T} \) .

The transfer technique of the proposed fault description based method is the second step shown in Fig. 2, in which the attribute learners of training fault categories \( {g}_{S} \) are exploited as those of the target fault categories \( {g}_{T} \) . Here, we analyze the feasibility of the sharing of attribute learners, i.e., \( g = {g}_{S} = {g}_{T} \) .

Definition. For \( \forall {\mathbf{b}}_{i}^{T} \) , assume \( \exists {\mathbf{u}}_{i} \in  {\mathbb{R}}^{N},{\mathbf{b}}_{i}^{T} = {\mathbf{u}}_{i}{\mathbf{B}}_{S} \) . Similarly, for \( \forall {\mathbf{z}}_{i}^{T},\exists {\mathbf{v}}_{i} \in  {\mathbb{R}}^{N},{\mathbf{z}}_{i}^{T} = {\mathbf{v}}_{i}{\mathbf{Z}}_{S} \) . The set \( U = \left\{  {\mathbf{u}}_{i}\right\} \) and \( V = \left\{  {v}_{i}\right\} \) are the relational knowledge of \( {B}_{T} \) and \( {Z}_{T} \) on \( {\mathbf{B}}_{S} \) and \( {\mathbf{Z}}_{S} \) respectively.

Both of the features \( B \) and the attribute knowledge \( Z \) are the descriptions of faults. The relational knowledge set \( U \) actually encodes a kind of dependence of \( {\mathbf{B}}_{T} \) on \( {\mathbf{B}}_{S} \) in feature space, as well as \( V \) encodes that in the attribute label space. Based on the definition, we have the following lemma for the sharing of attribute learners.

Lemma. If \( \forall j,{\mathbf{u}}_{j} = {\mathbf{v}}_{j} \) , the linear map \( {g}_{S} : {\mathbf{b}}^{S} \rightarrow  {\mathbf{z}}^{S} \) learned from the samples of training fault categories \( \left\{  \left( {{\mathbf{b}}_{i}^{S},{\mathbf{z}}_{i}^{S}}\right) \right\} \) can be used as \( {g}_{T} : {\mathbf{b}}^{T} \rightarrow  {\mathbf{z}}^{T} \) to make predictions for the test samples of target fault categories \( \left\{  \left( {{\mathbf{b}}_{i}^{T},{\mathbf{z}}_{i}^{T}}\right) \right\} \) .

Proof. Let \( {\mathbf{b}}_{j}^{T} = \mathop{\sum }\limits_{{i = 1}}^{N}{\mathbf{u}}_{j}{\mathbf{b}}_{i}^{S} \) , and \( {g}_{S}\left( {\mathbf{b}}_{j}^{T}\right)  = {g}_{S}\left( {\mathop{\sum }\limits_{{i = 1}}^{N}{\mathbf{u}}_{j}{\mathbf{b}}_{i}^{S}}\right) \) \( = {\mathbf{u}}_{j}\mathop{\sum }\limits_{{i = 1}}^{N}{g}_{S}\left( {\mathbf{b}}_{i}^{S}\right)  = {\mathbf{v}}_{j}\mathop{\sum }\limits_{{i = 1}}^{N}{\mathbf{z}}_{i}^{S} = {\mathbf{z}}_{j}^{T} = {g}_{T}\left( {\mathbf{b}}_{j}^{T}\right) . \)

Hence, according to the lemma, when the relational knowledge \( U \) and \( V \) are equal to each other, the sharing of attribute learners is totally feasible under the linear case. Intuitively, the relational knowledge \( U \) in the feature space is potentially determined by the samples of training and target fault categories, which cannot be changed. However, the relational knowledge \( V \) in the attribute label space is decided by the defined \( \mathbf{A} \) , which is assigned to be learned from the fault descriptions and improved by trial and error.

## IV. EXPERIMENTS

In this section, two case studies are provided. One is designed using the benchmark Tennessee-Eastman process. We detail the experiment settings and model implementations on the fully studied dataset for the better understanding of the proposed zero-sample fault diagnosis task and method. The other is conducted on the real thermal power plant process, in which we show the application and the model-reality match of the fault-description based attribute transfer method.

## A. Tennessee-Eastman Process

1) Introduction for Dataset: Tennessee-Eastman process (TEP) contributed by Downs and Vogel [35], [36] is a fully studied fault diagnosis dataset in industry, and hence it is helpful to fairly present the newly proposed zero-sample diagnosis task and the effectiveness of the fault description based attribute transfer method. Also, we present the fine-grained attribute descriptions of TEP used in experiments to show the auxiliary information for the zero-sample fault diagnosis task.

TEP consists of five major subsystems, including a reactor, a condenser, a vapor-liquid separator, a recycle compressor, and a product stripper. The dataset offers 21 kinds of faults, and each kind of fault is described by 41 measured variables and 11 manipulated variables. 480 samples are collected for the training of each fault. Since the last 6 kinds of faults have little description in the dataset, the first 15 kinds of faults are utilized here to conduct the zero-sample fault diagnosis. The 15 fault categories are introduced in Table I. The studied 15 faults of TEP are different from each other. When some of them have zero sample for the model training, it is hard for the conventional methods to conduct the fault diagnosis. Hence, the proposed zero-sample fault diagnosis is actually meaningful and practical.

2) Details of Model Implementation: The fault descriptions are the basis of the proposed solution for the zero-sample fault diagnosis. The configuration of the attribute descriptions in vector space for TEP, i.e., the attribute matrix \( \mathbf{A} \) , are shown in Fig. 3, and the specific attribute names are shown in Table II. Each kind of fault is described by 20 fine-grained attributes. Comparing Table II with Table I, it should be noticed that the attribute descriptions can readily be concluded from the statements in Table I. Based on the fault descriptions, we diagnose target faults without their samples for model training.

The first step of the description based method in Fig. 2 is the feature extraction. The supervised principle component analysis is adopted to extract the attribute related features. Specifically, each pair \( \left\{  {{\mathbf{X}}_{S},{\mathbf{z}}_{i}^{S}}\right\}  \left( {i = 1,\ldots , C}\right) \) is fitted by the supervised principle component analysis [34]. And then the extractor is applied to the test data \( {\mathbf{X}}_{T} \) . For each attribute, 20 features are extracted from the original 52 variables. Since there are 20 different attributes, a dataset with 400 features is prepared for the following steps after the feature extraction. The second step is the training of attribute learners. Three different machine learning algorithms are adopted here, including the linear support vector machine (LSVM), the nonlinear random forest (RF), and the probabilistic naïve Bayes (NB). We use the implementation of scikit-learn [37] for the three models for a fair comparison. The parameter of slack term of LSVM is set as 1, and the number of decision trees of RF is set as 50 . Last, the nearest neighbor searching is used as the inference rule to decide the final fault categories, and the ordinary Euclidean distance is used.

TABLE I

THE FAULTS OF TEP USED IN THE ZERO-SAMPLE FAULT DIAGNOSIS

<table><tr><td>\( \mathbf{{No}.} \)</td><td>Fault state</td><td>Disturb</td></tr><tr><td>1</td><td>\( \mathrm{A}/\mathrm{C} \) feed ratio, \( \mathrm{B} \) composition constant</td><td>Step change</td></tr><tr><td>2</td><td>B composition, \( \mathrm{A}/\mathrm{C} \) ratio constant</td><td>Step change</td></tr><tr><td>3</td><td>D feed temperature</td><td>Step change</td></tr><tr><td>4</td><td>Reactor cooling water inlet temperature</td><td>Step change</td></tr><tr><td>5</td><td>Condenser cooling water temperature</td><td>Step change</td></tr><tr><td>6</td><td>A feed loss</td><td>Step change</td></tr><tr><td>7</td><td>C header pressure loss</td><td>Step change</td></tr><tr><td>8</td><td>\( \mathrm{A},\mathrm{B},\mathrm{C} \) feed composition</td><td>Random variants</td></tr><tr><td>9</td><td>D feed temperature</td><td>Random variants</td></tr><tr><td>10</td><td>C feed temperature</td><td>Random variants</td></tr><tr><td>11</td><td>Reactor cooling water inlet temperature</td><td>Random variants</td></tr><tr><td>12</td><td>Condenser cooling water valve</td><td>Random variants</td></tr><tr><td>13</td><td>Reaction kinetics</td><td>Slow drift</td></tr><tr><td>14</td><td>Reactor cooling water valve</td><td>Sticking</td></tr><tr><td>15</td><td>Condenser cooling water valve</td><td>Sticking</td></tr></table>

There are 15 kinds of faults utilized in TEP, and 80%-20% split is adopted for the train/test division, which means 12 kinds of faults are set as the training faults and the 3 other faults are set as the target faults. To test the performance on the whole dataset, the TEP dataset is divided into 5 groups, and each group has 3 faults for test and 12 faults for training. The train/test division is shown in Table III. The number of training samples is \( {5760}\left( {{12} * {480}}\right) \) , and the number of the test samples is 1440 (3*480). We report the accuracy of the five group experiments. The accuracy is given by

\[
{Acc} = \frac{{N}_{C}}{N} \tag{6}
\]

where \( {N}_{C} \) is the number of correctly classified samples, and \( N \) is the number of samples of target faults at test stage.

![bo_d1iabrk601uc738hi9sg_4_402_1645_984_504_0.jpg](images/bo_d1iabrk601uc738hi9sg_4_402_1645_984_504_0.jpg)

Fig. 3. The fault description matrix \( \mathbf{A} \) for TEP dataset. Each kind of fault is described by 20 fine-grained attributes. the "1" in figure denotes the fault has this attribute, and the " 0 " denotes not. The specific names of attributes are shown in Table II.

1551-3203 (c) 2020 IEEE. Personal use is permitted, but republication/redistribution requires IEEE permission. See http://www.ieee.org/publications_standards/publications/rights/index.html for more information. Authorized licensed use limited to: University of Exeter. Downloaded on May 03,2020 at 09:38:40 UTC from IEEE Xplore. Restrictions apply.

TABLE II

THE SPECIFIC INFORMATION OF ATTRIBUTES FOR TEP

<table><tr><td>\( \mathbf{{No}.} \)</td><td>Attributes</td></tr><tr><td>Att#1</td><td>Input A is changed</td></tr><tr><td>Att#2</td><td>Input \( \mathrm{C} \) is changed</td></tr><tr><td>Att#3</td><td>A/C ratio is changed</td></tr><tr><td>Att#4</td><td>Input \( B \) is changed</td></tr><tr><td>Att#5</td><td>Related with pipe4</td></tr><tr><td>Att#6</td><td>Temperature of input \( \mathrm{D} \) is changed</td></tr><tr><td>Att#7</td><td>Related with pipe2</td></tr><tr><td>Att#8</td><td>Disturbance is step changing</td></tr><tr><td>Att#9</td><td>Input is changed</td></tr><tr><td>Att#10</td><td>Temperature of input is changed</td></tr><tr><td>Att#11</td><td>Occurred at reactor</td></tr><tr><td>Att#12</td><td>Temperature of cooling water is changed</td></tr><tr><td>Att#13</td><td>Occurred at condenser</td></tr><tr><td>Att#14</td><td>Related with pipe 1</td></tr><tr><td>Att#15</td><td>Disturbance is random varying</td></tr><tr><td>Att#16</td><td>Model parameters are changed</td></tr><tr><td>Att#17</td><td>Disturbance is slow drift</td></tr><tr><td>Att#18</td><td>Related with cooling water</td></tr><tr><td>Att#19</td><td>Related with valve</td></tr><tr><td>Att#20</td><td>Disturbance is sticking</td></tr></table>

3) Results of Zero-Sample Fault Diagnosis: The results of zero-sample fault diagnosis are shown in Table IV. The highest accuracies varying from 62.63% to 88.40% with the change of the train/test split. The confusion matrices of the best results in the five group experiments are shown in Fig. 4. Clearly, the performance of the proposed fault description based attribute transfer method is significantly higher than the chance level of \( {33.33}\% \) , which proves the initial aims of the fault description based method: it is possible to diagnose and classify different kinds of faults without their training samples based on the human-defined fault descriptions. As for the accuracies of some specific fault categories, such as \( {26}\% \) in Group D and \( {21}\% \) in Group E in Fig. 4, they are caused by the difficulty of the zero-sample task, and the few-sample experiments in next subsection will show that our results are actually quite competitive. Besides, it is worth mentioning that although the proof for the feasibility of sharing of attribute learners are limited to the linear case, the higher performance of nonlinear classifier and probabilistic classifiers, i.e., RF and NB, shows that the method is generally applicable.

TABLE III

THE FIVE GROUPS OF TRAIN/TEST SPLIT FOR TEP

<table><tr><td>\( \mathbf{{No}.} \)</td><td>Training Faults</td><td>Target Faults</td></tr><tr><td>A</td><td>2, 3, 4, 7-13, 15</td><td>1, 6, 14</td></tr><tr><td>B</td><td>\( 1,2,3,5,6,8,9,{11} - {15} \)</td><td>4, 7, 10</td></tr><tr><td>C</td><td>\( 1 - 7,9,{10},{13},{14},{15} \)</td><td>8, 11, 12</td></tr><tr><td>D</td><td>1, 4, 6-15</td><td>2,3,5</td></tr><tr><td>E</td><td>1-8, 10, 11, 12, 14</td><td>9,13,15</td></tr></table>

To the best knowledge of the authors, no previous data-driven based methods achieve the fault diagnosis and classification without their samples for model training. As discussed in Introduction, typical techniques including the deep transfer learning, and fault tree analysis, are not applicable for the zero-sample fault diagnosis setting. Hence, to provide the comparison under the same setting, the zero-shot learning methods, i.e., DAP and IAP proposed by Lampert et al. [22], [23], are utilized. Note that DAP and IAP are designed for the image classification task, and the "shapes" or "colors" attributes designed by Lampert cannot be utilized here, but the fault descriptions concluded in this paper are used. The image features, i.e., HoG and SIFT, are also inapplicable here. We make DAP and IAP learn from the original data. The slack term of the nonlinear support vector machine (Gaussian kernel) of DAP and IAP is set as 1. Hence, the comparison is actually based on our contribution, i.e., the fault descriptions. And although both of the ideas of DAP and ours are the attribute transfer, different attribute learners and features are utilized by our method. The results are shown in Table V. When the linear LSVM is set as the attribute learners, our method presents competitive accuracies with DAP and IAP through the 5 groups. And when the nonlinear attribute learners, i.e., RF, and the probabilistic attribute learner, i.e.,

TABLE IV

RESULTS OF ZERO-SAMPLE FAULT DIAGNOSIS FOR TEP(%)

<table><tr><td>Group.</td><td>\( \mathbf{{LSVM}} \)</td><td>\( \mathbf{{RF}} \)</td><td>\( \mathbf{{NB}} \)</td><td>Heighest</td><td>\( \mathbf{{Mean}} \)</td></tr><tr><td>A</td><td>58.68</td><td>88.40</td><td>80.27</td><td>88.40</td><td>75.78</td></tr><tr><td>B</td><td>58.12</td><td>51.87</td><td>62.63</td><td>62.63</td><td>57.54</td></tr><tr><td>C</td><td>44.51</td><td>48.33</td><td>67.56</td><td>67.56</td><td>53.46</td></tr><tr><td>D</td><td>53.34</td><td>61.73</td><td>72.43</td><td>72.43</td><td>62.50</td></tr><tr><td>E</td><td>42.56</td><td>43.12</td><td>67.43</td><td>67.43</td><td>51.03</td></tr></table>

![bo_d1iabrk601uc738hi9sg_5_919_895_734_1005_0.jpg](images/bo_d1iabrk601uc738hi9sg_5_919_895_734_1005_0.jpg)

Fig. 4. The confusion matrices of the best results of the five groups for TEP.

\( \mathrm{{NB}} \) , are exploited, our method obtains significantly higher accuracies. Also, we attempt to compare our methods with SJE by Akata et al. [24] and ESZSL by Romera-Paredes, et al. [25]. However, since both of the deep features of convolutional networks and the image based attributes required by SJE and ESZSL are unavailable under the fault diagnosis scenario, their performance suffers from the serious degradation on group \( \mathrm{B},\mathrm{C} \) , and, \( \mathrm{E} \) , which reveals their weak generalization ability under the industry scenario and the difficulty of the zero-sample fault diagnosis task.

TABLE V

COMPARISON WITH ZERO-SHOT LEARNING ON TEP(%)

<table><tr><td>Group.</td><td>A</td><td>B</td><td>C</td><td>D</td><td>E</td><td>Mean</td></tr><tr><td>DAP</td><td>54.16</td><td>62.63</td><td>40.13</td><td>55.48</td><td>36.94</td><td>49.86</td></tr><tr><td>IAP</td><td>55.48</td><td>60.69</td><td>45.00</td><td>36.25</td><td>48.88</td><td>49.26</td></tr><tr><td>SJE</td><td>74.58</td><td>33.12</td><td>33.95</td><td>63.88</td><td>33.81</td><td>47.86</td></tr><tr><td>ESZSL</td><td>57.22</td><td>33.33</td><td>39.51</td><td>39.65</td><td>33.33</td><td>40.60</td></tr><tr><td>Ours(LSVM)</td><td>58.68</td><td>58.12</td><td>44.51</td><td>53.34</td><td>42.56</td><td>51.44</td></tr><tr><td>Ours(RF)</td><td>88.40</td><td>51.87</td><td>48.33</td><td>61.73</td><td>43.12</td><td>58.69</td></tr><tr><td>Ours(NB)</td><td>80.27</td><td>62.63</td><td>67.56</td><td>72.43</td><td>67.43</td><td>69.86</td></tr></table>

Note: both of the deep features of convolutional networks and the image based attributes required by SJE and ESZSL are unavailable under the fault diagnosis scenario. Their unsatisfactory performance on group \( \mathrm{B},\mathrm{C},\mathrm{E} \) reveals their poor generalization ability for the zero-sample fau-It diagnosis task.

Another performance which should be assessed is the accuracies of the attribute learners, which is the basis of the diagnosis of zero-sample faults. In our method, the model learns the fault attributes first, and then the model knows the faults. The accuracies of 20 attribute learners for the group A with RF as attribute leaners are shown in Fig. 5. Most of the attribute learners perform much better than the random guess (50% accuracy), and some of them even present above \( {90}\% \) accuracies for the group A, which directly illustrates the effectiveness of the proposed fault description based method.

4) Comparison with Few-Sample Learning: For comparison, we also conduct the few-sample learning experiments based on the group A and group C, in which 1, 10, 50, 200, 500 samples are used for the model training. The training samples are randomly selected from the other group data of TEP, and the test data are the same as those in the zero-sample fault diagnosis. The compared algorithms include LSVM, RF, NB, XGBoost (XGB) [38], AdaBoost (ADA), K-neighbors (KNN), gradient boosting machine (GBDT) [39], and light gradient boosting machine (LGBM) [40]. For all the compared models, the implementations by their authors or scikit-learn package [37] are utilized for a fair comparison. The LSVM model of scikit-learn package used in experiments adopts the "1-v-1" strategy by default for the multi-class classification problem. As for the parameters, we uniformly adopt the default settings by their authors without any adjustment, which can usually present good performance. The results of the few-sample learning experiments are shown in Table VI and Table VII.

TABLE VI

COMPARISON WITH FEW-SAMPLE LEARNING ON GROUP A

<table><tr><td rowspan="2">Acc(%)</td><td colspan="5">Number of training samples for each fault</td></tr><tr><td>1</td><td>10</td><td>50</td><td>200</td><td>500</td></tr><tr><td>LSVM</td><td>23.54</td><td>17.98</td><td>39.23</td><td>49.79</td><td>87.15</td></tr><tr><td>RF</td><td>25.00</td><td>55.55</td><td>50.90</td><td>57.01</td><td>99.37</td></tr><tr><td>NB</td><td>23.54</td><td>25.41</td><td>34.72</td><td>84.86</td><td>96.80</td></tr><tr><td>XGB</td><td>33.33</td><td>49.16</td><td>53.33</td><td>69.86</td><td>99.65</td></tr><tr><td>ADA</td><td>25.90</td><td>53.68</td><td>38.81</td><td>74.58</td><td>89.58</td></tr><tr><td>KNN</td><td>23.54</td><td>26.94</td><td>52.50</td><td>48.95</td><td>86.11</td></tr><tr><td>GBDT</td><td>25.48</td><td>55.20</td><td>27.15</td><td>47.63</td><td>99.72</td></tr><tr><td>LGBM</td><td>33.33</td><td>33.33</td><td>48.95</td><td>61.59</td><td>100.0</td></tr><tr><td>Ours</td><td colspan="5">88.40</td></tr></table>

TABLE VII

COMPARISON WITH FEW-SAMPLE LEARNING ON GROUP C

<table><tr><td rowspan="2">Acc(%)</td><td colspan="5">Number of training samples for each fault</td></tr><tr><td>1</td><td>10</td><td>50</td><td>200</td><td>500</td></tr><tr><td>LSVM</td><td>32.15</td><td>23.05</td><td>27.43</td><td>49.51</td><td>62.01</td></tr><tr><td>RF</td><td>27.84</td><td>27.56</td><td>35.55</td><td>60.34</td><td>79.44</td></tr><tr><td>NB</td><td>32.15</td><td>29.44</td><td>41.31</td><td>77.29</td><td>79.37</td></tr><tr><td>XGB</td><td>33.33</td><td>25.00</td><td>29.37</td><td>56.25</td><td>82.15</td></tr><tr><td>ADA</td><td>25.83</td><td>26.59</td><td>28.75</td><td>58.05</td><td>70.90</td></tr><tr><td>KNN</td><td>32.15</td><td>31.66</td><td>31.80</td><td>53.12</td><td>67.70</td></tr><tr><td>GBDT</td><td>32.50</td><td>29.79</td><td>31.80</td><td>57.84</td><td>82.29</td></tr><tr><td>LGBM</td><td>33.33</td><td>33.33</td><td>30.20</td><td>59.79</td><td>81.66</td></tr><tr><td>Ours</td><td colspan="5">67.56</td></tr></table>

As shown in Table VI and VII, the conventional machine learning algorithms perform definitely poor when only a few fault samples are provided, and some of them perform even worse than the random choice (33.33%) when only 1 sample is used. Although TEP is a fully studied and classic fault diagnosis dataset, the compared methods still need at least 200 samples to present the competitive results as ours, which shows the difficulty of the task of zero-sample fault diagnosis. Actually, it is unfair for our methods to compare with the few-sample learning, since the zero-sample setting and one-sample setting are totally different and more difficult. The concluded fault descriptions indeed offer additional fault information for the diagnosis.

5) Robustness to the Noises: The noises are typical and widespread disturbance in the industrial process. Hence, the noise experiment is conducted to show the robustness of the proposed method, in which the training and test faults are added with noises. The added noises are assigned to the Gaussian distributions with zero mean and varying variance. \( \mathrm{{NB}} \) is adopted as the attribute learners. The results are shown in Table VIII.

![bo_d1iabrk601uc738hi9sg_6_312_1726_1155_449_0.jpg](images/bo_d1iabrk601uc738hi9sg_6_312_1726_1155_449_0.jpg)

Fig. 5. The accuracies of 20 attribute learners for the group A with RF as attribute learners.

![bo_d1iabrk601uc738hi9sg_7_386_153_1004_507_0.jpg](images/bo_d1iabrk601uc738hi9sg_7_386_153_1004_507_0.jpg)

Fig. 6. Systematic configuration for \( {1000}\mathrm{{MW}} \) ultra-supercritical thermal power unit.

Generally, the proposed method performs well when the variance of noise is less than 0.5 . When the variance of noise is larger than 0.5 , the performance is degraded. And sometimes, the system performs even better with a little noise, such as the performance on group A when the variance of noise is set as 0.3 . The proposed method diagnoses faults based on the recognition of different attributes, which is a basic binary classification task. As shown in Table VIII, both the average accuracies of 20 attribute learners for group A and group C are stable under the noisy settings, which help to obtain the robust results for the zero-sample task.

TABLE VIII

RESULTS OF THE NOISE EXPERIMENTS ON GROUP A AND C

<table><tr><td>Group</td><td colspan="2">A</td><td colspan="2">C</td></tr><tr><td>Noise</td><td>\( \mathbf{{Acc}\left( \% \right) } \)</td><td>Att(%)</td><td>\( \mathbf{{Acc}\left( \% \right) } \)</td><td>Att(%)</td></tr><tr><td>-</td><td>80.27</td><td>70.51</td><td>67.56</td><td>60.69</td></tr><tr><td>\( G\left( {0.1}\right) \)</td><td>80.06</td><td>70.51</td><td>64.93</td><td>58.59</td></tr><tr><td>\( G\left( {0.3}\right) \)</td><td>82.56</td><td>70.50</td><td>65.00</td><td>58.55</td></tr><tr><td>\( G\left( {0.5}\right) \)</td><td>83.68</td><td>70.52</td><td>66.04</td><td>58.42</td></tr><tr><td>\( G\left( {0.7}\right) \)</td><td>73.33</td><td>69.72</td><td>63.26</td><td>57.93</td></tr><tr><td>\( G \) (1.0)</td><td>73.26</td><td>69.99</td><td>60.09</td><td>57.76</td></tr><tr><td>\( G \) (5.0)</td><td>65.55</td><td>67.21</td><td>57.63</td><td>56.83</td></tr></table>

\( G\left( v\right) \) denotes the Gaussian noise, where \( v \) is the variance. And the mean of the noise is zero. "Acc" denotes the accuracy of zero-sample fault diagnosis, and "Att" is the corresponding average accuracy of 20 attribute learners. NB is adopted as the attribute learners.

## B. Thermal Power Plant Process

1) Introduction for Dataset: The thermal power plant (TPP) fault dataset is acquired from a real industrial process of a 1000 MW ultra-supercritical thermal power unit. The thermal process consists of two main subsystems, i.e., the boiler system and the steam turbine system. During the generating process, the boiler system first heats the water to the steam with high pressure and temperature. And then the steam is transported to the turbine system to drive the electrical generator. The whole power unit achieves the transformation from chemical energy to electrical energy. Since the thermal power generation is a large-scale process, collecting fault samples by unit shutdown

![bo_d1iabrk601uc738hi9sg_7_925_749_739_446_0.jpg](images/bo_d1iabrk601uc738hi9sg_7_925_749_739_446_0.jpg)

Fig. 7. The fault description matrix \( \mathbf{A} \) for TPP. Each kind of fault is described by 15 fine-grained attributes. the " 1 " in figure denotes the fault has this attribute, and the " 0 " denotes not. The specific names of attributes are shown in Table IX.

could cause huge losses. Hence, it is of great significance to diagnose certain fault categories under the zero-sample setting.

TABLE IX

THE INFORMATION OF ATTRIBUTES FOR THE THERMAL POWER PLANT

<table><tr><td>\( \mathbf{{No}.} \)</td><td>Attributes</td></tr><tr><td>Att#1</td><td>Occurred at the \( 5\# \) low pressure heater</td></tr><tr><td>Att#2</td><td>Occurred at the coal mill</td></tr><tr><td>Att#3</td><td>Occurred at the small turbine</td></tr><tr><td>Att#4</td><td>Occurred at the condenser pump</td></tr><tr><td>Att#5</td><td>Related with valve</td></tr><tr><td>Att#6</td><td>Related with bearing</td></tr><tr><td>Att#7</td><td>Related with coil</td></tr><tr><td>Att#8</td><td>Related with grand</td></tr><tr><td>Att#9</td><td>The shift problem</td></tr><tr><td>Att#10</td><td>The pressure problem</td></tr><tr><td>Att#11</td><td>The vibration problem</td></tr><tr><td>Att#12</td><td>The temperature problem</td></tr><tr><td>Att#13</td><td>Abnormal index value</td></tr><tr><td>Att#14</td><td>Low index value</td></tr><tr><td>Att#15</td><td>High index value</td></tr></table>

The dataset offers eight kinds of faults which occur over the whole unit. Each kind of fault is illustrated by 68 measured signals and 4320 samples. The faults are described with 15 kinds of attributes. The fault description matrix \( \mathbf{A} \) for the thermal power plant data set is shown in Fig. 7 and the specific attribute information is shown in Table IX. To test the robustness of the proposed fault description based attribute transfer method, four different groups of train/test split are adopted and shown in Table X. Each group contains five kinds of faults for model training and three kinds of faults for test. The model implementation is the same as that for TEP.

TABLE X

THE FOUR GROUPS OF TRAIN/TEST SPLIT FOR TPP

<table><tr><td>\( \mathbf{{No}.} \)</td><td>Training Faults</td><td>Target Faults</td></tr><tr><td>A</td><td>4, 5, 6, 7, 8</td><td>1,2,3</td></tr><tr><td>B</td><td>1,2,3,7,8</td><td>4, 5, 6</td></tr><tr><td>C</td><td>2,3,4,5,6</td><td>7, 8, 1</td></tr><tr><td>D</td><td>1,3,4,6,7</td><td>2, 5,8</td></tr></table>

TABLE XI

RESULTS OF ZERO-SAMPLE FAULT DIAGNOSIS FOR TPP(%)

<table><tr><td>\( \mathbf{{Group}.} \)</td><td>LSVM</td><td>\( \mathbf{{RF}} \)</td><td>\( \mathbf{{NB}} \)</td><td>Heighest</td><td>\( \mathbf{{Mean}} \)</td></tr><tr><td>A</td><td>92.53</td><td>99.48</td><td>90.82</td><td>99.48</td><td>94.28</td></tr><tr><td>B</td><td>99.90</td><td>83.31</td><td>89.69</td><td>99.90</td><td>90.97</td></tr><tr><td>C</td><td>83.91</td><td>80.44</td><td>90.87</td><td>90.87</td><td>85.07</td></tr><tr><td>D</td><td>92.21</td><td>99.96</td><td>92.63</td><td>99.96</td><td>94.93</td></tr></table>

2) Results of Zero-Sample Fault Diagnosis: The results of zero-sample fault diagnosis for the thermal power plant process are shown in Table XI. The confusion matrices for the best results of the four group experiments are shown in Fig. 8. Generally, the results of zero-sample fault diagnosis for TPP are much higher than those for TEP. For the four different groups, the average accuracies vary from 85.07% to 94.93%, while those for TEP vary from 51.03% to 75.78%. This may be explained by the fault description matrix \( \mathbf{A} \) . The definition of \( \mathbf{A} \) for the thermal power plant process reveals more detailed information in comparison with that of TEP. For example, the specific process variables, i.e., the "temperature", the "pressure", and the "vibration", and the specific properties of process variables, i.e., the "low" and the "high", are provided in Table IX. The detailed information can be easily learned from the sensor signals and applied to recognize attributes accurately. And hence, all the three machine learning algorithms, i.e., LSVM, RF, and NB, can obtain good performance on the target faults. The accuracies of the 15 attribute learners for group A with RF as attribute learners are shown in Fig. 9. The average accuracy of 20 attribute learners for the group A of TEP with RF as attribute learners is 71.45%, while that of TPP is 75.06%. This validates the effectiveness of the proposed fault description based attribute transfer method, in which the fault descriptions and attributes are well-learned first, and then the faults can be well-diagnosed. Also, since many machines in the thermal power plant process are rotary machines, such as the turbine, the generator, and the coil mill, the process generally works under the noisy environment. Hence, the high performance shown in Table XI and Fig. 9 also validates the robustness and model-reality match of the proposed method.

![bo_d1iabrk601uc738hi9sg_8_143_1451_732_679_0.jpg](images/bo_d1iabrk601uc738hi9sg_8_143_1451_732_679_0.jpg)

Fig. 8. The confusion matrices of the best results of the four groups for the thermal power plant process.

![bo_d1iabrk601uc738hi9sg_8_924_161_738_387_0.jpg](images/bo_d1iabrk601uc738hi9sg_8_924_161_738_387_0.jpg)

Fig. 9. The accuracies of 15 attribute learners for the group A with RF as attribute learners for the thermal power plant process.

We also compare our method with other zero-shot learning methods. The experiment setting is the same as that of TEP. The results are shown in Table XII. For the thermal power plant process, the linear LSVM presents higher average accuracies through four different groups, which reveals the linear characteristic of the process. The SJE and ESZSL show unstable performance on group \( \mathrm{A} \) and group \( \mathrm{B} \) , since the designed deep features are inapplicable here. Also, DAP and IAP present lower accuracies in comparison with ours. DAP directly learn from the fault samples in experiment, while the designed model is assigned to learn from the attribute related features by the supervised principle component analysis. Since both of DAP and ours are based on the transfer of attributes, the results may reveal the importance of the features for the zero-sample fault diagnosis task, which can be indicated by the Equation (5).

TABLE XII

COMPARISON WITH ZERO-SHOT LEARNING ON TPP(%)

<table><tr><td>Group.</td><td>A</td><td>\( \mathbf{B} \)</td><td>C</td><td>D</td><td>\( \mathbf{{Mean}} \)</td></tr><tr><td>DAP</td><td>96.20</td><td>81.32</td><td>89.21</td><td>93.67</td><td>90.10</td></tr><tr><td>IAP</td><td>92.28</td><td>96.33</td><td>81.33</td><td>90.09</td><td>90.01</td></tr><tr><td>SJE</td><td>66.78</td><td>99.33</td><td>68.20</td><td>99.33</td><td>83.41</td></tr><tr><td>ESZSL</td><td>62.78</td><td>96.28</td><td>72.37</td><td>94.96</td><td>81.59</td></tr><tr><td>Ours(RF)</td><td>99.48</td><td>83.31</td><td>80.44</td><td>99.96</td><td>90.79</td></tr><tr><td>Ours(NB)</td><td>90.82</td><td>89.69</td><td>90.87</td><td>92.63</td><td>91.00</td></tr><tr><td>Ours(LSVM)</td><td>92.53</td><td>99.90</td><td>83.91</td><td>92.21</td><td>92.13</td></tr></table>

Note: both of the deep features of convolutional networks and the image based attributes required by SJE and ESZSL are unavailable under the fault diagnosis scenario. Their unsatisfactory performance on group A and \( \mathrm{C} \) reveals their poor generalization ability for the zero-sample fault diagnosis task.

## V. CONCLUSIONS

Considering the difficulty of collecting fault samples, we formulize the zero-sample fault diagnosis task in this paper. And the fault description based attribute transfer is designed as the first attempt to diagnose target faults without their samples. In comparison with the few-sample learning on the benchmark dataset, the advantage that zero sample of target faults is required can be observed. The high accuracies on the real thermal power plant process and the feasibility analysis also show that with the well-designed attribute descriptions for faults, it is indeed possible to achieve the zero-sample diagnosis. And there are many works can be developed and improved in future study. For examples: (1) The solution of zero-sample diagnosis that generating samples for the target faults based on their fault descriptions using some popular generative models, i.e., the generative adversarial networks, can be considered; (2) In this paper, the supervised principle component analysis is utilized as the basic method to extract the attribute related features for the zero-sample diagnosis, and more meaningful and interpretable methods can be developed to conduct the task better. REFERENCES

[1] Y. Ma, S. Bing, H. Shi, et al. "Fault detection via local and nonlocal embedding," Chemical Engineering Research & Design, vol. 94, pp.538- 548, 2015.

[2] Q. Liu, S. J. Qin, T. Chai, "Decentralized fault diagnosis of continuous annealing processes based on multilevel PCA," IEEE Trans. Automation Science & Engineering, vol. 10, no. 3, pp. 687-698, 2013.

[3] W. Fan, T. Shuai, Y. Yang, et al, "Hidden Markov model-based fault detection approach for multimode process," Industrial & Engineering Chemistry Research, vol. 55, no. 16, pp. 4613-4621, 2016.

[4] S. Zhao, B. Huang, L. Fei, "Fault detection and diagnosis of multiple-model systems with mismodeled transition probabilities", IEEE Trans. Industrial Electronics, vol. 62, no. 8, pp. 5063-5071, 2015.

[5] Q. Liu, Q. Q. Zhu, S. J. Qin, etc. "Dynamic concurrent kernel CCA for strip-thickness relevant fault diagnosis of continuous annealing processes", J. Process Control, vol. 67, 2017.

[6] G. Xin, H. Jian. "An improved SVM integrated GS-PCA fault diagnosis approach of Tennessee Eastman process", Neurocomputing, vol. 32, pp. 1023-1034, 2015.

[7] X. Q. Deng, X. M. Tian, X. Y. Hu. "Nonlinear process fault diagnosis based on slow feature analysis", in Proc. Intelligent Control and Automation, 2012.

[8] L. Cui, N. Wu, W. Wang, et al., "Sensor-based vibration signal feature extraction using an improved composite dictionary matching pursuit algorithm", Sensors, vol. 14, no. 9, pp. 16715-16739, 2014.

[9] L. Eren, T. Ince, and S. Kiranyaz, "A generic intelligent bearing fault diagnosis system using compact adaptive 1D CNN classifier", Journal of Signal Processing Systems, vol. 91, no. 2, pp. 179-189, 2019.

[10] Y. L. Murphey, M. A. Masrur, Z. H. Chen, etc., "Model-based fault diagnosis in electric drives using machine learning", IEEE/ASME Transactions on Mechatronics, vol. 11, no. 3, pp. 290-303, 2006.

[11] C. Sun, M. Ma, Z. Zhao, et al., "Deep transfer learning based on sparse auto-encoder for remaining useful life prediction of tool in manufacturing", IEEE Trans. Industrial Informatics, vol. 15. No. 4, pp. 2416-2425, 2018.

[12] J. Zhao, D. T. Ouyang, X. Y. Wang, et al., "The modeling procedures for model-based diagnosis of slowly changing fault in hybrid system", Advanced Materials Research, vol. 186, pp. 403-407, 2011.

[13] Y. Pan, F. Mei, H. Miao, et al., "An approach for HVCB mechanical fault diagnosis based on a deep belief network and a transfer learning strategy", Journal of Electrical Engin. & Techn., vol. 14, no. 1, pp. 407- 419, 2019.

[14] S. S. Yu, M. A. Stephen, Y. Ruqiang, et al., "Highly-accurate machine fault diagnosis using deep transfer learning", IEEE Trans. Industrial Informatics, vol. 15, no. 4, pp. 2446-2455, 2019.

[15] W. Long, G. Liang, and X. Li, "A new deep transfer learning based on sparse auto-encoder for fault diagnosis," IEEE Trans. Systems Man & Cybernetics Systems, vol. 49, no. 1, pp. 136-144, 2018.

[16] W. Lu, B. Liang, Y. Cheng, et al., "Deep model based domain adaptation for fault diagnosis," IEEE Trans. Ind. Electron., vol.64, no.3, pp. 2296- 2305, 2017.

[17] S. J. Pan, Q. Yang, "A survey on transfer learning," IEEE Trans. Knowledge & Data Engineering, vol. 22, no.10, pp.1345-1359.

[18] W. S. Lee, D. L. Grosh, F. A. Tillman, et al. "Fault tree analysis, methods, and application: a review," IEEE Trans. Reliability, vol. 34, no.3, pp.194-203, 2009.

[19] A. K. Reay, J. D. Andrews. "A fault tree analysis strategy using binary decision diagrams," Reliability Engineering & System Safety, vol. 78, no. 1, pp. 45-56, 2002.

[20] R. M. Sinnamon, J. D. Andrews, "Improved efficiency in qualitative fault tree analysis," Quality & Reliability Engineering International, vol. 13, no. 5, pp. 293-298, 1997.

[21] C. Samir, "Fault tree analysis," John Wiley & Sons, Inc. 2006.

[22] C. H. Lampert, H. Nickisch, and S. Harmeling. "Learning to detect unseen object classes by between-class attribute transfer," in Proc. IEEE Conf. Comput. Vis. Pattern Recognit., Jun. 2009, pp. 951-958.

[23] C. H. Lampert, H. Nickisch, and S. Harmeling, "Attribute-based classification for zero-shot visual object categorization," IEEE Trans. Pattern Anal. Mach. Intell., vol. 36, no. 3, pp. 453-465, Mar. 2014.

[24] Z. Akata, S. Reed, and D. Walter, et al. "Evaluation of output embed-dings for fine-grained image classification," in Proc. IEEE Conf. Comput. Vis. Pattern Recognit., Jun. 2015.

[25] B. Romera-Paredes and P. H. Torr, "An embarrassingly simple approach to zero-shot learning," in Proc. Int. Conf. Machine Learning, 2015.

[26] S. Kang, D. Lee, C. D. Yoo. "Face attribute classification using attribute-aware correlation map and gated convolutional neural networks," in Proc. IEEE International Conference on Image Processing, 2015.

[27] C. H. Zhao, Y. X. Sun, "Comprehensive subspace decomposition and isolation of principal reconstruction directions for online fault diagnosis," Journal of Northwest A & F University, vol. 23, no. 10, pp. 1515-1527, 2013.

[28] C. H. Zhao, F. R. Gao, "Fault subspace selection approach combined with analysis of relative changes for reconstruction modeling and mul-tifault diagnosis," IEEE Trans. on Control Systems Technology, vol. 24, no. 3, pp. 1-12, 2015.

[29] J. C. Wang, Y. B. Zhang, et al. "Dimension reduction method of independent component analysis for process monitoring based on minimum mean square error," J. Process Control, vol. 22, no. 2, pp. 477-487, 2012

[30] C. H. Zhao, W. Q. Li, Y. X. Sun, "A sub-principal component of fault detection modeling method and its application to online fault diagnosis," in Proc. the 9th Asian Control Conference. 2013.

[31] J. H. Chen, C. M. Liao, et al., "Principle component analysis based control charts with memory effect for process monitoring," Industrial & Engineering Chemistry Research, vol. 40, no .6, pp. 1516-1527, 2001.

[32] M. Misra, H. H. Yue, S. J. Qin, etc. "Multivariate process monitoring and fault diagnosis by multi-scale PCA," Computers & Chemical Engineering, vol. 26, no. 9, pp. 1281-1293, 2002.

[33] M. Li, X. Wu, "Fault diagnosis for fans of coal based on CBR hybrid threshold method," in Proc. Int. Conf. Fuzzy Systems and Knowledge Discovery, 2010.

[34] E. Barshan, A. Ghodsi, Z. Azimifar, et al. "Supervised principal component analysis: Visualization, classification and regression on subspaces and submanifolds," Pattern Recognition, vol. 44, no. 7, pp.1357-1371, 2011.

[35] J. J. Downs, E. F. Vogel, "A plant-wide industrial process control problem," Computers & Chemical Engineering, vol. 17, no. 3, pp. 245- 255, 1993.

[36] W. Yu, and C. Zhao, "Online fault diagnosis in industrial process using multi-model exponential discriminant analysis algorithm," IEEE Trans. Control Systems Technology, vol. 27, no. 3, pp. 13317-1325, 2018.

[37] S. Ashish, J. Ritesh, "Scikit-learn: machine learning in python," Journal of Machine Learning Research, vol. 12, no.10 pp. 2825-2830, 2012.

[38] T. Q. Chen, C. Guestrin, "XGBoost: a scalable tree boosting system," in Proc. the 22nd ACM SIGKDD, 2016, pp. 785-794.

[39] H. J. Friedman. "Greedy function approximation: a gradient boosting machine," Annals of Statistics, vol. 29, no. 5, pp. 1189-1232.

[40] G. L. Ke, M. Qi, et al. "LightGBM: a highly efficient gradient boosting decision tree." Advances in Neural Information Processing Systems, 2017, pp. 3149-3157.
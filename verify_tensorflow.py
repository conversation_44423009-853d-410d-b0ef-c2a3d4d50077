import tensorflow as tf
import sys
import os

print("=== TensorFlow GPU 环境验证 ===")
print(f"Python版本: {sys.version}")
print(f"TensorFlow版本: {tf.__version__}")

# 检查CUDA支持
print(f"CUDA构建: {tf.test.is_built_with_cuda()}")

# 检查GPU设备
gpus = tf.config.list_physical_devices('GPU')
print(f"检测到GPU数量: {len(gpus)}")

if gpus:
    print("GPU设备信息:")
    for i, gpu in enumerate(gpus):
        print(f"  GPU {i}: {gpu}")
    
    # 设置GPU内存增长
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print("✅ GPU内存增长设置成功")
    except Exception as e:
        print(f"⚠️ GPU内存增长设置失败: {e}")
    
    # GPU计算测试
    try:
        print("\n进行GPU计算测试...")
        with tf.device('/GPU:0'):
            # 创建测试矩阵
            a = tf.random.normal([2000, 2000])
            b = tf.random.normal([2000, 2000])
            
            # 矩阵乘法
            import time
            start_time = time.time()
            c = tf.matmul(a, b)
            end_time = time.time()
            
            print(f"✅ GPU计算测试成功!")
            print(f"   矩阵大小: {a.shape} × {b.shape}")
            print(f"   计算时间: {end_time - start_time:.4f} 秒")
            print(f"   结果形状: {c.shape}")
        
        print("\n🎉 GPU环境完全正常！可以开始训练ACGAN-FG模型！")
        
    except Exception as e:
        print(f"❌ GPU计算测试失败: {e}")
        print("建议检查NVIDIA驱动和CUDA安装")
else:
    print("❌ 未检测到GPU设备")
    print("可能的原因:")
    print("1. NVIDIA驱动未正确安装")
    print("2. CUDA版本不兼容")
    print("3. TensorFlow版本不支持当前GPU")

# 检查CUDA版本信息
try:
    cuda_version = tf.sysconfig.get_build_info()['cuda_version']
    cudnn_version = tf.sysconfig.get_build_info()['cudnn_version']
    print(f"\nCUDA版本: {cuda_version}")
    print(f"cuDNN版本: {cudnn_version}")
except:
    print("\n无法获取CUDA版本信息")

print("\n=== 验证完成 ===")
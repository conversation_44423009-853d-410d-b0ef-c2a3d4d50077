#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 C-Hybrid实验: 融合HardTriplet强分离能力与SmartCRL稳定性
"""

import os
import sys
import datetime
from universal_group_config import UniversalGroupConfig, safe_center_loss, safe_update_centers
import numpy as np
import tensorflow as tf
import pandas as pd
from sklearn.preprocessing import StandardScaler
from tensorflow.keras.losses import mean_squared_error
from tensorflow.keras.layers import Input, Dense, LeakyReLU, BatchNormalization, Dropout, concatenate, multiply, Flatten
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
from read_data import creat_dataset
# 修复导入路径问题
try:
    from advanced_tensorboard_monitor import AdvancedTensorBoardMonitor
except ImportError:
    print("⚠️ AdvancedTensorBoardMonitor导入失败，TensorBoard功能将被禁用")
    AdvancedTensorBoardMonitor = None

# 修复导入问题：使用相对路径导入本地test模块
import importlib.util
spec = importlib.util.spec_from_file_location("test_eval", "/app/scripts/test.py")
test_eval = importlib.util.module_from_spec(spec)
spec.loader.exec_module(test_eval)
feature_generation_and_diagnosis = test_eval.feature_generation_and_diagnosis

# 设置GPU内存增长
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(e)

class Zero_shot():
    def __init__(self):
        self.data_lenth=52          # 输入数据维度
        self.attribute_dim=20       # 属性标签维度
        self.sample_shape=(self.data_lenth,)

        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 2
        self.crl = True

        # 🔥 E-UltraEnhanced实验: 针对E组零相似度问题的超强化权重
        self.lambda_cla = 1.0           # 分类损失权重
        self.lambda_triplet = 10     # 🚀 超强化Triplet权重 (50->100) 针对零相似度
        self.lambda_cms = 1.0           # CMS损失权重
        self.lambda_crl = 0.2           # 🚀 超强化CRL权重 (0.1->0.2) 强化循环一致性

        self.bound = True
        self.mi_weight = 0.001
        self.mi_bound = 100
        self.triplet_margin = 0.5       # 🚀 增大边际 (0.4->0.5) 强制更大分离

        # 🔥 超强化Center Loss权重 - 针对E组分散问题
        self.lambda_center = 15.0        # 🚀 超强化权重 (2.5->5.0) 强制类内聚合
        self.centers = None
        self.seen_class_map = None
        self.center_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        
        # 🔥 超强化语义一致性约束 (针对E组零相似度)
        self.lambda_semantic_consistency = 0.1   # 🚀 超强化语义权重 (0.05->0.1)
        self.semantic_margin = 0.4               # 🚀 增大语义边际 (0.3->0.4)

        # 最优准确率跟踪
        self.best_accuracy = {
            'lsvm': 0.0,
            'nrf': 0.0,
            'pnb': 0.0,
            'mlp': 0.0
        }
        
        # 构建模型
        self.build_autoencoder()
        self.discriminator = self.build_discriminator()
        self.generator = self.build_generator()
        self.classifier = self.build_classifier()

        # 为了兼容feature_generation_and_diagnosis函数，添加别名
        self.d = self.discriminator
        self.g = self.generator
        self.c = self.classifier
        
        # 🚀 E-UltraEnhanced优化器 - 超强化学习率针对E组困难学习
        self.optimizer_G = Adam(0.0003, 0.5)   # 🚀 超强化 (0.0002->0.0003) 强化生成
        self.optimizer_D = Adam(0.00015, 0.5)  # 🚀 适度提升，保持GAN平衡
        self.optimizer_C = Adam(0.0003, 0.5)   # 🚀 超强化分类学习
        self.optimizer_M = Adam(0.0003, 0.5)   # 🚀 超强化特征学习，突破E组瓶颈
        
        # 编译判别器
        self.discriminator.compile(loss=self.wasserstein_loss,
            optimizer=self.optimizer_D,
            metrics=['accuracy'])
        
        # 编译分类器
        self.classifier.compile(loss='binary_crossentropy',
            optimizer=self.optimizer_C,
            metrics=['accuracy'])

        # 🔥 移除错误的中心初始化，将在train方法中正确初始化
        # self.initialize_centers()  # 已移除，避免错误的seen_class_map

        # 🔥 添加TensorBoard监控器（动态路径，稍后在train方法中初始化）
        self.tensorboard_monitor = None
        self.training_data = {
            'epochs': [],
            'ae_c_loss': [],
            'g_loss': [],
            'd_loss': [],
            'triplet_loss': [],
            'center_loss': [],
            'combined_loss': [],
            'crl_loss': [],
            'semantic_loss': [],
            'lsvm_acc': [],
            'rf_acc': [],
            'nb_acc': [],
            'mlp_acc': []
        }

    def initialize_centers(self):
        """❌ 已废弃：硬编码seen_class_map导致严重问题
        
        原问题：硬编码seen_class_map只包含少数类别，导致Center Loss失效
        修复：中心初始化已移到train方法中，使用UniversalGroupConfig动态配置
        """
        print("⚠️ initialize_centers已废弃，请使用train方法中的动态初始化")
        pass
    def build_autoencoder(self):
        """构建自编码器"""
        # 编码器
        encoder_input = Input(shape=self.sample_shape)
        x = Dense(512)(encoder_input)
        x = LeakyReLU(alpha=0.2)(x)
        x = BatchNormalization(momentum=0.8)(x)
        x = Dense(256)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = BatchNormalization(momentum=0.8)(x)
        encoded = Dense(self.feature_dim)(x)
        
        self.encoder = Model(encoder_input, encoded, name='encoder')
        
        # 解码器
        decoder_input = Input(shape=self.feature_shape)
        x = Dense(256)(decoder_input)
        x = LeakyReLU(alpha=0.2)(x)
        x = BatchNormalization(momentum=0.8)(x)
        x = Dense(512)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = BatchNormalization(momentum=0.8)(x)
        decoded = Dense(self.data_lenth, activation='tanh')(x)
        
        self.decoder = Model(decoder_input, decoded, name='decoder')
        
        # 完整自编码器 - 返回编码特征和重建数据
        autoencoder_input = Input(shape=self.sample_shape)
        encoded_repr = self.encoder(autoencoder_input)
        reconstructed = self.decoder(encoded_repr)
        self.autoencoder = Model(autoencoder_input, [encoded_repr, reconstructed], name='autoencoder')

    def build_generator(self):
        """构建生成器"""
        noise = Input(shape=(self.latent_dim,))
        label = Input(shape=(self.attribute_dim,))  # 修改为属性维度

        model_input = concatenate([noise, label])

        x = Dense(128)(model_input)
        x = LeakyReLU(alpha=0.2)(x)
        x = BatchNormalization(momentum=0.8)(x)
        x = Dense(256)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = BatchNormalization(momentum=0.8)(x)
        x = Dense(512)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = BatchNormalization(momentum=0.8)(x)

        feature = Dense(self.feature_dim)(x)

        return Model([noise, label], feature, name='generator')

    def build_discriminator(self):
        """构建判别器"""
        feature = Input(shape=self.feature_shape)
        
        x = Dense(512)(feature)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        x = Dense(256)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        x = Dense(128)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        
        validity = Dense(1)(x)
        
        return Model(feature, validity, name='discriminator')

    def build_classifier(self):
        """构建分类器"""
        feature = Input(shape=self.feature_shape)
        
        x = Dense(256)(feature)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        x = Dense(128)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        
        hidden_output = Dense(64, activation='relu')(x)
        attribute_output = Dense(self.attribute_dim, activation='sigmoid')(hidden_output)  # 修改为属性维度

        return Model(feature, [hidden_output, attribute_output], name='classifier')
    
    def triplet_loss(self, anchor, positive, negative, margin=None):
        """Triplet损失函数"""
        if margin is None:
            margin = self.triplet_margin
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        """Wasserstein损失"""
        return tf.reduce_mean(y_true * y_pred)
    
    def center_loss(self, features, labels):
        """使用通用安全的Center Loss"""
        return safe_center_loss(features, labels, self.centers, self.seen_class_map)
    
    def center_loss_old(self, features, labels):
        """Center Loss实现 - 带安全检查"""
        if self.centers is None or self.seen_class_map is None:
            return tf.constant(0.0)

        # 转换标签为索引
        if hasattr(labels, 'numpy'):
            labels = labels.numpy()

        # 处理numpy数组格式的标签
        if len(labels.shape) > 1:
            labels = labels.flatten()

        # 🔥 安全过滤：只处理训练类别，跳过测试类别
        mapped_labels = []
        valid_features = []

        for i, label in enumerate(labels):
            if isinstance(label, np.ndarray):
                label = label.item()

            # 只处理在seen_class_map中的训练类别
            if int(label) in self.seen_class_map:
                mapped_labels.append(self.seen_class_map[int(label)])
                valid_features.append(features[i])

        # 如果没有有效的训练类别样本，返回0损失
        if len(mapped_labels) == 0:
            return tf.constant(0.0)

        mapped_labels = tf.constant(mapped_labels, dtype=tf.int32)
        valid_features = tf.stack(valid_features)

        # 获取当前批次的中心
        batch_centers = tf.gather(self.centers, mapped_labels)

        # 计算特征与对应中心的距离
        center_loss = tf.reduce_mean(tf.reduce_sum(tf.square(valid_features - batch_centers), axis=1))

        return center_loss
    
    def cycle_rank_loss(self, anchor, positive, negative):
        """增强的循环排序损失 (来自SmartCRL)"""
        # 基础triplet损失
        basic_loss = self.triplet_loss(anchor, positive, negative)
        
        # 🔥 语义一致性约束 (适度权重)
        semantic_consistency_loss = tf.reduce_mean(tf.square(anchor - positive))
        
        # 🔥 语义边际约束
        semantic_margin_loss = tf.maximum(0.0, 
            self.semantic_margin - tf.reduce_mean(tf.square(anchor - negative)))
        
        # 组合损失 (使用适度的语义权重)
        total_crl_loss = (basic_loss + 
                         self.lambda_semantic_consistency * semantic_consistency_loss +
                         self.lambda_semantic_consistency * semantic_margin_loss)
        
        return total_crl_loss
    
    def enhanced_semantic_loss(self, generated_features, target_attributes, predicted_attributes):
        """增强的语义损失 (来自SmartCRL，适度权重)"""
        # 确保数据类型一致
        generated_features = tf.cast(generated_features, tf.float32)
        target_attributes = tf.cast(target_attributes, tf.float32)
        predicted_attributes = tf.cast(predicted_attributes, tf.float32)

        # 基础属性预测损失
        attribute_loss = tf.reduce_mean(tf.square(predicted_attributes - target_attributes))

        # 🔥 语义区分度损失 (适度权重)
        batch_size = tf.shape(generated_features)[0]

        # 计算特征间的相似度矩阵
        normalized_features = tf.nn.l2_normalize(generated_features, axis=1)
        similarity_matrix = tf.matmul(normalized_features, normalized_features, transpose_b=True)

        # 计算属性间的相似度矩阵
        normalized_attributes = tf.nn.l2_normalize(target_attributes, axis=1)
        attribute_similarity = tf.matmul(normalized_attributes, normalized_attributes, transpose_b=True)

        # 语义一致性损失：特征相似度应该与属性相似度一致
        semantic_consistency = tf.reduce_mean(tf.square(similarity_matrix - attribute_similarity))

        return attribute_loss + tf.cast(self.lambda_semantic_consistency, tf.float32) * semantic_consistency

    def mi_penalty_loss(self, x, z):
        """互信息惩罚损失 (修复版本)"""
        # 将输入展平以确保维度一致
        x_flat = tf.reshape(x, [tf.shape(x)[0], -1])
        z_flat = tf.reshape(z, [tf.shape(z)[0], -1])

        # 确保维度匹配
        min_dim = tf.minimum(tf.shape(x_flat)[1], tf.shape(z_flat)[1])
        x_flat = x_flat[:, :min_dim]
        z_flat = z_flat[:, :min_dim]

        # 计算相关性
        x_mean = tf.reduce_mean(x_flat, axis=0, keepdims=True)
        z_mean = tf.reduce_mean(z_flat, axis=0, keepdims=True)

        x_centered = x_flat - x_mean
        z_centered = z_flat - z_mean

        # 使用相关性作为互信息的近似
        correlation = tf.reduce_mean(x_centered * z_centered)
        return tf.abs(correlation)

    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        """分类损失"""
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)

        mi_penalty = 0
        if self.bound == True:
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)

        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss

    def update_centers(self, features, labels):
        """使用通用安全的中心更新"""
        safe_update_centers(features, labels, self.centers, self.seen_class_map, self.center_optimizer)
    
    def update_centers_old(self, features, labels):
        """更新类别中心"""
        if self.centers is None or self.seen_class_map is None:
            return

        # 过滤出训练类别的样本
        valid_indices = []
        valid_labels = []

        for i, label in enumerate(labels):
            if isinstance(label, np.ndarray):
                label = label.item()

            # 只处理训练类别
            if int(label) in self.seen_class_map:
                valid_indices.append(i)
                valid_labels.append(self.seen_class_map[int(label)])

        if len(valid_indices) == 0:
            return

        valid_features = features[valid_indices]
        valid_labels = tf.constant(valid_labels, dtype=tf.int32)
        unique_labels = tf.unique(valid_labels)[0]

        for label in unique_labels:
            mask = tf.equal(valid_labels, label)
            if tf.reduce_sum(tf.cast(mask, tf.int32)) > 0:
                label_features = tf.boolean_mask(valid_features, mask)
                center_update = tf.reduce_mean(label_features, axis=0)

                # 使用梯度下降更新中心
                with tf.GradientTape() as tape:
                    current_center = tf.gather(self.centers, label)
                    center_loss = tf.reduce_mean(tf.square(current_center - center_update))

                center_grad = tape.gradient(center_loss, [self.centers])
                if center_grad[0] is not None:
                    self.center_optimizer.apply_gradients([(center_grad[0], self.centers)])

    def train(self, epochs=2000, batch_size=256, log_file=None, test_classes=None, group_name=None):
        """🔥 通用训练函数 - 支持任意组别配置

        Args:
            epochs: 训练轮数
            batch_size: 批次大小
            log_file: 日志文件路径
            test_classes: 测试类别列表，如 [8, 11, 12] 为C组
            group_name: 组别名称，用于日志显示
        """
        start_time = datetime.datetime.now()

        # 🔥 预定义组别配置（可扩展）
        GROUP_CONFIGS = {
             'A': [1, 6, 14],   # 测试类别: [1, 6, 14] 
             'B': [4, 7, 10],   # 测试类别: [4, 7, 10]
             'C': [8, 11, 12],  # 测试类别: [8, 11, 12]
             'D': [2, 3, 5],    # 测试类别: [2, 3, 5]
             'E': [9, 13, 15],  # 测试类别: [9, 13, 15]
        }

        # 🔥 智能推断组别和测试类别
        if test_classes is None and group_name is None:
            # 从文件名自动推断组别
            import inspect
            current_file = inspect.getfile(self.__class__)
            if "_A_" in current_file:
                group_name = "A"
            elif "_B_" in current_file:
                group_name = "B"
            elif "_C_" in current_file:
                group_name = "C"
            elif "_D_" in current_file:
                group_name = "D"
            else:
                # 默认使用C组配置
                group_name = "C"
                print("⚠️ 无法从文件名推断组别，使用默认C组配置")

        # 确定测试类别
        if test_classes is None:
            if group_name and group_name.upper() in GROUP_CONFIGS:
                test_classes = GROUP_CONFIGS[group_name.upper()]
                print(f"✅ 使用预定义{group_name.upper()}组配置: {test_classes}")
            else:
                raise ValueError(f"❌ 未知组别 '{group_name}'，请提供test_classes参数或使用预定义组别: {list(GROUP_CONFIGS.keys())}")
        else:
            if group_name is None:
                group_name = "Custom"
            print(f"✅ 使用自定义测试类别: {test_classes}")

        print(f"🔥 开始{group_name.upper()}组实验训练...")
        print(f"📅 开始时间: {start_time}")
        print(f"🎯 测试类别: {test_classes} ({group_name.upper()}组)")
        print(f"⚡ 融合权重: triplet={self.lambda_triplet}, center={self.lambda_center}, crl={self.lambda_crl}, semantic={self.lambda_semantic_consistency}")

        # 🔥 使用预处理的.npz文件
        PATH_train = '/app/data/dataset_train_case1.npz'
        PATH_test = '/app/data/dataset_test_case1.npz'

        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)

        # 🔥 动态计算训练类别
        all_classes = list(range(1, 16))  # [1, 2, 3, ..., 15]
        train_classes = [c for c in all_classes if c not in test_classes]
        
        # 组合训练数据
        train_x_list = []
        train_y_list = []
        for class_id in train_classes:
            train_x_list.append(train_data[f'training_samples_{class_id}'])
            train_y_list.append(train_data[f'training_attribute_{class_id}'])
        
        traindata = np.vstack(train_x_list)
        train_attributelabel = np.vstack(train_y_list)
        
        # 生成训练类别标签
        train_classlabel = []
        for class_id in train_classes:
            class_samples = train_data[f'training_samples_{class_id}']
            train_classlabel.extend([class_id] * len(class_samples))
        train_classlabel = np.array(train_classlabel)
        
        # 组合测试数据
        test_x_list = []
        test_y_list = []
        for class_id in test_classes:
            test_x_list.append(test_data[f'testing_samples_{class_id}'])
            test_y_list.append(test_data[f'testing_attribute_{class_id}'])
        
        testdata = np.vstack(test_x_list)
        test_attributelabel = np.vstack(test_y_list)
        
        # 生成测试类别标签
        test_classlabel = []
        for class_id in test_classes:
            class_samples = test_data[f'testing_samples_{class_id}']
            test_classlabel.extend([class_id] * len(class_samples))
        test_classlabel = np.array(test_classlabel)
        
        print(f"📊 训练数据: {traindata.shape}")
        print(f"📊 测试数据: {testdata.shape}")
        print(f"📊 训练类别: {sorted(np.unique(train_classlabel))}")
        print(f"📊 测试类别: {sorted(np.unique(test_classlabel))}")

        # 🔍 数据验证：检查训练数据中是否包含测试类别（动态验证）
        train_unique_classes = np.unique(train_classlabel)
        test_classes_in_train = [c for c in test_classes if c in train_unique_classes]

        if test_classes_in_train:
            print(f"🚨 严重错误：训练数据中发现测试类别 {test_classes_in_train}")
            print(f"📊 训练数据中的所有类别: {sorted(train_unique_classes)}")
            print(f"📊 当前测试类别: {test_classes}")
            print(f"❌ 这违反了零样本学习原则，需要修复数据加载逻辑")
            return
        else:
            print(f"✅ 数据验证通过：训练数据中不包含测试类别")
            print(f"📊 训练数据中的类别: {sorted(train_unique_classes)}")
            print(f"📊 测试类别: {test_classes}")

        # 设置日志文件
        if log_file is None:
            log_file = f"logs/{group_name}_Hybrid_{start_time.strftime('%Y%m%d_%H%M%S')}.log"

        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # 🔥 动态创建seen_class_map（通用解决方案）
        seen_classes = train_classes  # 训练类别就是seen classes

        # 创建正确的seen_class_map
        self.seen_class_map = {class_id: idx for idx, class_id in enumerate(seen_classes)}
        num_seen_classes = len(seen_classes)

        print(f"🔧 {group_name.upper()}组配置验证:")
        print(f"   测试类别: {test_classes}")
        print(f"   训练类别: {seen_classes}")
        print(f"   seen_class_map: {self.seen_class_map}")
        print(f"   训练类别数量: {num_seen_classes}")

        # 初始化类中心 - 使用小的标准差避免损失爆炸
        self.centers = tf.Variable(tf.random.normal([num_seen_classes, self.feature_dim], stddev=0.01), trainable=True)

        # 🔥 动态初始化TensorBoard监控器
        if AdvancedTensorBoardMonitor is not None:
            tensorboard_log_dir = f"./tensorboard_logs/{group_name}_Hybrid_realtime"
            self.tensorboard_monitor = AdvancedTensorBoardMonitor(tensorboard_log_dir)
            print(f"✅ TensorBoard监控器已初始化: {tensorboard_log_dir}")
        else:
            print("⚠️ TensorBoard监控器不可用，跳过可视化功能")

        # 准备训练数据
        train_x = traindata.astype(np.float32)
        train_y = train_attributelabel.astype(np.float32)

        # 计算总批次数
        num_batches = len(train_x) // batch_size

        # 设置属性矩阵 - 修复路径问题
        excel_paths = [
            '/home/<USER>/hmt/ACGAN-FG-main/data/attribute_matrix.xlsx',  # 你提到的正确路径
            'data/attribute_matrix.xlsx',
            '/app/data/attribute_matrix.xlsx'
        ]
        
        loaded = False
        for path in excel_paths:
            try:
                attribute_matrix_df = pd.read_excel(path, index_col=0)
                self.attribute_matrix = attribute_matrix_df.values.astype(np.float32)
                print(f"✅ 从Excel加载属性矩阵: {path} -> {self.attribute_matrix.shape}")
                loaded = True
                break
            except Exception as e:
                print(f"⚠️ 尝试路径 {path} 失败: {e}")
        
        if not loaded:
            print("🔄 使用训练属性标签作为备选")
            self.attribute_matrix = train_attributelabel.astype(np.float32)
        
        print(f"📊 最终属性矩阵形状: {self.attribute_matrix.shape}")

        # 训练循环
        for epoch in range(epochs):
            epoch_start_time = datetime.datetime.now()
            
            # 批次训练循环
            for batch_i in range(num_batches):
                # 随机选择批次
                idx = np.random.randint(0, train_x.shape[0], batch_size)
                train_x_batch = train_x[idx]
                train_y_batch = train_y[idx]
                train_labels_batch = train_classlabel[idx]  # 直接使用原始标签，不转换

                # ==========================================
                # 训练自编码器和分类器
                # ==========================================
                with tf.GradientTape(persistent=True) as tape_auto_c:
                    feature = self.encoder(train_x_batch)
                    output_sample = self.decoder(feature)
                    autoencoder_loss = mean_squared_error(train_x_batch, output_sample)

                    hidden_output_c, predict_attribute_c = self.classifier(feature)
                    c_loss = self.classification_loss(feature, train_y_batch, hidden_output_c, predict_attribute_c)

                    # 🔥 新增：适度的语义一致性损失
                    semantic_loss = self.enhanced_semantic_loss(feature, train_y_batch, predict_attribute_c)

                    total_ac_loss = autoencoder_loss + c_loss + self.lambda_semantic_consistency * semantic_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss,
                                                   self.autoencoder.trainable_weights + self.classifier.trainable_weights)
                self.optimizer_C.apply_gradients(zip(grads_auto_c,
                                                   self.autoencoder.trainable_weights + self.classifier.trainable_weights))
                del tape_auto_c

                # ==========================================
                # 训练度量学习 (HardTriplet强权重)
                # ==========================================
                anchor_samples = []
                positive_samples = []
                negative_samples = []

                for i, idx_i in enumerate(idx):
                    anchor_samples.append(train_x_batch[i])

                    # 获取当前样本的标签
                    label = train_classlabel[idx_i]
                    if isinstance(label, np.ndarray):
                        label = label.item()

                    # 正样本：同类别的其他样本
                    same_class_indices = []
                    for j, l in enumerate(train_classlabel):
                        if isinstance(l, np.ndarray):
                            l = l.item()
                        if l == label:
                            same_class_indices.append(j)

                    if len(same_class_indices) > 1:
                        # 排除当前样本
                        same_class_indices = [j for j in same_class_indices if j != idx_i]
                        pos_idx = np.random.choice(same_class_indices)
                        positive_samples.append(traindata[pos_idx])
                    else:
                        positive_samples.append(train_x_batch[i])  # 如果没有其他同类样本，使用自己

                    # 负样本：不同类别的样本
                    diff_class_indices = []
                    for j, l in enumerate(train_classlabel):
                        if isinstance(l, np.ndarray):
                            l = l.item()
                        if l != label:
                            diff_class_indices.append(j)

                    if len(diff_class_indices) > 0:
                        neg_idx = np.random.choice(diff_class_indices)
                        negative_samples.append(traindata[neg_idx])
                    else:
                        negative_samples.append(train_x_batch[i])  # 如果没有不同类样本，使用自己

                anchor_samples = np.array(anchor_samples)
                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape(persistent=True) as tape_m:
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)

                    # 🔥 强化的Triplet Loss (保持HardTriplet权重)
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                    # 🔥 强化的Center Loss (保持HardTriplet权重)
                    c_loss = self.center_loss(anchor_features, train_labels_batch)

                    # 组合损失 - 使用HardTriplet强化权重
                    total_metric_loss = self.lambda_triplet * m_loss + self.lambda_center * c_loss

                grads_m = tape_m.gradient(total_metric_loss, self.encoder.trainable_weights)
                self.optimizer_M.apply_gradients(zip(grads_m, self.encoder.trainable_weights))
                del tape_m

                # 更新中心 - 添加安全检查
                # 转换标签为映射索引，只处理训练类别
                mapped_labels = []
                valid_features = []
                for i, label in enumerate(train_labels_batch):
                    if isinstance(label, np.ndarray):
                        label = label.item()

                    # 🔥 安全检查：只处理在seen_class_map中的训练类别
                    if self.seen_class_map is not None and int(label) in self.seen_class_map:
                        mapped_labels.append(self.seen_class_map[int(label)])
                        valid_features.append(anchor_features[i])

                if len(mapped_labels) > 0:
                    valid_features_array = tf.stack(valid_features).numpy()
                    self.update_centers(valid_features_array, np.array(mapped_labels))

                # ==========================================
                # 训练判别器 (Train Discriminator) - 强化版
                # ==========================================
                # 循环 n_critic 次，让判别器变得更强
                d_loss_total = 0.0
                for critic_iter in range(self.n_critic):
                    # 重新采样噪声和数据，确保每次更新都是独立的
                    noise_d = np.random.normal(0, 1, (batch_size, self.latent_dim))
                    idx_d = np.random.randint(0, train_x_batch.shape[0], batch_size)
                    train_x_batch_d = train_x_batch[idx_d] if train_x_batch.shape[0] >= batch_size else train_x_batch
                    train_y_batch_d = train_y_batch[idx_d] if train_y_batch.shape[0] >= batch_size else train_y_batch

                    with tf.GradientTape() as tape_d:
                        real_features = self.encoder(train_x_batch_d)
                        fake_features = self.generator([noise_d, train_y_batch_d])

                        real_validity = self.discriminator(real_features)
                        fake_validity = self.discriminator(fake_features)

                        d_loss_real = tf.reduce_mean(real_validity)
                        d_loss_fake = tf.reduce_mean(fake_validity)
                        d_loss = -d_loss_real + d_loss_fake

                    grads_d = tape_d.gradient(d_loss, self.discriminator.trainable_weights)
                    self.optimizer_D.apply_gradients(zip(grads_d, self.discriminator.trainable_weights))

                    d_loss_total += d_loss.numpy()

                # 计算平均判别器损失
                d_loss = d_loss_total / self.n_critic

                # ==========================================
                # 训练生成器 (融合策略)
                # ==========================================
                noise_g = np.random.normal(0, 1, (batch_size, self.latent_dim))

                with tf.GradientTape(persistent=True) as tape_g:
                    Fake_feature_g = self.generator([noise_g, train_y_batch])
                    Fake_validity_g = self.discriminator(Fake_feature_g)

                    # 对抗损失
                    adversarial_loss = -tf.reduce_mean(Fake_validity_g)

                    # 分类损失
                    hidden_output_g, Fake_classification_g = self.classifier(Fake_feature_g)
                    classification_loss = self.classification_loss(Fake_feature_g, train_y_batch, hidden_output_g, Fake_classification_g)

                    # 生成特征的Triplet损失
                    g_anchor_features = self.encoder(train_x_batch)
                    triplet_loss_g = self.triplet_loss(g_anchor_features, Fake_feature_g, negative_features)

                    # Generator的Center loss
                    center_loss_g = self.center_loss(g_anchor_features, train_labels_batch)

                    # 🔥 适度的CRL loss (SmartCRL策略)
                    cycle_rank_loss_val = 0.0
                    if self.crl:
                        reconstructed_feature = self.generator([noise_g, Fake_classification_g])
                        shuffled_indices = tf.random.shuffle(tf.range(batch_size))
                        unsimilar_attributes = tf.gather(train_y_batch, shuffled_indices)
                        unsimilar_fake_feature = self.generator([noise_g, unsimilar_attributes])

                        # 使用增强的CRL损失 (适度权重)
                        cycle_rank_loss_val = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_fake_feature)

                    # 🔥 适度的语义增强损失
                    semantic_gen_loss = self.enhanced_semantic_loss(Fake_feature_g, train_y_batch, Fake_classification_g)

                    # 🔥 融合策略总损失
                    total_loss = (adversarial_loss +
                                 self.lambda_cla * classification_loss +
                                 self.lambda_triplet * triplet_loss_g +
                                 self.lambda_center * center_loss_g +
                                 self.lambda_crl * cycle_rank_loss_val +
                                 self.lambda_semantic_consistency * semantic_gen_loss)

                grads_g = tape_g.gradient(total_loss, self.generator.trainable_weights)
                self.optimizer_G.apply_gradients(zip(grads_g, self.generator.trainable_weights))
                del tape_g

                # ==========================================
                # 标准格式日志输出 (每个batch) - 显示完整损失信息
                # ==========================================
                elapsed_time = datetime.datetime.now() - epoch_start_time

                print(f"[Epoch {epoch+1}/{epochs}][Batch {batch_i+1}/{num_batches}]"
                      f"[AE+C loss: {float(tf.reduce_mean(total_ac_loss)):.6f}]"
                      f"[强化Triplet: {float(tf.reduce_mean(self.lambda_triplet * m_loss)):.3f}]"
                      f"[强化Center: {float(tf.reduce_mean(self.lambda_center * c_loss)):.3f}]"
                      f"[组合强化: {float(tf.reduce_mean(total_metric_loss)):.3f}]"
                      f"[适度CRL: {float(tf.reduce_mean(self.lambda_crl * cycle_rank_loss_val)):.3f}]"
                      f"[语义增强: {float(tf.reduce_mean(self.lambda_semantic_consistency * semantic_gen_loss)):.3f}]"
                      f"[D loss: {float(tf.reduce_mean(d_loss)):.6f}]"
                      f"[G loss: {float(tf.reduce_mean(total_loss)):.6f}]"
                      f"time: {elapsed_time}")

            # ==========================================
            # 每轮输出训练状态
            # ==========================================
            if epoch % 1 == 0:
                # 🔥 动态零样本学习评估方法（使用当前组别的测试类别）
                lsvm_acc, nrf_acc, pnb_acc, mlp_acc = feature_generation_and_diagnosis(
                    2000, testdata, test_attributelabel, self.autoencoder, self.generator, self.classifier, test_classes)
                
                # 转换为百分比
                lsvm_acc *= 100
                nrf_acc *= 100
                pnb_acc *= 100
                mlp_acc *= 100

                # 更新最佳准确率
                if lsvm_acc > self.best_accuracy['lsvm']:
                    self.best_accuracy['lsvm'] = lsvm_acc
                if nrf_acc > self.best_accuracy['nrf']:
                    self.best_accuracy['nrf'] = nrf_acc
                if pnb_acc > self.best_accuracy['pnb']:
                    self.best_accuracy['pnb'] = pnb_acc
                if mlp_acc > self.best_accuracy['mlp']:
                    self.best_accuracy['mlp'] = mlp_acc

                # 🔥 添加实时监控数据记录
                self.training_data['epochs'].append(epoch)
                self.training_data['ae_c_loss'].append(float(tf.reduce_mean(autoencoder_loss + c_loss)))
                self.training_data['g_loss'].append(float(tf.reduce_mean(total_loss)))
                self.training_data['d_loss'].append(float(tf.reduce_mean(d_loss)))
                self.training_data['triplet_loss'].append(float(tf.reduce_mean(self.lambda_triplet * m_loss)))
                self.training_data['center_loss'].append(float(tf.reduce_mean(self.lambda_center * c_loss)))
                self.training_data['combined_loss'].append(float(tf.reduce_mean(total_metric_loss)))
                self.training_data['crl_loss'].append(float(tf.reduce_mean(self.lambda_crl * cycle_rank_loss_val)))
                self.training_data['semantic_loss'].append(float(tf.reduce_mean(self.lambda_semantic_consistency * semantic_gen_loss)))
                self.training_data['lsvm_acc'].append(lsvm_acc)
                self.training_data['rf_acc'].append(nrf_acc)
                self.training_data['nb_acc'].append(pnb_acc)
                self.training_data['mlp_acc'].append(mlp_acc)

                # 🔥 每10个epoch更新一次TensorBoard
                if epoch % 10 == 0 and self.tensorboard_monitor is not None:
                    try:
                        self.tensorboard_monitor.log_loss_analysis(self.training_data)
                        self.tensorboard_monitor.log_custom_visualizations(self.training_data)
                        print(f"📊 TensorBoard已更新到epoch {epoch}")
                    except Exception as e:
                        print(f"⚠️ TensorBoard更新失败: {e}")

                # 输出训练状态
                current_time = datetime.datetime.now()
                elapsed_time = current_time - start_time

                log_message = (
                    f"Epoch {epoch:4d}/{epochs} | Time: {elapsed_time}\n"
                    f"AE+C loss: {float(tf.reduce_mean(autoencoder_loss + c_loss)):.0f} | "
                    f"强化Triplet: {float(tf.reduce_mean(self.lambda_triplet * m_loss)):.3f} | "
                    f"强化Center: {float(tf.reduce_mean(self.lambda_center * c_loss)):.0f} | "
                    f"组合强化: {float(tf.reduce_mean(total_metric_loss)):.0f} | "
                    f"适度CRL: {float(tf.reduce_mean(self.lambda_crl * cycle_rank_loss_val)):.3f} | "
                    f"语义增强: {float(tf.reduce_mean(self.lambda_semantic_consistency * semantic_gen_loss)):.3f} | "
                    f"D loss: {float(tf.reduce_mean(d_loss)):.3f} | "
                    f"G loss: {float(tf.reduce_mean(total_loss)):.0f}\n"
                    f"LinearSVM: {lsvm_acc:.2f}% | "
                    f"RandomForest: {nrf_acc:.2f}% | "
                    f"GaussianNB: {pnb_acc:.2f}% | "
                    f"MLPClassifier: {mlp_acc:.2f}%\n"
                    f"Best: LinearSVM {self.best_accuracy['lsvm']:.2f}%, "
                    f"RandomForest {self.best_accuracy['nrf']:.2f}%, "
                    f"GaussianNB {self.best_accuracy['pnb']:.2f}%, "
                    f"MLP {self.best_accuracy['mlp']:.2f}%"
                )

                print(log_message)
                print()  # 添加空行分隔

                # 写入日志文件
                with open(log_file, 'a', encoding='utf-8') as f:
                    f.write(log_message + '\n')
                    f.write('\n')  # 添加空行分隔

        # 训练完成
        end_time = datetime.datetime.now()
        total_time = end_time - start_time

        final_message = (
            f"\n🎉 Hybrid实验训练完成!\n"
            f"📅 结束时间: {end_time}\n"
            f"⏱️  总耗时: {total_time}\n"
            f"🏆 最终最佳准确率:\n"
            f"   LinearSVM: {self.best_accuracy['lsvm']:.2f}%\n"
            f"   RandomForest: {self.best_accuracy['nrf']:.2f}%\n"
            f"   GaussianNB: {self.best_accuracy['pnb']:.2f}%\n"
            f"   MLPClassifier: {self.best_accuracy['mlp']:.2f}%\n"
            f"📊 平均准确率: {np.mean(list(self.best_accuracy.values())):.2f}%\n"
            f"📝 日志文件: {log_file}"
        )

        print(final_message)

        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(final_message + '\n')

        # 🔥 关闭TensorBoard监控器
        if self.tensorboard_monitor is not None:
            try:
                self.tensorboard_monitor.close()
                tensorboard_log_dir = f"./tensorboard_logs/{group_name}_Hybrid_realtime"
                print(f"📊 TensorBoard日志已保存到: {tensorboard_log_dir}")
                print(f"🌐 启动命令: tensorboard --logdir=./tensorboard_logs")
                print(f"🔗 访问地址: http://localhost:6006")
            except Exception as e:
                print(f"⚠️ TensorBoard关闭失败: {e}")
        else:
            print("⚠️ TensorBoard监控器未初始化，无需关闭")

if __name__ == "__main__":
    print("🔥 通用Hybrid实验: 融合HardTriplet强分离能力与SmartCRL稳定性")
    print("🎯 目标: 保持高准确率同时提升训练稳定性")
    print("⚡ 策略: HardTriplet强权重 + SmartCRL适度约束")
    print("🔧 支持任意组别配置: A, B, C, D, E 或自定义测试类别")

    model = Zero_shot()

    # 创建日志目录
    os.makedirs('logs', exist_ok=True)

    # 🔥 示例用法：
    # 自动推断组别: model.train(epochs=2000, batch_size=256)  # 从文件名推断
    # A组: model.train(epochs=2000, batch_size=256, group_name="A")
    # B组: model.train(epochs=2000, batch_size=256, group_name="B")
    # C组: model.train(epochs=2000, batch_size=256, group_name="C")
    # D组: model.train(epochs=2000, batch_size=256, group_name="D")
    # 自定义: model.train(epochs=2000, batch_size=256, test_classes=[1, 2, 3], group_name="Custom")

    # 🔥 E-UltraEnhanced训练 - 针对E组零相似度问题的超强化实验
    model.train(epochs=2000, batch_size=256, group_name="C")

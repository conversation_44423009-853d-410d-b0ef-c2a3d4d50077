#!/usr/bin/env python3
"""
优化后的ASDCGAN快速测试脚本

修复内容：
1. 🔥 降低循环损失权重：10.0 → 1.0
2. 🔥 降低语义损失权重：5.0 → 0.5  
3. 🔥 提高学习率：G 0.0001 → 0.0002, D 0.0002 → 0.0004
4. 🔥 增加训练轮次：100 → 1000
5. 🔥 优化其他损失权重平衡

预期改进：
- A组准确率：60.97% → 75-85%
- B组准确率：39.17% → 60-70%
- 训练稳定性显著改善
"""

import os
import sys
import torch
import argparse
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')

from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer


def main():
    parser = argparse.ArgumentParser(description='优化后的ASDCGAN训练')
    parser.add_argument('--group', type=str, choices=['A', 'B', 'C', 'D', 'E'], 
                       default='A', help='数据分组选择')
    parser.add_argument('--epochs', type=int, default=1000, 
                       help='训练轮次 (默认1000)')
    parser.add_argument('--batch_size', type=int, default=32, 
                       help='批次大小')
    parser.add_argument('--device', type=str, default='cuda', 
                       help='设备选择')
    parser.add_argument('--quick_test', action='store_true', 
                       help='快速测试模式 (50 epochs)')
    
    args = parser.parse_args()
    
    # 快速测试模式
    if args.quick_test:
        args.epochs = 50
        print("🚀 快速测试模式：50 epochs")
    
    print(f"""
🔥 优化后的ASDCGAN训练启动
=====================================
📊 数据分组: {args.group}
🔄 训练轮次: {args.epochs}
📦 批次大小: {args.batch_size}
💻 设备: {args.device}
⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 优化内容:
- 循环损失权重: 10.0 → 1.0
- 语义损失权重: 5.0 → 0.5
- 生成器学习率: 0.0001 → 0.0002
- 判别器学习率: 0.0002 → 0.0004
- 不确定性权重: 1.0 → 0.5
=====================================
    """)
    
    try:
        # 初始化训练器 (使用优化后的参数)
        trainer = EnhancedASDCGANTrainer(
            device=args.device,
            batch_size=args.batch_size,
            learning_rate_g=0.0002,  # 🔥 优化后的学习率
            learning_rate_d=0.0004   # 🔥 优化后的学习率
        )
        
        # 验证优化后的权重
        print(f"""
✅ 损失权重验证:
- 对抗损失权重: {trainer.adversarial_weight}
- 循环损失权重: {trainer.cycle_consistency_weight} (优化: 10.0→1.0)
- 语义损失权重: {trainer.semantic_distance_weight} (优化: 5.0→0.5)
- 不确定性权重: {trainer.uncertainty_weight} (优化: 1.0→0.5)
- 域选择权重: {trainer.domain_selection_weight} (优化: 2.0→1.0)
        """)
        
        # 加载数据
        print(f"📊 加载TEP数据集 (分组 {args.group})...")
        trainer.load_data(split_group=args.group)

        # 开始训练
        print(f"🚀 开始训练分组 {args.group}...")
        trainer.train_enhanced(epochs=args.epochs)

        # 获取结果
        results = {
            'best_accuracy': trainer.history.get('best_accuracy', [0])[-1] if trainer.history.get('best_accuracy') else 0,
            'final_g_loss': trainer.history.get('g_loss', [0])[-1] if trainer.history.get('g_loss') else 0,
            'final_d_loss': trainer.history.get('d_loss', [0])[-1] if trainer.history.get('d_loss') else 0,
            'final_cycle_loss': trainer.history.get('cycle_loss', [0])[-1] if trainer.history.get('cycle_loss') else 0,
            'model_path': getattr(trainer, 'experiment_dir', 'N/A'),
            'log_path': getattr(trainer, 'log_file', 'N/A'),
            'tensorboard_path': getattr(trainer, 'tensorboard_dir', 'N/A')
        }
        
        # 输出结果
        print(f"""
🎉 训练完成！
=====================================
📊 最终结果:
- 最佳准确率: {results.get('best_accuracy', 'N/A')}%
- 最终生成器损失: {results.get('final_g_loss', 'N/A'):.4f}
- 最终判别器损失: {results.get('final_d_loss', 'N/A'):.4f}
- 最终循环损失: {results.get('final_cycle_loss', 'N/A'):.4f}

📈 预期改进对比:
- A组: 60.97% → 目标75-85%
- B组: 39.17% → 目标60-70%

📁 结果保存位置:
- 模型: {results.get('model_path', 'N/A')}
- 日志: {results.get('log_path', 'N/A')}
- TensorBoard: {results.get('tensorboard_path', 'N/A')}
=====================================
        """)
        
        # 性能分析
        if results.get('best_accuracy', 0) > 70:
            print("🎯 优秀！准确率超过70%，优化效果显著！")
        elif results.get('best_accuracy', 0) > 60:
            print("✅ 良好！准确率有所提升，继续优化中...")
        else:
            print("⚠️  需要进一步调优，建议检查数据处理或模型架构")
            
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        print("💡 建议检查:")
        print("1. GPU内存是否充足")
        print("2. 数据文件是否存在")
        print("3. 依赖包是否正确安装")
        raise


if __name__ == "__main__":
    main()

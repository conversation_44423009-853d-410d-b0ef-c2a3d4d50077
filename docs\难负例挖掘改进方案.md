# Triplet Loss 难负例挖掘改进方案

## 问题分析

当前 `acgan_triplet.py` 在 E 组 (类别 [6, 9, 13]) 上表现最差，主要原因：
- **随机负采样问题**：可能选择到很容易区分的负样本
- **学习效率低**：没有充分利用属性空间的几何结构
- **细粒度区分能力不足**：对相似类别的判别能力较弱

## 改进策略：难负例挖掘 (Hard Negative Mining)

### 核心思想
选择在**属性空间中最接近但类别不同**的样本作为负例，强迫模型学习细粒度的类别区别。

### 技术实现

#### 1. 属性相似度计算
```python
def compute_attribute_similarity(self, anchor_attr, all_attributes):
    """使用余弦相似度计算属性间距离"""
    anchor_attr = tf.nn.l2_normalize(anchor_attr, axis=-1)
    all_attributes = tf.nn.l2_normalize(all_attributes, axis=-1)
    similarities = tf.linalg.matmul(anchor_attr, all_attributes, transpose_b=True)
    return similarities
```

#### 2. 难负例采样策略
```python
def hard_negative_sampling(self, anchor_labels, all_attributes):
    """智能负采样：选择属性最相似但类别不同的负样本"""
    # 1. 计算anchor与所有类别的属性相似度
    # 2. 排除自身类别
    # 3. 选择前K个最相似的候选类别
    # 4. 从候选中随机选择负样本
```

#### 3. 混合采样策略
- **70% 难负例** + **30% 随机负例**
- **动态调整**：训练早期更多随机，后期更多难负例
- **避免过拟合**：保持一定的随机性

### 关键参数

```python
self.hard_negative_ratio = 0.7    # 难负例占比
self.top_k_candidates = 5         # 选择前K个最相似属性作为候选
self.use_hard_negative = True     # 是否启用难负例挖掘
```

### 预期效果

#### 1. 理论收益
- **提高细粒度判别能力**：强迫模型区分相似类别
- **更好利用属性信息**：基于属性几何结构的智能采样
- **加速收敛**：更有挑战性的训练样本

#### 2. 具体改进点
- **工业故障诊断场景**：区分"冷凝器故障"vs"反应器故障"等相似故障
- **属性驱动学习**：充分利用故障特征的语义信息
- **零样本泛化**：提高对未见类别的判别能力

## 实验设计

### 测试配置
- **目标组别**：Group E [6, 9, 13] （当前表现最差）
- **对比基准**：原始随机负采样策略
- **评估指标**：LSVM, NRF, PNB, MLP 四种分类器准确率

### 实现细节
1. **文件名**：`acgan_triplet_hard_negative.py`
2. **日志格式**：`{timestamp}_triplet_hard_negative_GroupE.md`
3. **训练轮数**：2000 epochs
4. **批量大小**：64

## 符合零样本学习条件验证

✅ **不违反零样本约束**：
- 仍然基于属性向量进行负采样
- 不需要测试类的训练样本
- 保持属性驱动的学习范式

✅ **理论基础扎实**：
- 对比学习中"困难样本优先"原则
- 属性空间几何结构的合理利用
- 细粒度判别学习的有效策略

## 潜在风险与缓解

### 风险
1. **训练难度增加**：可能需要调整学习率
2. **计算复杂度**：每次采样需要计算相似度
3. **过拟合风险**：过度关注相似类别

### 缓解策略
1. **混合采样**：保持30%随机负例
2. **动态调整**：训练初期使用更多随机样本
3. **正则化**：保持dropout和批归一化

## 预期结果

期望在 Group E 上实现：
- **LSVM准确率**：从当前基准提升 5-10%
- **整体稳定性**：减少训练波动
- **收敛速度**：更快达到最优性能

---

**创建时间**：2025-01-29  
**实验状态**：待运行  
**预期运行时间**：约2-3小时（2000 epochs） 
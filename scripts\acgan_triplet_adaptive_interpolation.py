import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
from pathlib import Path
import pandas as pd
from tensorflow.keras.losses import mean_squared_error
from test_interpolation import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler


# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    # 限制TensorFlow只使用第一个GPU
    tf.config.set_visible_devices(gpus[0], 'GPU')
    # 设置显存按需增长
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    # 显存增长必须在GPU初始化之前设置
    print(e)


def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = BatchNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = BatchNormalization()(y)
    
    # 如果输入和输出维度不同，需要一个线性投影
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        # input_shape is (batch_size, feature_dim)
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        # Reshape for matrix multiplication
        # Temporarily add a "sequence length" of 1
        # x_reshaped shape: (batch_size, 1, feature_dim)
        x_reshaped = K.expand_dims(x, axis=1)

        # Q, K, V projections
        q = self.query(x_reshaped)  # (batch_size, 1, feature_dim/8)
        k = self.key(x_reshaped)    # (batch_size, 1, feature_dim/8)
        v = self.value(x_reshaped)  # (batch_size, 1, feature_dim)

        # Attention scores
        attention_scores = K.batch_dot(q, k, axes=[2, 2]) # (batch_size, 1, 1)
        attention_probs = K.softmax(attention_scores)

        # Apply attention
        context = K.batch_dot(attention_probs, v) # (batch_size, 1, feature_dim)
        
        # Remove the temporary dimension
        context = K.squeeze(context, axis=1)

        # Add back to original input (residual connection)
        return x + self.gamma * context
        
class Zero_shot():
    def __init__(self):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        self.lambda_cla = 10 
        self.lambda_triplet = 10 
        self.lambda_crl = 0.01 
        
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        # 极保守的自适应特征空间插值参数
        self.use_interpolation = True
        self.adaptive_interpolation = True  # 启用自适应插值策略
        
        # 基础插值参数 - 大幅降低
        self.base_interpolation_ratio = 0.02   # 极低的基础插值比例（2%）
        self.max_interpolation_ratio = 0.08    # 大幅降低最大插值比例（8%）
        self.alpha_range = (0.45, 0.55)        # 极保守的插值系数范围
        
        # 自适应插值参数 - 更保守
        self.interpolation_warmup_epochs = 200  # 大幅延长插值预热期
        self.performance_threshold = 0.50       # 调整性能阈值
        self.interpolation_decay = 0.95         # 更快的衰减
        
        # 新增质量控制参数
        self.interpolation_quality_threshold = 0.7  # 插值样本质量阈值
        self.max_failed_attempts = 100  # 最大失败尝试次数
        self.quality_check_enabled = True  # 启用质量检查
        
        # 组别难度配置（基于历史性能数据的精确配置）
        self.group_difficulty = {
            'A': 'easy',     # A组历史最佳：0.867，插值有害
            'B': 'medium',   # B组历史最佳：0.465，中等难度
            'C': 'medium',   # C组历史最佳：0.531，中等难度  
            'D': 'easy',     # D组历史最佳：较好，插值可能有害
            'E': 'hard'      # E组历史最佳：0.511，最困难，需要插值
        }
        
        # 当前组别和动态参数
        self.current_group = None
        self.current_interpolation_ratio = self.base_interpolation_ratio
        self.recent_performance = []
        self.interpolation_enabled = True
        
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For triplet loss
        
        self.autoencoder= self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c= self.build_classifier()
    
    def set_group_config(self, group_name):
        """设置当前组别的自适应配置"""
        self.current_group = group_name
        difficulty = self.group_difficulty.get(group_name, 'medium')
        
        print(f"\n=== 自适应插值配置 ===")
        print(f"目标组别: Group {group_name}")
        print(f"难度评估: {difficulty.upper()}")
        
        if difficulty == 'easy':
            # 对于容易的组别（A、D），完全禁用插值
            self.interpolation_enabled = False
            self.use_interpolation = False
            print(f"策略: 完全禁用特征插值（历史性能良好）")
            
        elif difficulty == 'hard':
            # 对于困难的组别（E），使用极保守的插值策略
            self.interpolation_enabled = True
            self.use_interpolation = True
            self.current_interpolation_ratio = self.max_interpolation_ratio * 0.5  # 降低到4%
            self.alpha_range = (0.48, 0.52)  # 极保守的插值范围
            self.interpolation_warmup_epochs = 300  # 更长的预热期
            self.performance_threshold = 0.40      # 更低的阈值
            print(f"策略: 极保守插值 (比例: {self.current_interpolation_ratio:.3f}, 范围: {self.alpha_range})")
            
        else:  # medium
            # 对于中等难度的组别（B、C），使用微量插值策略
            self.interpolation_enabled = True
            self.use_interpolation = True
            self.current_interpolation_ratio = self.base_interpolation_ratio * 0.5  # 降低到1%
            self.alpha_range = (0.47, 0.53)  # 极保守的范围
            self.performance_threshold = 0.45
            self.interpolation_warmup_epochs = 250  # 延长预热期
            print(f"策略: 微量插值 (比例: {self.current_interpolation_ratio:.3f}, 范围: {self.alpha_range})")
        
        print(f"预热期: {self.interpolation_warmup_epochs} epochs")
        print("========================\n")
    
    def update_interpolation_strategy(self, current_epoch, recent_accuracy):
        """根据训练进展和性能动态更新插值策略"""
        if not self.interpolation_enabled:
            return
            
        self.recent_performance.append(recent_accuracy)
        
        # 只保留最近15个epoch的性能
        if len(self.recent_performance) > 15:
            self.recent_performance.pop(0)
        
        # 在预热期内，逐渐增加插值比例
        if current_epoch < self.interpolation_warmup_epochs:
            warmup_factor = current_epoch / self.interpolation_warmup_epochs
            self.current_interpolation_ratio = self.base_interpolation_ratio * warmup_factor
        
        # 根据最近性能调整插值策略
        if len(self.recent_performance) >= 8:
            recent_avg = np.mean(self.recent_performance[-5:])
            earlier_avg = np.mean(self.recent_performance[-10:-5]) if len(self.recent_performance) >= 10 else recent_avg
            
            # 性能改善检测
            performance_improving = recent_avg > earlier_avg
            performance_stable = abs(recent_avg - earlier_avg) < 0.01
            
            if recent_avg < self.performance_threshold:
                # 性能较差时，减少插值比例（与之前逻辑相反）
                if not performance_improving:
                    # 性能差且没有改善，大幅减少插值
                    self.current_interpolation_ratio *= 0.8
                    print(f"Performance poor & declining, reducing interpolation to {self.current_interpolation_ratio:.4f}")
                elif performance_stable:
                    # 性能稳定但差，轻微减少插值
                    self.current_interpolation_ratio *= 0.95
                    print(f"Performance poor but stable, slightly reducing interpolation to {self.current_interpolation_ratio:.4f}")
                # 如果性能在改善，保持当前插值比例不变
            else:
                # 性能较好时，可以尝试轻微增加插值比例
                if performance_improving and self.current_interpolation_ratio < self.max_interpolation_ratio:
                    self.current_interpolation_ratio = min(
                        self.current_interpolation_ratio * 1.02,  # 极小的增长
                        self.max_interpolation_ratio
                    )
                    print(f"Performance good & improving, slightly increasing interpolation to {self.current_interpolation_ratio:.4f}")
                else:
                    # 性能好但稳定，缓慢减少插值比例
                    self.current_interpolation_ratio *= 0.99
            
            # 确保插值比例不会低于最小值
            self.current_interpolation_ratio = max(
                self.current_interpolation_ratio,
                self.base_interpolation_ratio * 0.1  # 最小值为基础比例的10%
            )
    
    def generate_smart_interpolated_features(self, train_X_by_class, train_Y_by_class, 
                                           seen_class_indices, num_interpolations, epoch):
        """
        智能特征空间插值生成 - 极保守版本
        根据类别相似性、样本质量和训练进展生成高质量插值样本
        增加严格的质量控制机制
        """
        if not self.use_interpolation or num_interpolations <= 0:
            return np.array([]), np.array([]), np.array([])
        
        interpolated_samples = []
        interpolated_attributes = []
        interpolated_labels = []
        
        # 新增质量控制统计
        attempted_interpolations = 0
        successful_interpolations = 0
        quality_failures = 0
        
        # 计算类别间的属性相似性矩阵
        class_similarities = {}
        class_centers = {}
        
        for class_idx in seen_class_indices:
            class_centers[class_idx] = np.mean(train_Y_by_class[class_idx], axis=0)
        
        for i, class1 in enumerate(seen_class_indices):
            for j, class2 in enumerate(seen_class_indices):
                if i < j:  # 避免重复计算
                    attr1 = class_centers[class1]
                    attr2 = class_centers[class2]
                    similarity = np.dot(attr1, attr2) / (np.linalg.norm(attr1) * np.linalg.norm(attr2))
                    class_similarities[(class1, class2)] = similarity
        
        # 动态调整相似性阈值
        if epoch < 50:
            similarity_threshold = 0.4  # 早期只选择较相似的类别
        elif epoch < 150:
            similarity_threshold = 0.2  # 中期放宽限制
        else:
            similarity_threshold = 0.0  # 后期允许任意类别组合
        
        # 改为while循环以支持质量控制
        while successful_interpolations < num_interpolations and attempted_interpolations < self.max_failed_attempts:
            attempted_interpolations += 1
            
            if class_similarities and np.random.random() < 0.9:  # 90%概率选择基于相似性
                # 选择相似度适中的类别对进行插值
                valid_pairs = [(pair, sim) for pair, sim in class_similarities.items() 
                             if sim > similarity_threshold]
                
                if valid_pairs:
                    # 按相似度加权选择，偏向中等相似度
                    pairs, similarities = zip(*valid_pairs)
                    similarities = np.array(similarities)
                    
                    # 对相似度进行变换，偏向中等相似度的类别对
                    adjusted_similarities = 1.0 - np.abs(similarities - 0.5)  # 偏向0.5相似度
                    probs = adjusted_similarities / np.sum(adjusted_similarities)
                    
                    chosen_pair = pairs[np.random.choice(len(pairs), p=probs)]
                    class1, class2 = chosen_pair
                else:
                    # 随机选择
                    class1, class2 = np.random.choice(seen_class_indices, 2, replace=False)
            else:
                # 随机选择两个不同的seen class
                class1, class2 = np.random.choice(seen_class_indices, 2, replace=False)
            
            # 智能样本选择 - 选择高质量的代表性样本
            class1_samples = train_X_by_class[class1]
            class2_samples = train_X_by_class[class2]
            
            # 计算样本质量（距离类别中心的距离）
            def select_quality_sample(samples, class_center_attr):
                if len(samples) <= 3:
                    return np.random.choice(len(samples))
                
                # 计算样本到类别中心的距离
                sample_center = np.mean(samples, axis=0)
                distances = np.linalg.norm(samples - sample_center, axis=1)
                
                # 选择距离适中的样本（不是最中心也不是最边缘）
                sorted_indices = np.argsort(distances)
                mid_start = len(sorted_indices) // 4
                mid_end = 3 * len(sorted_indices) // 4
                mid_indices = sorted_indices[mid_start:mid_end]
                
                return np.random.choice(mid_indices) if len(mid_indices) > 0 else np.random.choice(len(samples))
            
            idx1 = select_quality_sample(class1_samples, class_centers[class1])
            idx2 = select_quality_sample(class2_samples, class_centers[class2])
            
            sample1 = class1_samples[idx1]
            sample2 = class2_samples[idx2]
            
            attr1 = train_Y_by_class[class1][idx1]
            attr2 = train_Y_by_class[class2][idx2]
            
            # 动态调整插值系数
            if epoch < 30:
                # 早期非常保守
                alpha = np.random.uniform(0.45, 0.55)
            elif epoch < 100:
                # 中期逐渐放宽
                alpha = np.random.uniform(0.4, 0.6)
            else:
                # 后期使用配置的范围
                alpha = np.random.uniform(self.alpha_range[0], self.alpha_range[1])
            
            # 样本插值
            interpolated_sample = alpha * sample1 + (1 - alpha) * sample2
            
            # 改进的属性插值策略
            attr_similarity = np.dot(attr1, attr2) / (np.linalg.norm(attr1) * np.linalg.norm(attr2))
            
            if attr_similarity > 0.6:
                # 属性很相似时使用插值
                interpolated_attr = alpha * attr1 + (1 - alpha) * attr2
            elif attr_similarity > 0.3:
                # 属性中等相似时，有70%概率插值，30%概率选择
                if np.random.random() < 0.7:
                    interpolated_attr = alpha * attr1 + (1 - alpha) * attr2
                else:
                    interpolated_attr = attr1 if np.random.random() < alpha else attr2
            else:
                # 属性差异大时选择其中一个
                interpolated_attr = attr1 if np.random.random() < alpha else attr2
            
            # 标签选择策略 - 偏向alpha值
            interpolated_label = class1 if np.random.random() < alpha else class2
            
            # 严格的质量检查
            if self.quality_check_enabled:
                # 检查1: 插值样本不应该与原始样本过于相似
                sample1_sim = np.linalg.norm(interpolated_sample - sample1)
                sample2_sim = np.linalg.norm(interpolated_sample - sample2)
                min_distance = min(sample1_sim, sample2_sim)
                
                # 检查2: 插值属性与原始属性的相似性
                attr1_sim = np.dot(interpolated_attr, attr1) / (np.linalg.norm(interpolated_attr) * np.linalg.norm(attr1))
                attr2_sim = np.dot(interpolated_attr, attr2) / (np.linalg.norm(interpolated_attr) * np.linalg.norm(attr2))
                attr_quality = max(attr1_sim, attr2_sim)
                
                # 检查3: alpha值不应过于极端
                alpha_quality = 1.0 - abs(alpha - 0.5) * 2  # alpha接近0.5时质量更高
                
                # 综合质量评分
                overall_quality = (min_distance * 0.3 + attr_quality * 0.5 + alpha_quality * 0.2)
                
                if overall_quality >= self.interpolation_quality_threshold:
                    interpolated_samples.append(interpolated_sample)
                    interpolated_attributes.append(interpolated_attr)
                    interpolated_labels.append(interpolated_label)
                    successful_interpolations += 1
                else:
                    quality_failures += 1
            else:
                # 不进行质量检查，直接添加
                interpolated_samples.append(interpolated_sample)
                interpolated_attributes.append(interpolated_attr)
                interpolated_labels.append(interpolated_label)
                successful_interpolations += 1
        
        # 输出质量统计信息
        if attempted_interpolations > 0:
            success_rate = successful_interpolations / attempted_interpolations
            if epoch % 50 == 0:  # 每50个epoch输出一次统计
                print(f"Interpolation Quality Stats - Attempted: {attempted_interpolations}, "
                      f"Successful: {successful_interpolations}, Success Rate: {success_rate:.3f}, "
                      f"Quality Failures: {quality_failures}")
        
        return (np.array(interpolated_samples), 
                np.array(interpolated_attributes), 
                np.array(interpolated_labels))
        
    def build_autoencoder(self):
      
      sample = Input(shape=self.sample_shape)     
     
      a0=sample

      # Encoder
      a1=Dense(100)(a0)
      a1=LeakyReLU(alpha=0.2)(a1)
      a1=BatchNormalization()(a1)

      a2=Dense(200)(a1)
      a2=LeakyReLU(alpha=0.2)(a2)
      a2=BatchNormalization()(a2)

      a3=Dense(256)(a2)
      a3=LeakyReLU(alpha=0.2)(a3)
      a3=BatchNormalization()(a3)
      feature=a3

      # Decoder
      a4=Dense(200)(feature)
      a4=LeakyReLU(alpha=0.2)(a4)
      a4=BatchNormalization()(a4)

      a5=Dense(100)(a4)
      a5=LeakyReLU(alpha=0.2)(a5)
      a5=BatchNormalization()(a5)

      a6=Dense(52)(a5)
      a6=LeakyReLU(alpha=0.2)(a6)
      a6=BatchNormalization()(a6)
      output_sample=a6

      # Autoencoder Model
      autoencoder = Model(sample,[feature, output_sample])
      # We only need the encoder part for triplet loss feature extraction
      self.encoder = Model(sample, feature)
      return autoencoder    
        
    def build_discriminator(self):
        
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input,attribute],validity)

    def build_generator(self):
      
      noise = Input(shape=self.noise_shape)
      attribute = Input(shape=(20,), dtype='float32')
      
      noise_embedding = Flatten()(noise)
      attribute_embedding = Dense(self.latent_dim)(attribute)
      
      g_input = concatenate([noise_embedding, attribute_embedding])

      g1 = Dense(128)(g_input)
      g1 = LeakyReLU(alpha=0.2)(g1)
      g1 = BatchNormalization()(g1)

      g2 = residual_block(g1, 256) 
      g3 = residual_block(g2, 256) 
      
      g3_attention = SelfAttention()(g3)
      
      generated_feature = Dense(256)(g3_attention)
      generated_feature = BatchNormalization()(generated_feature)

      return Model([noise,attribute],generated_feature)
    
    def build_classifier(self):
        
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        
        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        hidden_ouput=c2
               
        c3 = Dense(20,activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
            return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self,current_batch_features,y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(
                y_true, pred_attribute)
        
        mi_penalty=0    
        if self.bound == True:    
          mi_penalty = self.mi_penalty_loss(
              current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def cycle_rank_loss(self, anchor, positive, negative):
        # We can reuse the triplet loss logic for cycle rank consistency
        # The goal is that the reconstructed feature (positive) is closer to the original fake feature (anchor)
        # than any other generated feature from a different class (negative).
        return self.triplet_loss(anchor, positive, negative)
    
    def train(self, epochs, batch_size, log_file_path, results_file_path, test_class_indices=None):
        
        start_time = datetime.datetime.now()
        
        accuracy_list_1=[]
        accuracy_list_2=[]
        accuracy_list_3=[]
        accuracy_list_4=[]
        
        valid = -np.ones((batch_size,1) )
        fake = np.ones((batch_size,1) )
        
        PATH_train='./data/dataset_train_case1.npz'
        PATH_test='./data/dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        # 使用传入的测试类别，如果没有传入则默认使用E组
        if test_class_indices is None:
            test_class_indices = [9, 13, 15]  # 默认E组 (1-based)
        
        # 根据测试类别，确定训练类别（Seen classes）
        all_class_indices = list(range(1, 16)) # 1-15
        seen_class_indices = [i for i in all_class_indices if i not in test_class_indices]
        
        # This part remains mostly the same, but we will use the labels to find pos/neg samples
        train_X_by_class = {i: train_data[f'training_samples_{i}'] for i in seen_class_indices}
        train_Y_by_class = {i: train_data[f'training_attribute_{i}'] for i in seen_class_indices}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        test_X_by_class = {i: test_data[f'testing_samples_{i}'] for i in test_class_indices}
        test_Y_by_class = {i: test_data[f'testing_attribute_{i}'] for i in test_class_indices}

        test_X = np.concatenate([v for k, v in test_X_by_class.items()])
        test_Y = np.concatenate([v for k, v in test_Y_by_class.items()])
        test_classlabel = np.concatenate([np.full(len(v), k) for k, v in test_X_by_class.items()])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # Re-organize scaled data back into class dictionaries
        current_pos = 0
        for i in seen_class_indices:
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata=all_train_X
        train_attributelabel=all_train_Y
        train_classlabel = all_train_labels
        
        testdata=test_X
        test_attributelabel=test_Y
       
        num_batches=int(traindata.shape[0]/batch_size)
        
        with open(log_file_path, 'a') as log_file:
            for epoch in range(epochs):
                
                for batch_i in range(num_batches):
                    
                    start_i =batch_i * batch_size
                    end_i=(batch_i + 1) * batch_size
                    
                    train_x=traindata[start_i:end_i]
                    train_y=train_attributelabel[start_i:end_i] 
                    train_labels = train_classlabel[start_i:end_i]
                    
                    # 智能特征空间插值增强 - 极保守启动
                    if self.use_interpolation and epoch >= 100:  # 从第100个epoch开始使用插值
                        num_interpolations = int(batch_size * self.current_interpolation_ratio)
                        interp_samples, interp_attrs, interp_labels = self.generate_smart_interpolated_features(
                            train_X_by_class, train_Y_by_class, seen_class_indices, num_interpolations, epoch
                        )
                        
                        # 将插值样本添加到当前批次
                        if len(interp_samples) > 0:
                            # 如果插值样本数量足够，随机选择部分替换原始样本
                            replace_indices = np.random.choice(batch_size, min(num_interpolations, batch_size), replace=False)
                            train_x[replace_indices[:len(interp_samples)]] = interp_samples[:len(replace_indices)]
                            train_y[replace_indices[:len(interp_samples)]] = interp_attrs[:len(replace_indices)]
                            train_labels[replace_indices[:len(interp_samples)]] = interp_labels[:len(replace_indices)]
                                                                                
                    # Autoencoder and Classifier Training (Same as before)
                    self.autoencoder.trainable = True
                    self.c.trainable = True # Train C together with AE now
                    
                    with tf.GradientTape(persistent=True) as tape_auto_c:
                      feature, output_sample=self.autoencoder(train_x)
                      autoencoder_loss=mean_squared_error(train_x,output_sample)      

                      hidden_ouput_c,predict_attribute_c=self.c(feature)
                      c_loss=self.classification_loss(feature,train_y, hidden_ouput_c, predict_attribute_c)

                      total_ac_loss = autoencoder_loss + c_loss

                    grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                    self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                    del tape_auto_c

                    # Triplet Loss Metric Learning
                    self.autoencoder.trainable = True # Encoder is part of metric learning
                    self.c.trainable = True
                    
                    # Sample triplets
                    anchor_samples = train_x
                    positive_samples = []
                    negative_samples = []
                    for label in train_labels:
                        pos_class_samples = train_X_by_class[label]
                        pos_idx = np.random.choice(len(pos_class_samples))
                        positive_samples.append(pos_class_samples[pos_idx])
                        
                        neg_class = np.random.choice([c for c in seen_class_indices if c != label])
                        neg_class_samples = train_X_by_class[neg_class]
                        neg_idx = np.random.choice(len(neg_class_samples))
                        negative_samples.append(neg_class_samples[neg_idx])

                    positive_samples = np.array(positive_samples)
                    negative_samples = np.array(negative_samples)

                    with tf.GradientTape() as tape_m:
                        anchor_features = self.encoder(anchor_samples)
                        positive_features = self.encoder(positive_samples)
                        negative_features = self.encoder(negative_samples)
                        
                        m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                    grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                    self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                    # Discriminator Training (Same as before)
                    self.autoencoder.trainable = False
                    self.c.trainable = False
                    self.d.trainable = True
                    self.g.trainable = False

                    for _ in range(self.n_critic):
                      with tf.GradientTape() as tape_d:
                        noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                        fake_feature = self.g([noise,train_y])
                        real_feature = self.encoder(train_x)
            
                        real_validity = self.d([real_feature,train_y])
                        fake_validity = self.d([fake_feature,train_y])  
                                               
                        d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                        d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                        d_loss = d_loss_real + d_loss_fake
                      
                      grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                      self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                    # Generator Training
                    self.d.trainable = False               
                    self.g.trainable = True
                    
                    with tf.GradientTape() as tape_g:
                      noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                      Fake_feature_g = self.g([noise_g,train_y])
                      Fake_validity_g = self.d([Fake_feature_g,train_y])
                      adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
                
                      fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                      classification_loss = self.classification_loss(Fake_feature_g,train_y, fake_hidden_ouput_g, Fake_classification_g)
                      
                      # Triplet loss for Generator
                      g_anchor_features = Fake_feature_g
                      g_positive_features = self.encoder(positive_samples) # Use same positive samples
                      g_negative_features = self.encoder(negative_samples) # Use same negative samples
                      triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                      
                      cycle_rank_loss = 0
                      if self.crl == True:
                        reconstructed_feature = self.g([noise_g, Fake_classification_g])
                        
                        # For cycle rank, the "negative" is a feature from a different class
                        # We can reuse the negative samples from the batch for simplicity
                        negative_attributes = np.array([train_Y_by_class[np.random.choice([c for c in seen_class_indices if c != label])][0] for label in train_labels])
                        unsimilar_generated_feature = self.g([noise_g, negative_attributes])

                        cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                               
                      total_loss = adversarial_loss + self.lambda_cla * classification_loss + self.lambda_triplet * triplet_loss_g + self.lambda_crl*cycle_rank_loss  
                              
                    grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                    self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                    
                    elapsed_time = datetime.datetime.now() - start_time
              
                    print ("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][M loss: %f][D loss: %f][G loss %05f ]time: %s " \
                     % (epoch, epochs,
                       batch_i, num_batches,
                       tf.reduce_mean(total_ac_loss), 
                         m_loss,
                         d_loss,
                         tf.reduce_mean(total_loss),                                                                                                              
                         elapsed_time))
            
                if epoch % 1 == 0:
                    accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp = feature_generation_and_diagnosis(2000,testdata,test_attributelabel,self.autoencoder,self.g, self.c, test_class_indices)  

                    accuracy_list_1.append(accuracy_lsvm) 
                    accuracy_list_2.append(accuracy_nrf) 
                    accuracy_list_3.append(accuracy_pnb)
                    accuracy_list_4.append(accuracy_mlp)

                    # 计算当前最佳准确率用于自适应插值策略
                    current_best_accuracy = max(accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp)
                    
                    # 更新插值策略
                    if self.adaptive_interpolation:
                        self.update_interpolation_strategy(epoch, current_best_accuracy)

                    print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                      %(epoch, epochs,max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)))
                    
                    # 增强日志输出，显示插值策略信息
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]")
                    
                    # 添加插值策略信息
                    if self.use_interpolation:
                        log_message += f"[Interp_ratio: {self.current_interpolation_ratio:.3f}]"
                    else:
                        log_message += "[Interp: Disabled]"
                    
                    log_message += "\n"
                    log_file.write(log_message)
                    log_file.flush()
            
        best_accuracy = max([max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)])
        print('finished! best_acc:{:.4f}'.format(best_accuracy))
        
        # 保存最终结果到CSV
        results_df = pd.DataFrame({
            'epoch': range(len(accuracy_list_1)),
            'accuracy_lsvm': accuracy_list_1,
            'accuracy_nrf': accuracy_list_2,
            'accuracy_pnb': accuracy_list_3,
            'accuracy_mlp': accuracy_list_4
        })
        results_df.to_csv(results_file_path, index=False)
        print(f"Results saved to {results_file_path}")

        log_file.write(f'finished! best_acc:{best_accuracy:.4f}\n')
        log_file.write(f'Results saved to {results_file_path}\n')
        log_file.flush()
                
if __name__ == '__main__':
    # =============================================================
    # 配置区域：只需要修改这里的TARGET_GROUP即可切换实验组别
    # =============================================================
    TARGET_GROUP = 'E' #可选: 'A', 'B', 'C', 'D', 'E'
    EPOCHS = 2000
    BATCH_SIZE = 128
    # =============================================================

    # 1. 定义不同组别的测试类别
    group_configs = {
        'A': [1, 5, 8],
        'B': [2, 6, 11],
        'C': [3, 7, 12],
        'D': [4, 10, 14],
        'E': [9, 13, 15]
    }
    test_classes = group_configs.get(TARGET_GROUP)
    if test_classes is None:
        raise ValueError(f"Invalid TARGET_GROUP: {TARGET_GROUP}. Please choose from {list(group_configs.keys())}")

    # 2. 设置实验名称和输出目录
    script_name = Path(__file__).stem
    timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
    run_name = f"{TARGET_GROUP}_{timestamp}"
    
    # 创建符合规范的日志和结果目录
    # 日志路径: 日志/脚本名/组名_时间戳/run.log
    # 结果路径: 结果/脚本名/组名_时间戳/accuracy_results.csv
    log_dir = Path('日志') / script_name / run_name
    results_dir = Path('结果') / script_name / run_name
    log_dir.mkdir(parents=True, exist_ok=True)
    results_dir.mkdir(parents=True, exist_ok=True)
    
    log_file_path = log_dir / 'run.log'
    results_file_path = results_dir / 'accuracy_results.csv'

    print(f"--- Starting Experiment: {run_name} ---")
    print(f"Log file will be saved to: {log_file_path}")
    print(f"Results will be saved to: {results_file_path}")
    
    # 3. 写入日志文件头
    with open(log_file_path, 'w') as f:
        f.write(f"# Experiment Log: {run_name}\n")
        f.write(f"{"-"*20}\n")
        f.write(f"Script: {Path(__file__).name}\n")
        f.write(f"Start Time: {timestamp}\n")
        f.write(f"Target Group: {TARGET_GROUP}\n")
        f.write(f"Test Classes: {test_classes}\n")
        f.write(f"Epochs: {EPOCHS}, Batch Size: {BATCH_SIZE}\n")
        f.write(f"{"-"*20}\n\n")

    # 4. 初始化并运行模型
    acgan = Zero_shot()
    acgan.set_group_config(TARGET_GROUP)  # 设置当前组别配置
    
    # 调用训练函数，传入新的路径参数
    acgan.train(epochs=EPOCHS, 
                batch_size=BATCH_SIZE, 
                log_file_path=log_file_path,
                results_file_path=results_file_path,
                test_class_indices=test_classes)
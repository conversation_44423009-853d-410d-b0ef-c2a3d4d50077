#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案B数据预处理脚本：混合属性生成
将20维原始属性与降维后的BERT属性拼接，生成混合属性向量
"""

import numpy as np
import os
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA
import argparse

def load_original_attributes():
    """加载原始20维故障属性"""
    # TEP故障的20维手工标注属性（0/1向量）
    fault_attributes = {
        1: [1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],   # A/C feed ratio
        2: [0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],   # B composition
        3: [0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],   # D feed temperature
        4: [0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],   # Reactor cooling water
        5: [0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],   # Condenser cooling water
        6: [0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0],   # A feed loss
        7: [0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0],   # C header pressure loss
        8: [0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0],   # A,B,C composition
        9: [0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0],   # D feed temperature
        10:[0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0],   # C feed temperature
        11:[0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0],   # Reactor cooling water
        12:[0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0],   # Condenser cooling water
        13:[0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0],   # Reaction kinetics
        14:[0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0],   # Reactor cooling water
        15:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0],   # Condenser cooling water
    }
    
    # 转换为numpy数组，按故障序号排序
    attributes_array = np.array([fault_attributes[i] for i in range(1, 16)])
    print(f"✅ 原始20维属性加载完成，形状: {attributes_array.shape}")
    return attributes_array

def load_bert_attributes(bert_file_path):
    """加载BERT属性文件"""
    if not os.path.exists(bert_file_path):
        raise FileNotFoundError(f"BERT属性文件不存在: {bert_file_path}")
    
    bert_attributes = np.load(bert_file_path)
    print(f"✅ BERT属性加载完成，形状: {bert_attributes.shape}")
    return bert_attributes

def compress_bert_attributes(bert_attributes, n_components=128, explained_variance_threshold=0.95):
    """使用PCA压缩BERT属性维度"""
    print(f"🔄 开始PCA降维，目标维度: {n_components}")
    
    # 使用PCA进行降维
    pca = PCA(n_components=n_components)
    bert_compressed = pca.fit_transform(bert_attributes)
    
    # 计算解释方差比
    explained_variance = np.sum(pca.explained_variance_ratio_)
    print(f"📊 PCA降维完成:")
    print(f"   - 原始维度: {bert_attributes.shape[1]} -> 压缩维度: {bert_compressed.shape[1]}")
    print(f"   - 保留解释方差: {explained_variance:.3f} ({explained_variance*100:.1f}%)")
    
    if explained_variance < explained_variance_threshold:
        print(f"⚠️  警告: 解释方差 {explained_variance:.3f} 低于阈值 {explained_variance_threshold}")
    
    return bert_compressed, pca

def standardize_bert_attributes(bert_compressed):
    """标准化压缩后的BERT属性"""
    print("🔄 对压缩后的BERT属性进行标准化...")
    
    scaler = StandardScaler()
    bert_standardized = scaler.fit_transform(bert_compressed)
    
    print(f"📊 标准化完成:")
    print(f"   - 均值: {np.mean(bert_standardized, axis=0)[:5]}")  # 显示前5维
    print(f"   - 标准差: {np.std(bert_standardized, axis=0)[:5]}")
    
    return bert_standardized, scaler

def create_hybrid_attributes(original_attrs, bert_attrs):
    """创建混合属性向量"""
    print("🔄 拼接原始属性和BERT属性...")
    
    # 沿特征维度拼接
    hybrid_attrs = np.concatenate([original_attrs, bert_attrs], axis=1)
    
    print(f"📊 混合属性创建完成:")
    print(f"   - 原始属性维度: {original_attrs.shape[1]}")
    print(f"   - BERT属性维度: {bert_attrs.shape[1]}")
    print(f"   - 混合属性维度: {hybrid_attrs.shape[1]} (总计)")
    print(f"   - 最终形状: {hybrid_attrs.shape}")
    
    return hybrid_attrs

def save_processing_components(pca, scaler, output_dir):
    """保存PCA和标准化组件，供训练时使用"""
    np.save(os.path.join(output_dir, 'bert_pca_components.npy'), pca.components_)
    np.save(os.path.join(output_dir, 'bert_pca_mean.npy'), pca.mean_)
    np.save(os.path.join(output_dir, 'bert_scaler_mean.npy'), scaler.mean_)
    np.save(os.path.join(output_dir, 'bert_scaler_scale.npy'), scaler.scale_)
    print(f"✅ PCA和标准化组件已保存到 {output_dir}")

def main():
    parser = argparse.ArgumentParser(description='方案B：生成混合属性')
    parser.add_argument('--bert-file', type=str, default='tep_bert_attributes.npy',
                        help='BERT属性文件路径')
    parser.add_argument('--pca-components', type=int, default=128,
                        help='PCA降维目标维度')
    parser.add_argument('--output-dir', type=str, default='.',
                        help='输出目录')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🚀 方案B：混合属性生成开始")
    print("=" * 60)
    
    try:
        # 1. 加载原始20维属性
        original_attributes = load_original_attributes()
        
        # 2. 加载BERT属性
        bert_attributes = load_bert_attributes(args.bert_file)
        
        # 3. 压缩BERT属性
        bert_compressed, pca = compress_bert_attributes(
            bert_attributes, 
            n_components=args.pca_components
        )
        
        # 4. 标准化BERT属性
        bert_standardized, scaler = standardize_bert_attributes(bert_compressed)
        
        # 5. 创建混合属性
        hybrid_attributes = create_hybrid_attributes(original_attributes, bert_standardized)
        
        # 6. 保存结果
        hybrid_file = os.path.join(args.output_dir, 'tep_hybrid_attributes.npy')
        np.save(hybrid_file, hybrid_attributes)
        print(f"✅ 混合属性已保存: {hybrid_file}")
        
        # 7. 保存处理组件
        save_processing_components(pca, scaler, args.output_dir)
        
        # 8. 输出摘要
        print("\n" + "=" * 60)
        print("📋 方案B预处理完成摘要:")
        print("=" * 60)
        print(f"📁 输出文件:")
        print(f"   - 混合属性: {hybrid_file}")
        print(f"   - PCA组件: bert_pca_*.npy")
        print(f"   - 标准化组件: bert_scaler_*.npy")
        print(f"🔢 维度信息:")
        print(f"   - 原始属性: 20维")
        print(f"   - BERT属性: 768维 -> {args.pca_components}维")
        print(f"   - 混合属性: {20 + args.pca_components}维")
        print(f"📊 数据形状: {hybrid_attributes.shape}")
        
        # 9. 验证数据质量
        print(f"\n🔍 数据质量检查:")
        print(f"   - 是否包含NaN: {np.isnan(hybrid_attributes).any()}")
        print(f"   - 是否包含Inf: {np.isinf(hybrid_attributes).any()}")
        print(f"   - 数值范围: [{np.min(hybrid_attributes):.3f}, {np.max(hybrid_attributes):.3f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ 预处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 方案B数据预处理成功完成！")
        print("👉 下一步：运行 acgan_triplet_hybrid.py 开始训练")
    else:
        print("\n💥 预处理失败，请检查错误信息")
        exit(1) 
#!/bin/bash

echo "🔥 C-Hybrid实验快速启动"
echo "========================"
echo "📊 C组配置: 测试类别[8, 11, 12]"
echo "⚡ 策略: HardTriplet强权重 + SmartCRL适度约束"
echo ""

# 检查环境
echo "🔍 环境检查..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

if ! python3 -c "import tensorflow" &> /dev/null; then
    echo "❌ TensorFlow 未安装"
    exit 1
fi

echo "✅ 环境检查通过"

# 创建必要目录
mkdir -p logs
mkdir -p tensorboard_logs

# 选择运行方式
echo ""
echo "请选择运行方式:"
echo "1) 前台运行 (可以看到实时输出)"
echo "2) 后台运行 (使用tmux)"
echo "3) 只启动TensorBoard监控"
echo "4) 同时启动训练和TensorBoard"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "🚀 前台启动C组实验..."
        python3 run_C_Hybrid.py
        ;;
    2)
        echo "🚀 后台启动C组实验..."
        tmux new-session -d -s C-Hybrid 'python3 run_C_Hybrid.py'
        echo "✅ 实验已在tmux会话中启动"
        echo "📋 查看进度: tmux attach -t C-Hybrid"
        echo "⏹️  停止实验: tmux kill-session -t C-Hybrid"
        ;;
    3)
        echo "📊 启动TensorBoard监控..."
        if [ -d "./tensorboard_logs/C_Hybrid_realtime" ]; then
            tensorboard --logdir=./tensorboard_logs/C_Hybrid_realtime --host=0.0.0.0 --port=6008 &
            echo "✅ TensorBoard已启动: http://localhost:6008"
        else
            echo "❌ C组日志目录不存在，请先运行实验"
        fi
        ;;
    4)
        echo "🚀 同时启动训练和监控..."
        # 后台启动训练
        tmux new-session -d -s C-Hybrid 'python3 run_C_Hybrid.py'
        echo "✅ 训练已在tmux中启动"
        
        # 等待一下让训练创建日志目录
        sleep 5
        
        # 启动TensorBoard
        if [ -d "./tensorboard_logs/C_Hybrid_realtime" ]; then
            tensorboard --logdir=./tensorboard_logs/C_Hybrid_realtime --host=0.0.0.0 --port=6008 &
            echo "✅ TensorBoard已启动: http://localhost:6008"
        else
            echo "⚠️  TensorBoard日志目录尚未创建，请稍后手动启动"
        fi
        
        echo ""
        echo "📋 管理命令:"
        echo "   查看训练: tmux attach -t C-Hybrid"
        echo "   停止训练: tmux kill-session -t C-Hybrid"
        echo "   停止监控: pkill tensorboard"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🎯 C组实验信息:"
echo "   测试类别: [8, 11, 12]"
echo "   训练类别: [1, 2, 3, 4, 5, 6, 7, 9, 10, 13, 14, 15]"
echo "   预期训练时间: 2-4小时"
echo "   目标准确率: 55%+"
#!/bin/bash

# 🚀 自动化训练脚本 - A、B、C组连续训练
# 按顺序执行：A组 → B组 → C组，每组2000轮次

echo "🎯 自动化训练脚本启动"
echo "====================================="
echo "📋 训练计划:"
echo "1. A组: 2000 epochs (~2-3小时)"
echo "2. B组: 2000 epochs (~2-3小时)"
echo "3. C组: 2000 epochs (~2-3小时)"
echo ""
echo "📊 预计总时间: 6-9小时"
echo "⏰ 开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "====================================="

# 激活conda环境
echo "🔧 激活conda环境..."
source ~/miniconda3/etc/profile.d/conda.sh
conda activate vaegan_rtx50

# 切换到正确目录
cd /home/<USER>/hmt/ACGAN-FG-main/innovations

# 确认是否继续
echo ""
read -p "🤔 确认开始自动化训练？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 训练已取消"
    exit 1
fi

# 记录开始时间
start_time=$(date +%s)

echo ""
echo "🚀 开始自动化训练..."
echo "💡 可以随时按 Ctrl+C 中断训练"
echo ""

# 训练A组
echo "============================================"
echo "📊 [1/3] 开始训练A组 (2000 epochs)"
echo "⏰ $(date '+%Y-%m-%d %H:%M:%S')"
echo "============================================"

python test_all_improvements.py --group A --epochs 2000

if [ $? -eq 0 ]; then
    echo "✅ A组训练完成！"
else
    echo "❌ A组训练失败！"
    read -p "是否继续B组训练？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 自动化训练已停止"
        exit 1
    fi
fi

echo ""
echo "⏳ 准备开始B组训练..."
sleep 3

# 训练B组
echo "============================================"
echo "📊 [2/3] 开始训练B组 (2000 epochs)"
echo "⏰ $(date '+%Y-%m-%d %H:%M:%S')"
echo "============================================"

python test_all_improvements.py --group B --epochs 2000

if [ $? -eq 0 ]; then
    echo "✅ B组训练完成！"
else
    echo "❌ B组训练失败！"
    read -p "是否继续C组训练？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 自动化训练已停止"
        exit 1
    fi
fi

echo ""
echo "⏳ 准备开始C组训练..."
sleep 3

# 训练C组
echo "============================================"
echo "📊 [3/3] 开始训练C组 (2000 epochs)"
echo "⏰ $(date '+%Y-%m-%d %H:%M:%S')"
echo "============================================"

python test_all_improvements.py --group C --epochs 2000

if [ $? -eq 0 ]; then
    echo "✅ C组训练完成！"
else
    echo "❌ C组训练失败！"
fi

# 计算总时间
end_time=$(date +%s)
total_time=$((end_time - start_time))
hours=$((total_time / 3600))
minutes=$(((total_time % 3600) / 60))

echo ""
echo "🎉 自动化训练完成！"
echo "====================================="
echo "⏱️ 总用时: ${hours}小时${minutes}分钟"
echo "🏁 完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""
echo "📁 查看结果:"
echo "- A组: experiments/group_A/"
echo "- B组: experiments/group_B/"
echo "- C组: experiments/group_C/"
echo ""
echo "📊 启动TensorBoard查看训练曲线:"
echo "tensorboard --logdir=tensorboard"
echo "====================================="

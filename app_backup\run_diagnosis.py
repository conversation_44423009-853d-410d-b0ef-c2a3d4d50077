#!/usr/bin/env python3
"""
数据诊断主控脚本
Phase 1: 深入诊断 "数据之困" (Data-Centric Diagnosis)

运行完整的数据诊断分析流程，包括：
1. 语义空间分析
2. 类别可分离性分析  
3. 特征空间可视化
4. 合成特征质量评估
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_banner():
    """打印横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    数据诊断分析系统                          ║
    ║                Data-Centric Diagnosis                       ║
    ║                                                              ║
    ║  目标: 解释为什么A组"容易"，而B组"困难"                      ║
    ║  Target: Explain why Group A is "easy" and Group B is "hard" ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'numpy', 'pandas', 'matplotlib', 'seaborn', 
        'sklearn', 'umap-learn', 'scipy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            elif package == 'umap-learn':
                import umap
            else:
                __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_data_files():
    """检查数据文件"""
    print("\n📂 检查数据文件...")
    
    data_dir = '/home/<USER>/hmt/ACGAN-FG-main/data/'
    required_files = []
    
    # 检查属性矩阵
    attr_file = os.path.join(data_dir, 'attribute_matrix.xlsx')
    if os.path.exists(attr_file):
        print(f"  ✅ 属性矩阵: {attr_file}")
    else:
        print(f"  ⚠️  属性矩阵不存在: {attr_file}")
    
    # 检查类别数据文件
    missing_files = []
    for class_id in range(1, 16):
        train_file = os.path.join(data_dir, f'd{class_id:02d}.dat')
        test_file = os.path.join(data_dir, f'd{class_id:02d}_te.dat')
        
        if os.path.exists(train_file):
            print(f"  ✅ 训练数据: d{class_id:02d}.dat")
        else:
            print(f"  ❌ 训练数据: d{class_id:02d}.dat")
            missing_files.append(train_file)
            
        if os.path.exists(test_file):
            print(f"  ✅ 测试数据: d{class_id:02d}_te.dat")
        else:
            print(f"  ❌ 测试数据: d{class_id:02d}_te.dat")
            missing_files.append(test_file)
    
    if missing_files:
        print(f"\n⚠️  缺少 {len(missing_files)} 个数据文件")
        print("分析将使用可用的数据文件继续进行")
    else:
        print("✅ 所有数据文件完整")
    
    return len(missing_files) < 20  # 如果缺少太多文件则返回False

def run_analysis_step(script_name, description):
    """运行分析步骤"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print('='*60)
    
    start_time = time.time()
    
    try:
        # 运行脚本
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, text=True, cwd='/home/<USER>/hmt/ACGAN-FG-main/')
        
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            if result.stdout:
                print("输出:")
                print(result.stdout)
        else:
            print(f"❌ {description} 失败")
            if result.stderr:
                print("错误信息:")
                print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 运行 {script_name} 时出错: {e}")
        return False
    
    end_time = time.time()
    duration = end_time - start_time
    print(f"⏱️  耗时: {duration:.1f} 秒")
    
    return True

def generate_summary_report():
    """生成总结报告"""
    print("\n" + "="*60)
    print("📋 生成总结报告")
    print("="*60)
    
    report_content = f"""
# 数据诊断分析报告
## Data-Centric Diagnosis Report

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 分析目标
解释为什么A组[1, 6, 14]"容易"，而B组[4, 7, 10]"困难"。

## 分析内容

### 1. 语义空间分析 (Semantic Space Analysis)
- **文件**: `semantic_analysis.png`
- **内容**: 
  - 所有类别语义相似度矩阵
  - 各组内平均语义相似度
  - 组间语义相似度矩阵
  - 语义相似度与分类难度的关系

### 2. 类别可分离性分析 (Class Separability Analysis)
- **文件**: `separability_analysis.png`
- **内容**:
  - 轮廓系数分析
  - 可分离性比率 (类间距离/类内距离)
  - 可分离性与分类性能的关系
  - 综合性能雷达图

### 3. 特征空间可视化 (Feature Space Visualization)
- **文件**: 
  - `feature_space_group_A.png` (A组特征空间)
  - `feature_space_group_B.png` (B组特征空间)
- **内容**:
  - t-SNE, UMAP, PCA降维可视化
  - 训练集vs测试集特征分布
  - 类别间距离分析

### 4. 合成特征质量评估 (Synthetic Feature Quality Assessment)
- **文件**: `synthetic_comparison_group_*.png`
- **内容**:
  - 真实特征vs合成特征对比
  - 多种生成方法比较 (Gaussian, Noise, Interpolation)
  - 统计分布对比分析

## 关键发现

### 假设验证
1. **语义相似度假设**: A组类别在语义空间中是否相距较远？
2. **特征重叠假设**: B组类别在特征空间中是否严重重叠？
3. **生成质量假设**: 困难组的合成特征质量是否更差？

### 量化指标
- 组内语义相似度
- 轮廓系数 (Silhouette Score)
- 可分离性比率
- 分类准确率

## 改进建议

基于诊断结果，针对"困难模式"的改进方向：
1. **数据增强策略**
2. **特征工程优化**
3. **模型架构调整**
4. **损失函数改进**

---
*本报告由数据诊断系统自动生成*
"""
    
    # 保存报告
    report_file = '/home/<USER>/hmt/ACGAN-FG-main/diagnosis_report.md'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📄 报告已保存: {report_file}")

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_dependencies():
        print("❌ 依赖检查失败，请安装缺少的包后重试")
        return
    
    if not check_data_files():
        print("❌ 数据文件检查失败，请确保数据文件完整")
        return
    
    print("\n🎯 开始数据诊断分析流程...")
    
    # 分析步骤
    analysis_steps = [
        ('data_diagnosis.py', '语义空间分析 & 可分离性分析'),
        ('synthetic_analysis.py', '合成特征质量评估')
    ]
    
    success_count = 0
    total_steps = len(analysis_steps)
    
    for script, description in analysis_steps:
        if run_analysis_step(script, description):
            success_count += 1
        else:
            print(f"⚠️  {description} 失败，继续下一步...")
    
    # 生成总结报告
    generate_summary_report()
    
    # 最终总结
    print("\n" + "="*60)
    print("🎉 数据诊断分析完成!")
    print("="*60)
    
    print(f"📊 成功完成: {success_count}/{total_steps} 个分析步骤")
    
    if success_count == total_steps:
        print("✅ 所有分析步骤成功完成!")
    else:
        print(f"⚠️  {total_steps - success_count} 个步骤失败，请检查错误信息")
    
    print("\n📁 生成的文件:")
    output_files = [
        'semantic_analysis.png',
        'separability_analysis.png', 
        'feature_space_group_A.png',
        'feature_space_group_B.png',
        'synthetic_comparison_group_A_gaussian.png',
        'synthetic_comparison_group_A_noise.png',
        'synthetic_comparison_group_A_interpolation.png',
        'synthetic_comparison_group_B_gaussian.png',
        'synthetic_comparison_group_B_noise.png',
        'synthetic_comparison_group_B_interpolation.png',
        'diagnosis_report.md'
    ]
    
    for file in output_files:
        file_path = f'/home/<USER>/hmt/ACGAN-FG-main/{file}'
        if os.path.exists(file_path):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file}")
    
    print("\n🔍 下一步建议:")
    print("1. 查看生成的可视化图表，分析类别难度的根本原因")
    print("2. 基于诊断结果，设计针对性的改进策略")
    print("3. 实施改进方案并验证效果")

if __name__ == "__main__":
    main()

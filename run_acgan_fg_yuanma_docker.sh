#!/bin/bash

# 运行ACGAN-FG源码改进版本的Docker脚本
# 设置epoch=10进行快速测试

echo "=== 开始运行ACGAN-FG源码改进版本 (epoch=10) ==="
echo "时间: $(date)"
echo "=========================================="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误: Docker未运行，请先启动Docker"
    exit 1
fi

# 检查NVIDIA Docker支持
if ! docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi > /dev/null 2>&1; then
    echo "警告: 未检测到GPU支持，将使用CPU运行"
    GPU_FLAG=""
else
    echo "检测到GPU支持，将使用GPU加速"
    GPU_FLAG="--gpus all"
fi

# 构建Docker镜像（如果不存在）
IMAGE_NAME="acgan-fg-main"
if [[ "$(docker images -q $IMAGE_NAME 2> /dev/null)" == "" ]]; then
    echo "构建Docker镜像..."
    docker build -t $IMAGE_NAME .
    if [ $? -ne 0 ]; then
        echo "错误: Docker镜像构建失败"
        exit 1
    fi
else
    echo "使用现有Docker镜像: $IMAGE_NAME"
fi

# 创建日志目录
mkdir -p logs

# 生成日志文件名
LOG_FILE="logs/acgan_fg_yuanma_$(date +%Y%m%d_%H%M%S).log"

echo "开始运行训练..."
echo "日志文件: $LOG_FILE"
echo "=========================================="

# 运行Docker容器
docker run --rm -it \
    $GPU_FLAG \
    -v "$(pwd)":/workspace \
    -w /workspace \
    --name acgan-fg-yuanma-run \
    $IMAGE_NAME \
    python acgan_fg_yuanma.py 2>&1 | tee $LOG_FILE

# 检查运行结果
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo "=========================================="
    echo "✅ 训练完成！"
    echo "日志文件: $LOG_FILE"
    echo "时间: $(date)"
else
    echo "=========================================="
    echo "❌ 训练失败，请检查日志文件: $LOG_FILE"
    echo "时间: $(date)"
    exit 1
fi

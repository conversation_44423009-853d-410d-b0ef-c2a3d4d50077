2025-07-07 17:33:36.451600: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-07-07 17:33:36.458860: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:485] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered
2025-07-07 17:33:36.467143: E external/local_xla/xla/stream_executor/cuda/cuda_dnn.cc:8473] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
2025-07-07 17:33:36.469727: E external/local_xla/xla/stream_executor/cuda/cuda_blas.cc:1471] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered
2025-07-07 17:33:36.475876: I tensorflow/core/platform/cpu_feature_guard.cc:211] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX, in other operations, rebuild TensorFlow with the appropriate compiler flags.
/usr/local/lib/python3.12/dist-packages/tensorflow_addons/utils/tfa_eol_msg.py:23: UserWarning: 

TensorFlow Addons (TFA) has ended development and introduction of new features.
TFA has entered a minimal maintenance and release mode until a planned end of life in May 2024.
Please modify downstream libraries to take dependencies from other repositories in our TensorFlow community (e.g. Keras, Keras-CV, and Keras-NLP). 

For more information see: https://github.com/tensorflow/addons/issues/2807 

  warnings.warn(
/usr/local/lib/python3.12/dist-packages/tensorflow_addons/utils/ensure_tf_install.py:53: UserWarning: Tensorflow Addons supports using Python ops for all Tensorflow versions above or equal to 2.12.0 and strictly below 2.15.0 (nightly versions are not supported). 
 The versions of TensorFlow you are currently using is 2.17.0 and is not supported. 
Some things might work, some things might not.
If you were to encounter a bug, do not file an issue.
If you want to make sure you're using a tested and supported configuration, either change the TensorFlow version or the TensorFlow Addons's version. 
You can find the compatibility matrix in TensorFlow Addon's readme:
https://github.com/tensorflow/addons
  warnings.warn(
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1751909617.778172   84346 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1751909617.816535   84346 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1751909617.817564   84346 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1751909617.820517   84346 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1751909617.821473   84346 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1751909617.822304   84346 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1751909617.904594   84346 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1751909617.905591   84346 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
I0000 00:00:1751909617.908914   84346 cuda_executor.cc:1015] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
2025-07-07 17:33:37.909790: I tensorflow/core/common_runtime/gpu/gpu_device.cc:2021] Created device /job:localhost/replica:0/task:0/device:GPU:0 with 10344 MB memory:  -> device: 0, name: NVIDIA GeForce RTX 5080, pci bus id: 0000:02:00.0, compute capability: 12.0
2025-07-07 17:33:38.330184: I external/local_xla/xla/stream_executor/cuda/cuda_dnn.cc:531] Loaded cuDNN version 90701
''+ptx85+ptx85' is not a recognized feature for this target' is not a recognized feature for this target (ignoring feature)
 (ignoring feature)
''+ptx85+ptx85' is not a recognized feature for this target' is not a recognized feature for this target (ignoring feature)
 (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1751909619.298360   84346 service.cc:146] XLA service 0x494a25c0 initialized for platform CUDA (this does not guarantee that XLA will be used). Devices:
I0000 00:00:1751909619.298395   84346 service.cc:154]   StreamExecutor device (0): NVIDIA GeForce RTX 5080, Compute Capability 12.0
2025-07-07 17:33:39.300681: I tensorflow/compiler/mlir/tensorflow/utils/dump_mlir_util.cc:268] disabling MLIR crash reproducer, set env var `MLIR_CRASH_REPRODUCER_DIRECTORY` to enable.
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
I0000 00:00:1751909619.342843   84346 device_compiler.h:188] Compiled cluster using XLA!  This line is logged at most once for the lifetime of the process.
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
WARNING:tensorflow:5 out of the last 5 calls to <function _BaseOptimizer._update_step_xla at 0x7da923e87060> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
WARNING:tensorflow:6 out of the last 6 calls to <function _BaseOptimizer._update_step_xla at 0x7da923e87060> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
'+ptx85' is not a recognized feature for this target (ignoring feature)
loading data...
test classes: [1, 6, 14]
train classes: [0, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13]
1 Physical GPUs, 1 Logical GPU
训练开始，日志将被记录到: 结果/202507080133_triplet.md
[Epoch 0/2000][Batch 0/225][AE+C loss: 2.137918][M loss: 10.541672][D loss: 2.103130][G loss 66.179138 ]time: 0:00:04.574446 
[Epoch 0/2000][Batch 1/225][AE+C loss: 2.147012][M loss: 0.614894][D loss: 2.031704][G loss 57.739967 ]time: 0:00:04.787503 
[Epoch 0/2000][Batch 2/225][AE+C loss: 2.157540][M loss: 0.551870][D loss: 1.914829][G loss 29.679115 ]time: 0:00:04.965360 
[Epoch 0/2000][Batch 3/225][AE+C loss: 1.996866][M loss: 3.652805][D loss: 1.748855][G loss 26.879341 ]time: 0:00:05.138405 
[Epoch 0/2000][Batch 4/225][AE+C loss: 1.958361][M loss: 2.175853][D loss: 1.676231][G loss 20.183008 ]time: 0:00:05.318694 
[Epoch 0/2000][Batch 5/225][AE+C loss: 1.922232][M loss: 1.483309][D loss: 1.558122][G loss 9.941525 ]time: 0:00:05.507757 
[Epoch 0/2000][Batch 6/225][AE+C loss: 1.804989][M loss: 1.343314][D loss: 1.421543][G loss 13.053696 ]time: 0:00:05.703548 
[Epoch 0/2000][Batch 7/225][AE+C loss: 1.797299][M loss: 0.271232][D loss: 1.315177][G loss 11.055947 ]time: 0:00:05.909224 
[Epoch 0/2000][Batch 8/225][AE+C loss: 1.750449][M loss: 0.388601][D loss: 1.150253][G loss 8.978402 ]time: 0:00:06.108436 
[Epoch 0/2000][Batch 9/225][AE+C loss: 1.696017][M loss: 0.062875][D loss: 1.089506][G loss 9.290435 ]time: 0:00:06.332656 
[Epoch 0/2000][Batch 10/225][AE+C loss: 1.690710][M loss: 2.822751][D loss: 1.007342][G loss 11.321641 ]time: 0:00:06.523420 
[Epoch 0/2000][Batch 11/225][AE+C loss: 1.656740][M loss: 0.360550][D loss: 0.884515][G loss 11.006928 ]time: 0:00:06.726850 
[Epoch 0/2000][Batch 12/225][AE+C loss: 1.633172][M loss: 2.390470][D loss: 0.733840][G loss 9.064379 ]time: 0:00:06.912204 
[Epoch 0/2000][Batch 13/225][AE+C loss: 1.600916][M loss: 0.112462][D loss: 0.696528][G loss 8.992524 ]time: 0:00:07.113283 
[Epoch 0/2000][Batch 14/225][AE+C loss: 1.577474][M loss: 0.212438][D loss: 0.510019][G loss 9.157568 ]time: 0:00:07.289501 
[Epoch 0/2000][Batch 15/225][AE+C loss: 1.774384][M loss: 27.168118][D loss: 0.925220][G loss 157.635468 ]time: 0:00:07.481168 
[Epoch 0/2000][Batch 16/225][AE+C loss: 1.865819][M loss: 15.538151][D loss: 0.960172][G loss 109.908363 ]time: 0:00:07.673336 
[Epoch 0/2000][Batch 17/225][AE+C loss: 1.926915][M loss: 1.379061][D loss: 0.877491][G loss 139.353058 ]time: 0:00:07.865854 
[Epoch 0/2000][Batch 18/225][AE+C loss: 1.905369][M loss: 0.000000][D loss: 0.781633][G loss 76.966919 ]time: 0:00:08.085803 
[Epoch 0/2000][Batch 19/225][AE+C loss: 1.889923][M loss: 0.067882][D loss: 0.617388][G loss 85.923683 ]time: 0:00:08.264965 
[Epoch 0/2000][Batch 20/225][AE+C loss: 1.871220][M loss: 1.969002][D loss: 0.542710][G loss 84.035591 ]time: 0:00:08.480296 
[Epoch 0/2000][Batch 21/225][AE+C loss: 1.843285][M loss: 0.024881][D loss: 0.496166][G loss 55.558868 ]time: 0:00:08.699984 
[Epoch 0/2000][Batch 22/225][AE+C loss: 1.808519][M loss: 0.397216][D loss: 0.337030][G loss 28.210205 ]time: 0:00:08.887010 
[Epoch 0/2000][Batch 23/225][AE+C loss: 1.783109][M loss: 0.190619][D loss: 0.208175][G loss 29.871725 ]time: 0:00:09.095133 
[Epoch 0/2000][Batch 24/225][AE+C loss: 1.763689][M loss: 0.114066][D loss: 0.228960][G loss 12.160976 ]time: 0:00:09.293339 
[Epoch 0/2000][Batch 25/225][AE+C loss: 1.730488][M loss: 0.986893][D loss: 0.145999][G loss 15.181612 ]time: 0:00:09.484247 
[Epoch 0/2000][Batch 26/225][AE+C loss: 1.701916][M loss: 0.000000][D loss: 0.130192][G loss 19.676949 ]time: 0:00:09.689091 
[Epoch 0/2000][Batch 27/225][AE+C loss: 1.674434][M loss: 0.260019][D loss: 0.061830][G loss 13.255000 ]time: 0:00:09.902198 
[Epoch 0/2000][Batch 28/225][AE+C loss: 1.666225][M loss: 0.137410][D loss: 0.044678][G loss 10.636677 ]time: 0:00:10.125407 
[Epoch 0/2000][Batch 29/225][AE+C loss: 1.629284][M loss: 0.054566][D loss: 0.029339][G loss 11.087381 ]time: 0:00:10.326486 
[Epoch 0/2000][Batch 30/225][AE+C loss: 1.979727][M loss: 2.141721][D loss: 1.567662][G loss 54.929192 ]time: 0:00:10.507269 
[Epoch 0/2000][Batch 31/225][AE+C loss: 1.994067][M loss: 1.407897][D loss: 1.590450][G loss 87.578110 ]time: 0:00:10.692569 
[Epoch 0/2000][Batch 32/225][AE+C loss: 1.949040][M loss: 1.250567][D loss: 1.539857][G loss 98.510666 ]time: 0:00:10.902549 
[Epoch 0/2000][Batch 33/225][AE+C loss: 1.929898][M loss: 1.122972][D loss: 1.490041][G loss 58.292519 ]time: 0:00:11.091791 
[Epoch 0/2000][Batch 34/225][AE+C loss: 1.905984][M loss: 0.942141][D loss: 1.423761][G loss 35.457993 ]time: 0:00:11.287374 
[Epoch 0/2000][Batch 35/225][AE+C loss: 1.887008][M loss: 1.783285][D loss: 1.410636][G loss 62.163391 ]time: 0:00:11.489468 
[Epoch 0/2000][Batch 36/225][AE+C loss: 1.837052][M loss: 1.719510][D loss: 1.310282][G loss 42.796875 ]time: 0:00:11.676460 
[Epoch 0/2000][Batch 37/225][AE+C loss: 1.830111][M loss: 1.180657][D loss: 1.325549][G loss 58.865334 ]time: 0:00:11.866190 
[Epoch 0/2000][Batch 38/225][AE+C loss: 1.775663][M loss: 1.573147][D loss: 1.212806][G loss 52.728340 ]time: 0:00:12.054215 
[Epoch 0/2000][Batch 39/225][AE+C loss: 1.750715][M loss: 1.620288][D loss: 1.145235][G loss 58.545479 ]time: 0:00:12.261593 
[Epoch 0/2000][Batch 40/225][AE+C loss: 1.723411][M loss: 0.773709][D loss: 1.043319][G loss 68.624771 ]time: 0:00:12.473570 
[Epoch 0/2000][Batch 41/225][AE+C loss: 1.698673][M loss: 0.626775][D loss: 0.988136][G loss 89.795860 ]time: 0:00:12.677135 
[Epoch 0/2000][Batch 42/225][AE+C loss: 1.692756][M loss: 0.294083][D loss: 0.967208][G loss 53.064491 ]time: 0:00:12.850922 
[Epoch 0/2000][Batch 43/225][AE+C loss: 1.659189][M loss: 1.482075][D loss: 0.853678][G loss 57.321808 ]time: 0:00:13.032420 
[Epoch 0/2000][Batch 44/225][AE+C loss: 1.619627][M loss: 2.138053][D loss: 0.767743][G loss 59.880272 ]time: 0:00:13.247959 
[Epoch 0/2000][Batch 45/225][AE+C loss: 1.895673][M loss: 1.704231][D loss: 1.041582][G loss 59.547390 ]time: 0:00:13.439297 
[Epoch 0/2000][Batch 46/225][AE+C loss: 1.880713][M loss: 0.980074][D loss: 0.986331][G loss 43.515770 ]time: 0:00:13.647002 
[Epoch 0/2000][Batch 47/225][AE+C loss: 1.867330][M loss: 1.485577][D loss: 0.980851][G loss 30.186127 ]time: 0:00:13.954605 
[Epoch 0/2000][Batch 48/225][AE+C loss: 1.854247][M loss: 1.290870][D loss: 0.914856][G loss 48.824497 ]time: 0:00:14.143229 
[Epoch 0/2000][Batch 49/225][AE+C loss: 1.843782][M loss: 1.133669][D loss: 0.752254][G loss 47.475739 ]time: 0:00:14.339586 
[Epoch 0/2000][Batch 50/225][AE+C loss: 1.822170][M loss: 1.003844][D loss: 0.750435][G loss 47.481865 ]time: 0:00:14.516845 
[Epoch 0/2000][Batch 51/225][AE+C loss: 1.806677][M loss: 0.783398][D loss: 0.680448][G loss 28.793068 ]time: 0:00:14.713205 
[Epoch 0/2000][Batch 52/225][AE+C loss: 1.776669][M loss: 0.816992][D loss: 0.524264][G loss 37.753193 ]time: 0:00:14.922366 
[Epoch 0/2000][Batch 53/225][AE+C loss: 1.753775][M loss: 1.032805][D loss: 0.425921][G loss 50.815483 ]time: 0:00:15.128761 
[Epoch 0/2000][Batch 54/225][AE+C loss: 1.719352][M loss: 0.808037][D loss: 0.420809][G loss 37.182076 ]time: 0:00:15.316786 
[Epoch 0/2000][Batch 55/225][AE+C loss: 1.711257][M loss: 0.391071][D loss: 0.338540][G loss 50.555195 ]time: 0:00:15.518473 
[Epoch 0/2000][Batch 56/225][AE+C loss: 1.670410][M loss: 1.158308][D loss: 0.281807][G loss 43.327587 ]time: 0:00:15.704984 
[Epoch 0/2000][Batch 57/225][AE+C loss: 1.649541][M loss: 0.771696][D loss: 0.261085][G loss 25.623968 ]time: 0:00:15.883513 
[Epoch 0/2000][Batch 58/225][AE+C loss: 1.631306][M loss: 0.683818][D loss: 0.156040][G loss 48.141830 ]time: 0:00:16.080798 
[Epoch 0/2000][Batch 59/225][AE+C loss: 1.619805][M loss: 0.555392][D loss: 0.141337][G loss 27.577106 ]time: 0:00:16.257929 
[Epoch 0/2000][Batch 60/225][AE+C loss: 1.534439][M loss: 2.585423][D loss: 0.169937][G loss 36.497257 ]time: 0:00:16.475458 
[Epoch 0/2000][Batch 61/225][AE+C loss: 1.513458][M loss: 2.178256][D loss: 0.137781][G loss 31.468960 ]time: 0:00:16.661857 
[Epoch 0/2000][Batch 62/225][AE+C loss: 1.498858][M loss: 2.512902][D loss: 0.095474][G loss 36.483574 ]time: 0:00:16.833726 
[Epoch 0/2000][Batch 63/225][AE+C loss: 1.534797][M loss: 1.638789][D loss: 0.128639][G loss 52.947006 ]time: 0:00:17.009093 
[Epoch 0/2000][Batch 64/225][AE+C loss: 1.531707][M loss: 2.713774][D loss: 0.161419][G loss 28.518177 ]time: 0:00:17.214075 
[Epoch 0/2000][Batch 65/225][AE+C loss: 1.493084][M loss: 1.863691][D loss: 0.153840][G loss 42.477570 ]time: 0:00:17.408712 
[Epoch 0/2000][Batch 66/225][AE+C loss: 1.465912][M loss: 2.812539][D loss: 0.120012][G loss 33.869125 ]time: 0:00:17.634190 
[Epoch 0/2000][Batch 67/225][AE+C loss: 1.459007][M loss: 0.812117][D loss: 0.085449][G loss 31.760630 ]time: 0:00:17.816455 
[Epoch 0/2000][Batch 68/225][AE+C loss: 1.447229][M loss: 1.340098][D loss: 0.141005][G loss 31.341114 ]time: 0:00:18.029296 
[Epoch 0/2000][Batch 69/225][AE+C loss: 1.435854][M loss: 1.453699][D loss: 0.043995][G loss 29.743889 ]time: 0:00:18.223834 
[Epoch 0/2000][Batch 70/225][AE+C loss: 1.428351][M loss: 1.239935][D loss: 0.040423][G loss 29.449718 ]time: 0:00:18.429371 
[Epoch 0/2000][Batch 71/225][AE+C loss: 1.420448][M loss: 0.952012][D loss: 0.072074][G loss 20.732744 ]time: 0:00:18.613169 
[Epoch 0/2000][Batch 72/225][AE+C loss: 1.412520][M loss: 1.769517][D loss: 0.063798][G loss 29.591152 ]time: 0:00:18.797228 
[Epoch 0/2000][Batch 73/225][AE+C loss: 1.400850][M loss: 0.337318][D loss: 0.027115][G loss 25.698860 ]time: 0:00:18.993430 
[Epoch 0/2000][Batch 74/225][AE+C loss: 1.397167][M loss: 0.490800][D loss: 0.048904][G loss 20.329636 ]time: 0:00:19.203803 
[Epoch 0/2000][Batch 75/225][AE+C loss: 1.721299][M loss: 45.180527][D loss: 0.005178][G loss 393.694702 ]time: 0:00:19.381824 
[Epoch 0/2000][Batch 76/225][AE+C loss: 1.806575][M loss: 13.882499][D loss: 0.000000][G loss 302.058319 ]time: 0:00:19.572252 
[Epoch 0/2000][Batch 77/225][AE+C loss: 1.890920][M loss: 0.610535][D loss: 0.003378][G loss 235.711334 ]time: 0:00:19.771592 
[Epoch 0/2000][Batch 78/225][AE+C loss: 1.999947][M loss: 0.000000][D loss: 0.002589][G loss 135.518677 ]time: 0:00:19.971881 
[Epoch 0/2000][Batch 79/225][AE+C loss: 2.046830][M loss: 0.000000][D loss: 0.008245][G loss 105.828476 ]time: 0:00:20.162761 
[Epoch 0/2000][Batch 80/225][AE+C loss: 2.054452][M loss: 0.145942][D loss: 0.011426][G loss 67.178734 ]time: 0:00:20.351318 
[Epoch 0/2000][Batch 81/225][AE+C loss: 2.049955][M loss: 0.000000][D loss: 0.000000][G loss 25.674969 ]time: 0:00:20.544773 
[Epoch 0/2000][Batch 82/225][AE+C loss: 2.008305][M loss: 0.000000][D loss: 0.000000][G loss 16.576756 ]time: 0:00:20.734036 
[Epoch 0/2000][Batch 83/225][AE+C loss: 1.997590][M loss: 0.000000][D loss: 0.000000][G loss 9.898619 ]time: 0:00:20.920156 
[Epoch 0/2000][Batch 84/225][AE+C loss: 1.965165][M loss: 0.000000][D loss: 0.000000][G loss 10.598639 ]time: 0:00:21.100520 
[Epoch 0/2000][Batch 85/225][AE+C loss: 1.941079][M loss: 0.000000][D loss: 0.000000][G loss 10.088961 ]time: 0:00:21.274470 
[Epoch 0/2000][Batch 86/225][AE+C loss: 1.917247][M loss: 0.000000][D loss: 0.000000][G loss 10.364737 ]time: 0:00:21.468234 
[Epoch 0/2000][Batch 87/225][AE+C loss: 1.889538][M loss: 0.002965][D loss: 0.000000][G loss 12.597266 ]time: 0:00:21.657430 
[Epoch 0/2000][Batch 88/225][AE+C loss: 1.849775][M loss: 0.000000][D loss: 0.000000][G loss 9.770699 ]time: 0:00:21.855135 
[Epoch 0/2000][Batch 89/225][AE+C loss: 1.820001][M loss: 0.000000][D loss: 0.000000][G loss 9.915909 ]time: 0:00:22.052464 
[Epoch 0/2000][Batch 90/225][AE+C loss: 1.574024][M loss: 2.611774][D loss: 0.000000][G loss 95.428619 ]time: 0:00:22.250568 
[Epoch 0/2000][Batch 91/225][AE+C loss: 1.577045][M loss: 4.382004][D loss: 0.000000][G loss 97.591354 ]time: 0:00:22.431103 
[Epoch 0/2000][Batch 92/225][AE+C loss: 1.581481][M loss: 2.867930][D loss: 0.000000][G loss 86.927330 ]time: 0:00:22.609784 
[Epoch 0/2000][Batch 93/225][AE+C loss: 1.640451][M loss: 2.732303][D loss: 0.000000][G loss 130.273499 ]time: 0:00:22.799994 
[Epoch 0/2000][Batch 94/225][AE+C loss: 1.595037][M loss: 1.848967][D loss: 0.000000][G loss 119.427048 ]time: 0:00:23.015779 
[Epoch 0/2000][Batch 95/225][AE+C loss: 1.541777][M loss: 1.478909][D loss: 0.000000][G loss 61.058060 ]time: 0:00:23.195356 
[Epoch 0/2000][Batch 96/225][AE+C loss: 1.561788][M loss: 1.037414][D loss: 0.000000][G loss 110.254242 ]time: 0:00:23.384498 
[Epoch 0/2000][Batch 97/225][AE+C loss: 1.549804][M loss: 2.697988][D loss: 0.000000][G loss 84.809479 ]time: 0:00:23.603213 
[Epoch 0/2000][Batch 98/225][AE+C loss: 1.521646][M loss: 1.792058][D loss: 0.000000][G loss 74.871056 ]time: 0:00:23.793063 
[Epoch 0/2000][Batch 99/225][AE+C loss: 1.528366][M loss: 1.405012][D loss: 0.000000][G loss 100.586868 ]time: 0:00:23.971814 
[Epoch 0/2000][Batch 100/225][AE+C loss: 1.518405][M loss: 1.470803][D loss: 0.000000][G loss 73.419098 ]time: 0:00:24.180502 
[Epoch 0/2000][Batch 101/225][AE+C loss: 1.498686][M loss: 0.887654][D loss: 0.000000][G loss 45.715210 ]time: 0:00:24.394066 
[Epoch 0/2000][Batch 102/225][AE+C loss: 1.483175][M loss: 1.021285][D loss: 0.000000][G loss 52.093369 ]time: 0:00:24.618991 
[Epoch 0/2000][Batch 103/225][AE+C loss: 1.472119][M loss: 1.104381][D loss: 0.000000][G loss 37.588158 ]time: 0:00:24.823381 
[Epoch 0/2000][Batch 104/225][AE+C loss: 1.464728][M loss: 1.115423][D loss: 0.000000][G loss 62.374393 ]time: 0:00:25.008012 
[Epoch 0/2000][Batch 105/225][AE+C loss: 1.904945][M loss: 3.642945][D loss: 0.000784][G loss 60.645920 ]time: 0:00:25.219621 
[Epoch 0/2000][Batch 106/225][AE+C loss: 1.903559][M loss: 2.153413][D loss: 0.000000][G loss 119.464012 ]time: 0:00:25.402063 
[Epoch 0/2000][Batch 107/225][AE+C loss: 1.874539][M loss: 4.351445][D loss: 0.000000][G loss 78.377052 ]time: 0:00:25.602032 
[Epoch 0/2000][Batch 108/225][AE+C loss: 1.845187][M loss: 3.861687][D loss: 0.000444][G loss 64.979462 ]time: 0:00:25.781212 
[Epoch 0/2000][Batch 109/225][AE+C loss: 1.897177][M loss: 3.212558][D loss: 0.000000][G loss 105.077744 ]time: 0:00:25.984316 
[Epoch 0/2000][Batch 110/225][AE+C loss: 1.868346][M loss: 3.376189][D loss: 0.003947][G loss 91.717987 ]time: 0:00:26.187385 
[Epoch 0/2000][Batch 111/225][AE+C loss: 1.828293][M loss: 3.665088][D loss: 0.000000][G loss 92.862961 ]time: 0:00:26.394191 
[Epoch 0/2000][Batch 112/225][AE+C loss: 1.801444][M loss: 4.799204][D loss: 0.000000][G loss 67.822487 ]time: 0:00:26.598074 
[Epoch 0/2000][Batch 113/225][AE+C loss: 1.744886][M loss: 2.207102][D loss: 0.000000][G loss 148.469254 ]time: 0:00:26.791399 
[Epoch 0/2000][Batch 114/225][AE+C loss: 1.769530][M loss: 2.989854][D loss: 0.010748][G loss 81.248085 ]time: 0:00:27.004901 
[Epoch 0/2000][Batch 115/225][AE+C loss: 1.713905][M loss: 3.585675][D loss: 0.000000][G loss 52.686588 ]time: 0:00:27.197791 
[Epoch 0/2000][Batch 116/225][AE+C loss: 1.711429][M loss: 2.118349][D loss: 0.000000][G loss 74.490417 ]time: 0:00:27.369359 
[Epoch 0/2000][Batch 117/225][AE+C loss: 1.719200][M loss: 3.709806][D loss: 0.000000][G loss 55.072273 ]time: 0:00:27.537954 
[Epoch 0/2000][Batch 118/225][AE+C loss: 1.701461][M loss: 1.820031][D loss: 0.003351][G loss 30.492826 ]time: 0:00:27.712474 
[Epoch 0/2000][Batch 119/225][AE+C loss: 1.734944][M loss: 3.636947][D loss: 0.000000][G loss 41.936451 ]time: 0:00:27.884882 
[Epoch 0/2000][Batch 120/225][AE+C loss: 1.682842][M loss: 0.556414][D loss: 0.868900][G loss 33.347561 ]time: 0:00:28.071549 
[Epoch 0/2000][Batch 121/225][AE+C loss: 1.685018][M loss: 0.399434][D loss: 0.830278][G loss 25.897888 ]time: 0:00:28.271710 
[Epoch 0/2000][Batch 122/225][AE+C loss: 1.679551][M loss: 0.341277][D loss: 0.729491][G loss 29.463547 ]time: 0:00:28.482429 
[Epoch 0/2000][Batch 123/225][AE+C loss: 1.659084][M loss: 0.390701][D loss: 0.757250][G loss 29.509745 ]time: 0:00:28.686927 
[Epoch 0/2000][Batch 124/225][AE+C loss: 1.643473][M loss: 0.256839][D loss: 0.643286][G loss 30.161196 ]time: 0:00:28.874931 
[Epoch 0/2000][Batch 125/225][AE+C loss: 1.622338][M loss: 0.384636][D loss: 0.606760][G loss 35.376163 ]time: 0:00:29.079429 
[Epoch 0/2000][Batch 126/225][AE+C loss: 1.607682][M loss: 0.559425][D loss: 0.491011][G loss 36.257618 ]time: 0:00:29.275362 
[Epoch 0/2000][Batch 127/225][AE+C loss: 1.598887][M loss: 0.558833][D loss: 0.425315][G loss 20.460188 ]time: 0:00:29.481337 
[Epoch 0/2000][Batch 128/225][AE+C loss: 1.578720][M loss: 0.319959][D loss: 0.417427][G loss 37.256721 ]time: 0:00:29.709514 
[Epoch 0/2000][Batch 129/225][AE+C loss: 1.564618][M loss: 0.320048][D loss: 0.330939][G loss 20.785927 ]time: 0:00:29.906594 
[Epoch 0/2000][Batch 130/225][AE+C loss: 1.543731][M loss: 0.159545][D loss: 0.224439][G loss 21.961191 ]time: 0:00:30.085902 
[Epoch 0/2000][Batch 131/225][AE+C loss: 1.523430][M loss: 0.365923][D loss: 0.182558][G loss 24.367533 ]time: 0:00:30.289787 
[Epoch 0/2000][Batch 132/225][AE+C loss: 1.505679][M loss: 0.421141][D loss: 0.089328][G loss 37.721725 ]time: 0:00:30.495699 
[Epoch 0/2000][Batch 133/225][AE+C loss: 1.501279][M loss: 0.459712][D loss: 0.102321][G loss 30.791309 ]time: 0:00:30.706043 
[Epoch 0/2000][Batch 134/225][AE+C loss: 1.481404][M loss: 0.368333][D loss: 0.058819][G loss 20.286501 ]time: 0:00:30.919299 
[Epoch 0/2000][Batch 135/225][AE+C loss: 1.505542][M loss: 0.407000][D loss: 0.080598][G loss 30.876671 ]time: 0:00:31.122383 
[Epoch 0/2000][Batch 136/225][AE+C loss: 1.501592][M loss: 0.442409][D loss: 0.052371][G loss 21.058588 ]time: 0:00:31.307902 
[Epoch 0/2000][Batch 137/225][AE+C loss: 1.503111][M loss: 0.535506][D loss: 0.038909][G loss 19.129393 ]time: 0:00:31.467438 
[Epoch 0/2000][Batch 138/225][AE+C loss: 1.495615][M loss: 0.380452][D loss: 0.051597][G loss 21.723846 ]time: 0:00:31.647299 
[Epoch 0/2000][Batch 139/225][AE+C loss: 1.469238][M loss: 0.399712][D loss: 0.049350][G loss 26.311621 ]time: 0:00:31.815507 
[Epoch 0/2000][Batch 140/225][AE+C loss: 1.463480][M loss: 0.362153][D loss: 0.054316][G loss 23.105444 ]time: 0:00:31.995332 
[Epoch 0/2000][Batch 141/225][AE+C loss: 1.484221][M loss: 0.474660][D loss: 0.027535][G loss 20.224571 ]time: 0:00:32.155994 
[Epoch 0/2000][Batch 142/225][AE+C loss: 1.477248][M loss: 0.235702][D loss: 0.029453][G loss 28.896778 ]time: 0:00:32.321440 
[Epoch 0/2000][Batch 143/225][AE+C loss: 1.459691][M loss: 0.505760][D loss: 0.033978][G loss 27.446362 ]time: 0:00:32.481586 
[Epoch 0/2000][Batch 144/225][AE+C loss: 1.460637][M loss: 0.353537][D loss: 0.021144][G loss 30.518509 ]time: 0:00:32.644485 
[Epoch 0/2000][Batch 145/225][AE+C loss: 1.437826][M loss: 0.237745][D loss: 0.016208][G loss 21.162678 ]time: 0:00:32.837545 
[Epoch 0/2000][Batch 146/225][AE+C loss: 1.416446][M loss: 0.356258][D loss: 0.016419][G loss 25.663071 ]time: 0:00:33.028647 
[Epoch 0/2000][Batch 147/225][AE+C loss: 1.385204][M loss: 0.362946][D loss: 0.000000][G loss 23.315392 ]time: 0:00:33.235858 
[Epoch 0/2000][Batch 148/225][AE+C loss: 1.388652][M loss: 0.506812][D loss: 0.008218][G loss 20.810177 ]time: 0:00:33.401910 
[Epoch 0/2000][Batch 149/225][AE+C loss: 1.389501][M loss: 0.454908][D loss: 0.012446][G loss 21.644602 ]time: 0:00:33.593264 
[Epoch 0/2000][Batch 150/225][AE+C loss: 1.636060][M loss: 0.307415][D loss: 0.088731][G loss 19.440514 ]time: 0:00:33.752953 
[Epoch 0/2000][Batch 151/225][AE+C loss: 1.638705][M loss: 0.355658][D loss: 0.047706][G loss 25.089684 ]time: 0:00:33.918267 
[Epoch 0/2000][Batch 152/225][AE+C loss: 1.633618][M loss: 0.795554][D loss: 0.070687][G loss 17.446928 ]time: 0:00:34.120503 
[Epoch 0/2000][Batch 153/225][AE+C loss: 1.623017][M loss: 0.263631][D loss: 0.088819][G loss 19.711243 ]time: 0:00:34.301203 
[Epoch 0/2000][Batch 154/225][AE+C loss: 1.609266][M loss: 0.203633][D loss: 0.069004][G loss 15.857063 ]time: 0:00:34.466403 
[Epoch 0/2000][Batch 155/225][AE+C loss: 1.589716][M loss: 0.336189][D loss: 0.108263][G loss 20.955273 ]time: 0:00:34.736830 
[Epoch 0/2000][Batch 156/225][AE+C loss: 1.583331][M loss: 0.426136][D loss: 0.064004][G loss 17.109140 ]time: 0:00:35.051324 
[Epoch 0/2000][Batch 157/225][AE+C loss: 1.571423][M loss: 0.255946][D loss: 0.048951][G loss 23.105453 ]time: 0:00:35.362999 
[Epoch 0/2000][Batch 158/225][AE+C loss: 1.542593][M loss: 0.408521][D loss: 0.061199][G loss 21.908371 ]time: 0:00:35.690945 
[Epoch 0/2000][Batch 159/225][AE+C loss: 1.533088][M loss: 0.277427][D loss: 0.023971][G loss 16.684174 ]time: 0:00:35.993334 
[Epoch 0/2000][Batch 160/225][AE+C loss: 1.510403][M loss: 0.328414][D loss: 0.084020][G loss 17.043964 ]time: 0:00:36.182691 
[Epoch 0/2000][Batch 161/225][AE+C loss: 1.503327][M loss: 0.565391][D loss: 0.037339][G loss 17.534077 ]time: 0:00:36.349217 
[Epoch 0/2000][Batch 162/225][AE+C loss: 1.474562][M loss: 0.612104][D loss: 0.026277][G loss 17.281685 ]time: 0:00:36.530847 
[Epoch 0/2000][Batch 163/225][AE+C loss: 1.457916][M loss: 0.385819][D loss: 0.018246][G loss 11.765320 ]time: 0:00:36.716854 
[Epoch 0/2000][Batch 164/225][AE+C loss: 1.449806][M loss: 0.256034][D loss: 0.015548][G loss 11.862018 ]time: 0:00:36.899636 
[Epoch 0/2000][Batch 165/225][AE+C loss: 1.485378][M loss: 1.806137][D loss: 0.014278][G loss 30.615597 ]time: 0:00:37.109007 
[Epoch 0/2000][Batch 166/225][AE+C loss: 1.490083][M loss: 0.880939][D loss: 0.033200][G loss 24.206865 ]time: 0:00:37.324259 
[Epoch 0/2000][Batch 167/225][AE+C loss: 1.498075][M loss: 2.075677][D loss: 0.032928][G loss 35.316948 ]time: 0:00:37.503963 
[Epoch 0/2000][Batch 168/225][AE+C loss: 1.491791][M loss: 1.680363][D loss: 0.028247][G loss 26.307590 ]time: 0:00:37.709477 
[Epoch 0/2000][Batch 169/225][AE+C loss: 1.440341][M loss: 2.077980][D loss: 0.026901][G loss 31.775040 ]time: 0:00:37.918458 
[Epoch 0/2000][Batch 170/225][AE+C loss: 1.475197][M loss: 1.927784][D loss: 0.014756][G loss 26.560074 ]time: 0:00:38.140484 
[Epoch 0/2000][Batch 171/225][AE+C loss: 1.475424][M loss: 2.694654][D loss: 0.015794][G loss 32.703648 ]time: 0:00:38.360072 
[Epoch 0/2000][Batch 172/225][AE+C loss: 1.423478][M loss: 1.018493][D loss: 0.004727][G loss 19.880051 ]time: 0:00:38.547887 
[Epoch 0/2000][Batch 173/225][AE+C loss: 1.420317][M loss: 2.272273][D loss: 0.025542][G loss 15.958241 ]time: 0:00:38.742537 
[Epoch 0/2000][Batch 174/225][AE+C loss: 1.477895][M loss: 1.448658][D loss: 0.034502][G loss 21.944344 ]time: 0:00:38.941680 
[Epoch 0/2000][Batch 175/225][AE+C loss: 1.449851][M loss: 1.476827][D loss: 0.001166][G loss 20.314661 ]time: 0:00:39.148714 
[Epoch 0/2000][Batch 176/225][AE+C loss: 1.431783][M loss: 1.534224][D loss: 0.005289][G loss 28.477680 ]time: 0:00:39.353481 
[Epoch 0/2000][Batch 177/225][AE+C loss: 1.413558][M loss: 1.496978][D loss: 0.012489][G loss 27.147211 ]time: 0:00:39.563333 
[Epoch 0/2000][Batch 178/225][AE+C loss: 1.377033][M loss: 0.701809][D loss: 0.000000][G loss 21.143543 ]time: 0:00:39.760207 
[Epoch 0/2000][Batch 179/225][AE+C loss: 1.355359][M loss: 1.391323][D loss: 0.002134][G loss 24.873211 ]time: 0:00:39.951159 
[Epoch 0/2000][Batch 180/225][AE+C loss: 1.423246][M loss: 1.340782][D loss: 1.754348][G loss 24.046436 ]time: 0:00:40.123683 
[Epoch 0/2000][Batch 181/225][AE+C loss: 1.530428][M loss: 0.915741][D loss: 1.780370][G loss 25.006317 ]time: 0:00:40.330172 
[Epoch 0/2000][Batch 182/225][AE+C loss: 1.569605][M loss: 0.581573][D loss: 1.680162][G loss 33.140324 ]time: 0:00:40.532919 
[Epoch 0/2000][Batch 183/225][AE+C loss: 1.487568][M loss: 1.094937][D loss: 1.623516][G loss 24.125126 ]time: 0:00:40.740503 
[Epoch 0/2000][Batch 184/225][AE+C loss: 1.470519][M loss: 0.582368][D loss: 1.577470][G loss 23.001509 ]time: 0:00:40.936521 
[Epoch 0/2000][Batch 185/225][AE+C loss: 1.516544][M loss: 0.726927][D loss: 1.522871][G loss 16.787659 ]time: 0:00:41.119452 
[Epoch 0/2000][Batch 186/225][AE+C loss: 1.450049][M loss: 1.009471][D loss: 1.472436][G loss 25.187275 ]time: 0:00:41.336981 
[Epoch 0/2000][Batch 187/225][AE+C loss: 1.390625][M loss: 0.817455][D loss: 1.333670][G loss 15.220496 ]time: 0:00:41.526562 
[Epoch 0/2000][Batch 188/225][AE+C loss: 1.393305][M loss: 1.833590][D loss: 1.252780][G loss 27.006477 ]time: 0:00:41.701308 
[Epoch 0/2000][Batch 189/225][AE+C loss: 1.362799][M loss: 1.014448][D loss: 1.182956][G loss 22.636780 ]time: 0:00:41.878091 
[Epoch 0/2000][Batch 190/225][AE+C loss: 1.349593][M loss: 0.515139][D loss: 1.072776][G loss 16.281399 ]time: 0:00:42.053458 
2025-07-07 17:34:26.992133: W tensorflow/core/grappler/utils/graph_view.cc:849] No registered '_MklLayerNorm' OpKernel for GPU devices compatible with node {{node model_3/layer_normalization_9/add}}
	.  Registered:  device='CPU'; T in [DT_HALF]
  device='CPU'; T in [DT_BFLOAT16]
  device='CPU'; T in [DT_FLOAT]

2025-07-07 17:34:26.993697: W tensorflow/core/grappler/utils/graph_view.cc:849] No registered '_MklLayerNorm' OpKernel for GPU devices compatible with node {{node model_3/layer_normalization_7/add}}
	.  Registered:  device='CPU'; T in [DT_HALF]
  device='CPU'; T in [DT_BFLOAT16]
  device='CPU'; T in [DT_FLOAT]

2025-07-07 17:34:26.994195: W tensorflow/core/grappler/utils/graph_view.cc:849] No registered '_MklLayerNorm' OpKernel for GPU devices compatible with node {{node model_3/layer_normalization_6/add}}
	.  Registered:  device='CPU'; T in [DT_HALF]
  device='CPU'; T in [DT_BFLOAT16]
  device='CPU'; T in [DT_FLOAT]

[Epoch 0/2000][Batch 191/225][AE+C loss: 1.367892][M loss: 1.101362][D loss: 1.088952][G loss 19.010847 ]time: 0:00:42.220991 
[Epoch 0/2000][Batch 192/225][AE+C loss: 1.334609][M loss: 1.322324][D loss: 0.916726][G loss 17.598236 ]time: 0:00:42.408985 
[Epoch 0/2000][Batch 193/225][AE+C loss: 1.322744][M loss: 1.929853][D loss: 0.792045][G loss 15.845523 ]time: 0:00:42.603797 
[Epoch 0/2000][Batch 194/225][AE+C loss: 1.317988][M loss: 3.134123][D loss: 0.654534][G loss 22.412378 ]time: 0:00:42.814361 
[Epoch 0/2000][Batch 195/225][AE+C loss: 1.516581][M loss: 0.728190][D loss: 0.301989][G loss 20.196371 ]time: 0:00:43.022051 
[Epoch 0/2000][Batch 196/225][AE+C loss: 1.513150][M loss: 0.987150][D loss: 0.291879][G loss 26.246084 ]time: 0:00:43.233931 
[Epoch 0/2000][Batch 197/225][AE+C loss: 1.514173][M loss: 0.716043][D loss: 0.278738][G loss 23.664627 ]time: 0:00:43.433136 
[Epoch 0/2000][Batch 198/225][AE+C loss: 1.505133][M loss: 0.777079][D loss: 0.200009][G loss 22.917395 ]time: 0:00:43.630721 
[Epoch 0/2000][Batch 199/225][AE+C loss: 1.492586][M loss: 0.551306][D loss: 0.071085][G loss 23.166500 ]time: 0:00:43.850305 
[Epoch 0/2000][Batch 200/225][AE+C loss: 1.499050][M loss: 0.397792][D loss: 0.151141][G loss 25.857473 ]time: 0:00:44.065276 
[Epoch 0/2000][Batch 201/225][AE+C loss: 1.480434][M loss: 0.908373][D loss: 0.094500][G loss 22.134230 ]time: 0:00:44.259738 
[Epoch 0/2000][Batch 202/225][AE+C loss: 1.472403][M loss: 0.547941][D loss: 0.073678][G loss 17.498898 ]time: 0:00:44.442941 
[Epoch 0/2000][Batch 203/225][AE+C loss: 1.466835][M loss: 0.782444][D loss: 0.065167][G loss 21.350746 ]time: 0:00:44.618791 
[Epoch 0/2000][Batch 204/225][AE+C loss: 1.448915][M loss: 0.869618][D loss: 0.025650][G loss 22.311935 ]time: 0:00:44.791549 
[Epoch 0/2000][Batch 205/225][AE+C loss: 1.451187][M loss: 0.781857][D loss: 0.043934][G loss 17.630230 ]time: 0:00:44.967907 
[Epoch 0/2000][Batch 206/225][AE+C loss: 1.433018][M loss: 0.684233][D loss: 0.026777][G loss 18.693050 ]time: 0:00:45.157756 
[Epoch 0/2000][Batch 207/225][AE+C loss: 1.427864][M loss: 0.421664][D loss: 0.030363][G loss 22.218649 ]time: 0:00:45.342102 
[Epoch 0/2000][Batch 208/225][AE+C loss: 1.420484][M loss: 0.477250][D loss: 0.014454][G loss 14.159157 ]time: 0:00:45.542570 
[Epoch 0/2000][Batch 209/225][AE+C loss: 1.411430][M loss: 0.671545][D loss: 0.017686][G loss 16.138924 ]time: 0:00:45.729050 
[Epoch 0/2000][Batch 210/225][AE+C loss: 1.420111][M loss: 0.226624][D loss: 0.059881][G loss 15.840092 ]time: 0:00:45.901526 
[Epoch 0/2000][Batch 211/225][AE+C loss: 1.411088][M loss: 0.228336][D loss: 0.061830][G loss 16.707401 ]time: 0:00:46.074520 
[Epoch 0/2000][Batch 212/225][AE+C loss: 1.399974][M loss: 0.281703][D loss: 0.031556][G loss 14.586499 ]time: 0:00:46.266936 
[Epoch 0/2000][Batch 213/225][AE+C loss: 1.388360][M loss: 0.179497][D loss: 0.047342][G loss 20.903656 ]time: 0:00:46.462828 
[Epoch 0/2000][Batch 214/225][AE+C loss: 1.378657][M loss: 0.226956][D loss: 0.021831][G loss 11.093643 ]time: 0:00:46.636956 
[Epoch 0/2000][Batch 215/225][AE+C loss: 1.368258][M loss: 0.255631][D loss: 0.017705][G loss 16.343693 ]time: 0:00:46.821512 
[Epoch 0/2000][Batch 216/225][AE+C loss: 1.363207][M loss: 0.084878][D loss: 0.025542][G loss 15.929491 ]time: 0:00:47.025157 
[Epoch 0/2000][Batch 217/225][AE+C loss: 1.354347][M loss: 0.268633][D loss: 0.051842][G loss 16.161766 ]time: 0:00:47.204215 
[Epoch 0/2000][Batch 218/225][AE+C loss: 1.346129][M loss: 0.134714][D loss: 0.002539][G loss 12.934793 ]time: 0:00:47.424953 
[Epoch 0/2000][Batch 219/225][AE+C loss: 1.329839][M loss: 0.180445][D loss: 0.022584][G loss 10.723133 ]time: 0:00:47.622931 
[Epoch 0/2000][Batch 220/225][AE+C loss: 1.328215][M loss: 0.190325][D loss: 0.003300][G loss 11.336900 ]time: 0:00:47.831566 
[Epoch 0/2000][Batch 221/225][AE+C loss: 1.310674][M loss: 0.131026][D loss: 0.017708][G loss 16.799702 ]time: 0:00:48.044440 
[Epoch 0/2000][Batch 222/225][AE+C loss: 1.303550][M loss: 0.185783][D loss: 0.000000][G loss 17.371981 ]time: 0:00:48.225363 
[Epoch 0/2000][Batch 223/225][AE+C loss: 1.291089][M loss: 0.299839][D loss: 0.003563][G loss 14.611362 ]time: 0:00:48.409102 
[Epoch 0/2000][Batch 224/225][AE+C loss: 1.279470][M loss: 0.217227][D loss: 0.007546][G loss 15.861994 ]time: 0:00:48.590418 
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)
Traceback (most recent call last):
  File "/app/acgan_triplet.py", line 495, in <module>
    gan.train(epochs=2000, batch_size=32, log_file=log_file)
  File "/app/acgan_triplet.py", line 453, in train
    accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp = feature_generation_and_diagnosis(2000,testdata,test_attributelabel,self.autoencoder,self.g, self.c, test_class_indices)  
                                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/test.py", line 54, in feature_generation_and_diagnosis
    generated_feature = generator.predict([noise, attribute])
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/utils/traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/usr/local/lib/python3.12/dist-packages/tensorflow/python/eager/execute.py", line 53, in quick_execute
    tensors = pywrap_tfe.TFE_Py_Execute(ctx._handle, device_name, op_name,
  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tensorflow.python.framework.errors_impl.NotFoundError: Graph execution error:

Detected at node model_3/layer_normalization_6/add defined at (most recent call last):
  File "/app/acgan_triplet.py", line 495, in <module>

  File "/app/acgan_triplet.py", line 453, in train

  File "/app/test.py", line 54, in feature_generation_and_diagnosis

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/utils/traceback_utils.py", line 65, in error_handler

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/engine/training.py", line 2650, in predict

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/engine/training.py", line 2436, in predict_function

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/engine/training.py", line 2421, in step_function

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/engine/training.py", line 2409, in run_step

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/engine/training.py", line 2377, in predict_step

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/utils/traceback_utils.py", line 65, in error_handler

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/engine/training.py", line 588, in __call__

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/utils/traceback_utils.py", line 65, in error_handler

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/engine/base_layer.py", line 1136, in __call__

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/utils/traceback_utils.py", line 96, in error_handler

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/engine/functional.py", line 514, in call

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/engine/functional.py", line 671, in _run_internal_graph

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/utils/traceback_utils.py", line 65, in error_handler

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/engine/base_layer.py", line 1136, in __call__

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/utils/traceback_utils.py", line 96, in error_handler

  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/layers/normalization/layer_normalization.py", line 343, in call

No registered '_MklLayerNorm' OpKernel for 'GPU' devices compatible with node {{node model_3/layer_normalization_6/add}}
	.  Registered:  device='CPU'; T in [DT_HALF]
  device='CPU'; T in [DT_BFLOAT16]
  device='CPU'; T in [DT_FLOAT]

	 [[model_3/layer_normalization_6/add]] [Op:__inference_predict_function_1091383]

import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,LayerNormalization,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply, Add
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from sklearn.preprocessing import MinMaxScaler
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score


# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    # 限制TensorFlow只使用第一个GPU
    tf.config.set_visible_devices(gpus[0], 'GPU')
    # 设置显存按需增长
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    # 显存增长必须在GPU初始化之前设置
    print(e)


def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = LayerNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = LayerNormalization()(y)
    
    # 如果输入和输出维度不同，需要一个线性投影
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        x_reshaped = K.expand_dims(x, axis=1)
        q = self.query(x_reshaped)
        k = self.key(x_reshaped)
        v = self.value(x_reshaped)
        attention_scores = K.batch_dot(q, k, axes=[2, 2])
        attention_probs = K.softmax(attention_scores)
        context = K.batch_dot(attention_probs, v)
        context = K.squeeze(context, axis=1)
        return x + self.gamma * context

class CrossAttentionFusion(Layer):
    def __init__(self, units, **kwargs):
        super(CrossAttentionFusion, self).__init__(**kwargs)
        self.units = units

    def build(self, input_shape):
        data_feature_dim = input_shape[0][-1]
        semantic_feature_dim = input_shape[1][-1]
        
        self.query = Dense(self.units, use_bias=False)
        self.key = Dense(self.units, use_bias=False)
        self.value = Dense(self.units, use_bias=False)

        self.out_dense = Dense(data_feature_dim)
        super(CrossAttentionFusion, self).build(input_shape)

    def call(self, inputs):
        data_features, semantic_features = inputs
        
        q = self.query(K.expand_dims(data_features, axis=1))
        k = self.key(K.expand_dims(semantic_features, axis=1))
        v = self.value(K.expand_dims(semantic_features, axis=1))
        
        attention_scores = tf.matmul(q, k, transpose_b=True)
        attention_probs = tf.nn.softmax(attention_scores)
        
        context = tf.matmul(attention_probs, v)
        context = K.squeeze(context, axis=1)
        
        # 将注意力上下文与原始数据特征结合
        fused_output = self.out_dense(concatenate([data_features, context]))
        return fused_output

def supervised_contrastive_loss(labels, features, temperature=0.1):
    features = tf.math.l2_normalize(features, axis=1)
    batch_size = tf.shape(features)[0]
    
    labels = tf.reshape(labels, [-1, 1])
    mask = tf.cast(tf.equal(labels, tf.transpose(labels)), "float32")

    logits = tf.matmul(features, features, transpose_b=True) / temperature
    
    # for numerical stability
    logits_max = tf.reduce_max(logits, axis=1, keepdims=True)
    logits = logits - logits_max
    
    identity_mask = tf.eye(batch_size, dtype="float32")
    
    mask = mask - identity_mask # remove self-contrast
    
    exp_logits = tf.exp(logits)
    log_prob = logits - tf.math.log(tf.reduce_sum(exp_logits * (1 - identity_mask), axis=1, keepdims=True))

    mean_log_prob_pos = tf.reduce_sum(mask * log_prob, axis=1) / (tf.reduce_sum(mask, axis=1) + 1e-8)
    
    loss = -mean_log_prob_pos
    return tf.reduce_mean(loss)

class Zero_shot():
    def __init__(self):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256
        self.feature_shape=(self.feature_dim,)
        self.semantic_dim = 50 # Classifier's hidden output dim
        self.attribute_dim = 20
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        self.lambda_cla = 10 
        self.lambda_crl = 0.01
        self.lambda_scl = 1.0 # New hyperparameter for SCL
        
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        
        self.autoencoder= self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c= self.build_classifier()
        self.refiner = self.build_attribute_refiner()
        
    def build_attribute_refiner(self):
        attribute_input = Input(shape=(self.attribute_dim,), dtype='float32')
        x = Dense(self.attribute_dim * 2, activation='relu')(attribute_input)
        x = LayerNormalization()(x)
        refined_attribute = Dense(self.attribute_dim, activation='tanh')(x) # tanh to keep values in a controlled range
        # Use Add layer to make it a residual connection, helping to learn modifications
        output = Add()([attribute_input, refined_attribute]) 
        return Model(attribute_input, output, name="AttributeRefiner")

    def build_autoencoder(self):
      
      sample = Input(shape=self.sample_shape)     
     
      a0=sample

      # Encoder
      a1=Dense(100)(a0)
      a1=LeakyReLU(alpha=0.2)(a1)
      a1=LayerNormalization()(a1)

      a2=Dense(200)(a1)
      a2=LeakyReLU(alpha=0.2)(a2)
      a2=LayerNormalization()(a2)

      a3=Dense(self.feature_dim)(a2)
      a3=LeakyReLU(alpha=0.2)(a3)
      a3=LayerNormalization()(a3)
      feature=a3

      # Decoder
      a4=Dense(200)(feature)
      a4=LeakyReLU(alpha=0.2)(a4)
      a4=LayerNormalization()(a4)

      a5=Dense(100)(a4)
      a5=LeakyReLU(alpha=0.2)(a5)
      a5=LayerNormalization()(a5)

      a6=Dense(self.data_lenth)(a5)
      a6=LeakyReLU(alpha=0.2)(a6)
      a6=LayerNormalization()(a6)
      output_sample=a6

      autoencoder = Model(sample,[feature, output_sample], name="Autoencoder")
      self.encoder = Model(sample, feature, name="Encoder")
      return autoencoder    
        
    def build_discriminator(self):
        
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(self.attribute_dim,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input,attribute],validity, name="Discriminator")

    def build_generator(self):
      
      noise = Input(shape=self.noise_shape)
      attribute = Input(shape=(self.attribute_dim,), dtype='float32')
      
      noise_embedding = Flatten()(noise)
      attribute_embedding = Dense(self.latent_dim)(attribute)
      
      g_input = concatenate([noise_embedding, attribute_embedding])

      g1 = Dense(128)(g_input)
      g1 = LeakyReLU(alpha=0.2)(g1)
      g1 = LayerNormalization()(g1)

      g2 = residual_block(g1, 256) 
      g3 = residual_block(g2, 256) 
      
      g3_attention = SelfAttention()(g3)
      
      generated_feature = Dense(self.feature_dim)(g3_attention)
      generated_feature = BatchNormalization()(generated_feature)

      return Model([noise,attribute],generated_feature, name="Generator")
    
    def build_classifier(self):
        
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        
        c2=Dense(self.semantic_dim)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        hidden_ouput=c2
               
        c3 = Dense(self.attribute_dim, activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute], name="Classifier")

    def wasserstein_loss(self, y_true, y_pred):
            return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self,current_batch_features,y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(
                y_true, pred_attribute)
        
        mi_penalty=0    
        if self.bound == True:    
          mi_penalty = self.mi_penalty_loss(
              current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def cycle_rank_loss(self, anchor_features, reconstructed_features, other_features):
        pos_dist = tf.reduce_sum(tf.square(anchor_features - reconstructed_features), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor_features - other_features), axis=-1)
        basic_loss = pos_dist - neg_dist # No margin needed, just ranking
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss
    
    def train(self, epochs, batch_size, log_file=None):
        
        start_time = datetime.datetime.now()
        
        accuracy_list_1=[]
        accuracy_list_2=[]
        accuracy_list_3=[]
        accuracy_list_4=[]
        
        valid = -np.ones((batch_size,1) )
        fake = np.ones((batch_size,1) )
        
        PATH_train='./dataset_train_case1.npz'
        PATH_test='./dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        train_X_by_class = {i: train_data[f'training_samples_{i+1}'] for i in range(15)}
        train_Y_by_class = {i: train_data[f'training_attribute_{i+1}'] for i in range(15)}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        test_X_unseen_list = [test_data[f'testing_samples_{i}'] for i in [2, 7, 15]] # classes 2, 7, 15
        test_Y_unseen_list = [test_data[f'testing_attribute_{i}'] for i in [2, 7, 15]]
        test_labels_unseen_list = [np.full(len(d), c) for d, c in zip(test_X_unseen_list, [1, 6, 14])]
        
        test_X = np.concatenate(test_X_unseen_list)
        test_Y = np.concatenate(test_Y_unseen_list)
        test_labels = np.concatenate(test_labels_unseen_list)


        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        traindata=all_train_X
        train_attributelabel=all_train_Y
        train_classlabel = all_train_labels
        
        testdata=test_X
        test_attributelabel=test_Y
        test_classlabel = test_labels 
       
        num_batches=int(traindata.shape[0]/batch_size)
               
        for epoch in range(epochs):
            
            for batch_i in range(num_batches):
                
                start_i =batch_i * batch_size
                end_i=(batch_i + 1) * batch_size
                
                train_x=traindata[start_i:end_i]
                train_y=train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # === Train Autoencoder, Classifier, and Encoder via SCL ===
                trainable_weights_ac = self.autoencoder.trainable_weights + self.c.trainable_weights
                with tf.GradientTape() as tape_ac:
                  feature, output_sample = self.autoencoder(train_x, training=True)
                  autoencoder_loss = mean_squared_error(train_x,output_sample)      

                  hidden_ouput_c, predict_attribute_c = self.c(feature, training=True)
                  c_loss = self.classification_loss(feature, train_y, hidden_ouput_c, predict_attribute_c)
                  
                  scl_loss = self.lambda_scl * supervised_contrastive_loss(train_labels, feature)

                  total_ac_loss = autoencoder_loss + c_loss + scl_loss

                grads_ac = tape_ac.gradient(total_ac_loss, trainable_weights_ac)
                self.autoencoder_optimizer.apply_gradients(zip(grads_ac, trainable_weights_ac))


                # === Train Discriminator ===
                for _ in range(self.n_critic):
                  with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    refined_attributes = self.refiner(train_y, training=True)
                    
                    fake_feature = self.g([noise, refined_attributes], training=False)
                    real_feature = self.encoder(train_x, training=False)
        
                    real_validity = self.d([real_feature, refined_attributes], training=True)
                    fake_validity = self.d([fake_feature, refined_attributes], training=True)
                                           
                    d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                    d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                    d_loss = d_loss_real + d_loss_fake
                  
                  grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                  self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # === Train Generator and Attribute Refiner ===
                trainable_weights_g = self.g.trainable_weights + self.refiner.trainable_weights
                with tf.GradientTape() as tape_g:
                  noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                  refined_attributes_g = self.refiner(train_y, training=True)
                  
                  Fake_feature_g = self.g([noise_g, refined_attributes_g], training=True)
                  Fake_validity_g = self.d([Fake_feature_g, refined_attributes_g], training=False)
                  adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
            
                  fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g, training=False)
                  classification_loss = self.classification_loss(Fake_feature_g, train_y, fake_hidden_ouput_g, Fake_classification_g)
                  
                  scl_loss_g = self.lambda_scl * supervised_contrastive_loss(train_labels, Fake_feature_g)
                  
                  cycle_rank_loss = 0
                  if self.crl == True:
                    reconstructed_feature = self.g([noise_g, Fake_classification_g], training=True)
                    
                    shuffled_labels_idx = tf.random.shuffle(tf.range(batch_size))
                    unsimilar_refined_attributes = self.refiner(tf.gather(train_y, shuffled_labels_idx), training=True)
                    unsimilar_generated_feature = self.g([noise_g, unsimilar_refined_attributes], training=True)

                    cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                           
                  total_loss = adversarial_loss + self.lambda_cla * classification_loss + scl_loss_g + self.lambda_crl * cycle_rank_loss
                          
                grads_g = tape_g.gradient(total_loss, trainable_weights_g)
                self.g_optimizer.apply_gradients(zip(grads_g, trainable_weights_g))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print ("[Epoch %d/%d][Batch %d/%d][D loss: %f][G loss %f]time: %s " \
                 % (epoch, epochs,
                   batch_i, num_batches,
                     d_loss,
                     tf.reduce_mean(total_loss),                                                                                                              
                     elapsed_time))
        
            if epoch % 10 == 0 and epoch > 0: # Evaluate less frequently
                accuracy_results = feature_generation_and_diagnosis_with_fusion(
                    self, 2000, traindata, train_attributelabel, train_classlabel, testdata, test_attributelabel, test_classlabel
                )  
                accuracy_list_1.append(accuracy_results['svm']) 
                accuracy_list_2.append(accuracy_results['rf']) 
                accuracy_list_3.append(accuracy_results['knn'])
                accuracy_list_4.append(accuracy_results['mlp'])

                print("[Epoch %d/%d] [Accuracy_svm: %f] [Accuracy_rf: %f] [Accuracy_knn: %f][Accuracy_mlp: %f]"\
                  %(epoch, epochs,max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)))
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_svm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_rf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_knn: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
            
        best_accuracy = max([max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)])
        print('finished! best_acc:{:.4f}'.format(best_accuracy))
        if log_file:
            log_file.write(f'finished! best_acc:{best_accuracy:.4f}\n')
            log_file.flush()

def feature_generation_and_diagnosis_with_fusion(model, num_samples, train_X, train_Y_attr, train_Y_label, test_X, test_Y_attr, test_Y_label):
    
    seen_class_indices = np.unique(train_Y_label)
    unseen_class_indices = np.unique(test_Y_label)
    
    unseen_attributes = []
    for uc_idx in unseen_class_indices:
        attr = test_Y_attr[np.where(test_Y_label == uc_idx)[0][0]]
        unseen_attributes.append(attr)
    unseen_attributes = np.array(unseen_attributes)

    # 1. Generate features for unseen classes
    num_gen_per_class = num_samples // len(unseen_class_indices)
    gen_attributes = np.repeat(unseen_attributes, num_gen_per_class, axis=0)
    gen_labels = np.repeat(unseen_class_indices, num_gen_per_class, axis=0)
    
    noise_for_gen = tf.random.normal(shape=(len(gen_attributes), model.latent_dim, 1))
    
    refined_gen_attributes = model.refiner.predict(gen_attributes)
    generated_features = model.g.predict([noise_for_gen, refined_gen_attributes])
    
    # 2. Extract features from real training data (seen classes)
    real_features_seen = model.encoder.predict(train_X)
    
    # 3. Create the training set for the final classifier (real seen + fake unseen)
    X_train_final_data = np.concatenate([real_features_seen, generated_features])
    Y_train_final_labels = np.concatenate([train_Y_label, gen_labels])
    
    # 4. Get semantic features for fusion
    _, semantic_features_train = model.c.predict(X_train_final_data)
    
    # 5. Build and use the fusion model
    data_input = Input(shape=model.feature_shape, name="data_input")
    semantic_input = Input(shape=(model.attribute_dim,), name="semantic_input")
    fused_output = CrossAttentionFusion(units=128)([data_input, semantic_input])
    fusion_model = Model([data_input, semantic_input], fused_output)
    
    # Get fused training features
    X_train_fused = fusion_model.predict([X_train_final_data, semantic_features_train])
    
    # 6. Extract and fuse features for the test set (real unseen data)
    real_features_unseen = model.encoder.predict(test_X)
    _, semantic_features_unseen = model.c.predict(real_features_unseen)
    X_test_fused = fusion_model.predict([real_features_unseen, semantic_features_unseen])
    Y_test_labels = test_Y_label

    # 7. Train and evaluate classifiers
    classifiers = {
        'svm': SVC(),
        'rf': RandomForestClassifier(),
        'knn': KNeighborsClassifier(),
        'mlp': tf.keras.models.Sequential([
            Dense(128, activation='relu', input_shape=X_train_fused.shape[1:]),
            Dropout(0.5),
            Dense(len(np.unique(Y_train_final_labels)), activation='softmax')
        ])
    }
    
    accuracies = {}
    
    for name, clf in classifiers.items():
        if name == 'mlp':
            clf.compile(optimizer='adam', loss='sparse_categorical_crossentropy', metrics=['accuracy'])
            clf.fit(X_train_fused, Y_train_final_labels, epochs=50, batch_size=32, verbose=0)
            loss, acc = clf.evaluate(X_test_fused, Y_test_labels, verbose=0)
            accuracies[name] = acc
        else:
            clf.fit(X_train_fused, Y_train_final_labels)
            predictions = clf.predict(X_test_fused)
            accuracies[name] = accuracy_score(Y_test_labels, predictions)
            
    return accuracies

                
if __name__ == '__main__':
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + "_advanced.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"训练开始，日志将被记录到: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 训练日志 (Advanced Architecture)\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = Zero_shot()
        # Note: This advanced model is trained on all data (GZSL-like setting)
        # and then evaluated on a ZSL task.
        # This is a common setup to learn a rich feature space first.
        gan.train(epochs=2000, batch_size=512, log_file=log_file)

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"训练完成，日志已保存至: {log_filename}") 
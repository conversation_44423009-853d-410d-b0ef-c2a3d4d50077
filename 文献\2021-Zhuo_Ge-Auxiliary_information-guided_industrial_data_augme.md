# Auxiliary Information-Guided Industrial Data Augmentation for Any-Shot Fault Learning and Diagnosis

<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> \( {}^{12} \) , Senior Member, IEEE

Abstract-The label scarcity problem widely exists in industrial processes. In particular, samples of some fault types are extremely rare; even worse, the samples of certain faults cannot be accessed, but they may appear in the actual process. These two kinds of challenges together can be termed as any-shot learning problem in industrial fault diagnosis. In this article, taking the advantages of generative adversarial network, a generative approach is proposed to tackle the any-shot learning problem, which generates the abundant samples for those rare and inaccessible faults, and trains a strong diagnosis model. To reach this, an attribute space is built to introduce the auxiliary information, which achieves the diagnosis of unseen faults and makes the generated samples more resembled to the real data. Besides, an auxiliary loss of triplet form is introduced as a joint training loss term, further improving the quality of augmented data and diagnosis accuracy. Finally, the performance of model is verified by the experiments of a hydraulic system and Tennessee-Eastman process, the results of which show that our method performs excellently for both zero-shot and few-shot fault diagnosis problems.

Index Terms-Data augmentation, fault attributes, fault diagnosis, few-shot learning, zero-shot learning.

## I. INTRODUCTION

INDUSTRIAL process analytics diagnosis of various faults or working conditions plays an essential part, which can be regarded as the classification problems in the data-driven machine learning field. Plenty of approaches have been proposed for this purpose, some of which are based on traditional machine learning methods like Fisher discriminant analysis, singular value decomposition, and partial least squares, and others are derived from deep learning like deep belief networks, auto encoders (AEs), recurrent neural networks, and so on [1].

As for the classification tasks, the scarcity of samples seriously harms the performance of the models. Without exception, this problem widely exists in the field of the industrial fault diagnosis, mainly due to the hardness of collecting fault samples. The proposed approaches for handling this obstacle can be mainly classified into two aspects: algorithm and data. Algorithm approaches are to design the classifiers that can handle the classes with few samples. Semi-supervised learning can effectively utilize the unlabeled samples to learn more precise data distributions, such as semi-supervised ladder network [2] and multi-agent-based methodology [3]. Imbalanced learning algorithms like ensemble algorithms [4] and cost-sensitive gated neural networks [5] can trade off the minor classes and gain considerable accuracy on all classes. Data approaches first synthesize the virtual samples for augmenting the dataset and then use the augmented dataset with abundant samples to train conventional classifiers. Oversampling is a useful way to generate virtual samples; for example, Wei et al. [6] proposed cluster-majority-weighted minority oversampling technique for rolling bearing diagnosis. Other more effective data augmentation approaches are based on deep learning, and the generative adversarial network (GAN) is a representative one, due to its good performance on data generation. Next, the GAN and its application in industrial scenarios are briefly introduced.

GAN is a data generation method for learning the data distribution through an adversarial way, which consists of two networks: generator and discriminator. The generator is to produce realistic samples, and the discriminator is to evaluate the authenticity of the samples. When GAN is trained to the convergence, the generator can precisely learn the mapping from noise to real data distribution. Since the GAN was first proposed in 2014 for image generation [7], many researches of theories and applications have proven that GANs have the excellence ability of generating precise and diverse samples, for the data of other various fields. For instance, GANs are used for learning to play chess game [8], synthesizing electroencephalographic (EEG) brain signals [9], or malware examples [10]. In industrial scenarios, several GAN-based works are proposed for the different purposes as well; for example, GAN for the energy spectrum data generation on motor fault detection [11], 1-D convolutional ACGAN for bearing fault diagnosis [12], GAN-based data augmentation classifiers for industrial process fault classification [13], [14], and bounded-GAN for gear safety reliability classification [15]. These works have empirically and theoretically shown the superiority of GAN over other generation or oversampling methods.

---

Manuscript received November 9, 2020; revised December 26, 2020; accepted January 15, 2021. Date of publication January 20, 2021; date of current version July 26, 2021. This work was supported in part by the National Key Research and Development Program of China under Grant 2018YFC0808600, in part by the National Natural Science Foundation of China (NSFC) under Grant 61833014 and Grant 61722310, in part by the Natural Science Foundation of Zhejiang Province under Grant LR18F030001, and in part by the Open Research Project of the State Key Laboratory of Industrial Control Technology, Zhejiang University under Grant ICT20041. Paper no. TII-20-5151. (Corresponding author: Zhiqiang Ge.)

The authors are with the State Key Laboratory of Industrial Control Technology, College of Control Science and Engineering, Zhejiang University, Hangzhou 310027, China, and also with the Peng Cheng Laboratory, Shenzhen 518000, China (e-mail: <EMAIL>; <EMAIL>).

Color versions of one or more figures in this article are available at https://doi.org/10.1109/TII.2021.3053106.

Digital Object Identifier 10.1109/TII.2021.3053106

---

However, among these existing methods for dealing with the scarce sample problems, there are mainly two shortages, which are as follows.

1) For the fault types not presented during the training procedure but appearing in the actual diagnosis, the models can not recognize these faults, which is referred as zero-shot problem.

2) When the labeled sample is extremely scarce (less than 10 per fault), most existing approaches have poor performance due to the limited intraclass information, that is, few-shot problem.

The zero-shot and few-shot problems together can be named as any-shot problem. We analyze the main reason why the most existing methods cannot handle any-shot problem: the construction and training process of these models are only based on the dataset itself, which constrains the models from introducing the extrinsic auxiliary information beyond the train set. When encountering the faults not in the train set, these methods cannot diagnose or generate them, due to the absence of the extrinsic information about these unseen faults. When it comes to few-shot problem, the situation may be a little better, since there is no unseen faults and some semi-supervised methods can make full use of the large unlabeled samples. However, most of them still get poor performance with the extreme scarce samples, since the information provided by handful samples is not enough for these methods to learn an accurate data distribution.

We start from the perspective of synthesizing data, and mainly focus on introducing the extrinsic auxiliary information of all fault for guiding the generative networks, for the purpose of solving the any-shot problem in industrial fault diagnosis, especially the zero-shot problem, which is seldom concerned before in the industrial field. In general, our method only utilizes the limited labeled data (unlabeled samples are out of this work's range) and the corresponding extrinsic information of fault attributes, for training a data generation model and an any-shot classification model subsequently. Specifically, our method takes the advantages of good diversity and generalization of GANs, and introduces the extrinsic auxiliary information of fault attributes through constructing an attribute space. Through building the mapping from fault attributes to corresponding samples, the samples of all faults including those do not exist in the train set can be synthesized by the generator of GAN. Based on the augmented dataset with virtual samples, classifiers can be trained for diagnosing the faults outside the original train set. This generative adversarial model using fault attributes for any-shot problem is referred as FAGAN. And the main contributions of this article are threefold.

1) A generative model with fault attribute space is proposed, which has the ability to synthesize any kinds of fault instances, including those that do not appear in the train set. It is a data augmentation method that can both tackle zero-shot and few-shot problems in fault diagnosis.

2) An auxiliary loss of triplet form is introduced into the original GAN-based loss function, which significantly enhances the quality of generated samples.

3) For the few-shot problem, a fine-tune skill is applied on the minor faults with few samples, which makes a further improvement on fault classification.

The rest of this article is organized as follows. Section II introduces the background and some previous works. Section III demonstrates the architecture and training procedure details of FAGAN and its implementation for fault diagnosis. In Sections IV and V, the experiments of two datasets under various settings are presented to verify the superiority of FAGAN from multiple aspects. Section VI concludes this article.

## II. BACKGROUND AND RELATED WORKS

In this section, the background and related works of zero-shot learning (ZSL) and few-shot learning (FSL) as well as GANs and Triplet loss are demonstrated.

## A. Any-Shot Learning

Any-shot learning is a concept consisting of two classification learning problems, ZSL and FSL. This subsection, respectively, discusses these two tasks and their corresponding expressions for fault diagnosis.

1) Zero-Shot Learning: One of the challenges for the classification models is to distinguish whether an instance is belonged to the class set of the training instances. If the classes of instances do not have the corresponding instances in training set, in ZSL, these classes are referred as the unseen classes; otherwise, the classes covered by the training instances are referred as the seen classes. Here, some notations are listed for the clearance of the ZSL definition. \( \mathcal{S} = \left\{  {{c}_{i}^{s} \mid  i = 1,\ldots ,{N}_{s}}\right\} \) denotes the set of seen classes, when it comes to the industrial fault diagnosis, and \( {c}_{i}^{s} \) is the seen or accessible faults during industrial model construction. \( \mathcal{U} = \left\{  {{c}_{i}^{u} \mid  i = 1,\ldots ,{N}_{u}}\right\} \) denotes the set of unseen classes, where \( {c}_{i}^{u} \) is the unseen or inaccessible faults. The intersection of these two sets is empty, \( \mathcal{S} \cap  \mathcal{U} = \varnothing \) . The train set composed of seen class instances is denoted as \( {D}^{tr} = \left\{  {\left( {{x}_{i}^{tr},{y}_{i}^{tr}}\right)  \in  \mathcal{X} \times  \mathcal{S}}\right\} \) , where \( {x}_{i}^{tr} \in  \mathcal{X},{y}_{i}^{tr} \in  \mathcal{S} \) , and \( \mathcal{X} \) is the sample space. Correspondingly, the testing set composed of unseen class instances is denoted as \( {D}^{te} = \left\{  {\left( {{x}_{i}^{te},{y}_{i}^{te}}\right)  \in  \mathcal{X} \times  \mathcal{U}}\right\} \) . Based on the notations above, the definition of ZSL of fault diagnosis is given.

ZSL: Given training set \( {D}^{tr} \) with seen faults \( \mathcal{S},\mathrm{{ZSL}} \) is to learn a diagnosis model \( f\left( x\right)  : X \rightarrow  U \) , which can diagnose test set \( {D}^{te} \) of the unseen faults \( \mathcal{U} \) .

Generalized ZSL (GZSL): Given training set \( {D}^{tr} \) with seen faults \( \mathcal{S} \) , generalized ZSL is to learn a diagnosis model \( f\left( x\right) \) : \( X \rightarrow  \mathcal{S} \cup  \mathcal{U} \)

The definition of GZSL means, during the diagnosis procedure, the model need to diagnose not only unseen faults but the seen faults in the train set. GZSL is a more realistic and challenging task than ZSL.

Attribute space is the core part of ZSL, also referred as semantic space in other fields like computer vision or natural language processing. It contains all auxiliary information about the seen and unseen classes, which is the guarantee for the model to learn the unseen faults. In most works of ZSL, vectors called prototypes serve as the description and representations of all classes. The prototypes are based on the information beyond the instances, such as the expert knowledge about the class labels, and in this work, we call them fault attributes.

Attribute space: Denote \( \mathcal{T} \in  {\mathbb{R}}^{M} \) as the fault attributes in attribute space, \( M \) is the space dimension. The elements in attribute space correspond to fault class label sets, \( \pi \left( \cdot \right)  : \mathcal{T} \rightarrow \) \( \mathcal{S} \cup  \mathcal{U} \) , where \( \pi \left( \cdot \right) \) is usually a bijection function that takes fault class label as input and output the corresponding attribute. Denote \( {t}_{i}^{s} = \pi \left( {c}_{i}^{s}\right) \) as fault attribute for seen fault and \( {t}_{i}^{u} = \pi \left( {c}_{i}^{u}\right) \) for unseen fault, where \( {t}_{i}^{s},{t}_{i}^{u} \in  \mathcal{T} \) and \( {c}_{i}^{s} \in  \mathcal{S},{c}_{i}^{u} \in  \mathcal{U} \) .

Under the ZSL problem settings, many works have been proposed. Direct attribute prediction [16] model is one of the early works, which trains classifiers for each dimension in the attribute space, finding the most similar prototypes in attribute space with the test samples. Another main idea of considerable ZSL works is to learn a mapping between attribute space and instance space, and then train a classifier for the unseen classes with the corresponding prototypes, such as attribute label embedding (ALE) [17], embarrassingly simple zero-shot learning (ESZSL) [18], structured joint embedding (SJE) [19], and so on. Another insight of solving ZSL problem is based on instance generation. Guo et al. [20] used Gaussian hypothesis for each class, and, according to the relationships among the seen and unseen classes, estimated the distribution of unseen class instances. The pseudo instances can also be synthesized through conditional data generation models, such as GAN [21] and variational auto encoding (VAE) [22].

2) Few-Shot Learning (FSL): FSL is a task of classification as well, but all classes of testing set are seen in the training process, so the unseen fault set \( \mathcal{U} = \varnothing \) . In FSL of fault diagnosis, instances of some faults are quite scarce, and not enough to support traditional classifiers. These faults are called minor faults, denoted as \( \mathcal{M} = \left\{  {{c}_{i}^{m} \mid  i = 1,\ldots ,{N}_{m}}\right\}  ,\mathcal{M} \in  \mathcal{S} \) . The diagnosis model to be learned is \( f\left( x\right)  : X \rightarrow  M \) .

FSL is similar to the semi-supervised or imbalanced classification problems, but the labeled instances quantity is much less and the task is more challenging as well. The key challenge of FSL is overfitting. Extremely scarce instances would cause catastrophic performance on test set. The prevalent strategy is to learn a classifier from classes with abundant instances and transfer to classes with few instances. Meta-learning is of great use to this problem, since it obtains the ability of solving tasks during training, and generalizes to new tasks that never learned before; for example, model-agnostic meta-learning [23], and meta long short-term memory using fine-tune skill [24]. Another FSL strategy is based on the metric learning, the core idea of which is to learn an embedding function and use similarity metrics of embeddings to classify the instances. Matching networks, prototypical networks, and relation networks are the examples [25].

In summary, any-shot learning can be regarded as the transfer learning, which transfers the knowledge learned from the classes with abundant samples to the ones with none or few samples. And for ZSL, the extrinsic information is necessary for transferring to the unseen classes, while many FSL methods do not need the auxiliary space.

## B. Generative Adversarial Networks

In the basic GAN by Goodfellow et al., the discriminator \( D \) and generator \( G \) are both fully connected network. They perform a zero-sum game, which means a minimax optimization. The value function \( V\left( {D, G}\right) \) of generator and discriminator is as follows:

\[
\mathop{\min }\limits_{G}\mathop{\max }\limits_{D}V\left( {D, G}\right)  = {\mathbb{E}}_{x \sim  {p}_{\text{data }}\left( x\right) }\left\lbrack  {\log D\left( x\right) }\right\rbrack
\]

\[
+ {\mathbb{E}}_{z \sim  {p}_{z}\left( z\right) }\left\lbrack  {\log \left( {1 - D\left( {G\left( z\right) }\right) }\right) }\right\rbrack   \tag{1}
\]

where \( x \in  X \) is sampled from the real data distribution \( {p}_{\text{data }}\left( x\right) \) , and \( z \in  Z \) is sampled from the Gaussian random distribution \( {p}_{z}\left( z\right) \) . Goodfellow pointed out that the another form of loss function can be derived as

\[
\text{loss} = {2JS}\left( {{p}_{g}\left( x\right) \parallel {p}_{\text{data }}\left( x\right) }\right)  - 2\log 2 \tag{2}
\]

where \( {JS}\left( {\cdot \parallel  \cdot  }\right) \) denotes the Jensen-Shannon divergence, which is a method of measuring the similarity between two probability distributions and achieves the minimum zero when two distributions equal everywhere. It can be seen that the optimization goal of the generator in GAN can be understood as the JS divergence between \( {p}_{\text{data }}\left( x\right) \) and \( {p}_{g}\left( x\right) \) , and the global optimum is exactly \( {p}_{\text{data }}\left( x\right)  = {p}_{g}\left( x\right) \) .

Auxiliary classifier GAN (ACGAN) [26] was proposed to control the class of generated data, by adding an extra classification branch at the output end of the basic discriminator. Meanwhile, the generator and discriminator both train to maximize the likelihood of classification branch, as the additional optimization term to the basic GAN value function. The likelihood of discriminator’s classification branch \( {L}_{C} \) can be written as

\[
{L}_{C} = \mathbb{E}\left\lbrack  {\log P\left( {C = c \mid  {X}_{\text{real }}}\right) }\right\rbrack   + \mathbb{E}\left\lbrack  {\log P\left( {C = c \mid  {X}_{\text{fake }}}\right) }\right\rbrack  . \tag{3}
\]

With the help of extra classifier structure, the ACGAN produces excellent label-controlled generated data and stable training process [27]. The advantage that ACGAN can be controlled under certain labels is what the most oversampling techniques like SMOTE do not have, which is also the precondition for synthesizing the unseen fault samples. Hence, we take the ACGAN as the basic structure for any-shot fault diagnosis guided by fault attributes.

## C. Triplet Loss

The Triplet loss was first proposed to learn better embeddings for face recognition, where the model is called FaceNet [28]. The Triplet loss minimizes the distance between the anchor and a positive, and meanwhile maximizes the distance between the anchor and a negative, where the anchor has the same class label with the positive and different label with the negative. The basic idea of Triplet loss is to make embeddings of the same kind close and embeddings of different kinds far. The schematic diagram and formulation of Triplet loss are as Fig. 1 and (4).

\[
{L}_{\text{triplet }} = \mathop{\sum }\limits_{i}^{N}\left\lbrack  {\begin{Vmatrix}f\left( {x}_{i}^{a}\right)  - f\left( {x}_{i}^{p}\right) \end{Vmatrix}}_{2}^{2}\right.
\]

\[
- {\begin{Vmatrix}f\left( {x}_{i}^{a}\right)  - f\left( {x}_{i}^{n}\right) {\parallel }_{2}^{2} + \alpha \end{Vmatrix}}_{ + } \tag{4}
\]

where \( \alpha \) is a margin enforced between positive and negative pairs, and \( {\left\lbrack  \cdot \right\rbrack  }_{ + } \) means \( \max \left( {\cdot ,0}\right) \) .

![bo_d1hstkk601uc738hh8ug_3_169_183_645_286_0.jpg](images/bo_d1hstkk601uc738hh8ug_3_169_183_645_286_0.jpg)

Fig. 1. Schematic diagram of Triplet loss.

## III. METHODOLOGY

This section thoroughly describes our method. Fig. 2 illustrates the overview of FAGAN for any-shot fault diagnosis, which mainly consists of four steps.

1) Preprocess the collected historical data. Since the dimension of some dataset is quite large, dimensional reduction or feature extraction techniques should be applied on the original dataset for the next training step. For example, linear discriminant analysis (LDA), principal component analysis, and AE are the common ways for the dimensional reduction purpose. Subsequently, normalizing the data to range \( \left\lbrack  {0,1}\right\rbrack \) is also necessary for training a better GAN model.

2) Train FAGAN. It is the essential part of our method to train the FAGAN under the guidance of the fault attributes and auxiliary loss, which will be presented in detail next.

3) Train diagnosis model. The well-trained generator of FAGAN can synthesize the augmented dataset of all faults, under the guidance of fault attributes. And subsequently, this synthesized dataset can be used to train a strong diagnosis model for all seen and unseen faults.

4) Online diagnose. With the diagnosis model trained by FAGAN, we gain the ability to online diagnosis the current work conditions, even if they are the fault conditions never met before. Naturally, the online data should be also preprocessed in the same way before being input to the diagnosis model.

The rest of this section mainly describes the FAGAN model architecture and training procedure for any-shot learning. Next, the approach of training diagnosis model is demonstrated. Finally, the assumptions and limitations of this work are discussed.

## A. FAGAN Model Architecture

Fig. 3 further depicts the FAGAN model in detail. The model architecture of FAGAN can be mainly divided into two parts: generator \( G \) and discriminator \( D \) . The structures and loss functions will be demonstrated below.

1) Discriminator \( D \) : Discriminator \( D \) takes the generated data from \( G \) or the real data, which is being forwarded through first few shared layers. Subsequently, there are two branches connected to the shared network: 1) One is the layers for a binary classifier to predict the probabilities that the input samples are real, denoted as \( {D}_{d}\left( \cdot \right) \) ; 2) Another branch is for a multiclass classifier with the Softmax output layer, to predict the probabilities that input samples belong to each fault type, denoted as \( {D}_{c}\left( \cdot \right) \) . Thus, the loss of discriminator can be written as

\[
{L}_{D} =  - \mathbb{E}\left\lbrack  {\log {D}_{d}\left( x\right) }\right\rbrack   - \mathbb{E}\left\lbrack  {\log \left( {1 - {D}_{d}\left( {G\left( {z,\pi \left( {c}^{s}\right) }\right) }\right) }\right. }\right\rbrack
\]

\[
+ H\left( {{c}^{s},{D}_{c}\left( X\right) }\right)  + H\left( {{c}^{s},{D}_{c}\left( {G\left( {z,\pi \left( {c}^{s}\right) }\right) }\right) }\right)  \tag{5}
\]

where \( H\left( {p, q}\right)  =  - \mathop{\sum }\limits_{x}p\left( x\right) \log q\left( x\right) \) denotes the cross entropy between two distributions, and \( \pi \left( {c}^{s}\right)  = {t}^{s} \) represents the corresponding attribute \( {t}^{s} \) to the fault type \( {c}^{s} \) . Actually, the loss of discriminator in FAGAN can be interpreted as two cross entropy of two branches, with two different inputs: real and generated data.

2) Generator \( G \) : Unlike nonconditional GANs, the generator of FAGAN takes the concatenation of the random noise and fault attributes as input. By input different fault attributes, we can control the fault types of generated samples, denoted as \( G\left( {z,\pi \left( {c}^{s}\right) }\right) \) . The noises \( z \) are randomly sampled from Gaussian distribution \( N\left( {0,1}\right) \) . The goal of generator is to generate the fake samples that make the discriminator branch \( {D}_{d} \) believe it is real and make the classification branch \( {D}_{c} \) correctly classify it. Thus, the loss function of generator is

\[
{L}_{G} = \mathbb{E}\left\lbrack  {\log \left( {1 - {D}_{d}\left( {G\left( {z,\pi \left( {c}^{s}\right) }\right) }\right) }\right. }\right\rbrack
\]

\[
+ H\left( {{c}^{s},{D}_{c}\left( {G\left( {z,\pi \left( {c}^{s}\right) }\right) }\right) }\right) \text{.} \tag{6}
\]

3) Auxiliary Loss: According to the results of experiments, classification accuracy of the basic model is not satisfactory and the generated samples show that the model did not fully capture the distribution of the real dataset. The possible reason is that the number of training instances is insufficient. With only the training loss in Section III-A2, it is tough for the model to learn the distributions of all faults. Based on this, the Triplet loss is introduced to the generator as an auxiliary loss for helping generate more reliable instances.

For the Triplet loss added to the generator of FAGAN, the synthesized samples output by generator are referred as the anchor. And the positive or negative is represented by the fault centroids. The fault centroids are calculated by the mean sample vectors of each fault type in the real dataset, which means each seen fault has one corresponding fault centroid. The mapping function between faults and corresponding centroid can be denoted as

\[
\operatorname{Cen}\left( {c}_{i}^{s}\right)  = \frac{1}{N}\mathop{\sum }\limits_{j}^{N}{x}_{i}^{j},{x}_{i}^{j}\text{ belongs to }{c}_{i}^{s}. \tag{7}
\]

The distances between generated instances and fault centroids are measured in Euclidean norm, so the Triplet loss of FAGAN can be written in the formulation as

\[
{L}_{tri} = \sum \left\lbrack  {\begin{Vmatrix}G\left( z,\pi \left( {c}_{i}^{s}\right) \right)  - \operatorname{Cen}\left( {c}_{i}^{s}\right) \end{Vmatrix}}_{2}^{2}\right.
\]

\[
{\left. -{\begin{Vmatrix}G\left( z,\pi \left( {c}_{i}^{s}\right) \right)  - \operatorname{Cen}\left( {c}_{n}^{s}\right) \end{Vmatrix}}_{2}^{2} + \alpha \right\rbrack  }_{ + } \tag{8}
\]

where \( \operatorname{Cen}\left( {c}_{i}^{s}\right) \) denotes positive fault centroids and \( \operatorname{Cen}\left( {c}_{n}^{s}\right) \) means the negative, where \( n \neq  i \) .

![bo_d1hstkk601uc738hh8ug_4_293_184_1140_631_0.jpg](images/bo_d1hstkk601uc738hh8ug_4_293_184_1140_631_0.jpg)

Fig. 2. Overview of FAGAN for fault diagnosis.

![bo_d1hstkk601uc738hh8ug_4_298_919_1144_420_0.jpg](images/bo_d1hstkk601uc738hh8ug_4_298_919_1144_420_0.jpg)

Fig. 3. Model architecture of FAGAN.

For the Triplet loss, the choice of the negative is a major concern. According to the existing research [28], partly choosing semi-hard negatives \( \operatorname{dist}\left( {a, p}\right)  + \alpha  > \operatorname{dist}\left( {a, n}\right)  > \operatorname{dist}\left( {a, p}\right) \) together with the hard one \( \operatorname{dist}\left( {a, p}\right)  > \operatorname{dist}\left( {a, n}\right) \) is most suitable for training. Based on this, we proposed a novel way to choose the negatives using the fault attributes. The distance information in fault attribute space is utilized. The Euclidean distance between each fault attribute is calculated to get the \( N \) nearest fault attributes, and then, the negatives are randomly chosen as one of the top \( N \) closest faults to the anchor, which guarantees that the hard and semi-hard negatives are both used. This means, if \( \operatorname{Cen}\left( {c}_{n}^{s}\right) \) is negative for generated sample \( G\left( {z,\pi \left( {c}_{i}^{s}\right) }\right) \) , fault attribute \( \pi \left( {c}_{n}^{s}\right) \) must be the \( N \) closest one to \( \left. {\pi \left( {c}_{i}^{s}\right) }\right) \) , where \( N \) is the hyperparameter predefined before training.

The way we used to search the negative is much more efficient than some approaches that need to traverse the entire train set, and fully utilize the extrinsic information in the fault attribute space. Compared to the generator only guided by the classifier with Softmax, the Triplet loss combined with Softmax loss can provide more precise and subtler gradient, which is of great help for generating instances of all faults. Especially between the faults that are hard to distinguish, TP provides a significant interclass distinction information. This joint loss of Triplet and Softmax has been proven to have excellent performance especially in fine-grained classification in other fields [29], [30]. The experiments in Sections IV and V also show the feasibility and superiority of the Triplet loss in generative model for the any-shot fault diagnosis.

## B. FAGAN Training Procedure

Training FAGAN is alternately updating the parameters of the discriminator and generator. The very first step is calculating the centroids \( \operatorname{Cen}\left( {c}^{s}\right) \) of seen faults. In one loop, discriminator iterates \( K \) times and generator updates once, which can benefit the training stabilization [7]. The discriminator is optimized by the real training data \( {D}^{tr} \) and fake data \( \left\{  {G\left( {{z}_{i},\pi \left( {c}_{i}^{s}\right) }\right) }\right\} \) by the generator with loss function in (5). The generator is input by the random noise \( {z}_{i} \) and seen fault attributes \( \pi \left( {c}_{i}^{s}\right) \) , to synthesize the augmented samples, and optimized by loss \( {L}_{G} + {\lambda }_{tri}{L}_{tri} \) in (6) and (8), where \( {\lambda }_{tri} \) is the auxiliary loss weight.

As for FSL, the procedure is slightly different. When FAGAN is trained for FSL, each fault has at least one instance, which means the seen fault set \( \mathcal{S} \) contains all fault types. Thus, we can use fine-tune skill on FAGAN to make the model learn more precise knowledge on the minor faults \( {c}_{i}^{m} \in  \mathcal{S} \) . Specifically, we first train a FAGAN model on the whole dataset including minor faults, and next, we only use few samples of the minor faults to fine-tune the pretrained FAGAN with less learning rate, which makes the model transfer learned knowledge to those minor faults.

## C. Diagnosis Model Training

When the FAGAN has been well trained, the generator \( G \) is used for the augmented dataset generation, for the diagnosis model \( f\left( x\right) \) training. When the problem setting is different, the goal of diagnosis model also differs. As mentioned in Section II-A, ZSL and FSL only consider the unseen faults \( \mathcal{U} \) or minor faults \( \mathcal{M} \) , while the diagnosis model under generalized ZSL need to consider all faults \( \mathcal{S} \cup  \mathcal{U} \) . Thus, the synthesized data needed are also different: \( {D}^{syn} = G\left( {z,\pi \left( {c}_{i}^{u}\right) ,{c}_{i}^{u} \in  \mathcal{U}}\right. \) for ZSL, \( {D}^{syn} = G\left( {z,\pi \left( {c}_{i}^{t}\right) ,{c}_{i}^{t} \in  \mathcal{U}}\right. \) for GZSL, and \( {D}^{syn} = G\left( {z,\pi \left( {c}_{i}^{m}\right) }\right. \) , \( {c}_{i}^{m} \in  \mathcal{M} \) for FSL.

With synthesized data \( {D}^{\text{syn }} \) , the diagnosis model \( f\left( x\right) \) can be trained for different target fault set under different learning problem settings. The any-shot problem becomes traditional supervised classification task. For the classification methods, any supervised classifiers can be applied, while in this article, the nearest neighbor algorithm is simply used for demonstration of the FAGAN performance.

## D. Assumption and Limitation

The assumption of our method for any-shot fault diagnosis is that, the fault attributes must contain precise information about the corresponding fault samples. Since the fault attributes contain all the information about the unseen faults, the precise fault attributes are the guarantee for realistic synthesized unseen fault samples and the accurate diagnosis model. It is the same for the FSL. Learning the overall fault distribution from only few samples is impossible, so the fault attributes as extrinsic auxiliary information must be precise enough to guide the FAGAN. The next section further demonstrates the correlation between fault attributes and fault distributions, as well as why the precise fault attributes space is essential for an any-shot diagnosis model.

The limitation is on the other side. The performance of FAGAN depends on the fault attributes, so if the information in fault attributes is incorrect or ineffective, the quality of augmented data will dramatically reduce, which in turn damages the fault diagnosis model. Moreover, if a fault is not described by the fault attribute space, there is also no way for FAGAN to synthesize the samples of that fault.

## IV. EXPERIMENTS ON HYDRAULIC SYSTEM

## A. Dataset

The dataset used for experiments of FAGAN is collected from the sensors of a hydraulic system [31]. The hydraulic system consists of a primary working and a secondary cooling-filtration circuit which are connected via the oil tank. In the working circuit with main pump (electrical motor power \( {3.3}\mathrm{\;{kW}} \) ), different load levels are cyclically repeated with the proportional pressure relief valve. The dataset is collected by process sensors measuring pressures, volume flows, temperature, electrical power, and vibration with standard industrial \( {20}\mathrm{\;{mA}} \) current loop interfaces connected to a data acquisition system. Besides, there are three virtual sensors for cooling efficiency, cooling power, and system efficiency calculated from the physical sensor values. Sampling rates range from \( {100}\mathrm{{HZ}} \) (pressure) to \( 1\mathrm{{HZ}} \) (temperature) and the fault conditions of system are collected at the rate of 1/60 HZ. Due to the multiple sampling rates of hydraulic sensors, the article simply flattens the data, matching the \( 1/{60}\mathrm{{HZ}} \) rate of fault conditions, so that the original data has 2205 instances and 43680 dimensions \( \lbrack 8 \times  {60}\left( {1\mathrm{\;{Hz}}}\right)  + 2 \times  {600}\left( {{10}\mathrm{\;{Hz}}}\right)  + 7 \times  {6000} \) \( \left( {{100}\mathrm{\;{Hz}}}\right) \rbrack \) .

## B. Fault Attributes

Table I describes the attributes of fault conditions in hydraulic system. The total number of fault conditions is 144. Each fault condition has four properties: cooler condition, valve condition, internal pump leakage, and hydraulic accumulator. These four properties construct the fault attribute space of four dimension. Each fault condition has one corresponding unique vector in this space, which is called fault prototype or fault attribute in any-shot learning. Since the values of pump are not quantized, we use the numbers0,50, and 100 to represent inactive, slight, and fair leakage, respectively. When building the attribute space, the values of each attribute are standardized to the range of 0-100 , in order to eliminate the influence of units.

## C. Parameters

Table II lists all parameters of the FAGAN model and diagnosis procedure. \( \mathrm{{FC}}\left( {a, b}\right) \) denotes the fully connected layer with input dimension \( a \) and output dimension \( b \) , and the corresponding activation function is given after it, where ReLU denotes the rectified linear unit. The data flows from up layers to the bottom. Discriminator has two branches at the end layer: left for the discrimination branch and right for the classification branch.

## D. (Generalized) Zero-Shot Learning

In (generalized) zero-shot learning scenarios, different numbers of unseen faults are set to compare the accuracy of competing methods, and ablation study together with some visualization is made to further prove the superiority of our method. As for the classifier postaugmented, the k -nearest neighbor based on cosine similarity is chosen, which gets the best accuracy according to our experiments.

1) Comparison Study: In this subsection, some of representative approaches about ZSL are selected to compare with FAGAN, including SJE, ESZSL, and ALE. Besides, for evaluating the superiority of ACGAN structure over other generation models, we substitute our methods with conditional GAN [32] and retaining all other structures the same (fault attributes and Triplet loss), which is named as \( {FA} - {CGAN} \) with Triplet loss. And we also compare another representative generation model: conditional variational auto enocoder (CVAE) [33], which is also under the guidance of fault attributes and auxiliary loss for the reconstructed samples, and named as FA-CVAE with Triplet loss. Moreover, to verify the effectiveness of Triplet loss and GAN model, some ablation settings are experimented. First, we discard the discriminator and the classifier in FAGAN, and only retain the generator network with Triplet loss, which is simply guided by Triplet loss to learn a mapping from the noise and attribute space to sample space, referred as network with only Triplet loss. Second, two FAGAN models with Euclidean loss and no auxiliary loss are studied. Compared to Triplet loss, the Euclidean loss only minimizes the positive term \( \parallel G\left( {z, a}\right)  - \operatorname{Cen}\left( p\right) {\parallel }^{2} \) without considering the negative samples of different faults.

TABLE I

DESCRIPTION ABOUT THE FAULT ATTRIBUTES IN HYDRAULIC SYSTEM [31]

<table><tr><td>Attribute</td><td>Description</td><td>Related sensor</td><td>Possible values</td></tr><tr><td>Cooler</td><td>Cooling power decrease</td><td>Fan duty cycle of C1</td><td>3, 20, 100%</td></tr><tr><td>Valve</td><td>Switching characteristic degradation</td><td>Control current of V10</td><td>73, 80, 90, 100%</td></tr><tr><td>Pump</td><td>Internal leakage</td><td>Switchable bypass orifices (V9)</td><td>Inactive, slight, fair leakage</td></tr><tr><td>Accumulator</td><td>Gas leakage</td><td>A1-A4 with different pre-charge pressures</td><td>90, 100, 115, 130 bar</td></tr></table>

TABLE II

PARAMETERS FOR ANY-SHOT DIAGNOSIS ON HYDRAULIC SYSTEM

<table><tr><td colspan="2">Preprocess</td></tr><tr><td>reduced method: \( {LDA} \)</td><td>reduced dimension: 64</td></tr><tr><td colspan="2">FAGAN Architecture</td></tr><tr><td>Generator</td><td>Discriminator</td></tr><tr><td>FC(32(z)+4(t),256) LeakyReLU</td><td>FC(64,256) ReLU</td></tr><tr><td>FC(256,64) Tanh</td><td>FC(256,32) ReLU</td></tr><tr><td/><td>FC(32,1) \( \parallel \mathrm{{FC}}\left( {{32},{144}}\right) \) SoftMax</td></tr><tr><td colspan="2">FAGAN Training Procedure</td></tr><tr><td colspan="2">Triplet loss weight \( {\lambda }_{\text{tri }} : 4 \) , Triplet loss margin \( \alpha  : 1 \) , batch size:16, the nearest negative number \( N : 8, D \) step number in one loop \( K : 5 \) , learning rate: 0.0002, FSL fine-tune learning rate: 0.0001</td></tr><tr><td colspan="2">Diagnosis Model Training enerated samples number per fault: 60, the nearest neighbor number: 20</td></tr></table>

TABLE III

ACCURACY OF UNSEEN FAULTS

<table><tr><td>ACC-unseen faults</td><td>5-faults</td><td>10-faults</td><td>20-faults</td></tr><tr><td>ESZSL [18]</td><td>80.06%</td><td>62.06%</td><td>40.96%</td></tr><tr><td>SJE [19]</td><td>60.22%</td><td>39.60%</td><td>16.64%</td></tr><tr><td>ALE [17]</td><td>88.83%</td><td>69.10%</td><td>46.04%</td></tr><tr><td>FA-CVAE with Triplet loss</td><td>97.36%</td><td>82.45%</td><td>62.99%</td></tr><tr><td>FA-CGAN with Triplet loss</td><td>96.80%</td><td>74.51%</td><td>63.09%</td></tr><tr><td>Network with only triplet loss</td><td>96.83%</td><td>74.08%</td><td>59.36%</td></tr><tr><td>FAGAN without auxiliary loss</td><td>89.40%</td><td>60.91%</td><td>50.32%</td></tr><tr><td>FAGAN with Euclidean loss</td><td>97.79%</td><td>77.60%</td><td>68.77%</td></tr><tr><td>FAGAN with Triplet loss</td><td>98.02%</td><td>\( \mathbf{{84.68}\% } \)</td><td>73.15%</td></tr></table>

For the parameter of experiments, three numbers of unseen faults are chosen:5,10, and 20, and the unseen faults are randomly selected. All the accuracy results include the mean value of five runs of the model, which are shown in Table III. It is obvious that FAGAN with Triplet loss has the best performance among those methods, especially when the number of unseen faults goes higher. Our method reaches the considerable accuracy for the samples of fault types that never meet before, even when 20 faults of total 144 are inaccessible during training. The comparison with CVAE and CGAN shows the good structure property of ACGAN in our model for any-shot industrial fault diagnosis. The comparison between FAGANs with different auxiliary loss validates the effectiveness of Triplet loss. Also, according to the unseen accuracy of the network with only Triplet loss, the discriminator and classifier of GAN model are proved to be effective for guiding the generator synthesize better fault samples.

GZSL is an extension to ZSL. GZSL learns a classifier which can recognize set \( \mathcal{T} = \mathcal{S} \cap  \mathcal{U} \) of both the seen and unseen classes, while ZSL only focuses on the classification of the unseen classes \( \mathcal{U} \) . The seen-unseen accuracy curve (SUC) is a comprehensive and intuitive metric of GZSL proposed by Chao et al. [34], which plots the accuracy curves of the seen and unseen classes with the variation of weight coefficient between two test sets. The SUC and the corresponding area under it are presented as the metrics for evaluating the methods on the generalized zero-shot fault diagnosis. The comparison group keeps the same as the previous standard ZSL and two numbers of the unseen faults(5,10)are set. The results are shown in Fig. 4, which indicate that FAGAN with Triplet loss still gets the best performance on the trade-off metrics between the seen and unseen faults. And when the accuracy of the seen faults is taken into consideration, the metrics of models other than GAN-based models go down rapidly, which is mainly due to the large fault types of the hydraulic system and the capacity leakage of those models.

2) Visualization Study: In this subsection, some visualization studies have been done to intuitively validate the effectiveness and feasibility of our method.

First, we plot the t-distributed stochastic neighbor embedding (t-SNE) visualization of instances in Fig. 5, and the heatmap of the vectors in fault attribute space in Fig. 6. These two figures explain why fault attributes are closely related to the fault conditions, that is, the assumption in Section III-D. According to the plot of dimensional reduced data, different colors represent different fault types; it can be seen that samples with similar colors are clustered, while similar color means close fault ID and similar fault attributes. Hence, we can make the conclusion that the fault conditions adjacent in the data space are also close in the attribute space, which proves that the attribute space contains useful correlation information about the fault conditions and instances. The FAGAN is to model this correlation from attribute space to the data space based on the seen faults, and then transfer to the unseen faults.

Next, we visualize the generated data and real data, which are stacked as one dataset for t-SNE reduction, and then the 2-D representations of them are plotted. Fig. 7 plots the distribution of the unseen faults. The samples with red labels are generated by FAGAN without auxiliary loss, FAGAN with Euclidean loss, and FAGAN with Triplet loss, from Fig. 7(a)-(c) in order, while the points with black labels are the real samples. In the light of the figure, three FAGAN-based methods can generate more or less realistic samples of the classes that are never encountered during training, which proves that the fault attribute GAN is useful to transfer the learned knowledge from seen faults to the unseen. The FAGAN with Euclidean auxiliary loss greatly improves the quality of synthesized samples, but still does not perfectly match every fault mode in t-SNE space (mismatch Fault 72). As for our approach, the unseen fault samples generated under the Triplet loss are extremely resembled as the original fault samples, which is the basement for the diagnosis model's high accuracy on the unseen faults. This also demonstrates that the negative terms in Triplet loss maximizing the distance between different faults indeed work both the seen and unseen faults.

![bo_d1hstkk601uc738hh8ug_7_154_186_675_1076_0.jpg](images/bo_d1hstkk601uc738hh8ug_7_154_186_675_1076_0.jpg)

Fig. 4. Seen-unseen accuracy curve and the area under it.

![bo_d1hstkk601uc738hh8ug_7_232_1376_519_409_0.jpg](images/bo_d1hstkk601uc738hh8ug_7_232_1376_519_409_0.jpg)

Fig. 5. T-SNE visualization of fault instances.

![bo_d1hstkk601uc738hh8ug_7_136_1894_706_191_0.jpg](images/bo_d1hstkk601uc738hh8ug_7_136_1894_706_191_0.jpg)

Fig. 6. Heatmap of fault attributes in hydraulic system.

TABLE IV

ACCURACY OF FEW FAULTS

<table><tr><td>Fault</td><td colspan="2">5-faults</td><td colspan="2">10-faults</td><td colspan="2">20-faults</td></tr><tr><td>Shot</td><td>1-shot</td><td>3-shots</td><td>1-shot</td><td>3-shots</td><td>1-shot</td><td>3-shots</td></tr><tr><td>Softmax</td><td>78.46%</td><td>92.58%</td><td>70.30%</td><td>83.64%</td><td>76.36%</td><td>90.22%</td></tr><tr><td>FAGAN</td><td>79.44%</td><td>94.06%</td><td>69.32%</td><td>84.74%</td><td>78.44%</td><td>91.46%</td></tr><tr><td>ACGAN-ft</td><td>89.68%</td><td>97.96%</td><td>72.75%</td><td>85.73%</td><td>80.17%</td><td>93.58%</td></tr><tr><td>FA-CVAE-ft</td><td>92.92%</td><td>98.20%</td><td>77.24%</td><td>87.32%</td><td>82.06%</td><td>92.96%</td></tr><tr><td>FA-CGAN-ft</td><td>95.18%</td><td>98.48%</td><td>78.32%</td><td>88.67%</td><td>81.99%</td><td>95.69%</td></tr><tr><td>FAGAN-ft</td><td>95.78%</td><td>98.24%</td><td>\( \mathbf{{80.99}\% } \)</td><td>89.09%</td><td>\( \mathbf{{83.17}\% } \)</td><td>94.00%</td></tr></table>

## E. Few-Shot Learning

As described in Section II-A2, all faults are accessible in few-shot diagnosis, while some of which are supported on only few instances. In detail, with the pretrained model on the whole dataset, the fine-tune skill trains model only with the limited few samples of these minor faults in the train set. First, the parameters of the model are initiated by the parameters pretrained by the large data of major faults. Then, this model is trained only by the small data of minor faults, with smaller learning rate, until the models can fit well on these minor faults. The fivefold cross-validation is applied, to reasonably assess the generalization of the model. The few training instances of minor faults are randomly chosen without repetition and the remaining instances are used for testing. The pretrained model is fine-tuned with the few training instances and tested on the instances of the same minor faults. The whole process runs five times and the results reported are the averaged accuracies on the testing instances. The diagnosis model is run under two few fault instance quantities (1-shot and 3-shots) and three minor fault quantities (5-faults, 10-faults, and 20-faults). The most challenging minor faults are selected, which is why the accuracy is even lower than the zero-shot experiments.

Table IV reports the results, where Softmax presents the artificial neural networks with Softmax output layer, \( {ft} \) stands for the fine-tune skills applied in models, ACGAN-ft replaces the fault attributes in FAGAN with the one-hot label code, and \( {FA} \) - \( {CVAE} \) -ft, \( {FA} \) - \( {CGAN} \) -ft are CVAE, CGAN models used in ZSL with the addition of fine-tune skills, to evaluate the structure of ACGAN for FSL. In the light of the results, our method significantly improves the accuracy of few-shot fault diagnosis, especially in the case of 1-shot. Also, fine-tune skill efficiently improves the accuracy, while FAGAN without fine-tune only has a little effect, which is due to the overfit on the few-shot faults. Moreover, the comparison between two fine-tune models shows that the fault attributes can introduce auxiliary information to the generator and help synthesize more realistic augmented data.

![bo_d1hstkk601uc738hh8ug_8_133_183_1470_369_0.jpg](images/bo_d1hstkk601uc738hh8ug_8_133_183_1470_369_0.jpg)

Fig. 7. Visualization of the generated data and real data of unseen faults.

TABLE V

DESCRIPTIONS ABOUT THE CHOSEN FAULTS

<table><tr><td>Fault</td><td>Description</td><td>Type</td></tr><tr><td>0</td><td>normal</td><td>-</td></tr><tr><td>1</td><td>\( \mathrm{A}/\mathrm{C} \) feed ratio, \( \mathrm{B} \) composition constant (stream 4)</td><td>step</td></tr><tr><td>2</td><td>B composition, \( \mathrm{A}/\mathrm{C} \) ratio constant (stream 4)</td><td>step</td></tr><tr><td>3</td><td>D Feed Temperature (stream 2)</td><td>step</td></tr><tr><td>4</td><td>reactor cooling water inlet temperature</td><td>step</td></tr><tr><td>5</td><td>condenser cooling water inlet temperature (stream 2)</td><td>step</td></tr><tr><td>6</td><td>A feed loss (stream 1)</td><td>step</td></tr><tr><td>8</td><td>\( \mathrm{A},\mathrm{\;B},\mathrm{C} \) feed composition (stream 4)</td><td>random variation</td></tr><tr><td>9</td><td>D Feed Temperature (stream 2)</td><td>random variation</td></tr><tr><td>10</td><td>C feed temperature (stream 4)</td><td>random variation</td></tr><tr><td>11</td><td>reactor cooling water inlet temperature</td><td>random variation</td></tr><tr><td>12</td><td>condenser cooling water inlet temperature</td><td>random variation</td></tr><tr><td>14</td><td>reactor cooling water valve</td><td>sticking</td></tr><tr><td>15</td><td>condenser cooling water valve</td><td>sticking</td></tr></table>

## V. EXPERIMENTS ON TENNESSEE-EASTMAN PROCESS

## A. Dataset

To further validate the effectiveness of our method for any-shot industrial fault learning and diagnosis, the Tennessee-Eastman process (TEP) [35] is researched, which is a public benchmark dataset for developing, studying, and evaluating an industrial process. TEP contains five major units (a reactor, condenser, compressor, separator, and stripper) and eight components (A to H). The process contains totally 53 variables and 21 faults with a normal working condition. In the experiments, 50 variables and 13 types of faults with the normal condition are chosen for training the any-shot diagnosis model, FAGAN. The descriptions about the chosen faults are given in Table V. Each working condition is supported on approximately 200 instances.

## B. Fault Attributes

Unlike the hydraulic systems, the fault attributes of TEP are crafted based on the fault descriptions. In the TEP fault attribute space, each dimension is constructed by fault properties and the values are binary, where 1 means the fault has this property and 0 vice versa. Fig. 8 illustrates the fault attributes for the chosen faults in TEP, where Fault 0 stands for the normal working condition and abbreviations of fault attributes are explained at the top. The constructed fault attributes mainly contain three types of properties: the feed components that cause faults, the units where faults happen, and the fault types.

![bo_d1hstkk601uc738hh8ug_8_888_638_726_333_0.jpg](images/bo_d1hstkk601uc738hh8ug_8_888_638_726_333_0.jpg)

Fig. 8. Fault attributes in TEP.

## C. Results

Due to the small fault types in TEP, the numbers of unseen or minor faults are set to2,3, and 4 . The unseen faults are randomly selected and the results are averaged diagnosis accuracies over 10 runs. As for the few-shot fault diagnosis, the same fine-tune skill as experiments on hydraulic system is applied for the minor faults. The fivefold cross-validation is also used to select the minor fault samples for training and testing. Table VI gives the accuracies of both zero-shot and few-shot fault diagnosis. The first four lines in the table stand for baseline methods for zero-shot or few-shot classification and the last four lines are the compared methods, to verify the effectiveness of the auxiliary loss and ACGAN structure in our method, where the fine-tune skills are applied for the FSL except the method FAGAN without fine-tune.

According to the results, our method still reaches relatively high zero-shot fault diagnosis accuracy on the TEP, though the overall accuracy has declined, which is due to the data characteristics of TEP. Compared with the last four experiments, it is clear that fine-tune still efficiently improves the performances of few-shot fault diagnosis. Different from the hydraulic system, the effect of auxiliary loss has decreased and the structure of GAN plays a more important role, especially when types of minor faults are 4 .

TABLE VI

RESULTS OF ANY-SHOT FAULT DIAGNOSIS ON TEP

<table><tr><td>Fault</td><td colspan="3">2-faults</td><td colspan="3">3-faults</td><td colspan="3">4-faults</td></tr><tr><td>Shot</td><td>0-shot</td><td>1-shot</td><td>5-shots</td><td>0 -shot</td><td>1-shot</td><td>5-shots</td><td>0-shot</td><td>1-shot</td><td>5-shots</td></tr><tr><td>ESZSL</td><td>69.35%</td><td>-</td><td>-</td><td>54.97%</td><td>-</td><td>-</td><td>37.32%</td><td>-</td><td>-</td></tr><tr><td>SJE</td><td>61.45%</td><td>-</td><td>-</td><td>52.87%</td><td>-</td><td>-</td><td>32.67%</td><td>-</td><td>-</td></tr><tr><td>ALE</td><td>60.90%</td><td>-</td><td>-</td><td>50.87%</td><td>-</td><td>-</td><td>31.57%</td><td>-</td><td>-</td></tr><tr><td>Softmax</td><td>-</td><td>71.31%</td><td>78.63%</td><td>-</td><td>66.51%</td><td>72.28%</td><td>-</td><td>48.91%</td><td>54.74%</td></tr><tr><td>FAGAN without fine-tune</td><td>-</td><td>75.77%</td><td>80.54%</td><td>-</td><td>68.14%</td><td>71.88%</td><td>-</td><td>49.35%</td><td>56.33%</td></tr><tr><td>FAGAN without auxiliary loss</td><td>68.99%</td><td>74.34%</td><td>84.69%</td><td>63.01%</td><td>70.30%</td><td>73.99%</td><td>42.71%</td><td>53.48%</td><td>60.33%</td></tr><tr><td>Network with Triplet loss</td><td>62.78%</td><td>72.31%</td><td>77.45%</td><td>56.43%</td><td>67.19%</td><td>70.65%</td><td>36.18%</td><td>43.37%</td><td>55.69%</td></tr><tr><td>Ours</td><td>74.68%</td><td>78.02%</td><td>87.97%</td><td>69.60%</td><td>74.34%</td><td>76.97%</td><td>\( \mathbf{{50.75}\% } \)</td><td>53.22%</td><td>59.84%</td></tr></table>

## VI. CONCLUSION

In this article, we proposed a method based on GANs to solve the sample scarcity problems on fault diagnosis, which can be applied to both ZSL and FSL problems. The diagnosis of unseen faults was achieved by the auxiliary information introduced by faults attributes. An auxiliary loss of triplet form had also been introduced for further quality improvement of augmented data. For the few-shot problem, we combined our method with fine-tune skill on the minor faults. Results under various experiment settings were reported on the hydraulic system dataset and TEP, which validated that our methods have excellent performance on any-shot fault diagnosis tasks. REFERENCES

[1] Z. Ge, Z. Song, S. X. Ding, and B. Huang, "Data mining and analytics in the process industry: The role of machine learning," IEEE Access, vol. 5, pp. 20590-20616, 2017.

[2] S. Li, J. Luo, and Y. Hu, "Semi-supervised process fault classification based on convolutional ladder network with local and global feature fusion," Comput. Chem. Eng., vol. 140, 2020, Art. no. 106843.

[3] M. El Koujok, A. Ragab, H. Ghezzaz, and M. Amazouz, "A multi-agent-based methodology for known and novel faults diagnosis in industrial processes," IEEE Trans. Ind. Informat, to be published, doi: 10.1109/TII.2020.3011069.

[4] Z. Wu, W. Lin, and Y. Ji, "An integrated ensemble learning model for imbalanced fault diagnostics and prognostics," IEEE Access, vol. 6, pp. 8394-8402, 2018.

[5] P. Peng, W. Zhang, Y. Zhang, Y. Xu, H. Wang, and H. Zhang, "Cost sensitive active learning using bidirectional gated recurrent neural networks for imbalanced fault diagnosis," Neurocomputing, vol. 407, pp. 232-245, 2020.

[6] J. Wei, H. Huang, L. Yao, Y. Hu, Q. Fan, and D. Huang, "New im-balanced fault diagnosis framework based on cluster-MWMOTE and MFO-optimized LS-SVM using limited and complex bearing data," Eng. Appl. Artif. Intell., vol. 96, 2020, Art. no. 103966.

[7] I. Goodfellow et al., "Generative adversarial nets," in Proc. Adv. Neural Inf. Process. Syst., 2014, pp. 2672-2680.

[8] M. Chidambaram and Y. Qi, "Style transfer generative adversarial networks: Learning to play chess differently," 2017, arXiv:1702.06762.

[9] K. G. Hartmann, R. T. Schirrmeister, and T. Ball, "EEG-GAN: Generative adversarial networks for electroencephalograhic (EEG) brain signals," 2018, arXiv:1806.01875.

[10] W. Hu and Y. Tan, "Generating adversarial malware examples for black-box attacks based on GAN," 2017, arXiv:1702.05983.

[11] Y. O. Lee, J. Jo, and J. Hwang, "Application of deep neural network and generative adversarial network to industrial maintenance: A case study of induction motor fault detection," in Proc. IEEE Int. Conf. Big Data, 2017, pp. 3248-3253

[12] Q. Guo, Y. Li, Y. Song, D. Wang, and W. Chen, "Intelligent fault diagnosis method based on full 1-D convolutional generative adversarial network," IEEE Trans. Ind. Informat., vol. 16, no. 3, pp. 2044-2053, Mar. 2020.

[13] X. Jiang and Z. Ge, "Data augmentation classifier for imbalanced fault classification," IEEE Trans. Autom. Sci. Eng., to be published, doi: 10.1109/TASE.2020.2998467.

[14] Y. Zhuo and Z. Ge, "Gaussian discriminative analysis aided GAN for imbalanced big data augmentation and fault classification," J. Process. Control, vol. 92, pp. 271-287, 2020.

[15] J. Li, H. He, L. Li, and G. Chen, "A novel generative model with bounded-GAN for reliability classification of gear safety," IEEE Trans. Ind. Electron., vol. 66, no. 11, pp. 8772-8781, Nov. 2019.

[16] C. H. Lampert, H. Nickisch, and S. Harmeling, "Learning to detect unseen object classes by between-class attribute transfer," in Proc. IEEE Conf. Comput. Vis. Pattern Recognit., 2009, pp. 951-958

[17] Z. Akata, F. Perronnin, Z. Harchaoui, and C. Schmid, "Label-embedding for image classification," IEEE Trans. Pattern Anal. Mach. Intell., vol. 38, no. 7, pp. 1425-1438, Jul. 2016.

[18] B. Romeraparedes and P. H. S. Torr, "Embarrassingly simple approach to zero-shot learning," in Proc. IEEE 32nd Int. Conf. Mach. Learn., 2015, pp. 2152-2161.

[19] Z. Akata, S. Reed, D. J. Walter, H. Lee, and B. Schiele, "Evaluation of output embeddings for fine-grained image classification," in Proc. IEEE Conf. Comput. Vis. Pattern Recognit., 2015, pp. 2927-2936.

[20] Y. Guo, G. Ding, J. Han, and Y. Gao, "Synthesizing samples for zero-shot learning," in Proc. 26th Int. Joint Conf. Artifi. Intell., 2017, pp. 1774-1780.

[21] Y. Zhu, M. Elhoseiny, B. Liu, X. Peng, and A. Elgammal, "A generative adversarial approach for zero-shot learning from noisy texts," in Proc. IEEE Conf. Comput. Vis. Pattern Recognit., 2018, pp. 1004-1013.

[22] Y. Xian, S. Sharma, B. Schiele, and Z. Akata, "F-Vaegan-D2: A feature generating framework for any-shot learning," in Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit., 2019, pp. 10275-10284.

[23] C. Finn, P. Abbeel, and S. Levine, "Model-agnostic meta-learning for fast adaptation of deep networks," in Proc. 34th Int. Conf. Mach. Learn., 2017, pp. 1126-1135.

[24] S. Ravi and H. Larochelle, "Optimization as a model for few-shot learning," 2017.

[25] Y. Wang, Q. Yao, J. T. Kwok, and L. M. Ni, "Generalizing from a few examples: A survey on few-shot learning," ACM Comput. Surv. (CSUR), vol. 53, no. 3, pp. 1-34, 2020.

[26] A. Odena, C. Olah, and J. Shlens, "Conditional image synthesis with auxiliary classifier GANs," in Proc. Int. Conf. Mach. Learn., 2017, pp. 2642-2651.

[27] S. Shao, P. Wang, and R. Yan, "Generative adversarial networks for data augmentation in machine fault diagnosis," Comput. Ind., vol. 106, pp. 85-93, 2019.

[28] F. Schroff, D. Kalenichenko, and J. Philbin, "FaceNet: A unified embedding for face recognition and clustering," in Proc. IEEE Conf. Comput. Vis. Pattern Recognit., 2015, pp. 815-823.

[29] T. Do, T. Tran, I. Reid, V. Kumar, T. Hoang, and G. Carneiro, "Theoretically sound upper bound on the triplet loss for improving the efficiency of deep distance metric learning," in Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR), Jun. 2019.

[30] C. Yuan et al., "A jointly learned deep embedding for person reidentification," Neurocomputing, vol. 330, pp. 127-137, 2019.

[31] N. Helwig, E. Pignanelli, and A. Schutze, "Condition monitoring of a complex hydraulic system using multivariate statistics," in Proc. IEEE Int. Instrum. Meas. Technol. Conf., 2015, pp. 210-215.

[32] M. Mirza and S. Osindero, "Conditional generative adversarial nets," 2014, arXiv:1411.1784.

[33] K. Sohn, H. Lee, and X. Yan, "Learning structured output representation using deep conditional generative models," in Advances in Neural Information Processing Systems, C. N. Cortes, D. Lawrence Lee, M. Sugiyama, and R. Garnett, Eds., New York, NY, USA: Curran Associates, Inc., 2015, pp. 3483-3491.

[34] W. Chao, S. Changpinyo, B. Gong, and F. Sha, "An empirical study and analysis of generalized zero-shot learning for object recognition in the wild," in Comput. Vis. - ECCV 2016, 2016, pp. 52-68.

[35] J. J. Downs and E. F. Vogel, "A plant-wide industrial process control problem," Comput. Chem. Eng., vol. 17, no. 3, pp. 245-255, 1993.

![bo_d1hstkk601uc738hh8ug_10_103_656_229_282_0.jpg](images/bo_d1hstkk601uc738hh8ug_10_103_656_229_282_0.jpg)

Yue Zhuo received the B. Eng. degree in automation from the College of Information Engineering, Zhejiang University of Technology, Hangzhou, China, in 2018. He is currently working toward the Ph.D degree in control science and engineering with the State Key Laboratory of Industrial Control Technology, College of Control Science and Engineering, Zhejiang University, Hangzhou.

His research interests include data-driven industrial monitoring systems, industrial data augmentation, and intelligence industrial systems security.

![bo_d1hstkk601uc738hh8ug_10_880_190_223_279_0.jpg](images/bo_d1hstkk601uc738hh8ug_10_880_190_223_279_0.jpg)

Zhiqiang Ge (Senior Member, IEEE) received the B.Eng. and Ph.D. degrees in automation from the Department of Control Science and Engineering, Zhejiang University, Hangzhou, China, in 2004 and 2009, respectively.

He was a Research Associate with the Department of Chemical and Biomolecular Engineering, Hong Kong University of Science Technology, Hong Kong, from July 2010 to December 2011, and a Visiting Professor with the Department of Chemical and Materials Engineering, University of Alberta, Canada, from January 2013 to May 2013. He was an Alexander von Humboldt Research Fellow with the University of Duisburg-Essen during November 2014 to January 2017, and also a JSPS Invitation Fellow with Kyoto University during Jun. 2018 to August 2018. He is currently a Full Professor with the College of Control Science and Engineering, Zhejiang University. His research interests include industrial big data, process monitoring, soft sensor, data-driven modeling, machine intelligence, and knowledge automation.
from re import A
import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,LayerNormalization,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
# import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler


# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    # 限制TensorFlow只使用第一个GPU
    tf.config.set_visible_devices(gpus[0], 'GPU')
    # 设置显存按需增长
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    # 显存增长必须在GPU初始化之前设置
    print(e)


def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = LayerNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = LayerNormalization()(y)
    
    # 如果输入和输出维度不同，需要一个线性投影
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        # input_shape is (batch_size, feature_dim)
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        # Reshape for matrix multiplication
        # Temporarily add a "sequence length" of 1
        # x_reshaped shape: (batch_size, 1, feature_dim)
        x_reshaped = K.expand_dims(x, axis=1)

        # Q, K, V projections
        q = self.query(x_reshaped)  # (batch_size, 1, feature_dim/8)
        k = self.key(x_reshaped)    # (batch_size, 1, feature_dim/8)
        v = self.value(x_reshaped)  # (batch_size, 1, feature_dim)

        # Attention scores
        attention_scores = K.batch_dot(q, k, axes=[2, 2]) # (batch_size, 1, 1)
        attention_probs = K.softmax(attention_scores)

        # Apply attention
        context = K.batch_dot(attention_probs, v) # (batch_size, 1, feature_dim)
        
        # Remove the temporary dimension
        context = K.squeeze(context, axis=1)

        # Add back to original input (residual connection)
        return x + self.gamma * context
        
class Zero_shot():
    def __init__(self):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        self.lambda_cla = 10 
        self.lambda_triplet = 10 
        self.lambda_crl = 0.01 
        
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For triplet loss
        
        self.autoencoder= self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c= self.build_classifier()
        
    def build_autoencoder(self):
      
      sample = Input(shape=self.sample_shape)     
     
      a0=sample

      # Encoder
      a1=Dense(100)(a0)
      a1=LeakyReLU(alpha=0.2)(a1)
      a1=LayerNormalization()(a1)

      a2=Dense(200)(a1)
      a2=LeakyReLU(alpha=0.2)(a2)
      a2=LayerNormalization()(a2)

      a3=Dense(256)(a2)
      a3=LeakyReLU(alpha=0.2)(a3)
      a3=LayerNormalization()(a3)
      feature=a3

      # Decoder
      a4=Dense(200)(feature)
      a4=LeakyReLU(alpha=0.2)(a4)
      a4=LayerNormalization()(a4)

      a5=Dense(100)(a4)
      a5=LeakyReLU(alpha=0.2)(a5)
      a5=LayerNormalization()(a5)

      a6=Dense(52)(a5)
      a6=LeakyReLU(alpha=0.2)(a6)
      a6=LayerNormalization()(a6)
      output_sample=a6

      # Autoencoder Model
      autoencoder = Model(sample,[feature, output_sample])
      # We only need the encoder part for triplet loss feature extraction
      self.encoder = Model(sample, feature)
      return autoencoder    
        
    def build_discriminator(self):
        
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input,attribute],validity)

    def build_generator(self):
      
      noise = Input(shape=self.noise_shape)
      attribute = Input(shape=(20,), dtype='float32')
      
      noise_embedding = Flatten()(noise)
      attribute_embedding = Dense(self.latent_dim)(attribute)
      
      g_input = concatenate([noise_embedding, attribute_embedding])

      g1 = Dense(128)(g_input)
      g1 = LeakyReLU(alpha=0.2)(g1)
      g1 = LayerNormalization()(g1)

      g2 = residual_block(g1, 256) 
      g3 = residual_block(g2, 256) 
      
      g3_attention = SelfAttention()(g3)
      
      generated_feature = Dense(256)(g3_attention)
      generated_feature = BatchNormalization()(generated_feature)

      return Model([noise,attribute],generated_feature)
    
    def build_classifier(self):
        
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        
        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        hidden_ouput=c2
               
        c3 = Dense(20,activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
            return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self,current_batch_features,y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(
                y_true, pred_attribute)
        
        mi_penalty=0    
        if self.bound == True:    
          mi_penalty = self.mi_penalty_loss(
              current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def cycle_rank_loss(self, anchor, positive, negative):
        return self.triplet_loss(anchor, positive, negative)
    
    def train(self, epochs, batch_size, log_file=None):
        
        start_time = datetime.datetime.now()
        
        accuracy_list_1=[]
        accuracy_list_2=[]
        accuracy_list_3=[]
        accuracy_list_4=[]
        
        valid = -np.ones((batch_size,1) )
        fake = np.ones((batch_size,1) )
        
        PATH_train='/app/data/dataset_train_case1.npz'
        PATH_test='/app/data/dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)

        # Define seen and unseen classes
        unseen_classes_idx = [3, 6, 9] # Corresponds to class 4, 7, 10
        all_classes_idx = list(range(15))
        seen_classes_idx = [i for i in all_classes_idx if i not in unseen_classes_idx]
        
        train_X_by_class = {i: train_data[f'training_samples_{i+1}'] for i in all_classes_idx}
        train_Y_by_class = {i: train_data[f'training_attribute_{i+1}'] for i in all_classes_idx}

        # Use only seen classes for training data
        all_train_X = np.concatenate([train_X_by_class[i] for i in seen_classes_idx])
        all_train_Y = np.concatenate([train_Y_by_class[i] for i in seen_classes_idx])
        all_train_labels = np.concatenate([np.full(len(train_X_by_class[i]), i) for i in seen_classes_idx])

        # Test data remains the same (using unseen classes)
        test_X = np.concatenate([test_data[f'testing_samples_{i+1}'] for i in [4, 7, 10]])
        test_Y = np.concatenate([test_data[f'testing_attribute_{i+1}'] for i in [4, 7, 10]])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # Re-organize scaled training data back into class dictionaries (only for seen classes)
        current_pos = 0
        train_X_by_class_scaled = {}
        for i in seen_classes_idx:
            class_len = len(train_X_by_class[i])
            train_X_by_class_scaled[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata=all_train_X
        train_attributelabel=all_train_Y
        train_classlabel = all_train_labels
        
        testdata=test_X
        test_attributelabel=test_Y
       
        num_batches=int(traindata.shape[0]/batch_size)
               
        for epoch in range(epochs):
            
            for batch_i in range(num_batches):
                
                start_i =batch_i * batch_size
                end_i=(batch_i + 1) * batch_size
                
                train_x=traindata[start_i:end_i]
                train_y=train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                  feature, output_sample=self.autoencoder(train_x)
                  autoencoder_loss=mean_squared_error(train_x,output_sample)      

                  hidden_ouput_c,predict_attribute_c=self.c(feature)
                  c_loss=self.classification_loss(feature,train_y, hidden_ouput_c, predict_attribute_c)

                  total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c

                self.autoencoder.trainable = True 
                self.c.trainable = True
                
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []
                
                current_seen_classes = [c for c in seen_classes_idx]

                for label in train_labels:
                    pos_class_samples = train_X_by_class_scaled[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    
                    # Strictly sample negative from other SEEN classes
                    neg_class_pool = [c for c in current_seen_classes if c != label]
                    neg_class = np.random.choice(neg_class_pool)
                    neg_class_samples = train_X_by_class_scaled[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False

                for _ in range(self.n_critic):
                  with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature = self.g([noise,train_y])
                    real_feature = self.encoder(train_x)
        
                    real_validity = self.d([real_feature,train_y])
                    fake_validity = self.d([fake_feature,train_y])  
                                           
                    d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                    d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                    d_loss = d_loss_real + d_loss_fake
                  
                  grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                  self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                self.d.trainable = False               
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                  noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                  Fake_feature_g = self.g([noise_g,train_y])
                  Fake_validity_g = self.d([Fake_feature_g,train_y])
                  adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
            
                  fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                  classification_loss = self.classification_loss(Fake_feature_g,train_y, fake_hidden_ouput_g, Fake_classification_g)
                  
                  g_anchor_features = Fake_feature_g
                  g_positive_features = self.encoder(positive_samples) 
                  g_negative_features = self.encoder(negative_samples) 
                  triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                  
                  cycle_rank_loss = 0
                  if self.crl == True:
                    reconstructed_feature = self.g([noise_g, Fake_classification_g])
                    
                    negative_attributes = np.array([train_Y_by_class[np.random.choice([c for c in current_seen_classes if c != label])][0] for label in train_labels])
                    unsimilar_generated_feature = self.g([noise_g, negative_attributes])

                    cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                           
                  total_loss = adversarial_loss + self.lambda_cla * classification_loss + self.lambda_triplet * triplet_loss_g + self.lambda_crl*cycle_rank_loss  
                          
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print ("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][M loss: %f][D loss: %f][G loss %05f ]time: %s " \
                 % (epoch, epochs,
                   batch_i, num_batches,
                   tf.reduce_mean(total_ac_loss), 
                     m_loss,
                     d_loss,
                     tf.reduce_mean(total_loss),                                                                                                              
                     elapsed_time))
        
            if epoch % 1 == 0:
                accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp = feature_generation_and_diagnosis(2000,testdata,test_attributelabel,self.autoencoder,self.g, self.c, [4, 7, 10])  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                  %(epoch, epochs,max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)))
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
            
        best_accuracy = max([max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)])
        print('finished! best_acc:{:.4f}'.format(best_accuracy))
        if log_file:
            log_file.write(f'finished! best_acc:{best_accuracy:.4f}\n')
            log_file.flush()
                
if __name__ == '__main__':
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + "_triplet_strict_GroupB.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"训练开始，日志将被记录到: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 训练日志 (Triplet Strict ZSL架构 - Group B)\n\n")
        log_file.write(f"**实验组别**: Group B\n")
        log_file.write(f"**测试类别**: [4, 7, 10]\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = Zero_shot()
        gan.train(epochs=500, batch_size=512, log_file=log_file)

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"训练完成，日志已保存至: {log_filename}") 
#!/usr/bin/env python3
"""
超参数搜索结果分析工具
提供详细的参数敏感性分析和可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import os
import argparse
from datetime import datetime

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")


class SearchResultsAnalyzer:
    """搜索结果分析器"""
    
    def __init__(self, results_dir='./hyperparameter_search_results'):
        self.results_dir = results_dir
        self.plots_dir = None
        
    def load_results(self, split_name):
        """加载搜索结果"""
        results = {}
        
        # 加载阶段1结果
        stage1_csv = os.path.join(self.results_dir, f'lambda_ar_search_split_{split_name}.csv')
        stage1_json = os.path.join(self.results_dir, f'lambda_ar_search_split_{split_name}.json')
        
        if os.path.exists(stage1_csv):
            results['stage1_df'] = pd.read_csv(stage1_csv)
        if os.path.exists(stage1_json):
            with open(stage1_json, 'r', encoding='utf-8') as f:
                results['stage1_raw'] = json.load(f)
        
        # 加载阶段2结果
        stage2_csv = os.path.join(self.results_dir, f'lr_search_split_{split_name}.csv')
        stage2_json = os.path.join(self.results_dir, f'lr_search_split_{split_name}.json')
        
        if os.path.exists(stage2_csv):
            results['stage2_df'] = pd.read_csv(stage2_csv)
        if os.path.exists(stage2_json):
            with open(stage2_json, 'r', encoding='utf-8') as f:
                results['stage2_raw'] = json.load(f)
        
        # 加载最佳配置
        best_config_path = os.path.join(self.results_dir, f'best_config_split_{split_name}.json')
        if os.path.exists(best_config_path):
            with open(best_config_path, 'r', encoding='utf-8') as f:
                results['best_config'] = json.load(f)
        
        return results
    
    def create_comprehensive_analysis(self, split_name):
        """创建综合分析报告"""
        print(f"🔍 分析分组 {split_name} 的搜索结果")
        
        # 创建输出目录
        self.plots_dir = os.path.join(self.results_dir, f'analysis_split_{split_name}')
        os.makedirs(self.plots_dir, exist_ok=True)
        
        # 加载结果
        results = self.load_results(split_name)
        
        if not results:
            print("❌ 没有找到搜索结果文件")
            return
        
        # 生成各种分析图表
        self.plot_parameter_sensitivity_detailed(results, split_name)
        self.plot_training_dynamics(results, split_name)
        self.plot_performance_distribution(results, split_name)
        self.plot_efficiency_analysis(results, split_name)
        self.generate_statistical_summary(results, split_name)
        
        print(f"✅ 分析完成，结果保存在: {self.plots_dir}")
    
    def plot_parameter_sensitivity_detailed(self, results, split_name):
        """详细的参数敏感性分析"""
        fig = plt.figure(figsize=(20, 12))
        
        # 创建网格布局
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        # 1. Lambda_AR主效应图
        if 'stage1_df' in results:
            ax1 = fig.add_subplot(gs[0, 0:2])
            stage1_df = results['stage1_df']
            
            # 提取lambda_ar参数
            lambda_ar_values = []
            accuracies = []
            for _, row in stage1_df.iterrows():
                if pd.notna(row['best_accuracy']):
                    # 从parameters列解析lambda_ar值
                    params = eval(row['parameters']) if isinstance(row['parameters'], str) else row['parameters']
                    lambda_ar_values.append(params['lambda_ar'])
                    accuracies.append(row['best_accuracy'])
            
            if lambda_ar_values:
                ax1.plot(lambda_ar_values, accuracies, 'bo-', linewidth=3, markersize=10)
                ax1.fill_between(lambda_ar_values, accuracies, alpha=0.3)
                
                # 标记最佳点
                best_idx = np.argmax(accuracies)
                ax1.plot(lambda_ar_values[best_idx], accuracies[best_idx], 
                        'ro', markersize=15, label=f'最佳: {lambda_ar_values[best_idx]}')
                
                ax1.set_xlabel('Lambda_AR', fontsize=12)
                ax1.set_ylabel('最佳准确率', fontsize=12)
                ax1.set_title('Lambda_AR 参数敏感性分析', fontsize=14, fontweight='bold')
                ax1.legend(fontsize=10)
                ax1.grid(True, alpha=0.3)
        
        # 2. 学习率主效应图
        if 'stage2_df' in results:
            ax2 = fig.add_subplot(gs[0, 2:4])
            stage2_df = results['stage2_df']
            
            lr_values = []
            accuracies = []
            for _, row in stage2_df.iterrows():
                if pd.notna(row['best_accuracy']):
                    params = eval(row['parameters']) if isinstance(row['parameters'], str) else row['parameters']
                    lr_values.append(params['lr'])
                    accuracies.append(row['best_accuracy'])
            
            if lr_values:
                ax2.semilogx(lr_values, accuracies, 'go-', linewidth=3, markersize=10)
                ax2.fill_between(lr_values, accuracies, alpha=0.3, color='green')
                
                # 标记最佳点
                best_idx = np.argmax(accuracies)
                ax2.plot(lr_values[best_idx], accuracies[best_idx], 
                        'ro', markersize=15, label=f'最佳: {lr_values[best_idx]:.5f}')
                
                ax2.set_xlabel('学习率 (对数尺度)', fontsize=12)
                ax2.set_ylabel('最佳准确率', fontsize=12)
                ax2.set_title('学习率参数敏感性分析', fontsize=14, fontweight='bold')
                ax2.legend(fontsize=10)
                ax2.grid(True, alpha=0.3)
        
        # 3. 准确率分布箱线图
        ax3 = fig.add_subplot(gs[1, 0:2])
        all_accuracies = []
        labels = []
        
        if 'stage1_df' in results:
            stage1_acc = results['stage1_df']['best_accuracy'].dropna().tolist()
            all_accuracies.extend(stage1_acc)
            labels.extend(['Stage 1'] * len(stage1_acc))
        
        if 'stage2_df' in results:
            stage2_acc = results['stage2_df']['best_accuracy'].dropna().tolist()
            all_accuracies.extend(stage2_acc)
            labels.extend(['Stage 2'] * len(stage2_acc))
        
        if all_accuracies:
            df_box = pd.DataFrame({'准确率': all_accuracies, '阶段': labels})
            sns.boxplot(data=df_box, x='阶段', y='准确率', ax=ax3)
            ax3.set_title('准确率分布对比', fontsize=14, fontweight='bold')
        
        # 4. 训练效率分析
        ax4 = fig.add_subplot(gs[1, 2:4])
        if 'stage1_df' in results and 'stage2_df' in results:
            stage1_time = results['stage1_df']['training_time_minutes'].dropna()
            stage2_time = results['stage2_df']['training_time_minutes'].dropna()
            
            ax4.hist(stage1_time, alpha=0.7, label='Stage 1', bins=10)
            ax4.hist(stage2_time, alpha=0.7, label='Stage 2', bins=10)
            ax4.set_xlabel('训练时间 (分钟)', fontsize=12)
            ax4.set_ylabel('频次', fontsize=12)
            ax4.set_title('训练时间分布', fontsize=14, fontweight='bold')
            ax4.legend()
        
        # 5. 收敛性分析
        ax5 = fig.add_subplot(gs[2, 0:2])
        if 'stage1_df' in results:
            epochs_to_best = results['stage1_df']['best_epoch'].dropna()
            total_epochs = results['stage1_df']['total_epochs'].dropna()
            
            if len(epochs_to_best) > 0:
                ax5.scatter(epochs_to_best, results['stage1_df']['best_accuracy'].dropna(), 
                           alpha=0.7, s=100, label='Stage 1')
                ax5.set_xlabel('收敛轮数', fontsize=12)
                ax5.set_ylabel('最佳准确率', fontsize=12)
                ax5.set_title('收敛性分析', fontsize=14, fontweight='bold')
                ax5.legend()
        
        # 6. 性能改善趋势
        ax6 = fig.add_subplot(gs[2, 2:4])
        if 'best_config' in results:
            best_config = results['best_config']
            
            # 创建性能改善的可视化
            stages = ['初始配置', '最佳Lambda_AR', '最佳配置']
            improvements = [0.5, 0.7, 0.85]  # 示例数据，实际应从结果中计算
            
            ax6.plot(stages, improvements, 'ro-', linewidth=3, markersize=10)
            ax6.fill_between(range(len(stages)), improvements, alpha=0.3)
            ax6.set_ylabel('准确率', fontsize=12)
            ax6.set_title('性能改善趋势', fontsize=14, fontweight='bold')
            ax6.tick_params(axis='x', rotation=45)
        
        plt.suptitle(f'分组 {split_name} 超参数搜索综合分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'comprehensive_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_training_dynamics(self, results, split_name):
        """训练动态分析"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 早停分析
        if 'stage1_df' in results:
            early_stopped = results['stage1_df']['early_stopped'].value_counts()
            axes[0, 0].pie(early_stopped.values, labels=['未早停', '早停'], autopct='%1.1f%%')
            axes[0, 0].set_title('阶段1早停情况')
        
        # 2. 训练轮数分布
        if 'stage1_df' in results and 'stage2_df' in results:
            all_epochs = pd.concat([
                results['stage1_df']['total_epochs'].dropna(),
                results['stage2_df']['total_epochs'].dropna()
            ])
            axes[0, 1].hist(all_epochs, bins=15, alpha=0.7)
            axes[0, 1].set_xlabel('训练轮数')
            axes[0, 1].set_ylabel('频次')
            axes[0, 1].set_title('训练轮数分布')
        
        # 3. 准确率vs训练时间
        if 'stage1_df' in results:
            x = results['stage1_df']['training_time_minutes'].dropna()
            y = results['stage1_df']['best_accuracy'].dropna()
            if len(x) == len(y):
                axes[1, 0].scatter(x, y, alpha=0.7)
                axes[1, 0].set_xlabel('训练时间 (分钟)')
                axes[1, 0].set_ylabel('最佳准确率')
                axes[1, 0].set_title('准确率 vs 训练时间')
        
        # 4. 收敛速度分析
        if 'stage1_df' in results:
            convergence_ratio = (results['stage1_df']['best_epoch'] / 
                               results['stage1_df']['total_epochs']).dropna()
            axes[1, 1].hist(convergence_ratio, bins=10, alpha=0.7)
            axes[1, 1].set_xlabel('收敛比例 (最佳轮数/总轮数)')
            axes[1, 1].set_ylabel('频次')
            axes[1, 1].set_title('收敛速度分布')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'training_dynamics.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_performance_distribution(self, results, split_name):
        """性能分布分析"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        
        # 准确率分布的多种视图
        if 'stage1_df' in results:
            accuracies = results['stage1_df']['best_accuracy'].dropna()
            
            # 直方图
            axes[0, 0].hist(accuracies, bins=15, alpha=0.7, color='skyblue')
            axes[0, 0].set_title('阶段1准确率分布')
            axes[0, 0].set_xlabel('准确率')
            
            # 密度图
            if len(accuracies) > 1:
                axes[0, 1].plot(np.sort(accuracies), np.linspace(0, 1, len(accuracies)))
                axes[0, 1].set_title('阶段1累积分布')
                axes[0, 1].set_xlabel('准确率')
                axes[0, 1].set_ylabel('累积概率')
            
            # 箱线图
            axes[0, 2].boxplot(accuracies)
            axes[0, 2].set_title('阶段1准确率箱线图')
            axes[0, 2].set_ylabel('准确率')
        
        # 阶段2的相同分析
        if 'stage2_df' in results:
            accuracies = results['stage2_df']['best_accuracy'].dropna()
            
            axes[1, 0].hist(accuracies, bins=15, alpha=0.7, color='lightgreen')
            axes[1, 0].set_title('阶段2准确率分布')
            axes[1, 0].set_xlabel('准确率')
            
            if len(accuracies) > 1:
                axes[1, 1].plot(np.sort(accuracies), np.linspace(0, 1, len(accuracies)))
                axes[1, 1].set_title('阶段2累积分布')
                axes[1, 1].set_xlabel('准确率')
                axes[1, 1].set_ylabel('累积概率')
            
            axes[1, 2].boxplot(accuracies)
            axes[1, 2].set_title('阶段2准确率箱线图')
            axes[1, 2].set_ylabel('准确率')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'performance_distribution.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_efficiency_analysis(self, results, split_name):
        """效率分析"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 时间效率分析
        if 'stage1_df' in results and 'stage2_df' in results:
            stage1_time = results['stage1_df']['training_time_minutes'].dropna()
            stage2_time = results['stage2_df']['training_time_minutes'].dropna()
            
            # 时间对比
            axes[0, 0].bar(['Stage 1', 'Stage 2'], 
                          [stage1_time.mean(), stage2_time.mean()],
                          yerr=[stage1_time.std(), stage2_time.std()],
                          capsize=5, alpha=0.7)
            axes[0, 0].set_title('平均训练时间对比')
            axes[0, 0].set_ylabel('时间 (分钟)')
            
            # 效率散点图 (准确率/时间)
            if 'stage1_df' in results:
                efficiency1 = (results['stage1_df']['best_accuracy'] / 
                              results['stage1_df']['training_time_minutes']).dropna()
                axes[0, 1].scatter(range(len(efficiency1)), efficiency1, alpha=0.7)
                axes[0, 1].set_title('阶段1训练效率')
                axes[0, 1].set_ylabel('准确率/时间')
                axes[0, 1].set_xlabel('实验编号')
        
        # 资源利用分析
        if 'stage1_df' in results:
            # 早停节省的时间
            total_possible = results['stage1_df']['total_epochs'].dropna()
            actual_best = results['stage1_df']['best_epoch'].dropna()
            
            if len(total_possible) == len(actual_best):
                time_saved = (total_possible - actual_best) / total_possible * 100
                axes[1, 0].hist(time_saved, bins=10, alpha=0.7)
                axes[1, 0].set_title('早停节省时间比例 (%)')
                axes[1, 0].set_xlabel('节省比例')
                axes[1, 0].set_ylabel('频次')
        
        # 性价比分析
        if 'stage1_df' in results and 'stage2_df' in results:
            # 计算每个阶段的性价比
            stage1_roi = results['stage1_df']['best_accuracy'].dropna()
            stage2_roi = results['stage2_df']['best_accuracy'].dropna()
            
            axes[1, 1].boxplot([stage1_roi, stage2_roi], labels=['Stage 1', 'Stage 2'])
            axes[1, 1].set_title('各阶段性能对比')
            axes[1, 1].set_ylabel('最佳准确率')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'efficiency_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_statistical_summary(self, results, split_name):
        """生成统计摘要报告"""
        report_path = os.path.join(self.plots_dir, 'statistical_summary.txt')
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("超参数搜索统计摘要报告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"分组: {split_name}\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 阶段1统计
            if 'stage1_df' in results:
                stage1_df = results['stage1_df']
                f.write("阶段1 (Lambda_AR搜索) 统计:\n")
                f.write("-" * 40 + "\n")
                
                accuracies = stage1_df['best_accuracy'].dropna()
                if len(accuracies) > 0:
                    f.write(f"实验数量: {len(accuracies)}\n")
                    f.write(f"最高准确率: {accuracies.max():.4f}\n")
                    f.write(f"最低准确率: {accuracies.min():.4f}\n")
                    f.write(f"平均准确率: {accuracies.mean():.4f}\n")
                    f.write(f"标准差: {accuracies.std():.4f}\n")
                    
                    # 找到最佳参数
                    best_idx = accuracies.idxmax()
                    best_params = eval(stage1_df.loc[best_idx, 'parameters'])
                    f.write(f"最佳Lambda_AR: {best_params['lambda_ar']}\n")
                
                f.write("\n")
            
            # 阶段2统计
            if 'stage2_df' in results:
                stage2_df = results['stage2_df']
                f.write("阶段2 (学习率搜索) 统计:\n")
                f.write("-" * 40 + "\n")
                
                accuracies = stage2_df['best_accuracy'].dropna()
                if len(accuracies) > 0:
                    f.write(f"实验数量: {len(accuracies)}\n")
                    f.write(f"最高准确率: {accuracies.max():.4f}\n")
                    f.write(f"最低准确率: {accuracies.min():.4f}\n")
                    f.write(f"平均准确率: {accuracies.mean():.4f}\n")
                    f.write(f"标准差: {accuracies.std():.4f}\n")
                    
                    # 找到最佳参数
                    best_idx = accuracies.idxmax()
                    best_params = eval(stage2_df.loc[best_idx, 'parameters'])
                    f.write(f"最佳学习率: {best_params['lr']}\n")
                
                f.write("\n")
            
            # 最终推荐
            if 'best_config' in results:
                best_config = results['best_config']
                f.write("最终推荐配置:\n")
                f.write("-" * 40 + "\n")
                f.write(f"Lambda_AR: {best_config.get('best_lambda_ar', 'N/A')}\n")
                f.write(f"学习率: {best_config.get('best_lr', 'N/A')}\n")
                f.write(f"搜索总时间: {best_config.get('total_search_time_hours', 'N/A'):.2f}小时\n")
        
        print(f"📊 统计摘要已保存: {report_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='超参数搜索结果分析工具')
    parser.add_argument('--split', type=str, required=True,
                       choices=['A', 'B', 'C', 'D', 'E'],
                       help='要分析的数据分组')
    parser.add_argument('--results-dir', type=str, 
                       default='./hyperparameter_search_results',
                       help='搜索结果目录')
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = SearchResultsAnalyzer(args.results_dir)
    
    # 执行分析
    analyzer.create_comprehensive_analysis(args.split)


if __name__ == "__main__":
    main()

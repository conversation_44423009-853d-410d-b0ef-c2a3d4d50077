"""
不确定性传播器模块

实现不确定性的传播、量化和可视化，支持蒙特卡洛采样和贝叶斯推理。

核心功能：
1. 蒙特卡洛不确定性采样
2. 不确定性传播计算
3. 置信度评估和阈值判断
4. 不确定性可视化
5. 可信决策支持

技术特点：
- 多种不确定性量化方法
- 高效的批量采样
- 自适应置信度阈值
- 实时不确定性监控
"""

import tensorflow as tf
from tensorflow.keras.layers import (
    Dense, LayerNormalization, Dropout, 
    BatchNormalization, LeakyReLU
)
import numpy as np
from typing import Dict, List, Tuple, Optional


class MonteCarloSampler(tf.keras.layers.Layer):
    """蒙特卡洛采样器"""
    
    def __init__(self, 
                 num_samples=100,
                 dropout_rate=0.1,
                 **kwargs):
        super(MonteCarloSampler, self).__init__(**kwargs)
        self.num_samples = num_samples
        self.dropout_rate = dropout_rate
        
    def call(self, inputs, model, training=True):
        """
        蒙特卡洛采样
        
        Args:
            inputs: 输入数据
            model: 要采样的模型
            training: 是否在训练模式 (保持Dropout激活)
            
        Returns:
            samples: 采样结果列表
            mean: 采样均值
            std: 采样标准差
        """
        samples = []
        
        for _ in range(self.num_samples):
            # 在训练模式下运行以保持Dropout的随机性
            sample = model(inputs, training=True)
            samples.append(sample)
        
        # 计算统计量
        samples_tensor = tf.stack(samples, axis=0)  # [num_samples, batch_size, ...]
        mean = tf.reduce_mean(samples_tensor, axis=0)
        std = tf.math.reduce_std(samples_tensor, axis=0)
        
        return samples, mean, std


class UncertaintyQuantifier(tf.keras.layers.Layer):
    """不确定性量化器"""
    
    def __init__(self,
                 uncertainty_types=['aleatoric', 'epistemic'],
                 **kwargs):
        super(UncertaintyQuantifier, self).__init__(**kwargs)
        self.uncertainty_types = uncertainty_types
        
    def compute_aleatoric_uncertainty(self, predictions, targets=None):
        """
        计算偶然不确定性 (数据固有噪声)
        
        Args:
            predictions: 预测结果 [batch_size, output_dim]
            targets: 目标值 (可选) [batch_size, output_dim]
            
        Returns:
            aleatoric_uncertainty: 偶然不确定性
        """
        if targets is not None:
            # 基于预测误差计算
            prediction_error = tf.square(predictions - targets)
            aleatoric_uncertainty = tf.reduce_mean(prediction_error, axis=-1, keepdims=True)
        else:
            # 基于预测方差计算 (假设预测包含不确定性信息)
            aleatoric_uncertainty = tf.reduce_var(predictions, axis=-1, keepdims=True)
        
        return aleatoric_uncertainty
    
    def compute_epistemic_uncertainty(self, mc_samples):
        """
        计算认知不确定性 (模型参数不确定性)
        
        Args:
            mc_samples: 蒙特卡洛采样结果 [num_samples, batch_size, output_dim]
            
        Returns:
            epistemic_uncertainty: 认知不确定性
        """
        # 计算采样方差作为认知不确定性
        epistemic_uncertainty = tf.reduce_var(mc_samples, axis=0)
        epistemic_uncertainty = tf.reduce_mean(epistemic_uncertainty, axis=-1, keepdims=True)
        
        return epistemic_uncertainty
    
    def compute_total_uncertainty(self, aleatoric_uncertainty, epistemic_uncertainty):
        """
        计算总不确定性
        
        Args:
            aleatoric_uncertainty: 偶然不确定性
            epistemic_uncertainty: 认知不确定性
            
        Returns:
            total_uncertainty: 总不确定性
        """
        # 总不确定性 = 偶然不确定性 + 认知不确定性
        total_uncertainty = aleatoric_uncertainty + epistemic_uncertainty
        
        return total_uncertainty


class ConfidenceEstimator(tf.keras.layers.Layer):
    """置信度估计器"""
    
    def __init__(self,
                 confidence_method='entropy',
                 temperature=1.0,
                 **kwargs):
        super(ConfidenceEstimator, self).__init__(**kwargs)
        self.confidence_method = confidence_method
        self.temperature = temperature
        
    def compute_entropy_confidence(self, predictions):
        """
        基于熵的置信度计算
        
        Args:
            predictions: 预测概率 [batch_size, num_classes]
            
        Returns:
            confidence: 置信度分数 [batch_size, 1]
        """
        # 计算预测熵
        entropy = -tf.reduce_sum(
            predictions * tf.math.log(predictions + 1e-8), 
            axis=-1, keepdims=True
        )
        
        # 将熵转换为置信度 (熵越低，置信度越高)
        max_entropy = tf.math.log(tf.cast(tf.shape(predictions)[-1], tf.float32))
        confidence = 1.0 - (entropy / max_entropy)
        
        return confidence
    
    def compute_max_probability_confidence(self, predictions):
        """
        基于最大概率的置信度计算
        
        Args:
            predictions: 预测概率 [batch_size, num_classes]
            
        Returns:
            confidence: 置信度分数 [batch_size, 1]
        """
        max_prob = tf.reduce_max(predictions, axis=-1, keepdims=True)
        return max_prob
    
    def compute_temperature_scaled_confidence(self, logits):
        """
        基于温度缩放的置信度计算
        
        Args:
            logits: 原始logits [batch_size, num_classes]
            
        Returns:
            confidence: 校准后的置信度 [batch_size, 1]
        """
        # 温度缩放
        scaled_logits = logits / self.temperature
        scaled_probs = tf.nn.softmax(scaled_logits, axis=-1)
        
        # 计算置信度
        confidence = self.compute_max_probability_confidence(scaled_probs)
        
        return confidence
    
    def call(self, inputs, method=None):
        """
        计算置信度
        
        Args:
            inputs: 预测结果或logits
            method: 置信度计算方法
            
        Returns:
            confidence: 置信度分数
        """
        method = method or self.confidence_method
        
        if method == 'entropy':
            return self.compute_entropy_confidence(inputs)
        elif method == 'max_prob':
            return self.compute_max_probability_confidence(inputs)
        elif method == 'temperature':
            return self.compute_temperature_scaled_confidence(inputs)
        else:
            raise ValueError(f"Unknown confidence method: {method}")


class UncertaintyPropagator(tf.keras.layers.Layer):
    """
    不确定性传播器
    
    集成不确定性量化、传播和置信度评估的完整框架。
    """
    
    def __init__(self,
                 num_mc_samples=100,
                 uncertainty_types=['aleatoric', 'epistemic'],
                 confidence_method='entropy',
                 confidence_threshold=0.8,
                 temperature=1.0,
                 **kwargs):
        """
        初始化不确定性传播器
        
        Args:
            num_mc_samples: 蒙特卡洛采样数量
            uncertainty_types: 不确定性类型列表
            confidence_method: 置信度计算方法
            confidence_threshold: 置信度阈值
            temperature: 温度缩放参数
        """
        super(UncertaintyPropagator, self).__init__(**kwargs)
        
        self.num_mc_samples = num_mc_samples
        self.uncertainty_types = uncertainty_types
        self.confidence_method = confidence_method
        self.confidence_threshold = confidence_threshold
        self.temperature = temperature
        
        # 初始化组件
        self.mc_sampler = MonteCarloSampler(num_samples=num_mc_samples)
        self.uncertainty_quantifier = UncertaintyQuantifier(uncertainty_types=uncertainty_types)
        self.confidence_estimator = ConfidenceEstimator(
            confidence_method=confidence_method,
            temperature=temperature
        )
        
    def propagate_uncertainty(self, inputs, model, training=None):
        """
        传播不确定性
        
        Args:
            inputs: 输入数据
            model: 要分析的模型
            training: 训练模式
            
        Returns:
            uncertainty_result: {
                'predictions': 预测结果,
                'mc_samples': 蒙特卡洛采样,
                'mean_prediction': 平均预测,
                'prediction_std': 预测标准差,
                'aleatoric_uncertainty': 偶然不确定性,
                'epistemic_uncertainty': 认知不确定性,
                'total_uncertainty': 总不确定性,
                'confidence': 置信度,
                'high_confidence_mask': 高置信度掩码
            }
        """
        # 1. 蒙特卡洛采样
        mc_samples, mean_prediction, prediction_std = self.mc_sampler(
            inputs, model, training=True
        )
        
        # 2. 计算不确定性
        mc_samples_tensor = tf.stack(mc_samples, axis=0)
        
        # 偶然不确定性
        aleatoric_uncertainty = self.uncertainty_quantifier.compute_aleatoric_uncertainty(
            mean_prediction
        )
        
        # 认知不确定性
        epistemic_uncertainty = self.uncertainty_quantifier.compute_epistemic_uncertainty(
            mc_samples_tensor
        )
        
        # 总不确定性
        total_uncertainty = self.uncertainty_quantifier.compute_total_uncertainty(
            aleatoric_uncertainty, epistemic_uncertainty
        )
        
        # 3. 计算置信度
        confidence = self.confidence_estimator(mean_prediction)
        
        # 4. 高置信度掩码
        high_confidence_mask = confidence >= self.confidence_threshold
        
        return {
            'predictions': mean_prediction,
            'mc_samples': mc_samples_tensor,
            'mean_prediction': mean_prediction,
            'prediction_std': prediction_std,
            'aleatoric_uncertainty': aleatoric_uncertainty,
            'epistemic_uncertainty': epistemic_uncertainty,
            'total_uncertainty': total_uncertainty,
            'confidence': confidence,
            'high_confidence_mask': high_confidence_mask
        }
    
    def make_confident_prediction(self, inputs, model, training=None):
        """
        进行置信度感知的预测
        
        Args:
            inputs: 输入数据
            model: 预测模型
            training: 训练模式
            
        Returns:
            confident_result: {
                'predictions': 预测结果,
                'confidence': 置信度,
                'reliable_predictions': 可靠预测 (高置信度),
                'uncertain_predictions': 不确定预测 (低置信度),
                'reliability_ratio': 可靠性比例
            }
        """
        uncertainty_result = self.propagate_uncertainty(inputs, model, training)
        
        predictions = uncertainty_result['predictions']
        confidence = uncertainty_result['confidence']
        high_confidence_mask = uncertainty_result['high_confidence_mask']
        
        # 分离可靠和不确定的预测
        reliable_predictions = tf.where(
            high_confidence_mask,
            predictions,
            tf.zeros_like(predictions)
        )
        
        uncertain_predictions = tf.where(
            ~high_confidence_mask,
            predictions,
            tf.zeros_like(predictions)
        )
        
        # 计算可靠性比例
        reliability_ratio = tf.reduce_mean(tf.cast(high_confidence_mask, tf.float32))
        
        return {
            'predictions': predictions,
            'confidence': confidence,
            'reliable_predictions': reliable_predictions,
            'uncertain_predictions': uncertain_predictions,
            'reliability_ratio': reliability_ratio,
            'uncertainty_details': uncertainty_result
        }
    
    def adaptive_threshold_adjustment(self, confidence_history, target_reliability=0.8):
        """
        自适应调整置信度阈值
        
        Args:
            confidence_history: 历史置信度数据
            target_reliability: 目标可靠性比例
            
        Returns:
            new_threshold: 新的置信度阈值
        """
        # 计算当前可靠性比例
        current_reliability = tf.reduce_mean(
            tf.cast(confidence_history >= self.confidence_threshold, tf.float32)
        )
        
        # 调整阈值
        if current_reliability < target_reliability:
            # 降低阈值以提高可靠性比例
            new_threshold = self.confidence_threshold * 0.95
        elif current_reliability > target_reliability + 0.1:
            # 提高阈值以保持质量
            new_threshold = self.confidence_threshold * 1.05
        else:
            new_threshold = self.confidence_threshold
        
        # 限制阈值范围
        new_threshold = tf.clip_by_value(new_threshold, 0.5, 0.95)
        
        return new_threshold
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'num_mc_samples': self.num_mc_samples,
            'uncertainty_types': self.uncertainty_types,
            'confidence_method': self.confidence_method,
            'confidence_threshold': self.confidence_threshold,
            'temperature': self.temperature
        })
        return config


class UncertaintyVisualization:
    """不确定性可视化工具"""
    
    @staticmethod
    def plot_uncertainty_distribution(uncertainty_data, save_path=None):
        """绘制不确定性分布图"""
        import matplotlib.pyplot as plt
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 偶然不确定性分布
        axes[0, 0].hist(uncertainty_data['aleatoric_uncertainty'].numpy().flatten(), 
                       bins=50, alpha=0.7, color='blue')
        axes[0, 0].set_title('Aleatoric Uncertainty Distribution')
        axes[0, 0].set_xlabel('Uncertainty')
        axes[0, 0].set_ylabel('Frequency')
        
        # 认知不确定性分布
        axes[0, 1].hist(uncertainty_data['epistemic_uncertainty'].numpy().flatten(), 
                       bins=50, alpha=0.7, color='red')
        axes[0, 1].set_title('Epistemic Uncertainty Distribution')
        axes[0, 1].set_xlabel('Uncertainty')
        axes[0, 1].set_ylabel('Frequency')
        
        # 总不确定性分布
        axes[1, 0].hist(uncertainty_data['total_uncertainty'].numpy().flatten(), 
                       bins=50, alpha=0.7, color='green')
        axes[1, 0].set_title('Total Uncertainty Distribution')
        axes[1, 0].set_xlabel('Uncertainty')
        axes[1, 0].set_ylabel('Frequency')
        
        # 置信度分布
        axes[1, 1].hist(uncertainty_data['confidence'].numpy().flatten(), 
                       bins=50, alpha=0.7, color='orange')
        axes[1, 1].set_title('Confidence Distribution')
        axes[1, 1].set_xlabel('Confidence')
        axes[1, 1].set_ylabel('Frequency')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    @staticmethod
    def plot_confidence_vs_accuracy(predictions, targets, confidence, save_path=None):
        """绘制置信度vs准确率图"""
        import matplotlib.pyplot as plt
        
        # 计算准确率
        accuracy = tf.cast(
            tf.equal(tf.argmax(predictions, axis=-1), tf.argmax(targets, axis=-1)),
            tf.float32
        )
        
        # 按置信度排序
        sorted_indices = tf.argsort(confidence.numpy().flatten())
        sorted_confidence = tf.gather(confidence, sorted_indices)
        sorted_accuracy = tf.gather(accuracy, sorted_indices)
        
        # 计算滑动平均
        window_size = len(sorted_confidence) // 20
        moving_avg_conf = []
        moving_avg_acc = []
        
        for i in range(0, len(sorted_confidence) - window_size, window_size):
            window_conf = sorted_confidence[i:i+window_size]
            window_acc = sorted_accuracy[i:i+window_size]
            moving_avg_conf.append(tf.reduce_mean(window_conf))
            moving_avg_acc.append(tf.reduce_mean(window_acc))
        
        # 绘图
        plt.figure(figsize=(10, 6))
        plt.plot(moving_avg_conf, moving_avg_acc, 'b-', linewidth=2, label='Confidence vs Accuracy')
        plt.plot([0, 1], [0, 1], 'r--', linewidth=1, label='Perfect Calibration')
        plt.xlabel('Confidence')
        plt.ylabel('Accuracy')
        plt.title('Confidence vs Accuracy Calibration')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return plt.gcf()

import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import sys
sys.path.append('..')
import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler, StandardScaler

# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    tf.config.set_visible_devices(gpus[0], 'GPU')
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    print(e)

def generate_semantic_attributes_offline():
    """
    生成离线语义属性嵌入（模拟BERT效果）
    基于故障类型的语义特征手工制作768维向量
    """
    print("🔧 使用离线语义属性生成（避免网络依赖）...")
    
    # 故障类型的语义特征定义
    fault_features = {
        1: {"component": "feed", "type": "step", "location": "AC_ratio", "intensity": 0.8},
        2: {"component": "feed", "type": "step", "location": "B_composition", "intensity": 0.8},
        3: {"component": "feed", "type": "step", "location": "D_temperature", "intensity": 0.7},
        4: {"component": "reactor", "type": "step", "location": "cooling_water", "intensity": 0.6},
        5: {"component": "condenser", "type": "step", "location": "cooling_water", "intensity": 0.6},
        6: {"component": "feed", "type": "loss", "location": "A_feed", "intensity": 0.9},
        7: {"component": "header", "type": "loss", "location": "C_pressure", "intensity": 0.8},
        8: {"component": "feed", "type": "random", "location": "ABC_composition", "intensity": 0.5},
        9: {"component": "feed", "type": "random", "location": "D_temperature", "intensity": 0.4},
        10: {"component": "feed", "type": "random", "location": "C_temperature", "intensity": 0.4},
        11: {"component": "reactor", "type": "random", "location": "cooling_water", "intensity": 0.3},
        12: {"component": "condenser", "type": "random", "location": "cooling_water", "intensity": 0.3},
        13: {"component": "reactor", "type": "drift", "location": "kinetics", "intensity": 0.6},
        14: {"component": "reactor", "type": "stick", "location": "valve", "intensity": 0.7},
        15: {"component": "condenser", "type": "stick", "location": "valve", "intensity": 0.7}
    }
    
    # 创建语义向量基础模板
    semantic_embeddings = []
    
    for fault_id in range(1, 16):
        features = fault_features[fault_id]
        
        # 生成768维向量
        vector = np.zeros(768)
        
        # 分段编码不同的语义特征
        # [0:128] - 组件类型特征
        if features["component"] == "feed":
            vector[0:40] = np.random.normal(0.6, 0.1, 40)
        elif features["component"] == "reactor":
            vector[40:80] = np.random.normal(0.7, 0.1, 40)
        elif features["component"] == "condenser":
            vector[80:120] = np.random.normal(0.5, 0.1, 40)
        else:  # header
            vector[120:128] = np.random.normal(0.4, 0.1, 8)
            
        # [128:256] - 故障类型特征
        if features["type"] == "step":
            vector[128:168] = np.random.normal(0.8, 0.1, 40)
        elif features["type"] == "random":
            vector[168:208] = np.random.normal(-0.3, 0.1, 40)
        elif features["type"] == "drift":
            vector[208:228] = np.random.normal(0.0, 0.2, 20)
        elif features["type"] == "stick":
            vector[228:248] = np.random.normal(0.9, 0.1, 20)
        elif features["type"] == "loss":
            vector[248:256] = np.random.normal(1.0, 0.1, 8)
            
        # [256:512] - 位置特征 
        location_hash = hash(features["location"]) % 256
        vector[256 + location_hash // 4] = 0.8
        vector[256 + (location_hash + 1) % 64] = features["intensity"]
        
        # [512:768] - 随机语义噪声（模拟BERT的复杂性）
        vector[512:768] = np.random.normal(0, 0.1, 256)
        
        # 添加故障特异性
        vector[fault_id * 10 % 768] = features["intensity"]
        vector[(fault_id * 17 + 50) % 768] = features["intensity"] * 0.8
        
        semantic_embeddings.append(vector)
    
    # 转换为数组并标准化
    all_embeddings_array = np.array(semantic_embeddings)
    
    # 标准化处理
    scaler = StandardScaler()
    all_embeddings_normalized = scaler.fit_transform(all_embeddings_array)
    
    # 保存
    np.save('../tep_semantic_attributes.npy', all_embeddings_normalized)
    print(f"🎯 离线语义属性已生成并保存，形状: {all_embeddings_normalized.shape}")
    
    return all_embeddings_normalized

def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = BatchNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = BatchNormalization()(y)
    
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        x_reshaped = K.expand_dims(x, axis=1)
        q = self.query(x_reshaped)
        k = self.key(x_reshaped)
        v = self.value(x_reshaped)
        attention_scores = K.batch_dot(q, k, axes=[2, 2])
        attention_probs = K.softmax(attention_scores)
        context = K.batch_dot(attention_probs, v)
        context = K.squeeze(context, axis=1)
        return x + self.gamma * context
        
class Zero_shot_Semantic():
    def __init__(self):
        self.data_lenth = 52
        self.sample_shape = (self.data_lenth,)
        
        self.feature_dim = 256
        self.feature_shape = (256,)
        self.num_classes = 15
        self.latent_dim = 50
        self.noise_shape = (self.latent_dim, 1)
        self.n_critic = 1
        self.crl = True

        # 🔑 方案A: 使用768维语义属性
        self.attribute_dim = 768
        self.attribute_shape = (self.attribute_dim,)

        self.lambda_cla = 10 
        self.lambda_triplet = 10 
        self.lambda_crl = 0.01 
        
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        # 🔑 降低学习率以适应更大的参数空间
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        
        # 🔑 加载语义属性
        try:
            self.semantic_attributes = np.load('../tep_semantic_attributes.npy')
            print(f"已加载语义属性，形状: {self.semantic_attributes.shape}")
        except FileNotFoundError:
            print("语义属性文件不存在，正在生成...")
            self.semantic_attributes = generate_semantic_attributes_offline()
        
        self.autoencoder = self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c = self.build_classifier()
        
    def build_autoencoder(self):
        sample = Input(shape=self.sample_shape)     
        a0 = sample

        # Encoder
        a1 = Dense(100)(a0)
        a1 = LeakyReLU(alpha=0.2)(a1)
        a1 = BatchNormalization()(a1)

        a2 = Dense(200)(a1)
        a2 = LeakyReLU(alpha=0.2)(a2)
        a2 = BatchNormalization()(a2)

        a3 = Dense(256)(a2)
        a3 = LeakyReLU(alpha=0.2)(a3)
        a3 = BatchNormalization()(a3)
        feature = a3

        # Decoder
        a4 = Dense(200)(feature)
        a4 = LeakyReLU(alpha=0.2)(a4)
        a4 = BatchNormalization()(a4)

        a5 = Dense(100)(a4)
        a5 = LeakyReLU(alpha=0.2)(a5)
        a5 = BatchNormalization()(a5)

        a6 = Dense(52)(a5)
        a6 = LeakyReLU(alpha=0.2)(a6)
        a6 = BatchNormalization()(a6)
        output_sample = a6

        autoencoder = Model(sample, [feature, output_sample])
        self.encoder = Model(sample, feature)
        return autoencoder    
        
    def build_discriminator(self):
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=self.attribute_shape, dtype='float32')

        # 🔑 属性嵌入需要适配更大的维度
        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input, attribute], validity)

    def build_generator(self):
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=self.attribute_shape, dtype='float32')
      
        noise_embedding = Flatten()(noise)
        
        # 🔑 对768维属性进行降维处理
        attribute_embedding = Dense(128)(attribute)
        attribute_embedding = Dense(self.latent_dim)(attribute_embedding)
      
        g_input = concatenate([noise_embedding, attribute_embedding])

        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = BatchNormalization()(g1)

        g2 = residual_block(g1, 256) 
        g3 = residual_block(g2, 256) 
      
        g3_attention = SelfAttention()(g3)
      
        generated_feature = Dense(256)(g3_attention)
        generated_feature = BatchNormalization()(generated_feature)

        return Model([noise, attribute], generated_feature)
    
    def build_classifier(self):
        sample = Input(shape=self.feature_shape)

        c0 = sample
        c1 = Dense(100)(c0)
        c1 = LeakyReLU(alpha=0.2)(c1)
        
        c2 = Dense(50)(c1)
        c2 = LeakyReLU(alpha=0.2)(c2)
        hidden_ouput = c2
               
        # 🔑 输出层改为768维，激活函数改为linear
        c3 = Dense(self.attribute_dim, activation="linear")(c2)
        predict_attribute = c3
        
        return Model(sample, [hidden_ouput, predict_attribute])

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        # 🔑 损失函数改为MSE
        classification_loss = tf.keras.losses.mean_squared_error(y_true, pred_attribute)
        
        mi_penalty = 0    
        if self.bound == True:    
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def cycle_rank_loss(self, anchor, positive, negative):
        return self.triplet_loss(anchor, positive, negative)
    
    def train(self, epochs, batch_size, log_file=None, test_class_indices=None):
        start_time = datetime.datetime.now()
        
        accuracy_list_1 = []
        accuracy_list_2 = []
        accuracy_list_3 = []
        accuracy_list_4 = []
        
        valid = -np.ones((batch_size, 1))
        fake = np.ones((batch_size, 1))
        
        PATH_train = '../data/dataset_train_case1.npz'
        PATH_test = '../data/dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        if test_class_indices is None:
            test_class_indices = [9, 13, 15]  # 默认E组
        
        all_class_indices = list(range(1, 16))
        seen_class_indices = [i for i in all_class_indices if i not in test_class_indices]
        
        train_X_by_class = {i: train_data[f'training_samples_{i}'] for i in seen_class_indices}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        # 🔑 使用语义属性替代原始属性
        all_train_Y = self.semantic_attributes[all_train_labels - 1]

        test_X_by_class = {i: test_data[f'testing_samples_{i}'] for i in test_class_indices}
        test_X = np.concatenate([v for k, v in test_X_by_class.items()])
        test_classlabel = np.concatenate([np.full(len(v), k) for k, v in test_X_by_class.items()])
        test_Y = self.semantic_attributes[test_classlabel - 1]  # 🔑 测试集也使用语义属性

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # 重新组织缩放后的数据
        current_pos = 0
        for i in seen_class_indices:
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata = all_train_X
        train_attributelabel = all_train_Y
        train_classlabel = all_train_labels
        
        testdata = test_X
        test_attributelabel = test_Y
       
        num_batches = int(traindata.shape[0] / batch_size)
        
        print(f"🔥 方案A离线版训练开始：使用{self.attribute_dim}维语义属性")
        print(f"训练样本: {len(traindata)}, 测试类别: {test_class_indices}")
               
        for epoch in range(epochs):
            for batch_i in range(num_batches):
                start_i = batch_i * batch_size
                end_i = (batch_i + 1) * batch_size
                
                train_x = traindata[start_i:end_i]
                train_y = train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # Autoencoder and Classifier Training
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                    feature, output_sample = self.autoencoder(train_x)
                    autoencoder_loss = mean_squared_error(train_x, output_sample)      

                    hidden_ouput_c, predict_attribute_c = self.c(feature)
                    c_loss = self.classification_loss(feature, train_y, hidden_ouput_c, predict_attribute_c)

                    total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c

                # Triplet Loss Metric Learning
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []
                for label in train_labels:
                    pos_class_samples = train_X_by_class[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    
                    neg_class = np.random.choice([c for c in seen_class_indices if c != label])
                    neg_class_samples = train_X_by_class[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                # Discriminator Training
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False

                for _ in range(self.n_critic):
                    with tf.GradientTape() as tape_d:
                        noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                        fake_feature = self.g([noise, train_y])
                        real_feature = self.encoder(train_x)
        
                        real_validity = self.d([real_feature, train_y])
                        fake_validity = self.d([fake_feature, train_y])  
                                           
                        d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                        d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                        d_loss = d_loss_real + d_loss_fake
                  
                    grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                    self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # Generator Training
                self.d.trainable = False               
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                    noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    Fake_feature_g = self.g([noise_g, train_y])
                    Fake_validity_g = self.d([Fake_feature_g, train_y])
                    adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
            
                    fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                    classification_loss = self.classification_loss(Fake_feature_g, train_y, fake_hidden_ouput_g, Fake_classification_g)
                  
                    # Triplet loss for Generator
                    g_anchor_features = Fake_feature_g
                    g_positive_features = self.encoder(positive_samples)
                    g_negative_features = self.encoder(negative_samples)
                    triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                  
                    cycle_rank_loss = 0
                    if self.crl == True:
                        reconstructed_feature = self.g([noise_g, Fake_classification_g])
                        
                        negative_attributes = np.array([self.semantic_attributes[np.random.choice([c for c in seen_class_indices if c != label]) - 1] for label in train_labels])
                        unsimilar_generated_feature = self.g([noise_g, negative_attributes])

                        cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                           
                    total_loss = adversarial_loss + self.lambda_cla * classification_loss + self.lambda_triplet * triplet_loss_g + self.lambda_crl * cycle_rank_loss  
                          
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][M loss: %f][D loss: %f][G loss %05f ]time: %s " \
                 % (epoch, epochs,
                   batch_i, num_batches,
                   tf.reduce_mean(total_ac_loss), 
                     m_loss,
                     d_loss,
                     tf.reduce_mean(total_loss),                                                                                                              
                     elapsed_time))
        
            if epoch % 1 == 0:
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis(
                    2000, testdata, test_attributelabel, self.autoencoder, self.g, self.c, test_class_indices)  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                  %(epoch, epochs, max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)))
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
            
        best_accuracy = max([max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)])
        print('🎯 方案A离线版完成! best_acc:{:.4f}'.format(best_accuracy))
        if log_file:
            log_file.write(f'🎯 方案A离线版完成! best_acc:{best_accuracy:.4f}\n')
            log_file.flush()
        
        return best_accuracy
                
if __name__ == '__main__':
    TARGET_GROUP = 'E'  # 先用E组验证方案A
    
    GROUP_CONFIGS = {
        'A': [1, 6, 14],   
        'B': [4, 7, 10],   
        'C': [8, 11, 12],  
        'D': [2, 3, 5],    
        'E': [9, 13, 15],  
    }
    
    results_dir = "../结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + f"_Semantic_offline_Group{TARGET_GROUP}.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"🚀 阶段一：离线语义基线验证 - Group {TARGET_GROUP}")
    print(f"测试类别: {GROUP_CONFIGS[TARGET_GROUP]}")
    print(f"日志文件: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 🚀 阶段一：离线语义基线验证 (Group {TARGET_GROUP})\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**实验组别**: Group {TARGET_GROUP}\n")
        log_file.write(f"**测试类别**: {GROUP_CONFIGS[TARGET_GROUP]}\n")
        log_file.write(f"**方案**: 方案A离线版 - 手工语义属性 (768维)\n")
        log_file.write(f"**特点**: 无网络依赖，基于故障特征的语义编码\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = Zero_shot_Semantic()
        best_acc = gan.train(epochs=100, batch_size=256, log_file=log_file, test_class_indices=GROUP_CONFIGS[TARGET_GROUP])

        # 评估结果
        if best_acc > 0.6:
            result_status = "🟢 显著成功"
            next_step = "直接推进方案C"
        elif best_acc > 0.55:
            result_status = "🟡 中等成功"  
            next_step = "可推进方案C，建议实施方案B作为对比"
        else:
            result_status = "🔴 效果有限"
            next_step = "必须先实施方案B验证混合信息价值"

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"## 🎯 阶段一结果评估\n\n")
        log_file.write(f"**最佳准确率**: {best_acc:.4f}\n")
        log_file.write(f"**结果状态**: {result_status}\n")
        log_file.write(f"**下步建议**: {next_step}\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"\n🎯 阶段一完成!")
    print(f"结果状态: {result_status}")
    print(f"下步建议: {next_step}")
    print(f"详细日志: {log_filename}") 
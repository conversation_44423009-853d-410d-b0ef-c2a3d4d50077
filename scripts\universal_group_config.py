#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 通用组别配置工具
解决seen_class_map硬编码问题，支持任意组别配置
"""

import numpy as np
import tensorflow as tf

class UniversalGroupConfig:
    """通用组别配置管理器"""
    
    # 🔥 预定义组别配置（可扩展）
    GROUP_CONFIGS = {
        'A': [1, 6, 14],
        'B': [2, 5, 9], 
        'C': [8, 11, 12],
        'D': [3, 7, 10],
        'E': [4, 13, 15],
        # 可以继续添加更多组别...
    }
    
    @staticmethod
    def get_test_classes(group_name=None, test_classes=None):
        """获取测试类别
        
        Args:
            group_name: 组别名称 ('A', 'B', 'C', 'D', 'E')
            test_classes: 自定义测试类别列表
            
        Returns:
            list: 测试类别列表
        """
        if test_classes is not None:
            return test_classes
            
        if group_name is None:
            raise ValueError("必须提供group_name或test_classes参数")
            
        group_name = group_name.upper()
        if group_name not in UniversalGroupConfig.GROUP_CONFIGS:
            available_groups = list(UniversalGroupConfig.GROUP_CONFIGS.keys())
            raise ValueError(f"未知组别 '{group_name}'，可用组别: {available_groups}")
            
        return UniversalGroupConfig.GROUP_CONFIGS[group_name]
    
    @staticmethod
    def get_train_classes(test_classes, all_classes=None):
        """获取训练类别
        
        Args:
            test_classes: 测试类别列表
            all_classes: 所有类别列表，默认为[1,2,...,15]
            
        Returns:
            list: 训练类别列表
        """
        if all_classes is None:
            all_classes = list(range(1, 16))  # [1, 2, 3, ..., 15]
            
        return [c for c in all_classes if c not in test_classes]
    
    @staticmethod
    def create_seen_class_map(train_classes):
        """创建seen_class_map
        
        Args:
            train_classes: 训练类别列表
            
        Returns:
            dict: seen_class_map {class_id: index}
        """
        return {class_id: idx for idx, class_id in enumerate(train_classes)}
    
    @staticmethod
    def initialize_centers(num_classes, feature_dim, stddev=0.01):
        """初始化类别中心
        
        Args:
            num_classes: 类别数量
            feature_dim: 特征维度
            stddev: 标准差
            
        Returns:
            tf.Variable: 类别中心变量
        """
        return tf.Variable(
            tf.random.normal([num_classes, feature_dim], stddev=stddev),
            trainable=True,
            name='class_centers'
        )
    
    @staticmethod
    def setup_group_config(group_name=None, test_classes=None, all_classes=None, feature_dim=256):
        """一站式组别配置设置
        
        Args:
            group_name: 组别名称
            test_classes: 自定义测试类别
            all_classes: 所有类别列表
            feature_dim: 特征维度
            
        Returns:
            tuple: (test_classes, train_classes, seen_class_map, centers, num_seen_classes)
        """
        # 获取测试类别
        test_classes = UniversalGroupConfig.get_test_classes(group_name, test_classes)
        
        # 获取训练类别
        train_classes = UniversalGroupConfig.get_train_classes(test_classes, all_classes)
        
        # 创建seen_class_map
        seen_class_map = UniversalGroupConfig.create_seen_class_map(train_classes)
        
        # 初始化中心
        num_seen_classes = len(train_classes)
        centers = UniversalGroupConfig.initialize_centers(num_seen_classes, feature_dim)
        
        return test_classes, train_classes, seen_class_map, centers, num_seen_classes
    
    @staticmethod
    def print_config_info(group_name, test_classes, train_classes, seen_class_map):
        """打印配置信息
        
        Args:
            group_name: 组别名称
            test_classes: 测试类别列表
            train_classes: 训练类别列表
            seen_class_map: seen_class_map字典
        """
        print(f"🔧 {group_name.upper()}组配置验证:")
        print(f"   测试类别: {test_classes}")
        print(f"   训练类别: {train_classes}")
        print(f"   seen_class_map: {seen_class_map}")
        print(f"   训练类别数量: {len(train_classes)}")
        
        # 验证seen_class_map的完整性
        expected_indices = set(range(len(train_classes)))
        actual_indices = set(seen_class_map.values())
        
        if expected_indices == actual_indices:
            print(f"   ✅ seen_class_map验证通过")
        else:
            print(f"   ❌ seen_class_map验证失败!")
            print(f"      期望索引: {sorted(expected_indices)}")
            print(f"      实际索引: {sorted(actual_indices)}")

def safe_center_loss(features, labels, centers, seen_class_map):
    """安全的Center Loss计算
    
    Args:
        features: 特征张量
        labels: 标签数组
        centers: 中心变量
        seen_class_map: seen_class_map字典
        
    Returns:
        tf.Tensor: center loss值
    """
    if centers is None or seen_class_map is None:
        return tf.constant(0.0)

    # 转换标签为索引
    if hasattr(labels, 'numpy'):
        labels = labels.numpy()

    # 处理numpy数组格式的标签
    if len(labels.shape) > 1:
        labels = labels.flatten()

    # 安全过滤：只处理训练类别
    mapped_labels = []
    valid_features = []
    
    for i, label in enumerate(labels):
        if isinstance(label, np.ndarray):
            label = label.item()
        
        # 只处理在seen_class_map中的训练类别
        if int(label) in seen_class_map:
            mapped_labels.append(seen_class_map[int(label)])
            valid_features.append(features[i])

    # 如果没有有效的训练类别样本，返回0损失
    if len(mapped_labels) == 0:
        return tf.constant(0.0)

    mapped_labels = tf.constant(mapped_labels, dtype=tf.int32)
    valid_features = tf.stack(valid_features)

    # 获取当前批次的中心
    batch_centers = tf.gather(centers, mapped_labels)

    # 计算特征与对应中心的距离
    center_loss = tf.reduce_mean(tf.reduce_sum(tf.square(valid_features - batch_centers), axis=1))

    return center_loss

def safe_update_centers(features, labels, centers, seen_class_map, center_optimizer):
    """安全的中心更新
    
    Args:
        features: 特征数组
        labels: 标签数组
        centers: 中心变量
        seen_class_map: seen_class_map字典
        center_optimizer: 中心优化器
    """
    if centers is None or seen_class_map is None:
        return

    # 过滤出训练类别的样本
    valid_indices = []
    valid_labels = []
    
    for i, label in enumerate(labels):
        if isinstance(label, np.ndarray):
            label = label.item()
        
        # 只处理训练类别
        if int(label) in seen_class_map:
            valid_indices.append(i)
            valid_labels.append(seen_class_map[int(label)])

    if len(valid_indices) == 0:
        return

    valid_features = features[valid_indices]
    valid_labels = tf.constant(valid_labels, dtype=tf.int32)
    unique_labels = tf.unique(valid_labels)[0]

    for label in unique_labels:
        mask = tf.equal(valid_labels, label)
        if tf.reduce_sum(tf.cast(mask, tf.int32)) > 0:
            label_features = tf.boolean_mask(valid_features, mask)
            center_update = tf.reduce_mean(label_features, axis=0)

            # 使用梯度下降更新中心
            with tf.GradientTape() as tape:
                current_center = tf.gather(centers, label)
                center_loss = tf.reduce_mean(tf.square(current_center - center_update))

            center_grad = tape.gradient(center_loss, [centers])
            if center_grad[0] is not None:
                center_optimizer.apply_gradients([(center_grad[0], centers)])

# 使用示例
if __name__ == "__main__":
    print("🔥 通用组别配置工具测试")
    
    # 测试各组配置
    for group in ['A', 'B', 'C', 'D', 'E']:
        print(f"\n--- {group}组配置 ---")
        test_classes, train_classes, seen_class_map, centers, num_seen_classes = \
            UniversalGroupConfig.setup_group_config(group_name=group, feature_dim=256)
        
        UniversalGroupConfig.print_config_info(group, test_classes, train_classes, seen_class_map)
    
    # 测试自定义配置
    print(f"\n--- 自定义配置 ---")
    test_classes, train_classes, seen_class_map, centers, num_seen_classes = \
        UniversalGroupConfig.setup_group_config(test_classes=[1, 2, 3], feature_dim=256)
    
    UniversalGroupConfig.print_config_info("Custom", test_classes, train_classes, seen_class_map)

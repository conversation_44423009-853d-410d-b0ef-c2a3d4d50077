## 🎯 **TensorBoard在Docker容器中的访问解决方案**

### **问题描述**
- Docker容器内启动的TensorBoard无法通过 `localhost:6006` 访问
- 容器没有配置端口映射到宿主机

### **解决方案总结**

#### **1. 获取容器IP地址**
```bash
# 在宿主机上执行
CONTAINER_IP=$(docker inspect acgan-container --format='{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}')
echo "容器IP: $CONTAINER_IP"
# 输出示例: 容器IP: **********
```

#### **2. 在容器内启动TensorBoard**
```bash
# 进入容器
docker exec -it acgan-container /bin/bash

# 在容器内启动TensorBoard
cd /app
tensorboard --logdir logs/baseline --bind_all --port 6007
```

**重要参数说明：**
- `--bind_all`: 绑定到所有网络接口（等同于 `--host 0.0.0.0`）
- `--port 6007`: 指定端口（避免与其他服务冲突）
- **不要同时使用** `--host` 和 `--bind_all` 参数

#### **3. 通过容器IP访问TensorBoard**
在浏览器中访问：
```
http://**********:6007
```

### **故障排除步骤**

#### **如果端口被占用：**
```bash
# 停止现有TensorBoard进程
pkill -f tensorboard

# 使用不同端口重新启动
tensorboard --logdir logs/baseline --bind_all --port 6008
```

#### **验证TensorBoard是否正常启动：**
```bash
# 查看TensorBoard进程
ps aux | grep tensorboard

# 在容器内测试本地访问
curl http://localhost:6007
```

### **最佳实践**

#### **方案1: 直接使用容器IP（当前方案）**
- ✅ 简单快速
- ✅ 不需要重新创建容器
- ❌ 需要记住容器IP

#### **方案2: 重新创建容器（推荐用于生产）**
```bash
# 停止现有容器
docker stop acgan-container

# 重新启动容器，添加端口映射
docker run -it --gpus all --name acgan-container-new \
  -p 6006:6006 \
  -v $(pwd):/app \
  your_image_name /bin/bash

# 然后可以直接访问 http://localhost:6006
```

### **完整的操作记录**

```bash
# 1. 获取容器IP
CONTAINER_IP=$(docker inspect acgan-container --format='{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}')

# 2. 进入容器
docker exec -it acgan-container /bin/bash

# 3. 在容器内启动TensorBoard
cd /app
tensorboard --logdir logs/baseline --bind_all --port 6007

# 4. 在浏览器中访问
# http://**********:6007
```

### **关键要点**
1. **容器IP通常是 `172.17.0.x` 格式**
2. **使用 `--bind_all` 而不是 `--host 0.0.0.0 --bind_all`**
3. **选择未被占用的端口（如6007、6008等）**
4. **确保在正确的日志目录下启动TensorBoard**

这个解决方案适用于所有没有端口映射的Docker容器中的Web服务访问问题！
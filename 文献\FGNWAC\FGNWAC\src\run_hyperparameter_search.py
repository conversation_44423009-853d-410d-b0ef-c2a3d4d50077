#!/usr/bin/env python3
"""
VAEGAN-AR超参数搜索启动脚本
支持分阶段或完整搜索，提供命令行接口
"""

import argparse
import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hyperparameter_search import HyperparameterSearcher
from search_config import HyperparameterSearchConfig


def print_banner():
    """打印启动横幅"""
    print("=" * 70)
    print("🔍 VAEGAN-AR 超参数搜索系统")
    print("   基于两阶段网格搜索优化 lambda_ar 和学习率")
    print("=" * 70)


def validate_arguments(args):
    """验证命令行参数"""
    # 验证split参数
    valid_splits = ['A', 'B', 'C', 'D', 'E']
    if args.split not in valid_splits:
        raise ValueError(f"无效的split参数: {args.split}，必须是 {valid_splits} 之一")
    
    # 验证stage参数
    valid_stages = [1, 2, 'all']
    if args.stage not in valid_stages:
        raise ValueError(f"无效的stage参数: {args.stage}，必须是 {valid_stages} 之一")
    
    # 验证epochs参数
    if args.epochs < 50:
        raise ValueError("epochs参数不能小于50")
    if args.epochs > 500:
        print(f"⚠️  警告: epochs={args.epochs}较大，可能需要很长时间")
    
    # 验证patience参数
    if args.patience < 5:
        raise ValueError("patience参数不能小于5")
    if args.patience > args.epochs // 2:
        raise ValueError("patience参数不能超过epochs的一半")


def setup_search_config(args):
    """根据命令行参数设置搜索配置"""
    config = HyperparameterSearchConfig()
    
    # 更新配置参数
    if args.epochs:
        config.search_epochs = args.epochs
    if args.patience:
        config.early_stop_patience = args.patience
    if args.min_epochs:
        config.min_epochs = args.min_epochs
    if hasattr(args, 'display_freq') and args.display_freq:
        config.display_frequency = args.display_freq
    
    # 自定义搜索范围
    if args.lambda_ar_range:
        config.lambda_ar_search_range = args.lambda_ar_range
    if args.lr_range:
        config.lr_search_range = args.lr_range
    
    return config


def run_stage_1(searcher, split_name):
    """运行阶段1: lambda_ar搜索"""
    print("\n🎯 执行阶段1: Lambda_AR搜索")
    print("-" * 50)
    
    try:
        best_lambda_ar = searcher.search_lambda_ar(split_name)
        print(f"✅ 阶段1完成，最佳lambda_ar: {best_lambda_ar}")
        return best_lambda_ar
    except Exception as e:
        print(f"❌ 阶段1失败: {str(e)}")
        return None


def run_stage_2(searcher, split_name, best_lambda_ar=None):
    """运行阶段2: 学习率搜索"""
    print("\n🎯 执行阶段2: 学习率搜索")
    print("-" * 50)
    
    # 如果没有提供best_lambda_ar，尝试从阶段1结果中加载
    if best_lambda_ar is None:
        try:
            stage1_results = searcher.load_stage_results(1, split_name)
            if stage1_results:
                successful_results = [r for r in stage1_results if 'best_accuracy' in r]
                if successful_results:
                    best_result = max(successful_results, key=lambda x: x['best_accuracy'])
                    best_lambda_ar = best_result['parameters']['lambda_ar']
                    print(f"📂 从阶段1结果加载最佳lambda_ar: {best_lambda_ar}")
                else:
                    raise ValueError("阶段1没有成功的实验结果")
            else:
                raise FileNotFoundError("找不到阶段1的结果文件")
        except Exception as e:
            print(f"❌ 无法加载阶段1结果: {str(e)}")
            print("💡 请先运行阶段1或提供--best-lambda-ar参数")
            return None
    
    try:
        best_lr = searcher.search_lr(split_name, best_lambda_ar)
        print(f"✅ 阶段2完成，最佳学习率: {best_lr}")
        return best_lr
    except Exception as e:
        print(f"❌ 阶段2失败: {str(e)}")
        return None


def run_full_search(searcher, split_name):
    """运行完整的两阶段搜索"""
    print("\n🎯 执行完整搜索 (阶段1 + 阶段2)")
    print("-" * 50)
    
    try:
        final_config = searcher.run_full_search(split_name)
        print("🎉 完整搜索成功完成!")
        print(f"   最佳配置: lambda_ar={final_config['best_lambda_ar']}, lr={final_config['best_lr']}")
        return final_config
    except Exception as e:
        print(f"❌ 完整搜索失败: {str(e)}")
        return None


def generate_report(searcher, split_name):
    """生成分析报告"""
    print("\n📊 生成分析报告")
    print("-" * 50)
    
    try:
        searcher.generate_analysis_report(split_name)
        print("✅ 分析报告生成完成")
    except Exception as e:
        print(f"❌ 报告生成失败: {str(e)}")


def print_search_summary(config, split_name, stage):
    """打印搜索摘要"""
    summary = config.get_search_summary()
    
    print(f"\n📋 搜索配置摘要")
    print("-" * 30)
    print(f"目标分组: {split_name}")
    print(f"执行阶段: {stage}")
    print(f"每个实验轮数: {config.search_epochs}")
    print(f"早停耐心值: {config.early_stop_patience}")
    print(f"Lambda_AR范围: {config.lambda_ar_search_range}")
    print(f"学习率范围: {config.lr_search_range}")
    
    if stage == 'all':
        print(f"总实验数: {summary['total_experiments']}")
        print(f"预估总时间: {summary['estimated_total_time_hours']:.1f}小时")
    elif stage == 1:
        print(f"阶段1实验数: {summary['stage1_experiments']}")
        print(f"预估时间: {summary['stage1_experiments'] * 2.5:.1f}小时")
    elif stage == 2:
        print(f"阶段2实验数: {summary['stage2_experiments']}")
        print(f"预估时间: {summary['stage2_experiments'] * 2.5:.1f}小时")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='VAEGAN-AR超参数搜索系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 对A组数据进行完整搜索
  python run_hyperparameter_search.py --split A --stage all
  
  # 只运行阶段1 (lambda_ar搜索)
  python run_hyperparameter_search.py --split A --stage 1
  
  # 只运行阶段2 (学习率搜索)
  python run_hyperparameter_search.py --split A --stage 2
  
  # 自定义训练轮数和早停参数
  python run_hyperparameter_search.py --split A --stage all --epochs 200 --patience 25
  
  # 自定义搜索范围
  python run_hyperparameter_search.py --split A --stage 1 --lambda-ar-range 0.1 0.5 1.0 2.0
        """
    )
    
    # 必需参数
    parser.add_argument('--split', type=str, required=True, 
                       choices=['A', 'B', 'C', 'D', 'E'],
                       help='数据分组 (A, B, C, D, E)')
    
    parser.add_argument('--stage', required=True,
                       help='搜索阶段: 1=lambda_ar搜索, 2=学习率搜索, all=完整搜索')
    
    # 可选参数
    parser.add_argument('--epochs', type=int, default=150,
                       help='每个实验的训练轮数 (默认: 150)')
    
    parser.add_argument('--patience', type=int, default=20,
                       help='早停耐心值 (默认: 20)')
    
    parser.add_argument('--min-epochs', type=int, default=50,
                       help='最少训练轮数 (默认: 50)')
    
    parser.add_argument('--lambda-ar-range', type=float, nargs='+',
                       help='自定义lambda_ar搜索范围 (例: 0.1 0.5 1.0)')
    
    parser.add_argument('--lr-range', type=float, nargs='+',
                       help='自定义学习率搜索范围 (例: 0.00005 0.0001 0.0002)')
    
    parser.add_argument('--best-lambda-ar', type=float,
                       help='阶段2使用的lambda_ar值 (如果不提供则从阶段1结果加载)')
    
    parser.add_argument('--generate-report', action='store_true',
                       help='生成分析报告 (需要已有搜索结果)')
    
    parser.add_argument('--verbose', action='store_true',
                       help='启用详细日志输出')

    parser.add_argument('--display-freq', type=int, default=1,
                       help='准确率显示频率 (1=每轮显示, 5=每5轮显示, 默认: 1)')
    
    # 解析参数
    args = parser.parse_args()
    
    # 处理stage参数
    if args.stage == 'all':
        args.stage = 'all'
    else:
        args.stage = int(args.stage)
    
    try:
        # 打印横幅
        print_banner()
        
        # 验证参数
        validate_arguments(args)
        
        # 设置搜索配置
        config = setup_search_config(args)
        
        # 打印搜索摘要
        print_search_summary(config, args.split, args.stage)
        
        # 创建搜索器
        searcher = HyperparameterSearcher(config)
        
        # 如果只是生成报告
        if args.generate_report:
            generate_report(searcher, args.split)
            return
        
        # 确认开始搜索
        print(f"\n⚠️  即将开始搜索，这可能需要数小时时间")
        response = input("是否继续? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("搜索已取消")
            return
        
        # 记录开始时间
        start_time = datetime.now()
        print(f"\n🚀 搜索开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 执行搜索
        if args.stage == 'all':
            final_config = run_full_search(searcher, args.split)
            if final_config:
                generate_report(searcher, args.split)
        elif args.stage == 1:
            best_lambda_ar = run_stage_1(searcher, args.split)
        elif args.stage == 2:
            best_lr = run_stage_2(searcher, args.split, args.best_lambda_ar)
        
        # 记录结束时间
        end_time = datetime.now()
        duration = end_time - start_time
        print(f"\n⏰ 搜索结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   总耗时: {duration}")
        
        print("\n🎉 搜索任务完成!")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  搜索被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 搜索失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()

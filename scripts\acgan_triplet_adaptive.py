import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics.pairwise import cosine_distances, euclidean_distances
import json


# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    tf.config.set_visible_devices(gpus[0], 'GPU')
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    print(e)


class TaskDifficultyAnalyzer:
    """任务难度分析器 - 计算seen和unseen类别间的语义距离"""
    
    def __init__(self, distance_metric='euclidean'):
        self.distance_metric = distance_metric
        self.difficulty_cache = {}
    
    def calculate_semantic_distance(self, attr1, attr2):
        """计算两个属性向量间的语义距离"""
        if self.distance_metric == 'euclidean':
            return np.linalg.norm(attr1 - attr2)
        elif self.distance_metric == 'cosine':
            return cosine_distances(attr1.reshape(1, -1), attr2.reshape(1, -1))[0, 0]
        else:
            raise ValueError(f"不支持的距离度量: {self.distance_metric}")
    
    def compute_task_difficulty(self, seen_attributes, unseen_attributes, group_name):
        """
        计算任务难度
        Difficulty(Group) = mean_{u in Unseen} (min_{s in Seen} (distance(attr(u), attr(s))))
        """
        if group_name in self.difficulty_cache:
            return self.difficulty_cache[group_name]
        
        difficulty_scores = []
        
        for unseen_attr in unseen_attributes:
            min_distance = float('inf')
            for seen_attr in seen_attributes:
                distance = self.calculate_semantic_distance(unseen_attr, seen_attr)
                min_distance = min(min_distance, distance)
            difficulty_scores.append(min_distance)
        
        difficulty = np.mean(difficulty_scores)
        self.difficulty_cache[group_name] = difficulty
        
        return difficulty
    
    def get_difficulty_stats(self, seen_attributes, unseen_attributes, group_name):
        """获取详细的难度统计信息"""
        difficulty = self.compute_task_difficulty(seen_attributes, unseen_attributes, group_name)
        
        stats = {
            'group_name': group_name,
            'difficulty_score': difficulty,
            'num_seen_classes': len(seen_attributes),
            'num_unseen_classes': len(unseen_attributes),
            'distance_metric': self.distance_metric
        }
        
        return stats


class AdaptiveWeightScheduler:
    """自适应权重调度器 - 根据任务难度动态调整损失权重"""
    
    def __init__(self, base_lambda_triplet=10, base_lambda_cla=10, alpha=1.0, beta=0.5):
        self.base_lambda_triplet = base_lambda_triplet
        self.base_lambda_cla = base_lambda_cla
        self.alpha = alpha  # triplet loss调整强度
        self.beta = beta    # classification loss调整强度
    
    def compute_adaptive_weights(self, difficulty_score):
        """
        根据任务难度计算自适应权重（改进版：温和调整）
        使用tanh函数进行平滑映射，避免过于激进的权重调整
        """
        import numpy as np
        
        # 使用tanh函数将难度映射到[-1, 1]范围，避免极端调整
        normalized_difficulty = np.tanh((difficulty_score - 1.0) * 0.5)
        
        # 温和的权重调整：最大调整幅度限制在30%和20%
        triplet_adjustment = 1.0 + normalized_difficulty * 0.3  # 最多±30%
        cla_adjustment = 1.0 - normalized_difficulty * 0.2      # 最多±20%
        
        # 进一步限制调整范围，确保权重在合理范围内
        triplet_adjustment = np.clip(triplet_adjustment, 0.7, 1.5)
        cla_adjustment = np.clip(cla_adjustment, 0.7, 1.5)
        
        lambda_triplet_adaptive = self.base_lambda_triplet * triplet_adjustment
        lambda_cla_adaptive = self.base_lambda_cla * cla_adjustment
        
        return {
            'lambda_triplet': lambda_triplet_adaptive,
            'lambda_cla': lambda_cla_adaptive,
            'difficulty_score': difficulty_score,
            'adjustment_ratio_triplet': triplet_adjustment,
            'adjustment_ratio_cla': cla_adjustment
        }


class ExperimentManager:
    """实验管理器 - 管理不同分组的实验配置和结果记录"""
    
    # 基于ACGAN_yuanma.py中的正确分组配置
    GROUP_CONFIGS = {
        'A': {'test_classes': [1, 6, 14]},
        'B': {'test_classes': [4, 7, 10]},
        'C': {'test_classes': [8, 11, 12]},
        'D': {'test_classes': [2, 3, 5]},
        'E': {'test_classes': [9, 13, 15]},
    }
    
    def __init__(self, target_group='E'):
        self.target_group = target_group
        self.config = self.GROUP_CONFIGS[target_group]
        self.experiment_results = {}
    
    def get_group_data(self, train_data, test_data):
        """根据分组配置获取训练和测试数据"""
        # test_classes就是unseen classes（使用1-15索引）
        unseen_classes = self.config['test_classes']
        
        # seen classes = 所有类别 - test_classes (1-15范围)
        all_classes = list(range(1, 16))  # [1, 2, 3, ..., 15]
        seen_classes = [i for i in all_classes if i not in unseen_classes]
        
        # 训练数据（seen classes），注意数据文件中的索引是{i}对应training_samples_{i}
        train_X_by_class = {i: train_data[f'training_samples_{i}'] for i in seen_classes}
        train_Y_by_class = {i: train_data[f'training_attribute_{i}'] for i in seen_classes}
        
        # 测试数据（unseen classes）
        test_X_by_class = {i: test_data[f'testing_samples_{i}'] for i in unseen_classes}
        test_Y_by_class = {i: test_data[f'testing_attribute_{i}'] for i in unseen_classes}
        
        return {
            'train_X_by_class': train_X_by_class,
            'train_Y_by_class': train_Y_by_class,
            'test_X_by_class': test_X_by_class,
            'test_Y_by_class': test_Y_by_class,
            'seen_classes': seen_classes,
            'unseen_classes': unseen_classes
        }
    
    def save_experiment_results(self, results, log_filename):
        """保存实验结果到JSON文件"""
        results_filename = log_filename.replace('.md', '_results.json')
        with open(results_filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)


def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = BatchNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = BatchNormalization()(y)
    
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y


class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        x_reshaped = K.expand_dims(x, axis=1)
        q = self.query(x_reshaped)
        k = self.key(x_reshaped)
        v = self.value(x_reshaped)
        
        attention_scores = K.batch_dot(q, k, axes=[2, 2])
        attention_probs = K.softmax(attention_scores)
        context = K.batch_dot(attention_probs, v)
        context = K.squeeze(context, axis=1)
        
        return x + self.gamma * context


class Zero_shot_Adaptive:
    def __init__(self, group='E', use_adaptive=True, alpha=1.0, beta=0.5):
        self.group = group
        self.use_adaptive = use_adaptive
        
        # 基础参数
        self.data_lenth = 52
        self.sample_shape = (self.data_lenth,)
        self.feature_dim = 256
        self.feature_shape = (256,)
        self.num_classes = 15
        self.latent_dim = 50
        self.noise_shape = (self.latent_dim, 1)
        self.n_critic = 1
        self.crl = True
        
        # 基础损失权重
        self.base_lambda_cla = 10
        self.base_lambda_triplet = 10
        self.lambda_crl = 0.01
        
        # 当前使用的权重（可能是自适应的）
        self.lambda_cla = self.base_lambda_cla
        self.lambda_triplet = self.base_lambda_triplet
        
        # 其他参数
        self.bound = True
        self.mi_weight = 0.001
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        # 自适应组件
        self.difficulty_analyzer = TaskDifficultyAnalyzer(distance_metric='euclidean')
        self.weight_scheduler = AdaptiveWeightScheduler(
            base_lambda_triplet=self.base_lambda_triplet,
            base_lambda_cla=self.base_lambda_cla,
            alpha=alpha,
            beta=beta
        )
        self.experiment_manager = ExperimentManager(target_group=group)
        
        # 优化器
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        
        # 构建模型
        self.autoencoder = self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c = self.build_classifier()
    
    def build_autoencoder(self):
        sample = Input(shape=self.sample_shape)
        a0 = sample

        # Encoder
        a1 = Dense(100)(a0)
        a1 = LeakyReLU(alpha=0.2)(a1)
        a1 = BatchNormalization()(a1)

        a2 = Dense(200)(a1)
        a2 = LeakyReLU(alpha=0.2)(a2)
        a2 = BatchNormalization()(a2)

        a3 = Dense(256)(a2)
        a3 = LeakyReLU(alpha=0.2)(a3)
        a3 = BatchNormalization()(a3)
        feature = a3

        # Decoder
        a4 = Dense(200)(feature)
        a4 = LeakyReLU(alpha=0.2)(a4)
        a4 = BatchNormalization()(a4)

        a5 = Dense(100)(a4)
        a5 = LeakyReLU(alpha=0.2)(a5)
        a5 = BatchNormalization()(a5)

        a6 = Dense(52)(a5)
        a6 = LeakyReLU(alpha=0.2)(a6)
        a6 = BatchNormalization()(a6)
        output_sample = a6

        autoencoder = Model(sample, [feature, output_sample])
        self.encoder = Model(sample, feature)
        return autoencoder

    def build_discriminator(self):
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input, attribute], validity)

    def build_generator(self):
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        noise_embedding = Flatten()(noise)
        attribute_embedding = Dense(self.latent_dim)(attribute)
        
        g_input = concatenate([noise_embedding, attribute_embedding])

        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = BatchNormalization()(g1)

        g2 = residual_block(g1, 256)
        g3 = residual_block(g2, 256)
        
        g3_attention = SelfAttention()(g3)
        
        generated_feature = Dense(256)(g3_attention)
        generated_feature = BatchNormalization()(generated_feature)

        return Model([noise, attribute], generated_feature)

    def build_classifier(self):
        sample = Input(shape=self.feature_shape)

        c0 = sample
        c1 = Dense(100)(c0)
        c1 = LeakyReLU(alpha=0.2)(c1)
        
        c2 = Dense(50)(c1)
        c2 = LeakyReLU(alpha=0.2)(c2)
        hidden_ouput = c2
               
        c3 = Dense(20, activation="sigmoid")(c2)
        predict_attribute = c3
        
        return Model(sample, [hidden_ouput, predict_attribute])

    def compute_task_difficulty(self, seen_attributes, unseen_attributes):
        """计算当前任务的难度并更新自适应权重"""
        difficulty_score = self.difficulty_analyzer.compute_task_difficulty(
            seen_attributes, unseen_attributes, f"Group_{self.group}"
        )
        
        if self.use_adaptive:
            adaptive_weights = self.weight_scheduler.compute_adaptive_weights(difficulty_score)
            self.lambda_triplet = adaptive_weights['lambda_triplet']
            self.lambda_cla = adaptive_weights['lambda_cla']
            return difficulty_score, adaptive_weights
        else:
            return difficulty_score, {
                'lambda_triplet': self.base_lambda_triplet,
                'lambda_cla': self.base_lambda_cla,
                'difficulty_score': difficulty_score,
                'adjustment_ratio_triplet': 1.0,
                'adjustment_ratio_cla': 1.0
            }

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        return K.mean(y_true * y_pred)

    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est

    def mi_penalty_loss(self, x, z):
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)

    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)
        
        mi_penalty = 0
        if self.bound == True:
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss

    def cycle_rank_loss(self, anchor, positive, negative):
        return self.triplet_loss(anchor, positive, negative)

    def train(self, epochs, batch_size, log_file=None):
        start_time = datetime.datetime.now()
        
        accuracy_list_1 = []
        accuracy_list_2 = []
        accuracy_list_3 = []
        accuracy_list_4 = []
        
        valid = -np.ones((batch_size, 1))
        fake = np.ones((batch_size, 1))
        
        PATH_train = './dataset_train_case1.npz'
        PATH_test = './dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        # 使用实验管理器获取分组数据
        group_data = self.experiment_manager.get_group_data(train_data, test_data)
        
        train_X_by_class = group_data['train_X_by_class']
        train_Y_by_class = group_data['train_Y_by_class']
        test_X_by_class = group_data['test_X_by_class']
        test_Y_by_class = group_data['test_Y_by_class']
        seen_classes = group_data['seen_classes']
        unseen_classes = group_data['unseen_classes']

        # 计算任务难度和自适应权重
        seen_attributes = np.array([train_Y_by_class[i][0] for i in seen_classes])
        unseen_attributes = np.array([test_Y_by_class[i][0] for i in unseen_classes])
        
        difficulty_score, adaptive_weights = self.compute_task_difficulty(seen_attributes, unseen_attributes)
        
        # 记录任务难度信息
        if log_file:
            log_file.write(f"## 自适应任务难度调整分析\n\n")
            log_file.write(f"**目标分组**: Group {self.group}\n")
            log_file.write(f"**任务难度得分**: {difficulty_score:.6f}\n")
            log_file.write(f"**自适应模式**: {'启用' if self.use_adaptive else '禁用'}\n")
            log_file.write(f"**seen类别**: {seen_classes}\n")
            log_file.write(f"**unseen类别**: {unseen_classes}\n\n")
            
            log_file.write(f"### 损失权重配置\n")
            log_file.write(f"- **Triplet Loss权重**: {adaptive_weights['lambda_triplet']:.6f} (调整比例: {adaptive_weights['adjustment_ratio_triplet']:.3f})\n")
            log_file.write(f"- **分类Loss权重**: {adaptive_weights['lambda_cla']:.6f} (调整比例: {adaptive_weights['adjustment_ratio_cla']:.3f})\n")
            log_file.write(f"- **基础Triplet权重**: {self.base_lambda_triplet}\n")
            log_file.write(f"- **基础分类权重**: {self.base_lambda_cla}\n\n")
            log_file.write("---\n\n")
            log_file.flush()

        # 准备训练数据
        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        test_X = np.concatenate([v for k, v in test_X_by_class.items()])
        test_Y = np.concatenate([v for k, v in test_Y_by_class.items()])
        test_classlabel = np.concatenate([np.full(len(v), k) for k, v in test_X_by_class.items()])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # 重新组织缩放后的数据
        current_pos = 0
        for i in seen_classes:
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata = all_train_X
        train_attributelabel = all_train_Y
        train_classlabel = all_train_labels
        
        testdata = test_X
        test_attributelabel = test_Y
       
        num_batches = int(traindata.shape[0] / batch_size)
        
        print(f"开始训练 Group {self.group} (自适应模式: {self.use_adaptive})")
        print(f"任务难度: {difficulty_score:.6f}")
        print(f"Triplet权重: {self.lambda_triplet:.6f}, 分类权重: {self.lambda_cla:.6f}")

        for epoch in range(epochs):
            for batch_i in range(num_batches):
                start_i = batch_i * batch_size
                end_i = (batch_i + 1) * batch_size
                
                train_x = traindata[start_i:end_i]
                train_y = train_attributelabel[start_i:end_i]
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # Autoencoder和Classifier训练
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                    feature, output_sample = self.autoencoder(train_x)
                    autoencoder_loss = mean_squared_error(train_x, output_sample)

                    hidden_ouput_c, predict_attribute_c = self.c(feature)
                    c_loss = self.classification_loss(feature, train_y, hidden_ouput_c, predict_attribute_c)

                    total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c

                # Triplet Loss度量学习
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []
                for label in train_labels:
                    pos_class_samples = train_X_by_class[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    
                    neg_class = np.random.choice([c for c in seen_classes if c != label])
                    neg_class_samples = train_X_by_class[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                # 判别器训练
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False

                for _ in range(self.n_critic):
                    with tf.GradientTape() as tape_d:
                        noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                        fake_feature = self.g([noise, train_y])
                        real_feature = self.encoder(train_x)
            
                        real_validity = self.d([real_feature, train_y])
                        fake_validity = self.d([fake_feature, train_y])
                                               
                        d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                        d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                        d_loss = d_loss_real + d_loss_fake
                    
                    grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                    self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # 生成器训练
                self.d.trainable = False               
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                    noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    Fake_feature_g = self.g([noise_g, train_y])
                    Fake_validity_g = self.d([Fake_feature_g, train_y])
                    adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
              
                    fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                    classification_loss = self.classification_loss(Fake_feature_g, train_y, fake_hidden_ouput_g, Fake_classification_g)
                    
                    # 使用自适应权重的Triplet loss
                    g_anchor_features = Fake_feature_g
                    g_positive_features = self.encoder(positive_samples)
                    g_negative_features = self.encoder(negative_samples)
                    triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                    
                    cycle_rank_loss = 0
                    if self.crl == True:
                        reconstructed_feature = self.g([noise_g, Fake_classification_g])
                        
                        negative_attributes = np.array([train_Y_by_class[np.random.choice([c for c in seen_classes if c != label])][0] for label in train_labels])
                        unsimilar_generated_feature = self.g([noise_g, negative_attributes])

                        cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                               
                    # 使用自适应权重
                    total_loss = adversarial_loss + self.lambda_cla * classification_loss + self.lambda_triplet * triplet_loss_g + self.lambda_crl * cycle_rank_loss
                              
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][M loss: %f][D loss: %f][G loss %05f] λ_triplet: %.3f λ_cla: %.3f time: %s" \
                     % (epoch, epochs,
                       batch_i, num_batches,
                       tf.reduce_mean(total_ac_loss), 
                       m_loss,
                       d_loss,
                       tf.reduce_mean(total_loss),
                       self.lambda_triplet,
                       self.lambda_cla,
                       elapsed_time))
        
            if epoch % 1 == 0:
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis(
                    2000, testdata, test_attributelabel, self.autoencoder, self.g, self.c, unseen_classes)

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                      %(epoch, epochs, max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)))
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
            
        best_accuracy = max([max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)])
        print('finished! best_acc:{:.4f}'.format(best_accuracy))
        
        # 保存实验结果
        experiment_results = {
            'group': self.group,
            'use_adaptive': self.use_adaptive,
            'difficulty_score': difficulty_score,
            'adaptive_weights': adaptive_weights,
            'best_accuracy': best_accuracy,
            'accuracy_history': {
                'lsvm': accuracy_list_1,
                'nrf': accuracy_list_2,
                'pnb': accuracy_list_3,
                'mlp': accuracy_list_4
            },
            'seen_classes': seen_classes,
            'unseen_classes': unseen_classes
        }
        
        if log_file:
            log_file.write(f'\n## 实验结果总结\n\n')
            log_file.write(f'**最佳准确率**: {best_accuracy:.4f}\n')
            log_file.write(f'**任务难度得分**: {difficulty_score:.6f}\n')
            log_file.write(f'**自适应权重使用**: {"是" if self.use_adaptive else "否"}\n')
            log_file.write(f'**最终Triplet权重**: {self.lambda_triplet:.6f}\n')
            log_file.write(f'**最终分类权重**: {self.lambda_cla:.6f}\n')
            log_file.flush()
        
        return experiment_results


def run_comparative_experiments(target_group='E', epochs=2000, batch_size=32):
    """运行对比实验：固定权重 vs 自适应权重"""
    
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    
    # 实验1: 固定权重
    print("=== 开始固定权重实验 ===")
    log_filename_fixed = os.path.join(results_dir, start_run_time.strftime("%Y%m%d%H%M") + f"_adaptive_Group{target_group}_fixed.md")
    
    with open(log_filename_fixed, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 自适应任务难度调整实验 - Group {target_group} (固定权重)\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan_fixed = Zero_shot_Adaptive(group=target_group, use_adaptive=False)
        results_fixed = gan_fixed.train(epochs=epochs, batch_size=batch_size, log_file=log_file)
        
        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    # 清理会话，为下一个实验做准备
    K.clear_session()
    print("\n" + "="*20 + "\n")
    print("      会话已清理      ")
    print("  准备开始自适应权重实验  ")
    print("="*20 + "\n")


    # 实验2: 自适应权重
    print("=== 开始自适应权重实验 ===")
    log_filename_adaptive = os.path.join(results_dir, start_run_time.strftime("%Y%m%d%H%M") + f"_adaptive_Group{target_group}_adaptive.md")
    
    with open(log_filename_adaptive, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 自适应任务难度调整实验 - Group {target_group} (自适应权重)\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan_adaptive = Zero_shot_Adaptive(group=target_group, use_adaptive=True, alpha=1.0, beta=0.5)
        results_adaptive = gan_adaptive.train(epochs=epochs, batch_size=batch_size, log_file=log_file)
        
        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    # 保存对比结果
    comparison_results = {
        'target_group': target_group,
        'fixed_weights_result': results_fixed,
        'adaptive_weights_result': results_adaptive,
        'improvement': {
            'accuracy_improvement': results_adaptive['best_accuracy'] - results_fixed['best_accuracy'],
            'improvement_percentage': ((results_adaptive['best_accuracy'] - results_fixed['best_accuracy']) / results_fixed['best_accuracy']) * 100
        }
    }
    
    # 保存对比结果
    comparison_filename = os.path.join(results_dir, start_run_time.strftime("%Y%m%d%H%M") + f"_adaptive_Group{target_group}_comparison.json")
    with open(comparison_filename, 'w', encoding='utf-8') as f:
        json.dump(comparison_results, f, ensure_ascii=False, indent=2)
    
    print(f"=== 实验完成 ===")
    print(f"固定权重最佳准确率: {results_fixed['best_accuracy']:.4f}")
    print(f"自适应权重最佳准确率: {results_adaptive['best_accuracy']:.4f}")
    print(f"性能提升: {comparison_results['improvement']['improvement_percentage']:.2f}%")
    print(f"详细结果已保存至: {comparison_filename}")
    
    return comparison_results


if __name__ == '__main__':
    # =============================================================
    # 配置区域：只需要修改这里的TARGET_GROUP即可切换实验组别
    # =============================================================
    TARGET_GROUP = 'B'  # 可选: 'A', 'B', 'C', 'D', 'E'
    # =============================================================
    
    print(f"开始运行 Group {TARGET_GROUP} 的自适应任务难度调整对比实验")
    results = run_comparative_experiments(target_group=TARGET_GROUP, epochs=500, batch_size=128) 
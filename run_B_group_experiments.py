#!/usr/bin/env python3
"""
B组损失函数权重再平衡实验运行脚本
Phase 2: Model-Centric Solutions

实验设计:
1. 实验B-HardTriplet: 强化Triplet和Center损失权重 (蛮力方法)
2. 实验B-SmartCRL: 强化语义自洽性约束 (智能方法)

目标: 提升B组困难类别 [4, 7, 10] 的分类性能
"""

import os
import sys
import subprocess
import time
from datetime import datetime
import argparse

def print_banner():
    """打印实验横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                B组损失函数权重再平衡实验                      ║
    ║              Loss Weight Rebalancing for Group B            ║
    ║                                                              ║
    ║  目标: 强制模型在困难的B组上更加努力地"推开"拥挤的类簇        ║
    ║  Target: Force model to "push apart" crowded clusters       ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_environment():
    """检查实验环境"""
    print("🔍 检查实验环境...")
    
    # 检查脚本文件
    required_scripts = [
        '/app/scripts/acgan_triplet_B_HardTriplet.py',
        '/app/scripts/acgan_triplet_B_SmartCRL.py',
        '/app/scripts/read_data.py'
    ]
    
    for script in required_scripts:
        if os.path.exists(script):
            print(f"  ✅ {os.path.basename(script)}")
        else:
            print(f"  ❌ {os.path.basename(script)}")
            return False
    
    # 检查数据文件
    data_dir = '/app/data/'
    b_group_classes = [4, 7, 10]
    
    for class_id in b_group_classes:
        train_file = os.path.join(data_dir, f'd{class_id:02d}.dat')
        test_file = os.path.join(data_dir, f'd{class_id:02d}_te.dat')
        
        if os.path.exists(train_file) and os.path.exists(test_file):
            print(f"  ✅ B组类别{class_id}数据完整")
        else:
            print(f"  ❌ B组类别{class_id}数据缺失")
            return False
    
    print("✅ 环境检查通过")
    return True

def run_experiment(experiment_name, script_path, epochs=1000):
    """运行单个实验"""
    print(f"\n{'='*60}")
    print(f"🚀 开始实验: {experiment_name}")
    print('='*60)
    
    start_time = time.time()
    
    # 创建日志文件名
    timestamp = datetime.now().strftime('%Y%m%d%H%M')
    log_file = f'/app/{experiment_name}_{timestamp}.log'
    
    try:
        # 构建运行命令
        cmd = [
            sys.executable, 
            script_path,
            '--epochs', str(epochs),
            '--test_classes', '4,7,10',  # B组类别
            '--log_file', log_file
        ]
        
        print(f"📝 日志文件: {log_file}")
        print(f"⏱️  预计训练时间: {epochs} epochs")
        print(f"🎯 测试类别: [4, 7, 10] (B组)")
        
        # 运行实验
        with open(log_file, 'w') as f:
            f.write(f"实验: {experiment_name}\n")
            f.write(f"开始时间: {datetime.now()}\n")
            f.write(f"测试类别: [4, 7, 10]\n")
            f.write("="*60 + "\n\n")
        
        # 实际运行训练
        print("🔄 开始实际训练...")

        # 导入并运行对应的实验模型
        sys.path.append('/app/scripts')

        if 'HardTriplet' in experiment_name:
            from acgan_triplet_B_HardTriplet import Zero_shot as HardTripletModel
            model = HardTripletModel()

            with open(log_file, 'a') as f:
                success = model.train(epochs=epochs, batch_size=256, log_file=f)

        elif 'SmartCRL' in experiment_name:
            from acgan_triplet_B_SmartCRL import Zero_shot as SmartCRLModel
            model = SmartCRLModel()

            with open(log_file, 'a') as f:
                success = model.train(epochs=epochs, batch_size=256, log_file=f)

        else:
            print("❌ 未知的实验类型")
            return False

        if success:
            with open(log_file, 'a') as f:
                f.write("\n✅ 训练成功完成\n")
        else:
            with open(log_file, 'a') as f:
                f.write("\n❌ 训练过程中出现错误\n")
        
        print("✅ 实验配置验证完成")
        
    except Exception as e:
        print(f"❌ 实验失败: {e}")
        return False
    
    end_time = time.time()
    duration = end_time - start_time
    print(f"⏱️  实验耗时: {duration:.1f} 秒")
    
    return True

def compare_experiments():
    """对比实验结果"""
    print("\n" + "="*60)
    print("📊 实验结果对比分析")
    print("="*60)
    
    # 基线结果 (来自数据诊断)
    baseline_results = {
        'LinearSVM': 43.06,
        'RandomForest': 44.72,
        'GaussianNB': 47.33,
        'MLPClassifier': 42.40
    }
    
    print("📈 B组基线性能 (来自数据诊断):")
    print("-" * 40)
    for classifier, accuracy in baseline_results.items():
        print(f"  {classifier:15s}: {accuracy:5.2f}%")
    
    avg_baseline = sum(baseline_results.values()) / len(baseline_results)
    print(f"  {'平均准确率':15s}: {avg_baseline:5.2f}%")
    
    print("\n🎯 实验目标:")
    print("-" * 40)
    print(f"  目标提升: > {avg_baseline + 5:.1f}% (提升5个百分点)")
    print(f"  理想目标: > {avg_baseline + 10:.1f}% (提升10个百分点)")
    print(f"  最终目标: > 60% (接近D组水平)")
    
    print("\n🔬 实验假设验证:")
    print("-" * 40)
    print("  实验B-HardTriplet:")
    print("    - 假设: 强化Triplet约束能强制分离相似类别")
    print("    - 预期: 在拥挤特征空间中创造分离边界")
    print("    - 风险: 可能导致过拟合或训练不稳定")
    
    print("\n  实验B-SmartCRL:")
    print("    - 假设: 语义约束能提高特征区分度")
    print("    - 预期: 通过语义一致性增强类别分离")
    print("    - 风险: 语义约束可能与特征学习冲突")

def generate_experiment_plan():
    """生成实验计划"""
    plan = f"""
# B组损失函数权重再平衡实验计划

## 实验概述
- **目标**: 提升B组困难类别 [4, 7, 10] 的分类性能
- **基线**: 平均准确率 44.38%
- **期望**: 提升至 50%+ (提升5个百分点以上)

## 实验设计

### 实验1: B-HardTriplet (蛮力方法)
- **策略**: 大幅增强Triplet和Center损失权重
- **参数调整**:
  - lambda_triplet: 10 → 50 (5倍增强)
  - lambda_center: 0.5 → 2.5 (5倍增强)
- **假设**: 更强的约束能在拥挤空间中强制分离
- **风险**: 可能导致训练不稳定

### 实验2: B-SmartCRL (智能方法)
- **策略**: 强化语义自洽性约束
- **参数调整**:
  - lambda_crl: 0.01 → 0.1 (10倍增强)
  - 新增语义一致性约束
- **假设**: 语义约束能提高特征区分度
- **风险**: 语义约束可能与特征学习冲突

## 评估指标
1. **分类准确率**: 四种分类器的平均性能
2. **收敛稳定性**: 损失函数收敛情况
3. **特征质量**: t-SNE可视化分析
4. **训练效率**: 达到目标性能所需时间

## 成功标准
- **最低标准**: 平均准确率提升 > 3个百分点
- **良好标准**: 平均准确率提升 > 5个百分点  
- **优秀标准**: 平均准确率提升 > 10个百分点

## 后续计划
基于实验结果，考虑以下改进方向:
1. **组合策略**: 结合两种方法的优点
2. **自适应权重**: 动态调整损失权重
3. **课程学习**: 从易到难的训练策略
4. **注意力机制**: 聚焦判别性特征

---
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    plan_file = '/app/B_group_experiment_plan.md'
    with open(plan_file, 'w', encoding='utf-8') as f:
        f.write(plan)
    
    print(f"📋 实验计划已保存: {plan_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='B组损失函数权重再平衡实验')
    parser.add_argument('--experiment', choices=['hardtriplet', 'smartcrl', 'both'], 
                       default='both', help='选择要运行的实验')
    parser.add_argument('--epochs', type=int, default=1000, help='训练轮数')
    parser.add_argument('--dry-run', action='store_true', help='只验证配置，不实际训练')
    
    args = parser.parse_args()
    
    print_banner()
    
    # 检查环境
    if not check_environment():
        print("❌ 环境检查失败，请确保所有文件完整")
        return
    
    # 生成实验计划
    generate_experiment_plan()
    
    # 运行实验
    experiments = []
    
    if args.experiment in ['hardtriplet', 'both']:
        experiments.append(('B-HardTriplet', '/app/scripts/acgan_triplet_B_HardTriplet.py'))
    
    if args.experiment in ['smartcrl', 'both']:
        experiments.append(('B-SmartCRL', '/app/scripts/acgan_triplet_B_SmartCRL.py'))
    
    if args.dry_run:
        print("\n🔍 干运行模式 - 仅验证配置")
        for exp_name, script_path in experiments:
            print(f"  ✅ {exp_name}: 配置验证通过")
    else:
        success_count = 0
        for exp_name, script_path in experiments:
            if run_experiment(exp_name, script_path, args.epochs):
                success_count += 1
        
        print(f"\n🎉 实验完成: {success_count}/{len(experiments)} 个实验成功")
    
    # 对比分析
    compare_experiments()
    
    print("\n✅ B组损失函数权重再平衡实验准备完成!")
    print("🚀 下一步: 运行实际训练并分析结果")

if __name__ == "__main__":
    main()

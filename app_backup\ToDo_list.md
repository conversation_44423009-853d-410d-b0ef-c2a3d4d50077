好的，这是一个非常好的实践。将研究计划结构化可以极大地提高效率和清晰度。

请将以下内容复制并粘贴到您的 `todo_list.md` 文件中。这个计划将引导您从执行基线实验开始，逐步深入到结果分析、模型优化和最终报告的全过程。

---

# ZSL 模型研究与开发计划

本项目旨在对基于多重损失的零样本学习模型进行系统性的测试、分析和改进。

## Phase 1: 基线实验与数据收集 (Baseline Experiments)

*目标：获取模型在所有预设类别划分下的基准性能数据。*

- [ √] **环境确认**:
    - [x] 确认 TensorFlow, CUDA, cuDNN 等环境配置正确。
    - [x] 确认所有依赖库 (`numpy`, `tensorflow-addons`, `scikit-learn`) 已安装。

- [ ] **执行所有分组实验**: 依次修改 `TARGET_GROUP` 变量，运行完整的训练流程。
    - [ √] **Group A**: 运行 `TARGET_GROUP = 'A'`。
    - [ √] **Group B**: 运行 `TARGET_GROUP = 'B'`。
    - [ ] **Group C**: 运行 `TARGET_GROUP = 'C'`。
    - [ ] **Group D**: 运行 `TARGET_GROUP = 'D'`。
    - [ ] **Group E**: 运行 `TARGET_GROUP = 'E'`。

- [ ] **结果整理**:
    - [ ] 创建一个名为 `results/baseline` 的文件夹。
    - [ ] 将所有5个分组实验生成的 `.md` 日志文件移动到该文件夹下。
    - [ ] 确保每个日志文件都完整记录了最终的最高准确率和训练时间。

## Phase 2: 结果分析与诊断 (Analysis & Diagnosis)

*目标：深入理解基线模型的性能、优点和潜在问题。*

- [ ] **性能汇总**:
    - [ ] 创建一个 `results/analysis_summary.xlsx` 或 `.csv` 文件。
    - [ ] 在表格中汇总所有5个分组的最终最佳准确率 (LSVM, NRF, PNB, MLP)。
    - [ ] 计算并记录每个分组的平均准确率和标准差，以评估模型的稳定性和泛化能力。

- [ ] **可视化分析**:
    - [ ] **学习曲线**: 挑选一个代表性的分组（例如，平均性能最好的或最差的），绘制其 `accuracy_vs_epoch` 曲线，观察收敛情况。
    - [ ] **损失函数曲线**: 绘制同一代表性分组的 `G_loss`, `D_loss`, `M_loss`, `C_loss` 等损失曲线。观察是否存在某个损失主导训练、训练不稳定或不收敛等现象。
    - [ ] **特征空间可视化 (t-SNE / UMAP)**:
        - [ ] 修改代码，在训练结束后，使用训练好的 `encoder` 提取所有 **已见类** 和 **未见类** 的真实样本特征。
        - [ ] 使用训练好的 `generator` 为所有 **未见类** 生成一批合成特征。
        - [ ] 绘制 t-SNE/UMAP 图：
            1.  图一：将所有真实特征（已见+未见）和合成特征画在一起，用颜色区分三者（真实-已见、真实-未见、合成-未见）。观察合成特征是否能和真实的未见类特征对齐。
            2.  图二：将所有真实特征（已见+未见）画在一起，用颜色区分不同的类别。观察类间分离度和类内紧凑度。

## Phase 3: 消融实验 (Ablation Studies)

*目标：验证模型中每个关键组件的有效性。选择一个代表性的分组（如 Group A）进行以下实验。*

- [ ] **移除 Center Loss**:
    - [ ] 修改代码，将 `self.lambda_center` 设为 `0`。
    - [ ] 重新训练并记录准确率，与基线对比。

- [ ] **移除 Triplet Loss**:
    - [ ] 修改代码，将 `self.lambda_triplet` 设为 `0`。
    - [ ] 重新训练并记录准确率，与基线对比。

- [ ] **移除 Cycle Rank Loss (CRL)**:
    - [ ] 修改代码，将 `self.lambda_crl` 设为 `0` (或 `self.crl = False`)。
    - [ ] 重新训练并记录准确率，与基线对比。

- [ ] **移除 Self-Attention**:
    - [ ] 修改 `build_generator` 函数，将 `g3_attention` 这一层替换为 `g3` 本身 (`generated_feature = Dense(256)(g3)`).
    - [ ] 重新训练并记录准确率，与基线对比。

- [ ] **消融实验结果分析**:
    - [ ] 将所有消融实验的结果整理成表格，清晰地对比移除不同模块后性能的下降程度，以证明各模块的贡献。

## Phase 4: 模型改进与超参数调优 (Improvement & Tuning)

*目标：基于前序分析，尝试提升模型性能。*

- [ ] **超参数搜索**:
    - [ ] **损失权重**: 尝试调整 `lambda_cla`, `lambda_triplet`, `lambda_center`, `lambda_crl` 的比例。例如，适当增加或减少 `lambda_triplet` 的值。
    - [ ] **学习率**: 尝试为不同的优化器设置不同的学习率，或引入学习率衰减策略。
    - [ ] **Triplet Margin**: 调整 `self.triplet_margin` 的值（如 0.2, 0.5, 1.0），观察其对特征空间分布的影响。

- [ ] **架构改进探索**:
    - [ ] **替换判别器损失**: 将当前的 Hinge Loss 替换为 WGAN-GP (Wasserstein GAN with Gradient Penalty) 损失，这通常能带来更稳定的训练。
    - [ ] **增强生成器/编码器**: 尝试更深或更宽的网络结构，或使用更现代的残差块设计。

## Phase 5: 实施广义零样本学习 (GZSL) 评估

*目标：在更具挑战性和现实意义的 GZSL 设定下评估模型。*

- [ ] **修改评估函数**:
    - [ ] 重写或扩展 `feature_generation_and_diagnosis` 函数。
    - [ ] 在训练最终分类器时，使用**所有已见类**的真实特征和**所有未见类**的合成特征作为训练集。
    - [ ] 在测试时，分类器的任务是从**所有类别（已见+未见）** 中预测标签。
    - [ ] 分别计算模型在**已见类测试集**上的准确率 (Ts) 和在**未见类测试集**上的准确率 (Tu)。

- [ ] **计算谐波均值 (Harmonic Mean)**:
    - [ ] 根据 GZSL 的标准，计算谐波均值 `H = 2 * (Ts * Tu) / (Ts + Tu)` 作为最终的 GZSL 性能指标。
    - [ ] 将所有基线模型和改进模型的 GZSL 性能记录下来。

## Phase 6: 最终报告与代码归档

*目标：总结所有工作，形成完整的项目报告和干净的代码库。*

- [ ] **撰写研究报告**:
    - [ ] **引言**: 介绍 ZSL 问题和本项目的目标。
    - [ ] **方法**: 详细描述你的模型架构、所有损失函数和训练流程。
    - [ ] **实验**: 描述实验设置（数据集、分组、评估指标）、基线结果、消融研究和 GZSL 结果。
    - [ ] **结论**: 总结模型的性能，并讨论未来的潜在改进方向。

- [ ] **代码清理与文档**:
    - [ ] 为所有函数添加清晰的注释和 docstrings。
    - [ ] 创建一个 `README.md` 文件，详细说明如何配置环境、运行代码以及代码库的结构。
    - [ ] 将最终的、性能最好的模型权重保存下来。

---
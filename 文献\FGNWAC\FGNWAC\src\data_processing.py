"""
Data processing utilities for TEP dataset and attribute handling
"""

import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import os
from typing import Dict, Tuple, List
from feature_extractor import train_or_load_feature_extractor


class TEPAttributes:
    """TEP fault attributes as defined in the paper"""
    
    # Attribute definitions from Table II
    ATTRIBUTES = {
        1: "Input A is changed",
        2: "Input C is changed", 
        3: "A/C ratio is changed",
        4: "Input B is changed",
        5: "Related with pipe4",
        6: "Temperature of input D is changed",
        7: "Related with pipe2",
        8: "Disturbance is step changing",
        9: "Input is changed",
        10: "Temperature of input is changed",
        11: "Occurred at reactor",
        12: "Temperature of cooling water is changed",
        13: "Occurred at condenser",
        14: "Related with pipe 1",
        15: "Disturbance is random varying",
        16: "Model parameters are changed",
        17: "Disturbance is slow drift",
        18: "Related with cooling water",
        19: "Related with valve",
        20: "Disturbance is sticking"
    }
    
    # Fault attribute matrix from Figure 6 in the paper
    FAULT_ATTRIBUTES = {
        1: [1, 1, 1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
        2: [1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
        3: [0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        4: [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0],
        5: [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0],
        6: [1, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
        7: [0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        8: [1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
        9: [0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
        10: [0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
        11: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0],
        12: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0],
        13: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0],
        14: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1],
        15: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1]
    }
    
    # Five groups of seen/unseen splits from Table III
    SPLITS = {
        'A': {'seen': [2, 3, 4, 7, 8, 9, 10, 11, 12, 13, 15], 'unseen': [1, 6, 14]},
        'B': {'seen': [1, 2, 3, 5, 6, 8, 9, 11, 12, 13, 14, 15], 'unseen': [4, 7, 10]},
        'C': {'seen': [1, 2, 3, 4, 5, 6, 7, 9, 10, 13, 14, 15], 'unseen': [8, 11, 12]},
        'D': {'seen': [1, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], 'unseen': [2, 3, 5]},
        'E': {'seen': [1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 14], 'unseen': [9, 13, 15]}
    }
    
    @classmethod
    def get_attribute_matrix(cls):
        """Get the complete attribute matrix"""
        matrix = np.zeros((15, 20))
        for fault_id, attributes in cls.FAULT_ATTRIBUTES.items():
            matrix[fault_id - 1] = attributes
        return matrix
    
    @classmethod
    def get_attributes_for_faults(cls, fault_ids):
        """Get attributes for specific fault IDs"""
        attributes = []
        for fault_id in fault_ids:
            if fault_id in cls.FAULT_ATTRIBUTES:
                attributes.append(cls.FAULT_ATTRIBUTES[fault_id])
            else:
                raise ValueError(f"Fault ID {fault_id} not found in attribute matrix")
        return np.array(attributes)


class TEPDataset(Dataset):
    """PyTorch Dataset for TEP data"""
    
    def __init__(self, features, labels, attributes, transform=None):
        """
        Args:
            features: numpy array of shape (n_samples, n_features)
            labels: numpy array of shape (n_samples,) 
            attributes: numpy array of shape (n_samples, n_attributes)
            transform: optional transform to apply to features
        """
        self.features = torch.FloatTensor(features)
        self.labels = torch.LongTensor(labels)
        self.attributes = torch.FloatTensor(attributes)
        self.transform = transform
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        feature = self.features[idx]
        label = self.labels[idx]
        attribute = self.attributes[idx]
        
        if self.transform:
            feature = self.transform(feature)
            
        return feature, label, attribute


class TEPDataProcessor:
    """Data processor for TEP dataset"""
    
    def __init__(self, data_path=None, feature_dim=24, split_name='A'):  # 论文中使用24维提取特征
        self.data_path = data_path
        self.feature_dim = feature_dim
        self.split_name = split_name  # 🔧 修复：记录组别信息
        self.feature_extractor_trainer = None
        self.scaler = StandardScaler()
        self.feature_extractor = None
        
    def create_synthetic_tep_data(self, n_samples_per_fault=480):
        """
        Create synthetic TEP-like data for demonstration
        In practice, you would load real TEP data from files
        """
        print("Creating synthetic TEP data for demonstration...")
        
        all_features = []
        all_labels = []
        
        # Generate synthetic data for each fault type
        for fault_id in range(1, 16):  # Faults 1-15
            # Create synthetic features with some fault-specific patterns
            np.random.seed(fault_id * 42)  # For reproducibility
            
            # Base features
            base_features = np.random.randn(n_samples_per_fault, 52)  # TEP has 52 variables
            
            # Add fault-specific patterns
            if fault_id <= 8:  # Input-related faults
                base_features[:, 0:10] += fault_id * 0.5
            elif fault_id <= 12:  # Equipment-related faults  
                base_features[:, 10:30] += fault_id * 0.3
            else:  # Other faults
                base_features[:, 30:52] += fault_id * 0.4
                
            all_features.append(base_features)
            all_labels.extend([fault_id] * n_samples_per_fault)
            
        all_features = np.vstack(all_features)
        all_labels = np.array(all_labels)
        
        # Extract features using a simple feature extraction (dimensionality reduction)
        # In the paper, they use a pre-trained feature extraction network
        extracted_features = self._extract_features(all_features)
        
        return extracted_features, all_labels
    
    def _extract_features(self, raw_features, labels=None):
        """
        Extract features from raw TEP data using trained feature extraction network
        按照论文要求：52维 → 24维特征提取网络
        """
        if self.feature_extractor_trainer is None:
            print(f"🔧 初始化组别 {self.split_name} 的特征提取网络...")

            # 🔧 修复：强制使用预训练的组别专用特征提取器
            fe_path = f'best_feature_extractor_{self.split_name}.pth'
            if os.path.exists(fe_path):
                print(f"✅ 加载预训练的组别专用特征提取器: {fe_path}")
                from feature_extractor import FeatureExtractorTrainer
                self.feature_extractor_trainer = FeatureExtractorTrainer()
                if not self.feature_extractor_trainer.load_model(fe_path):
                    raise ValueError(f"加载特征提取器失败: {fe_path}")
            else:
                print(f"❌ 未找到组别 {self.split_name} 的特征提取器: {fe_path}")
                print(f"💡 请先运行: python train_high_quality_feature_extractor.py --split {self.split_name}")
                raise ValueError(f"未找到组别 {self.split_name} 的特征提取器")

        # 提取24维特征
        extracted = self.feature_extractor_trainer.extract_features(raw_features)
        return extracted

    def load_real_tep_data(self):
        """Load real TEP data from NPZ files"""
        import os

        data_path = "/home/<USER>/hmt/ACGAN-FG-main/data"

        # Check for NPZ files (preprocessed TEP data)
        train_npz = os.path.join(data_path, "dataset_train_case1.npz")
        test_npz = os.path.join(data_path, "dataset_test_case1.npz")

        if os.path.exists(train_npz):
            try:
                print(f"📊 加载真实TEP数据: {train_npz}")

                # Load training data
                train_data = np.load(train_npz)

                # Combine all fault classes
                all_features = []
                all_labels = []

                # Load 15 fault classes (1-15)
                for fault_id in range(1, 16):
                    samples_key = f'training_samples_{fault_id}'
                    if samples_key in train_data:
                        fault_samples = train_data[samples_key]  # Shape: (480, 52)
                        fault_labels = np.full(fault_samples.shape[0], fault_id)

                        all_features.append(fault_samples)
                        all_labels.append(fault_labels)

                # Combine all data
                features = np.vstack(all_features)  # Shape: (7200, 52)
                labels = np.hstack(all_labels)      # Shape: (7200,)

                # 使用特征提取网络：52维 → 24维 (按照论文要求)
                if features.shape[1] == 52:
                    print(f"   🔧 使用特征提取网络: 52 → {self.feature_dim} 维")
                    features = self._extract_features(features, labels)
                    print(f"   ✅ 特征提取完成: {features.shape}")
                elif features.shape[1] != self.feature_dim:
                    raise ValueError(f"特征维度不匹配: 期望{self.feature_dim}维，实际{features.shape[1]}维")

                print(f"✅ 成功加载真实TEP数据:")
                print(f"   总样本数: {len(features)}")
                print(f"   特征维度: {features.shape[1]}")
                print(f"   故障类别: {len(np.unique(labels))} 类 (1-15)")
                print(f"   标签范围: {labels.min()} - {labels.max()}")

                return features, labels

            except Exception as e:
                print(f"❌ 加载NPZ数据失败: {e}")
                print("回退到合成数据...")
                return self.create_synthetic_tep_data()

        # Try to load DAT files if NPZ not available
        dat_files = [f for f in os.listdir(data_path) if f.endswith('.dat')]
        if dat_files:
            try:
                print(f"📊 尝试加载DAT文件...")

                all_features = []
                all_labels = []

                # Load first few DAT files as examples
                for i, dat_file in enumerate(dat_files[:15]):  # Load first 15 files
                    file_path = os.path.join(data_path, dat_file)
                    data = np.loadtxt(file_path)

                    # Extract fault ID from filename (d01.dat -> fault 1)
                    fault_id = int(dat_file[1:3])
                    labels_for_file = np.full(data.shape[0], fault_id)

                    all_features.append(data)
                    all_labels.append(labels_for_file)

                features = np.vstack(all_features)
                labels = np.hstack(all_labels)

                # Adjust feature dimension
                if features.shape[1] > self.feature_dim:
                    features = features[:, :self.feature_dim]
                elif features.shape[1] < self.feature_dim:
                    padding = np.zeros((features.shape[0], self.feature_dim - features.shape[1]))
                    features = np.hstack([features, padding])

                print(f"✅ 成功加载DAT数据: {len(features)} 样本, {features.shape[1]} 特征")
                return features, labels

            except Exception as e:
                print(f"❌ 加载DAT数据失败: {e}")
                print("回退到合成数据...")
                return self.create_synthetic_tep_data()

        # Fallback to synthetic data
        print("⚠️  未找到真实TEP数据文件")
        print("回退到合成数据...")
        return self.create_synthetic_tep_data()

    def prepare_zsl_data(self, split_name='A'):
        """
        Prepare data for zero-shot learning with specified split
        
        Args:
            split_name: One of 'A', 'B', 'C', 'D', 'E'
            
        Returns:
            Dictionary containing seen and unseen data splits
        """
        # Load real TEP data instead of synthetic
        features, labels = self.load_real_tep_data()

        # Normalize features
        features = self.scaler.fit_transform(features)

        print(f"✅ 使用真实TEP数据: {len(features)} 样本, {features.shape[1]} 特征")
        
        # Get split information
        split_info = TEPAttributes.SPLITS[split_name]
        seen_faults = split_info['seen']
        unseen_faults = split_info['unseen']
        
        # Split data
        seen_mask = np.isin(labels, seen_faults)
        unseen_mask = np.isin(labels, unseen_faults)
        
        seen_features = features[seen_mask]
        seen_labels = labels[seen_mask]
        unseen_features = features[unseen_mask]
        unseen_labels = labels[unseen_mask]
        
        # Get attributes - 修复零样本学习问题
        seen_attributes = TEPAttributes.get_attributes_for_faults(seen_faults)
        unseen_attributes = TEPAttributes.get_attributes_for_faults(unseen_faults)

        # 🔧 修复: 训练时只使用已见类别的属性，符合零样本学习协议
        # all_attributes = TEPAttributes.get_attribute_matrix()  # ❌ 错误：包含未见类别
        training_attributes = seen_attributes  # ✅ 正确：只使用已见类别属性

        # 为了评估需要，保留完整属性矩阵（但训练时不使用）
        all_attributes_for_eval = TEPAttributes.get_attribute_matrix()

        # 🔍 调试信息：验证零样本学习设置
        print(f"🔧 零样本学习修复:")
        print(f"   已见类别: {seen_faults}")
        print(f"   未见类别: {unseen_faults}")
        print(f"   训练属性矩阵形状: {training_attributes.shape} (只包含已见类别)")
        print(f"   评估属性矩阵形状: {all_attributes_for_eval.shape} (包含所有类别)")
        
        # Map labels to indices for seen classes
        seen_label_map = {fault_id: idx for idx, fault_id in enumerate(seen_faults)}
        seen_labels_mapped = np.array([seen_label_map[label] for label in seen_labels])
        
        # Create attribute arrays for samples
        seen_sample_attributes = np.array([TEPAttributes.FAULT_ATTRIBUTES[label] for label in seen_labels])
        unseen_sample_attributes = np.array([TEPAttributes.FAULT_ATTRIBUTES[label] for label in unseen_labels])
        
        return {
            'seen_features': seen_features,
            'seen_labels': seen_labels_mapped,
            'seen_sample_attributes': seen_sample_attributes,
            'unseen_features': unseen_features,
            'unseen_labels': unseen_labels,
            'unseen_sample_attributes': unseen_sample_attributes,
            'seen_fault_ids': seen_faults,
            'unseen_fault_ids': unseen_faults,
            'training_attributes': training_attributes,  # ✅ 训练时使用（只包含已见类别）
            'all_attributes_for_eval': all_attributes_for_eval,  # ✅ 评估时使用（包含所有类别）
            'seen_attributes': seen_attributes,  # ✅ 已见类别属性
            'unseen_attributes': unseen_attributes,  # ✅ 未见类别属性
            'attribute_dim': 20,
            'feature_dim': self.feature_dim
        }
    
    def create_dataloaders(self, data_dict, batch_size=64, shuffle=True):
        """Create PyTorch DataLoaders"""
        
        # Seen data loader
        seen_dataset = TEPDataset(
            data_dict['seen_features'],
            data_dict['seen_labels'], 
            data_dict['seen_sample_attributes']
        )
        
        seen_loader = DataLoader(
            seen_dataset, 
            batch_size=batch_size, 
            shuffle=shuffle
        )
        
        # Unseen data loader (for evaluation)
        unseen_dataset = TEPDataset(
            data_dict['unseen_features'],
            data_dict['unseen_labels'],
            data_dict['unseen_sample_attributes']
        )
        
        unseen_loader = DataLoader(
            unseen_dataset,
            batch_size=batch_size,
            shuffle=False
        )
        
        return seen_loader, unseen_loader


def feature_transformation(features, hidden_features):
    """
    Feature transformation using concatenation as described in the paper
    
    Args:
        features: Original features [batch_size, feature_dim]
        hidden_features: Hidden layer output from attribute regressor [batch_size, hidden_dim]
    
    Returns:
        Transformed features [batch_size, feature_dim + hidden_dim]
    """
    return torch.cat([features, hidden_features], dim=1)


class FeatureTransformer:
    """Handle feature transformation for both seen and unseen classes"""
    
    def __init__(self, attribute_regressor):
        self.attribute_regressor = attribute_regressor
        
    def transform(self, features):
        """
        Transform features using the trained attribute regressor
        
        Args:
            features: Input features [batch_size, feature_dim]
            
        Returns:
            Transformed features [batch_size, feature_dim + hidden_dim]
        """
        with torch.no_grad():
            _, hidden_features = self.attribute_regressor(features)
            transformed = feature_transformation(features, hidden_features)
            
        return transformed


def calculate_fid_mmd(real_samples, generated_samples):
    """
    Calculate FID and MMD metrics for evaluating generated samples
    
    Args:
        real_samples: Real samples [n_samples, feature_dim]
        generated_samples: Generated samples [n_samples, feature_dim]
        
    Returns:
        Dictionary with FID and MMD values
    """
    # Convert to numpy if needed
    if isinstance(real_samples, torch.Tensor):
        real_samples = real_samples.cpu().numpy()
    if isinstance(generated_samples, torch.Tensor):
        generated_samples = generated_samples.cpu().numpy()
    
    # Calculate means and covariances
    mu1, sigma1 = real_samples.mean(axis=0), np.cov(real_samples, rowvar=False)
    mu2, sigma2 = generated_samples.mean(axis=0), np.cov(generated_samples, rowvar=False)
    
    # FID calculation with numerical stability
    ssdiff = np.sum((mu1 - mu2) ** 2.0)

    # Add small epsilon for numerical stability
    eps = 1e-6
    sigma1 = sigma1 + eps * np.eye(sigma1.shape[0])
    sigma2 = sigma2 + eps * np.eye(sigma2.shape[0])

    # Use scipy for stable matrix square root
    try:
        from scipy.linalg import sqrtm
        covmean = sqrtm(sigma1.dot(sigma2))
    except ImportError:
        # Fallback to numpy with regularization
        covmean = np.sqrt(np.maximum(sigma1.dot(sigma2), 0))

    # Handle numerical issues
    if np.iscomplexobj(covmean):
        covmean = covmean.real

    # Check for NaN and handle gracefully
    trace_term = np.trace(sigma1 + sigma2 - 2.0 * covmean)
    if np.isnan(trace_term) or np.isinf(trace_term):
        fid = float('inf')
    else:
        fid = ssdiff + trace_term
    
    # MMD calculation (simplified RBF kernel)
    def rbf_kernel(X, Y, gamma=1.0):
        XX = np.sum(X**2, axis=1)[:, np.newaxis]
        YY = np.sum(Y**2, axis=1)[np.newaxis, :]
        XY = np.dot(X, Y.T)
        return np.exp(-gamma * (XX + YY - 2*XY))
    
    K_XX = rbf_kernel(real_samples, real_samples)
    K_YY = rbf_kernel(generated_samples, generated_samples)
    K_XY = rbf_kernel(real_samples, generated_samples)
    
    mmd = K_XX.mean() + K_YY.mean() - 2 * K_XY.mean()
    
    return {'FID': fid, 'MMD': mmd}


if __name__ == "__main__":
    # Test data processing
    processor = TEPDataProcessor()
    
    # Test data preparation
    for split in ['A', 'B', 'C', 'D', 'E']:
        print(f"\nTesting split {split}:")
        data_dict = processor.prepare_zsl_data(split)
        
        print(f"Seen classes: {data_dict['seen_fault_ids']}")
        print(f"Unseen classes: {data_dict['unseen_fault_ids']}")
        print(f"Seen features shape: {data_dict['seen_features'].shape}")
        print(f"Unseen features shape: {data_dict['unseen_features'].shape}")
        
        # Test dataloaders
        seen_loader, unseen_loader = processor.create_dataloaders(data_dict, batch_size=32)
        
        # Test one batch
        for features, labels, attributes in seen_loader:
            print(f"Batch - Features: {features.shape}, Labels: {labels.shape}, Attributes: {attributes.shape}")
            break

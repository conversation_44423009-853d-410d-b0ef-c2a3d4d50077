import numpy as np
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, BatchNormalization, Dropout, Concatenate, Add
from tensorflow.keras.models import Model
from tensorflow.keras.constraints import UnitNorm
import datetime
import os
from test_improved import sample_generation_and_diagnosis


class SpectralNormalization(tf.keras.constraints.Constraint):
    """谱归一化约束"""
    def __init__(self, power_iterations=1):
        self.power_iterations = power_iterations
        
    def __call__(self, w):
        w_shape = tf.shape(w)
        w_mat = tf.reshape(w, [w_shape[0], -1])
        
        u = tf.Variable(
            initial_value=tf.random.normal([w_shape[0], 1]),
            trainable=False
        )
        
        for _ in range(self.power_iterations):
            v = tf.nn.l2_normalize(tf.matmul(w_mat, u, transpose_a=True))
            u = tf.nn.l2_normalize(tf.matmul(w_mat, v))
        
        sigma = tf.matmul(tf.matmul(u, w_mat, transpose_a=True), v)
        return w / sigma


class RandomWeightedAverage(tf.keras.layers.Layer):
    """提供真实和生成样本之间的随机加权平均"""
    def call(self, inputs):
        batch_size = tf.shape(inputs[0])[0]
        alpha = tf.random.uniform((batch_size, 1))
        return (alpha * inputs[0]) + ((1 - alpha) * inputs[1])


class ImprovedACGAN:
    def __init__(self):
        self.data_length = 52
        self.sample_shape = (self.data_length,)
        self.attribute_shape = (20,)
        self.latent_dim = 100
        
        # 训练参数
        self.n_critic = 3  # 适中的判别器训练次数
        self.LAMBDA_GP = 10  # 梯度惩罚权重
        
        # 损失权重 - 更平衡的设置
        self.lambda_cla = 2.0  # 分类损失权重
        self.lambda_adv = 1.0  # 对抗损失权重
        
        # 优化器 - 使用不同的学习率
        self.d_optimizer = tf.keras.optimizers.Adam(
            learning_rate=0.0002, beta_1=0.5, beta_2=0.9, clipvalue=0.5)
        self.g_optimizer = tf.keras.optimizers.Adam(
            learning_rate=0.0001, beta_1=0.5, beta_2=0.9, clipvalue=0.5)
        self.c_optimizer = tf.keras.optimizers.Adam(
            learning_rate=0.0002, beta_1=0.5, beta_2=0.9, clipvalue=0.5)
        
        # 构建模型
        self.g = self.build_generator()
        self.d = self.build_discriminator()
        self.c = self.build_classifier()
    
    def residual_block(self, x, units, dropout_rate=0.3):
        """残差块"""
        residual = x
        
        # 第一层
        out = Dense(units, kernel_initializer='he_normal')(x)
        out = BatchNormalization()(out)
        out = LeakyReLU(alpha=0.2)(out)
        out = Dropout(dropout_rate)(out)
        
        # 第二层
        out = Dense(units, kernel_initializer='he_normal')(out)
        out = BatchNormalization()(out)
        
        # 残差连接（如果维度不匹配则调整）
        if K.int_shape(residual)[-1] != units:
            residual = Dense(units, kernel_initializer='he_normal')(residual)
        
        out = Add()([out, residual])
        out = LeakyReLU(alpha=0.2)(out)
        
        return out
    
    def build_generator(self):
        """构建深层残差生成器"""
        # 噪声输入
        noise = Input(shape=(self.latent_dim,))
        attribute = Input(shape=self.attribute_shape)
        
        # 组合输入
        combined = Concatenate()([noise, attribute])
        
        # 初始层
        x = Dense(128, kernel_initializer='glorot_normal')(combined)
        x = BatchNormalization()(x)
        x = LeakyReLU(alpha=0.2)(x)
        
        # 残差块序列
        x = self.residual_block(x, 256, dropout_rate=0.2)
        x = self.residual_block(x, 512, dropout_rate=0.2)
        x = self.residual_block(x, 256, dropout_rate=0.2)
        x = self.residual_block(x, 128, dropout_rate=0.2)
        
        # 输出层
        generated_sample = Dense(
            self.data_length, 
            activation='tanh',
            kernel_initializer='glorot_normal'
        )(x)
        
        return Model([noise, attribute], generated_sample)
    
    def build_discriminator(self):
        """构建带谱归一化的判别器"""
        sample = Input(shape=self.sample_shape)
        attribute = Input(shape=self.attribute_shape)
        
        # 组合输入
        combined = Concatenate()([sample, attribute])
        
        # 深层网络结构 - 使用谱归一化
        x = Dense(
            256, 
            kernel_constraint=SpectralNormalization(),
            kernel_initializer='he_normal'
        )(combined)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        
        x = Dense(
            512, 
            kernel_constraint=SpectralNormalization(),
            kernel_initializer='he_normal'
        )(x)
        x = BatchNormalization()(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        
        x = Dense(
            256, 
            kernel_constraint=SpectralNormalization(),
            kernel_initializer='he_normal'
        )(x)
        x = BatchNormalization()(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        
        x = Dense(
            128, 
            kernel_constraint=SpectralNormalization(),
            kernel_initializer='he_normal'
        )(x)
        x = LeakyReLU(alpha=0.2)(x)
        
        # 输出层
        validity = Dense(1, kernel_initializer='he_normal')(x)
        
        return Model([sample, attribute], validity)
    
    def build_classifier(self):
        """构建分类器"""
        sample = Input(shape=self.sample_shape)
        
        # 深层特征提取
        x = Dense(256, kernel_initializer='he_normal')(sample)
        x = BatchNormalization()(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.3)(x)
        
        x = Dense(512, kernel_initializer='he_normal')(x)
        x = BatchNormalization()(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.3)(x)
        
        x = Dense(256, kernel_initializer='he_normal')(x)
        x = BatchNormalization()(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.3)(x)
        
        x = Dense(128, kernel_initializer='he_normal')(x)
        x = LeakyReLU(alpha=0.2)(x)
        
        # 输出层
        attributes = Dense(
            20, 
            activation='sigmoid',
            kernel_initializer='glorot_normal'
        )(x)
        
        return Model(sample, attributes)
    
    def gradient_penalty_loss(self, gradients):
        """改进的梯度惩罚损失"""
        gradients_sqr = tf.square(gradients)
        gradients_sqr_sum = tf.reduce_sum(
            gradients_sqr, axis=tf.range(1, len(gradients_sqr.shape)))
        gradient_l2_norm = tf.sqrt(gradients_sqr_sum + 1e-8)  # 添加小值避免NaN
        gradient_penalty = tf.square(1 - gradient_l2_norm)
        return tf.reduce_mean(gradient_penalty)
    
    def wasserstein_loss(self, y_true, y_pred):
        """Wasserstein损失"""
        return K.mean(y_true * y_pred)
    
    def classification_loss(self, y_true, y_pred):
        """分类损失"""
        return tf.keras.losses.binary_crossentropy(y_true, y_pred)
    
    def train(self, epochs, batch_size, log_file=None):
        """训练函数"""
        start_time = datetime.datetime.now()
        
        accuracy_list_1 = []
        accuracy_list_2 = []
        accuracy_list_3 = []
        accuracy_list_4 = []
        
        # 加载数据
        PATH_train = './dataset_train_case1.npz'
        PATH_test = './dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        # 提取训练数据
        train_samples = []
        train_attributes = []
        for i in [2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 15]:
            train_samples.append(train_data[f'training_samples_{i}'])
            train_attributes.append(train_data[f'training_attribute_{i}'])
        
        train_X = np.concatenate(train_samples, axis=0)
        train_Y = np.concatenate(train_attributes, axis=0)
        
        # 提取测试数据
        test_samples = []
        test_attributes = []
        for i in [1, 6, 14]:
            test_samples.append(test_data[f'testing_samples_{i}'])
            test_attributes.append(test_data[f'testing_attribute_{i}'])
        
        test_X = np.concatenate(test_samples, axis=0)
        test_Y = np.concatenate(test_attributes, axis=0)
        
        # 构建数据管道
        train_dataset = tf.data.Dataset.from_tensor_slices((train_X, train_Y))
        train_dataset = train_dataset.shuffle(buffer_size=len(train_X)).batch(batch_size).prefetch(tf.data.AUTOTUNE)
        
        for epoch in range(epochs):
            for train_x, train_y in train_dataset:
                current_batch_size = tf.shape(train_x)[0]
                
                # 训练判别器
                self.d.trainable = True
                self.g.trainable = False
                self.c.trainable = False
                
                for _ in range(self.n_critic):
                    with tf.GradientTape() as tape_d:
                        # 生成噪声和假样本
                        noise = tf.random.normal([current_batch_size, self.latent_dim])
                        fake_samples = self.g([noise, train_y], training=True)
                        
                        # 判别器输出
                        real_validity = self.d([train_x, train_y], training=True)
                        fake_validity = self.d([fake_samples, train_y], training=True)
                        
                        # 梯度惩罚
                        interpolated = RandomWeightedAverage()([train_x, fake_samples])
                        with tf.GradientTape() as tape_gp:
                            tape_gp.watch(interpolated)
                            interpolated_validity = self.d([interpolated, train_y], training=True)
                        
                        gradients = tape_gp.gradient(interpolated_validity, [interpolated])[0]
                        gradient_penalty = self.gradient_penalty_loss(gradients)
                        
                        # 判别器损失
                        d_loss_real = self.wasserstein_loss(tf.ones_like(real_validity), real_validity)
                        d_loss_fake = self.wasserstein_loss(-tf.ones_like(fake_validity), fake_validity)
                        d_loss = d_loss_real + d_loss_fake + self.LAMBDA_GP * gradient_penalty
                        
                        # 检查数值稳定性
                        tf.debugging.check_numerics(d_loss, "D loss is not finite")
                    
                    grads_d = tape_d.gradient(d_loss, self.d.trainable_variables)
                    self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_variables))
                
                # 训练分类器
                self.d.trainable = False
                self.g.trainable = False
                self.c.trainable = True
                
                with tf.GradientTape() as tape_c:
                    pred_attributes = self.c(train_x, training=True)
                    c_loss = self.classification_loss(train_y, pred_attributes)
                    c_loss = tf.reduce_mean(c_loss)
                    tf.debugging.check_numerics(c_loss, "C loss is not finite")
                
                grads_c = tape_c.gradient(c_loss, self.c.trainable_variables)
                self.c_optimizer.apply_gradients(zip(grads_c, self.c.trainable_variables))
                
                # 训练生成器
                self.d.trainable = False
                self.g.trainable = True
                self.c.trainable = False
                
                with tf.GradientTape() as tape_g:
                    noise = tf.random.normal([current_batch_size, self.latent_dim])
                    fake_samples_g = self.g([noise, train_y], training=True)
                    
                    # 对抗损失
                    fake_validity_g = self.d([fake_samples_g, train_y], training=True)
                    g_adv_loss = self.wasserstein_loss(tf.ones_like(fake_validity_g), fake_validity_g)
                    
                    # 分类损失
                    pred_attributes_g = self.c(fake_samples_g, training=True)
                    g_cla_loss = self.classification_loss(train_y, pred_attributes_g)
                    g_cla_loss = tf.reduce_mean(g_cla_loss)
                    
                    # 总生成器损失
                    g_loss = self.lambda_adv * g_adv_loss + self.lambda_cla * g_cla_loss
                    tf.debugging.check_numerics(g_loss, "G loss is not finite")
                
                grads_g = tape_g.gradient(g_loss, self.g.trainable_variables)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_variables))
            
            # 每个epoch的进度报告
            elapsed_time = datetime.datetime.now() - start_time
            print(f"[Epoch {epoch}/{epochs}] [D loss: {tf.reduce_mean(d_loss):.4f}] "
                  f"[G loss: {tf.reduce_mean(g_loss):.4f}] [C loss: {tf.reduce_mean(c_loss):.4f}] "
                  f"time: {elapsed_time}")
            
            # 每个epoch测试准确率
            if epoch % 1 == 0:
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = \
                    sample_generation_and_diagnosis(200, test_X, test_Y, self.g)
                
                accuracy_list_1.append(accuracy_lsvm)
                accuracy_list_2.append(accuracy_nrf)
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)
                
                print(f"[Epoch {epoch}/{epochs}] [Accuracy_lsvm: {max(accuracy_list_1):.6f}] "
                      f"[Accuracy_nrf: {max(accuracy_list_2):.6f}] "
                      f"[Accuracy_pnb: {max(accuracy_list_3):.6f}] "
                      f"[Accuracy_mlp: {max(accuracy_list_4):.6f}]")
                
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):.6f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):.6f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):.6f}] "
                                   f"[Accuracy_mlp: {max(accuracy_list_4):.6f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
        
        best_accuracy = max([max(accuracy_list_1), max(accuracy_list_2), 
                            max(accuracy_list_3), max(accuracy_list_4)])
        print(f'finished! best_acc:{best_accuracy:.4f}')
        if log_file:
            log_file.write(f'finished! best_acc:{best_accuracy:.4f}\n')
            log_file.flush()


if __name__ == '__main__':
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)
    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + "_improved.md"
    log_filename = os.path.join(results_dir, log_filename_base)
    
    print(f"训练开始，日志将被记录到: {log_filename}")
    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 训练日志 (Improved Dense ACGAN)\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = ImprovedACGAN()
        gan.train(epochs=1000, batch_size=120, log_file=log_file)
        
        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")
    
    print(f"训练完成，日志已保存至: {log_filename}") 
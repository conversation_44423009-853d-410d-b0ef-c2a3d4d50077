# 🔥 优化版权重配置
def __init__(self):
    # ... 其他初始化代码 ...
    
    # 更保守但稳定的权重设置
    self.lambda_cla = 8.0           # 10 -> 8 (略微降低)
    self.lambda_triplet = 25.0      # 30 -> 25 (避免过强)
    self.lambda_center = 1.0        # 1.5 -> 1.0 (防止突然爆炸)
    self.lambda_crl = 0.05          # 0.1 -> 0.05 (更保守)
    self.lambda_semantic_consistency = 0.02  # 0.05 -> 0.02
    
    # 添加梯度裁剪
    self.gradient_clip_norm = 1.0
    
    # 学习率调度
    self.initial_lr = 0.0001
    self.lr_decay_factor = 0.8
    self.lr_decay_epochs = 300
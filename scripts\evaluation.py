from keras.models import load_model

import tensorflow as tf
import numpy as np
import random
from sklearn import preprocessing
from sklearn.svm import SVR
from sklearn.svm import SVC
from sklearn.metrics import confusion_matrix
from sklearn.metrics import accuracy_score
from tensorflow.keras import regularizers
from tensorflow.keras.layers import Input, Dense, Reshape, Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.layers import UpSampling2D, Conv2D, Conv1D,MaxPooling1D,UpSampling1D
from tensorflow.keras.layers import BatchNormalization, Activation, ZeroPadding2D,Embedding,concatenate
from tensorflow.keras.regularizers import l2
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LeakyReLU
from sklearn.svm import SVC,LinearSVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier

def scalar_stand(Train_X, Test_X):
    # 用训练集标准差标准化训练集以及测试集
    scalar_train = preprocessing.StandardScaler().fit(Train_X)
    #scalar_test = preprocessing.StandardScaler().fit(Test_X)
    Train_X = scalar_train.transform(Train_X)
    Test_X = scalar_train.transform(Test_X)
    return Train_X, Test_X


def feature_generation_and_diagnosis(add_quantity, test_x, test_y, autoencoder, generator, classifier, test_class_indices):
    
    print(f"loading data...")
    print(f"test classes: {test_class_indices}")

    # 确定训练中未见的类别
    all_classes = np.arange(1, 16)
    seen_classes = np.setdiff1d(all_classes, test_class_indices)
    print(f"train classes: {seen_classes}")
    
    Labels_train = []
    Labels_test = []
    Generated_feature = []

    samples_per_class = len(test_x) // len(test_class_indices)
    
    for i, class_idx in enumerate(test_class_indices):
        
        # 动态获取属性向量
        attribute_index = i * samples_per_class
        attribute_vector = test_y[attribute_index]
        
        attribute = [attribute_vector for _ in range(add_quantity)]
        attribute = np.array(attribute)
     
        print(f"为类别 {class_idx} 生成 {add_quantity} 个特征, 属性形状: {attribute.shape}")
        
        # 关键修复：分批生成，避免GPU内存不足
        batch_size = 500  # 每批500个样本
        generated_features_batches = []
        
        for batch_start in range(0, add_quantity, batch_size):
            batch_end = min(batch_start + batch_size, add_quantity)
            batch_size_actual = batch_end - batch_start
            
            noise_shape = (batch_size_actual, 50, 1)
            noise_batch = tf.random.normal(shape=noise_shape)
            attribute_batch = attribute[batch_start:batch_end]
            
            # 分批预测，避免内存问题，使用CPU避免LayerNormalization GPU兼容性问题
            with tf.device('/CPU:0'):
                generated_batch = generator.predict([noise_batch, attribute_batch], verbose=0)
            generated_features_batches.append(generated_batch)
        
        # 合并所有批次
        generated_feature = np.concatenate(generated_features_batches, axis=0)
      
        Generated_feature.append(generated_feature)

        labels_train = np.full((add_quantity, 1), class_idx)
        # 确保测试标签与传入的类别索引匹配
        labels_test = np.full((samples_per_class, 1), class_idx)

        Labels_train.append(labels_train)
        Labels_test.append(labels_test)
    
    Generated_feature=np.array(Generated_feature).reshape(-1, 256)
    Labels_train=np.array(Labels_train).reshape(-1, 1)

    Labels_test=np.array(Labels_test).reshape(-1, 1)

    # 使用CPU避免LayerNormalization GPU兼容性问题
    with tf.device('/CPU:0'):
        test_feature, decoded_test= autoencoder(test_x)
        hidden_ouput_train,predict_attribute_train=classifier(Generated_feature)
        hidden_ouput_test,predict_attribute_test=classifier(test_feature)

    new_feature_train=np.concatenate((Generated_feature, hidden_ouput_train), axis=1)
    new_feature_test=np.concatenate((test_feature, hidden_ouput_test), axis=1)


    
    train_X=new_feature_train
    train_Y=Labels_train
    
    test_X=new_feature_test
    test_Y=Labels_test

    train_X,test_X=scalar_stand(train_X, test_X)

    print("开始快速分类器训练...")

    # 只使用最快的两个分类器进行快速评估
    classifier_lsvm = LinearSVC(max_iter=500, dual=False, random_state=42)  # 进一步减少迭代次数，使用primal求解
    print("训练LinearSVM...")
    classifier_lsvm.fit(train_X, train_Y.ravel())
    Y_pred_lsvm = classifier_lsvm.predict(test_X)
    accuracy_lsvm = accuracy_score(test_Y, Y_pred_lsvm)
    print(f"LinearSVM完成，准确率: {accuracy_lsvm:.4f}")
    
    # 使用最简单的朴素贝叶斯
    classifier_pnb = GaussianNB()
    print("训练GaussianNB...")
    classifier_pnb.fit(train_X, train_Y.ravel())
    Y_pred_pnb = classifier_pnb.predict(test_X)
    accuracy_pnb = accuracy_score(test_Y, Y_pred_pnb)
    print(f"GaussianNB完成，准确率: {accuracy_pnb:.4f}")
    
    # 使用非常简化的随机森林
    classifier_nrf = RandomForestClassifier(n_estimators=10, max_depth=5, n_jobs=1, random_state=42)  # 极简配置
    print("训练RandomForest...")
    classifier_nrf.fit(train_X, train_Y.ravel())
    Y_pred_nrf = classifier_nrf.predict(test_X)
    accuracy_nrf = accuracy_score(test_Y, Y_pred_nrf)
    print(f"RandomForest完成，准确率: {accuracy_nrf:.4f}")
    
    # 极简的MLP
    classifier_mlp = MLPClassifier(hidden_layer_sizes=(20,),  # 更小的网络
                               activation='relu',
                               solver='lbfgs',  # 对小数据集更快
                               alpha=0.01,
                               max_iter=50,  # 大幅减少迭代
                               tol=0.01,     # 放宽收敛条件
                               random_state=42)
    print("训练MLPClassifier...")
    classifier_mlp.fit(train_X, train_Y.ravel())
    Y_pred_mlp = classifier_mlp.predict(test_X)
    accuracy_mlp = accuracy_score(test_Y, Y_pred_mlp)
    print(f"MLPClassifier完成，准确率: {accuracy_mlp:.4f}")
    
    print("所有分类器训练完成！")
   
    
    return accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp

#!/usr/bin/env python3
"""
快速改进策略 - 基于Group B成功经验的系统性优化
目标: 将所有组别提升到75%+准确率
"""

import os
import sys
import json
import torch
import numpy as np
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')

def analyze_group_b_success():
    """分析Group B成功的关键因素"""
    print("🔍 分析Group B成功因素...")
    
    # 读取Group B的配置
    config_file = 'experiments/group_B/experiment_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        print("📊 Group B成功配置:")
        model_config = config.get('model_config', {})
        training_config = config.get('training_config', {})
        
        print(f"   特征维度: {model_config.get('feature_dim', 'N/A')}")
        print(f"   属性维度: {model_config.get('attribute_dim', 'N/A')}")
        print(f"   潜在维度: {model_config.get('latent_dim', 'N/A')}")
        print(f"   隐藏层: {model_config.get('hidden_dims', 'N/A')}")
        
        print(f"   批次大小: {training_config.get('batch_size', 'N/A')}")
        print(f"   生成器学习率: {training_config.get('learning_rate_g', 'N/A')}")
        print(f"   判别器学习率: {training_config.get('learning_rate_d', 'N/A')}")
        
        print(f"   对抗损失权重: {training_config.get('adversarial_weight', 'N/A')}")
        print(f"   循环一致性权重: {training_config.get('cycle_consistency_weight', 'N/A')}")
        print(f"   语义距离权重: {training_config.get('semantic_distance_weight', 'N/A')}")
        
        return config
    else:
        print("❌ 无法找到Group B配置文件")
        return None

def create_optimized_configs():
    """基于Group B成功经验创建优化配置"""
    print("\n🔧 创建优化配置...")
    
    # Group B的成功配置作为基础
    base_config = {
        'model_config': {
            'feature_dim': 52,
            'attribute_dim': 20,
            'latent_dim': 50,
            'hidden_dims': [128, 256, 128]
        },
        'training_config': {
            'batch_size': 32,
            'learning_rate_g': 0.0002,  # 可能需要调整
            'learning_rate_d': 0.0004,  # 可能需要调整
            'adversarial_weight': 1.0,
            'cycle_consistency_weight': 0.1,
            'semantic_distance_weight': 0.1,
            'uncertainty_weight': 0.1,
            'domain_selection_weight': 0.1,
            'gradient_penalty_weight': 10.0
        }
    }
    
    # 为不同组别创建特定优化
    group_optimizations = {
        'A': {
            # Group A需要大幅提升 (60.69% -> 80%+)
            'learning_rate_g': 0.0001,  # 降低学习率，更稳定训练
            'learning_rate_d': 0.0002,
            'batch_size': 64,  # 增大批次，更稳定梯度
            'adversarial_weight': 0.5,  # 降低对抗权重
            'cycle_consistency_weight': 0.2,  # 增强循环一致性
            'semantic_distance_weight': 0.15,  # 增强语义约束
        },
        'B': {
            # Group B已经成功，微调即可
            'learning_rate_g': 0.0002,
            'learning_rate_d': 0.0004,
            'batch_size': 32,
            'adversarial_weight': 1.0,
            'cycle_consistency_weight': 0.1,
            'semantic_distance_weight': 0.1,
        },
        'C': {
            # Group C需要显著提升 (44.72% -> 75%+)
            'learning_rate_g': 0.00015,
            'learning_rate_d': 0.0003,
            'batch_size': 48,
            'adversarial_weight': 0.8,
            'cycle_consistency_weight': 0.15,
            'semantic_distance_weight': 0.12,
            'uncertainty_weight': 0.15,  # 增强不确定性约束
        },
        'D': {
            # Group D接近基准，小幅优化
            'learning_rate_g': 0.00018,
            'learning_rate_d': 0.00035,
            'batch_size': 40,
            'adversarial_weight': 0.9,
            'cycle_consistency_weight': 0.12,
            'semantic_distance_weight': 0.11,
        },
        'E': {
            # Group E需要大幅提升 (47.67% -> 75%+)
            'learning_rate_g': 0.00012,
            'learning_rate_d': 0.00025,
            'batch_size': 56,
            'adversarial_weight': 0.6,
            'cycle_consistency_weight': 0.18,
            'semantic_distance_weight': 0.14,
            'domain_selection_weight': 0.15,  # 增强域选择
        }
    }
    
    # 生成优化配置文件
    configs_dir = 'optimized_configs'
    os.makedirs(configs_dir, exist_ok=True)
    
    for group, optimizations in group_optimizations.items():
        # 合并基础配置和组别优化
        config = base_config.copy()
        config['training_config'].update(optimizations)
        
        # 添加组别信息
        config['group_info'] = {
            'group': group,
            'test_classes': get_test_classes(group),
            'optimization_target': get_target_accuracy(group),
            'current_accuracy': get_current_accuracy(group)
        }
        
        # 保存配置
        config_file = f'{configs_dir}/group_{group.lower()}_optimized.json'
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ 已生成 {config_file}")
    
    return configs_dir

def get_test_classes(group):
    """获取测试类别"""
    test_classes = {
        'A': [1, 6, 14],
        'B': [4, 7, 10], 
        'C': [8, 11, 12],
        'D': [2, 3, 5],
        'E': [9, 13, 15]
    }
    return test_classes.get(group, [])

def get_target_accuracy(group):
    """获取目标准确率"""
    # 基于当前性能设定合理目标
    targets = {
        'A': 80.0,  # 从60.69%提升到80%
        'B': 65.0,  # 从61.04%提升到65% (已超基准)
        'C': 75.0,  # 从44.72%提升到75%
        'D': 75.0,  # 从60.24%提升到75%
        'E': 75.0   # 从47.67%提升到75%
    }
    return targets.get(group, 75.0)

def get_current_accuracy(group):
    """获取当前准确率"""
    current = {
        'A': 60.69,
        'B': 61.04,
        'C': 44.72,
        'D': 60.24,
        'E': 47.67
    }
    return current.get(group, 0.0)

def create_training_script():
    """创建优化训练脚本"""
    print("\n🚀 创建优化训练脚本...")
    
    script_content = '''#!/usr/bin/env python3
"""
优化训练脚本 - 基于Group B成功经验
"""

import os
import sys
import json
import argparse
from datetime import datetime

sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')
from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer

def load_config(config_file):
    """加载配置文件"""
    with open(config_file, 'r') as f:
        return json.load(f)

def run_optimized_training(group, config_file, epochs=1000):
    """运行优化训练"""
    print(f"🚀 开始Group {group}优化训练...")
    
    # 加载配置
    config = load_config(config_file)
    
    # 创建训练器
    trainer = EnhancedASDCGANTrainer(
        model_config=config['model_config'],
        training_config=config['training_config'],
        experiment_name=f"optimized_group_{group.lower()}",
        device='cuda' if torch.cuda.is_available() else 'cpu'
    )
    
    # 设置数据
    test_classes = config['group_info']['test_classes']
    trainer.setup_data(test_classes=test_classes)
    
    # 开始训练
    history = trainer.train(epochs=epochs)
    
    # 输出结果
    final_acc = history['accuracy'][-1] if history['accuracy'] else 0
    target_acc = config['group_info']['optimization_target']
    
    print(f"\\n📊 Group {group} 训练完成:")
    print(f"   最终准确率: {final_acc:.2f}%")
    print(f"   目标准确率: {target_acc:.2f}%")
    print(f"   是否达标: {'✅' if final_acc >= target_acc else '❌'}")
    
    return final_acc >= target_acc

def main():
    parser = argparse.ArgumentParser(description='优化训练脚本')
    parser.add_argument('--group', type=str, required=True, choices=['A', 'B', 'C', 'D', 'E'],
                       help='训练组别')
    parser.add_argument('--epochs', type=int, default=1000, help='训练轮次')
    
    args = parser.parse_args()
    
    config_file = f'optimized_configs/group_{args.group.lower()}_optimized.json'
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    success = run_optimized_training(args.group, config_file, args.epochs)
    return success

if __name__ == "__main__":
    main()
'''
    
    with open('run_optimized_training.py', 'w') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod('run_optimized_training.py', 0o755)
    print("✅ 已创建 run_optimized_training.py")

def main():
    """主函数"""
    print("🚀 ASDCGAN快速改进策略")
    print("=" * 60)
    print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 目标: 基于Group B成功经验，提升所有组别性能")
    print("=" * 60)
    
    # 1. 分析Group B成功因素
    group_b_config = analyze_group_b_success()
    
    # 2. 创建优化配置
    configs_dir = create_optimized_configs()
    
    # 3. 创建训练脚本
    create_training_script()
    
    # 4. 输出下一步指令
    print("\n🎯 下一步执行指令:")
    print("=" * 40)
    
    priority_groups = ['A', 'C', 'E']  # 优先改进差距最大的组别
    
    for i, group in enumerate(priority_groups, 1):
        current_acc = get_current_accuracy(group)
        target_acc = get_target_accuracy(group)
        improvement = target_acc - current_acc
        
        print(f"{i}. Group {group} (需提升 {improvement:.1f}%):")
        print(f"   python run_optimized_training.py --group {group} --epochs 1000")
        print()
    
    print("📊 预期结果:")
    print("   - Group A: 60.69% → 80%+ (发表标准)")
    print("   - Group C: 44.72% → 75%+ (发表标准)")  
    print("   - Group E: 47.67% → 75%+ (发表标准)")
    print()
    print("⏱️  预计时间: 每组2-3小时，总计6-9小时")
    print("🎯 成功标准: 所有组别达到75%+准确率")

if __name__ == "__main__":
    main()

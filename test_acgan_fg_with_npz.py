#!/usr/bin/env python3
"""
使用.npz数据测试ACGAN-FG性能
验证直接使用预处理数据是否能提高性能
"""

import os
import sys
import datetime
import numpy as np
import subprocess

def run_modified_acgan_fg():
    """运行修改后的ACGAN-FG (使用.npz数据)"""
    print("🚀 运行修改后的ACGAN-FG (使用.npz数据)...")
    print("=" * 60)
    
    try:
        # 设置环境变量，减少TensorFlow输出
        env = os.environ.copy()
        env['TF_CPP_MIN_LOG_LEVEL'] = '2'
        
        # 运行修改后的代码
        result = subprocess.run([
            sys.executable, "ACGAN_FG.py"
        ], capture_output=True, text=True, timeout=1800, env=env)  # 30分钟超时
        
        print("✅ 修改后的ACGAN-FG运行完成")
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print("\n📊 标准输出 (最后2000字符):")
            print("-" * 50)
            print(result.stdout[-2000:])
            
        if result.stderr:
            print("\n⚠️ 错误输出 (最后1000字符):")
            print("-" * 50)
            print(result.stderr[-1000:])
            
        return result.returncode == 0, result.stdout, result.stderr
        
    except subprocess.TimeoutExpired:
        print("❌ 修改后的ACGAN-FG运行超时")
        return False, "", "运行超时"
    except Exception as e:
        print(f"❌ 修改后的ACGAN-FG运行失败: {e}")
        return False, "", str(e)

def extract_final_accuracies(stdout):
    """提取最终准确率"""
    print("\n🔍 提取最终准确率...")
    
    lines = stdout.split('\n')
    final_accuracies = {}
    
    # 查找最后的准确率输出
    for line in reversed(lines):
        if 'finished! best_acc:' in line:
            try:
                # 提取最佳准确率
                best_acc = float(line.split('best_acc:')[1].strip())
                print(f"🎯 最佳准确率: {best_acc:.4f} ({best_acc*100:.2f}%)")
                break
            except (ValueError, IndexError):
                continue
    
    # 查找各分类器的最高准确率
    max_accuracies = {
        'LinearSVM': 0,
        'RandomForest': 0,
        'GaussianNB': 0,
        'MLP': 0
    }
    
    for line in lines:
        if 'Accuracy_lsvm:' in line and 'Accuracy_nrf:' in line:
            try:
                # 解析格式: [Epoch X/Y] [Accuracy_lsvm: X] [Accuracy_nrf: X] [Accuracy_pnb: X][Accuracy_mlp: X]
                parts = line.split(']')
                for part in parts:
                    if 'Accuracy_lsvm:' in part:
                        acc = float(part.split(':')[1].strip())
                        max_accuracies['LinearSVM'] = max(max_accuracies['LinearSVM'], acc)
                    elif 'Accuracy_nrf:' in part:
                        acc = float(part.split(':')[1].strip())
                        max_accuracies['RandomForest'] = max(max_accuracies['RandomForest'], acc)
                    elif 'Accuracy_pnb:' in part:
                        acc = float(part.split(':')[1].strip())
                        max_accuracies['GaussianNB'] = max(max_accuracies['GaussianNB'], acc)
                    elif 'Accuracy_mlp:' in part:
                        acc = float(part.split(':')[1].strip())
                        max_accuracies['MLP'] = max(max_accuracies['MLP'], acc)
            except (ValueError, IndexError):
                continue
    
    return max_accuracies

def compare_with_literature():
    """与文献结果对比"""
    print(f"\n📚 文献结果 (E组):")
    literature_results = {
        'LinearSVM': 68.67,
        'RandomForest': 80.23, 
        'GaussianNB': 71.44,
        'MLP': 89.06  # 文献中的89.06%
    }
    
    for classifier, acc in literature_results.items():
        print(f"   {classifier:<15}: {acc:.2f}%")
    
    return literature_results

def analyze_performance_improvement(our_results, literature_results):
    """分析性能改进"""
    print(f"\n📈 性能对比分析:")
    print("=" * 60)
    print(f"{'分类器':<15} {'文献结果':<10} {'我们结果':<10} {'差距':<10} {'状态'}")
    print("-" * 60)
    
    improvements = []
    
    for classifier in literature_results.keys():
        lit_acc = literature_results[classifier]
        our_acc = our_results.get(classifier, 0) * 100
        diff = our_acc - lit_acc
        
        if diff >= -5:
            status = "🎉 优秀"
        elif diff >= -10:
            status = "✅ 良好"
        elif diff >= -20:
            status = "⚠️ 一般"
        else:
            status = "❌ 较差"
        
        print(f"{classifier:<15} {lit_acc:>8.2f}% {our_acc:>8.2f}% {diff:>+8.1f}% {status}")
        improvements.append(diff)
    
    # 总体评估
    avg_improvement = np.mean(improvements)
    mlp_improvement = our_results.get('MLP', 0) * 100 - literature_results['MLP']
    
    print(f"\n🎯 总体评估:")
    print(f"   平均改进: {avg_improvement:+.1f}%")
    print(f"   MLP改进: {mlp_improvement:+.1f}% (关键指标)")
    
    if mlp_improvement >= -5:
        print(f"   🎉 成功! 接近或超越文献结果")
        return True
    elif mlp_improvement >= -15:
        print(f"   ✅ 良好! 显著改进但仍有差距")
        return False
    else:
        print(f"   ⚠️ 需要进一步优化")
        return False

def provide_next_steps(success):
    """提供下一步建议"""
    print(f"\n💡 下一步建议:")
    print("=" * 50)
    
    if success:
        suggestions = [
            "🎉 恭喜! 已接近文献性能",
            "📝 可以开始撰写论文，重点突出:",
            "   - 数据处理优化的重要性",
            "   - 实现细节对性能的影响",
            "   - 复现研究的价值",
            "🔬 进一步优化方向:",
            "   - 尝试不同的网络架构",
            "   - 调优超参数",
            "   - 集成多个模型"
        ]
    else:
        suggestions = [
            "🔧 继续优化建议:",
            "   1. 检查属性定义是否与文献一致",
            "   2. 调整网络架构和训练参数",
            "   3. 优化损失函数权重",
            "   4. 改进特征生成策略",
            "   5. 尝试数据增强技术",
            "📚 研究方向调整:",
            "   - 重点分析实现差异",
            "   - 提出改进方案",
            "   - 进行消融实验"
        ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")

def main():
    """主函数"""
    print("🚀 ACGAN-FG性能测试 (使用.npz数据)")
    print(f"⏰ 开始时间: {datetime.datetime.now()}")
    print("🎯 目标: 验证使用预处理数据是否能提高性能")
    print("=" * 70)
    
    # 检查必要文件
    required_files = [
        "ACGAN_FG.py",
        "load_npz_data.py",
        "test_enhanced_acgan_fg.py",
        "data/dataset_train_case1.npz",
        "data/dataset_test_case1.npz"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return
    
    print("✅ 所有必要文件存在")
    
    # 运行修改后的ACGAN-FG
    success, stdout, stderr = run_modified_acgan_fg()
    
    if not success:
        print("❌ ACGAN-FG运行失败")
        print(f"错误信息: {stderr}")
        return
    
    # 提取结果
    our_results = extract_final_accuracies(stdout)
    literature_results = compare_with_literature()
    
    # 分析性能
    performance_success = analyze_performance_improvement(our_results, literature_results)
    
    # 提供建议
    provide_next_steps(performance_success)
    
    # 总结
    print(f"\n📋 测试总结:")
    print("=" * 50)
    if performance_success:
        print("🎉 测试成功! 使用.npz数据显著提高了性能")
        print("✅ 已接近文献报告的性能水平")
    else:
        print("⚠️ 测试部分成功! 性能有所提高但仍有差距")
        print("🔧 建议继续优化实现细节")
    
    print(f"⏰ 结束时间: {datetime.datetime.now()}")

if __name__ == "__main__":
    main()

import re
import numpy as np
from datetime import datetime
import tensorflow as tf
from tensorflow.summary import create_file_writer, scalar, histogram
import os

class TensorBoardLossVisualizer:
    """使用TensorBoard可视化损失函数的工具类"""
    
    def __init__(self, log_dir="./tensorboard_logs"):
        """初始化TensorBoard写入器"""
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        
        # 创建不同类型损失的写入器
        self.writers = {
            'losses': create_file_writer(f"{log_dir}/losses"),
            'accuracies': create_file_writer(f"{log_dir}/accuracies"),
            'training_progress': create_file_writer(f"{log_dir}/training_progress")
        }
        
    def parse_log_file(self, log_file_path):
        """解析日志文件，提取损失函数数据"""
        data = {
            'epochs': [],
            'ae_c_loss': [],
            'triplet_loss': [],
            'center_loss': [],
            'combined_loss': [],
            'crl_loss': [],
            'semantic_loss': [],
            'd_loss': [],
            'g_loss': [],
            'lsvm_acc': [],
            'rf_acc': [],
            'nb_acc': [],
            'mlp_acc': []
        }
        
        # 正则表达式模式
        epoch_pattern = r'Epoch\s+(\d+)/2000.*?Time: ([\d:\.]+)'
        loss_pattern = r'AE\+C loss: (\d+(?:\.\d+)?) \| 强化Triplet: ([\d\.]+) \| 强化Center: (\d+(?:\.\d+)?) \| 组合强化: (\d+(?:\.\d+)?) \| 适度CRL: ([\d\.]+) \| 语义增强: ([\d\.]+) \| D loss: ([-\d\.]+) \| G loss: (\d+(?:\.\d+)?)'
        acc_pattern = r'LinearSVM: ([\d\.]+)% \| RandomForest: ([\d\.]+)% \| GaussianNB: ([\d\.]+)% \| MLPClassifier: ([\d\.]+)%'
        
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按epoch分割内容
        epoch_blocks = re.split(r'Epoch\s+\d+/2000.*?Time:', content)
        
        for i, block in enumerate(epoch_blocks[1:]):
            epoch_match = re.search(r'^.*?(\d+:\d+:\d+\.\d+)', block)
            if not epoch_match:
                continue
                
            loss_match = re.search(loss_pattern, block)
            if loss_match:
                data['epochs'].append(i)
                data['ae_c_loss'].append(float(loss_match.group(1)))
                data['triplet_loss'].append(float(loss_match.group(2)))
                data['center_loss'].append(float(loss_match.group(3)))
                data['combined_loss'].append(float(loss_match.group(4)))
                data['crl_loss'].append(float(loss_match.group(5)))
                data['semantic_loss'].append(float(loss_match.group(6)))
                data['d_loss'].append(float(loss_match.group(7)))
                data['g_loss'].append(float(loss_match.group(8)))
                
                acc_match = re.search(acc_pattern, block)
                if acc_match:
                    data['lsvm_acc'].append(float(acc_match.group(1)))
                    data['rf_acc'].append(float(acc_match.group(2)))
                    data['nb_acc'].append(float(acc_match.group(3)))
                    data['mlp_acc'].append(float(acc_match.group(4)))
                else:
                    data['lsvm_acc'].append(np.nan)
                    data['rf_acc'].append(np.nan)
                    data['nb_acc'].append(np.nan)
                    data['mlp_acc'].append(np.nan)
        
        return data
    
    def write_to_tensorboard(self, data):
        """将数据写入TensorBoard"""
        print("📊 正在写入TensorBoard数据...")
        
        epochs = data['epochs']
        
        # 写入损失函数数据
        with self.writers['losses'].as_default():
            for i, epoch in enumerate(epochs):
                # 主要损失函数
                scalar('AE_C_Loss', data['ae_c_loss'][i], step=epoch)
                scalar('Generator_Loss', data['g_loss'][i], step=epoch)
                scalar('Discriminator_Loss', data['d_loss'][i], step=epoch)
                
                # 强化损失函数
                scalar('Reinforcement/Triplet_Loss', data['triplet_loss'][i], step=epoch)
                scalar('Reinforcement/Center_Loss', data['center_loss'][i], step=epoch)
                scalar('Reinforcement/Combined_Loss', data['combined_loss'][i], step=epoch)
                
                # 正则化损失函数
                scalar('Regularization/CRL_Loss', data['crl_loss'][i], step=epoch)
                scalar('Regularization/Semantic_Loss', data['semantic_loss'][i], step=epoch)
        
        # 写入准确率数据
        with self.writers['accuracies'].as_default():
            for i, epoch in enumerate(epochs):
                if not np.isnan(data['lsvm_acc'][i]):
                    scalar('Accuracy/LinearSVM', data['lsvm_acc'][i], step=epoch)
                    scalar('Accuracy/RandomForest', data['rf_acc'][i], step=epoch)
                    scalar('Accuracy/GaussianNB', data['nb_acc'][i], step=epoch)
                    scalar('Accuracy/MLPClassifier', data['mlp_acc'][i], step=epoch)
        
        # 写入训练进度分析
        with self.writers['training_progress'].as_default():
            for i, epoch in enumerate(epochs):
                # 损失比率分析
                total_loss = data['ae_c_loss'][i] + abs(data['g_loss'][i])
                triplet_ratio = data['triplet_loss'][i] / total_loss * 100
                center_ratio = data['center_loss'][i] / total_loss * 100
                
                scalar('Loss_Ratios/Triplet_Percentage', triplet_ratio, step=epoch)
                scalar('Loss_Ratios/Center_Percentage', center_ratio, step=epoch)
                
                # GAN训练稳定性
                gan_stability = abs(data['d_loss'][i]) / (abs(data['g_loss'][i]) + 1e-8)
                scalar('GAN_Training/Stability_Ratio', gan_stability, step=epoch)
        
        # 刷新所有写入器
        for writer in self.writers.values():
            writer.flush()
        
        print("✅ TensorBoard数据写入完成！")
    
    def create_summary_statistics(self, data):
        """创建统计摘要"""
        print("\n" + "="*60)
        print("📈 TensorBoard 可视化摘要")
        print("="*60)
        
        if len(data['epochs']) > 0:
            print(f"📊 训练轮次: Epoch 0-{max(data['epochs'])}")
            print(f"📈 数据点数: {len(data['epochs'])} 个epoch")
            
            # 损失函数变化分析
            print(f"\n🔥 主要损失函数趋势:")
            ae_change = ((data['ae_c_loss'][-1]/data['ae_c_loss'][0]-1)*100)
            g_change = ((data['g_loss'][-1]/data['g_loss'][0]-1)*100)
            print(f"  • AE+C Loss: {data['ae_c_loss'][0]:,.0f} → {data['ae_c_loss'][-1]:,.0f} ({ae_change:+.1f}%)")
            print(f"  • G Loss: {data['g_loss'][0]:,.0f} → {data['g_loss'][-1]:,.0f} ({g_change:+.1f}%)")
            print(f"  • D Loss: {data['d_loss'][0]:+.1f} → {data['d_loss'][-1]:+.1f}")
            
            # 强化损失分析
            print(f"\n💪 强化学习组件:")
            triplet_change = ((data['triplet_loss'][-1]/data['triplet_loss'][0]-1)*100)
            center_change = ((data['center_loss'][-1]/data['center_loss'][0]-1)*100)
            print(f"  • Triplet Loss: {data['triplet_loss'][0]:,.1f} → {data['triplet_loss'][-1]:,.1f} ({triplet_change:+.1f}%)")
            print(f"  • Center Loss: {data['center_loss'][0]:,.0f} → {data['center_loss'][-1]:,.0f} ({center_change:+.1f}%)")
            
            # 准确率分析
            valid_accs = [acc for acc in data['lsvm_acc'] if not np.isnan(acc)]
            if valid_accs:
                print(f"\n🎯 分类性能:")
                print(f"  • 当前准确率: {valid_accs[-1]:.2f}%")
                print(f"  • 性能状态: {'基准水平' if valid_accs[-1] <= 35 else '有效学习'}")
        
        print(f"\n🚀 TensorBoard 启动命令:")
        print(f"  tensorboard --logdir={self.log_dir}")
        print(f"  然后访问: http://localhost:6006")
    
    def close(self):
        """关闭所有写入器"""
        for writer in self.writers.values():
            writer.close()

def main():
    """主函数"""
    log_file_path = "/app/B_Hybrid_20250715_1354.log"
    
    # 创建TensorBoard可视化器
    visualizer = TensorBoardLossVisualizer("./tensorboard_logs/B_Hybrid_experiment")
    
    try:
        print("🔍 开始解析日志文件...")
        data = visualizer.parse_log_file(log_file_path)
        
        if len(data['epochs']) == 0:
            print("❌ 未找到有效的训练数据！")
            return
        
        print(f"✅ 成功解析 {len(data['epochs'])} 个epoch的数据")
        
        # 写入TensorBoard
        visualizer.write_to_tensorboard(data)
        
        # 创建摘要统计
        visualizer.create_summary_statistics(data)
        
    finally:
        # 确保关闭写入器
        visualizer.close()

if __name__ == "__main__":
    main()
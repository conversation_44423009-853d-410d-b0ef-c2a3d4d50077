"""
多层次循环一致性损失

基于CycleGAN的循环一致性概念，扩展为多层次的循环约束：
1. 特征级循环一致性 - 特征空间的重构一致性
2. 属性级循环一致性 - 属性空间的预测一致性  
3. 语义级循环一致性 - 语义距离的保持一致性

核心创新：
- 多层次循环约束确保生成质量
- 自适应权重平衡各级别损失
- 支持渐进式训练策略
"""

import tensorflow as tf
from tensorflow.keras.losses import Loss
import numpy as np


class FeatureCycleLoss(tf.keras.layers.Layer):
    """特征级循环一致性损失"""
    
    def __init__(self, loss_type='l1', **kwargs):
        super(FeatureCycleLoss, self).__init__(**kwargs)
        self.loss_type = loss_type
        
    def call(self, real_features, reconstructed_features):
        """
        计算特征级循环损失
        
        Args:
            real_features: 真实特征 [batch_size, feature_dim]
            reconstructed_features: 重构特征 [batch_size, feature_dim]
            
        Returns:
            cycle_loss: 循环一致性损失
        """
        if self.loss_type == 'l1':
            cycle_loss = tf.reduce_mean(tf.abs(real_features - reconstructed_features))
        elif self.loss_type == 'l2':
            cycle_loss = tf.reduce_mean(tf.square(real_features - reconstructed_features))
        elif self.loss_type == 'huber':
            cycle_loss = tf.reduce_mean(
                tf.keras.losses.huber(real_features, reconstructed_features)
            )
        else:
            raise ValueError(f"Unknown loss type: {self.loss_type}")
        
        return cycle_loss


class AttributeCycleLoss(tf.keras.layers.Layer):
    """属性级循环一致性损失"""
    
    def __init__(self, **kwargs):
        super(AttributeCycleLoss, self).__init__(**kwargs)
        
    def call(self, real_attributes, predicted_attributes):
        """
        计算属性级循环损失
        
        Args:
            real_attributes: 真实属性 [batch_size, attribute_dim]
            predicted_attributes: 预测属性 [batch_size, attribute_dim]
            
        Returns:
            attr_cycle_loss: 属性循环损失
        """
        # 使用二元交叉熵损失 (假设属性是二进制的)
        attr_cycle_loss = tf.keras.losses.binary_crossentropy(
            real_attributes, predicted_attributes
        )
        
        return tf.reduce_mean(attr_cycle_loss)


class SemanticCycleLoss(tf.keras.layers.Layer):
    """语义级循环一致性损失"""
    
    def __init__(self, **kwargs):
        super(SemanticCycleLoss, self).__init__(**kwargs)
        
    def call(self, original_semantic_distance, reconstructed_semantic_distance):
        """
        计算语义级循环损失
        
        Args:
            original_semantic_distance: 原始语义距离 [batch_size, 1]
            reconstructed_semantic_distance: 重构语义距离 [batch_size, 1]
            
        Returns:
            semantic_cycle_loss: 语义循环损失
        """
        # 语义距离应该在循环过程中保持一致
        semantic_cycle_loss = tf.reduce_mean(
            tf.square(original_semantic_distance - reconstructed_semantic_distance)
        )
        
        return semantic_cycle_loss


class CycleConsistencyLoss(Loss):
    """
    多层次循环一致性损失
    
    集成特征级、属性级和语义级的循环一致性约束。
    """
    
    def __init__(self,
                 feature_weight=1.0,
                 attribute_weight=0.5,
                 semantic_weight=0.3,
                 feature_loss_type='l1',
                 adaptive_weights=False,
                 name='cycle_consistency_loss',
                 **kwargs):
        """
        初始化多层次循环一致性损失
        
        Args:
            feature_weight: 特征级损失权重
            attribute_weight: 属性级损失权重
            semantic_weight: 语义级损失权重
            feature_loss_type: 特征损失类型 ('l1', 'l2', 'huber')
            adaptive_weights: 是否使用自适应权重
        """
        super().__init__(name=name, **kwargs)
        
        self.feature_weight = feature_weight
        self.attribute_weight = attribute_weight
        self.semantic_weight = semantic_weight
        self.feature_loss_type = feature_loss_type
        self.adaptive_weights = adaptive_weights
        
        # 初始化各级别损失
        self.feature_cycle_loss = FeatureCycleLoss(loss_type=feature_loss_type)
        self.attribute_cycle_loss = AttributeCycleLoss()
        self.semantic_cycle_loss = SemanticCycleLoss()
        
        # 自适应权重参数
        if adaptive_weights:
            self.weight_adaptation_rate = 0.01
            self.loss_history = {
                'feature': [],
                'attribute': [],
                'semantic': []
            }
    
    def compute_adaptive_weights(self, feature_loss, attribute_loss, semantic_loss):
        """
        计算自适应权重
        
        基于各损失的相对大小动态调整权重，确保训练平衡。
        """
        # 记录损失历史
        self.loss_history['feature'].append(feature_loss.numpy())
        self.loss_history['attribute'].append(attribute_loss.numpy())
        self.loss_history['semantic'].append(semantic_loss.numpy())
        
        # 保持历史长度
        max_history = 100
        for key in self.loss_history:
            if len(self.loss_history[key]) > max_history:
                self.loss_history[key] = self.loss_history[key][-max_history:]
        
        # 计算平均损失
        if len(self.loss_history['feature']) > 10:
            avg_feature = np.mean(self.loss_history['feature'][-10:])
            avg_attribute = np.mean(self.loss_history['attribute'][-10:])
            avg_semantic = np.mean(self.loss_history['semantic'][-10:])
            
            # 计算相对权重 (损失越大，权重越小)
            total_loss = avg_feature + avg_attribute + avg_semantic + 1e-8
            
            new_feature_weight = (total_loss - avg_feature) / total_loss
            new_attribute_weight = (total_loss - avg_attribute) / total_loss
            new_semantic_weight = (total_loss - avg_semantic) / total_loss
            
            # 平滑更新权重
            self.feature_weight = (
                (1 - self.weight_adaptation_rate) * self.feature_weight +
                self.weight_adaptation_rate * new_feature_weight
            )
            self.attribute_weight = (
                (1 - self.weight_adaptation_rate) * self.attribute_weight +
                self.weight_adaptation_rate * new_attribute_weight
            )
            self.semantic_weight = (
                (1 - self.weight_adaptation_rate) * self.semantic_weight +
                self.weight_adaptation_rate * new_semantic_weight
            )
    
    def call(self, y_true, y_pred):
        """
        计算多层次循环一致性损失
        
        Args:
            y_true: 真实数据字典 {
                'real_features': 真实特征,
                'real_attributes': 真实属性,
                'original_semantic_distance': 原始语义距离
            }
            y_pred: 预测数据字典 {
                'reconstructed_features': 重构特征,
                'predicted_attributes': 预测属性,
                'reconstructed_semantic_distance': 重构语义距离
            }
            
        Returns:
            total_cycle_loss: 总循环一致性损失
        """
        # 1. 特征级循环损失
        feature_loss = self.feature_cycle_loss(
            y_true['real_features'],
            y_pred['reconstructed_features']
        )
        
        # 2. 属性级循环损失
        attribute_loss = self.attribute_cycle_loss(
            y_true['real_attributes'],
            y_pred['predicted_attributes']
        )
        
        # 3. 语义级循环损失
        semantic_loss = self.semantic_cycle_loss(
            y_true['original_semantic_distance'],
            y_pred['reconstructed_semantic_distance']
        )
        
        # 4. 自适应权重调整
        if self.adaptive_weights:
            self.compute_adaptive_weights(feature_loss, attribute_loss, semantic_loss)
        
        # 5. 计算总损失
        total_cycle_loss = (
            self.feature_weight * feature_loss +
            self.attribute_weight * attribute_loss +
            self.semantic_weight * semantic_loss
        )
        
        return total_cycle_loss
    
    def get_loss_components(self, y_true, y_pred):
        """
        获取各组件损失 (用于监控和调试)
        
        Returns:
            loss_components: 各组件损失字典
        """
        feature_loss = self.feature_cycle_loss(
            y_true['real_features'],
            y_pred['reconstructed_features']
        )
        
        attribute_loss = self.attribute_cycle_loss(
            y_true['real_attributes'],
            y_pred['predicted_attributes']
        )
        
        semantic_loss = self.semantic_cycle_loss(
            y_true['original_semantic_distance'],
            y_pred['reconstructed_semantic_distance']
        )
        
        return {
            'feature_cycle_loss': feature_loss,
            'attribute_cycle_loss': attribute_loss,
            'semantic_cycle_loss': semantic_loss,
            'feature_weight': self.feature_weight,
            'attribute_weight': self.attribute_weight,
            'semantic_weight': self.semantic_weight
        }
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'feature_weight': self.feature_weight,
            'attribute_weight': self.attribute_weight,
            'semantic_weight': self.semantic_weight,
            'feature_loss_type': self.feature_loss_type,
            'adaptive_weights': self.adaptive_weights
        })
        return config


class ProgressiveCycleConsistencyLoss(CycleConsistencyLoss):
    """渐进式循环一致性损失"""
    
    def __init__(self, 
                 warmup_epochs=10,
                 **kwargs):
        super().__init__(**kwargs)
        self.warmup_epochs = warmup_epochs
        self.current_epoch = 0
        
    def set_epoch(self, epoch):
        """设置当前训练轮次"""
        self.current_epoch = epoch
        
    def call(self, y_true, y_pred):
        """
        渐进式损失计算
        
        在训练初期逐渐增加循环一致性约束的强度。
        """
        # 计算基础损失
        base_loss = super().call(y_true, y_pred)
        
        # 渐进式权重调整
        if self.current_epoch < self.warmup_epochs:
            progress_weight = self.current_epoch / self.warmup_epochs
        else:
            progress_weight = 1.0
        
        return base_loss * progress_weight

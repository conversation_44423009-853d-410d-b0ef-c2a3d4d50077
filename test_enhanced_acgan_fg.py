#!/usr/bin/env python3
"""
增强版ACGAN-FG测试脚本
尝试复现文献中89%的E组准确率

主要改进:
1. 增强分类器配置 - 使用更强的网络和参数
2. 优化特征生成策略 - 改进噪声和属性处理
3. 改进属性处理方式 - 使用平均属性向量
4. 添加详细调试信息 - 便于问题诊断
"""

from keras.models import load_model
import tensorflow as tf
import numpy as np
from sklearn import preprocessing
from sklearn.svm import LinearSVC
from sklearn.metrics import accuracy_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier

def scalar_stand(Train_X, Test_X):
    """标准化数据"""
    scalar_train = preprocessing.StandardScaler().fit(Train_X)
    Train_X = scalar_train.transform(Train_X)
    Test_X = scalar_train.transform(Test_X)
    return Train_X, Test_X

def enhanced_feature_generation_and_diagnosis(add_quantity, test_x, test_y, autoencoder, generator, classifier, test_class_indices):
    """
    增强版特征生成和诊断函数
    目标: 复现文献中E组89%的准确率
    """
    
    print(f"🚀 增强版ACGAN-FG测试开始...")
    print(f"🎯 目标: 复现E组89%准确率")
    print(f"📊 测试类别: {test_class_indices}")
    print(f"📈 每类生成样本数: {add_quantity}")

    Labels_train = []
    Labels_test = []
    Generated_feature = []

    samples_per_class = len(test_x) // len(test_class_indices)
    print(f"📊 每类测试样本数: {samples_per_class}")
    
    # 改进的特征生成过程
    for i, class_idx in enumerate(test_class_indices):
        print(f"\n🔄 处理类别 {class_idx} ({i+1}/{len(test_class_indices)})...")
        
        # 改进的属性向量获取 - 使用该类别的平均属性
        start_idx = i * samples_per_class
        end_idx = (i + 1) * samples_per_class
        class_test_labels = test_y[start_idx:end_idx]
        
        # 使用平均属性向量而不是单个样本的属性
        attribute_vector = np.mean(class_test_labels, axis=0)
        print(f"   属性向量形状: {attribute_vector.shape}")
        
        # 为该类别生成多个特征
        attribute = np.tile(attribute_vector, (add_quantity, 1))
        
        # 改进的批量生成策略
        batch_size = 200  # 减小批次大小提高稳定性
        generated_features_batches = []
        
        for batch_start in range(0, add_quantity, batch_size):
            batch_end = min(batch_start + batch_size, add_quantity)
            batch_size_actual = batch_end - batch_start
            
            # 使用更好的噪声分布
            noise_shape = (batch_size_actual, 50, 1)
            noise_batch = tf.random.normal(shape=noise_shape, mean=0.0, stddev=1.0)
            attribute_batch = attribute[batch_start:batch_end]
            
            # 生成特征
            try:
                generated_batch = generator.predict([noise_batch, attribute_batch], verbose=0)
                generated_features_batches.append(generated_batch)
            except Exception as e:
                print(f"   ⚠️ 生成批次失败: {e}")
                generated_batch = np.random.normal(0, 1, (batch_size_actual, 256))
                generated_features_batches.append(generated_batch)
        
        # 合并所有批次
        generated_feature = np.concatenate(generated_features_batches, axis=0)
        Generated_feature.append(generated_feature)

        # 标签处理
        labels_train = np.full((add_quantity, 1), class_idx)
        labels_test = np.full((samples_per_class, 1), class_idx)
        Labels_train.append(labels_train)
        Labels_test.append(labels_test)
    
    # 整合数据
    Generated_feature = np.array(Generated_feature).reshape(-1, 256)
    Labels_train = np.array(Labels_train).reshape(-1, 1)
    Labels_test = np.array(Labels_test).reshape(-1, 1)
    
    print(f"\n📊 数据统计:")
    print(f"   生成特征形状: {Generated_feature.shape}")
    print(f"   训练标签形状: {Labels_train.shape}")
    
    # 提取测试特征
    test_feature, decoded_test = autoencoder(test_x)

    # 获取分类器隐藏层输出
    hidden_ouput_train, predict_attribute_train = classifier(Generated_feature)
    hidden_ouput_test, predict_attribute_test = classifier(test_feature)

    # 特征拼接 - ACGAN-FG的关键步骤
    new_feature_train = np.concatenate((Generated_feature, hidden_ouput_train), axis=1)
    new_feature_test = np.concatenate((test_feature, hidden_ouput_test), axis=1)

    # 数据标准化
    train_X, test_X = scalar_stand(new_feature_train, new_feature_test)
    train_Y = Labels_train
    test_Y = Labels_test

    print(f"\n🚀 开始增强版分类器训练...")
    
    results = {}
    
    # 1. 增强版LinearSVM
    print(f"\n🔧 训练增强版LinearSVM...")
    try:
        classifier_lsvm = LinearSVC(C=1.0, max_iter=2000, dual=False, random_state=42, tol=1e-4)
        classifier_lsvm.fit(train_X, train_Y.ravel())
        Y_pred_lsvm = classifier_lsvm.predict(test_X)
        accuracy_lsvm = accuracy_score(test_Y, Y_pred_lsvm)
        results['LinearSVM'] = accuracy_lsvm
        print(f"   ✅ LinearSVM准确率: {accuracy_lsvm*100:.2f}%")
    except Exception as e:
        print(f"   ❌ LinearSVM训练失败: {e}")
        results['LinearSVM'] = 0.0
    
    # 2. 增强版GaussianNB
    print(f"\n🔧 训练增强版GaussianNB...")
    try:
        classifier_pnb = GaussianNB(var_smoothing=1e-9)
        classifier_pnb.fit(train_X, train_Y.ravel())
        Y_pred_pnb = classifier_pnb.predict(test_X)
        accuracy_pnb = accuracy_score(test_Y, Y_pred_pnb)
        results['GaussianNB'] = accuracy_pnb
        print(f"   ✅ GaussianNB准确率: {accuracy_pnb*100:.2f}%")
    except Exception as e:
        print(f"   ❌ GaussianNB训练失败: {e}")
        results['GaussianNB'] = 0.0
    
    # 3. 增强版RandomForest
    print(f"\n🔧 训练增强版RandomForest...")
    try:
        classifier_nrf = RandomForestClassifier(
            n_estimators=100, max_depth=15, min_samples_split=5, 
            min_samples_leaf=2, n_jobs=-1, random_state=42
        )
        classifier_nrf.fit(train_X, train_Y.ravel())
        Y_pred_nrf = classifier_nrf.predict(test_X)
        accuracy_nrf = accuracy_score(test_Y, Y_pred_nrf)
        results['RandomForest'] = accuracy_nrf
        print(f"   ✅ RandomForest准确率: {accuracy_nrf*100:.2f}%")
    except Exception as e:
        print(f"   ❌ RandomForest训练失败: {e}")
        results['RandomForest'] = 0.0
    
    # 4. 增强版MLP - 重点优化，目标89%
    print(f"\n🔧 训练增强版MLP (目标89%)...")
    try:
        classifier_mlp = MLPClassifier(
            hidden_layer_sizes=(128, 64, 32),  # 更深的网络
            activation='relu',
            solver='adam',                     # 更好的优化器
            alpha=0.001,                       # 适度正则化
            learning_rate_init=0.001,          # 合适的学习率
            max_iter=500,                      # 充分训练
            tol=1e-6,                          # 严格收敛
            early_stopping=True,               # 早停
            validation_fraction=0.1,
            n_iter_no_change=20,
            random_state=42
        )
        classifier_mlp.fit(train_X, train_Y.ravel())
        Y_pred_mlp = classifier_mlp.predict(test_X)
        accuracy_mlp = accuracy_score(test_Y, Y_pred_mlp)
        results['MLP'] = accuracy_mlp
        print(f"   ✅ MLP准确率: {accuracy_mlp*100:.2f}%")
        
        if accuracy_mlp > 0.85:
            print(f"   🎉 MLP接近文献结果!")
        elif accuracy_mlp > 0.70:
            print(f"   ✅ MLP表现良好!")
        else:
            print(f"   ⚠️ MLP仍需改进...")
            
    except Exception as e:
        print(f"   ❌ MLP训练失败: {e}")
        results['MLP'] = 0.0
    
    print(f"\n📊 增强版ACGAN-FG结果总结:")
    print(f"=" * 50)
    for classifier_name, accuracy in results.items():
        print(f"{classifier_name:<15}: {accuracy*100:.2f}%")
    
    # 计算平均准确率
    avg_accuracy = np.mean(list(results.values()))
    print(f"{'平均准确率':<15}: {avg_accuracy*100:.2f}%")
    
    # 与文献对比
    print(f"\n🎯 与文献结果对比 (E组):")
    literature_results = {
        'LinearSVM': 68.67,
        'RandomForest': 80.23, 
        'GaussianNB': 71.44,
        'MLP': 89.06  # 文献中的89.06%
    }
    
    for classifier_name in results.keys():
        if classifier_name in literature_results:
            lit_acc = literature_results[classifier_name]
            our_acc = results[classifier_name] * 100
            diff = our_acc - lit_acc
            status = "✅" if diff > -10 else "⚠️" if diff > -20 else "❌"
            print(f"{classifier_name:<15}: 文献{lit_acc:.1f}% vs 我们{our_acc:.1f}% ({diff:+.1f}%) {status}")
    
    return results['LinearSVM'], results['RandomForest'], results['GaussianNB'], results['MLP']

def main():
    """主函数 - 测试增强版ACGAN-FG"""
    print("🚀 增强版ACGAN-FG测试启动")
    print("🎯 目标: 验证是否能复现文献中E组89%的准确率")
    print("=" * 60)
    
    # 这里需要加载模型和数据
    # 由于需要与ACGAN_FG.py集成，这里只是框架
    print("⚠️ 需要与ACGAN_FG.py集成使用")
    print("💡 请在ACGAN_FG.py中调用enhanced_feature_generation_and_diagnosis函数")

if __name__ == "__main__":
    main()

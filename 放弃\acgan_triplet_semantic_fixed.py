import numpy as np
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, LayerNormalization, BatchNormalization, Flatten, multiply, concatenate, Layer
from tensorflow.keras.models import Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import pytz
import read_data
from tensorflow.keras.losses import mean_squared_error
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import LinearSVC
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import MinMaxScaler
import tensorflow.keras.backend as K

# GPU配置
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    tf.config.set_visible_devices(gpus[0], 'GPU')
    tf.config.experimental.set_memory_growth(gpus[0], True)

def residual_block(x, filters):
    residual = x
    x = Dense(filters)(x)
    x = LeakyReLU(alpha=0.2)(x)
    x = LayerNormalization()(x)
    x = Dense(filters)(x)
    x = LeakyReLU(alpha=0.2)(x)
    x = LayerNormalization()(x)
    if residual.shape[-1] != filters:
        residual = Dense(filters)(residual)
    return tf.keras.layers.add([x, residual])

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        x_reshaped = K.expand_dims(x, axis=1)
        q = self.query(x_reshaped)
        k = self.key(x_reshaped)
        v = self.value(x_reshaped)
        attention_scores = K.batch_dot(q, k, axes=[2, 2])
        attention_probs = K.softmax(attention_scores)
        context = K.batch_dot(attention_probs, v)
        context = K.squeeze(context, axis=1)
        return x + self.gamma * context

def feature_generation_and_diagnosis(sample_number, testdata, test_attributelabel, autoencoder, g, c):
    """
    🔥 基于成功triplet方法的特征生成和诊断函数
    使用相同的零样本学习策略
    """
    # 生成特征用于零样本学习
    target_attributes = test_attributelabel[:3]  # 前3个类别的属性
    
    generated_features_list = []
    generated_labels_list = []
    
    for class_idx, target_attr in enumerate(target_attributes):
        noise = tf.random.normal(shape=(sample_number // len(target_attributes), 50, 1))
        target_attrs = np.tile(target_attr, (sample_number // len(target_attributes), 1))
        
        generated_features = g.predict([noise, target_attrs], verbose=0)
        generated_features_list.append(generated_features)
        generated_labels_list.extend([class_idx] * (sample_number // len(target_attributes)))
    
    # 合并生成的特征
    all_generated_features = np.vstack(generated_features_list)
    all_generated_labels = np.array(generated_labels_list)
    
    # 训练分类器
    classifiers = {
        'lsvm': LinearSVC(random_state=42),
        'rf': RandomForestClassifier(n_estimators=50, random_state=42),
        'nb': GaussianNB(),
        'mlp': MLPClassifier(hidden_layer_sizes=(100,), random_state=42, max_iter=500)
    }
    
    for clf in classifiers.values():
        clf.fit(all_generated_features, all_generated_labels)
    
    # 测试真实数据
    test_features, _ = autoencoder.predict(testdata, verbose=0)
    
    # 创建测试标签
    true_labels = []
    samples_per_class = len(testdata) // 3
    for class_idx in range(3):
        true_labels.extend([class_idx] * samples_per_class)
    true_labels = np.array(true_labels)
    
    # 计算准确率
    accuracies = []
    for clf in classifiers.values():
        predictions = clf.predict(test_features)
        accuracy = accuracy_score(true_labels, predictions)
        accuracies.append(accuracy)
    
    return accuracies

class TripletSemanticZSL:
    """
    基于成功triplet方法的语义增强版本
    核心创新：在稳定的triplet框架上添加语义指导，避免对抗训练的不稳定性
    """
    
    def __init__(self):
        # 基本参数（与成功的triplet方法保持一致）
        self.data_lenth = 52
        self.sample_shape = (self.data_lenth,)
        self.feature_dim = 256
        self.feature_shape = (256,)
        self.num_classes = 15
        self.latent_dim = 50
        self.noise_shape = (self.latent_dim, 1)
        self.n_critic = 1
        self.crl = True
        
        # 损失权重（基于成功的triplet方法）
        self.lambda_cla = 10 
        self.lambda_triplet = 10 
        self.lambda_crl = 0.01 
        
        # 🔥 新增：语义指导权重（适中的权重，避免过度干扰triplet学习）
        self.lambda_semantic = 2.0
        self.lambda_semantic_triplet = 1.0
        
        # 互信息惩罚
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        # 优化器（与成功方法保持一致）
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        
        # 🔥 创建专业TEP语义描述矩阵
        self.fault_descriptions = self.create_professional_tep_descriptions()
        
        # 构建网络（与成功方法保持一致，但分类器输出语义描述）
        self.autoencoder = self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c = self.build_classifier()
        
    def create_professional_tep_descriptions(self):
        """
        使用与ACGAN_SemanticTransfer相同的高质量语义描述矩阵
        """
        professional_descriptions = np.array([
            # 类别0: 正常操作
            [0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0],
            # 类别1: A/C进料比故障 (目标类别)
            [1,1,1,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 1,1,0,0],
            # 类别2: B组分故障  
            [0,1,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 0,1,0,0],
            # 类别3: D进料温度故障
            [0,0,0,1, 0,0,0,0, 1,1,0,0, 0,0,0,0, 0,0,1,0],
            # 类别4: 反应器冷却水故障
            [0,0,0,0, 1,1,0,0, 1,0,1,0, 0,0,0,0, 0,0,0,1],
            # 类别5: 冷凝器冷却水故障
            [0,0,0,0, 0,0,1,0, 0,1,1,0, 0,0,0,0, 0,0,0,1],
            # 类别6: A进料丢失故障 (目标类别)
            [1,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 1,0,1,1],
            # 类别7: C压头压力丢失故障
            [0,0,1,0, 0,0,0,0, 0,0,0,0, 1,1,1,0, 1,0,0,0],
            # 类别8: A/B/C进料组成故障
            [1,1,1,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 0,1,1,0],
            # 类别9: D进料温度故障(随机变化)
            [0,0,0,1, 0,0,0,0, 1,0,0,1, 0,0,0,0, 0,0,1,1],
            # 类别10: C进料温度故障
            [0,0,1,0, 0,0,0,0, 0,1,0,1, 0,0,0,0, 0,0,1,0],
            # 类别11: 反应器冷却水温度故障
            [0,0,0,0, 1,0,1,0, 1,1,0,0, 0,0,0,0, 0,0,0,1],
            # 类别12: 冷凝器冷却水温度故障
            [0,0,0,0, 0,1,1,0, 0,0,1,1, 0,0,0,0, 0,0,0,1],
            # 类别13: 反应动力学故障
            [0,0,0,0, 1,1,1,1, 0,0,0,0, 0,0,0,0, 0,1,0,0],
            # 类别14: 反应器冷却水阀门故障 (目标类别)
            [0,0,0,0, 1,1,1,0, 1,0,0,0, 0,0,1,0, 1,0,0,1]
        ], dtype=np.float32)
        
        print("✅ 创建基于TEP工艺专业知识的语义描述矩阵")
        return professional_descriptions
    
    def build_autoencoder(self):
        """与成功triplet方法完全相同的autoencoder"""
        sample = Input(shape=self.sample_shape)     
        a0 = sample

        # Encoder
        a1 = Dense(100)(a0)
        a1 = LeakyReLU(alpha=0.2)(a1)
        a1 = LayerNormalization()(a1)

        a2 = Dense(200)(a1)
        a2 = LeakyReLU(alpha=0.2)(a2)
        a2 = LayerNormalization()(a2)

        a3 = Dense(256)(a2)
        a3 = LeakyReLU(alpha=0.2)(a3)
        a3 = LayerNormalization()(a3)
        feature = a3

        # Decoder
        a4 = Dense(200)(feature)
        a4 = LeakyReLU(alpha=0.2)(a4)
        a4 = LayerNormalization()(a4)

        a5 = Dense(100)(a4)
        a5 = LeakyReLU(alpha=0.2)(a5)
        a5 = LayerNormalization()(a5)

        a6 = Dense(52)(a5)
        a6 = LeakyReLU(alpha=0.2)(a6)
        a6 = LayerNormalization()(a6)
        output_sample = a6

        autoencoder = Model(sample, [feature, output_sample])
        self.encoder = Model(sample, feature)
        return autoencoder    
        
    def build_discriminator(self):
        """与成功triplet方法完全相同的discriminator"""
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)
        return Model([sample_input, attribute], validity)

    def build_generator(self):
        """与成功triplet方法完全相同的generator"""
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        noise_embedding = Flatten()(noise)
        attribute_embedding = Dense(self.latent_dim)(attribute)
        
        g_input = concatenate([noise_embedding, attribute_embedding])

        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = LayerNormalization()(g1)

        g2 = residual_block(g1, 256) 
        g3 = residual_block(g2, 256) 
        
        g3_attention = SelfAttention()(g3)
        
        generated_feature = Dense(256)(g3_attention)
        generated_feature = BatchNormalization()(generated_feature)

        return Model([noise, attribute], generated_feature)
    
    def build_classifier(self):
        """与成功triplet方法完全相同的classifier"""
        sample = Input(shape=self.feature_shape)

        c0 = sample
        c1 = Dense(100)(c0)
        c1 = LeakyReLU(alpha=0.2)(c1)
        
        c2 = Dense(50)(c1)
        c2 = LeakyReLU(alpha=0.2)(c2)
        hidden_output = c2
               
        c3 = Dense(20, activation="sigmoid")(c2)
        predict_attribute = c3
        
        return Model(sample, [hidden_output, predict_attribute])

    def triplet_loss(self, anchor, positive, negative):
        """与成功triplet方法完全相同的triplet loss"""
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        """与成功triplet方法完全相同的wasserstein loss"""
        return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        """与成功triplet方法完全相同的MI估计"""
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        """与成功triplet方法完全相同的MI惩罚"""
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        """与成功triplet方法完全相同的分类损失"""
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)
        
        mi_penalty = 0    
        if self.bound == True:    
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def cycle_rank_loss(self, anchor, positive, negative):
        """与成功triplet方法完全相同的cycle rank loss"""
        return self.triplet_loss(anchor, positive, negative)
    
    def semantic_consistency_loss(self, features, class_labels):
        """
        🔥 新增：语义一致性损失
        确保特征能够预测出正确的语义描述
        """
        _, predicted_attributes = self.c(features)
        
        # 获取真实的语义描述
        true_semantics = tf.gather(self.fault_descriptions, class_labels)
        
        # L2距离损失
        semantic_loss = tf.reduce_mean(tf.square(predicted_attributes - true_semantics))
        return semantic_loss
    
    def semantic_triplet_loss(self, anchor_features, pos_features, neg_features, 
                            anchor_labels, pos_labels, neg_labels):
        """
        🔥 新增：语义指导的triplet损失
        在特征空间和语义空间同时进行triplet学习
        """
        # 获取语义描述
        anchor_semantics = tf.gather(self.fault_descriptions, anchor_labels)
        pos_semantics = tf.gather(self.fault_descriptions, pos_labels)  
        neg_semantics = tf.gather(self.fault_descriptions, neg_labels)
        
        # 预测的语义特征
        _, anchor_pred = self.c(anchor_features)
        _, pos_pred = self.c(pos_features)
        _, neg_pred = self.c(neg_features)
        
        # 语义空间的triplet损失
        pos_semantic_dist = tf.reduce_mean(tf.square(anchor_pred - pos_semantics))
        neg_semantic_dist = tf.reduce_mean(tf.square(anchor_pred - neg_semantics))
        
        semantic_triplet = tf.maximum(0.0, pos_semantic_dist - neg_semantic_dist + self.triplet_margin)
        return tf.reduce_mean(semantic_triplet)

    def train(self, epochs, batch_size, log_file=None):
        """
        🔥 基于成功triplet方法的训练过程，添加语义指导
        """
        # 北京时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        current_time = datetime.datetime.now(beijing_tz).strftime("%Y%m%d%H%M")
        if log_file is None:
            log_file = f"结果/{current_time}_triplet_semantic.md"
        
        # 创建日志文件
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("# 训练日志 (Triplet+语义指导融合架构)\n\n")
            f.write("## 核心创新点\n")
            f.write("1. 基于成功的triplet方法（88.16%准确率）\n")
            f.write("2. 添加语义指导机制，避免对抗训练的不稳定性\n")
            f.write("3. 在特征空间和语义空间同时进行triplet学习\n")
            f.write("4. 保持triplet方法的稳定性，增强语义迁移能力\n\n")
            f.write(f"**开始时间**: {datetime.datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("---\n\n")
        
        start_time = datetime.datetime.now()
        
        accuracy_list_1 = []
        accuracy_list_2 = []
        accuracy_list_3 = []
        accuracy_list_4 = []
        
        valid = -np.ones((batch_size, 1))
        fake = np.ones((batch_size, 1))
        
        PATH_train = './dataset_train_case1.npz'
        PATH_test = './dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)

        # 🔥 使用strict模式：严格分离seen/unseen类别
        unseen_classes_idx = [0, 5, 13]  # 对应类别1, 6, 14
        all_classes_idx = list(range(15))
        seen_classes_idx = [i for i in all_classes_idx if i not in unseen_classes_idx]
        
        train_X_by_class = {i: train_data[f'training_samples_{i+1}'] for i in all_classes_idx}
        train_Y_by_class = {i: train_data[f'training_attribute_{i+1}'] for i in all_classes_idx}

        # 只使用seen类别进行训练
        all_train_X = np.concatenate([train_X_by_class[i] for i in seen_classes_idx])
        all_train_Y = np.concatenate([train_Y_by_class[i] for i in seen_classes_idx])
        all_train_labels = np.concatenate([np.full(len(train_X_by_class[i]), i) for i in seen_classes_idx])

        # 测试数据使用unseen类别
        test_X = np.concatenate([test_data[f'testing_samples_{i+1}'] for i in [1, 6, 14]])
        test_Y = np.concatenate([test_data[f'testing_attribute_{i+1}'] for i in [1, 6, 14]])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # 重新组织scaled训练数据
        current_pos = 0
        train_X_by_class_scaled = {}
        for i in seen_classes_idx:
            class_len = len(train_X_by_class[i])
            train_X_by_class_scaled[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata = all_train_X
        train_attributelabel = all_train_Y
        train_classlabel = all_train_labels
        
        testdata = test_X
        test_attributelabel = test_Y
       
        num_batches = int(traindata.shape[0] / batch_size)
        
        print("🔄 开始Triplet+语义指导融合训练...")
        print(f"训练数据: {traindata.shape}, 测试数据: {testdata.shape}")
        print(f"见过类别: {seen_classes_idx}, 未见过类别: {unseen_classes_idx}")
               
        for epoch in range(epochs):
            for batch_i in range(num_batches):
                start_i = batch_i * batch_size
                end_i = (batch_i + 1) * batch_size
                
                train_x = traindata[start_i:end_i]
                train_y = train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # 1. Autoencoder和Classifier训练（与成功方法相同）
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                    feature, output_sample = self.autoencoder(train_x)
                    autoencoder_loss = mean_squared_error(train_x, output_sample)      

                    hidden_output_c, predict_attribute_c = self.c(feature)
                    c_loss = self.classification_loss(feature, train_y, hidden_output_c, predict_attribute_c)
                    
                    # 🔥 新增：语义一致性损失
                    semantic_loss = self.semantic_consistency_loss(feature, train_labels)

                    total_ac_loss = autoencoder_loss + c_loss + self.lambda_semantic * semantic_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, 
                                                   self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, 
                                                              self.autoencoder.trainable_weights + self.c.trainable_weights))
                del tape_auto_c

                # 2. Triplet Loss训练（与成功方法相同，但添加语义指导）
                self.autoencoder.trainable = True 
                self.c.trainable = True
                
                # 采样triplets
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []
                pos_labels = []
                neg_labels = []
                
                current_seen_classes = [c for c in seen_classes_idx]

                for label in train_labels:
                    pos_class_samples = train_X_by_class_scaled[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    pos_labels.append(label)
                    
                    neg_class_pool = [c for c in current_seen_classes if c != label]
                    neg_class = np.random.choice(neg_class_pool)
                    neg_class_samples = train_X_by_class_scaled[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])
                    neg_labels.append(neg_class)

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)
                pos_labels = np.array(pos_labels)
                neg_labels = np.array(neg_labels)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    # 原始triplet损失
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)
                    
                    # 🔥 新增：语义指导的triplet损失
                    semantic_triplet_loss = self.semantic_triplet_loss(
                        anchor_features, positive_features, negative_features,
                        train_labels, pos_labels, neg_labels)
                    
                    total_m_loss = m_loss + self.lambda_semantic_triplet * semantic_triplet_loss

                grads_m = tape_m.gradient(total_m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                # 3. Discriminator训练（与成功方法完全相同）
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False

                with tf.GradientTape() as tape_d:
                    noise_d = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature = self.g([noise_d, train_y])
                    real_feature, _ = self.autoencoder(train_x)
                    
                    real_validity = self.d([real_feature, train_y])
                    fake_validity = self.d([fake_feature, train_y])
                    
                    d_loss_real = tf.reduce_mean(valid * real_validity)
                    d_loss_fake = tf.reduce_mean(fake * fake_validity)
                    d_loss = d_loss_real + d_loss_fake

                grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))
                
                # 4. Generator训练（与成功方法相同，但添加语义指导）
                self.d.trainable = False
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                    noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    Fake_feature_g = self.g([noise_g, train_y])
                    Fake_validity_g = self.d([Fake_feature_g, train_y])
                    adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
              
                    fake_hidden_output_g, Fake_classification_g = self.c(Fake_feature_g)
                    classification_loss = self.classification_loss(Fake_feature_g, train_y, 
                                                                 fake_hidden_output_g, Fake_classification_g)
                    
                    # 原始triplet损失
                    g_anchor_features = Fake_feature_g
                    g_positive_features = self.encoder(positive_samples)
                    g_negative_features = self.encoder(negative_samples)
                    triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                    
                    # 🔥 新增：生成特征的语义一致性
                    generated_semantic_loss = self.semantic_consistency_loss(Fake_feature_g, train_labels)
                    
                    cycle_rank_loss = 0
                    if self.crl == True:
                        reconstructed_feature = self.g([noise_g, Fake_classification_g])
                        negative_attributes = np.array([train_Y_by_class[np.random.choice([c for c in current_seen_classes if c != label])][0] for label in train_labels])
                        unsimilar_generated_feature = self.g([noise_g, negative_attributes])
                        cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                           
                    total_loss = (adversarial_loss + 
                                self.lambda_cla * classification_loss + 
                                self.lambda_triplet * triplet_loss_g + 
                                self.lambda_crl * cycle_rank_loss +
                                self.lambda_semantic * generated_semantic_loss)
                          
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                if batch_i % 20 == 0:
                    print(f"[Epoch {epoch}/{epochs}][Batch {batch_i}/{num_batches}]"
                          f"[AE+C: {tf.reduce_mean(total_ac_loss):.4f}]"
                          f"[M: {tf.reduce_mean(total_m_loss):.4f}]"
                          f"[Semantic: {semantic_loss:.4f}]"
                          f"[D: {tf.reduce_mean(d_loss):.4f}]"
                          f"[G: {tf.reduce_mean(total_loss):.4f}] time: {elapsed_time}")
        
            # 每个epoch测试一次
            if epoch % 1 == 0:
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis(
                    2000, testdata, test_attributelabel, self.autoencoder, self.g, self.c)  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                with open(log_file, 'a', encoding='utf-8') as f:
                    f.write(f"[Epoch {epoch}/{epochs}] "
                           f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                           f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                           f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                           f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")

                print(f"[Epoch {epoch}/{epochs}] [Accuracy_lsvm: {max(accuracy_list_1):f}] "
                      f"[Accuracy_nrf: {max(accuracy_list_2):f}] [Accuracy_pnb: {max(accuracy_list_3):f}]"
                      f"[Accuracy_mlp: {max(accuracy_list_4):f}]")
            
        best_accuracy = max([max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)])
        print(f'finished! best_acc:{best_accuracy:.4f}')
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f'\nfinished! best_acc:{best_accuracy:.4f}\n')
            f.write(f"\n## 最终结果\n")
            f.write(f"- LSVM最佳: {max(accuracy_list_1):.6f}\n")
            f.write(f"- RF最佳: {max(accuracy_list_2):.6f}\n")
            f.write(f"- NB最佳: {max(accuracy_list_3):.6f}\n")
            f.write(f"- MLP最佳: {max(accuracy_list_4):.6f}\n")

if __name__ == '__main__':
    print("🚀 开始训练Triplet+语义指导融合网络...")
    print("基于成功的triplet方法（88.16%），添加语义迁移机制")
    print("=" * 80)
    
    gan = TripletSemanticZSL()
    gan.train(epochs=100, batch_size=120)  # 🔥 减少epochs以快速验证
    
    print("=" * 80)
    print("融合训练完成！")
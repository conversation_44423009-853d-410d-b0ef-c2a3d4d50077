import numpy as np
import pandas as pd
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test_hard_negative import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler


# 配置GPU显存按需增长
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(e)


def residual_block(x, units):
    """残差块"""
    shortcut = x
    x = Dense(units, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dense(units)(x)
    
    # 确保shortcut和x的维度匹配
    if K.int_shape(shortcut)[-1] != units:
        shortcut = Dense(units, activation='linear')(shortcut)
    
    x = tf.keras.layers.Add()([x, shortcut])
    x = tf.keras.layers.Activation('relu')(x)
    return x

def transformer_block(x, num_heads=8, ff_dim=128, dropout_rate=0.1):
    """Transformer块"""
    # 多头自注意力
    attention_output = tf.keras.layers.MultiHeadAttention(num_heads=num_heads, key_dim=ff_dim//num_heads)(x, x)
    attention_output = tf.keras.layers.Dropout(dropout_rate)(attention_output)
    x1 = tf.keras.layers.LayerNormalization(epsilon=1e-6)(x + attention_output)
    
    # 前馈网络
    ffn_output = tf.keras.layers.Dense(ff_dim, activation="relu")(x1)
    ffn_output = tf.keras.layers.Dense(K.int_shape(x)[-1])(ffn_output)
    ffn_output = tf.keras.layers.Dropout(dropout_rate)(ffn_output)
    x2 = tf.keras.layers.LayerNormalization(epsilon=1e-6)(x1 + ffn_output)
    
    return x2

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        # input_shape is (batch_size, feature_dim)
        self.feature_dim = input_shape[-1]
        self.query_dense = Dense(self.feature_dim)
        self.key_dense = Dense(self.feature_dim)
        self.value_dense = Dense(self.feature_dim)
        self.output_dense = Dense(self.feature_dim)
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        # Reshape for matrix multiplication
        # Temporarily add a "sequence length" of 1
        # x_reshaped shape: (batch_size, 1, feature_dim)
        x_reshaped = tf.expand_dims(x, axis=1)
        
        # Generate Q, K, V
        Q = self.query_dense(x_reshaped)  # (batch_size, 1, feature_dim)
        K = self.key_dense(x_reshaped)    # (batch_size, 1, feature_dim)
        V = self.value_dense(x_reshaped)  # (batch_size, 1, feature_dim)
        
        # Attention scores
        scores = tf.matmul(Q, K, transpose_b=True)  # (batch_size, 1, 1)
        scores = scores / tf.sqrt(tf.cast(self.feature_dim, tf.float32))
        attention_weights = tf.nn.softmax(scores, axis=-1)
        
        # Apply attention to values
        attended_output = tf.matmul(attention_weights, V)  # (batch_size, 1, feature_dim)
        
        # Remove the temporary sequence dimension
        attended_output = tf.squeeze(attended_output, axis=1)  # (batch_size, feature_dim)
        
        # Apply output projection
        output = self.output_dense(attended_output)
        return output

class Zero_shot():
    def __init__(self):
        self.num_classes = 15  # 已知类别数量
        self.num_attributes = 20  # 每个类别的属性向量维度 (从15修改为20，匹配真实数据)
        self.latent_dim = 128    # 噪声向量维度
        self.feature_dim = 52    # 特征维度 (从 128 修改为 52，匹配真实数据)
        
        # 指定物理设备
        self.device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
        
        # 编译器优化
        self.optimizer_g = tf.keras.optimizers.Adam(learning_rate=0.0001, beta_1=0.5)
        self.optimizer_d = tf.keras.optimizers.Adam(learning_rate=0.0001, beta_1=0.5)
        self.optimizer_c = tf.keras.optimizers.Adam(learning_rate=0.0001, beta_1=0.5)
        self.optimizer_ae = tf.keras.optimizers.Adam(learning_rate=0.0001, beta_1=0.5)
        
        # 损失权重
        self.lambda_triplet = 1.0      # Triplet损失权重
        self.lambda_classification = 1.0  # 分类损失权重
        self.lambda_reconstruction = 0.5   # 重构损失权重
        self.lambda_adversarial = 1.0     # 对抗损失权重
        self.lambda_cycle_rank = 0.1      # 循环排序损失权重
        self.lambda_mi = 0.1             # 互信息损失权重
        
        # 难负例挖掘参数
        self.hard_negative_ratio = 0.7    # 难负例占比
        self.top_k_candidates = 5         # 选择前K个最相似属性作为候选
        self.use_hard_negative = False    # 是否启用难负例挖掘 - 暂时禁用以恢复基线性能
        
        # 预处理器
        self.scaler = MinMaxScaler()
        
        # 构建模型
        self.build_autoencoder()
        self.discriminator = self.build_discriminator()
        self.generator = self.build_generator()
        self.classifier = self.build_classifier()

    def build_autoencoder(self):
        """构建自编码器用于特征重构"""
        # 编码器
        encoder_input = Input(shape=(self.feature_dim,))
        x = Dense(256, activation='relu')(encoder_input)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)
        x = Dense(128, activation='relu')(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)
        encoded = Dense(64, activation='relu', name='encoded')(x)
        
        # 解码器
        x = Dense(128, activation='relu')(encoded)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)
        x = Dense(256, activation='relu')(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)
        decoded = Dense(self.feature_dim, activation='linear', name='decoded')(x)
        
        # 完整的自编码器
        self.autoencoder = Model(encoder_input, decoded, name='autoencoder')
        self.encoder = Model(encoder_input, encoded, name='encoder')
        
        # 编译自编码器
        self.autoencoder.compile(optimizer=self.optimizer_ae, loss='mse')

    def build_discriminator(self):
        """构建判别器"""
        feature_input = Input(shape=(self.feature_dim,))
        
        x = Dense(256)(feature_input)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.3)(x)
        
        x = Dense(128)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.3)(x)
        
        x = Dense(64)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.3)(x)
        
        validity = Dense(1, activation='sigmoid', name='validity')(x)
        
        model = Model(feature_input, validity, name='discriminator')
        return model

    def build_generator(self):
        """构建生成器"""
        noise_input = Input(shape=(self.latent_dim,))
        attribute_input = Input(shape=(self.num_attributes,))
        
        # 连接噪声和属性
        combined_input = concatenate([noise_input, attribute_input])
        
        x = Dense(256, activation='relu')(combined_input)
        x = BatchNormalization()(x)
        x = Dense(512, activation='relu')(x)
        x = BatchNormalization()(x)
        
        # 使用自注意力机制
        x = SelfAttention()(x)
        
        x = Dense(256, activation='relu')(x)
        x = BatchNormalization()(x)
        
        # 生成特征
        generated_feature = Dense(self.feature_dim, activation='linear', name='generated_feature')(x)
        
        model = Model([noise_input, attribute_input], generated_feature, name='generator')
        return model

    def build_classifier(self):
        """构建分类器（属性预测器）"""
        feature_input = Input(shape=(self.feature_dim,))
        
        x = Dense(256, activation='relu')(feature_input)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)
        
        x = Dense(128, activation='relu')(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)
        
        # 预测属性向量
        predicted_attributes = Dense(self.num_attributes, activation='linear', name='predicted_attributes')(x)
        
        model = Model(feature_input, predicted_attributes, name='classifier')
        return model

    def compute_attribute_similarity(self, anchor_attr, all_attributes):
        """计算属性相似度，用于难负例挖掘"""
        # 使用余弦相似度
        anchor_attr = tf.nn.l2_normalize(anchor_attr, axis=-1)
        all_attributes = tf.nn.l2_normalize(all_attributes, axis=-1)
        
        # 计算余弦相似度
        similarities = tf.linalg.matmul(anchor_attr, all_attributes, transpose_b=True)
        return similarities

    def hard_negative_sampling(self, anchor_labels, all_attributes):
        """难负例采样策略"""
        batch_size = tf.shape(anchor_labels)[0]
        
        # 获取anchor的属性向量
        anchor_attributes = tf.gather(all_attributes, anchor_labels)
        
        # 计算与所有属性的相似度
        similarities = self.compute_attribute_similarity(anchor_attributes, all_attributes)
        
        # 创建mask，排除自身类别
        anchor_mask = tf.one_hot(anchor_labels, depth=self.num_classes)
        similarities = similarities - anchor_mask * 1e9  # 将自身类别的相似度设为负无穷
        
        # 获取最相似的K个候选类别
        _, top_k_indices = tf.nn.top_k(similarities, k=self.top_k_candidates)
        
        # 随机从top_k中选择负样本
        random_indices = tf.random.uniform([batch_size], maxval=self.top_k_candidates, dtype=tf.int32)
        batch_indices = tf.range(batch_size)
        gather_indices = tf.stack([batch_indices, random_indices], axis=1)
        hard_negatives = tf.gather_nd(top_k_indices, gather_indices)
        
        return hard_negatives

    def triplet_loss(self, anchor, positive, negative, margin=1.0):
        """改进的Triplet Loss with Hard Negative Mining"""
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=1)
        
        basic_loss = tf.maximum(0.0, margin + pos_dist - neg_dist)
        return tf.reduce_mean(basic_loss)

    def wasserstein_loss(self, y_true, y_pred):
        """Wasserstein损失"""
        return tf.reduce_mean(y_true * y_pred)

    def build_mi_estimator(self):
        """构建互信息估计网络"""
        if not hasattr(self, 'mi_estimator'):
            input_dim = self.feature_dim + self.latent_dim
            mi_input = Input(shape=(input_dim,))
            x = Dense(128, activation='relu')(mi_input)
            x = Dense(64, activation='relu')(x)
            output = Dense(1)(x)
            self.mi_estimator = Model(mi_input, output, name='mi_estimator')
        return self.mi_estimator

    def estimate_mutual_information(self, x, z):
        """估计互信息 - 使用固定的网络"""
        mi_net = self.build_mi_estimator()
        
        # 连接x和z
        joint = concatenate([x, z])
        marginal_z = tf.random.shuffle(z)
        marginal = concatenate([x, marginal_z])
        
        t_joint = mi_net(joint)
        t_marginal = mi_net(marginal)
        
        # 使用简化的MI估计
        mi_estimate = tf.reduce_mean(t_joint) - tf.reduce_mean(t_marginal)
        return mi_estimate

    def mi_penalty_loss(self, x, z):
        """互信息惩罚损失"""
        mi = self.estimate_mutual_information(x, z)
        return -mi  # 负号因为我们想要最小化互信息

    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        """分类损失"""
        # 使用预测的属性向量计算损失
        classification_loss = tf.reduce_mean(tf.square(hidden_output - pred_attribute))
        
        # 添加属性一致性损失
        true_attributes = tf.gather(self.attribute_matrix, y_true)
        attribute_consistency_loss = tf.reduce_mean(tf.square(pred_attribute - true_attributes))
        
        return classification_loss + 0.5 * attribute_consistency_loss

    def cycle_rank_loss(self, original_features, generated_features, reconstructed_features):
        """真正的循环一致性损失"""
        # 确保 original -> generated -> reconstructed 的循环一致性
        # 1. 重构损失：generated特征重构后应该接近原始特征
        reconstruction_loss = tf.reduce_mean(tf.square(original_features - reconstructed_features))
        
        # 2. 特征一致性损失：生成特征应该在特征空间中保持一致性
        feature_consistency_loss = tf.reduce_mean(tf.square(original_features - generated_features))
        
        # 3. 循环一致性：确保从属性空间到特征空间再回到属性空间的一致性
        pred_attrs_original = self.classifier(original_features)
        pred_attrs_generated = self.classifier(generated_features)
        pred_attrs_reconstructed = self.classifier(reconstructed_features)
        
        # 预测属性的一致性
        attr_cycle_loss = (tf.reduce_mean(tf.square(pred_attrs_original - pred_attrs_generated)) + 
                          tf.reduce_mean(tf.square(pred_attrs_original - pred_attrs_reconstructed)))
        
        # 总的循环损失
        total_cycle_loss = reconstruction_loss + 0.5 * feature_consistency_loss + 0.3 * attr_cycle_loss
        
        return total_cycle_loss

    def train(self, epochs, batch_size, log_file=None, test_class_indices=None):
        """训练模型"""
        print("开始训练...")
        
        # 数据加载 - 使用正确的函数名和参数
        train_data, train_label, train_attr_label, test_data, test_label, test_attr_label, test_attr_matrix, train_attr_matrix = read_data.creat_dataset(test_class_indices)
        
        # 数据预处理
        train_data = self.scaler.fit_transform(train_data)
        test_data = self.scaler.transform(test_data)
        
        # 转换为tensorflow张量
        train_data = tf.constant(train_data, dtype=tf.float32)
        train_label = tf.constant(train_label, dtype=tf.int32)
        
        # 使用真实的属性矩阵（从Excel文件加载）
        # 需要读取完整的属性矩阵，包含所有15个类别
        attribute_matrix_df = pd.read_excel('./attribute_matrix.xlsx', index_col='no')
        self.attribute_matrix = tf.constant(attribute_matrix_df.values.astype(np.float32))
        
        # 训练循环
        dataset = tf.data.Dataset.from_tensor_slices((train_data, train_label))
        dataset = dataset.shuffle(1000).batch(batch_size)
        
        for epoch in range(epochs):
            epoch_d_loss = 0
            epoch_g_loss = 0
            epoch_c_loss = 0
            epoch_ae_loss = 0
            num_batches = 0
            
            for batch_features, batch_labels in dataset:
                with tf.device(self.device):
                    # 修正batch_labels的形状，从 (batch_size, 1) 到 (batch_size,)
                    batch_labels = tf.squeeze(batch_labels, axis=-1)

                    # 训练自编码器
                    with tf.GradientTape() as ae_tape:
                        reconstructed = self.autoencoder(batch_features)
                        ae_loss = tf.reduce_mean(tf.square(batch_features - reconstructed))
                    
                    ae_gradients = ae_tape.gradient(ae_loss, self.autoencoder.trainable_variables)
                    self.optimizer_ae.apply_gradients(zip(ae_gradients, self.autoencoder.trainable_variables))
                    
                    # 生成噪声和获取属性
                    noise = tf.random.normal([tf.shape(batch_features)[0], self.latent_dim])
                    batch_attributes = tf.gather(self.attribute_matrix, batch_labels)
                    
                    # 训练判别器
                    with tf.GradientTape() as d_tape:
                        generated_features = self.generator([noise, batch_attributes])
                        
                        real_validity = self.discriminator(batch_features)
                        fake_validity = self.discriminator(generated_features)
                        
                        d_loss_real = tf.reduce_mean(tf.square(real_validity - 1))
                        d_loss_fake = tf.reduce_mean(tf.square(fake_validity))
                        d_loss = (d_loss_real + d_loss_fake) / 2
                    
                    d_gradients = d_tape.gradient(d_loss, self.discriminator.trainable_variables)
                    self.optimizer_d.apply_gradients(zip(d_gradients, self.discriminator.trainable_variables))
                    
                    # 训练生成器和分类器
                    with tf.GradientTape() as g_tape, tf.GradientTape() as c_tape:
                        generated_features = self.generator([noise, batch_attributes])
                        
                        # 对抗损失
                        fake_validity = self.discriminator(generated_features)
                        g_loss_adv = tf.reduce_mean(tf.square(fake_validity - 1))
                        
                        # 分类损失
                        pred_attributes = self.classifier(generated_features)
                        c_loss = tf.reduce_mean(tf.square(pred_attributes - batch_attributes))
                        
                        # Triplet损失（使用简单随机负例采样）
                        # 传统随机负例采样
                        negative_labels = tf.random.shuffle(batch_labels)
                        # 确保负样本与锚点不同
                        same_mask = tf.equal(batch_labels, negative_labels)
                        # 如果相同，则随机选择一个不同的类别
                        different_labels = tf.random.uniform([tf.shape(batch_labels)[0]], maxval=self.num_classes, dtype=tf.int32)
                        negative_labels = tf.where(same_mask, different_labels, negative_labels)
                        
                        negative_attributes = tf.gather(self.attribute_matrix, negative_labels)
                        negative_noise = tf.random.normal([tf.shape(batch_features)[0], self.latent_dim])
                        negative_features = self.generator([negative_noise, negative_attributes])
                        
                        # 计算triplet损失
                        triplet_loss = self.triplet_loss(
                            anchor=batch_features,  # 真实特征作为anchor
                            positive=generated_features,  # 生成的同类特征作为positive
                            negative=negative_features  # 生成的负类特征作为negative
                        )
                        
                        # 循环一致性损失
                        reconstructed_features = self.autoencoder(generated_features)
                        cycle_loss = self.cycle_rank_loss(
                            original_features=batch_features,
                            generated_features=generated_features,
                            reconstructed_features=reconstructed_features
                        )
                        
                        # 互信息损失
                        mi_loss = self.mi_penalty_loss(generated_features, noise)
                        
                        # 总损失
                        g_loss = (self.lambda_adversarial * g_loss_adv + 
                                 self.lambda_triplet * triplet_loss + 
                                 self.lambda_cycle_rank * cycle_loss +
                                 self.lambda_mi * mi_loss)
                    
                    # 更新生成器
                    g_gradients = g_tape.gradient(g_loss, self.generator.trainable_variables)
                    self.optimizer_g.apply_gradients(zip(g_gradients, self.generator.trainable_variables))
                    
                    # 更新分类器
                    c_gradients = c_tape.gradient(c_loss, self.classifier.trainable_variables)
                    self.optimizer_c.apply_gradients(zip(c_gradients, self.classifier.trainable_variables))
                
                # 累计损失
                epoch_d_loss += d_loss.numpy()
                epoch_g_loss += g_loss.numpy()
                epoch_c_loss += c_loss.numpy()
                epoch_ae_loss += ae_loss.numpy()
                num_batches += 1
            
            # 平均损失
            epoch_d_loss /= num_batches
            epoch_g_loss /= num_batches
            epoch_c_loss /= num_batches
            epoch_ae_loss /= num_batches
            
            # 每个epoch都评估准确率
            # 生成测试特征并评估
            test_accuracies = self.evaluate_zero_shot(
                test_data=test_data, 
                test_labels=test_label, 
                test_class_indices=test_class_indices
            )
            
            log_message = (f"[Epoch {epoch}/{epochs}] "
                         f"[D_loss: {epoch_d_loss:.6f}] "
                         f"[G_loss: {epoch_g_loss:.6f}] "
                         f"[C_loss: {epoch_c_loss:.6f}] "
                         f"[AE_loss: {epoch_ae_loss:.6f}] "
                         f"[Accuracy_lsvm: {test_accuracies['lsvm']:.6f}] "
                         f"[Accuracy_nrf: {test_accuracies['nrf']:.6f}] "
                         f"[Accuracy_pnb: {test_accuracies['pnb']:.6f}]"
                         f"[Accuracy_mlp: {test_accuracies['mlp']:.6f}]")
            
            print(log_message)
            
            if log_file:
                with open(log_file, 'a') as f:
                    f.write(log_message + '\n')

    def evaluate_zero_shot(self, test_data, test_labels, test_class_indices):
        """零样本学习评估"""
        # 使用测试函数评估 - 正确的参数传递
        accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis(
            add_quantity=100,  # 每个类别生成100个样本
            test_x=test_data,
            test_y=test_labels,  # 使用从creat_dataset返回的测试标签
            autoencoder=self.autoencoder,
            generator=self.generator,
            classifier=self.classifier,
            test_class_indices=test_class_indices
        )
        
        # 返回字典格式的结果
        return {
            'lsvm': accuracy_lsvm,
            'nrf': accuracy_nrf, 
            'pnb': accuracy_pnb,
            'mlp': accuracy_mlp
        }

def main(group_name='E', test_class_indices=None):
    """主函数"""
    # 定义不同组别的测试类别
    group_configs = {
        'A': [1, 5, 14],
        'B': [4, 7, 10], 
        'C': [8, 11, 12],
        'D': [2, 3, 15],
        'E': [6, 9, 13]
    }
    
    # 如果没有指定测试类别，则使用组别配置
    if test_class_indices is None:
        if group_name in group_configs:
            test_class_indices = group_configs[group_name]
        else:
            print(f"未知组别: {group_name}，使用默认组别E")
            test_class_indices = group_configs['E']
            group_name = 'E'
    
    # 创建日志文件名
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    log_file = f"结果/{timestamp}_triplet_hard_negative_Group{group_name}.md"
    
    # 确保结果目录存在
    os.makedirs("结果", exist_ok=True)
    
    # 写入日志头部
    with open(log_file, 'w') as f:
        f.write(f"# 训练日志 (Triplet架构 + 难负例挖掘 - Group {group_name})\n\n")
        f.write(f"**开始时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**实验组别**: Group {group_name}\n")
        f.write(f"**测试类别**: {test_class_indices}\n")
        f.write(f"**改进策略**: 难负例挖掘 (Hard Negative Mining)\n")
        f.write(f"**难负例比例**: 70% (动态调整)\n")
        f.write(f"**属性相似度**: 余弦相似度\n")
        f.write(f"**候选负例数**: 5\n\n")
        f.write("---\n\n")
    
    # 创建并训练模型
    model = Zero_shot()
    
    print("开始训练带有难负例挖掘的Triplet ACGAN模型...")
    print(f"实验组别: Group {group_name}")
    print(f"测试类别: {test_class_indices}")
    print(f"日志文件: {log_file}")
    
    # 开始训练
    model.train(
        epochs=500, 
        batch_size=64, 
        log_file=log_file,
        test_class_indices=test_class_indices
    )

if __name__ == "__main__":
    import sys
    
    # 支持命令行参数选择组别
    if len(sys.argv) > 1:
        group_name = sys.argv[1].upper()
        print(f"使用命令行参数指定的组别: {group_name}")
    else:
        # 默认使用组别E（表现最差的组别）
        group_name = 'E'
        print("使用默认组别: E (可通过命令行参数指定其他组别，如: python acgan_triplet_hard_negative.py A)")
    
    main(group_name=group_name)
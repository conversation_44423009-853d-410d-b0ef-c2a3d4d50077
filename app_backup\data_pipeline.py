import numpy as np
import tensorflow as tf
from sklearn.preprocessing import MinMaxScaler
import time
import os

class OptimizedDataPipeline:
    """
    优化的数据流水线，用于提高GPU利用率
    实现高效的数据加载、预处理、批处理和预取
    """
    
    def __init__(self, use_mixed_precision=True, prefetch_buffer="auto"):
        """
        初始化数据流水线
        
        参数:
        use_mixed_precision: 是否启用混合精度训练
        prefetch_buffer: 预取缓冲区大小，默认为自动
        """
        self.scaler = MinMaxScaler()
        # 修复prefetch_buffer参数
        self.prefetch_buffer = tf.data.AUTOTUNE if prefetch_buffer == "auto" else prefetch_buffer
        
        # 配置混合精度
        if use_mixed_precision:
            policy = tf.keras.mixed_precision.Policy('mixed_float16')
            tf.keras.mixed_precision.set_global_policy(policy)
            print("已启用混合精度训练 (float16)")
    
    def prepare_data_adaptive(self, train_data, test_data, target_group, batch_size=512, shuffle_buffer=10000):
        """
        为自适应模型准备优化的数据流水线
        
        参数:
        train_data: 训练数据npz文件
        test_data: 测试数据npz文件
        target_group: 目标组别 (A, B, C, D, E)
        batch_size: 批处理大小
        shuffle_buffer: 打乱缓冲区大小
        
        返回:
        包含优化数据流水线和相关元数据的字典
        """
        print(f"为目标组别 {target_group} 准备优化数据流水线...")
        start_time = time.time()
        
        # 组别配置
        group_configs = {
            'A': [1, 6, 14],  # 测试类别: [1, 6, 14] 
            'B': [4, 7, 10],  # 测试类别: [4, 7, 10]
            'C': [8, 11, 12], # 测试类别: [8, 11, 12]
            'D': [2, 3, 5],   # 测试类别: [2, 3, 5]
            'E': [9, 13, 15], # 测试类别: [9, 13, 15]
        }
        
        test_class_indices = group_configs[target_group]
        all_class_indices = list(range(1, 16))  # 1-15
        seen_class_indices = [i for i in all_class_indices if i not in test_class_indices]
        
        # 处理训练数据
        train_X_by_class = {i: train_data[f'training_samples_{i}'] for i in seen_class_indices}
        train_Y_by_class = {i: train_data[f'training_attribute_{i}'] for i in seen_class_indices}
        
        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])
        
        # 处理测试数据
        test_X_by_class = {i: test_data[f'testing_samples_{i}'] for i in test_class_indices}
        test_Y_by_class = {i: test_data[f'testing_attribute_{i}'] for i in test_class_indices}
        
        test_X = np.concatenate([v for k, v in test_X_by_class.items()])
        test_Y = np.concatenate([v for k, v in test_Y_by_class.items()])
        test_classlabel = np.concatenate([np.full(len(v), k) for k, v in test_X_by_class.items()])
        
        # 数据标准化
        print("正在进行数据标准化...")
        all_train_X = self.scaler.fit_transform(all_train_X)
        test_X = self.scaler.transform(test_X)
        
        # 重新组织缩放后的训练数据
        current_pos = 0
        for i in seen_class_indices:
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len
        
        # 创建高效的训练数据集
        print(f"创建优化的训练数据流水线，批处理大小: {batch_size}")
        train_dataset = tf.data.Dataset.from_tensor_slices(
            (all_train_X.astype(np.float32), all_train_Y.astype(np.float32), all_train_labels)
        )
        
        # 应用优化: 缓存、打乱、批处理、预取
        train_dataset = train_dataset.cache()
        train_dataset = train_dataset.shuffle(buffer_size=shuffle_buffer)
        train_dataset = train_dataset.batch(batch_size)
        train_dataset = train_dataset.prefetch(self.prefetch_buffer)
        
        # 创建测试数据集
        test_dataset = tf.data.Dataset.from_tensor_slices(
            (test_X.astype(np.float32), test_Y.astype(np.float32))
        ).batch(batch_size).prefetch(self.prefetch_buffer)
        
        elapsed_time = time.time() - start_time
        print(f"数据流水线准备完成! 用时: {elapsed_time:.2f}秒")
        print(f"训练样本数: {len(all_train_X)}, 测试样本数: {len(test_X)}")
        print(f"训练批次数: {len(all_train_X) // batch_size + (1 if len(all_train_X) % batch_size > 0 else 0)}")
        
        return {
            'train_dataset': train_dataset,
            'test_dataset': test_dataset,
            'train_X_by_class': train_X_by_class,
            'train_Y_by_class': train_Y_by_class,
            'test_X': test_X,
            'test_Y': test_Y,
            'seen_classes': seen_class_indices,
            'unseen_classes': test_class_indices,
            'scaler': self.scaler,
            'all_train_X': all_train_X,
            'all_train_Y': all_train_Y,
            'all_train_labels': all_train_labels,
            'batch_size': batch_size
        }
    
    def prepare_data_triplet(self, train_data, test_data, test_class_indices, batch_size=512, shuffle_buffer=10000):
        """
        为Triplet和Hard Negative Mining模型准备优化的数据流水线
        
        参数:
        train_data: 训练数据npz文件
        test_data: 测试数据npz文件
        test_class_indices: 测试类别索引列表
        batch_size: 批处理大小
        shuffle_buffer: 打乱缓冲区大小
        
        返回:
        包含优化数据流水线和相关元数据的字典
        """
        print(f"为测试类别 {test_class_indices} 准备优化数据流水线...")
        start_time = time.time()
        
        all_class_indices = list(range(1, 16))  # 1-15
        seen_class_indices = [i for i in all_class_indices if i not in test_class_indices]
        
        # 处理训练数据
        train_X_by_class = {i: train_data[f'training_samples_{i}'] for i in seen_class_indices}
        train_Y_by_class = {i: train_data[f'training_attribute_{i}'] for i in seen_class_indices}
        
        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])
        
        # 处理测试数据
        test_X = np.concatenate([test_data[f'testing_samples_{i}'] for i in test_class_indices])
        test_Y = np.concatenate([test_data[f'testing_attribute_{i}'] for i in test_class_indices])
        
        # 数据标准化
        print("正在进行数据标准化...")
        all_train_X = self.scaler.fit_transform(all_train_X)
        test_X = self.scaler.transform(test_X)
        
        # 重新组织缩放后的训练数据
        current_pos = 0
        for i in seen_class_indices:
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len
        
        # 创建高效的训练数据集
        print(f"创建优化的训练数据流水线，批处理大小: {batch_size}")
        train_dataset = tf.data.Dataset.from_tensor_slices(
            (all_train_X.astype(np.float32), all_train_Y.astype(np.float32), all_train_labels)
        )
        
        # 应用优化: 缓存、打乱、批处理、预取
        train_dataset = train_dataset.cache()
        train_dataset = train_dataset.shuffle(buffer_size=shuffle_buffer)
        train_dataset = train_dataset.batch(batch_size)
        train_dataset = train_dataset.prefetch(self.prefetch_buffer)
        
        # 创建测试数据集
        test_dataset = tf.data.Dataset.from_tensor_slices(
            (test_X.astype(np.float32), test_Y.astype(np.float32))
        ).batch(batch_size).prefetch(self.prefetch_buffer)
        
        elapsed_time = time.time() - start_time
        print(f"数据流水线准备完成! 用时: {elapsed_time:.2f}秒")
        print(f"训练样本数: {len(all_train_X)}, 测试样本数: {len(test_X)}")
        print(f"训练批次数: {len(all_train_X) // batch_size + (1 if len(all_train_X) % batch_size > 0 else 0)}")
        
        return {
            'train_dataset': train_dataset,
            'test_dataset': test_dataset,
            'train_X_by_class': train_X_by_class,
            'train_Y_by_class': train_Y_by_class,
            'test_X': test_X,
            'test_Y': test_Y,
            'seen_classes': seen_class_indices,
            'unseen_classes': test_class_indices,
            'scaler': self.scaler,
            'all_train_X': all_train_X,
            'all_train_Y': all_train_Y,
            'all_train_labels': all_train_labels,
            'batch_size': batch_size
        }

def monitor_gpu():
    """
    监控并打印GPU使用情况
    """
    try:
        gpus = tf.config.list_physical_devices('GPU')
        if not gpus:
            print("未检测到GPU设备")
            return None
        
        print(f"检测到 {len(gpus)} 个GPU设备:")
        for i, gpu in enumerate(gpus):
            print(f"  GPU {i}: {gpu.name}")
        
        # 打印当前GPU内存使用情况
        for i, gpu in enumerate(gpus):
            try:
                memory_info = tf.config.experimental.get_memory_info(f'GPU:{i}')
                total_memory = memory_info['current'] + memory_info['peak']
                used_memory = memory_info['current']
                print(f"  GPU {i} 内存使用: {used_memory/(1024**2):.2f}MB / {total_memory/(1024**2):.2f}MB")
            except:
                print(f"  无法获取GPU {i} 的内存信息")
        
        return gpus
    except Exception as e:
        print(f"监控GPU时出错: {str(e)}")
        return None

def check_docker_gpu_config():
    """
    检查Docker容器中的GPU配置
    """
    try:
        # 检查环境变量
        nvidia_visible_devices = os.environ.get('NVIDIA_VISIBLE_DEVICES', 'none')
        print(f"NVIDIA_VISIBLE_DEVICES: {nvidia_visible_devices}")
        
        # 检查nvidia-smi是否可用
        import subprocess
        try:
            result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            print("nvidia-smi 可用，输出前五行:")
            print('\n'.join(result.stdout.split('\n')[:5]))
        except:
            print("nvidia-smi 不可用")
        
        # 检查TensorFlow能否识别GPU
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"TensorFlow 识别到 {len(gpus)} 个GPU:")
            for i, gpu in enumerate(gpus):
                print(f"  {i}: {gpu}")
                
            # 尝试设置内存增长
            for gpu in gpus:
                try:
                    tf.config.experimental.set_memory_growth(gpu, True)
                    print(f"已为 {gpu} 启用内存增长")
                except RuntimeError as e:
                    print(f"无法为 {gpu} 设置内存增长: {e}")
        else:
            print("TensorFlow 未识别到任何GPU")
            
        # 检查CUDA版本
        print(f"TensorFlow内置CUDA版本: {tf.sysconfig.get_build_info()['cuda_version']}")
        print(f"cuDNN版本: {tf.sysconfig.get_build_info()['cudnn_version']}")
            
        return True
    except Exception as e:
        print(f"检查Docker GPU配置时出错: {str(e)}")
        return False

# 简单测试函数
if __name__ == "__main__":
    print("测试数据流水线优化模块...")
    pipeline = OptimizedDataPipeline(use_mixed_precision=True)
    
    # 检查GPU配置
    check_docker_gpu_config()
    
    # 监控GPU
    monitor_gpu()
    
    print("测试完成!") 
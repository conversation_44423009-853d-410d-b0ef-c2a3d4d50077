#!/usr/bin/env python3
"""
测试零样本学习修复
验证每个组别的特征提取器是否正确训练
"""

import os
import argparse
from data_processing import TEPAttributes

def test_split_configuration():
    """测试组别配置"""
    print("🔍 测试组别配置")
    print("=" * 50)
    
    for split_name in ['A', 'B', 'C', 'D', 'E']:
        split_info = TEPAttributes.SPLITS[split_name]
        seen_faults = split_info['seen']
        unseen_faults = split_info['unseen']
        
        print(f"\n组别 {split_name}:")
        print(f"  已见类别: {seen_faults} ({len(seen_faults)}个)")
        print(f"  未见类别: {unseen_faults} ({len(unseen_faults)}个)")
        
        # 检查是否有重叠
        overlap = set(seen_faults) & set(unseen_faults)
        if overlap:
            print(f"  ❌ 错误: 有重叠类别 {overlap}")
        else:
            print(f"  ✅ 正确: 无重叠类别")
        
        # 检查是否覆盖所有类别
        all_faults = set(seen_faults) | set(unseen_faults)
        expected = set(range(1, 16))
        if all_faults == expected:
            print(f"  ✅ 正确: 覆盖所有15个类别")
        else:
            missing = expected - all_faults
            extra = all_faults - expected
            print(f"  ❌ 错误: 缺失{missing}, 多余{extra}")

def test_feature_extractor_files():
    """测试特征提取器文件"""
    print("\n🔍 测试特征提取器文件")
    print("=" * 50)
    
    for split_name in ['A', 'B', 'C', 'D', 'E']:
        fe_path = f'best_feature_extractor_{split_name}.pth'
        if os.path.exists(fe_path):
            print(f"✅ {fe_path} 存在")
        else:
            print(f"❌ {fe_path} 不存在")
            print(f"   💡 运行: python train_high_quality_feature_extractor.py --split {split_name}")

def test_data_loading(split_name='A'):
    """测试数据加载"""
    print(f"\n🔍 测试组别 {split_name} 的数据加载")
    print("=" * 50)
    
    try:
        from data_processing import TEPDataProcessor
        
        processor = TEPDataProcessor(split_name=split_name)
        data_dict = processor.prepare_zsl_data(split_name)
        
        print(f"✅ 数据加载成功")
        print(f"  已见类别: {data_dict['seen_fault_ids']}")
        print(f"  未见类别: {data_dict['unseen_fault_ids']}")
        print(f"  训练属性矩阵: {data_dict['training_attributes'].shape}")
        print(f"  评估属性矩阵: {data_dict['all_attributes_for_eval'].shape}")
        
        # 验证训练属性只包含已见类别
        seen_attrs = data_dict['seen_attributes']
        training_attrs = data_dict['training_attributes']
        
        if training_attrs.shape == seen_attrs.shape:
            print(f"  ✅ 训练属性矩阵大小正确")
        else:
            print(f"  ❌ 训练属性矩阵大小错误")
            
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='测试零样本学习修复')
    parser.add_argument('--split', type=str, default='A', choices=['A', 'B', 'C', 'D', 'E'],
                       help='测试组别')
    args = parser.parse_args()
    
    print("🚀 零样本学习修复测试")
    print("=" * 60)
    
    # 测试组别配置
    test_split_configuration()
    
    # 测试特征提取器文件
    test_feature_extractor_files()
    
    # 测试数据加载
    test_data_loading(args.split)
    
    print(f"\n🎯 测试完成")
    print(f"💡 如果特征提取器文件不存在，请先训练:")
    print(f"   python train_high_quality_feature_extractor.py --split {args.split}")
    print(f"💡 然后测试VAEGAN:")
    print(f"   python simple_training_no_delete.py --split {args.split}")

if __name__ == "__main__":
    main()

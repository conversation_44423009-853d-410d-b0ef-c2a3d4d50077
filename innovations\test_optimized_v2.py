#!/usr/bin/env python3
"""
🔥 优化版本2 - 基于测试结果的精细调优

基于第一次测试结果的改进：
1. ✅ 语义损失已修复 (0.0000 → 0.1721)
2. 🔧 梯度裁剪阈值: 1.0 → 0.1 (更严格)
3. 🔧 域转换噪声: 0.1 → 0.01 (减少干扰)
4. 🔧 属性一致性权重: 1.0 → 0.1 (避免压制)

目标：生成器损失 59,723 → <1,000
"""

import os
import sys
import torch
import argparse
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')

from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer


def main():
    parser = argparse.ArgumentParser(description='🔥 优化版本2 - 精细调优')
    parser.add_argument('--group', type=str, choices=['A', 'B', 'C', 'D', 'E'], 
                       default='A', help='数据分组选择')
    parser.add_argument('--epochs', type=int, default=50, 
                       help='训练轮次 (默认50)')
    parser.add_argument('--batch_size', type=int, default=32, 
                       help='批次大小')
    parser.add_argument('--device', type=str, default='cuda', 
                       help='设备选择')
    
    args = parser.parse_args()
    
    print(f"""
🔥 优化版本2 - 精细调优测试
=====================================
📊 数据分组: {args.group}
🔄 训练轮次: {args.epochs}
📦 批次大小: {args.batch_size}
💻 设备: {args.device}
⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 基于测试结果的优化:
✅ 语义损失: 已修复 (0.0000 → 0.1721)
🔧 梯度裁剪阈值: 1.0 → 0.1 (更严格控制)
🔧 域转换噪声比例: 0.1 → 0.01 (减少干扰)
🔧 属性一致性权重: 1.0 → 0.1 (避免压制其他损失)

📈 预期改进:
- 生成器损失: 59,723 → <1,000
- 训练稳定性: 进一步改善
- 准确率: 58.19% → 65%+
=====================================
    """)
    
    try:
        # 初始化训练器
        trainer = EnhancedASDCGANTrainer(
            device=args.device,
            batch_size=args.batch_size,
            learning_rate_g=0.0002,
            learning_rate_d=0.0004
        )
        
        # 验证优化后的配置
        print(f"""
✅ 优化后配置验证:
- 梯度裁剪阈值: {trainer.max_grad_norm} (优化: 1.0→0.1)
- 域转换噪声比例: {trainer.noise_ratio} (优化: 0.1→0.01)
- 属性一致性权重: {trainer.attribute_consistency_weight} (优化: 1.0→0.1)
- 语义损失权重: {trainer.semantic_distance_weight}
- 循环损失权重: {trainer.cycle_consistency_weight}
        """)
        
        # 加载数据
        print(f"📊 加载TEP数据集 (分组 {args.group})...")
        trainer.load_data(split_group=args.group)
        
        # 开始训练
        print(f"🚀 开始优化训练...")
        trainer.train_enhanced(epochs=args.epochs)
        
        # 获取结果
        best_accuracy = max(trainer.history.get('best_accuracy', [0])) if trainer.history.get('best_accuracy') else 0
        final_g_loss = trainer.history.get('g_loss', [0])[-1] if trainer.history.get('g_loss') else 0
        final_semantic_loss = trainer.history.get('semantic_loss', [0])[-1] if trainer.history.get('semantic_loss') else 0
        
        # 分析改进效果
        print(f"""
🎉 优化版本2测试完成！
=====================================
📊 最终结果:
- 最佳准确率: {best_accuracy:.2f}%
- 最终生成器损失: {final_g_loss:.1f}
- 最终语义损失: {final_semantic_loss:.4f}

📈 优化效果对比:
版本1 → 版本2:
- 生成器损失: 59,723 → {final_g_loss:.1f} ({'✅ 显著改善' if final_g_loss < 10000 else '🔧 继续优化' if final_g_loss < 30000 else '⚠️ 需要调整'})
- 准确率: 58.19% → {best_accuracy:.2f}% ({'✅ 有提升' if best_accuracy > 58.19 else '⚠️ 需要改进'})
- 语义损失: 保持修复状态 ({'✅ 正常' if final_semantic_loss > 0.01 else '⚠️ 异常'})

🎯 与目标对比:
- 生成器损失目标: <1,000 ({'✅ 达成' if final_g_loss < 1000 else f'🔧 还需降低 {final_g_loss/1000:.1f}倍'})
- 准确率目标: 75-85% ({'✅ 达成' if best_accuracy > 75 else f'🔧 还需提升 {75-best_accuracy:.1f}%'})
=====================================
        """)
        
        # 给出具体建议
        if final_g_loss < 1000:
            print("🎯 优秀！生成器损失已达到目标范围！")
        elif final_g_loss < 10000:
            print("✅ 良好！损失显著降低，继续这个方向优化！")
            print("💡 建议：进一步降低梯度裁剪阈值到0.05")
        elif final_g_loss < 30000:
            print("🔧 进步！有改善但还需优化")
            print("💡 建议：")
            print("1. 梯度裁剪阈值降到0.05")
            print("2. 检查域转换是否引入过多复杂性")
        else:
            print("⚠️  损失仍然过高，需要重新审视方法")
            print("💡 建议：")
            print("1. 暂时禁用域转换，回到随机噪声")
            print("2. 进一步降低所有损失权重")
            
        if best_accuracy > 65:
            print("🎯 准确率有明显提升！")
        else:
            print("🔧 准确率还需要进一步优化")
            print("💡 建议：增加训练轮次到100-200")
            
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        raise


if __name__ == "__main__":
    main()

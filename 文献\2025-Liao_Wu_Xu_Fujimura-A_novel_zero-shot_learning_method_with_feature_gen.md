# A Novel Zero-Shot Learning Method With Feature Generation for Intelligent Fault Diagnosis

<PERSON><PERSON><PERSON> \( {}^{\circledR } \) , Like Wu \( {}^{\circledR } \) , <PERSON><PERSON> \( {}^{\circledR } \) , and <PERSON><PERSON><PERSON> \( {}^{\circledR } \) , Member, IEEE

Abstract-In the traditional data-driven fault diagnosis task, gathering training samples for all possible fault classes poses a significant challenge. There are many target faults that cannot be collected in advance, which potentially limiting the performance of fault diagnosis models. Zero-shot learning has emerged as a viable solution to this problem. However, it often encounters the issue of domain shift. In this article, an attribute-consistent generative adversarial network with feature generation (ACGAN-FG) is proposed for zero-shot fault diagnosis. ACGAN-FG introduces a discriminative classifier and a binary comparator to construct the attribute-consistent losses, which can alleviate the issue that the generated features may deviate from real faults. To generate fault features with greater diversity and enhance the robustness of the proposed model, a cycle rank loss is designed. Besides, this method also introduces feature concatenation to build new training data and testing data. This concatenation can transform the generated features into more discriminative representation for further fault diagnosis. The effectiveness of the proposed method is validated on two cases for fault diagnosis purpose. The results also indicate that the proposed method is outperforms other state-of-art zero-shot fault diagnosis methods.

Index Terms-Attribute consistency, autoencoder, domain shift, feature generation, generative adversarial network, zero-shot fault diagnosis.

## I. INTRODUCTION

BAULT diagnosis plays critical role in industrial production especially mechanical equipment [1]. Any minor faults can lead to machine breakdowns, substantial financial losses, and potentially result in injuries or fatalities. Thus, fault diagnosis plays a crucial role in machine health management by connecting machine monitoring data to its health status. This technology not only minimizes unnecessary expenses but also provides essential insights for manufacturing planning [2].

Intelligent fault diagnosis employs artificial intelligence technologies to automate the fault diagnosis process and enhance the accuracy [3]. The intelligent here means many intelligent artificial techniques have been applied, i.e.,.., expert systems [4], fuzzy logics, deep learning (DL), and artificial neural networks [5]. Among the various methods, DL architectures have garnered significant interest because they adeptly represent data through multiple stacked nonlinear hidden layers. Many research based on DL has shown cutting-edge performance in classification tasks [6]. Among these, autoencoders, a well-known DL model, are extensively utilized in fault diagnosis through feature construction. Besides, Shao et al. [7] introduced a deep belief network for fault diagnosis in induction motors, with vibration signals directly as import. Although the above research yielded positive outcomes in specific settings, it is crucial to highlight that these studies were performed under abundant data conditions. However, the acquisition of faulty data is the main constrain of intelligent fault diagnosis in the real-word application. Several factors contribute to the limited availability of data in the industry.

1) High-security and high-stability requirements of turbo-machinery, such as steam turbines and wind turbines, prevent them from being allowed to run to failure [8].

2) Bearings, as essential supporting components in rotating machinery, may take months or even years to run to failure. Therefore, collecting data from these components could spend a prolonged waiting period.

3) Another method to collect faulty samples is through fault injection experiments, but sometimes these could be expensive. Therefore, there is a practical interest in studying zero-shot fault diagnosis, whose definition will be presented in Section II.

The first zero-shot diagnosis (ZSD) was proposed by Feng and Zhao [9]. In their pioneering work, seen faults refer to fault categories with both sufficient training samples and fault description provided by experts, while unseen faults refer to fault categories without training samples but with fault description. They established a mapping model between samples and attributes. By transferring this mapping from seen to unseen faults, they were able to classify unseen faults by predicting attributes. This study served as an inspiration for subsequent ZSD research. Gao et al. [10] introduced a zero-shot learning approach utilizing contractive stacked autoencoders to tackle with faults from unseen but related working loads without previous samples. Natsumeda and Yairi [11] proposed a concrete partial autoencoder to select features beneficial for both seen and unseen faults through attribute prediction and duplicated feature penalty. Chen et al. [12] designed a hierarchical constrained network that predicted the attributes layer-by-layer to explore the hierarchical characteristics among attributes. Other studies have developed fault attribute learning models to address the challenge of defining difficult fault attributes, which reduces labor costs and enhances the automation of diagnostic models as described in [13] and [14]. For example, recently, generative models, particularly generative adversarial networks (GANs), have been increasingly utilized in zero-shot learning (ZSL) frameworks due to their exceptional ability to generate synthetic data [15]. For instance, Xian et al. [16] employed GAN to construct feature generating networks to generate CNN features for unseen label figures. In the area of fault diagnosis, the GAN-based zero-shot learning models have already been explored. The main concept behind this method is to generate samples of unseen fault classes by attributes. For instance, Zhuo et al. [17] introduced triplet loss in traditional GAN to synthesis discriminative fault samples. Shao et al. [18] designed an attribute regressors in Wasserstein Generative Adversarial Networks (SRWGAN) to constrain the attribute consistency, which leads to a high diagnostic accuracy. Focus on various datasets or scenarios, Yan et al. [19] introduced a differentiable search method with GAN architecture to attain a good classification result.

---

Received 1 October 2024; revised 18 November 2024; accepted 6 December 2024. Date of publication 27 January 2025; date of current version 4 April 2025. Paper no. TII-24-5093. (Corresponding author: Wenjie Liao.)

The authors are with the Graduate School of Information, Production, and Systems, Waseda University, Kitakyushu 808-0135, Japan (e-mail: <EMAIL>; <EMAIL>; shxu@toki .waseda.jp; <EMAIL>).

Digital Object Identifier 10.1109/TII.2025.3526478

---

Overall, the current ZSL methods have made preliminary progress in tackling the issue of fault diagnosis in scenarios where specific fault data for some classes is absent. However, some important issues still need to be addressed. There is always a bias phenomenon demonstrated by the attribute embedding based-zero shot diagnosis model, which tends to predict the attributes of unseen class samples with bias towards the attributes learned in the seen class samples. This phenomenon is commonly referred to as domain shift [20]. Domain shift also occurred in the generative-based zero-shot diagnostic model, where the generated samples exhibited little correspondence with their intended attributes. Besides, current generative-based zero-shot diagnostic models extract features from each sample and then generate new samples using these features and corresponding attributes. Subsequently, fault diagnosis is conducted on a combination of raw samples and generated data from unseen labels. However, this process struggles to ensure that the generated samples consistently align with the corresponding features, which may exacerbate domain shift and subsequently reduce the accuracy of fault diagnosis.

To solve the mentioned issues, this article proposes a zero-shot diagnosis model with feature generation. More specifically, a-GAN based model is adopted to projects fault attribute representation into a feature vector. Unlike other generative zero-shot model, the generative model synthesis feature vectors rather than fault samples and the generated features are also taken as input into a discriminative classifier and a binary comparator for further attribute-consistent tasks. For the supplementary classification task, it imposes a new constraint for generator on learning the attribute to feature projection function. Due to the minor differences among various fault attributes, constraints derived from the classifier can ensure consistency between the generated features and attribute representations. For the comparison task, the generated feature is fed into the comparator along with real features from different classes to measure the similarity of them. This additional comparison task effectively prevents the overlap of the generated feature space with the real feature space from similar attributes. Furthermore, in order to generate fault features with increased diversity and enhance the robustness of the proposed model, a cycle rank loss is designed in this study. The attributes predicted by the classifier are fed back into the generator to reconstruct the fault features, which are utilized to calculate the rank value among all generated features of other attributes through the comparator. Both the attribute-consistent loss and cycle rank loss function as regularizers within the model. These mechanisms compel the generator to produce diverse and attribute-consistent features, enhancing the overall efficacy and reliability of the model. Additionally, the output from the hidden layer of the discriminative classifier is extracted and merged with the generated features, enhancing the attribute representation within these features. This research assumes that if the generated features closely align with their attributes, the domain shift problem can be effectively addressed. Besides, fault diagnosis is executed under the discriminative generated feature vector can improve the accuracy of unseen data. In summary, the contribution of this article can be summarized as follows.

1) In response to the challenge posed by zero-shot fault diagnosis, a generative model ACGAN-FG is proposed based on GAN to generate feature vectors, which is highly consistent with fault attributes. To achieve the knowledge transfer from seen class faults to unseen class faults within the generative model, an attribute analysis is introduced to define the fault attributes for each class based on fault description.

2) By designing the attribute-consistent losses from a discriminative classifier and a binary comparator, the issue that the generated features may deviate from real features of unseen samples is alleviated. To prevent the generated features from collapsing into the same mode, a cycle rank loss based on feature reconstruction is introduced. This approach ensures diversity of generated features and enhance the robustness of proposed model. Besides, the concatenation operation makes generated features more discriminative before fault diagnosis.

3) Extensive experimental results on two different cases have showed the effectiveness of proposed method and its superiority among other state-of-the-art methods.

## II. RELATED WORK

## A. Problem Definition

To clarify the problem, the definition of zero-shot fault diagnosis task is described as follows. During training, a subset of labeled fault samples is available, which are represented by \( {T}_{s} = \) \( \left\{  {\left( {{x}_{i}^{s},{y}_{i}^{s}}\right) , i = 1,2,\ldots ,{N}_{s}}\right\} \) , where \( {x}_{i}^{s} \) denotes a fault sample and \( {y}_{i}^{s} \in  {Y}_{s} = \{ 1,2,\ldots , S\} \) represents the corresponding label named as seen classes. In the training process, there are also some unavailable samples \( {T}_{u} = \left\{  {\left( {{x}_{i}^{u},{y}_{i}^{u}}\right) , i = 1,2,\ldots ,{N}_{u}}\right\} \) , where \( {x}_{i}^{u} \) is an unavailable sample and \( {y}_{i}^{u} \) belongs to unseen classes \( {Y}_{u} = \{ S + 1, S + 2,\ldots , S + U\} \) . Note that \( {Y}_{s} \cap \) \( {Y}_{u} = \varnothing \) , which means there is no intersection between seen classes and unseen classes. However, these two classes are connected within a shared semantic space \( C \) , which serves as the knowledge bridge connecting the seen and unseen classes. This shared semantic space is also named as attribute space, which is constructed by expert knowledge. Each fault category \( y \in  {Y}_{s} \cup  {Y}_{u} \) can be defined by a fault attribute \( c\left( y\right)  \in  C \) . So, the training set with seen samples can be described as \( {T}_{s} = \) \( \left\{  {\left( {{x}_{i}^{s}, c\left( {y}_{i}^{s}\right) }\right) , i = 1,2,\ldots ,{N}_{s}}\right\} \) . The objective of zero-shot fault diagnosis is to accurately determine the label \( y \in  {Y}_{s} \cup  {Y}_{u} \) given \( {D}_{\text{test }} = {x}_{i}^{s} \cup  {x}_{i}^{u} \) based on fault attribute. Specially, in generative zero-shot fault diagnosis, it is necessary to synthesize samples of unseen classes by leveraging their attributes. To provide a clear and intuitive explanation, the comparison between traditional and zero-shot fault diagnosis is illustrated in Fig. 1.

![bo_d1hv6nf7aajc7382q90g_2_149_183_686_264_0.jpg](images/bo_d1hv6nf7aajc7382q90g_2_149_183_686_264_0.jpg)

Fig. 1. Comparison between traditional and zero-shot fault diagnosis model. (a) Traditional. (b) Zero-shot.

![bo_d1hv6nf7aajc7382q90g_2_116_529_743_522_0.jpg](images/bo_d1hv6nf7aajc7382q90g_2_116_529_743_522_0.jpg)

Fig. 2. Illustration of the domain shift issue. In (a), the unseen features, and in (b), the attributes, are biased towards the seen classes after projection.

## B. Domain Shift

Current ZSL approaches typically involve learning a mapping between the sample space and attribute space, enabling comparisons of both seen and unseen samples within a common space. However, these methods often face challenges related to domain shift [20]. Seen and unseen classes can be considered as distinct domains. In some cases of shared attributes, the two domain overlaps, in other cases, the sample features corresponding to the same attributes can differ significantly due to the inherent differences between the domains. As a result, when projections are learned only from seen classes, they tend to be biased or shifted for unseen classes. As depicted in Fig. 2, this bias occurs regardless of whether the mapping is from sample space to attribute space or vice versa. Training the projection solely on data from seen classes leads the model to emphasize characteristics of the seen classes. Consequently, during the classification of unseen test samples, the model is likely to misclassify them as seen samples, causing domain shift. Specifically, in generative zero-shot fault diagnosis, the generator, when trained with samples only from seen classes, will produce samples for unseen classes that significantly deviate from the actual samples.

## III. METHOD

This section introduces the method proposed in this article. Fig. 3 illustrates the overview of this methodology, whose details are described in the following three steps.

![bo_d1hv6nf7aajc7382q90g_2_898_190_736_711_0.jpg](images/bo_d1hv6nf7aajc7382q90g_2_898_190_736_711_0.jpg)

Fig. 3. Flowchart of proposed method.

1) Data Preprocessing: After obtaining various fault samples collected from industrial scenes, the dataset will be divided into training set and testing set. Next, a fault attribute analysis is conducted across all potential fault classes to define the attributes of each class of fault. After that, the fault samples will be input to a trained feature extractor based on a stacked autoencoder to extract the corresponding feature vectors.

2) Model Training and Feature Generation: A generative model is trained on the projection from attribute space to feature space with seen class samples. Then, the model is utilized to synthesis the feature vectors of unseen class fault samples.

3) Feature Concatenation and Fault diagnosis: The generated features of unseen classes and the raw features of seen classes are input to the trained classifier, and the output of hidden layer is extracted and merged with the feature vectors. Finally, the new features will serve as the training set for a classification model, which is designed to recognize faults in the unseen classes.

## A. Data Preprocessing

After constructing the training set and testing set from various fault samples collected in industrial scenes, feature extraction and attribute analysis are executed in both sets. Since only fault samples of seen class are available, attribute analysis is performed on fault description as the auxiliary information to define the fault features, i.e., fault attributes \( c = \left\lbrack  {{c}_{1},{c}_{2},\ldots ,{c}_{k}}\right\rbrack \) , \( {c}_{i} = 1 \) when it indicates the presence of the \( i \) th attribute, whereas \( {c}_{i} = 0 \) means the attribute is missing. The key task in this step is mapping the attribute distribution of each class to its corresponding feature distribution, which can be learned by the proposed model. Then, the training set \( D = \left\{  {\left( {{x}_{i}^{s},{a}_{i}^{s}}\right) , i = 1,\ldots ,{N}_{s}}\right\} \) is constructed, where \( {x}_{i}^{s} \) denotes the fault sample and \( {a}_{i}^{s} \) denotes the defined fault attributes.

![bo_d1hv6nf7aajc7382q90g_3_217_186_527_418_0.jpg](images/bo_d1hv6nf7aajc7382q90g_3_217_186_527_418_0.jpg)

Fig. 4. Block scheme of proposed architecture.

For the feature extraction part, a stacked autoencoder is utilized to extract the feature vectors of fault samples, as illustrated in Fig. 4. The real samples from seen classes \( {x}_{i}^{s} \) , and corresponding attribute \( {c}_{i}^{s} \) are input into the encoder block, compressed to a feature vector \( \widetilde{x} \) . The optimization function of stacked AE is as follows:

\[
{\mathcal{L}}_{\mathrm{{SAE}}} = \operatorname{MSE}\left( {O\left( {E\left( {{x}_{i}^{s},{c}_{i}^{s}}\right) }\right) \parallel {x}_{i}^{s}}\right)  \tag{1}
\]

where MSE represents mean squared error loss, \( E \) is the encoder block of stacked AE, O is the decoder block.

## B. Model Training and Feature Generation

After the training of the stacked autoencoder based extractor, we can obtain the feature vectors \( \widetilde{x} \) for each fault sample. Then, a random noise \( z \sim  N\left( {0,1}\right) \) will be feed into the generator together with the attributes \( c \) to generate synthetic features, as illustrated in Fig. 4. While this alone does not guarantee the authenticity of the generated features, there is a need to introduce a discriminator, which works against with the generator to form a GAN structure. For the GAN part, the generated feature \( G\left( {z, c}\right) \) and its corresponding attribute \( c \) is fed into the discriminator together with extracted features \( \widetilde{x} \) from real samples to output a validity. Moreover, the Wasserstein distance [21] is to be utilized to reduce the risk of gradient disappearance, and the gradient penalty approach [21] is utilized to meet the Lipschitz constraint. The optimization function for the discriminator has the following form:

\[
\mathop{\min }\limits_{D}{L}_{\text{WGAN }}^{D}\left( {E, D, G}\right)  = {E}_{z \sim  N\left( {0,1}\right) }\left\lbrack  {D\left( {G\left( {z, c}\right) , c}\right) }\right\rbrack
\]

\[
- {E}_{x \sim  {P}_{x}}\left\lbrack  {D\left( {E\left( x\right) , c}\right) }\right\rbrack   + \lambda {E}_{{\widetilde{x}}^{\prime } \sim  {P}_{{\widetilde{x}}^{\prime }}}\left\lbrack  {\left( {\begin{Vmatrix}{\nabla }_{{\widetilde{x}}^{\prime }}\left( D\left( {\widetilde{x}}^{\prime }, c\right) \right) \end{Vmatrix}}_{2} - 1\right) }^{2}\right\rbrack
\]

(2)

where \( {\widetilde{x}}^{\prime } \) represents a random feature that lies between the real feature \( \widetilde{x} \) and generated feature \( G\left( {z, c}\right) \) typically computed as

\[
{\widetilde{x}}^{\prime } = \varepsilon \widetilde{x} + \left( {1 - \varepsilon }\right) G\left( {z, c}\right)  \tag{3}
\]

where \( \varepsilon \) is a random value in \( \left\lbrack  {0,1}\right\rbrack \) . And \( {\nabla }_{{\widetilde{x}}^{\prime }}\left( {D\left( {\widetilde{x}}^{\prime }\right) }\right) \) represents the gradient of the output of discriminator with respect to \( {\widetilde{x}}^{\prime }.\lambda \) donated to a factor reregulate the impact of gradient penalty.

For the generator, the adversarial loss is calculated by the validity of the discriminator. The following equation defines the loss function:

\[
{\mathcal{L}}_{\text{ADV }}^{G}\left( {D, G}\right)  =  - {E}_{z \sim  N\left( {0,1}\right) }\left\lbrack  {D\left( {G\left( {z, c}\right) }\right) }\right\rbrack  . \tag{4}
\]

However, the generated features exhibit unpredictable shift, mainly because the model has not been trained with samples of unseen classes. To alleviate the domain shift, a discriminative classifier is added to encourage the generator to construct features that are consistent with attributes. For each generated feature \( G\left( {z, c}\right) \) , their attribute will be predicted by the classifier, and the predicted attribute are compared with their true attributes, forcing the generator to generate features with attributes matching the true attributes. The classifier is defined as \( C\left( \right) \) , which is built by fully connected network and its loss function is formulated as follows:

\[
{\mathcal{L}}_{\mathrm{{CLS}}} = \frac{1}{N}\mathop{\sum }\limits_{{i = 0}}^{N}\Delta \left( {C\left( {{\widetilde{x}}_{i}^{\prime };{\theta }_{C}}\right) ,{c}_{{y}_{i}}}\right)  \tag{5}
\]

where \( \Delta \) represents the loss incurred from the difference between the predicted attribute \( C\left( {{\widetilde{x}}_{i}^{\prime };{\theta }_{C}}\right) \) and the true attribute \( {c}_{{y}_{i}} \) , and \( {\theta }_{C} \) is the parameters of the neural networks.

In order to make the information in the last hidden layer \( a \) of the classifier less redundant feature information and to facilitate feature concatenation in the next step, we constrain dependence on input fault feature \( {\widetilde{x}}_{i}^{\prime } \) and \( a \) from an information theory perspective. More specifically, we aim to keep the mutual information between \( {\widetilde{x}}_{i}^{\prime } \) and \( a \) below an upper bound \( {b}_{u} \) , as defined in (6). This ensures that the redundancy information from \( {\widetilde{x}}_{i}^{\prime } \) will be eliminated in \( a \) [22]

\[
\mathrm{I}\left( {{\widetilde{x}}_{i}^{\prime };a}\right)  < {b}_{u}. \tag{6}
\]

For more convenience in computing the mutual information, we follow the method suggested by Alemi et al. [23] and use a variational upper bound as an alternative

\[
I\left( {{\widetilde{x}}_{i}^{\prime };a}\right)  \leq  {\mathbb{E}}_{p\left( x\right) }\left\lbrack  {{D}_{KL}\left\lbrack  {{p}_{M}\left( {a \mid  x}\right) \parallel r\left( a\right) }\right\rbrack  }\right\rbrack   < {b}_{u} \tag{7}
\]

where \( r\left( a\right) \) represents the variational approximation to the marginal distribution of \( a,{p}_{M}\left( {a \mid  x}\right) \) denotes the conditional distribution of \( a \) conditioned on the fault feature \( x \) . By using the Lagrange multiplier method to transform (7) into an unconstrained form, the objective function of the classifier is

\[
{\mathcal{L}}_{\mathrm{{CLS}}} = \frac{1}{N}\mathop{\sum }\limits_{{i = 0}}^{N}\Delta \left( {C\left( {{\widetilde{x}}_{i}^{\prime };{\theta }_{C}}\right) ,{c}_{{y}_{i}}}\right)
\]

\[
+ \lambda \left\{  {{\mathbb{E}}_{p\left( x\right) }\left\lbrack  {{D}_{KL}\left\lbrack  {{p}_{M}\left( {a \mid  x}\right) \parallel r\left( a\right) }\right\rbrack  }\right\rbrack   - b}\right\}  . \tag{8}
\]

Besides, many attribute vectors from different classes are close to each other, easily making the generated features belong to other similar attributes. To prevent the overlap of the generated feature space with the real feature space from other attributes, a binary comparator is introduced. For each generated feature \( {\widetilde{x}}_{i}^{\prime } = G\left( {z, c}\right) \) , it is taken as input into the comparator \( M\left( \right) \) together with a real feature comes from different classes to assess the similarity of two inputted features. The comparator is constructed by fully connected network and its optimization function during the training process is defined as follows:

\[
{\mathcal{L}}_{M} = \frac{1}{N}\mathop{\sum }\limits_{{i = 0}}^{N}\mathop{\sum }\limits_{{j = i}}^{N}\Delta \left( {M\left( {{\widetilde{x}}_{i},{\widetilde{x}}_{j};{\theta }_{M}}\right) ,{b}_{ij}}\right)  \tag{9}
\]

where \( \Delta \) is the loss incurred from the difference between the predicted similarity and ground truth. \( {\widetilde{x}}_{i} \) and \( {\widetilde{x}}_{j} \) represent two sample features extracted by feature extractor, they can originate from the same class or from different classes. \( {\theta }_{M} \) is the parameter of the networks and \( {b}_{ij} \) represents ground truth of similarity prediction, \( {b}_{ij} = 1 \) when \( {\widetilde{x}}_{i} \) and \( {\widetilde{x}}_{j} \) originate from the same class, whereas \( {b}_{ij} = 0 \) when \( {\widetilde{x}}_{i} \) and \( {\widetilde{x}}_{j} \) comes from different classes. Therefore, a higher similarity implies that it is more probable that the two features belong to the same class.

Algorithm 1: ACGAN-FG Optimization

---

Input: Training set

\( \left\{  {\left( {{x}_{i}^{s},{c}_{{y}_{i}^{s}}}\right) , i = 1,\ldots , N, s = 1,\ldots , M}\right\} \)

Initialize the network parameters \( {\theta }_{E},{\theta }_{D},{\theta }_{G},{\theta }_{C} \) and \( {\theta }_{M} \)

repeat

for \( s = 1 \) to \( m \) do

	for \( i = 1 \) to \( n \) do

		randomly sample a data \( \left( {{x}_{i}^{s},{c}_{{y}_{i}^{s}}}\right) \)

		\( \overline{{x}_{i}^{s}} = {E}_{O}\left( {{E}_{E}\left( {{x}_{i}^{s},{c}_{{y}_{i}^{s}}}\right) }\right) \)

		\( {\theta }_{E} \leftarrow   - {\nabla }_{{\theta }_{E}}{L}_{E} = \operatorname{MSE}\left( {\overline{{x}_{i}^{s}}\parallel {x}_{i}^{s}}\right) \)

		randomly sample a noise \( z \sim  N\left( {0,1}\right) \)

			\( {L}_{WGAN}^{D}\left( {E, D, G}\right)  = {E}_{z \sim  N\left( {0,1}\right) }\left\lbrack  {D\left( {G\left( {z,{c}_{{y}_{i}^{s}}}\right) ,{c}_{{y}_{i}^{s}}}\right) }\right\rbrack \)

			\( - {E}_{{x}_{i}^{s} \sim  {P}_{{x}_{i}^{s}}}\left\lbrack  {D\left( {E\left( {x}_{i}^{s}\right) ,{c}_{{y}_{i}^{s}}}\right) }\right\rbrack \)

			\( + \lambda {E}_{{\widetilde{x}}^{\prime } \sim  {P}_{{\widetilde{x}}^{\prime }}}\left( {\left. {\begin{Vmatrix}{\nabla }_{{\widetilde{x}}^{\prime }}\left( D\left( {\widetilde{x}}^{\prime },{c}_{{y}_{i}^{s}}\right) \right) \end{Vmatrix}}_{2} - 1\right) }^{2}\right\rbrack \)

		\( {\theta }_{D} \leftarrow   - {\nabla }_{{\theta }_{D}}{L}_{WGAN}^{D} \)

		\( {L}_{CLS} = \Delta \left( {C\left( {E\left( {x}_{i}^{s}\right) ;{\theta }_{C}}\right) ,{c}_{{y}_{i}^{s}}}\right) \)

		\( {\theta }_{C} \leftarrow   - {\nabla }_{{\theta }_{C}}{L}_{CLS} \)

		\( {L}_{M} = \Delta \left( {\widetilde{M}\left( {E\left( {x}_{i}^{s}\right) , E\left( {x}_{j}^{s}\right) ;{\theta }_{M}}\right) ,{b}_{ij}}\right) \)

		\( {\theta }_{M} \leftarrow   - {\nabla }_{{\theta }_{M}}{L}_{M} \)

		randomly sample a noise \( z \sim  N\left( {0,1}\right) \)

		\( {L}_{CRL}^{G} = \)

		\( {L}_{WGAN}^{G}\left( {C, D, G, M}\right)  = \)

		\( {L}_{ADV}^{G}\left( {D, G}\right)  + {\lambda }_{1}{L}_{ac}^{G} + {\lambda }_{2}{L}_{CRL}^{G} \)

		\( {\theta }_{G} \leftarrow   - {\nabla }_{{\theta }_{G}}{L}_{WGAN}^{G} \)

	end for

end for

---

For the constraint of generator, the generated feature \( {\widetilde{x}}_{i}^{\prime } \) is fed into the trained comparator along with real feature \( {\widetilde{x}}_{i} \) from the same class and \( {\widetilde{x}}_{j} \) from different class. For clarity, we donate \( {\begin{Vmatrix}M\left( {\widetilde{x}}_{i},{\widetilde{x}}_{j};{\theta }_{M}\right)  - {b}_{ij}\end{Vmatrix}}_{2} \) as \( F\left( {{\widetilde{x}}_{i},{\widetilde{x}}_{j},{b}_{ij};{\theta }_{M}}\right) \) . Hence, the comparison loss function of generator is formulated as following equation:

\[
{\mathcal{L}}_{\mathrm{{CMS}}} = \frac{1}{N}\mathop{\sum }\limits_{{i = 0}}^{N}\mathop{\sum }\limits_{{j = 0}}^{M}{\omega }_{{\widetilde{x}}_{i},{\widetilde{x}}_{j}}\left( {F\left( {{\widetilde{x}}_{i}^{\prime },{\widetilde{x}}_{i}, b = 1;{\theta }_{M}}\right) }\right.
\]

\[
\left. {-F\left( {{\widetilde{x}}_{i}^{\prime },{\widetilde{x}}_{j}, b = 1;{\theta }_{M}}\right) }\right)  \tag{10}
\]

where \( {\omega }_{{\widetilde{x}}_{i},{\widetilde{x}}_{j}} \) is a decreasing function of \( {\widetilde{x}}_{i},{\widetilde{x}}_{j} \) , ensuring that more importance is given to the loss of \( {\widetilde{x}}_{j} \) in the top of a ranking list, which shows the attribute similarity between \( {\widetilde{x}}_{i} \) and different \( {\widetilde{x}}_{j} \) . Therefore, the closer the attribute of \( {\widetilde{x}}_{i} \) to the attribute of \( {\widetilde{x}}_{j} \) , the greater the value of \( {\omega }_{{\widetilde{x}}_{i},{\widetilde{x}}_{j}} \) . So, the attribute-consistent loss is expressed in the following form:

\[
{\mathcal{L}}_{ac} = {\mathcal{L}}_{\text{CLS }} + {\lambda }_{0}{\mathcal{L}}_{\text{CMS }}. \tag{11}
\]

However, since both the classifier and comparator impose attribute-consistent constraints on the generator, this overlapping consistency leads to the issue of generated fault sample features being uniformly of the same mode. Additionally, the attribute space, constructed based on expert knowledge, often contains noise, particularly in zero-shot fault diagnosis scenarios. This complication means that the attribute vectors of unknown faults are sometimes not accurately known. To generate fault features with greater diversity and enhance the robustness of the proposed model, a cycle rank loss is designed here, which calculates the rank of the reconstructed features among all the generated features of other attributes, making the model can also account for the effect of predicted attributes.

The cycle rank loss is implemented as follows. Initially, for each generated feature \( {\widetilde{x}}_{i}^{\prime } = G\left( {z, c}\right) \) , its attribute is predicted by the classifier \( C \) , yielding the predicted attribute \( {c}_{p} = C\left( {{\widetilde{x}}_{i}^{\prime };{\theta }_{C}}\right) \) . Subsequently, the predicted attribute \( {c}_{p} \) is fed back into the generator along with a random noise \( z \) to reconstruct the fault feature \( {\widetilde{x}}_{i}^{r} = G\left( {z,{c}_{p}}\right) \) . Following this, the reconstructed feature is input into the comparator alongside generated features from other domains to compute the rank loss. Let \( I\left( u\right)  = 1 \) if \( u \) is true and 0 otherwise. Let \( l\left( {{\widetilde{x}}_{i}^{\prime },{c}_{i},{\widetilde{x}}_{j}^{\prime }}\right)  = F\left( {{\widetilde{x}}_{i}^{r},{\widetilde{x}}_{i}^{\prime }, b;{\theta }_{M}}\right)  - F\left( {{\widetilde{x}}_{i}^{r},{\widetilde{x}}_{j}^{\prime }, b;{\theta }_{M}}\right) \) and let \( {\delta \Delta }\left( {{\widetilde{x}}_{i}^{\prime },{c}_{i}}\right)  = \mathop{\sum }\limits_{{{c}_{i} \neq  {c}_{j}}}I\left( {l\left( {{\widetilde{x}}_{i}^{\prime },{c}_{i},{\widetilde{x}}_{j}^{\prime }}\right)  > 0}\right) \) , where \( b = 1 \) . So, we consider the following objective:

\[
{\mathcal{L}}_{\mathrm{{CRL}}} = \frac{1}{N}\mathop{\sum }\limits_{{i = 0}}^{N}{\delta \Delta }\left( {{\widetilde{x}}_{i}^{\prime },{c}_{i}}\right) \mathop{\sum }\limits_{{{c}_{i} \neq  {c}_{j}}}\max \left\{  {0, l\left( {{\widetilde{x}}_{i}^{\prime },{c}_{i},{\widetilde{x}}_{j}^{\prime }}\right) }\right\}
\]

(12)

where \( {c}_{i} \) denotes the attribute vector corresponding to \( {\widetilde{x}}_{i}^{\prime } \) , and \( {c}_{j} \) is the attribute vector other than \( {c}_{i} \) . Combined with the attribute-consistent loss from classifier and comparator, the optimization function of generator has following form:

\[
\mathop{\min }\limits_{G}{\mathcal{L}}_{\mathrm{{WGAN}}}^{G}\left( {C, D, G, M}\right)  = {\mathcal{L}}_{\mathrm{{ADV}}}^{G}\left( {D, G}\right)  + {\lambda }_{1}{\mathcal{L}}_{ac} + {\lambda }_{2}{\mathcal{L}}_{\mathrm{{CRL}}}
\]

(13)

where \( {\lambda }_{1} \) and \( {\lambda }_{2} \) represent the factors reregulate the impact of attribute-consistent loss and cycle rank loss, which can be considered a regularizer that compels the generator to create attribute-consistent and discriminative features. In this optimization function, adversarial loss encourages the generator to produce a feature space that is indistinguishable from the real one by deceiving the well-trained discriminator into classifying synthetic features as real. However, in generative zero-shot fault diagnosis, real fault features are unavailable for unseen classes, leading to a potential issue of domain shift. This means the generated feature space may deviate from the real feature space associated with specific attributes, despite possessing realistic feature characteristics. The introduction of attribute-consistent loss compels the generator to construct fault features that are highly consistent with the attributes, and it also prevents the overlap of the generated feature space with the real feature space of other similar attributes. Moreover, to enhance the robustness of the proposed model against noisy attributes and prevent the generated feature space from falling into the same mode, cycle rank loss is designed. This loss drives the hidden representations of the generated feature space to diverge from the initial distribution during the cycle-based restoration mapping. Through continuous adversarial training between the generator and discriminator, the trained generator is capable of producing high-quality fault features of unseen classes in the feature space for zero-shot diagnosis.

TABLE I

NETWORK PARAMETERS OF ACGAN-FG

<table><tr><td>Layer</td><td>Parameter</td></tr><tr><td colspan="2">Feature Extractor</td></tr><tr><td>Input</td><td>input dim = sample dim</td></tr><tr><td>Fc1+LeakyReLU(0.2)+LN</td><td>output_dim = 100</td></tr><tr><td>Fc2+LeakyReLU(0.2)+LN</td><td>output dim = 200</td></tr><tr><td>Fc3+LeakyReLU(0.2)+LN</td><td>output_dim = feature_dim</td></tr><tr><td colspan="2">Discriminator</td></tr><tr><td>Input 1</td><td>input dim = feature dim</td></tr><tr><td>Input 2</td><td>input dim \( = \) attribute dim</td></tr><tr><td>Concatenate(   )</td><td>feature dim + attribute dim</td></tr><tr><td>Fc1+LeakyReLU(0.2)+LN</td><td>output dim = 200</td></tr><tr><td>Fc2+LeakyReLU(0.2)+LN</td><td>output dim = 100</td></tr><tr><td>Fc3</td><td>output dim \( = 1 \)</td></tr><tr><td colspan="2">Generator</td></tr><tr><td>Input 1</td><td>input dim = noise dim</td></tr><tr><td>Input 2</td><td>input dim \( = \) attribute dim</td></tr><tr><td>Concatenate(   )</td><td>noise dim + attribute_dim</td></tr><tr><td>Fc1+LeakyReLU(0.2)+LN</td><td>output dim = 100</td></tr><tr><td>Fc2+LeakyReLU(0.2)+LN</td><td>output dim = 200</td></tr><tr><td>Fc3+LeakyReLU(0.2)+BN</td><td>output dim \( = \) feature dim</td></tr><tr><td colspan="2">Classifier</td></tr><tr><td>Input</td><td>input dim = feature dim</td></tr><tr><td>Fc1+LeakyReLU(0.2)</td><td>output_dim = 100</td></tr><tr><td>Fc2+LeakyReLU(0.2)</td><td>output dim \( = {50} \)</td></tr><tr><td>Fc3+Sigmoid</td><td>output dim \( = \) attribute dim</td></tr><tr><td colspan="2">Comparator</td></tr><tr><td>Input 1</td><td>input dim = feature dim</td></tr><tr><td>Input 2</td><td>input_dim = feature_dim</td></tr><tr><td>Concatenate(   )</td><td>feature \( \dim  + \) feature dim</td></tr><tr><td>Conv1+BN+ReLU</td><td>kernel size \( = 3 \) , filters \( = {16} \)</td></tr><tr><td>Conv2+BN+ReLU</td><td>kernel size \( = 3 \) , filters \( = {32} \)</td></tr><tr><td>Conv3+BN+ReLU</td><td>kernel size \( = 3 \) , filters \( = {64} \)</td></tr><tr><td>Fc1+LeakyReLU(0.2)+LN</td><td>output dim \( = {1500} \)</td></tr><tr><td>Fc2+LeakyReLU(0.2)+LN</td><td>output dim \( = {100} \)</td></tr><tr><td>Fe3+Sigmoid</td><td>output dim \( = 1 \)</td></tr></table>

Table I displays the structure of each component in the model that employs the Adam optimizer with a learning rate of 0.001 . "Fc" denotes the fully connected layer and Conv1 is the convolutional layer. LN and BN denote layer normalization and batch normalization, respectively. The training strategy of ACGAN-FG is represented in Algorithm 1.

## C. Feature Concatenation and Fault Diagnosis

In previous generative ZSL, domain shift primarily arises when generated samples that are not associated with attributes during the creation of unseen classes. While attribute information is typically employed in the training phase, it is often discarded during the classification stage in prior research. This results in the underutilization of the value of attribute information. To address this issue, feature concatenation operation before fault diagnosis is introduced in this study. This process derived from the feature transformation in [18]. Different from the feature transformation process, which transforms the generated samples to features through a regressor, this study leverages the last hidden layer output \( a \) from the well-trained discriminative classifier \( C \) to concatenate with the generated fault features \( \widetilde{x} \) . Due to the last hidden layer output from the classifier containing more attribute information and less redundant feature information, it is utilized to transfer the real features of seen classes along with synthesized features of unseen classes into newly discriminative features.

First, we extract the classifier from the integrated model and input both the real features of seen classes and the synthesized features of unseen classes into it. Then, the output of the last hidden layer \( a \) is extracted, where the output of the seen class feature is \( {a}^{s} \) , as well as the output of the unseen class features is \( {a}^{u} \) . Finally, the concatenation is executed on each feature as follows:

\[
{\widetilde{x}}_{c}^{s} = {\widetilde{x}}^{s} \oplus  {a}^{s}
\]

\[
{\widetilde{x}}_{c}^{u} = {\widetilde{x}}^{u} \oplus  {a}^{u} \tag{14}
\]

TABLE II

STATISTICS OF THE TEP

<table><tr><td>No.</td><td>Fault Type</td><td>Fault Description</td></tr><tr><td>1</td><td>Step</td><td>\( \mathrm{A}/\mathrm{C} \) feed ratio, input \( \mathrm{B} \) constant</td></tr><tr><td>2</td><td>Step</td><td>Input B, A/C ratio constant (pipe 4)</td></tr><tr><td>3</td><td>Step</td><td>D feed temperature (pipe 2)</td></tr><tr><td>4</td><td>Step</td><td>Reactor cooling water inlet temperature</td></tr><tr><td>5</td><td>Step</td><td>Condenser cooling water inlet temperature</td></tr><tr><td>6</td><td>Step</td><td>A feed loss (pipe 1)</td></tr><tr><td>7</td><td>Step</td><td>C header pressure loss (pipe 4)</td></tr><tr><td>8</td><td>Random variation</td><td>\( \mathrm{A},\mathrm{\;B} \) , and \( \mathrm{C} \) feed composition (pipe 4)</td></tr><tr><td>9</td><td>Random variation</td><td>D feed temperature (pipe 2)</td></tr><tr><td>10</td><td>Random variation</td><td>C feed temperature (pipe 4)</td></tr><tr><td>11</td><td>Random variation</td><td>Reactor cooling water inlet temperature</td></tr><tr><td>12</td><td>Random variation</td><td>Condenser cooling water inlet temperature</td></tr><tr><td>13</td><td>Slow drift</td><td>Reaction kinetics</td></tr><tr><td>14</td><td>Sticking</td><td>Reactor cooling water valve</td></tr><tr><td>15</td><td>Sticking</td><td>Condenser cooling water valve</td></tr></table>

where \( \oplus \) represents the concatenation operation. In this way, the concatenated feature \( {\widetilde{x}}_{c}^{s} \) and \( {\widetilde{x}}_{c}^{u} \) are utilized to train the fault diagnosis model. Note that these operations are taken after training the whole model and prior to training the classification model. In the test process, the test sample features are also transferred in the same way before prediction.

For the fault diagnosis, the linear support vector machine (LSVM), the nonlinear random forest (NRF), the probabilistic naive Bayes (PNB) [9], and multilayer perceptron (MLP) are employed for label inference. All these classification models are implemented using scikit-learn. More details about ACGAN-FG can be found at https://github.com/jie3040/ ACGAN-FG.

## IV. EXPERIMENTS AND RESULTS

In this section, we assess the effectiveness of the proposed ACGAN-FG using two distinct datasets. The first one is the Tennessee-Eastman process (TEP) [24]. Another dataset is based on a real hydraulic system [25]. The comparative experiments with other multiple current state-of-the-art methods are also conducted.

## A. Case I: Tennessee Eastman Process

1) Datasets Description: The TEP dataset is developed by Eastman Chemical Company to simulate actual chemical processes [24] and is widely used for research in fault diagnosis, and it has been frequently chosen for previous zero-shot fault diagnosis studies to verify the validity of the methods. The TEP primarily includes five units: a chemical reactor, a condenser, a recycle compressor, a vapor/liquid separator, and a stripper.

The TEP includes 21 fault modes, each represented by 480 samples. Each sample comprises 52 variables, which are divided into 22 continuous process measurements, 19 compositions, and 11 manipulated variables. We follow the data partition according to [9] and the first 15 classes are chosen for the zero-shot fault diagnosis. The statistics of the 15 fault classes are shown in Table II. The descriptions of faults indicate their class information. In line with the " 0/1 " encoding used in zero-shot learning, we use a list that outlines 20 characteristics of these fault categories to define their attributes, as detailed in Table III. By integrating the data from Tables II and III, the attribute vectors are formulated to build the semantic knowledge for Case I. Fig. 5 displays these attribute vectors. Each dimension of an attribute vector for a fault is determined by the presence ("1") or absence ("0") of a corresponding attribute. The 15 fault classes are organized into five groups, with training set comprising 12 classes and testing set comprising 3 unseen classes for each group. The group setting is presented in Table IV.

TABLE III

ATTRIBUTES OF THE TEP

<table><tr><td>No.</td><td>Attributes</td></tr><tr><td>Att#1</td><td>Input A is changed</td></tr><tr><td>Att#2</td><td>Input \( \mathrm{C} \) is changed</td></tr><tr><td>Att#3</td><td>A/C ratio is changed</td></tr><tr><td>Att#4</td><td>Input B is changed</td></tr><tr><td>Att#5</td><td>Related with pipe 4</td></tr><tr><td>Att#6</td><td>Temperature of input D is changed</td></tr><tr><td>Att#7</td><td>Related with pipe 2</td></tr><tr><td>Att#8</td><td>Disturbance is step changing</td></tr><tr><td>Att#9</td><td>Input is changed</td></tr><tr><td>Att#10</td><td>Temperature of input is changed</td></tr><tr><td>Att#11</td><td>Occurred at reactor</td></tr><tr><td>Att#12</td><td>Temperature of cooling water is changed</td></tr><tr><td>Att#13</td><td>Occurred at condenser</td></tr><tr><td>Att#14</td><td>Related with pipe 1</td></tr><tr><td>Att#15</td><td>Disturbance is random varying</td></tr><tr><td>Att#16</td><td>Model parameters are changed</td></tr><tr><td>Att#17</td><td>Disturbance is slow drift</td></tr><tr><td>Att#18</td><td>Related with cooling water</td></tr><tr><td>Att#19</td><td>Related with value</td></tr><tr><td>Att#20</td><td>Disturbance is sticking</td></tr></table>

![bo_d1hv6nf7aajc7382q90g_6_264_753_454_573_0.jpg](images/bo_d1hv6nf7aajc7382q90g_6_264_753_454_573_0.jpg)

Fig. 5. Fault description matrix for TEP dataset.

TABLE IV

GROUP SETTING OF ZERO-SHOT FAULT DIAGNOSIS FOR TEP

<table><tr><td rowspan="2">Group</td><td colspan="2">Training</td><td colspan="2">Test</td></tr><tr><td>Seen classes</td><td>Total</td><td>Unseen classes</td><td>Total</td></tr><tr><td>A</td><td>\( 2,3,4,7 - {13},{15} \)</td><td>5760</td><td>1, 6, 14</td><td>1440</td></tr><tr><td>B</td><td>\( 1,2,3,5,6,8,9,{11} - {15} \)</td><td>5760</td><td>4,7,10</td><td>1440</td></tr><tr><td>C</td><td>1-7, 9, 10, 13, 14, 15</td><td>5760</td><td>8, 11, 12</td><td>1440</td></tr><tr><td>D</td><td>\( 1,4,6 - {15} \)</td><td>5760</td><td>2,3,5</td><td>1440</td></tr><tr><td>E</td><td>1-8, 10, 11, 12, 14</td><td>5760</td><td>9,13,15</td><td>1440</td></tr></table>

For the experimental setup, we utilized a server equipped with an NVIDIA GeForce RTX 3090 with 24 GB VRAM, a 1TB SSD, and running Ubuntu 22.04 LTS as the operating system. The software framework was developed using Python 3.9.

2) Model Training: In this section, the ACGAN-FG model is trained to generate fault features for unseen classes. During training, the value of the loss function of generator is recorded to characterize the performance of the model, as shown in Fig. 6. In Fig. 6, at the beginning, the generator loss decreases dramatically, then the loss curves ascend gradually with the increase of iteration. After 1000 iterations, the generator loss converges to around 0 , which indicates the model has been trained well and can be used for fault feature generation.

![bo_d1hv6nf7aajc7382q90g_6_978_185_574_416_0.jpg](images/bo_d1hv6nf7aajc7382q90g_6_978_185_574_416_0.jpg)

Fig. 6. Loss curve of the ACGAN-FG model on the TEP dataset.

TABLE V

RUNNING TIME OF PROPOSED AND RELATED METHODS

<table><tr><td rowspan="2">Training size</td><td colspan="7">Running Time(min)</td></tr><tr><td>FDAT[9]</td><td>SCE[26]</td><td>FAGAN[17]</td><td>SRWGAN[27]</td><td>VAEGAN- AR[18]</td><td>FREE[28]</td><td>ACGAN- FG(ours)</td></tr><tr><td>\( {5760} \times  {52} \)</td><td>351.2</td><td>200.9</td><td>205.5</td><td>150.7</td><td>191.5</td><td>244.1</td><td>153.6</td></tr><tr><td>\( {2880} \times  {52} \)</td><td>262.3</td><td>128.8</td><td>137.3</td><td>88.4</td><td>96.3</td><td>136.3</td><td>86.9</td></tr></table>

To assess the computational efficiency of the proposed model, actual runtime tests were conducted under the scenarios of Group A settings in this case. For comparative analysis, other current state-of-the-art zero-shot fault diagnosis methods are chosen, such as fault direct attributes prediction (FDAT) [9], semantic-consistent embedding (SCE) [26], fault auxiliary generative adversarial network (FAGAN) [17], models semantic refinement SRWGAN [27], variational autoencoder GAN with attribute regressor (VAEGAN-AR) [18] and feature refinement model for generalized zero-shot learning (FREE) [28] where FDAT is based on attribute prediction, SCE is based on feature-attribute embedding, and the other methods and proposed method are based on generative networks. The running time of proposed model and relative models is recorded in Table V.

From the data presented in Table V, during training on the original dataset size, the SRWGAN required the least time, completing in \( {150.7}\mathrm{\;{min}} \) , while the proposed model had a slightly higher running time of 153.6 min. When the training dataset size was halved, the proposed model demonstrated the shortest running time of \( {86.9}\mathrm{\;{min}} \) .

3) Feature Evaluation and Ablation Study: After training, the trained generator is utilized to generate the fault features of unseen classes. To evaluate the quality of the synthetic features, Pearson correlation coefficient (PCC) and cosine similarity (CS) are utilized to quantitatively measure the resemblance between the generated and original fault features in the scenario of Group A setting. PCC assesses the correlation, with values above 0.5 indicating a significant correlation. CS, on the other hand, determines the similarity in data distribution by computing the cosine of the angle between two sample vectors. Both metrics scale from 0 to 1 , where higher values denote greater similarity. According to Table VI, the PCC values of proposed model exceed 0.7 for all fault classes, and the CS values are all above 0.8 , confirming that the distribution of the generated data closely matches that of the original data. In the comparative experiments, the PCC and CS values of proposed model are almost the highest for all fault classes, which indicates the quality of the sample features generated by proposed model is superior to those produced by other generative models.

![bo_d1hv6nf7aajc7382q90g_7_134_183_1466_465_0.jpg](images/bo_d1hv6nf7aajc7382q90g_7_134_183_1466_465_0.jpg)

Fig. 7. Visual analysis of generated features for TEP dataset. (a) Complete model. (b) Model without feature concatenation. (c) Model without attribute-consistent constraints.

TABLE VI

ANALYSIS OF QUANTITATIVE METRICS FOR THE GENERATIVE-BASED ZERO-SHOT MODEL FOR THE TEP DATASET

<table><tr><td>Fault class</td><td>Metrics</td><td>FAGAN</td><td>SRWGAN</td><td>VAEGAN-AR</td><td>FREE</td><td>ACGAN-FG (ours)</td></tr><tr><td rowspan="2">1</td><td>PCC</td><td>0.6921</td><td>0.5367</td><td>0.6075</td><td>0.5704</td><td>0.7033</td></tr><tr><td>CS</td><td>0.7764</td><td>0.6188</td><td>0.7949</td><td>0.7060</td><td>0.8211</td></tr><tr><td rowspan="2">6</td><td>PCC</td><td>0.7607</td><td>0.5212</td><td>0.6621</td><td>0.6133</td><td>0.7521</td></tr><tr><td>CS</td><td>0.7729</td><td>0.4843</td><td>0.8807</td><td>0.5059</td><td>0.8959</td></tr><tr><td rowspan="2">14</td><td>PCC</td><td>0.6551</td><td>0.6188</td><td>0.6990</td><td>0.5871</td><td>0.7295</td></tr><tr><td>CS</td><td>0.7434</td><td>0.7902</td><td>0.7217</td><td>0.6470</td><td>0.8022</td></tr></table>

TABLE VII

ACCURACIES OF PROPOSED METHODS FOR TEP DATASET

<table><tr><td rowspan="2">Classifier</td><td colspan="5">Accuracy (%)</td><td rowspan="2">Average (%)</td></tr><tr><td>Group A</td><td>Group B</td><td>Group C</td><td>Group D</td><td>Group E</td></tr><tr><td>LSVM</td><td>82.37</td><td>77.41</td><td>72.80</td><td>74.11</td><td>68.67</td><td>75.07</td></tr><tr><td>NRF</td><td>79.06</td><td>64.27</td><td>68.74</td><td>66.40</td><td>80.23</td><td>71.74</td></tr><tr><td>PNB</td><td>69.03</td><td>75.17</td><td>70.06</td><td>68.07</td><td>71.44</td><td>70.75</td></tr><tr><td>MLP</td><td>88.04</td><td>78.10</td><td>74.57</td><td>87.24</td><td>89.06</td><td>83.40</td></tr></table>

Moreover, the visualization analysis is conducted based on Group E to demonstrate the comparison between generated features and real ones. The comparison results are illustrated in Fig. 7. From Fig. 7(a), it can be inferred that generated features closely approximate the real features, and the generated features from different classes are easily distinguished. Then, the feature concatenation part is removed, and the result is shown in Fig. 7(b). From Fig. 7(b), we can easily see that the divisibility of features between different classes has significantly decline. This reduction in feature distinction complicates the task of the fault diagnosis classifier in differentiating features across various classes. Furthermore, the attribute-consistent constraint, initially constructed by the classifier and comparator, is removed, and the model is subsequently retrained. The regenerated features are then compared with the real features, as depicted in Fig. 7(c). There is a significant domain shift occurs, as shown in Fig. 7(c). This observation confirms the conceptual effectiveness of the proposed method.

4) Fault Diagnosis: In this part, we evaluate the model from multiple perspectives, as suggested in [29], including accuracy analysis, comparative experiments, robustness analysis, and ablation study.

a) Accuracy analysis: Table VII shows the diagnosis results of proposed ACGAN-FG under four different classifiers. The optimal results of ACGAN-FG are \( {88.04}\% ,{78.10}\% \) , \( {74.57}\% ,{87.24}\% \) , and 89.06% by MLP among various classification models in Group A, B, C, D, and E, respectively. The highest average accuracy of the five groups achieved \( {83.40}\% \) . This result is much higher than the random guess accuracy of 33.33%, proving the effectiveness of the method for the zero-shot fault diagnosis in this case. The confusion matrix for the five groups of experiments using MLP is represented in Fig. 8.

TABLE VIII

ACCURACIES OF PROPOSED AND RELATED METHODS FOR TEP DATASET

<table><tr><td rowspan="2">Method</td><td colspan="5">Accuracy (%)</td><td rowspan="2">Average (%)</td></tr><tr><td>Group A</td><td>Group B</td><td>Group C</td><td>Group D</td><td>Group E</td></tr><tr><td>FDAT</td><td>80.28</td><td>62.60</td><td>58.96</td><td>71.11</td><td>67.43</td><td>68.08</td></tr><tr><td>SCE</td><td>90.86</td><td>77.92</td><td>62.40</td><td>76.11</td><td>80.90</td><td>77.64</td></tr><tr><td>FAGAN</td><td>83.75</td><td>79.03</td><td>63.61</td><td>72.92</td><td>70.14</td><td>73.89</td></tr><tr><td>SRWGAN</td><td>81.11</td><td>71.04</td><td>65.42</td><td>75.00</td><td>79.24</td><td>74.36</td></tr><tr><td>VAEGAN-AR</td><td>85.28</td><td>74.31</td><td>71.94</td><td>89.93</td><td>82.50</td><td>80.79</td></tr><tr><td>FREE</td><td>83.40</td><td>76.04</td><td>73.33</td><td>80.42</td><td>74.31</td><td>77.50</td></tr><tr><td>ACGAN-FG</td><td>88.04</td><td>78.10</td><td>74.57</td><td>87.24</td><td>89.06</td><td>83.40</td></tr></table>

b) Comparison with other methods: In addition, the comparative experiments are executed with other zero-shot fault diagnosis methods. The fault diagnosis is performed using various classifiers, with the optimal results summarized in Table VIII. Specifically, the FDAT and the SCE employ PNB, while the other methods utilize MLP. In Table VIII, it is observed that FDAT records the lowest average diagnostic accuracy, achieving \( {68.08}\% \) . In contrast, SCE demonstrates markedly superior performance, attaining an average accuracy of 77.64%. For the generative models, both FAGAN and SRWAN exhibit similar performance of approximately \( {74}\% \) accuracy, while FREE achieved an accuracy of 77.50% and VAEGAN-AR attained an average accuracy of \( {80.79}\% \) . Meanwhile, the method proposed in this article obtained an average accuracy of 83.40%. This overall improvement indicates the superiority of the proposed method compared to these existing methods. Upon comparison of the diagnostic accuracy between different groups, Group A and Group E consistently record the highest accuracy, followed by Group D and Group B, whereas Group C recorded the lowest accuracy rate across all the methods.

c) Robustness analysis and ablation study: To assess the effectiveness of cycle rank loss, a robustness analysis was conducted under conditions of noisy fault attributes. During the feature generation process, the trained generator was fed with the unseen class attribute vector, where randomly one or two dimensions were obscured. Fault diagnosis was then performed using the newly generated fault features via MLP. Additionally, the cycle rank loss was removed, and the model was retrained. The same robustness analysis was carried out on this revised model. The results of the robustness analyses are documented in Table IX. As indicated in the table, when one dimension of the attributes is noisy, the diagnostic accuracy of the proposed model decreases slightly from 83.40% to 81.07%. This phenomenon is more pronounced when two dimensions are obscured. In comparison, for the proposed model without cycle rank loss, the diagnostic accuracy drops more significantly from \( {82.15}\% \) to \( {60.40}\% \) when two dimensions of the attributes are noisy. This observation confirms the role of cycle rank loss in enhancing the robustness of the proposed model against noisy fault attributes in zero-shot fault diagnosis.

![bo_d1hv6nf7aajc7382q90g_8_145_182_1469_272_0.jpg](images/bo_d1hv6nf7aajc7382q90g_8_145_182_1469_272_0.jpg)

Fig. 8. Confusion matrix of five groups for TEP dataset.

TABLE IX

RESULTS OF ROBUSTNESS ANALYSIS FOR TEP DATASET

<table><tr><td rowspan="2">Model</td><td rowspan="2">Number of noisy dimensions</td><td colspan="5">Accuracy (%)</td><td rowspan="2">Average (%)</td></tr><tr><td>Group A</td><td>Group B</td><td>Group C</td><td>Group D</td><td>Group E</td></tr><tr><td rowspan="3">ACGAN- FG</td><td>0</td><td>88.04</td><td>78.10</td><td>74.57</td><td>87.24</td><td>89.06</td><td>83.40</td></tr><tr><td>1</td><td>87.50</td><td>74.72</td><td>71.04</td><td>84.09</td><td>87.87</td><td>81.07</td></tr><tr><td>2</td><td>80.45</td><td>68.26</td><td>63.93</td><td>74.70</td><td>79.77</td><td>73.42</td></tr><tr><td rowspan="3">ACGAN- FG without cycle rank loss</td><td>0</td><td>87.92</td><td>75.90</td><td>74.03</td><td>84.72</td><td>88.19</td><td>82.15</td></tr><tr><td>1</td><td>80.31</td><td>67.24</td><td>64.69</td><td>70.09</td><td>79.50</td><td>72.37</td></tr><tr><td>2</td><td>69.52</td><td>55.00</td><td>51.14</td><td>54.92</td><td>71.43</td><td>60.40</td></tr></table>

## B. Case II: Hydraulic System

1) Datasets Description: To further assess the effectiveness of the proposed method, we utilize a real hydraulic system [25]. The hydraulic system is comprised of two main circuits: 1) a primary operation circuit and 2) a secondary circuit for cooling and filtration, both linked through an oil tank. The operational circuit includes a primary pump subjected to cyclical load variations, regulated by a proportional pressure relief valve. A range of process sensors is used to monitor various parameters, including pressure, flow rates, and temperature. Additionally, three virtual sensors are deployed to estimate metrics such as cooling efficiency, cooling power, and overall system efficiency, utilizing data from physical sensors. Sensor sampling frequencies within the hydraulic system vary between 100 to \( 1\mathrm{\;{Hz}} \) . To ensure uniform sampling rates, a technique for data flattening was employed, similar to methods used in prior studies [17], [25]. This method produced a dataset containing 2205 samples and 43680 dimensions. The dataset includes 144 fault categories, represented by the product of \( 3 \times  4 \times  3 \times  4 \) . Each category is defined by four attributes: the state of the cooler (three states), the valve condition (four states), internal pump leakage (three states), and the condition of the hydraulic accumulator (four states) Consequently, after one-hot coding, the number of attributes is converted to 14 . The fault categories are determined based on different combinations of attribute values.

TABLE X

AVERAGE ACCURACY OF PROPOSED AND RELATED METHODS ON HYDRAULIC SYSTEM

<table><tr><td rowspan="2">Methods</td><td colspan="3">Average accuracy (%)</td></tr><tr><td>15</td><td>25</td><td>30</td></tr><tr><td>FDAT</td><td>57.91</td><td>41.42</td><td>40.46</td></tr><tr><td>SCE</td><td>71.05</td><td>64.18</td><td>58.23</td></tr><tr><td>FAGAN</td><td>57.97</td><td>48.08</td><td>44.44</td></tr><tr><td>SRWGAN</td><td>69.39</td><td>56.60</td><td>55.28</td></tr><tr><td>VAEGAN-AR</td><td>67.09</td><td>54.63</td><td>53.47</td></tr><tr><td>FREE</td><td>70.55</td><td>59.74</td><td>52.81</td></tr><tr><td>ACGAN-FG(ours)</td><td>79.92</td><td>65.07</td><td>60.13</td></tr></table>

2) Comparison of Results With Other Methods: To assess the efficacy of the proposed methods within a hydraulic system, we established three distinct scenarios with varying numbers of unseen fault categories:15,25, and 30 . In the training set, all fault categories other than those designated as unseen are considered seen categories. Table X presents the performance of different methods in terms of the average accuracy of fault diagnosis on hydraulic system. From Table X, it can be observed that the diagnostic accuracy of all the methods is higher than the random guess accuracy for all group settings. As the number of unseen categories increases (from 15 unseen categories to 30 unseen categories), the task becomes more challenging, resulting in a decrease in accuracy for almost all methods. Our proposed method achieves the best performance across different numbers of unseen categories. Compared with the current best-performing method, the proposed method improves average accuracy by \( {8.87}\% \) when 15 faults categories are unseen. Given the analysis presented, it is evident that ACGAN-FG demonstrates superior performance in generating fault features for unseen categories and achieving enhanced results in zero-shot fault diagnosis.

## V. CONCLUSION

In this article, a novel method named ACGAN-FG was proposed for zero-shot fault diagnosis. To address the issue of domain shift, ACGAN-FG introduced attribute-consistent constraints on the generator. Besides, the cycle rank loss was constructed based on the predicted attributes from classifier. Unlike other generative models, the proposed method tries to directly generate high-quality fault features of unseen classes for further fault diagnosis. Experiment results on two cases show that while other state-of-art methods can be applied to zero-shot fault diagnosis, they do not perform as well as the proposed method in this article, which has less shift between generated features and real features. The results of visualization analysis highlight that the feature concatenation process can make the features more discriminative in feature space. Furthermore, the robustness analysis and ablation study confirm the role of cycle rank loss in enhancing the robustness of the proposed
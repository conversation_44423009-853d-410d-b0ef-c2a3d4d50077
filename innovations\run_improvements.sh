#!/bin/bash

# 🔥 全面改进版ASDCGAN运行脚本
# 包含三大关键改进：梯度裁剪、域转换、属性一致性损失

echo "🔥 全面改进版ASDCGAN运行脚本"
echo "=================================="
echo "三大关键改进:"
echo "1. ✅ 梯度裁剪 - 防止损失爆炸"
echo "2. ✅ 域转换 - 用真实特征替换随机噪声"
echo "3. ✅ 属性一致性损失 - 完善属性约束"
echo "=================================="

# 激活conda环境
echo "🔧 激活conda环境 vaegan_rtx50..."
source ~/miniconda3/etc/profile.d/conda.sh
conda activate vaegan_rtx50

cd /home/<USER>/hmt/ACGAN-FG-main/innovations

# 设置权限
chmod +x test_all_improvements.py

echo "选择测试模式:"
echo "1. 快速测试 A组 (50 epochs) - 推荐"
echo "2. 快速测试 B组 (50 epochs)"
echo "3. 完整训练 A组 (100 epochs)"
echo "4. 完整训练 B组 (100 epochs)"
echo "5. 对比测试 (禁用域转换)"
echo "6. 对比测试 (禁用梯度裁剪)"
echo "7. 自定义"

read -p "请选择 (1-7): " choice

case $choice in
    1)
        echo "🚀 启动快速测试 A组 (全部改进)..."
        python test_all_improvements.py --group A --quick_test
        ;;
    2)
        echo "🚀 启动快速测试 B组 (全部改进)..."
        python test_all_improvements.py --group B --quick_test
        ;;
    3)
        echo "🚀 启动完整训练 A组 (全部改进)..."
        python test_all_improvements.py --group A --epochs 100
        ;;
    4)
        echo "🚀 启动完整训练 B组 (全部改进)..."
        python test_all_improvements.py --group B --epochs 100
        ;;
    5)
        echo "🚀 启动对比测试 (禁用域转换)..."
        python test_all_improvements.py --group A --quick_test --disable_domain_transfer
        ;;
    6)
        echo "🚀 启动对比测试 (禁用梯度裁剪)..."
        python test_all_improvements.py --group A --quick_test --disable_grad_clip
        ;;
    7)
        read -p "输入分组 (A/B/C/D/E): " group
        read -p "输入训练轮次: " epochs
        read -p "是否禁用域转换? (y/n): " disable_dt
        read -p "是否禁用梯度裁剪? (y/n): " disable_gc
        
        cmd="python test_all_improvements.py --group $group --epochs $epochs"
        if [ "$disable_dt" = "y" ]; then
            cmd="$cmd --disable_domain_transfer"
        fi
        if [ "$disable_gc" = "y" ]; then
            cmd="$cmd --disable_grad_clip"
        fi
        
        echo "🚀 启动自定义训练..."
        echo "命令: $cmd"
        eval $cmd
        ;;
    *)
        echo "❌ 无效选择，退出"
        exit 1
        ;;
esac

echo ""
echo "✅ 训练完成！"
echo "📊 查看结果:"
echo "- 实验目录: experiments/group_*"
echo "- 训练日志: experiments/group_*/run_*/training.log"
echo "- TensorBoard: tensorboard/group_*"
echo "💡 启动TensorBoard: tensorboard --logdir=tensorboard"
echo ""
echo "🎯 预期改进效果:"
echo "- 生成器损失: 1800+ → <100"
echo "- 语义损失: 0.0000 → 有意义的非零值"
echo "- A组准确率: 60.97% → 75-85%"
echo "- B组准确率: 39.17% → 60-70%"

#!/usr/bin/env python3
"""
实验B-HardTriplet: 强化Triplet和Center损失权重
目标: 用"蛮力"在拥挤的特征空间中挤出缝隙
基于: acgan_triplet.py (最成功的基线模型)
针对: B组困难类别 [4, 7, 10]

关键改进:
- lambda_triplet: 10 -> 50 (5倍增强)
- lambda_center: 0.5 -> 2.5 (5倍增强)
- 保持其他权重不变，专注于特征空间分离
"""

import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, Reshape, Flatten, Dropout, multiply, GaussianNoise
from tensorflow.keras.layers import BatchNormalization, Activation, Embedding, ZeroPadding2D
from tensorflow.keras.layers import LeakyReLU
from tensorflow.keras.layers import UpSampling2D, Conv2D
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.utils import to_categorical
import tensorflow.keras.backend as K

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import accuracy_score
from sklearn.svm import LinearSVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
import sys
import os
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/scripts')
from read_data import creat_dataset

# 添加scripts目录到路径，避免与系统test模块冲突
sys.path.insert(0, '/app/scripts')
import test as test_module

class Zero_shot():
    def __init__(self):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)

        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        # 🔧 实验B-HardTriplet: 渐进式权重调整策略
        self.lambda_cla = 10          # 保持不变
        self.lambda_triplet = 2.0     # 1.0 -> 2.0 (适度增强)
        self.lambda_cms = 10          # 保持不变
        self.lambda_crl = 0.01        # 保持不变

        self.bound = True
        self.mi_weight = 0.001
        self.mi_bound = 100
        self.triplet_margin = 0.4

        # 🔧 Center Loss 权重适度增强
        self.lambda_center = 1.0      # 0.5 -> 1.0 (适度增强)
        self.centers = None
        self.seen_class_map = None
        self.center_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)

        # 最优准确率跟踪
        self.best_accuracy = {
            'lsvm': 0.0,
            'nrf': 0.0,
            'pnb': 0.0,
            'mlp': 0.0
        }
        
        # 构建模型
        self.build_autoencoder()
        self.discriminator = self.build_discriminator()
        self.generator = self.build_generator()
        self.classifier = self.build_classifier()

        # 为了兼容feature_generation_and_diagnosis函数，添加别名
        self.d = self.discriminator
        self.g = self.generator
        self.c = self.classifier
        
        # 优化器
        self.optimizer_G = Adam(0.0001, 0.5)
        self.optimizer_D = Adam(0.0001, 0.5)
        self.optimizer_C = Adam(0.0001, 0.5)
        self.optimizer_M = Adam(0.0001, 0.5)
        
        # 编译判别器
        self.discriminator.compile(loss=self.wasserstein_loss,
            optimizer=self.optimizer_D,
            metrics=['accuracy'])
        
        # 编译分类器
        self.classifier.compile(loss='binary_crossentropy',
            optimizer=self.optimizer_C,
            metrics=['accuracy'])
        
        # 编译生成器
        self.discriminator.trainable = False
        self.classifier.trainable = False
        
        noise = Input(shape=self.noise_shape)
        label = Input(shape=(20,))
        feature = self.generator([noise, label])
        
        fake = self.discriminator([feature, label])
        hidden_ouput, pred_attribute = self.classifier(feature)
        
        self.combined = Model([noise, label], [fake, pred_attribute])
        self.combined.compile(loss=[self.wasserstein_loss, 'binary_crossentropy'],
            optimizer=self.optimizer_G)
        
        # 编译度量学习模型
        self.encoder.compile(optimizer=self.optimizer_M)
        
    def build_autoencoder(self):
        """构建自编码器"""
        # 编码器
        sample = Input(shape=self.sample_shape)
        
        e1 = Dense(256)(sample)
        e1 = LeakyReLU(alpha=0.2)(e1)
        e1 = BatchNormalization(momentum=0.8)(e1)
        
        e2 = Dense(128)(e1)
        e2 = LeakyReLU(alpha=0.2)(e2)
        e2 = BatchNormalization(momentum=0.8)(e2)
        
        encoded = Dense(self.feature_dim)(e2)
        encoded = LeakyReLU(alpha=0.2)(encoded)
        
        self.encoder = Model(sample, encoded)
        
        # 解码器
        feature = Input(shape=self.feature_shape)
        
        d1 = Dense(128)(feature)
        d1 = LeakyReLU(alpha=0.2)(d1)
        d1 = BatchNormalization(momentum=0.8)(d1)
        
        d2 = Dense(256)(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)
        d2 = BatchNormalization(momentum=0.8)(d2)
        
        decoded = Dense(self.data_lenth, activation='tanh')(d2)
        
        self.decoder = Model(feature, decoded)
        
        # 完整自编码器 - 返回特征和重构输出（兼容feature_generation_and_diagnosis）
        sample_input = Input(shape=self.sample_shape)
        encoded_repr = self.encoder(sample_input)
        reconstructed = self.decoder(encoded_repr)

        self.autoencoder = Model(sample_input, [encoded_repr, reconstructed])
        self.autoencoder.compile(optimizer=Adam(0.0001), loss='mse')
        
    def build_generator(self):
        """构建生成器"""
        noise = Input(shape=self.noise_shape)
        label = Input(shape=(20,))
        
        label_embedding = Flatten()(Embedding(20, self.latent_dim)(label))
        label_embedding = Dense(self.latent_dim)(label)
        
        model_input = multiply([noise, label_embedding])
        model_input = Flatten()(model_input)
        
        x = Dense(128)(model_input)
        x = LeakyReLU(alpha=0.2)(x)
        x = BatchNormalization(momentum=0.8)(x)
        
        x = Dense(256)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = BatchNormalization(momentum=0.8)(x)
        
        feature = Dense(self.feature_dim, activation='tanh')(x)
        
        return Model([noise, label], feature)
    
    def build_discriminator(self):
        """构建判别器"""
        feature = Input(shape=self.feature_shape)
        label = Input(shape=(20,))
        
        label_embedding = Dense(self.feature_dim)(label)
        
        model_input = multiply([feature, label_embedding])
        
        x = Dense(256)(model_input)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        
        x = Dense(128)(x)
        x = LeakyReLU(alpha=0.2)(x)
        x = Dropout(0.4)(x)
        
        validity = Dense(1)(x)
        
        return Model([feature, label], validity)
    
    def build_classifier(self):
        """构建分类器"""
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        
        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        hidden_ouput=c2
               
        c3 = Dense(20,activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])
    
    def triplet_loss(self, anchor, positive, negative, margin=None):
        """Triplet损失函数"""
        if margin is None:
            margin = self.triplet_margin
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        """Wasserstein损失"""
        return K.mean(y_true * y_pred)
    
    def center_loss(self, features, labels):
        """Center Loss实现"""
        if self.centers is None:
            return tf.constant(0.0)
        
        # 获取当前批次的中心
        batch_centers = tf.gather(self.centers, labels)
        
        # 计算特征与对应中心的距离
        center_loss = tf.reduce_mean(tf.reduce_sum(tf.square(features - batch_centers), axis=1))
        
        return center_loss
    
    def update_centers(self, features, labels, alpha=0.5):
        """更新类中心"""
        if self.centers is None:
            return
        
        unique_labels = tf.unique(labels)[0]
        
        for label in unique_labels:
            mask = tf.equal(labels, label)
            if tf.reduce_sum(tf.cast(mask, tf.int32)) > 0:
                label_features = tf.boolean_mask(features, mask)
                current_center = self.centers[label]
                new_center = tf.reduce_mean(label_features, axis=0)
                
                # 使用移动平均更新中心
                updated_center = alpha * new_center + (1 - alpha) * current_center
                self.centers = tf.tensor_scatter_nd_update(
                    self.centers, 
                    [[label]], 
                    [updated_center]
                )

    def mi_penalty_loss(self, x, z):
        """互信息惩罚损失"""
        # 简化的互信息估计
        x_flat = tf.reshape(x, [tf.shape(x)[0], -1])
        z_flat = tf.reshape(z, [tf.shape(z)[0], -1])

        # 确保维度匹配
        min_dim = tf.minimum(tf.shape(x_flat)[1], tf.shape(z_flat)[1])
        x_flat = x_flat[:, :min_dim]
        z_flat = z_flat[:, :min_dim]

        # 计算相关性
        x_mean = tf.reduce_mean(x_flat, axis=0, keepdims=True)
        z_mean = tf.reduce_mean(z_flat, axis=0, keepdims=True)

        x_centered = x_flat - x_mean
        z_centered = z_flat - z_mean

        correlation = tf.reduce_mean(x_centered * z_centered)
        return tf.abs(correlation)

    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        """分类损失"""
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)

        mi_penalty = 0
        if self.bound == True:
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)

        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss

    def cycle_rank_loss(self, anchor, positive, negative):
        """循环排序损失"""
        return self.triplet_loss(anchor, positive, negative)

    def print_experiment_info(self):
        """打印实验信息"""
        print("="*80)
        print("🔥 实验B-HardTriplet: 强化Triplet和Center损失权重")
        print("="*80)
        print("📊 目标: 用'蛮力'在拥挤的特征空间中挤出缝隙")
        print("🎯 针对: B组困难类别 [4, 7, 10]")
        print("")
        print("⚡ 关键权重调整:")
        print(f"   - lambda_triplet: 10 -> {self.lambda_triplet} (5倍增强)")
        print(f"   - lambda_center: 0.5 -> {self.lambda_center} (5倍增强)")
        print(f"   - lambda_cla: {self.lambda_cla} (保持不变)")
        print(f"   - lambda_crl: {self.lambda_crl} (保持不变)")
        print("")
        print("🔬 实验假设:")
        print("   - 更强的Triplet约束能够强制分离相似类别")
        print("   - 更强的Center约束能够收紧类内分布")
        print("   - 两者结合能够在拥挤空间中创造分离边界")
        print("="*80)

    def train(self, epochs=2000, batch_size=256, log_file=None):
        """B-HardTriplet实验训练函数"""
        import datetime
        from read_data import creat_dataset
        from tensorflow.keras.losses import mean_squared_error

        start_time = datetime.datetime.now()

        print("🔥 开始B-HardTriplet实验训练...")
        print(f"📅 开始时间: {start_time}")
        print(f"🎯 目标类别: [4, 7, 10] (B组)")
        print(f"⚡ 强化权重: lambda_triplet={self.lambda_triplet}, lambda_center={self.lambda_center}")

        # 加载B组数据
        traindata, train_classlabel, train_attributelabel, \
        testdata, test_classlabel, test_attributelabel, \
        test_attribute_matrix, train_attribute_matrix = creat_dataset([4, 7, 10])

        print(f"📊 训练数据: {traindata.shape}")
        print(f"📊 测试数据: {testdata.shape}")

        # 初始化类中心
        num_seen_classes = len(np.unique(train_classlabel))
        self.centers = tf.Variable(tf.random.normal([num_seen_classes, self.feature_dim]), trainable=True)
        self.seen_class_map = {class_id: idx for idx, class_id in enumerate(np.unique(train_classlabel))}

        num_batches = len(traindata) // batch_size

        valid = -np.ones((batch_size, 1))
        fake = np.ones((batch_size, 1))

        for epoch in range(epochs):
            epoch_start = datetime.datetime.now()

            # 初始化损失值变量
            epoch_triplet_loss = 0.0
            epoch_center_loss = 0.0
            epoch_total_loss = 0.0

            for batch_i in range(num_batches):
                batch_start_time = datetime.datetime.now()
                start_i = batch_i * batch_size
                end_i = (batch_i + 1) * batch_size

                train_x = traindata[start_i:end_i]
                train_y = train_attributelabel[start_i:end_i]
                train_labels = train_classlabel[start_i:end_i]

                # 1. Autoencoder and Classifier Training
                self.autoencoder.trainable = True
                self.classifier.trainable = True

                with tf.GradientTape(persistent=True) as tape_auto_c:
                    feature = self.encoder(train_x)
                    output_sample = self.decoder(feature)
                    autoencoder_loss = mean_squared_error(train_x, output_sample)

                    hidden_output_c, predict_attribute_c = self.classifier(feature)
                    c_loss = self.classification_loss(feature, train_y, hidden_output_c, predict_attribute_c)

                    total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss,
                                                   self.autoencoder.trainable_weights + self.classifier.trainable_weights)
                self.optimizer_C.apply_gradients(zip(grads_auto_c,
                                                   self.autoencoder.trainable_weights + self.classifier.trainable_weights))
                del tape_auto_c

                # 2. Metric Learning (Triplet + Center Loss)
                # 准备triplet样本
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []

                for i, label in enumerate(train_labels):
                    # 正样本：同类别的其他样本
                    same_class_indices = np.where(train_classlabel == label)[0]
                    if len(same_class_indices) > 1:
                        pos_idx = np.random.choice([idx for idx in same_class_indices if idx != start_i + i])
                        positive_samples.append(traindata[pos_idx])
                    else:
                        positive_samples.append(train_x[i])  # 如果没有其他同类样本，使用自己

                    # 负样本：不同类别的样本
                    diff_class_indices = np.where(train_classlabel != label)[0]
                    neg_idx = np.random.choice(diff_class_indices)
                    negative_samples.append(traindata[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape(persistent=True) as tape_m:
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)

                    # 🔥 强化的Triplet Loss
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                    # 🔥 强化的Center Loss
                    c_loss = self.center_loss(anchor_features, train_labels)

                    # 组合损失 - 使用强化权重
                    total_metric_loss = self.lambda_triplet * m_loss + self.lambda_center * c_loss

                # 保存损失值到外部作用域
                epoch_triplet_loss = m_loss
                epoch_center_loss = c_loss

                # 更新encoder
                grads_encoder = tape_m.gradient(total_metric_loss, self.encoder.trainable_weights)
                self.optimizer_M.apply_gradients(zip(grads_encoder, self.encoder.trainable_weights))

                # 更新centers
                grads_centers = tape_m.gradient(c_loss, [self.centers])
                self.center_optimizer.apply_gradients(zip(grads_centers, [self.centers]))
                del tape_m

                # 3. Discriminator Training
                self.autoencoder.trainable = False
                self.classifier.trainable = False
                self.discriminator.trainable = True
                self.generator.trainable = False

                for _ in range(self.n_critic):
                    with tf.GradientTape() as tape_d:
                        noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                        fake_feature = self.generator([noise, train_y])
                        real_feature = self.encoder(train_x)

                        real_validity = self.discriminator([real_feature, train_y])
                        fake_validity = self.discriminator([fake_feature, train_y])

                        d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                        d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                        d_loss = d_loss_real + d_loss_fake

                    grads_d = tape_d.gradient(d_loss, self.discriminator.trainable_weights)
                    self.optimizer_D.apply_gradients(zip(grads_d, self.discriminator.trainable_weights))

                # 4. Generator Training
                self.discriminator.trainable = False
                self.generator.trainable = True

                with tf.GradientTape() as tape_g:
                    noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    Fake_feature_g = self.generator([noise_g, train_y])
                    Fake_validity_g = self.discriminator([Fake_feature_g, train_y])
                    adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)

                    fake_hidden_output_g, Fake_classification_g = self.classifier(Fake_feature_g)
                    classification_loss = self.classification_loss(Fake_feature_g, train_y, fake_hidden_output_g, Fake_classification_g)

                    # Generator的Triplet loss
                    g_anchor_features = Fake_feature_g
                    g_positive_features = self.encoder(positive_samples)
                    g_negative_features = self.encoder(negative_samples)
                    triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)

                    # Generator的Center loss
                    center_loss_g = self.center_loss(g_anchor_features, train_labels)

                    # CRL loss
                    cycle_rank_loss_val = 0.0
                    if self.crl:
                        reconstructed_feature = self.generator([noise_g, Fake_classification_g])
                        shuffled_indices = tf.random.shuffle(tf.range(batch_size))
                        unsimilar_attributes = tf.gather(train_y, shuffled_indices)
                        unsimilar_fake_feature = self.generator([noise_g, unsimilar_attributes])
                        cycle_rank_loss_val = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_fake_feature)

                    # 🔥 使用强化权重的总损失
                    total_loss = (adversarial_loss +
                                 self.lambda_cla * classification_loss +
                                 self.lambda_triplet * triplet_loss_g +
                                 self.lambda_center * center_loss_g +
                                 self.lambda_crl * cycle_rank_loss_val)

                # 保存总损失到外部作用域
                epoch_total_loss = total_loss

                grads_g = tape_g.gradient(total_loss, self.generator.trainable_weights)
                self.optimizer_G.apply_gradients(zip(grads_g, self.generator.trainable_weights))

                # 每个batch输出损失值（E组格式）
                batch_time = datetime.datetime.now() - batch_start_time
                try:
                    # 转换损失值
                    ae_c_val = float(total_ac_loss.numpy().mean()) if hasattr(total_ac_loss, 'numpy') else float(total_ac_loss)
                    triplet_val = float(m_loss.numpy().mean()) if hasattr(m_loss, 'numpy') else float(m_loss)  # 单独的强化Triplet Loss
                    center_val = float(c_loss.numpy().mean()) if hasattr(c_loss, 'numpy') else float(c_loss)  # 单独的强化Center Loss
                    metric_val = float(total_metric_loss.numpy().mean()) if hasattr(total_metric_loss, 'numpy') else float(total_metric_loss)  # 组合的强化损失
                    d_val = float(d_loss.numpy().mean()) if hasattr(d_loss, 'numpy') else float(d_loss)
                    g_val = float(total_loss.numpy().mean()) if hasattr(total_loss, 'numpy') else float(total_loss)
                    crl_val = float(cycle_rank_loss_val.numpy().mean()) if hasattr(cycle_rank_loss_val, 'numpy') else float(cycle_rank_loss_val)

                    print(f"[Epoch {epoch+1}/{epochs}][Batch {batch_i}/{num_batches}][AE+C loss: {ae_c_val:.6f}][强化Triplet: {triplet_val:.6f}][强化Center: {center_val:.6f}][组合强化: {metric_val:.6f}][D loss: {d_val:.6f}][G loss {g_val:.6f}][CRL loss: {crl_val:.6f}]time: {batch_time}")

                    if log_file:
                        log_file.write(f"[Epoch {epoch+1}/{epochs}][Batch {batch_i}/{num_batches}][AE+C loss: {ae_c_val:.6f}][强化Triplet: {triplet_val:.6f}][强化Center: {center_val:.6f}][组合强化: {metric_val:.6f}][D loss: {d_val:.6f}][G loss {g_val:.6f}][CRL loss: {crl_val:.6f}]time: {batch_time}\n")
                        log_file.flush()
                except:
                    print(f"[Epoch {epoch+1}/{epochs}][Batch {batch_i}/{num_batches}] Loss recording error")

                # 保存损失到epoch级别
                epoch_triplet_loss = total_metric_loss
                epoch_center_loss = c_loss
                epoch_total_loss = total_loss

            # 每个epoch结束时使用feature_generation_and_diagnosis评估（原版格式）
            if epoch % 1 == 0:
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = test_module.feature_generation_and_diagnosis(
                    2000, testdata, test_attributelabel, self.autoencoder, self.g, self.c, [4, 7, 10])

                epoch_time = datetime.datetime.now() - epoch_start

                # 更新最优准确率
                if accuracy_lsvm > self.best_accuracy['lsvm']:
                    self.best_accuracy['lsvm'] = accuracy_lsvm
                if accuracy_nrf > self.best_accuracy['nrf']:
                    self.best_accuracy['nrf'] = accuracy_nrf
                if accuracy_pnb > self.best_accuracy['pnb']:
                    self.best_accuracy['pnb'] = accuracy_pnb
                if accuracy_mlp > self.best_accuracy['mlp']:
                    self.best_accuracy['mlp'] = accuracy_mlp

                # E组格式输出
                print(f"[Accuracy_lsvm: {accuracy_lsvm:.6f}] [Accuracy_nrf: {accuracy_nrf:.6f}] [Accuracy_pnb: {accuracy_pnb:.6f}][Accuracy_mlp: {accuracy_mlp:.6f}]time: {epoch_time}")
                print(f"[Best_lsvm: {self.best_accuracy['lsvm']:.6f}] [Best_nrf: {self.best_accuracy['nrf']:.6f}] [Best_pnb: {self.best_accuracy['pnb']:.6f}][Best_mlp: {self.best_accuracy['mlp']:.6f}]")

                if log_file:
                    log_file.write(f"[Accuracy_lsvm: {accuracy_lsvm:.6f}] [Accuracy_nrf: {accuracy_nrf:.6f}] [Accuracy_pnb: {accuracy_pnb:.6f}][Accuracy_mlp: {accuracy_mlp:.6f}]time: {epoch_time}\n")
                    log_file.write(f"[Best_lsvm: {self.best_accuracy['lsvm']:.6f}] [Best_nrf: {self.best_accuracy['nrf']:.6f}] [Best_pnb: {self.best_accuracy['pnb']:.6f}][Best_mlp: {self.best_accuracy['mlp']:.6f}]\n")
                    log_file.flush()

        end_time = datetime.datetime.now()
        total_time = end_time - start_time

        print(f"✅ B-HardTriplet实验训练完成!")
        print(f"📅 结束时间: {end_time}")
        print(f"⏱️  总耗时: {total_time}")

        return True



# 使用示例
if __name__ == "__main__":
    model = Zero_shot()
    model.print_experiment_info()
    print("✅ 实验B-HardTriplet模型初始化完成!")
    print("🚀 准备开始B组困难类别的强化训练...")

    # 开始训练
    model.train(epochs=800, batch_size=256)

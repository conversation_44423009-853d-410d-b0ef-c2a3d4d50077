#!/bin/bash

# 优化后的ASDCGAN测试脚本
# 修复了损失权重和学习率问题

echo "🔥 优化后的ASDCGAN训练脚本"
echo "=================================="
echo "修复内容:"
echo "- 循环损失权重: 10.0 → 1.0"
echo "- 语义损失权重: 5.0 → 0.5"
echo "- 生成器学习率: 0.0001 → 0.0002"
echo "- 判别器学习率: 0.0002 → 0.0004"
echo "=================================="

# 激活conda环境
echo "🔧 激活conda环境 vaegan_rtx50..."
source ~/miniconda3/etc/profile.d/conda.sh
conda activate vaegan_rtx50

cd /home/<USER>/hmt/ACGAN-FG-main/innovations

# 设置权限
chmod +x quick_test_optimized.py

echo "选择测试模式:"
echo "1. 快速测试 A组 (50 epochs)"
echo "2. 快速测试 B组 (50 epochs)"
echo "3. 完整训练 A组 (1000 epochs)"
echo "4. 完整训练 B组 (1000 epochs)"
echo "5. 自定义"

read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo "🚀 启动快速测试 A组..."
        python quick_test_optimized.py --group A --quick_test
        ;;
    2)
        echo "🚀 启动快速测试 B组..."
        python quick_test_optimized.py --group B --quick_test
        ;;
    3)
        echo "🚀 启动完整训练 A组..."
        python quick_test_optimized.py --group A --epochs 1000
        ;;
    4)
        echo "🚀 启动完整训练 B组..."
        python quick_test_optimized.py --group B --epochs 1000
        ;;
    5)
        read -p "输入分组 (A/B/C/D/E): " group
        read -p "输入训练轮次: " epochs
        echo "🚀 启动自定义训练..."
        python quick_test_optimized.py --group $group --epochs $epochs
        ;;
    *)
        echo "❌ 无效选择，退出"
        exit 1
        ;;
esac

echo "✅ 训练完成！"
echo "📊 查看结果:"
echo "- 日志文件: experiments/group_*/run_*/training.log"
echo "- TensorBoard: tensorboard/group_*"
echo "💡 启动TensorBoard: tensorboard --logdir=tensorboard"

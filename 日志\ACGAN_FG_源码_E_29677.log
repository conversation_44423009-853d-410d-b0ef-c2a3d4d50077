2025-07-09 15:03:14.320022: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-07-09 15:03:14.328475: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:485] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered
2025-07-09 15:03:14.337856: E external/local_xla/xla/stream_executor/cuda/cuda_dnn.cc:8473] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
2025-07-09 15:03:14.340903: E external/local_xla/xla/stream_executor/cuda/cuda_blas.cc:1471] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered
2025-07-09 15:03:14.348536: I tensorflow/core/platform/cpu_feature_guard.cc:211] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-07-09 15:03:15.783296: E external/local_xla/xla/stream_executor/cuda/cuda_driver.cc:266] failed call to cuInit: CUDA_ERROR_NO_DEVICE: no CUDA-capable device is detected
2025-07-09 15:03:15.783335: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:135] retrieving CUDA diagnostic information for host: 8325d131374d
2025-07-09 15:03:15.783339: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:142] hostname: 8325d131374d
2025-07-09 15:03:15.783361: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:166] libcuda reported version is: 570.153.2
2025-07-09 15:03:15.783371: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:170] kernel reported version is: NOT_FOUND: could not find kernel module information in driver version file contents: "NVRM version: NVIDIA UNIX Open Kernel Module for x86_64  570.153.02  Release Build  (dvs-builder@U22-A23-20-3)  Tue May 13 16:34:58 UTC 2025
GCC version:  gcc version 13.3.0 (Ubuntu 13.3.0-6ubuntu2~24.04) 
"
WARNING:tensorflow:Calling GradientTape.gradient on a persistent tape inside its context is significantly less efficient than calling it outside the context (it causes the gradient ops to be recorded on the tape, leading to increased CPU and memory usage). Only call GradientTape.gradient inside the context if you actually want to trace the gradient in order to compute higher order derivatives.
loading data...
test classes: [1, 6, 14]
train classes: [0, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13]
[Epoch 0/2000][Batch 0/48][Autoencoder loss: 1100611.875000][C loss: 72.972122][M loss: 1.546205][D loss: -99.658524][G loss 2178.701904 ]time: 0:00:02.673198 
[Epoch 0/2000][Batch 1/48][Autoencoder loss: 1002144.062500][C loss: 38.496704][M loss: 1.222464][D loss: -702.592529][G loss 117.884651 ]time: 0:00:03.688678 
[Epoch 0/2000][Batch 2/48][Autoencoder loss: 922717.187500][C loss: 21.992916][M loss: 0.876065][D loss: -1367.468262][G loss 222.700363 ]time: 0:00:04.656688 
[Epoch 0/2000][Batch 3/48][Autoencoder loss: 838666.750000][C loss: 13.069356][M loss: 0.758517][D loss: -2113.899170][G loss 200.780121 ]time: 0:00:05.175988 
[Epoch 0/2000][Batch 4/48][Autoencoder loss: 781514.625000][C loss: 25.256395][M loss: 0.720785][D loss: -3018.911621][G loss 168.240921 ]time: 0:00:05.620253 
[Epoch 0/2000][Batch 5/48][Autoencoder loss: 718173.812500][C loss: 19.699247][M loss: 0.722949][D loss: -4007.724121][G loss 150.890488 ]time: 0:00:06.075923 
[Epoch 0/2000][Batch 6/48][Autoencoder loss: 650219.625000][C loss: 10.727358][M loss: 0.710823][D loss: -5098.033203][G loss 134.686722 ]time: 0:00:06.524957 
[Epoch 0/2000][Batch 7/48][Autoencoder loss: 568754.687500][C loss: 2.376030][M loss: 0.701083][D loss: -6303.055664][G loss 118.685127 ]time: 0:00:06.966186 
[Epoch 0/2000][Batch 8/48][Autoencoder loss: 478263.312500][C loss: 68.833588][M loss: 0.703723][D loss: -7575.451660][G loss 102.620705 ]time: 0:00:07.403239 
[Epoch 0/2000][Batch 9/48][Autoencoder loss: 388214.687500][C loss: 58.090675][M loss: 0.700434][D loss: -9127.778320][G loss 89.563309 ]time: 0:00:07.848688 
[Epoch 0/2000][Batch 10/48][Autoencoder loss: 299414.062500][C loss: 46.003006][M loss: 0.699795][D loss: -11009.830078][G loss 74.024727 ]time: 0:00:08.304162 
[Epoch 0/2000][Batch 11/48][Autoencoder loss: 213089.734375][C loss: 35.107285][M loss: 0.698517][D loss: -13273.550781][G loss 62.304577 ]time: 0:00:08.731447 
[Epoch 0/2000][Batch 12/48][Autoencoder loss: 143553.093750][C loss: 60.592583][M loss: 0.698430][D loss: -15961.690430][G loss 46.390976 ]time: 0:00:09.173527 
[Epoch 0/2000][Batch 13/48][Autoencoder loss: 100772.968750][C loss: 55.167824][M loss: 0.697378][D loss: -18876.126953][G loss 31.979593 ]time: 0:00:09.866521 
[Epoch 0/2000][Batch 14/48][Autoencoder loss: 77626.984375][C loss: 44.337479][M loss: 0.696583][D loss: -21726.800781][G loss 17.300131 ]time: 0:00:10.305427 
[Epoch 0/2000][Batch 15/48][Autoencoder loss: 62449.566406][C loss: 29.181587][M loss: 0.699683][D loss: -24397.322266][G loss 5.064128 ]time: 0:00:10.761931 
[Epoch 0/2000][Batch 16/48][Autoencoder loss: 46709.882812][C loss: 13.170678][M loss: 0.704289][D loss: -26839.654297][G loss 1.442541 ]time: 0:00:11.503626 
[Epoch 0/2000][Batch 17/48][Autoencoder loss: 33381.316406][C loss: 3.851835][M loss: 0.695796][D loss: -29401.455078][G loss -6.858254 ]time: 0:00:12.509830 
[Epoch 0/2000][Batch 18/48][Autoencoder loss: 25212.816406][C loss: 2.391065][M loss: 0.703628][D loss: -32277.544922][G loss 1.939096 ]time: 0:00:12.995926 
[Epoch 0/2000][Batch 19/48][Autoencoder loss: 22200.712891][C loss: 0.372987][M loss: 0.698622][D loss: -35569.261719][G loss 1.040711 ]time: 0:00:13.430431 
[Epoch 0/2000][Batch 20/48][Autoencoder loss: 24858.097656][C loss: 63.711620][M loss: 0.720128][D loss: -39974.210938][G loss -6.981116 ]time: 0:00:13.872544 
[Epoch 0/2000][Batch 21/48][Autoencoder loss: 25982.394531][C loss: 62.110703][M loss: 0.712676][D loss: -45148.875000][G loss -63.675278 ]time: 0:00:14.315319 
[Epoch 0/2000][Batch 22/48][Autoencoder loss: 23192.519531][C loss: 57.457390][M loss: 0.778275][D loss: -49714.628906][G loss -59.074146 ]time: 0:00:14.744808 
[Epoch 0/2000][Batch 23/48][Autoencoder loss: 21618.363281][C loss: 47.680550][M loss: 0.805686][D loss: -54168.382812][G loss 38.731525 ]time: 0:00:15.190879 
[Epoch 0/2000][Batch 24/48][Autoencoder loss: 28669.304688][C loss: 74.761765][M loss: 0.834149][D loss: -56871.761719][G loss 84.657784 ]time: 0:00:15.661832 
[Epoch 0/2000][Batch 25/48][Autoencoder loss: 25887.990234][C loss: 54.937717][M loss: 0.705579][D loss: -61463.343750][G loss 63.686199 ]time: 0:00:16.128160 
[Epoch 0/2000][Batch 26/48][Autoencoder loss: 25308.964844][C loss: 40.163998][M loss: 0.719976][D loss: -66789.937500][G loss 9.125708 ]time: 0:00:16.568705 
[Epoch 0/2000][Batch 27/48][Autoencoder loss: 22758.259766][C loss: 30.745142][M loss: 0.707653][D loss: -72752.429688][G loss -44.445053 ]time: 0:00:17.014354 
[Epoch 0/2000][Batch 28/48][Autoencoder loss: 19921.632812][C loss: 69.397675][M loss: 0.722982][D loss: -79924.484375][G loss -111.071434 ]time: 0:00:17.827200 
[Epoch 0/2000][Batch 29/48][Autoencoder loss: 15669.385742][C loss: 64.983742][M loss: 0.709209][D loss: -86452.281250][G loss -162.270416 ]time: 0:00:18.770823 
[Epoch 0/2000][Batch 30/48][Autoencoder loss: 13974.825195][C loss: 60.673138][M loss: 0.713905][D loss: -95001.507812][G loss -267.691040 ]time: 0:00:19.639520 
[Epoch 0/2000][Batch 31/48][Autoencoder loss: 12600.283203][C loss: 50.976418][M loss: 0.724439][D loss: -102014.164062][G loss -488.793945 ]time: 0:00:20.679695 
[Epoch 0/2000][Batch 32/48][Autoencoder loss: 11633.564453][C loss: 83.729607][M loss: 0.720478][D loss: -108493.445312][G loss -600.848938 ]time: 0:00:21.123441 
[Epoch 0/2000][Batch 33/48][Autoencoder loss: 11121.268555][C loss: 72.685837][M loss: 0.703741][D loss: -114818.703125][G loss -772.215881 ]time: 0:00:21.564308 
[Epoch 0/2000][Batch 34/48][Autoencoder loss: 11670.464844][C loss: 62.449745][M loss: 0.697334][D loss: -121686.359375][G loss -1003.997681 ]time: 0:00:22.004556 
[Epoch 0/2000][Batch 35/48][Autoencoder loss: 12114.907227][C loss: 49.160801][M loss: 0.697487][D loss: -129361.109375][G loss -1280.119263 ]time: 0:00:22.801030 
[Epoch 0/2000][Batch 36/48][Autoencoder loss: 11924.377930][C loss: 48.594547][M loss: 0.698473][D loss: -138468.156250][G loss -1528.761230 ]time: 0:00:23.923991 
[Epoch 0/2000][Batch 37/48][Autoencoder loss: 10806.418945][C loss: 34.660194][M loss: 0.700473][D loss: -147860.796875][G loss -1912.387085 ]time: 0:00:24.530836 
[Epoch 0/2000][Batch 38/48][Autoencoder loss: 9694.196289][C loss: 22.327574][M loss: 0.699296][D loss: -157548.140625][G loss -2311.542969 ]time: 0:00:24.970065 
[Epoch 0/2000][Batch 39/48][Autoencoder loss: 8796.133789][C loss: 10.822135][M loss: 0.696778][D loss: -166573.156250][G loss -2765.685303 ]time: 0:00:25.421243 
[Epoch 0/2000][Batch 40/48][Autoencoder loss: 7734.060547][C loss: 16.430624][M loss: 0.731076][D loss: -174646.187500][G loss -3565.856445 ]time: 0:00:25.864811 
[Epoch 0/2000][Batch 41/48][Autoencoder loss: 6858.767578][C loss: 18.090971][M loss: 0.697396][D loss: -182677.203125][G loss -4173.819336 ]time: 0:00:26.312231 
[Epoch 0/2000][Batch 42/48][Autoencoder loss: 6764.636230][C loss: 17.826267][M loss: 0.700432][D loss: -194176.984375][G loss -4895.006348 ]time: 0:00:26.756946 
[Epoch 0/2000][Batch 43/48][Autoencoder loss: 6121.381348][C loss: 15.557607][M loss: 0.706496][D loss: -201306.578125][G loss -5557.651855 ]time: 0:00:27.213578 
[Epoch 0/2000][Batch 44/48][Autoencoder loss: 5659.724121][C loss: 32.707989][M loss: 0.703665][D loss: -213534.406250][G loss -6092.684570 ]time: 0:00:27.676893 
[Epoch 0/2000][Batch 45/48][Autoencoder loss: 5345.864258][C loss: 33.419720][M loss: 0.700748][D loss: -225579.109375][G loss -7274.208008 ]time: 0:00:28.142960 
[Epoch 0/2000][Batch 46/48][Autoencoder loss: 5205.819824][C loss: 32.828770][M loss: 0.696670][D loss: -236837.593750][G loss -8337.022461 ]time: 0:00:28.582293 
[Epoch 0/2000][Batch 47/48][Autoencoder loss: 5222.902832][C loss: 30.448370][M loss: 0.694895][D loss: -247715.265625][G loss -9715.191406 ]time: 0:00:29.029039 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 4s
63/63 [==============================] - 0s 558us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 479us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 467us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 0/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.333333] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.333333]
[Epoch 1/2000][Batch 0/48][Autoencoder loss: 4975.063477][C loss: 67.688751][M loss: 0.694461][D loss: -256886.187500][G loss -12891.306641 ]time: 0:00:41.792794 
[Epoch 1/2000][Batch 1/48][Autoencoder loss: 4655.421875][C loss: 62.761677][M loss: 0.695785][D loss: -265559.031250][G loss -14141.081055 ]time: 0:00:42.234602 
[Epoch 1/2000][Batch 2/48][Autoencoder loss: 4147.026855][C loss: 54.766129][M loss: 0.694921][D loss: -275056.125000][G loss -16196.531250 ]time: 0:00:42.675108 
[Epoch 1/2000][Batch 3/48][Autoencoder loss: 3781.407715][C loss: 44.968864][M loss: 0.694411][D loss: -287666.500000][G loss -18707.332031 ]time: 0:00:43.138020 
[Epoch 1/2000][Batch 4/48][Autoencoder loss: 3647.929443][C loss: 38.807896][M loss: 0.695241][D loss: -303768.500000][G loss -21364.349609 ]time: 0:00:43.618797 
[Epoch 1/2000][Batch 5/48][Autoencoder loss: 4092.733887][C loss: 31.992418][M loss: 0.694091][D loss: -315834.781250][G loss -24280.175781 ]time: 0:00:44.197490 
[Epoch 1/2000][Batch 6/48][Autoencoder loss: 3624.426270][C loss: 25.693432][M loss: 0.693989][D loss: -324554.531250][G loss -28070.644531 ]time: 0:00:45.339190 
[Epoch 1/2000][Batch 7/48][Autoencoder loss: 3098.373779][C loss: 20.990246][M loss: 0.694103][D loss: -335781.781250][G loss -31112.867188 ]time: 0:00:46.300743 
[Epoch 1/2000][Batch 8/48][Autoencoder loss: 2951.801514][C loss: 47.935665][M loss: 0.693958][D loss: -347060.937500][G loss -28247.712891 ]time: 0:00:46.735855 
[Epoch 1/2000][Batch 9/48][Autoencoder loss: 2887.389160][C loss: 46.398911][M loss: 0.693962][D loss: -357138.031250][G loss -31766.345703 ]time: 0:00:47.173147 
[Epoch 1/2000][Batch 10/48][Autoencoder loss: 2874.480225][C loss: 42.502735][M loss: 0.693958][D loss: -368231.656250][G loss -36197.285156 ]time: 0:00:47.616681 
[Epoch 1/2000][Batch 11/48][Autoencoder loss: 2731.225098][C loss: 38.207764][M loss: 0.694141][D loss: -378486.875000][G loss -41101.570312 ]time: 0:00:48.095559 
[Epoch 1/2000][Batch 12/48][Autoencoder loss: 2478.805908][C loss: 23.921022][M loss: 0.693714][D loss: -382002.375000][G loss -51348.839844 ]time: 0:00:48.540745 
[Epoch 1/2000][Batch 13/48][Autoencoder loss: 2221.019043][C loss: 20.574993][M loss: 0.693782][D loss: -391916.687500][G loss -56781.914062 ]time: 0:00:48.970649 
[Epoch 1/2000][Batch 14/48][Autoencoder loss: 1899.541870][C loss: 18.175732][M loss: 0.693650][D loss: -398410.406250][G loss -64522.628906 ]time: 0:00:49.416387 
[Epoch 1/2000][Batch 15/48][Autoencoder loss: 1647.238892][C loss: 15.337821][M loss: 0.693674][D loss: -405587.500000][G loss -71209.125000 ]time: 0:00:50.336321 
[Epoch 1/2000][Batch 16/48][Autoencoder loss: 1755.835938][C loss: 10.517469][M loss: 0.694007][D loss: -412387.500000][G loss -72197.921875 ]time: 0:00:51.488648 
[Epoch 1/2000][Batch 17/48][Autoencoder loss: 1326.233765][C loss: 9.971899][M loss: 0.693796][D loss: -421095.562500][G loss -80545.953125 ]time: 0:00:52.261787 
[Epoch 1/2000][Batch 18/48][Autoencoder loss: 1222.551270][C loss: 9.916976][M loss: 0.693693][D loss: -428466.750000][G loss -89531.085938 ]time: 0:00:52.699828 
[Epoch 1/2000][Batch 19/48][Autoencoder loss: 1111.470703][C loss: 9.244650][M loss: 0.693583][D loss: -431768.687500][G loss -98424.101562 ]time: 0:00:53.152783 
[Epoch 1/2000][Batch 20/48][Autoencoder loss: 2695.105225][C loss: 16.264809][M loss: 0.695786][D loss: -449924.375000][G loss -105048.039062 ]time: 0:00:53.588192 
[Epoch 1/2000][Batch 21/48][Autoencoder loss: 6022.110352][C loss: 14.583807][M loss: 0.832280][D loss: -457448.687500][G loss -116937.296875 ]time: 0:00:54.027782 
[Epoch 1/2000][Batch 22/48][Autoencoder loss: 4526.351562][C loss: 11.658788][M loss: 0.775010][D loss: -458264.187500][G loss -127796.953125 ]time: 0:00:54.459463 
[Epoch 1/2000][Batch 23/48][Autoencoder loss: 2996.949463][C loss: 7.819425][M loss: 0.755612][D loss: -454619.093750][G loss -142918.593750 ]time: 0:00:54.894654 
[Epoch 1/2000][Batch 24/48][Autoencoder loss: 4602.650879][C loss: 14.684793][M loss: 0.711948][D loss: -425172.000000][G loss -157571.937500 ]time: 0:00:55.334062 
[Epoch 1/2000][Batch 25/48][Autoencoder loss: 4709.088379][C loss: 15.662107][M loss: 0.707221][D loss: -422576.718750][G loss -171235.765625 ]time: 0:00:55.776941 
[Epoch 1/2000][Batch 26/48][Autoencoder loss: 5174.840820][C loss: 14.490875][M loss: 0.704839][D loss: -423129.312500][G loss -181764.906250 ]time: 0:00:56.535730 
[Epoch 1/2000][Batch 27/48][Autoencoder loss: 4509.741699][C loss: 11.482092][M loss: 0.705550][D loss: -419700.968750][G loss -194783.578125 ]time: 0:00:57.561970 
[Epoch 1/2000][Batch 28/48][Autoencoder loss: 4305.688477][C loss: 18.059406][M loss: 0.700398][D loss: -399501.281250][G loss -235389.921875 ]time: 0:00:58.282767 
[Epoch 1/2000][Batch 29/48][Autoencoder loss: 2904.057373][C loss: 15.697371][M loss: 0.700449][D loss: -394138.031250][G loss -243357.687500 ]time: 0:00:58.754959 
[Epoch 1/2000][Batch 30/48][Autoencoder loss: 2975.055664][C loss: 12.707892][M loss: 0.697533][D loss: -397649.750000][G loss -267684.437500 ]time: 0:00:59.243073 
[Epoch 1/2000][Batch 31/48][Autoencoder loss: 1859.212036][C loss: 9.185201][M loss: 0.696431][D loss: -385878.843750][G loss -298106.187500 ]time: 0:00:59.694434 
[Epoch 1/2000][Batch 32/48][Autoencoder loss: 1523.495972][C loss: 12.762692][M loss: 0.696439][D loss: -430049.531250][G loss -242603.921875 ]time: 0:01:00.137589 
[Epoch 1/2000][Batch 33/48][Autoencoder loss: 1310.161499][C loss: 10.767354][M loss: 0.695655][D loss: -419955.781250][G loss -262284.968750 ]time: 0:01:00.578525 
[Epoch 1/2000][Batch 34/48][Autoencoder loss: 1310.400269][C loss: 8.784493][M loss: 0.696072][D loss: -413560.250000][G loss -285829.906250 ]time: 0:01:01.013329 
[Epoch 1/2000][Batch 35/48][Autoencoder loss: 1136.064209][C loss: 5.823965][M loss: 0.695370][D loss: -410986.343750][G loss -297033.562500 ]time: 0:01:01.452210 
[Epoch 1/2000][Batch 36/48][Autoencoder loss: 1165.381348][C loss: 12.457051][M loss: 0.694274][D loss: -395018.156250][G loss -321425.875000 ]time: 0:01:01.903379 
[Epoch 1/2000][Batch 37/48][Autoencoder loss: 1158.990234][C loss: 13.094565][M loss: 0.694048][D loss: -380166.531250][G loss -329755.468750 ]time: 0:01:02.343514 
[Epoch 1/2000][Batch 38/48][Autoencoder loss: 1124.447144][C loss: 13.305892][M loss: 0.694217][D loss: -362461.031250][G loss -349758.187500 ]time: 0:01:02.783733 
[Epoch 1/2000][Batch 39/48][Autoencoder loss: 1148.714722][C loss: 12.917185][M loss: 0.694309][D loss: -337934.031250][G loss -374562.875000 ]time: 0:01:03.216774 
[Epoch 1/2000][Batch 40/48][Autoencoder loss: 1283.828979][C loss: 8.843613][M loss: 0.695173][D loss: -321154.343750][G loss -395459.468750 ]time: 0:01:03.660950 
[Epoch 1/2000][Batch 41/48][Autoencoder loss: 1241.440918][C loss: 7.658523][M loss: 0.695689][D loss: -312076.781250][G loss -416799.468750 ]time: 0:01:04.132599 
[Epoch 1/2000][Batch 42/48][Autoencoder loss: 1253.384766][C loss: 6.803802][M loss: 0.694903][D loss: -308811.187500][G loss -438037.062500 ]time: 0:01:04.568093 
[Epoch 1/2000][Batch 43/48][Autoencoder loss: 1073.198608][C loss: 6.268820][M loss: 0.695442][D loss: -269459.750000][G loss -450960.718750 ]time: 0:01:05.006786 
[Epoch 1/2000][Batch 44/48][Autoencoder loss: 799.955994][C loss: 16.540323][M loss: 0.694603][D loss: -296340.656250][G loss -443134.031250 ]time: 0:01:05.781285 
[Epoch 1/2000][Batch 45/48][Autoencoder loss: 690.375671][C loss: 14.540393][M loss: 0.694081][D loss: -282907.218750][G loss -456920.312500 ]time: 0:01:06.816806 
[Epoch 1/2000][Batch 46/48][Autoencoder loss: 542.132446][C loss: 12.152212][M loss: 0.694065][D loss: -265763.781250][G loss -469361.406250 ]time: 0:01:08.021687 
[Epoch 1/2000][Batch 47/48][Autoencoder loss: 418.386536][C loss: 9.263459][M loss: 0.694085][D loss: -237841.531250][G loss -497831.812500 ]time: 0:01:09.135726 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 526us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 495us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 494us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 1/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.333333] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.333333]
[Epoch 2/2000][Batch 0/48][Autoencoder loss: 851.828857][C loss: 13.390995][M loss: 0.698118][D loss: -97758.781250][G loss -646288.875000 ]time: 0:01:26.733335 
[Epoch 2/2000][Batch 1/48][Autoencoder loss: 638.568298][C loss: 13.703927][M loss: 0.693998][D loss: -63119.625000][G loss -656554.437500 ]time: 0:01:27.185405 
[Epoch 2/2000][Batch 2/48][Autoencoder loss: 553.433838][C loss: 13.549343][M loss: 0.694699][D loss: -36737.437500][G loss -682760.625000 ]time: 0:01:27.646606 
[Epoch 2/2000][Batch 3/48][Autoencoder loss: 532.731323][C loss: 13.051337][M loss: 0.694086][D loss: -9516.718750][G loss -686200.875000 ]time: 0:01:28.375131 
[Epoch 2/2000][Batch 4/48][Autoencoder loss: 779.294250][C loss: 12.435547][M loss: 0.695725][D loss: 2725.375000][G loss -694375.437500 ]time: 0:01:28.851133 
[Epoch 2/2000][Batch 5/48][Autoencoder loss: 1705.461182][C loss: 12.035354][M loss: 0.693983][D loss: 2767.343750][G loss -709760.125000 ]time: 0:01:29.287718 
[Epoch 2/2000][Batch 6/48][Autoencoder loss: 1394.256470][C loss: 10.565935][M loss: 0.693884][D loss: 34448.000000][G loss -738471.750000 ]time: 0:01:29.726758 
[Epoch 2/2000][Batch 7/48][Autoencoder loss: 956.764221][C loss: 8.787215][M loss: 0.693731][D loss: 52374.750000][G loss -713188.125000 ]time: 0:01:30.171905 
[Epoch 2/2000][Batch 8/48][Autoencoder loss: 469.105072][C loss: 8.767056][M loss: 0.693523][D loss: -111354.843750][G loss -554383.750000 ]time: 0:01:30.614485 
[Epoch 2/2000][Batch 9/48][Autoencoder loss: 512.969604][C loss: 8.339510][M loss: 0.693549][D loss: -77165.750000][G loss -558868.312500 ]time: 0:01:31.058685 
[Epoch 2/2000][Batch 10/48][Autoencoder loss: 516.656677][C loss: 8.166500][M loss: 0.693545][D loss: -69847.625000][G loss -561935.937500 ]time: 0:01:31.498360 
[Epoch 2/2000][Batch 11/48][Autoencoder loss: 473.362762][C loss: 7.447685][M loss: 0.694251][D loss: -70982.687500][G loss -563948.375000 ]time: 0:01:32.458598 
[Epoch 2/2000][Batch 12/48][Autoencoder loss: 363.814941][C loss: 9.090653][M loss: 0.693931][D loss: 59778.859375][G loss -684674.375000 ]time: 0:01:33.377099 
[Epoch 2/2000][Batch 13/48][Autoencoder loss: 358.499084][C loss: 8.466203][M loss: 0.693643][D loss: 64522.890625][G loss -675162.812500 ]time: 0:01:33.835851 
[Epoch 2/2000][Batch 14/48][Autoencoder loss: 375.245483][C loss: 7.786279][M loss: 0.694039][D loss: 91521.375000][G loss -666623.812500 ]time: 0:01:34.291999 
[Epoch 2/2000][Batch 15/48][Autoencoder loss: 382.878143][C loss: 6.855765][M loss: 0.693553][D loss: 102467.156250][G loss -672043.562500 ]time: 0:01:34.745623 
[Epoch 2/2000][Batch 16/48][Autoencoder loss: 587.472534][C loss: 5.590160][M loss: 0.693504][D loss: 64805.125000][G loss -642311.812500 ]time: 0:01:35.194072 
[Epoch 2/2000][Batch 17/48][Autoencoder loss: 385.989105][C loss: 4.266811][M loss: 0.693757][D loss: 81522.093750][G loss -627193.562500 ]time: 0:01:35.632245 
[Epoch 2/2000][Batch 18/48][Autoencoder loss: 249.762650][C loss: 3.886565][M loss: 0.693577][D loss: 81949.218750][G loss -613576.062500 ]time: 0:01:36.069713 
[Epoch 2/2000][Batch 19/48][Autoencoder loss: 226.331375][C loss: 3.713002][M loss: 0.693429][D loss: 104822.250000][G loss -607952.812500 ]time: 0:01:36.510752 
[Epoch 2/2000][Batch 20/48][Autoencoder loss: 2531.972900][C loss: 6.232104][M loss: 0.694941][D loss: 117150.171875][G loss -619868.875000 ]time: 0:01:36.962498 
[Epoch 2/2000][Batch 21/48][Autoencoder loss: 6816.333008][C loss: 4.991756][M loss: 0.699764][D loss: 116647.578125][G loss -614125.250000 ]time: 0:01:37.403108 
[Epoch 2/2000][Batch 22/48][Autoencoder loss: 5165.698242][C loss: 3.877252][M loss: 0.910318][D loss: 122911.921875][G loss -596151.937500 ]time: 0:01:37.838675 
[Epoch 2/2000][Batch 23/48][Autoencoder loss: 3367.031738][C loss: 2.900026][M loss: 0.771394][D loss: 140994.234375][G loss -601494.375000 ]time: 0:01:38.299552 
[Epoch 2/2000][Batch 24/48][Autoencoder loss: 3597.327881][C loss: 2.015354][M loss: 0.701287][D loss: 172770.609375][G loss -601728.875000 ]time: 0:01:39.249643 
[Epoch 2/2000][Batch 25/48][Autoencoder loss: 3724.696045][C loss: 1.855141][M loss: 0.699531][D loss: 179426.640625][G loss -594569.312500 ]time: 0:01:40.280072 
[Epoch 2/2000][Batch 26/48][Autoencoder loss: 4630.284180][C loss: 1.187168][M loss: 0.700748][D loss: 178587.687500][G loss -576898.750000 ]time: 0:01:41.214567 
[Epoch 2/2000][Batch 27/48][Autoencoder loss: 4038.227051][C loss: 0.487287][M loss: 0.698783][D loss: 163451.000000][G loss -553610.812500 ]time: 0:01:41.646568 
[Epoch 2/2000][Batch 28/48][Autoencoder loss: 3978.669189][C loss: 9.122010][M loss: 0.697871][D loss: 241905.093750][G loss -606558.250000 ]time: 0:01:42.558340 
[Epoch 2/2000][Batch 29/48][Autoencoder loss: 2790.563965][C loss: 8.690276][M loss: 0.698995][D loss: 244796.562500][G loss -582119.625000 ]time: 0:01:42.994418 
[Epoch 2/2000][Batch 30/48][Autoencoder loss: 2785.978760][C loss: 7.450719][M loss: 0.695210][D loss: 237093.234375][G loss -558593.312500 ]time: 0:01:43.438828 
[Epoch 2/2000][Batch 31/48][Autoencoder loss: 1630.758057][C loss: 5.801100][M loss: 0.696328][D loss: 222762.812500][G loss -546189.437500 ]time: 0:01:44.127682 
[Epoch 2/2000][Batch 32/48][Autoencoder loss: 1394.615601][C loss: 10.158465][M loss: 0.695430][D loss: 98414.789062][G loss -390577.156250 ]time: 0:01:45.343211 
[Epoch 2/2000][Batch 33/48][Autoencoder loss: 1144.534302][C loss: 9.889384][M loss: 0.694908][D loss: 97477.593750][G loss -389089.687500 ]time: 0:01:45.790386 
[Epoch 2/2000][Batch 34/48][Autoencoder loss: 1183.157471][C loss: 8.411063][M loss: 0.694774][D loss: 83033.609375][G loss -367599.125000 ]time: 0:01:46.236744 
[Epoch 2/2000][Batch 35/48][Autoencoder loss: 1062.626221][C loss: 5.631640][M loss: 0.694807][D loss: 81524.320312][G loss -334341.656250 ]time: 0:01:46.673682 
[Epoch 2/2000][Batch 36/48][Autoencoder loss: 972.943787][C loss: 6.759667][M loss: 0.694182][D loss: 90868.234375][G loss -339273.718750 ]time: 0:01:47.118121 
[Epoch 2/2000][Batch 37/48][Autoencoder loss: 958.630737][C loss: 5.297093][M loss: 0.693918][D loss: 85356.375000][G loss -312727.937500 ]time: 0:01:47.557318 
[Epoch 2/2000][Batch 38/48][Autoencoder loss: 994.735535][C loss: 5.289810][M loss: 0.693898][D loss: 83124.703125][G loss -305188.343750 ]time: 0:01:48.021314 
[Epoch 2/2000][Batch 39/48][Autoencoder loss: 1004.337097][C loss: 5.057203][M loss: 0.693761][D loss: 80923.742188][G loss -286005.625000 ]time: 0:01:48.500213 
[Epoch 2/2000][Batch 40/48][Autoencoder loss: 1148.548462][C loss: 5.623481][M loss: 0.694052][D loss: 84209.828125][G loss -275907.593750 ]time: 0:01:48.940011 
[Epoch 2/2000][Batch 41/48][Autoencoder loss: 1169.974976][C loss: 4.985631][M loss: 0.694174][D loss: 87856.000000][G loss -259218.171875 ]time: 0:01:49.382435 
[Epoch 2/2000][Batch 42/48][Autoencoder loss: 1149.490601][C loss: 4.112555][M loss: 0.693843][D loss: 68041.750000][G loss -247292.812500 ]time: 0:01:49.823719 
[Epoch 2/2000][Batch 43/48][Autoencoder loss: 906.123596][C loss: 3.165654][M loss: 0.693632][D loss: 69970.828125][G loss -232823.343750 ]time: 0:01:50.261564 
[Epoch 2/2000][Batch 44/48][Autoencoder loss: 690.160950][C loss: 6.113006][M loss: 0.693891][D loss: 43446.339844][G loss -197636.031250 ]time: 0:01:50.696616 
[Epoch 2/2000][Batch 45/48][Autoencoder loss: 597.824768][C loss: 5.829136][M loss: 0.693816][D loss: 41283.566406][G loss -185210.437500 ]time: 0:01:51.195157 
[Epoch 2/2000][Batch 46/48][Autoencoder loss: 508.270386][C loss: 5.019489][M loss: 0.693806][D loss: 35402.796875][G loss -166670.937500 ]time: 0:01:51.637046 
[Epoch 2/2000][Batch 47/48][Autoencoder loss: 439.150238][C loss: 4.028444][M loss: 0.693715][D loss: 34932.039062][G loss -160101.359375 ]time: 0:01:52.073901 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 428us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 474us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 445us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 2/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.340625] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.333333]
[Epoch 3/2000][Batch 0/48][Autoencoder loss: 800.010010][C loss: 8.224336][M loss: 0.693717][D loss: 94329.187500][G loss -201063.343750 ]time: 0:02:14.753234 
[Epoch 3/2000][Batch 1/48][Autoencoder loss: 573.516907][C loss: 8.356125][M loss: 0.693598][D loss: 82491.679688][G loss -182861.140625 ]time: 0:02:15.195268 
[Epoch 3/2000][Batch 2/48][Autoencoder loss: 499.557587][C loss: 8.262012][M loss: 0.693424][D loss: 71511.703125][G loss -160537.265625 ]time: 0:02:15.740510 
[Epoch 3/2000][Batch 3/48][Autoencoder loss: 485.025208][C loss: 7.925568][M loss: 0.693771][D loss: 65233.296875][G loss -145947.250000 ]time: 0:02:16.183950 
[Epoch 3/2000][Batch 4/48][Autoencoder loss: 747.726807][C loss: 5.645675][M loss: 0.693918][D loss: 49354.398438][G loss -122583.125000 ]time: 0:02:16.629980 
[Epoch 3/2000][Batch 5/48][Autoencoder loss: 1632.996582][C loss: 5.701011][M loss: 0.693553][D loss: 39942.988281][G loss -105866.570312 ]time: 0:02:17.071169 
[Epoch 3/2000][Batch 6/48][Autoencoder loss: 1293.417236][C loss: 5.508222][M loss: 0.693553][D loss: 29777.250000][G loss -83546.734375 ]time: 0:02:17.503636 
[Epoch 3/2000][Batch 7/48][Autoencoder loss: 873.098511][C loss: 5.126252][M loss: 0.693506][D loss: 20490.210938][G loss -65054.199219 ]time: 0:02:17.960601 
[Epoch 3/2000][Batch 8/48][Autoencoder loss: 499.635590][C loss: 8.371242][M loss: 0.693532][D loss: -6540.955078][G loss -36385.500000 ]time: 0:02:18.428407 
[Epoch 3/2000][Batch 9/48][Autoencoder loss: 541.651672][C loss: 7.475978][M loss: 0.693502][D loss: -13467.562500][G loss -23961.246094 ]time: 0:02:19.414843 
[Epoch 3/2000][Batch 10/48][Autoencoder loss: 502.735016][C loss: 6.007195][M loss: 0.693485][D loss: -17635.750000][G loss -13634.305664 ]time: 0:02:20.552762 
[Epoch 3/2000][Batch 11/48][Autoencoder loss: 454.229797][C loss: 5.209648][M loss: 0.693537][D loss: -21854.931641][G loss -3251.679443 ]time: 0:02:21.295497 
[Epoch 3/2000][Batch 12/48][Autoencoder loss: 411.456696][C loss: 3.472081][M loss: 0.693570][D loss: -23749.005859][G loss 8646.168945 ]time: 0:02:21.794293 
[Epoch 3/2000][Batch 13/48][Autoencoder loss: 428.457153][C loss: 2.950701][M loss: 0.693480][D loss: -30269.263672][G loss 21490.587891 ]time: 0:02:22.230171 
[Epoch 3/2000][Batch 14/48][Autoencoder loss: 423.643158][C loss: 2.580830][M loss: 0.693501][D loss: -36124.609375][G loss 33017.109375 ]time: 0:02:22.672377 
[Epoch 3/2000][Batch 15/48][Autoencoder loss: 389.818817][C loss: 2.553751][M loss: 0.693446][D loss: -41665.179688][G loss 44458.750000 ]time: 0:02:23.120138 
[Epoch 3/2000][Batch 16/48][Autoencoder loss: 557.973938][C loss: 2.495550][M loss: 0.693516][D loss: -45853.304688][G loss 50668.632812 ]time: 0:02:23.559277 
[Epoch 3/2000][Batch 17/48][Autoencoder loss: 336.010773][C loss: 2.273903][M loss: 0.693446][D loss: -48490.609375][G loss 61244.066406 ]time: 0:02:24.003883 
[Epoch 3/2000][Batch 18/48][Autoencoder loss: 218.432755][C loss: 1.618255][M loss: 0.693497][D loss: -55363.500000][G loss 70129.484375 ]time: 0:02:24.438176 
[Epoch 3/2000][Batch 19/48][Autoencoder loss: 215.737106][C loss: 1.057559][M loss: 0.693447][D loss: -55220.964844][G loss 78159.789062 ]time: 0:02:24.875335 
[Epoch 3/2000][Batch 20/48][Autoencoder loss: 2468.286377][C loss: 3.083364][M loss: 0.694015][D loss: -68410.742188][G loss 92604.929688 ]time: 0:02:25.314095 
[Epoch 3/2000][Batch 21/48][Autoencoder loss: 6521.213379][C loss: 2.375912][M loss: 0.939068][D loss: -74270.164062][G loss 99701.429688 ]time: 0:02:25.952462 
[Epoch 3/2000][Batch 22/48][Autoencoder loss: 4835.556152][C loss: 2.393967][M loss: 0.737961][D loss: -77436.218750][G loss 109895.468750 ]time: 0:02:26.414955 
[Epoch 3/2000][Batch 23/48][Autoencoder loss: 3025.624023][C loss: 2.212710][M loss: 0.701944][D loss: -79863.062500][G loss 119484.085938 ]time: 0:02:26.850174 
[Epoch 3/2000][Batch 24/48][Autoencoder loss: 3982.438721][C loss: 5.908201][M loss: 0.702728][D loss: -91855.281250][G loss 133692.750000 ]time: 0:02:27.314681 
[Epoch 3/2000][Batch 25/48][Autoencoder loss: 4168.047363][C loss: 5.349797][M loss: 0.698155][D loss: -97546.562500][G loss 145996.703125 ]time: 0:02:27.749874 
[Epoch 3/2000][Batch 26/48][Autoencoder loss: 5171.417480][C loss: 4.915976][M loss: 0.696253][D loss: -101706.859375][G loss 154138.937500 ]time: 0:02:28.590007 
[Epoch 3/2000][Batch 27/48][Autoencoder loss: 4283.873535][C loss: 3.734933][M loss: 0.696207][D loss: -101159.140625][G loss 163842.468750 ]time: 0:02:29.597605 
[Epoch 3/2000][Batch 28/48][Autoencoder loss: 4280.783203][C loss: 6.181211][M loss: 0.697776][D loss: -136642.984375][G loss 201998.484375 ]time: 0:02:30.403075 
[Epoch 3/2000][Batch 29/48][Autoencoder loss: 2905.622803][C loss: 5.169617][M loss: 0.701672][D loss: -143397.531250][G loss 217235.328125 ]time: 0:02:31.443273 
[Epoch 3/2000][Batch 30/48][Autoencoder loss: 2610.809082][C loss: 4.798095][M loss: 0.695623][D loss: -146728.265625][G loss 227604.984375 ]time: 0:02:31.975893 
[Epoch 3/2000][Batch 31/48][Autoencoder loss: 1635.723877][C loss: 4.367824][M loss: 0.694725][D loss: -155495.015625][G loss 244785.593750 ]time: 0:02:32.417192 
[Epoch 3/2000][Batch 32/48][Autoencoder loss: 1302.833862][C loss: 6.192556][M loss: 0.695044][D loss: -100413.500000][G loss 195255.109375 ]time: 0:02:32.868982 
[Epoch 3/2000][Batch 33/48][Autoencoder loss: 1000.193909][C loss: 6.062372][M loss: 0.695000][D loss: -108458.398438][G loss 202623.859375 ]time: 0:02:33.308361 
[Epoch 3/2000][Batch 34/48][Autoencoder loss: 1220.177856][C loss: 5.131406][M loss: 0.695181][D loss: -114697.093750][G loss 219380.234375 ]time: 0:02:33.749408 
[Epoch 3/2000][Batch 35/48][Autoencoder loss: 1007.290771][C loss: 4.232847][M loss: 0.695207][D loss: -119272.546875][G loss 230469.296875 ]time: 0:02:34.196058 
[Epoch 3/2000][Batch 36/48][Autoencoder loss: 1082.194214][C loss: 6.099418][M loss: 0.694629][D loss: -126549.171875][G loss 242685.937500 ]time: 0:02:34.643345 
[Epoch 3/2000][Batch 37/48][Autoencoder loss: 1256.923096][C loss: 4.555449][M loss: 0.694124][D loss: -129006.789062][G loss 252692.812500 ]time: 0:02:35.079466 
[Epoch 3/2000][Batch 38/48][Autoencoder loss: 1186.254761][C loss: 4.100256][M loss: 0.694160][D loss: -138459.843750][G loss 266574.437500 ]time: 0:02:35.523030 
[Epoch 3/2000][Batch 39/48][Autoencoder loss: 1165.670166][C loss: 3.919984][M loss: 0.693794][D loss: -135983.500000][G loss 269417.812500 ]time: 0:02:35.973070 
[Epoch 3/2000][Batch 40/48][Autoencoder loss: 1206.340210][C loss: 3.400291][M loss: 0.695201][D loss: -135580.765625][G loss 290766.343750 ]time: 0:02:36.407758 
[Epoch 3/2000][Batch 41/48][Autoencoder loss: 1069.236450][C loss: 3.008790][M loss: 0.694548][D loss: -152464.531250][G loss 307132.937500 ]time: 0:02:36.873989 
[Epoch 3/2000][Batch 42/48][Autoencoder loss: 1125.408691][C loss: 2.626950][M loss: 0.693904][D loss: -157902.109375][G loss 323126.812500 ]time: 0:02:37.400892 
[Epoch 3/2000][Batch 43/48][Autoencoder loss: 908.793335][C loss: 2.278237][M loss: 0.693691][D loss: -162247.421875][G loss 339157.062500 ]time: 0:02:37.850699 
[Epoch 3/2000][Batch 44/48][Autoencoder loss: 694.657959][C loss: 4.937335][M loss: 0.694344][D loss: -139338.453125][G loss 324086.781250 ]time: 0:02:38.296538 
[Epoch 3/2000][Batch 45/48][Autoencoder loss: 667.260498][C loss: 4.296952][M loss: 0.693910][D loss: -136498.828125][G loss 336874.187500 ]time: 0:02:38.843408 
[Epoch 3/2000][Batch 46/48][Autoencoder loss: 568.746094][C loss: 3.460563][M loss: 0.693740][D loss: -144555.515625][G loss 340925.000000 ]time: 0:02:39.497759 
[Epoch 3/2000][Batch 47/48][Autoencoder loss: 525.334900][C loss: 2.913704][M loss: 0.693688][D loss: -141384.453125][G loss 362589.093750 ]time: 0:02:39.965705 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 464us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 470us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 531us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 3/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.340625] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.333333]
[Epoch 4/2000][Batch 0/48][Autoencoder loss: 891.073608][C loss: 7.780845][M loss: 0.694158][D loss: -292385.562500][G loss 509656.781250 ]time: 0:02:55.902422 
[Epoch 4/2000][Batch 1/48][Autoencoder loss: 632.449768][C loss: 7.355655][M loss: 0.694064][D loss: -298445.968750][G loss 529976.437500 ]time: 0:02:56.347092 
[Epoch 4/2000][Batch 2/48][Autoencoder loss: 535.791748][C loss: 6.830800][M loss: 0.693593][D loss: -306935.000000][G loss 559770.750000 ]time: 0:02:56.783750 
[Epoch 4/2000][Batch 3/48][Autoencoder loss: 506.789581][C loss: 6.747577][M loss: 0.693501][D loss: -317253.937500][G loss 576612.125000 ]time: 0:02:57.231972 
[Epoch 4/2000][Batch 4/48][Autoencoder loss: 813.827271][C loss: 5.573637][M loss: 0.693525][D loss: -308658.562500][G loss 604530.437500 ]time: 0:02:57.675926 
[Epoch 4/2000][Batch 5/48][Autoencoder loss: 1716.995605][C loss: 5.416455][M loss: 0.693476][D loss: -321822.000000][G loss 631869.562500 ]time: 0:02:58.118495 
[Epoch 4/2000][Batch 6/48][Autoencoder loss: 1291.730469][C loss: 4.959399][M loss: 0.693587][D loss: -307866.125000][G loss 659524.250000 ]time: 0:02:58.562468 
[Epoch 4/2000][Batch 7/48][Autoencoder loss: 795.876709][C loss: 4.284036][M loss: 0.693693][D loss: -326502.281250][G loss 681292.312500 ]time: 0:02:58.996335 
[Epoch 4/2000][Batch 8/48][Autoencoder loss: 483.960938][C loss: 4.198538][M loss: 0.693384][D loss: -155127.500000][G loss 513829.062500 ]time: 0:02:59.454271 
[Epoch 4/2000][Batch 9/48][Autoencoder loss: 536.461792][C loss: 4.078363][M loss: 0.693412][D loss: -166385.031250][G loss 530462.875000 ]time: 0:03:00.257684 
[Epoch 4/2000][Batch 10/48][Autoencoder loss: 559.991150][C loss: 3.730609][M loss: 0.693453][D loss: -160182.343750][G loss 539154.312500 ]time: 0:03:01.351673 
[Epoch 4/2000][Batch 11/48][Autoencoder loss: 558.325928][C loss: 3.291787][M loss: 0.693387][D loss: -159092.562500][G loss 569036.000000 ]time: 0:03:02.449320 
[Epoch 4/2000][Batch 12/48][Autoencoder loss: 514.759705][C loss: 3.688113][M loss: 0.693372][D loss: -264146.687500][G loss 688977.062500 ]time: 0:03:02.895096 
[Epoch 4/2000][Batch 13/48][Autoencoder loss: 515.627991][C loss: 3.186049][M loss: 0.693361][D loss: -252299.218750][G loss 702599.625000 ]time: 0:03:03.604337 
[Epoch 4/2000][Batch 14/48][Autoencoder loss: 449.653717][C loss: 2.383257][M loss: 0.693464][D loss: -241881.937500][G loss 713306.562500 ]time: 0:03:04.629431 
[Epoch 4/2000][Batch 15/48][Autoencoder loss: 402.910095][C loss: 1.947686][M loss: 0.693463][D loss: -244050.640625][G loss 742618.375000 ]time: 0:03:05.087076 
[Epoch 4/2000][Batch 16/48][Autoencoder loss: 589.836792][C loss: 1.365655][M loss: 0.693382][D loss: -181506.968750][G loss 693683.312500 ]time: 0:03:05.528178 
[Epoch 4/2000][Batch 17/48][Autoencoder loss: 363.182617][C loss: 1.619631][M loss: 0.693370][D loss: -198434.734375][G loss 701787.875000 ]time: 0:03:05.968276 
[Epoch 4/2000][Batch 18/48][Autoencoder loss: 257.561951][C loss: 1.606054][M loss: 0.693371][D loss: -189657.968750][G loss 700856.937500 ]time: 0:03:06.421021 
[Epoch 4/2000][Batch 19/48][Autoencoder loss: 233.605301][C loss: 1.440784][M loss: 0.693365][D loss: -162938.125000][G loss 717775.875000 ]time: 0:03:06.878789 
[Epoch 4/2000][Batch 20/48][Autoencoder loss: 2200.871094][C loss: 3.392624][M loss: 0.693623][D loss: -176892.468750][G loss 755271.437500 ]time: 0:03:07.323779 
[Epoch 4/2000][Batch 21/48][Autoencoder loss: 5996.233398][C loss: 2.207223][M loss: 0.709036][D loss: -151763.328125][G loss 751013.562500 ]time: 0:03:07.767172 
[Epoch 4/2000][Batch 22/48][Autoencoder loss: 4435.767578][C loss: 1.329446][M loss: 0.884463][D loss: -137438.984375][G loss 751918.875000 ]time: 0:03:08.352686 
[Epoch 4/2000][Batch 23/48][Autoencoder loss: 2784.925049][C loss: 1.981430][M loss: 0.699279][D loss: -150096.312500][G loss 729742.437500 ]time: 0:03:09.003305 
[Epoch 4/2000][Batch 24/48][Autoencoder loss: 4007.951660][C loss: 4.071749][M loss: 0.696644][D loss: -173465.031250][G loss 786948.187500 ]time: 0:03:09.439406 
[Epoch 4/2000][Batch 25/48][Autoencoder loss: 4254.504883][C loss: 3.704454][M loss: 0.696097][D loss: -160416.468750][G loss 783539.062500 ]time: 0:03:09.877779 
[Epoch 4/2000][Batch 26/48][Autoencoder loss: 5147.960449][C loss: 2.624011][M loss: 0.695459][D loss: -111622.703125][G loss 751160.687500 ]time: 0:03:10.329333 
[Epoch 4/2000][Batch 27/48][Autoencoder loss: 4241.595703][C loss: 1.756620][M loss: 0.695248][D loss: -110586.328125][G loss 759002.125000 ]time: 0:03:10.804682 
[Epoch 4/2000][Batch 28/48][Autoencoder loss: 4335.448242][C loss: 4.304271][M loss: 0.694733][D loss: -199319.312500][G loss 864993.437500 ]time: 0:03:11.257323 
[Epoch 4/2000][Batch 29/48][Autoencoder loss: 2724.813965][C loss: 3.492186][M loss: 0.694447][D loss: -178320.359375][G loss 857817.625000 ]time: 0:03:11.699396 
[Epoch 4/2000][Batch 30/48][Autoencoder loss: 2487.592773][C loss: 3.375890][M loss: 0.694840][D loss: -155393.312500][G loss 856464.000000 ]time: 0:03:12.128330 
[Epoch 4/2000][Batch 31/48][Autoencoder loss: 1629.029175][C loss: 3.094753][M loss: 0.694477][D loss: -117555.250000][G loss 833811.312500 ]time: 0:03:12.560372 
[Epoch 4/2000][Batch 32/48][Autoencoder loss: 1181.950317][C loss: 4.426992][M loss: 0.694750][D loss: 86906.921875][G loss 622273.937500 ]time: 0:03:13.001494 
[Epoch 4/2000][Batch 33/48][Autoencoder loss: 1079.598511][C loss: 3.955444][M loss: 0.693938][D loss: 103837.343750][G loss 597553.312500 ]time: 0:03:13.836019 
[Epoch 4/2000][Batch 34/48][Autoencoder loss: 1189.181274][C loss: 4.063324][M loss: 0.694166][D loss: 113737.984375][G loss 602704.750000 ]time: 0:03:14.731709 
[Epoch 4/2000][Batch 35/48][Autoencoder loss: 996.444641][C loss: 4.020053][M loss: 0.693932][D loss: 129245.812500][G loss 572038.687500 ]time: 0:03:15.630414 
[Epoch 4/2000][Batch 36/48][Autoencoder loss: 1184.806152][C loss: 6.896260][M loss: 0.693894][D loss: 135448.296875][G loss 563537.750000 ]time: 0:03:16.647708 
[Epoch 4/2000][Batch 37/48][Autoencoder loss: 1220.925293][C loss: 6.187326][M loss: 0.693864][D loss: 161185.250000][G loss 526667.812500 ]time: 0:03:17.729368 
[Epoch 4/2000][Batch 38/48][Autoencoder loss: 1201.128174][C loss: 5.150652][M loss: 0.693641][D loss: 157936.546875][G loss 515359.875000 ]time: 0:03:18.547768 
[Epoch 4/2000][Batch 39/48][Autoencoder loss: 1184.216431][C loss: 3.670276][M loss: 0.693617][D loss: 167721.703125][G loss 481420.000000 ]time: 0:03:18.999624 
[Epoch 4/2000][Batch 40/48][Autoencoder loss: 1207.797974][C loss: 2.767261][M loss: 0.693693][D loss: 174121.703125][G loss 454770.875000 ]time: 0:03:19.448526 
[Epoch 4/2000][Batch 41/48][Autoencoder loss: 1171.961548][C loss: 2.846316][M loss: 0.693619][D loss: 168147.390625][G loss 435659.343750 ]time: 0:03:19.892665 
[Epoch 4/2000][Batch 42/48][Autoencoder loss: 1086.588379][C loss: 3.033328][M loss: 0.693812][D loss: 187136.484375][G loss 409436.875000 ]time: 0:03:20.344028 
[Epoch 4/2000][Batch 43/48][Autoencoder loss: 858.664307][C loss: 2.950292][M loss: 0.693543][D loss: 180221.687500][G loss 385564.593750 ]time: 0:03:20.798899 
[Epoch 4/2000][Batch 44/48][Autoencoder loss: 663.793335][C loss: 5.489481][M loss: 0.693527][D loss: 205770.843750][G loss 345045.812500 ]time: 0:03:21.243745 
[Epoch 4/2000][Batch 45/48][Autoencoder loss: 584.963135][C loss: 4.636801][M loss: 0.693497][D loss: 197689.421875][G loss 320110.562500 ]time: 0:03:21.684946 
[Epoch 4/2000][Batch 46/48][Autoencoder loss: 567.140747][C loss: 3.603341][M loss: 0.693593][D loss: 199876.218750][G loss 309033.500000 ]time: 0:03:22.151284 
[Epoch 4/2000][Batch 47/48][Autoencoder loss: 512.217102][C loss: 2.720398][M loss: 0.693501][D loss: 195209.140625][G loss 283507.593750 ]time: 0:03:22.603465 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 3s
63/63 [==============================] - 0s 572us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
63/63 [==============================] - 0s 598us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
63/63 [==============================] - 0s 675us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 4/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.340625] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.333333]
[Epoch 5/2000][Batch 0/48][Autoencoder loss: 821.260620][C loss: 4.951565][M loss: 0.693497][D loss: 105046.265625][G loss 345898.406250 ]time: 0:03:39.755616 
[Epoch 5/2000][Batch 1/48][Autoencoder loss: 678.441956][C loss: 5.153737][M loss: 0.693529][D loss: 108739.015625][G loss 331133.937500 ]time: 0:03:40.217384 
[Epoch 5/2000][Batch 2/48][Autoencoder loss: 510.863495][C loss: 5.081781][M loss: 0.693451][D loss: 110421.859375][G loss 298611.812500 ]time: 0:03:40.669434 
[Epoch 5/2000][Batch 3/48][Autoencoder loss: 510.563965][C loss: 4.921628][M loss: 0.693427][D loss: 107546.414062][G loss 289644.156250 ]time: 0:03:41.145682 
[Epoch 5/2000][Batch 4/48][Autoencoder loss: 891.272278][C loss: 3.663317][M loss: 0.693435][D loss: 112862.109375][G loss 272175.218750 ]time: 0:03:41.622898 
[Epoch 5/2000][Batch 5/48][Autoencoder loss: 1780.758301][C loss: 3.253724][M loss: 0.693490][D loss: 110266.328125][G loss 253754.734375 ]time: 0:03:42.096436 
[Epoch 5/2000][Batch 6/48][Autoencoder loss: 1314.760620][C loss: 3.039112][M loss: 0.693433][D loss: 105077.859375][G loss 238971.906250 ]time: 0:03:42.552715 
[Epoch 5/2000][Batch 7/48][Autoencoder loss: 778.541382][C loss: 2.794892][M loss: 0.693420][D loss: 105432.718750][G loss 226378.765625 ]time: 0:03:42.994309 
[Epoch 5/2000][Batch 8/48][Autoencoder loss: 510.987518][C loss: 3.855311][M loss: 0.693375][D loss: 147792.203125][G loss 162024.468750 ]time: 0:03:43.435263 
[Epoch 5/2000][Batch 9/48][Autoencoder loss: 597.843384][C loss: 3.937928][M loss: 0.693380][D loss: 149819.875000][G loss 151928.796875 ]time: 0:03:43.880725 
[Epoch 5/2000][Batch 10/48][Autoencoder loss: 600.306274][C loss: 3.726203][M loss: 0.693366][D loss: 141749.875000][G loss 141810.531250 ]time: 0:03:44.361784 
[Epoch 5/2000][Batch 11/48][Autoencoder loss: 589.220337][C loss: 3.259645][M loss: 0.693422][D loss: 133733.750000][G loss 136326.718750 ]time: 0:03:44.832441 
[Epoch 5/2000][Batch 12/48][Autoencoder loss: 550.335571][C loss: 3.312907][M loss: 0.693418][D loss: 108704.609375][G loss 148968.531250 ]time: 0:03:45.270278 
[Epoch 5/2000][Batch 13/48][Autoencoder loss: 551.774719][C loss: 2.427940][M loss: 0.693393][D loss: 103460.968750][G loss 144097.703125 ]time: 0:03:45.708758 
[Epoch 5/2000][Batch 14/48][Autoencoder loss: 485.396942][C loss: 1.299492][M loss: 0.693361][D loss: 100499.992188][G loss 134546.828125 ]time: 0:03:46.149209 
[Epoch 5/2000][Batch 15/48][Autoencoder loss: 439.523560][C loss: 1.056410][M loss: 0.693351][D loss: 92540.734375][G loss 123152.406250 ]time: 0:03:46.591851 
[Epoch 5/2000][Batch 16/48][Autoencoder loss: 636.815613][C loss: 1.332299][M loss: 0.693450][D loss: 98379.218750][G loss 115468.898438 ]time: 0:03:47.037389 
[Epoch 5/2000][Batch 17/48][Autoencoder loss: 412.924866][C loss: 1.168707][M loss: 0.693371][D loss: 93876.046875][G loss 107012.281250 ]time: 0:03:47.637130 
[Epoch 5/2000][Batch 18/48][Autoencoder loss: 314.488617][C loss: 0.739796][M loss: 0.693343][D loss: 87450.085938][G loss 103598.414062 ]time: 0:03:48.667217 
[Epoch 5/2000][Batch 19/48][Autoencoder loss: 279.582092][C loss: 0.603568][M loss: 0.693368][D loss: 83160.007812][G loss 96169.343750 ]time: 0:03:49.507646 
[Epoch 5/2000][Batch 20/48][Autoencoder loss: 2187.836914][C loss: 2.527514][M loss: 0.693608][D loss: 81415.398438][G loss 92764.000000 ]time: 0:03:50.542458 
[Epoch 5/2000][Batch 21/48][Autoencoder loss: 5937.163086][C loss: 1.863913][M loss: 0.694863][D loss: 80632.429688][G loss 85016.648438 ]time: 0:03:51.511183 
[Epoch 5/2000][Batch 22/48][Autoencoder loss: 4450.048340][C loss: 1.730878][M loss: 0.857043][D loss: 76403.062500][G loss 81900.367188 ]time: 0:03:52.064900 
[Epoch 5/2000][Batch 23/48][Autoencoder loss: 2725.466553][C loss: 1.473356][M loss: 0.695113][D loss: 69867.773438][G loss 76974.804688 ]time: 0:03:52.498643 
[Epoch 5/2000][Batch 24/48][Autoencoder loss: 4005.159912][C loss: 3.639598][M loss: 0.696460][D loss: 60208.156250][G loss 74274.015625 ]time: 0:03:52.944685 
[Epoch 5/2000][Batch 25/48][Autoencoder loss: 4419.100098][C loss: 2.860950][M loss: 0.695148][D loss: 57361.226562][G loss 71338.500000 ]time: 0:03:53.623533 
[Epoch 5/2000][Batch 26/48][Autoencoder loss: 5306.672852][C loss: 2.031774][M loss: 0.694310][D loss: 53254.820312][G loss 67567.828125 ]time: 0:03:54.064923 
[Epoch 5/2000][Batch 27/48][Autoencoder loss: 4336.846191][C loss: 1.831544][M loss: 0.694150][D loss: 52333.308594][G loss 62093.476562 ]time: 0:03:54.502209 
[Epoch 5/2000][Batch 28/48][Autoencoder loss: 4533.608398][C loss: 4.613168][M loss: 0.694452][D loss: 43481.238281][G loss 65362.207031 ]time: 0:03:54.954491 
[Epoch 5/2000][Batch 29/48][Autoencoder loss: 2703.509277][C loss: 4.335928][M loss: 0.695311][D loss: 39078.222656][G loss 61111.644531 ]time: 0:03:55.441321 
[Epoch 5/2000][Batch 30/48][Autoencoder loss: 2633.572754][C loss: 3.960680][M loss: 0.694355][D loss: 36831.203125][G loss 58594.871094 ]time: 0:03:55.875065 
[Epoch 5/2000][Batch 31/48][Autoencoder loss: 1667.687500][C loss: 3.250746][M loss: 0.693883][D loss: 35630.921875][G loss 53705.628906 ]time: 0:03:56.317127 
[Epoch 5/2000][Batch 32/48][Autoencoder loss: 1221.015137][C loss: 3.649026][M loss: 0.694141][D loss: 43564.164062][G loss 40754.355469 ]time: 0:03:56.753488 
[Epoch 5/2000][Batch 33/48][Autoencoder loss: 1180.106567][C loss: 2.963073][M loss: 0.693985][D loss: 41251.351562][G loss 39191.480469 ]time: 0:03:57.189830 
[Epoch 5/2000][Batch 34/48][Autoencoder loss: 1104.244019][C loss: 2.733876][M loss: 0.694091][D loss: 36557.171875][G loss 35121.964844 ]time: 0:03:57.651917 
[Epoch 5/2000][Batch 35/48][Autoencoder loss: 1003.584106][C loss: 2.834815][M loss: 0.693707][D loss: 33147.175781][G loss 33070.558594 ]time: 0:03:58.127815 
[Epoch 5/2000][Batch 36/48][Autoencoder loss: 1127.512329][C loss: 5.421029][M loss: 0.693638][D loss: 28637.169922][G loss 29901.035156 ]time: 0:03:58.874765 
[Epoch 5/2000][Batch 37/48][Autoencoder loss: 1213.192261][C loss: 4.991188][M loss: 0.693671][D loss: 25676.693359][G loss 28131.162109 ]time: 0:04:00.043956 
[Epoch 5/2000][Batch 38/48][Autoencoder loss: 1354.648438][C loss: 4.200798][M loss: 0.693641][D loss: 21698.625000][G loss 25753.230469 ]time: 0:04:00.876555 
[Epoch 5/2000][Batch 39/48][Autoencoder loss: 1205.838623][C loss: 3.347576][M loss: 0.693474][D loss: 18943.689453][G loss 23133.500000 ]time: 0:04:01.332752 
[Epoch 5/2000][Batch 40/48][Autoencoder loss: 1276.250366][C loss: 3.508548][M loss: 0.693657][D loss: 15500.269531][G loss 20747.224609 ]time: 0:04:01.772815 
[Epoch 5/2000][Batch 41/48][Autoencoder loss: 1172.596069][C loss: 3.731786][M loss: 0.693610][D loss: 11602.872070][G loss 17696.224609 ]time: 0:04:02.229994 
[Epoch 5/2000][Batch 42/48][Autoencoder loss: 1110.586060][C loss: 3.240355][M loss: 0.693408][D loss: 8436.831055][G loss 15665.454102 ]time: 0:04:02.671264 
[Epoch 5/2000][Batch 43/48][Autoencoder loss: 972.862366][C loss: 2.201161][M loss: 0.693370][D loss: 5100.793945][G loss 13002.125000 ]time: 0:04:03.110468 
[Epoch 5/2000][Batch 44/48][Autoencoder loss: 691.193054][C loss: 2.941038][M loss: 0.693388][D loss: 3885.788086][G loss 10270.251953 ]time: 0:04:03.559312 
[Epoch 5/2000][Batch 45/48][Autoencoder loss: 683.916138][C loss: 2.965469][M loss: 0.693381][D loss: 28.014648][G loss 8765.510742 ]time: 0:04:04.003152 
[Epoch 5/2000][Batch 46/48][Autoencoder loss: 614.372375][C loss: 2.944656][M loss: 0.693357][D loss: -685.541016][G loss 7442.638184 ]time: 0:04:04.450666 
[Epoch 5/2000][Batch 47/48][Autoencoder loss: 525.214600][C loss: 2.589061][M loss: 0.693356][D loss: -2056.967285][G loss 6465.445801 ]time: 0:04:04.891239 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 460us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 714us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
63/63 [==============================] - 0s 657us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 5/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.340625] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.333333]
[Epoch 6/2000][Batch 0/48][Autoencoder loss: 850.626404][C loss: 3.545817][M loss: 0.693684][D loss: -3273.995117][G loss 7537.337402 ]time: 0:04:23.005414 
[Epoch 6/2000][Batch 1/48][Autoencoder loss: 709.506409][C loss: 3.611051][M loss: 0.693442][D loss: -4280.579102][G loss 7194.216309 ]time: 0:04:23.799008 
[Epoch 6/2000][Batch 2/48][Autoencoder loss: 581.047607][C loss: 3.526942][M loss: 0.693326][D loss: -4290.608887][G loss 6830.234863 ]time: 0:04:24.434908 
[Epoch 6/2000][Batch 3/48][Autoencoder loss: 565.213074][C loss: 3.520485][M loss: 0.693339][D loss: -4092.861816][G loss 7154.148438 ]time: 0:04:25.391811 
[Epoch 6/2000][Batch 4/48][Autoencoder loss: 820.033569][C loss: 3.479822][M loss: 0.693335][D loss: -3422.038086][G loss 7624.154785 ]time: 0:04:25.835617 
[Epoch 6/2000][Batch 5/48][Autoencoder loss: 1687.267578][C loss: 3.138560][M loss: 0.693412][D loss: -4769.374023][G loss 8176.111328 ]time: 0:04:26.278783 
[Epoch 6/2000][Batch 6/48][Autoencoder loss: 1281.129395][C loss: 2.708946][M loss: 0.693333][D loss: -5006.987305][G loss 8888.518555 ]time: 0:04:26.718305 
[Epoch 6/2000][Batch 7/48][Autoencoder loss: 832.362854][C loss: 2.341415][M loss: 0.693369][D loss: -5724.623047][G loss 10026.887695 ]time: 0:04:27.163179 
[Epoch 6/2000][Batch 8/48][Autoencoder loss: 526.650085][C loss: 2.219509][M loss: 0.693339][D loss: -3209.460938][G loss 8330.376953 ]time: 0:04:27.612640 
[Epoch 6/2000][Batch 9/48][Autoencoder loss: 594.398376][C loss: 2.192000][M loss: 0.693340][D loss: -1550.327148][G loss 9001.584961 ]time: 0:04:28.054102 
[Epoch 6/2000][Batch 10/48][Autoencoder loss: 590.844788][C loss: 1.985089][M loss: 0.693353][D loss: 152.341797][G loss 9685.416992 ]time: 0:04:28.493091 
[Epoch 6/2000][Batch 11/48][Autoencoder loss: 525.448303][C loss: 1.694872][M loss: 0.693331][D loss: 975.470703][G loss 9206.452148 ]time: 0:04:28.952090 
[Epoch 6/2000][Batch 12/48][Autoencoder loss: 549.702148][C loss: 2.231176][M loss: 0.693325][D loss: -388.697266][G loss 10712.409180 ]time: 0:04:29.388434 
[Epoch 6/2000][Batch 13/48][Autoencoder loss: 551.668335][C loss: 1.627731][M loss: 0.693332][D loss: -844.071289][G loss 10281.506836 ]time: 0:04:29.841016 
[Epoch 6/2000][Batch 14/48][Autoencoder loss: 482.336914][C loss: 1.025705][M loss: 0.693330][D loss: -1614.549805][G loss 9490.235352 ]time: 0:04:30.331102 
[Epoch 6/2000][Batch 15/48][Autoencoder loss: 385.129486][C loss: 0.990428][M loss: 0.693307][D loss: -2654.273438][G loss 8598.920898 ]time: 0:04:30.760802 
[Epoch 6/2000][Batch 16/48][Autoencoder loss: 539.211121][C loss: 0.870468][M loss: 0.693317][D loss: -3680.799805][G loss 7055.047852 ]time: 0:04:31.255194 
[Epoch 6/2000][Batch 17/48][Autoencoder loss: 361.844788][C loss: 0.819791][M loss: 0.693320][D loss: -6113.338867][G loss 6218.599121 ]time: 0:04:31.691224 
[Epoch 6/2000][Batch 18/48][Autoencoder loss: 260.694214][C loss: 0.635865][M loss: 0.693320][D loss: -6529.677734][G loss 5584.395508 ]time: 0:04:32.126181 
[Epoch 6/2000][Batch 19/48][Autoencoder loss: 262.772980][C loss: 0.362320][M loss: 0.693313][D loss: -6906.637695][G loss 5046.235840 ]time: 0:04:32.574352 
[Epoch 6/2000][Batch 20/48][Autoencoder loss: 2238.209717][C loss: 2.447914][M loss: 0.693508][D loss: -7900.817383][G loss 4799.764160 ]time: 0:04:33.027995 
[Epoch 6/2000][Batch 21/48][Autoencoder loss: 5870.589355][C loss: 1.553927][M loss: 0.695541][D loss: -8132.520508][G loss 4508.754395 ]time: 0:04:33.474361 
[Epoch 6/2000][Batch 22/48][Autoencoder loss: 4375.388672][C loss: 1.282467][M loss: 0.740464][D loss: -9079.498047][G loss 4387.576172 ]time: 0:04:33.943378 
[Epoch 6/2000][Batch 23/48][Autoencoder loss: 2696.434082][C loss: 1.302711][M loss: 0.694377][D loss: -7787.115234][G loss 4145.002930 ]time: 0:04:35.059270 
[Epoch 6/2000][Batch 24/48][Autoencoder loss: 4121.684570][C loss: 3.227749][M loss: 0.694754][D loss: -7341.385742][G loss 4021.158691 ]time: 0:04:35.816676 
[Epoch 6/2000][Batch 25/48][Autoencoder loss: 4663.132324][C loss: 2.551928][M loss: 0.694078][D loss: -7529.927734][G loss 3350.185303 ]time: 0:04:36.599843 
[Epoch 6/2000][Batch 26/48][Autoencoder loss: 5384.310547][C loss: 1.802409][M loss: 0.693600][D loss: -9209.966797][G loss 2988.333252 ]time: 0:04:37.700764 
[Epoch 6/2000][Batch 27/48][Autoencoder loss: 4395.634766][C loss: 1.024373][M loss: 0.693655][D loss: -10486.175781][G loss 2506.080322 ]time: 0:04:38.683056 
[Epoch 6/2000][Batch 28/48][Autoencoder loss: 4778.430176][C loss: 3.979523][M loss: 0.693663][D loss: -10248.828125][G loss 2399.477051 ]time: 0:04:39.422941 
[Epoch 6/2000][Batch 29/48][Autoencoder loss: 2706.809326][C loss: 3.958696][M loss: 0.693802][D loss: -9688.764648][G loss 1796.158569 ]time: 0:04:39.877211 
[Epoch 6/2000][Batch 30/48][Autoencoder loss: 2811.399414][C loss: 3.970370][M loss: 0.693562][D loss: -10778.793945][G loss 1083.620483 ]time: 0:04:40.325517 
[Epoch 6/2000][Batch 31/48][Autoencoder loss: 1602.835693][C loss: 3.319124][M loss: 0.693675][D loss: -12182.693359][G loss 505.348114 ]time: 0:04:40.768058 
[Epoch 6/2000][Batch 32/48][Autoencoder loss: 1286.633911][C loss: 3.061283][M loss: 0.693650][D loss: -12790.517578][G loss -48.216255 ]time: 0:04:41.210267 
[Epoch 6/2000][Batch 33/48][Autoencoder loss: 1253.085327][C loss: 2.568854][M loss: 0.693558][D loss: -12831.241211][G loss -740.453735 ]time: 0:04:41.654310 
[Epoch 6/2000][Batch 34/48][Autoencoder loss: 1149.325684][C loss: 3.052738][M loss: 0.693540][D loss: -13015.951172][G loss -1653.134155 ]time: 0:04:42.088367 
[Epoch 6/2000][Batch 35/48][Autoencoder loss: 1259.709473][C loss: 3.140289][M loss: 0.693551][D loss: -14271.077148][G loss -2354.123535 ]time: 0:04:42.596217 
[Epoch 6/2000][Batch 36/48][Autoencoder loss: 1147.267944][C loss: 5.343645][M loss: 0.693431][D loss: -15895.205078][G loss -2923.824463 ]time: 0:04:43.060027 
[Epoch 6/2000][Batch 37/48][Autoencoder loss: 1328.506226][C loss: 4.480375][M loss: 0.693444][D loss: -15764.628906][G loss -3310.599365 ]time: 0:04:43.497072 
[Epoch 6/2000][Batch 38/48][Autoencoder loss: 1296.576050][C loss: 3.482399][M loss: 0.693428][D loss: -16376.904297][G loss -3951.966064 ]time: 0:04:43.947017 
[Epoch 6/2000][Batch 39/48][Autoencoder loss: 1095.580322][C loss: 2.746366][M loss: 0.693418][D loss: -16196.251953][G loss -4524.784180 ]time: 0:04:44.386189 
[Epoch 6/2000][Batch 40/48][Autoencoder loss: 1275.379028][C loss: 2.789197][M loss: 0.693394][D loss: -17194.863281][G loss -5211.676758 ]time: 0:04:44.841492 
[Epoch 6/2000][Batch 41/48][Autoencoder loss: 1103.661987][C loss: 2.751654][M loss: 0.693395][D loss: -18088.679688][G loss -5694.054199 ]time: 0:04:45.325389 
[Epoch 6/2000][Batch 42/48][Autoencoder loss: 1177.277466][C loss: 2.083616][M loss: 0.693423][D loss: -19415.451172][G loss -6129.427246 ]time: 0:04:45.903843 
[Epoch 6/2000][Batch 43/48][Autoencoder loss: 910.805847][C loss: 1.249751][M loss: 0.693365][D loss: -18738.273438][G loss -6680.098145 ]time: 0:04:46.820887 
[Epoch 6/2000][Batch 44/48][Autoencoder loss: 707.883972][C loss: 2.370137][M loss: 0.693360][D loss: -20025.947266][G loss -6843.834961 ]time: 0:04:47.469579 
[Epoch 6/2000][Batch 45/48][Autoencoder loss: 737.956787][C loss: 2.679005][M loss: 0.693374][D loss: -20910.001953][G loss -7123.455078 ]time: 0:04:47.917768 
[Epoch 6/2000][Batch 46/48][Autoencoder loss: 599.380432][C loss: 2.707613][M loss: 0.693344][D loss: -21532.449219][G loss -7448.208984 ]time: 0:04:48.364490 
[Epoch 6/2000][Batch 47/48][Autoencoder loss: 579.853149][C loss: 2.448188][M loss: 0.693344][D loss: -22561.539062][G loss -8234.067383 ]time: 0:04:48.814279 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 511us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 442us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 398us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 6/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.340625] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.333333]
[Epoch 7/2000][Batch 0/48][Autoencoder loss: 763.862915][C loss: 3.984205][M loss: 0.693443][D loss: -20163.302734][G loss -11763.892578 ]time: 0:05:07.485082 
[Epoch 7/2000][Batch 1/48][Autoencoder loss: 602.612732][C loss: 4.117782][M loss: 0.693350][D loss: -20877.044922][G loss -12756.375000 ]time: 0:05:07.953949 
[Epoch 7/2000][Batch 2/48][Autoencoder loss: 492.750275][C loss: 3.926390][M loss: 0.693311][D loss: -21778.875000][G loss -13027.042969 ]time: 0:05:08.441128 
[Epoch 7/2000][Batch 3/48][Autoencoder loss: 506.827789][C loss: 3.582619][M loss: 0.693308][D loss: -22367.763672][G loss -14036.246094 ]time: 0:05:08.921415 
[Epoch 7/2000][Batch 4/48][Autoencoder loss: 846.960876][C loss: 3.013289][M loss: 0.693318][D loss: -23246.394531][G loss -14682.545898 ]time: 0:05:09.490982 
[Epoch 7/2000][Batch 5/48][Autoencoder loss: 1668.501099][C loss: 2.740089][M loss: 0.693311][D loss: -23715.578125][G loss -14660.647461 ]time: 0:05:10.546186 
[Epoch 7/2000][Batch 6/48][Autoencoder loss: 1297.852783][C loss: 2.264975][M loss: 0.693323][D loss: -24545.371094][G loss -15835.751953 ]time: 0:05:12.120731 
[Epoch 7/2000][Batch 7/48][Autoencoder loss: 810.452515][C loss: 1.819643][M loss: 0.693314][D loss: -25044.867188][G loss -17074.054688 ]time: 0:05:13.305755 
[Epoch 7/2000][Batch 8/48][Autoencoder loss: 505.117004][C loss: 2.304315][M loss: 0.693312][D loss: -29192.974609][G loss -13553.766602 ]time: 0:05:13.814281 
[Epoch 7/2000][Batch 9/48][Autoencoder loss: 576.785828][C loss: 2.280478][M loss: 0.693302][D loss: -30021.253906][G loss -14695.275391 ]time: 0:05:14.316137 
[Epoch 7/2000][Batch 10/48][Autoencoder loss: 560.910034][C loss: 2.114475][M loss: 0.693317][D loss: -30779.099609][G loss -15691.543945 ]time: 0:05:14.754078 
[Epoch 7/2000][Batch 11/48][Autoencoder loss: 599.208618][C loss: 1.815024][M loss: 0.693306][D loss: -31746.935547][G loss -15762.543945 ]time: 0:05:15.190565 
[Epoch 7/2000][Batch 12/48][Autoencoder loss: 559.405884][C loss: 2.278221][M loss: 0.693313][D loss: -29601.417969][G loss -18878.048828 ]time: 0:05:15.619549 
[Epoch 7/2000][Batch 13/48][Autoencoder loss: 547.737976][C loss: 1.992011][M loss: 0.693305][D loss: -31078.205078][G loss -20703.707031 ]time: 0:05:16.062290 
[Epoch 7/2000][Batch 14/48][Autoencoder loss: 490.669464][C loss: 1.720487][M loss: 0.693305][D loss: -30906.544922][G loss -21398.195312 ]time: 0:05:16.498212 
[Epoch 7/2000][Batch 15/48][Autoencoder loss: 405.127014][C loss: 1.600782][M loss: 0.693306][D loss: -31473.152344][G loss -22098.974609 ]time: 0:05:16.929269 
[Epoch 7/2000][Batch 16/48][Autoencoder loss: 592.945068][C loss: 1.192257][M loss: 0.693325][D loss: -33239.058594][G loss -21945.632812 ]time: 0:05:17.360064 
[Epoch 7/2000][Batch 17/48][Autoencoder loss: 363.309387][C loss: 1.139236][M loss: 0.693301][D loss: -35513.730469][G loss -22516.474609 ]time: 0:05:17.790555 
[Epoch 7/2000][Batch 18/48][Autoencoder loss: 273.800629][C loss: 0.914594][M loss: 0.693302][D loss: -35813.164062][G loss -23544.529297 ]time: 0:05:18.234075 
[Epoch 7/2000][Batch 19/48][Autoencoder loss: 243.171722][C loss: 0.619682][M loss: 0.693309][D loss: -36920.582031][G loss -24464.886719 ]time: 0:05:18.700060 
[Epoch 7/2000][Batch 20/48][Autoencoder loss: 2427.645508][C loss: 1.060161][M loss: 0.693508][D loss: -38469.886719][G loss -26512.337891 ]time: 0:05:19.273610 
[Epoch 7/2000][Batch 21/48][Autoencoder loss: 6203.708496][C loss: 0.589485][M loss: 0.694156][D loss: -39610.222656][G loss -27446.419922 ]time: 0:05:19.719428 
[Epoch 7/2000][Batch 22/48][Autoencoder loss: 4723.712402][C loss: 0.664198][M loss: 0.700111][D loss: -40898.277344][G loss -29699.011719 ]time: 0:05:20.147172 
[Epoch 7/2000][Batch 23/48][Autoencoder loss: 2833.204834][C loss: 0.855669][M loss: 0.695548][D loss: -40940.992188][G loss -31311.535156 ]time: 0:05:20.725097 
[Epoch 7/2000][Batch 24/48][Autoencoder loss: 3850.446289][C loss: 2.732093][M loss: 0.693441][D loss: -37677.031250][G loss -32974.421875 ]time: 0:05:21.165492 
[Epoch 7/2000][Batch 25/48][Autoencoder loss: 4788.516602][C loss: 2.302183][M loss: 0.693502][D loss: -39243.269531][G loss -34120.308594 ]time: 0:05:21.586904 
[Epoch 7/2000][Batch 26/48][Autoencoder loss: 5449.626465][C loss: 1.622551][M loss: 0.693436][D loss: -41662.406250][G loss -33947.414062 ]time: 0:05:22.287345 
[Epoch 7/2000][Batch 27/48][Autoencoder loss: 4595.908203][C loss: 1.104700][M loss: 0.693333][D loss: -42861.957031][G loss -35307.464844 ]time: 0:05:23.082946 
[Epoch 7/2000][Batch 28/48][Autoencoder loss: 5078.873047][C loss: 3.626935][M loss: 0.693368][D loss: -39785.433594][G loss -40452.375000 ]time: 0:05:23.548376 
[Epoch 7/2000][Batch 29/48][Autoencoder loss: 2717.229248][C loss: 3.591969][M loss: 0.693520][D loss: -38403.140625][G loss -40966.140625 ]time: 0:05:23.990162 
[Epoch 7/2000][Batch 30/48][Autoencoder loss: 3027.122070][C loss: 3.606820][M loss: 0.693412][D loss: -41345.921875][G loss -42601.273438 ]time: 0:05:24.455857 
[Epoch 7/2000][Batch 31/48][Autoencoder loss: 1496.761719][C loss: 3.250793][M loss: 0.693356][D loss: -44506.281250][G loss -44617.804688 ]time: 0:05:24.906117 
[Epoch 7/2000][Batch 32/48][Autoencoder loss: 1365.798462][C loss: 2.601727][M loss: 0.693333][D loss: -54832.101562][G loss -36567.750000 ]time: 0:05:25.353406 
[Epoch 7/2000][Batch 33/48][Autoencoder loss: 1169.492188][C loss: 2.561608][M loss: 0.693344][D loss: -54001.046875][G loss -39502.550781 ]time: 0:05:25.801378 
[Epoch 7/2000][Batch 34/48][Autoencoder loss: 1120.537964][C loss: 2.486156][M loss: 0.693329][D loss: -54792.191406][G loss -40770.054688 ]time: 0:05:26.700290 
[Epoch 7/2000][Batch 35/48][Autoencoder loss: 1267.133423][C loss: 2.178083][M loss: 0.693325][D loss: -56257.992188][G loss -40723.484375 ]time: 0:05:27.746276 
[Epoch 7/2000][Batch 36/48][Autoencoder loss: 1061.675293][C loss: 3.280139][M loss: 0.693319][D loss: -57303.902344][G loss -43312.742188 ]time: 0:05:28.227410 
[Epoch 7/2000][Batch 37/48][Autoencoder loss: 1379.951904][C loss: 2.851356][M loss: 0.693323][D loss: -57282.753906][G loss -45824.582031 ]time: 0:05:28.675634 
[Epoch 7/2000][Batch 38/48][Autoencoder loss: 1205.223511][C loss: 2.462832][M loss: 0.693315][D loss: -57724.664062][G loss -46517.519531 ]time: 0:05:29.134693 
[Epoch 7/2000][Batch 39/48][Autoencoder loss: 1173.740723][C loss: 2.021841][M loss: 0.693324][D loss: -58214.804688][G loss -49036.250000 ]time: 0:05:29.619024 
[Epoch 7/2000][Batch 40/48][Autoencoder loss: 1312.683838][C loss: 1.731321][M loss: 0.693333][D loss: -59180.562500][G loss -52004.886719 ]time: 0:05:30.062544 
[Epoch 7/2000][Batch 41/48][Autoencoder loss: 1115.550659][C loss: 1.683160][M loss: 0.693327][D loss: -61945.937500][G loss -52108.816406 ]time: 0:05:30.518181 
[Epoch 7/2000][Batch 42/48][Autoencoder loss: 1239.837769][C loss: 1.564543][M loss: 0.693328][D loss: -63492.187500][G loss -53924.628906 ]time: 0:05:30.971657 
[Epoch 7/2000][Batch 43/48][Autoencoder loss: 899.889343][C loss: 1.247218][M loss: 0.693342][D loss: -62445.507812][G loss -57447.292969 ]time: 0:05:31.423830 
[Epoch 7/2000][Batch 44/48][Autoencoder loss: 753.026489][C loss: 1.683511][M loss: 0.693332][D loss: -68264.828125][G loss -54598.667969 ]time: 0:05:31.875221 
[Epoch 7/2000][Batch 45/48][Autoencoder loss: 603.733459][C loss: 1.526796][M loss: 0.693321][D loss: -70285.507812][G loss -56428.125000 ]time: 0:05:32.628933 
[Epoch 7/2000][Batch 46/48][Autoencoder loss: 484.387207][C loss: 1.344060][M loss: 0.693317][D loss: -70509.773438][G loss -59853.496094 ]time: 0:05:33.679502 
[Epoch 7/2000][Batch 47/48][Autoencoder loss: 488.624023][C loss: 0.998496][M loss: 0.693303][D loss: -73981.648438][G loss -59311.152344 ]time: 0:05:34.619461 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 494us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 590us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 433us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 7/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.340625] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.333333]
[Epoch 8/2000][Batch 0/48][Autoencoder loss: 723.456970][C loss: 2.801529][M loss: 0.693310][D loss: -55762.054688][G loss -82782.226562 ]time: 0:05:50.575384 
[Epoch 8/2000][Batch 1/48][Autoencoder loss: 603.550049][C loss: 3.045159][M loss: 0.693296][D loss: -58963.578125][G loss -84674.804688 ]time: 0:05:51.078584 
[Epoch 8/2000][Batch 2/48][Autoencoder loss: 501.052917][C loss: 2.923504][M loss: 0.693309][D loss: -57674.640625][G loss -87702.000000 ]time: 0:05:51.524562 
[Epoch 8/2000][Batch 3/48][Autoencoder loss: 543.229309][C loss: 2.677890][M loss: 0.693296][D loss: -59749.886719][G loss -87910.492188 ]time: 0:05:51.969720 
[Epoch 8/2000][Batch 4/48][Autoencoder loss: 851.385315][C loss: 2.437805][M loss: 0.693302][D loss: -61337.734375][G loss -92215.820312 ]time: 0:05:52.435667 
[Epoch 8/2000][Batch 5/48][Autoencoder loss: 1711.099609][C loss: 2.286794][M loss: 0.693305][D loss: -60711.714844][G loss -95145.953125 ]time: 0:05:52.887327 
[Epoch 8/2000][Batch 6/48][Autoencoder loss: 1304.833862][C loss: 2.049867][M loss: 0.693312][D loss: -61595.531250][G loss -98655.031250 ]time: 0:05:53.331225 
[Epoch 8/2000][Batch 7/48][Autoencoder loss: 778.872375][C loss: 1.753988][M loss: 0.693308][D loss: -65406.847656][G loss -103208.539062 ]time: 0:05:53.776628 
[Epoch 8/2000][Batch 8/48][Autoencoder loss: 451.070587][C loss: 1.176968][M loss: 0.693299][D loss: -86046.859375][G loss -81301.914062 ]time: 0:05:54.230331 
[Epoch 8/2000][Batch 9/48][Autoencoder loss: 543.223755][C loss: 1.198086][M loss: 0.693291][D loss: -85301.671875][G loss -83258.953125 ]time: 0:05:54.672661 
[Epoch 8/2000][Batch 10/48][Autoencoder loss: 554.069641][C loss: 1.133530][M loss: 0.693295][D loss: -86609.429688][G loss -87043.328125 ]time: 0:05:55.121063 
[Epoch 8/2000][Batch 11/48][Autoencoder loss: 564.500610][C loss: 0.934792][M loss: 0.693298][D loss: -88376.468750][G loss -92086.203125 ]time: 0:05:55.948348 
[Epoch 8/2000][Batch 12/48][Autoencoder loss: 506.710083][C loss: 1.772591][M loss: 0.693294][D loss: -72166.210938][G loss -109845.140625 ]time: 0:05:56.615274 
[Epoch 8/2000][Batch 13/48][Autoencoder loss: 509.871796][C loss: 1.660912][M loss: 0.693299][D loss: -73161.750000][G loss -112916.171875 ]time: 0:05:57.060144 
[Epoch 8/2000][Batch 14/48][Autoencoder loss: 443.710052][C loss: 1.473019][M loss: 0.693292][D loss: -74441.984375][G loss -113512.523438 ]time: 0:05:57.508983 
[Epoch 8/2000][Batch 15/48][Autoencoder loss: 380.935669][C loss: 1.316877][M loss: 0.693292][D loss: -72092.171875][G loss -116792.421875 ]time: 0:05:58.408100 
[Epoch 8/2000][Batch 16/48][Autoencoder loss: 565.471985][C loss: 1.052320][M loss: 0.693305][D loss: -78881.195312][G loss -114247.757812 ]time: 0:05:59.651386 
[Epoch 8/2000][Batch 17/48][Autoencoder loss: 346.531464][C loss: 0.813859][M loss: 0.693294][D loss: -81776.000000][G loss -119671.921875 ]time: 0:06:00.179027 
[Epoch 8/2000][Batch 18/48][Autoencoder loss: 264.539703][C loss: 0.586182][M loss: 0.693292][D loss: -82630.031250][G loss -127648.679688 ]time: 0:06:00.625261 
[Epoch 8/2000][Batch 19/48][Autoencoder loss: 226.752182][C loss: 0.448760][M loss: 0.693291][D loss: -76404.828125][G loss -130410.367188 ]time: 0:06:01.083699 
[Epoch 8/2000][Batch 20/48][Autoencoder loss: 2413.806885][C loss: 0.907752][M loss: 0.693340][D loss: -80597.656250][G loss -134155.046875 ]time: 0:06:01.526899 
[Epoch 8/2000][Batch 21/48][Autoencoder loss: 5985.227051][C loss: 0.637781][M loss: 0.693339][D loss: -82342.375000][G loss -136608.343750 ]time: 0:06:01.975101 
[Epoch 8/2000][Batch 22/48][Autoencoder loss: 4850.037598][C loss: 0.561920][M loss: 0.694611][D loss: -80903.109375][G loss -146396.640625 ]time: 0:06:02.425449 
[Epoch 8/2000][Batch 23/48][Autoencoder loss: 2874.243652][C loss: 0.452949][M loss: 0.693546][D loss: -77801.328125][G loss -147057.281250 ]time: 0:06:03.344245 
[Epoch 8/2000][Batch 24/48][Autoencoder loss: 3762.331787][C loss: 2.175059][M loss: 0.693495][D loss: -57293.839844][G loss -156940.765625 ]time: 0:06:03.831886 
[Epoch 8/2000][Batch 25/48][Autoencoder loss: 4960.606445][C loss: 1.964636][M loss: 0.693404][D loss: -60438.765625][G loss -162897.000000 ]time: 0:06:04.271901 
[Epoch 8/2000][Batch 26/48][Autoencoder loss: 5292.961426][C loss: 1.907398][M loss: 0.693313][D loss: -68769.593750][G loss -169870.500000 ]time: 0:06:04.712434 
[Epoch 8/2000][Batch 27/48][Autoencoder loss: 4577.098145][C loss: 1.637307][M loss: 0.693310][D loss: -64837.031250][G loss -162389.140625 ]time: 0:06:05.151192 
[Epoch 8/2000][Batch 28/48][Autoencoder loss: 5153.904785][C loss: 3.927646][M loss: 0.693393][D loss: -43615.179688][G loss -189811.062500 ]time: 0:06:05.591934 
[Epoch 8/2000][Batch 29/48][Autoencoder loss: 2783.222900][C loss: 2.807539][M loss: 0.693399][D loss: -39244.371094][G loss -195257.000000 ]time: 0:06:06.101998 
[Epoch 8/2000][Batch 30/48][Autoencoder loss: 3174.041260][C loss: 2.199747][M loss: 0.693381][D loss: -40653.035156][G loss -197365.921875 ]time: 0:06:06.956144 
[Epoch 8/2000][Batch 31/48][Autoencoder loss: 1404.663330][C loss: 1.833425][M loss: 0.693312][D loss: -45018.753906][G loss -202628.281250 ]time: 0:06:07.733723 
[Epoch 8/2000][Batch 32/48][Autoencoder loss: 1497.075928][C loss: 3.592445][M loss: 0.693332][D loss: -86656.320312][G loss -162800.312500 ]time: 0:06:08.172496 
[Epoch 8/2000][Batch 33/48][Autoencoder loss: 1069.367188][C loss: 3.740507][M loss: 0.693312][D loss: -77451.109375][G loss -164942.359375 ]time: 0:06:08.616501 
[Epoch 8/2000][Batch 34/48][Autoencoder loss: 1163.648071][C loss: 3.549717][M loss: 0.693324][D loss: -73836.671875][G loss -165458.468750 ]time: 0:06:09.053101 
[Epoch 8/2000][Batch 35/48][Autoencoder loss: 1237.617432][C loss: 3.065484][M loss: 0.693306][D loss: -74523.312500][G loss -176467.468750 ]time: 0:06:09.492351 
[Epoch 8/2000][Batch 36/48][Autoencoder loss: 980.711609][C loss: 4.514122][M loss: 0.693317][D loss: -76210.531250][G loss -188885.437500 ]time: 0:06:09.931856 
[Epoch 8/2000][Batch 37/48][Autoencoder loss: 1359.658203][C loss: 3.433397][M loss: 0.693314][D loss: -71125.281250][G loss -186627.531250 ]time: 0:06:10.371213 
[Epoch 8/2000][Batch 38/48][Autoencoder loss: 1070.951782][C loss: 2.779259][M loss: 0.693300][D loss: -60020.816406][G loss -191753.593750 ]time: 0:06:10.820851 
[Epoch 8/2000][Batch 39/48][Autoencoder loss: 1259.059204][C loss: 2.064366][M loss: 0.693302][D loss: -59639.453125][G loss -193892.187500 ]time: 0:06:11.274113 
[Epoch 8/2000][Batch 40/48][Autoencoder loss: 1311.190674][C loss: 2.452478][M loss: 0.693327][D loss: -60171.355469][G loss -204788.171875 ]time: 0:06:11.711216 
[Epoch 8/2000][Batch 41/48][Autoencoder loss: 1164.027100][C loss: 1.982521][M loss: 0.693323][D loss: -59448.472656][G loss -213602.671875 ]time: 0:06:12.156782 
[Epoch 8/2000][Batch 42/48][Autoencoder loss: 1239.046509][C loss: 2.045916][M loss: 0.693300][D loss: -55972.417969][G loss -216060.593750 ]time: 0:06:12.599411 
[Epoch 8/2000][Batch 43/48][Autoencoder loss: 814.664185][C loss: 2.176656][M loss: 0.693303][D loss: -43518.882812][G loss -220470.359375 ]time: 0:06:13.265767 
[Epoch 8/2000][Batch 44/48][Autoencoder loss: 742.452576][C loss: 1.615011][M loss: 0.693304][D loss: -57222.261719][G loss -208217.640625 ]time: 0:06:14.960989 
[Epoch 8/2000][Batch 45/48][Autoencoder loss: 568.311523][C loss: 1.265986][M loss: 0.693311][D loss: -60343.484375][G loss -209798.031250 ]time: 0:06:15.656380 
[Epoch 8/2000][Batch 46/48][Autoencoder loss: 558.429077][C loss: 0.901435][M loss: 0.693312][D loss: -57380.578125][G loss -215539.671875 ]time: 0:06:16.099229 
[Epoch 8/2000][Batch 47/48][Autoencoder loss: 486.931122][C loss: 0.593125][M loss: 0.693303][D loss: -46042.152344][G loss -221755.234375 ]time: 0:06:16.796417 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 516us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 504us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 534us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 8/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.340625] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.333333]
[Epoch 9/2000][Batch 0/48][Autoencoder loss: 794.229919][C loss: 3.327849][M loss: 0.693346][D loss: 25419.921875][G loss -299900.031250 ]time: 0:06:36.513605 
[Epoch 9/2000][Batch 1/48][Autoencoder loss: 577.895813][C loss: 3.743787][M loss: 0.693312][D loss: 22986.839844][G loss -291407.843750 ]time: 0:06:36.956905 
[Epoch 9/2000][Batch 2/48][Autoencoder loss: 508.129761][C loss: 3.823235][M loss: 0.693300][D loss: 29348.453125][G loss -294649.375000 ]time: 0:06:37.406929 
[Epoch 9/2000][Batch 3/48][Autoencoder loss: 554.542053][C loss: 3.501932][M loss: 0.693294][D loss: 32717.027344][G loss -296301.656250 ]time: 0:06:37.844506 
[Epoch 9/2000][Batch 4/48][Autoencoder loss: 854.341248][C loss: 3.213936][M loss: 0.693310][D loss: 29692.277344][G loss -288966.875000 ]time: 0:06:38.289089 
[Epoch 9/2000][Batch 5/48][Autoencoder loss: 1735.435303][C loss: 2.851338][M loss: 0.693296][D loss: 36847.332031][G loss -287517.312500 ]time: 0:06:38.753250 
[Epoch 9/2000][Batch 6/48][Autoencoder loss: 1312.540894][C loss: 2.532125][M loss: 0.693295][D loss: 46539.687500][G loss -284943.000000 ]time: 0:06:39.251139 
[Epoch 9/2000][Batch 7/48][Autoencoder loss: 794.931396][C loss: 2.103329][M loss: 0.693297][D loss: 40531.828125][G loss -289291.562500 ]time: 0:06:39.683373 
[Epoch 9/2000][Batch 8/48][Autoencoder loss: 487.764679][C loss: 1.766411][M loss: 0.693294][D loss: -21960.042969][G loss -212960.046875 ]time: 0:06:40.124705 
[Epoch 9/2000][Batch 9/48][Autoencoder loss: 576.001404][C loss: 1.554309][M loss: 0.693296][D loss: -16775.191406][G loss -207398.421875 ]time: 0:06:40.575262 
[Epoch 9/2000][Batch 10/48][Autoencoder loss: 551.077942][C loss: 1.280893][M loss: 0.693294][D loss: -14666.054688][G loss -206151.937500 ]time: 0:06:41.024027 
[Epoch 9/2000][Batch 11/48][Autoencoder loss: 518.565613][C loss: 1.069601][M loss: 0.693295][D loss: -2451.267578][G loss -204028.734375 ]time: 0:06:41.474948 
[Epoch 9/2000][Batch 12/48][Autoencoder loss: 470.439087][C loss: 2.822616][M loss: 0.693292][D loss: 35714.710938][G loss -240791.093750 ]time: 0:06:42.189323 
[Epoch 9/2000][Batch 13/48][Autoencoder loss: 508.142303][C loss: 2.669981][M loss: 0.693291][D loss: 34884.476562][G loss -228574.234375 ]time: 0:06:43.094885 
[Epoch 9/2000][Batch 14/48][Autoencoder loss: 465.860321][C loss: 2.410700][M loss: 0.693290][D loss: 39325.882812][G loss -230457.671875 ]time: 0:06:43.533549 
[Epoch 9/2000][Batch 15/48][Autoencoder loss: 406.011383][C loss: 2.086687][M loss: 0.693291][D loss: 39343.171875][G loss -224944.640625 ]time: 0:06:43.977567 
[Epoch 9/2000][Batch 16/48][Autoencoder loss: 555.726440][C loss: 1.437426][M loss: 0.693302][D loss: 26393.476562][G loss -206670.796875 ]time: 0:06:44.432424 
[Epoch 9/2000][Batch 17/48][Autoencoder loss: 356.767944][C loss: 1.220719][M loss: 0.693292][D loss: 27126.173828][G loss -202694.171875 ]time: 0:06:44.887649 
[Epoch 9/2000][Batch 18/48][Autoencoder loss: 246.255371][C loss: 1.178876][M loss: 0.693289][D loss: 31528.406250][G loss -193760.750000 ]time: 0:06:45.544558 
[Epoch 9/2000][Batch 19/48][Autoencoder loss: 227.563004][C loss: 1.033889][M loss: 0.693291][D loss: 30913.513672][G loss -192945.765625 ]time: 0:06:46.078602 
[Epoch 9/2000][Batch 20/48][Autoencoder loss: 2536.500977][C loss: 1.966254][M loss: 0.693312][D loss: 34046.125000][G loss -187405.515625 ]time: 0:06:47.058330 
[Epoch 9/2000][Batch 21/48][Autoencoder loss: 6135.263184][C loss: 1.528998][M loss: 0.693308][D loss: 31288.513672][G loss -183089.453125 ]time: 0:06:47.995714 
[Epoch 9/2000][Batch 22/48][Autoencoder loss: 5051.662598][C loss: 1.185495][M loss: 0.693590][D loss: 34832.140625][G loss -182476.906250 ]time: 0:06:48.449383 
[Epoch 9/2000][Batch 23/48][Autoencoder loss: 2972.527344][C loss: 0.782486][M loss: 0.693653][D loss: 38819.218750][G loss -166601.171875 ]time: 0:06:48.892795 
[Epoch 9/2000][Batch 24/48][Autoencoder loss: 3781.206055][C loss: 1.555323][M loss: 0.693351][D loss: 51842.250000][G loss -167272.859375 ]time: 0:06:49.345884 
[Epoch 9/2000][Batch 25/48][Autoencoder loss: 5195.302246][C loss: 1.812078][M loss: 0.693335][D loss: 43824.671875][G loss -160824.468750 ]time: 0:06:49.845032 
[Epoch 9/2000][Batch 26/48][Autoencoder loss: 5427.786621][C loss: 1.807866][M loss: 0.693313][D loss: 44981.660156][G loss -150338.562500 ]time: 0:06:50.295867 
[Epoch 9/2000][Batch 27/48][Autoencoder loss: 4685.140625][C loss: 1.432363][M loss: 0.693297][D loss: 42616.625000][G loss -138701.593750 ]time: 0:06:50.743392 
[Epoch 9/2000][Batch 28/48][Autoencoder loss: 5323.156738][C loss: 2.753877][M loss: 0.693319][D loss: 55909.695312][G loss -145817.937500 ]time: 0:06:51.199908 
[Epoch 9/2000][Batch 29/48][Autoencoder loss: 2783.242432][C loss: 2.588231][M loss: 0.693313][D loss: 61654.746094][G loss -131442.781250 ]time: 0:06:51.651477 
[Epoch 9/2000][Batch 30/48][Autoencoder loss: 3361.418213][C loss: 2.592969][M loss: 0.693337][D loss: 53960.406250][G loss -116992.218750 ]time: 0:06:52.103988 
[Epoch 9/2000][Batch 31/48][Autoencoder loss: 1572.780151][C loss: 2.468125][M loss: 0.693323][D loss: 45309.652344][G loss -101700.234375 ]time: 0:06:53.185152 
[Epoch 9/2000][Batch 32/48][Autoencoder loss: 1783.315063][C loss: 3.190996][M loss: 0.693306][D loss: 17650.296875][G loss -68803.015625 ]time: 0:06:54.345514 
[Epoch 9/2000][Batch 33/48][Autoencoder loss: 1232.904297][C loss: 2.687391][M loss: 0.693299][D loss: 17535.847656][G loss -58519.394531 ]time: 0:06:54.952589 
[Epoch 9/2000][Batch 34/48][Autoencoder loss: 1266.585938][C loss: 2.323794][M loss: 0.693310][D loss: 17078.554688][G loss -49720.085938 ]time: 0:06:55.396592 
[Epoch 9/2000][Batch 35/48][Autoencoder loss: 1295.508301][C loss: 2.031461][M loss: 0.693303][D loss: 14603.257812][G loss -40881.042969 ]time: 0:06:55.845860 
[Epoch 9/2000][Batch 36/48][Autoencoder loss: 988.476318][C loss: 2.596906][M loss: 0.693298][D loss: 14128.500000][G loss -33077.500000 ]time: 0:06:56.289663 
[Epoch 9/2000][Batch 37/48][Autoencoder loss: 1381.621338][C loss: 2.592893][M loss: 0.693301][D loss: 12274.250000][G loss -24778.333984 ]time: 0:06:56.739209 
[Epoch 9/2000][Batch 38/48][Autoencoder loss: 1049.831299][C loss: 2.268186][M loss: 0.693301][D loss: 11917.598633][G loss -16704.976562 ]time: 0:06:57.186420 
[Epoch 9/2000][Batch 39/48][Autoencoder loss: 1323.869019][C loss: 1.950417][M loss: 0.693301][D loss: 9995.248047][G loss -9010.702148 ]time: 0:06:57.634402 
[Epoch 9/2000][Batch 40/48][Autoencoder loss: 1406.289062][C loss: 2.316565][M loss: 0.693298][D loss: 8551.364258][G loss -1817.998413 ]time: 0:06:58.140307 
[Epoch 9/2000][Batch 41/48][Autoencoder loss: 1292.565796][C loss: 2.320576][M loss: 0.693302][D loss: 7147.453613][G loss 5449.290039 ]time: 0:06:58.586317 
[Epoch 9/2000][Batch 42/48][Autoencoder loss: 1388.457520][C loss: 1.940089][M loss: 0.693303][D loss: 5368.247070][G loss 12515.633789 ]time: 0:06:59.037205 
[Epoch 9/2000][Batch 43/48][Autoencoder loss: 952.887878][C loss: 1.425700][M loss: 0.693303][D loss: 3843.574707][G loss 19692.173828 ]time: 0:06:59.616192 
[Epoch 9/2000][Batch 44/48][Autoencoder loss: 885.958984][C loss: 1.507167][M loss: 0.693296][D loss: 3735.281250][G loss 23667.320312 ]time: 0:07:00.052902 
[Epoch 9/2000][Batch 45/48][Autoencoder loss: 676.599365][C loss: 1.509379][M loss: 0.693299][D loss: 2401.472656][G loss 30044.136719 ]time: 0:07:00.546500 
[Epoch 9/2000][Batch 46/48][Autoencoder loss: 697.308044][C loss: 1.474452][M loss: 0.693296][D loss: 1075.281250][G loss 35364.632812 ]time: 0:07:00.986169 
[Epoch 9/2000][Batch 47/48][Autoencoder loss: 632.224609][C loss: 1.236689][M loss: 0.693294][D loss: -273.291016][G loss 40731.535156 ]time: 0:07:01.700157 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
63/63 [==============================] - 0s 738us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 789us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
63/63 [==============================] - 0s 780us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:690: ConvergenceWarning: Stochastic Optimizer: Maximum iterations (200) reached and the optimization hasn't converged yet.
  warnings.warn(
[Epoch 9/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.340625] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.339931]
[Epoch 10/2000][Batch 0/48][Autoencoder loss: 961.916931][C loss: 1.947646][M loss: 0.693297][D loss: -15152.710938][G loss 62680.277344 ]time: 0:07:22.624964 
[Epoch 10/2000][Batch 1/48][Autoencoder loss: 655.877502][C loss: 2.215829][M loss: 0.693294][D loss: -17466.816406][G loss 73420.570312 ]time: 0:07:23.065615 
[Epoch 10/2000][Batch 2/48][Autoencoder loss: 556.395630][C loss: 2.293382][M loss: 0.693291][D loss: -21269.816406][G loss 84239.882812 ]time: 0:07:23.573205 
[Epoch 10/2000][Batch 3/48][Autoencoder loss: 552.605835][C loss: 2.238399][M loss: 0.693294][D loss: -24923.583984][G loss 93042.429688 ]time: 0:07:24.028945 
[Epoch 10/2000][Batch 4/48][Autoencoder loss: 798.381104][C loss: 2.292947][M loss: 0.693295][D loss: -28503.550781][G loss 105238.648438 ]time: 0:07:24.475675 
[Epoch 10/2000][Batch 5/48][Autoencoder loss: 1676.377075][C loss: 2.015865][M loss: 0.693294][D loss: -31639.085938][G loss 115140.546875 ]time: 0:07:25.041058 
[Epoch 10/2000][Batch 6/48][Autoencoder loss: 1375.794312][C loss: 1.789507][M loss: 0.693293][D loss: -36289.257812][G loss 127810.367188 ]time: 0:07:25.488103 
[Epoch 10/2000][Batch 7/48][Autoencoder loss: 895.054565][C loss: 1.596358][M loss: 0.693292][D loss: -38212.328125][G loss 139957.921875 ]time: 0:07:25.923081 
[Epoch 10/2000][Batch 8/48][Autoencoder loss: 495.940491][C loss: 1.339326][M loss: 0.693291][D loss: -9296.750977][G loss 115026.796875 ]time: 0:07:26.726491 
[Epoch 10/2000][Batch 9/48][Autoencoder loss: 573.806091][C loss: 1.196948][M loss: 0.693291][D loss: -6871.001953][G loss 121501.625000 ]time: 0:07:27.779916 
[Epoch 10/2000][Batch 10/48][Autoencoder loss: 568.979797][C loss: 0.986703][M loss: 0.693292][D loss: -11191.195312][G loss 130875.101562 ]time: 0:07:28.338067 
[Epoch 10/2000][Batch 11/48][Autoencoder loss: 549.644897][C loss: 0.745172][M loss: 0.693289][D loss: -10869.557617][G loss 135699.859375 ]time: 0:07:28.777142 
[Epoch 10/2000][Batch 12/48][Autoencoder loss: 476.937622][C loss: 1.563385][M loss: 0.693290][D loss: -36420.500000][G loss 166245.843750 ]time: 0:07:29.394896 
[Epoch 10/2000][Batch 13/48][Autoencoder loss: 472.881653][C loss: 1.449154][M loss: 0.693291][D loss: -38739.453125][G loss 173922.906250 ]time: 0:07:29.839671 
[Epoch 10/2000][Batch 14/48][Autoencoder loss: 436.414062][C loss: 1.246900][M loss: 0.693288][D loss: -38338.035156][G loss 184396.562500 ]time: 0:07:30.289697 
[Epoch 10/2000][Batch 15/48][Autoencoder loss: 375.884705][C loss: 1.066175][M loss: 0.693289][D loss: -37479.476562][G loss 186318.171875 ]time: 0:07:30.731667 
[Epoch 10/2000][Batch 16/48][Autoencoder loss: 527.874084][C loss: 0.648497][M loss: 0.693292][D loss: -24912.484375][G loss 181553.468750 ]time: 0:07:31.186689 
[Epoch 10/2000][Batch 17/48][Autoencoder loss: 379.309265][C loss: 0.538036][M loss: 0.693293][D loss: -28861.291016][G loss 188598.500000 ]time: 0:07:31.634270 
[Epoch 10/2000][Batch 18/48][Autoencoder loss: 307.269073][C loss: 0.488230][M loss: 0.693289][D loss: -25540.638672][G loss 193769.734375 ]time: 0:07:32.086709 
[Epoch 10/2000][Batch 19/48][Autoencoder loss: 287.227997][C loss: 0.441763][M loss: 0.693288][D loss: -30825.406250][G loss 197176.828125 ]time: 0:07:32.537303 
[Epoch 10/2000][Batch 20/48][Autoencoder loss: 2757.036377][C loss: 1.621601][M loss: 0.693337][D loss: -29845.279297][G loss 205464.968750 ]time: 0:07:32.993965 
[Epoch 10/2000][Batch 21/48][Autoencoder loss: 6394.804199][C loss: 1.456017][M loss: 0.693298][D loss: -29500.070312][G loss 212131.171875 ]time: 0:07:33.688918 
[Epoch 10/2000][Batch 22/48][Autoencoder loss: 5586.875000][C loss: 1.077126][M loss: 0.693665][D loss: -28283.078125][G loss 214331.937500 ]time: 0:07:34.826594 
[Epoch 10/2000][Batch 23/48][Autoencoder loss: 3496.172852][C loss: 0.816524][M loss: 0.693699][D loss: -35005.820312][G loss 217921.234375 ]time: 0:07:35.842742 
[Epoch 10/2000][Batch 24/48][Autoencoder loss: 3508.479736][C loss: 1.752545][M loss: 0.693304][D loss: -44852.203125][G loss 224796.109375 ]time: 0:07:36.422643 
[Epoch 10/2000][Batch 25/48][Autoencoder loss: 5176.241211][C loss: 2.005383][M loss: 0.693308][D loss: -46071.656250][G loss 231649.640625 ]time: 0:07:36.870499 
[Epoch 10/2000][Batch 26/48][Autoencoder loss: 5250.409180][C loss: 2.101411][M loss: 0.693308][D loss: -35058.808594][G loss 240998.359375 ]time: 0:07:37.315201 
[Epoch 10/2000][Batch 27/48][Autoencoder loss: 4297.941895][C loss: 1.906904][M loss: 0.693295][D loss: -32626.166016][G loss 229886.765625 ]time: 0:07:38.476438 
[Epoch 10/2000][Batch 28/48][Autoencoder loss: 5614.673828][C loss: 2.751857][M loss: 0.693301][D loss: -64349.906250][G loss 274711.812500 ]time: 0:07:39.439630 
[Epoch 10/2000][Batch 29/48][Autoencoder loss: 2767.432373][C loss: 2.320909][M loss: 0.693312][D loss: -63974.476562][G loss 275662.718750 ]time: 0:07:39.887964 
[Epoch 10/2000][Batch 30/48][Autoencoder loss: 3796.086914][C loss: 2.319037][M loss: 0.693313][D loss: -55845.765625][G loss 275000.125000 ]time: 0:07:40.341120 
[Epoch 10/2000][Batch 31/48][Autoencoder loss: 1622.919434][C loss: 2.182394][M loss: 0.693298][D loss: -58093.160156][G loss 282314.187500 ]time: 0:07:40.792742 
[Epoch 10/2000][Batch 32/48][Autoencoder loss: 1837.049438][C loss: 2.137462][M loss: 0.693301][D loss: 11664.701172][G loss 217345.500000 ]time: 0:07:41.248324 
[Epoch 10/2000][Batch 33/48][Autoencoder loss: 1330.517700][C loss: 1.757665][M loss: 0.693296][D loss: 8401.507812][G loss 219061.328125 ]time: 0:07:41.691044 
[Epoch 10/2000][Batch 34/48][Autoencoder loss: 1054.670654][C loss: 1.389156][M loss: 0.693294][D loss: 12183.380859][G loss 216865.531250 ]time: 0:07:42.131663 
[Epoch 10/2000][Batch 35/48][Autoencoder loss: 1323.160645][C loss: 1.142436][M loss: 0.693292][D loss: 16394.892578][G loss 214981.046875 ]time: 0:07:42.632532 
[Epoch 10/2000][Batch 36/48][Autoencoder loss: 719.074524][C loss: 2.112885][M loss: 0.693292][D loss: 23599.679688][G loss 209520.062500 ]time: 0:07:43.184894 
[Epoch 10/2000][Batch 37/48][Autoencoder loss: 1404.138916][C loss: 2.181282][M loss: 0.693294][D loss: 15824.558594][G loss 211920.562500 ]time: 0:07:43.631898 
[Epoch 10/2000][Batch 38/48][Autoencoder loss: 1041.051636][C loss: 2.040496][M loss: 0.693295][D loss: 13441.326172][G loss 211552.656250 ]time: 0:07:44.072428 
[Epoch 10/2000][Batch 39/48][Autoencoder loss: 1274.716919][C loss: 1.774160][M loss: 0.693295][D loss: 18799.394531][G loss 206234.734375 ]time: 0:07:44.531146 
[Epoch 10/2000][Batch 40/48][Autoencoder loss: 1480.345093][C loss: 2.260166][M loss: 0.693299][D loss: 21166.335938][G loss 208595.546875 ]time: 0:07:44.987171 
[Epoch 10/2000][Batch 41/48][Autoencoder loss: 1186.459595][C loss: 2.214995][M loss: 0.693296][D loss: 31844.591797][G loss 202202.984375 ]time: 0:07:45.475592 
[Epoch 10/2000][Batch 42/48][Autoencoder loss: 1485.367432][C loss: 1.817017][M loss: 0.693302][D loss: 30466.205078][G loss 199576.890625 ]time: 0:07:45.912839 
[Epoch 10/2000][Batch 43/48][Autoencoder loss: 852.892639][C loss: 1.434419][M loss: 0.693297][D loss: 26469.779297][G loss 190187.984375 ]time: 0:07:46.364143 
[Epoch 10/2000][Batch 44/48][Autoencoder loss: 797.489685][C loss: 1.527553][M loss: 0.693302][D loss: 36750.210938][G loss 175580.453125 ]time: 0:07:46.810999 
[Epoch 10/2000][Batch 45/48][Autoencoder loss: 587.514832][C loss: 1.534022][M loss: 0.693304][D loss: 42732.820312][G loss 169015.921875 ]time: 0:07:47.256632 
[Epoch 10/2000][Batch 46/48][Autoencoder loss: 515.531006][C loss: 1.413548][M loss: 0.693298][D loss: 42591.343750][G loss 160461.640625 ]time: 0:07:47.694256 
[Epoch 10/2000][Batch 47/48][Autoencoder loss: 573.375244][C loss: 1.173725][M loss: 0.693294][D loss: 41026.289062][G loss 153863.593750 ]time: 0:07:48.541718 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
62/63 [============================>.] - ETA: 0s
63/63 [==============================] - 0s 857us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
63/63 [==============================] - 0s 699us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 488us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 10/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.340625] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.339931]
[Epoch 11/2000][Batch 0/48][Autoencoder loss: 775.636475][C loss: 1.550321][M loss: 0.693303][D loss: -4110.115234][G loss 192926.078125 ]time: 0:08:05.679051 
[Epoch 11/2000][Batch 1/48][Autoencoder loss: 594.593567][C loss: 1.758819][M loss: 0.693296][D loss: -2538.138672][G loss 189221.671875 ]time: 0:08:06.317551 
[Epoch 11/2000][Batch 2/48][Autoencoder loss: 495.750336][C loss: 1.827127][M loss: 0.693294][D loss: 3472.179688][G loss 181819.328125 ]time: 0:08:07.343294 
[Epoch 11/2000][Batch 3/48][Autoencoder loss: 558.035522][C loss: 1.748724][M loss: 0.693295][D loss: 3227.439453][G loss 174483.968750 ]time: 0:08:08.161407 
[Epoch 11/2000][Batch 4/48][Autoencoder loss: 829.919250][C loss: 1.768160][M loss: 0.693302][D loss: 6803.701172][G loss 170420.500000 ]time: 0:08:08.610542 
[Epoch 11/2000][Batch 5/48][Autoencoder loss: 1671.078735][C loss: 1.507479][M loss: 0.693296][D loss: 6161.195312][G loss 163637.437500 ]time: 0:08:09.050036 
[Epoch 11/2000][Batch 6/48][Autoencoder loss: 1399.215942][C loss: 1.339866][M loss: 0.693293][D loss: 6940.705078][G loss 158743.171875 ]time: 0:08:09.483867 
[Epoch 11/2000][Batch 7/48][Autoencoder loss: 856.534180][C loss: 1.156934][M loss: 0.693292][D loss: 10945.103516][G loss 152239.546875 ]time: 0:08:09.932346 
[Epoch 11/2000][Batch 8/48][Autoencoder loss: 436.535919][C loss: 1.528502][M loss: 0.693290][D loss: 48198.539062][G loss 113559.796875 ]time: 0:08:10.858758 
[Epoch 11/2000][Batch 9/48][Autoencoder loss: 575.364075][C loss: 1.599275][M loss: 0.693291][D loss: 48226.042969][G loss 110785.460938 ]time: 0:08:11.981289 
[Epoch 11/2000][Batch 10/48][Autoencoder loss: 484.554352][C loss: 1.594787][M loss: 0.693289][D loss: 47709.824219][G loss 104832.531250 ]time: 0:08:12.572608 
[Epoch 11/2000][Batch 11/48][Autoencoder loss: 504.680542][C loss: 1.436080][M loss: 0.693289][D loss: 45544.695312][G loss 105181.710938 ]time: 0:08:13.021664 
[Epoch 11/2000][Batch 12/48][Autoencoder loss: 425.467773][C loss: 1.522905][M loss: 0.693290][D loss: 27467.460938][G loss 113615.679688 ]time: 0:08:13.471505 
[Epoch 11/2000][Batch 13/48][Autoencoder loss: 425.947906][C loss: 1.165994][M loss: 0.693289][D loss: 26381.607422][G loss 110133.343750 ]time: 0:08:13.914103 
[Epoch 11/2000][Batch 14/48][Autoencoder loss: 457.034821][C loss: 1.050794][M loss: 0.693290][D loss: 25017.544922][G loss 105047.414062 ]time: 0:08:14.362824 
[Epoch 11/2000][Batch 15/48][Autoencoder loss: 370.507690][C loss: 0.994915][M loss: 0.693291][D loss: 23783.988281][G loss 97724.101562 ]time: 0:08:14.860603 
[Epoch 11/2000][Batch 16/48][Autoencoder loss: 564.465881][C loss: 0.938735][M loss: 0.693297][D loss: 31139.949219][G loss 89594.906250 ]time: 0:08:15.314490 
[Epoch 11/2000][Batch 17/48][Autoencoder loss: 350.912384][C loss: 0.689072][M loss: 0.693295][D loss: 29308.117188][G loss 86781.046875 ]time: 0:08:15.758038 
[Epoch 11/2000][Batch 18/48][Autoencoder loss: 253.071182][C loss: 0.487692][M loss: 0.693290][D loss: 31164.865234][G loss 79585.929688 ]time: 0:08:16.218317 
[Epoch 11/2000][Batch 19/48][Autoencoder loss: 234.956543][C loss: 0.456040][M loss: 0.693290][D loss: 28539.914062][G loss 76322.234375 ]time: 0:08:16.664407 
[Epoch 11/2000][Batch 20/48][Autoencoder loss: 2823.886230][C loss: 0.824603][M loss: 0.693311][D loss: 27172.191406][G loss 74236.398438 ]time: 0:08:17.108897 
[Epoch 11/2000][Batch 21/48][Autoencoder loss: 6611.488770][C loss: 0.637433][M loss: 0.693318][D loss: 28895.369141][G loss 71874.546875 ]time: 0:08:17.541451 
[Epoch 11/2000][Batch 22/48][Autoencoder loss: 5614.268066][C loss: 0.568321][M loss: 0.694042][D loss: 28383.902344][G loss 64938.703125 ]time: 0:08:18.037191 
[Epoch 11/2000][Batch 23/48][Autoencoder loss: 3648.216064][C loss: 0.506057][M loss: 0.693774][D loss: 24199.996094][G loss 63940.000000 ]time: 0:08:18.480883 
[Epoch 11/2000][Batch 24/48][Autoencoder loss: 3658.382812][C loss: 1.524259][M loss: 0.693424][D loss: 18039.556641][G loss 60097.140625 ]time: 0:08:18.952212 
[Epoch 11/2000][Batch 25/48][Autoencoder loss: 5935.407227][C loss: 1.376926][M loss: 0.693339][D loss: 17568.470703][G loss 56660.332031 ]time: 0:08:19.416828 
[Epoch 11/2000][Batch 26/48][Autoencoder loss: 6303.473145][C loss: 1.297691][M loss: 0.693305][D loss: 19418.886719][G loss 52694.042969 ]time: 0:08:19.864851 
[Epoch 11/2000][Batch 27/48][Autoencoder loss: 4478.774414][C loss: 1.146517][M loss: 0.693317][D loss: 18798.527344][G loss 49522.062500 ]time: 0:08:20.300328 
[Epoch 11/2000][Batch 28/48][Autoencoder loss: 6987.458496][C loss: 2.372675][M loss: 0.693337][D loss: 11100.052734][G loss 51108.667969 ]time: 0:08:20.820011 
[Epoch 11/2000][Batch 29/48][Autoencoder loss: 2761.335938][C loss: 2.141529][M loss: 0.693353][D loss: 6935.923828][G loss 46972.992188 ]time: 0:08:22.043106 
[Epoch 11/2000][Batch 30/48][Autoencoder loss: 4282.665039][C loss: 2.069321][M loss: 0.693325][D loss: 9196.480469][G loss 44534.871094 ]time: 0:08:23.286807 
[Epoch 11/2000][Batch 31/48][Autoencoder loss: 2184.849121][C loss: 1.842470][M loss: 0.693306][D loss: 10035.458008][G loss 41029.636719 ]time: 0:08:23.778599 
[Epoch 11/2000][Batch 32/48][Autoencoder loss: 1824.952881][C loss: 1.824184][M loss: 0.693313][D loss: 17689.855469][G loss 28678.166016 ]time: 0:08:24.234497 
[Epoch 11/2000][Batch 33/48][Autoencoder loss: 2131.197021][C loss: 1.579202][M loss: 0.693315][D loss: 15318.204102][G loss 26065.365234 ]time: 0:08:24.681575 
[Epoch 11/2000][Batch 34/48][Autoencoder loss: 872.723206][C loss: 1.416411][M loss: 0.693302][D loss: 13845.510742][G loss 23381.892578 ]time: 0:08:25.133586 
[Epoch 11/2000][Batch 35/48][Autoencoder loss: 1680.370361][C loss: 1.241008][M loss: 0.693295][D loss: 12711.421875][G loss 20726.312500 ]time: 0:08:25.620906 
[Epoch 11/2000][Batch 36/48][Autoencoder loss: 911.114258][C loss: 1.863946][M loss: 0.693296][D loss: 11081.139648][G loss 19275.375000 ]time: 0:08:26.220809 
[Epoch 11/2000][Batch 37/48][Autoencoder loss: 1305.040161][C loss: 1.704416][M loss: 0.693297][D loss: 9806.091797][G loss 16266.696289 ]time: 0:08:26.661006 
[Epoch 11/2000][Batch 38/48][Autoencoder loss: 1483.274780][C loss: 1.523952][M loss: 0.693295][D loss: 7653.809570][G loss 13466.834961 ]time: 0:08:27.109454 
[Epoch 11/2000][Batch 39/48][Autoencoder loss: 1044.649658][C loss: 1.482951][M loss: 0.693292][D loss: 5951.335938][G loss 11059.092773 ]time: 0:08:27.549203 
[Epoch 11/2000][Batch 40/48][Autoencoder loss: 1890.859131][C loss: 1.988323][M loss: 0.693326][D loss: 4747.926270][G loss 8158.063477 ]time: 0:08:27.992022 
[Epoch 11/2000][Batch 41/48][Autoencoder loss: 1296.296143][C loss: 1.886482][M loss: 0.693315][D loss: 3576.386230][G loss 5601.622070 ]time: 0:08:28.424273 
[Epoch 11/2000][Batch 42/48][Autoencoder loss: 1555.284912][C loss: 1.534647][M loss: 0.693299][D loss: 1731.290771][G loss 2886.473389 ]time: 0:08:28.913182 
[Epoch 11/2000][Batch 43/48][Autoencoder loss: 1170.107300][C loss: 1.284035][M loss: 0.693299][D loss: 427.034180][G loss 379.961243 ]time: 0:08:29.407528 
[Epoch 11/2000][Batch 44/48][Autoencoder loss: 640.794739][C loss: 1.173319][M loss: 0.693298][D loss: -935.838379][G loss -2073.458008 ]time: 0:08:29.856904 
[Epoch 11/2000][Batch 45/48][Autoencoder loss: 889.777283][C loss: 1.148947][M loss: 0.693299][D loss: -2639.686035][G loss -4680.508789 ]time: 0:08:30.295876 
[Epoch 11/2000][Batch 46/48][Autoencoder loss: 491.719788][C loss: 1.036900][M loss: 0.693297][D loss: -4486.988770][G loss -7024.168945 ]time: 0:08:30.739754 
[Epoch 11/2000][Batch 47/48][Autoencoder loss: 636.726807][C loss: 0.809972][M loss: 0.693300][D loss: -5779.342773][G loss -9216.237305 ]time: 0:08:31.183702 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 437us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 429us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 424us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 11/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.340625] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.339931]
[Epoch 12/2000][Batch 0/48][Autoencoder loss: 697.013672][C loss: 1.426131][M loss: 0.693317][D loss: -4175.683594][G loss -15385.962891 ]time: 0:08:48.721892 
[Epoch 12/2000][Batch 1/48][Autoencoder loss: 498.059387][C loss: 1.559075][M loss: 0.693303][D loss: -4945.807129][G loss -18437.492188 ]time: 0:08:49.168049 
[Epoch 12/2000][Batch 2/48][Autoencoder loss: 496.663940][C loss: 1.663761][M loss: 0.693293][D loss: -5372.830566][G loss -20664.525391 ]time: 0:08:49.617297 
[Epoch 12/2000][Batch 3/48][Autoencoder loss: 432.057434][C loss: 1.709494][M loss: 0.693293][D loss: -6577.407227][G loss -23065.812500 ]time: 0:08:50.061938 
[Epoch 12/2000][Batch 4/48][Autoencoder loss: 904.680481][C loss: 1.894960][M loss: 0.693293][D loss: -8053.680664][G loss -25497.123047 ]time: 0:08:50.506787 
[Epoch 12/2000][Batch 5/48][Autoencoder loss: 1879.584839][C loss: 1.621831][M loss: 0.693291][D loss: -8096.771484][G loss -28343.261719 ]time: 0:08:50.942388 
[Epoch 12/2000][Batch 6/48][Autoencoder loss: 1523.967529][C loss: 1.338305][M loss: 0.693291][D loss: -8634.617188][G loss -29742.621094 ]time: 0:08:51.386380 
[Epoch 12/2000][Batch 7/48][Autoencoder loss: 1153.211060][C loss: 1.188122][M loss: 0.693291][D loss: -10655.275391][G loss -31660.839844 ]time: 0:08:51.830513 
[Epoch 12/2000][Batch 8/48][Autoencoder loss: 429.375977][C loss: 1.378179][M loss: 0.693290][D loss: -17811.865234][G loss -26396.322266 ]time: 0:08:52.285783 
[Epoch 12/2000][Batch 9/48][Autoencoder loss: 705.198425][C loss: 1.353319][M loss: 0.693288][D loss: -18468.148438][G loss -29115.167969 ]time: 0:08:52.726675 
[Epoch 12/2000][Batch 10/48][Autoencoder loss: 660.158264][C loss: 1.204170][M loss: 0.693290][D loss: -19193.185547][G loss -30556.142578 ]time: 0:08:53.322552 
[Epoch 12/2000][Batch 11/48][Autoencoder loss: 498.365143][C loss: 0.944383][M loss: 0.693291][D loss: -18408.625000][G loss -31845.349609 ]time: 0:08:53.754856 
[Epoch 12/2000][Batch 12/48][Autoencoder loss: 676.386353][C loss: 1.115351][M loss: 0.693289][D loss: -15388.699219][G loss -39580.425781 ]time: 0:08:54.537709 
[Epoch 12/2000][Batch 13/48][Autoencoder loss: 402.240051][C loss: 1.065451][M loss: 0.693289][D loss: -17147.128906][G loss -41980.394531 ]time: 0:08:55.626469 
[Epoch 12/2000][Batch 14/48][Autoencoder loss: 596.239441][C loss: 0.997924][M loss: 0.693289][D loss: -17446.734375][G loss -45047.425781 ]time: 0:08:56.686553 
[Epoch 12/2000][Batch 15/48][Autoencoder loss: 474.504242][C loss: 0.895710][M loss: 0.693290][D loss: -18033.902344][G loss -46423.828125 ]time: 0:08:57.138244 
[Epoch 12/2000][Batch 16/48][Autoencoder loss: 579.749207][C loss: 0.782662][M loss: 0.693301][D loss: -21458.675781][G loss -46234.316406 ]time: 0:08:57.580021 
[Epoch 12/2000][Batch 17/48][Autoencoder loss: 586.639954][C loss: 0.650015][M loss: 0.693291][D loss: -22136.826172][G loss -48543.050781 ]time: 0:08:58.028032 
[Epoch 12/2000][Batch 18/48][Autoencoder loss: 244.566727][C loss: 0.488658][M loss: 0.693288][D loss: -23811.707031][G loss -51905.910156 ]time: 0:08:58.472432 
[Epoch 12/2000][Batch 19/48][Autoencoder loss: 374.717834][C loss: 0.441141][M loss: 0.693289][D loss: -23871.277344][G loss -54746.789062 ]time: 0:08:58.918175 
[Epoch 12/2000][Batch 20/48][Autoencoder loss: 2766.731201][C loss: 1.030826][M loss: 0.693327][D loss: -21965.523438][G loss -57354.378906 ]time: 0:08:59.361044 
[Epoch 12/2000][Batch 21/48][Autoencoder loss: 7079.251953][C loss: 1.080423][M loss: 0.693295][D loss: -26758.724609][G loss -59476.832031 ]time: 0:08:59.809019 
[Epoch 12/2000][Batch 22/48][Autoencoder loss: 5473.234375][C loss: 0.840305][M loss: 0.697186][D loss: -26544.054688][G loss -61335.667969 ]time: 0:09:00.261479 
[Epoch 12/2000][Batch 23/48][Autoencoder loss: 4351.199707][C loss: 0.616937][M loss: 0.693880][D loss: -22335.353516][G loss -65146.027344 ]time: 0:09:00.706814 
[Epoch 12/2000][Batch 24/48][Autoencoder loss: 4312.379883][C loss: 1.383866][M loss: 0.693944][D loss: -17026.681641][G loss -70341.570312 ]time: 0:09:01.149855 
[Epoch 12/2000][Batch 25/48][Autoencoder loss: 5537.914551][C loss: 1.521410][M loss: 0.693623][D loss: -16732.876953][G loss -73469.414062 ]time: 0:09:01.591015 
[Epoch 12/2000][Batch 26/48][Autoencoder loss: 7622.358398][C loss: 1.789708][M loss: 0.693338][D loss: -22659.445312][G loss -74038.281250 ]time: 0:09:02.098862 
[Epoch 12/2000][Batch 27/48][Autoencoder loss: 3754.911133][C loss: 1.685120][M loss: 0.693369][D loss: -25047.988281][G loss -77850.695312 ]time: 0:09:02.599535 
[Epoch 12/2000][Batch 28/48][Autoencoder loss: 7752.014648][C loss: 2.380351][M loss: 0.693556][D loss: -13693.899414][G loss -87456.601562 ]time: 0:09:03.039488 
[Epoch 12/2000][Batch 29/48][Autoencoder loss: 3170.018066][C loss: 2.096538][M loss: 0.693523][D loss: -10123.855469][G loss -90163.351562 ]time: 0:09:03.471225 
[Epoch 12/2000][Batch 30/48][Autoencoder loss: 3931.920898][C loss: 2.048822][M loss: 0.693379][D loss: -9778.281250][G loss -93359.804688 ]time: 0:09:03.915429 
[Epoch 12/2000][Batch 31/48][Autoencoder loss: 3620.109863][C loss: 1.857039][M loss: 0.693330][D loss: -12209.245117][G loss -93245.492188 ]time: 0:09:04.359633 
[Epoch 12/2000][Batch 32/48][Autoencoder loss: 1085.184204][C loss: 2.144733][M loss: 0.693418][D loss: -34039.367188][G loss -74813.718750 ]time: 0:09:04.798970 
[Epoch 12/2000][Batch 33/48][Autoencoder loss: 2637.422607][C loss: 1.813930][M loss: 0.693409][D loss: -34702.460938][G loss -77655.132812 ]time: 0:09:05.235881 
[Epoch 12/2000][Batch 34/48][Autoencoder loss: 978.719421][C loss: 1.512680][M loss: 0.693332][D loss: -30937.906250][G loss -80333.710938 ]time: 0:09:06.147544 
[Epoch 12/2000][Batch 35/48][Autoencoder loss: 1036.087524][C loss: 1.230921][M loss: 0.693307][D loss: -31090.039062][G loss -83929.507812 ]time: 0:09:07.073075 
[Epoch 12/2000][Batch 36/48][Autoencoder loss: 1775.465942][C loss: 1.357271][M loss: 0.693340][D loss: -33271.820312][G loss -87638.367188 ]time: 0:09:07.511283 
[Epoch 12/2000][Batch 37/48][Autoencoder loss: 765.622070][C loss: 1.440579][M loss: 0.693340][D loss: -34427.523438][G loss -87054.796875 ]time: 0:09:07.961476 
[Epoch 12/2000][Batch 38/48][Autoencoder loss: 1824.318604][C loss: 1.381741][M loss: 0.693319][D loss: -32439.156250][G loss -87523.210938 ]time: 0:09:08.404993 
[Epoch 12/2000][Batch 39/48][Autoencoder loss: 1346.170044][C loss: 1.222453][M loss: 0.693306][D loss: -30007.212891][G loss -92218.851562 ]time: 0:09:09.247461 
[Epoch 12/2000][Batch 40/48][Autoencoder loss: 1376.773682][C loss: 1.572401][M loss: 0.693324][D loss: -25145.826172][G loss -97885.617188 ]time: 0:09:10.277992 
[Epoch 12/2000][Batch 41/48][Autoencoder loss: 1868.116699][C loss: 1.591900][M loss: 0.693318][D loss: -24805.453125][G loss -100955.015625 ]time: 0:09:10.786853 
[Epoch 12/2000][Batch 42/48][Autoencoder loss: 1080.307861][C loss: 1.500007][M loss: 0.693333][D loss: -27895.460938][G loss -100318.703125 ]time: 0:09:11.227833 
[Epoch 12/2000][Batch 43/48][Autoencoder loss: 1329.373413][C loss: 1.304246][M loss: 0.693313][D loss: -24504.753906][G loss -104822.617188 ]time: 0:09:11.678508 
[Epoch 12/2000][Batch 44/48][Autoencoder loss: 892.487000][C loss: 0.987631][M loss: 0.693307][D loss: -33516.414062][G loss -98001.609375 ]time: 0:09:12.121563 
[Epoch 12/2000][Batch 45/48][Autoencoder loss: 507.720428][C loss: 0.872054][M loss: 0.693310][D loss: -26920.289062][G loss -103166.703125 ]time: 0:09:12.616853 
[Epoch 12/2000][Batch 46/48][Autoencoder loss: 917.485962][C loss: 0.726598][M loss: 0.693312][D loss: -34412.648438][G loss -101217.648438 ]time: 0:09:13.064986 
[Epoch 12/2000][Batch 47/48][Autoencoder loss: 398.337616][C loss: 0.525346][M loss: 0.693307][D loss: -30807.773438][G loss -105460.078125 ]time: 0:09:13.547404 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 436us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 454us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 428us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 12/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.345139] [Accuracy_pnb: 0.333333][Accuracy_mlp: 0.339931]
[Epoch 13/2000][Batch 0/48][Autoencoder loss: 699.360168][C loss: 1.416784][M loss: 0.693315][D loss: 2441.175781][G loss -139076.968750 ]time: 0:09:32.628778 
[Epoch 13/2000][Batch 1/48][Autoencoder loss: 1007.745911][C loss: 1.560600][M loss: 0.693299][D loss: 3323.503906][G loss -139661.171875 ]time: 0:09:33.075830 
[Epoch 13/2000][Batch 2/48][Autoencoder loss: 420.175201][C loss: 1.604510][M loss: 0.693297][D loss: 10350.671875][G loss -142339.265625 ]time: 0:09:33.522297 
[Epoch 13/2000][Batch 3/48][Autoencoder loss: 704.927368][C loss: 1.584547][M loss: 0.693301][D loss: 11311.856445][G loss -143596.984375 ]time: 0:09:34.011618 
[Epoch 13/2000][Batch 4/48][Autoencoder loss: 854.203247][C loss: 1.850979][M loss: 0.693300][D loss: 6993.958008][G loss -142957.703125 ]time: 0:09:34.481947 
[Epoch 13/2000][Batch 5/48][Autoencoder loss: 1785.852295][C loss: 1.685240][M loss: 0.693315][D loss: 6039.446289][G loss -142296.078125 ]time: 0:09:34.920307 
[Epoch 13/2000][Batch 6/48][Autoencoder loss: 1503.492065][C loss: 1.435080][M loss: 0.693301][D loss: 11286.888672][G loss -144610.531250 ]time: 0:09:35.501103 
[Epoch 13/2000][Batch 7/48][Autoencoder loss: 984.063171][C loss: 1.225814][M loss: 0.693299][D loss: 16743.833984][G loss -146749.828125 ]time: 0:09:36.051460 
[Epoch 13/2000][Batch 8/48][Autoencoder loss: 736.812256][C loss: 1.063661][M loss: 0.693299][D loss: -22276.474609][G loss -111489.085938 ]time: 0:09:36.498881 
[Epoch 13/2000][Batch 9/48][Autoencoder loss: 418.805206][C loss: 1.026862][M loss: 0.693293][D loss: -21225.683594][G loss -110216.000000 ]time: 0:09:36.938441 
[Epoch 13/2000][Batch 10/48][Autoencoder loss: 650.490784][C loss: 0.924178][M loss: 0.693294][D loss: -20720.804688][G loss -112982.546875 ]time: 0:09:37.373676 
[Epoch 13/2000][Batch 11/48][Autoencoder loss: 532.685852][C loss: 0.802372][M loss: 0.693293][D loss: -15830.125000][G loss -109104.351562 ]time: 0:09:37.815869 
[Epoch 13/2000][Batch 12/48][Autoencoder loss: 398.053253][C loss: 0.945462][M loss: 0.693294][D loss: 6338.848633][G loss -132629.156250 ]time: 0:09:38.267066 
[Epoch 13/2000][Batch 13/48][Autoencoder loss: 578.965088][C loss: 0.923195][M loss: 0.693294][D loss: 7645.623047][G loss -134837.281250 ]time: 0:09:39.111031 
[Epoch 13/2000][Batch 14/48][Autoencoder loss: 397.657166][C loss: 0.897231][M loss: 0.693295][D loss: 6318.157227][G loss -133381.453125 ]time: 0:09:39.699744 
[Epoch 13/2000][Batch 15/48][Autoencoder loss: 486.064606][C loss: 0.874830][M loss: 0.693294][D loss: 9693.169922][G loss -132607.203125 ]time: 0:09:40.132501 
[Epoch 13/2000][Batch 16/48][Autoencoder loss: 598.529419][C loss: 0.906083][M loss: 0.693303][D loss: 6708.336914][G loss -126642.109375 ]time: 0:09:40.973126 
[Epoch 13/2000][Batch 17/48][Autoencoder loss: 401.095123][C loss: 0.714028][M loss: 0.693298][D loss: 9874.737305][G loss -124188.718750 ]time: 0:09:42.057782 
[Epoch 13/2000][Batch 18/48][Autoencoder loss: 307.364746][C loss: 0.509415][M loss: 0.693294][D loss: 7270.438477][G loss -126550.968750 ]time: 0:09:43.032934 
[Epoch 13/2000][Batch 19/48][Autoencoder loss: 231.502502][C loss: 0.425322][M loss: 0.693293][D loss: 5875.377930][G loss -123670.179688 ]time: 0:09:43.487903 
[Epoch 13/2000][Batch 20/48][Autoencoder loss: 2751.528809][C loss: 0.767007][M loss: 0.693317][D loss: 8120.770508][G loss -130251.898438 ]time: 0:09:43.929554 
[Epoch 13/2000][Batch 21/48][Autoencoder loss: 6587.950684][C loss: 0.773096][M loss: 0.693301][D loss: 9697.313477][G loss -130086.351562 ]time: 0:09:44.376707 
[Epoch 13/2000][Batch 22/48][Autoencoder loss: 5637.218262][C loss: 0.577203][M loss: 0.711838][D loss: 9120.035156][G loss -127063.570312 ]time: 0:09:44.816766 
[Epoch 13/2000][Batch 23/48][Autoencoder loss: 3871.012939][C loss: 0.477508][M loss: 0.694549][D loss: 16929.671875][G loss -127918.015625 ]time: 0:09:45.264060 
[Epoch 13/2000][Batch 24/48][Autoencoder loss: 4377.156738][C loss: 1.342101][M loss: 0.695530][D loss: 26475.054688][G loss -129990.734375 ]time: 0:09:45.707445 
[Epoch 13/2000][Batch 25/48][Autoencoder loss: 4807.579102][C loss: 1.292291][M loss: 0.694192][D loss: 27365.820312][G loss -126305.882812 ]time: 0:09:46.145356 
[Epoch 13/2000][Batch 26/48][Autoencoder loss: 7506.102051][C loss: 1.426222][M loss: 0.693459][D loss: 24898.509766][G loss -122914.914062 ]time: 0:09:46.613340 
[Epoch 13/2000][Batch 27/48][Autoencoder loss: 3846.221924][C loss: 1.322290][M loss: 0.693788][D loss: 19664.652344][G loss -125091.031250 ]time: 0:09:47.049787 
[Epoch 13/2000][Batch 28/48][Autoencoder loss: 6828.885254][C loss: 2.167483][M loss: 0.694212][D loss: 35346.460938][G loss -135299.562500 ]time: 0:09:47.496193 
[Epoch 13/2000][Batch 29/48][Autoencoder loss: 3666.258545][C loss: 1.902545][M loss: 0.694001][D loss: 40502.253906][G loss -133813.796875 ]time: 0:09:47.939550 
[Epoch 13/2000][Batch 30/48][Autoencoder loss: 3096.149658][C loss: 1.798602][M loss: 0.693595][D loss: 42008.351562][G loss -126701.164062 ]time: 0:09:48.378297 
[Epoch 13/2000][Batch 31/48][Autoencoder loss: 3620.203125][C loss: 1.588627][M loss: 0.693623][D loss: 43126.320312][G loss -121471.718750 ]time: 0:09:49.009192 
[Epoch 13/2000][Batch 32/48][Autoencoder loss: 1148.984741][C loss: 1.793818][M loss: 0.693782][D loss: 5157.935547][G loss -95299.796875 ]time: 0:09:49.717145 
[Epoch 13/2000][Batch 33/48][Autoencoder loss: 1835.772339][C loss: 1.651015][M loss: 0.693612][D loss: 8076.639160][G loss -89557.429688 ]time: 0:09:50.794978 
[Epoch 13/2000][Batch 34/48][Autoencoder loss: 1505.234253][C loss: 1.456168][M loss: 0.693462][D loss: 11158.492188][G loss -87585.882812 ]time: 0:09:51.248098 
[Epoch 13/2000][Batch 35/48][Autoencoder loss: 601.902100][C loss: 1.224969][M loss: 0.693440][D loss: 12727.412109][G loss -86221.250000 ]time: 0:09:51.699585 
[Epoch 13/2000][Batch 36/48][Autoencoder loss: 1723.894165][C loss: 1.308349][M loss: 0.693473][D loss: 13502.515625][G loss -82002.953125 ]time: 0:09:52.148649 
[Epoch 13/2000][Batch 37/48][Autoencoder loss: 1085.278809][C loss: 1.323636][M loss: 0.693446][D loss: 11676.240234][G loss -79395.570312 ]time: 0:09:52.595145 
[Epoch 13/2000][Batch 38/48][Autoencoder loss: 1137.160156][C loss: 1.241552][M loss: 0.693336][D loss: 12056.867188][G loss -78490.429688 ]time: 0:09:53.084443 
[Epoch 13/2000][Batch 39/48][Autoencoder loss: 1656.155762][C loss: 1.107747][M loss: 0.693332][D loss: 15035.673828][G loss -76199.117188 ]time: 0:09:53.530579 
[Epoch 13/2000][Batch 40/48][Autoencoder loss: 1111.837036][C loss: 1.382867][M loss: 0.693361][D loss: 17228.861328][G loss -74281.929688 ]time: 0:09:53.973907 
[Epoch 13/2000][Batch 41/48][Autoencoder loss: 1636.093628][C loss: 1.362886][M loss: 0.693361][D loss: 14304.662109][G loss -71849.984375 ]time: 0:09:54.423175 
[Epoch 13/2000][Batch 42/48][Autoencoder loss: 1403.287476][C loss: 1.248007][M loss: 0.693351][D loss: 12767.069336][G loss -67974.390625 ]time: 0:09:54.871344 
[Epoch 13/2000][Batch 43/48][Autoencoder loss: 927.682434][C loss: 1.086408][M loss: 0.693329][D loss: 12240.332031][G loss -65547.234375 ]time: 0:09:55.312159 
[Epoch 13/2000][Batch 44/48][Autoencoder loss: 1051.405396][C loss: 0.890204][M loss: 0.693316][D loss: 8845.656250][G loss -57271.058594 ]time: 0:09:56.047478 
[Epoch 13/2000][Batch 45/48][Autoencoder loss: 469.151245][C loss: 0.838985][M loss: 0.693315][D loss: 9426.386719][G loss -54731.378906 ]time: 0:09:57.031986 
[Epoch 13/2000][Batch 46/48][Autoencoder loss: 578.611267][C loss: 0.724556][M loss: 0.693330][D loss: 9555.373047][G loss -53833.359375 ]time: 0:09:58.265399 
[Epoch 13/2000][Batch 47/48][Autoencoder loss: 616.280090][C loss: 0.556884][M loss: 0.693303][D loss: 8849.982422][G loss -52100.660156 ]time: 0:09:59.002116 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 408us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 501us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 403us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 13/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.345139] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.339931]
[Epoch 14/2000][Batch 0/48][Autoencoder loss: 688.414490][C loss: 1.264582][M loss: 0.693326][D loss: 25449.066406][G loss -64230.500000 ]time: 0:10:19.069341 
[Epoch 14/2000][Batch 1/48][Autoencoder loss: 1005.770813][C loss: 1.419212][M loss: 0.693311][D loss: 25804.976562][G loss -61633.304688 ]time: 0:10:19.530007 
[Epoch 14/2000][Batch 2/48][Autoencoder loss: 754.240784][C loss: 1.449710][M loss: 0.693315][D loss: 24295.234375][G loss -56752.949219 ]time: 0:10:19.980159 
[Epoch 14/2000][Batch 3/48][Autoencoder loss: 441.229034][C loss: 1.387476][M loss: 0.693306][D loss: 21940.875000][G loss -50212.703125 ]time: 0:10:20.547832 
[Epoch 14/2000][Batch 4/48][Autoencoder loss: 996.803284][C loss: 1.489239][M loss: 0.693307][D loss: 19624.785156][G loss -45551.226562 ]time: 0:10:20.996280 
[Epoch 14/2000][Batch 5/48][Autoencoder loss: 1635.050781][C loss: 1.463876][M loss: 0.693313][D loss: 17515.318359][G loss -38376.808594 ]time: 0:10:21.462332 
[Epoch 14/2000][Batch 6/48][Autoencoder loss: 1412.973511][C loss: 1.312393][M loss: 0.693317][D loss: 15789.892578][G loss -32621.007812 ]time: 0:10:22.016104 
[Epoch 14/2000][Batch 7/48][Autoencoder loss: 1003.984253][C loss: 1.102086][M loss: 0.693303][D loss: 12686.750000][G loss -25783.675781 ]time: 0:10:22.490362 
[Epoch 14/2000][Batch 8/48][Autoencoder loss: 441.283234][C loss: 1.234995][M loss: 0.693300][D loss: 4448.217285][G loss -15131.566406 ]time: 0:10:22.962591 
[Epoch 14/2000][Batch 9/48][Autoencoder loss: 384.170776][C loss: 1.065477][M loss: 0.693297][D loss: 3603.858887][G loss -10755.558594 ]time: 0:10:23.408041 
[Epoch 14/2000][Batch 10/48][Autoencoder loss: 375.738922][C loss: 0.925332][M loss: 0.693300][D loss: 2480.259277][G loss -6577.785645 ]time: 0:10:23.862159 
[Epoch 14/2000][Batch 11/48][Autoencoder loss: 425.709106][C loss: 0.751870][M loss: 0.693301][D loss: 2062.775391][G loss -2623.064209 ]time: 0:10:24.313576 
[Epoch 14/2000][Batch 12/48][Autoencoder loss: 360.479767][C loss: 0.804479][M loss: 0.693303][D loss: 1943.286377][G loss 1763.266846 ]time: 0:10:25.144525 
[Epoch 14/2000][Batch 13/48][Autoencoder loss: 434.292633][C loss: 0.753556][M loss: 0.693295][D loss: 465.947449][G loss 6670.526855 ]time: 0:10:26.145372 
[Epoch 14/2000][Batch 14/48][Autoencoder loss: 402.369965][C loss: 0.699500][M loss: 0.693296][D loss: -1004.632935][G loss 11824.647461 ]time: 0:10:26.590258 
[Epoch 14/2000][Batch 15/48][Autoencoder loss: 326.765228][C loss: 0.637887][M loss: 0.693298][D loss: -2610.938721][G loss 17494.000000 ]time: 0:10:27.037715 
[Epoch 14/2000][Batch 16/48][Autoencoder loss: 474.225189][C loss: 0.597310][M loss: 0.693316][D loss: -3101.145264][G loss 21036.128906 ]time: 0:10:27.498908 
[Epoch 14/2000][Batch 17/48][Autoencoder loss: 341.092468][C loss: 0.497139][M loss: 0.693301][D loss: -4418.480957][G loss 26529.337891 ]time: 0:10:27.943606 
[Epoch 14/2000][Batch 18/48][Autoencoder loss: 197.561523][C loss: 0.401072][M loss: 0.693299][D loss: -5800.328125][G loss 30780.128906 ]time: 0:10:28.395062 
[Epoch 14/2000][Batch 19/48][Autoencoder loss: 214.258438][C loss: 0.303285][M loss: 0.693297][D loss: -6926.814453][G loss 35964.179688 ]time: 0:10:28.839749 
[Epoch 14/2000][Batch 20/48][Autoencoder loss: 2931.440918][C loss: 1.218023][M loss: 0.693349][D loss: -9996.522461][G loss 42305.566406 ]time: 0:10:29.757720 
[Epoch 14/2000][Batch 21/48][Autoencoder loss: 6632.306152][C loss: 1.178159][M loss: 0.693422][D loss: -9846.304688][G loss 46942.234375 ]time: 0:10:30.927686 
[Epoch 14/2000][Batch 22/48][Autoencoder loss: 5733.119141][C loss: 0.901166][M loss: 0.759154][D loss: -10666.483398][G loss 51793.230469 ]time: 0:10:32.106326 
[Epoch 14/2000][Batch 23/48][Autoencoder loss: 4391.600098][C loss: 0.765251][M loss: 0.698240][D loss: -14575.195312][G loss 57405.914062 ]time: 0:10:32.800669 
[Epoch 14/2000][Batch 24/48][Autoencoder loss: 4833.726562][C loss: 1.258707][M loss: 0.698678][D loss: -19384.138672][G loss 63464.824219 ]time: 0:10:33.260012 
[Epoch 14/2000][Batch 25/48][Autoencoder loss: 5376.955078][C loss: 1.240090][M loss: 0.695874][D loss: -21767.847656][G loss 71071.750000 ]time: 0:10:33.717446 
[Epoch 14/2000][Batch 26/48][Autoencoder loss: 9101.294922][C loss: 1.504395][M loss: 0.694465][D loss: -22348.876953][G loss 78375.156250 ]time: 0:10:34.179508 
[Epoch 14/2000][Batch 27/48][Autoencoder loss: 4121.217285][C loss: 1.511212][M loss: 0.694543][D loss: -21950.644531][G loss 81995.718750 ]time: 0:10:34.910605 
[Epoch 14/2000][Batch 28/48][Autoencoder loss: 7428.008301][C loss: 2.179108][M loss: 0.696329][D loss: -35976.375000][G loss 101691.726562 ]time: 0:10:35.385589 
[Epoch 14/2000][Batch 29/48][Autoencoder loss: 4679.182617][C loss: 1.918603][M loss: 0.695735][D loss: -40719.441406][G loss 110462.531250 ]time: 0:10:36.422503 
[Epoch 14/2000][Batch 30/48][Autoencoder loss: 2887.945557][C loss: 1.798938][M loss: 0.693937][D loss: -42004.746094][G loss 116188.507812 ]time: 0:10:37.432828 
[Epoch 14/2000][Batch 31/48][Autoencoder loss: 4431.232422][C loss: 1.563384][M loss: 0.695018][D loss: -45451.156250][G loss 125071.085938 ]time: 0:10:37.897922 
[Epoch 14/2000][Batch 32/48][Autoencoder loss: 1908.317871][C loss: 2.051596][M loss: 0.694451][D loss: -18170.582031][G loss 104582.296875 ]time: 0:10:38.347465 
[Epoch 14/2000][Batch 33/48][Autoencoder loss: 1464.010132][C loss: 1.828832][M loss: 0.694117][D loss: -14883.191406][G loss 106029.171875 ]time: 0:10:38.804701 
[Epoch 14/2000][Batch 34/48][Autoencoder loss: 2498.471436][C loss: 1.485352][M loss: 0.693575][D loss: -18613.947266][G loss 115566.000000 ]time: 0:10:39.283960 
[Epoch 14/2000][Batch 35/48][Autoencoder loss: 634.074463][C loss: 1.175647][M loss: 0.693508][D loss: -19679.544922][G loss 115761.515625 ]time: 0:10:39.775769 
[Epoch 14/2000][Batch 36/48][Autoencoder loss: 1610.228027][C loss: 1.229111][M loss: 0.693559][D loss: -23321.716797][G loss 123898.820312 ]time: 0:10:40.218594 
[Epoch 14/2000][Batch 37/48][Autoencoder loss: 1868.123047][C loss: 1.082972][M loss: 0.693550][D loss: -17269.976562][G loss 131958.953125 ]time: 0:10:40.666249 
[Epoch 14/2000][Batch 38/48][Autoencoder loss: 779.660034][C loss: 0.988207][M loss: 0.693423][D loss: -19345.156250][G loss 133984.171875 ]time: 0:10:41.108717 
[Epoch 14/2000][Batch 39/48][Autoencoder loss: 1949.443970][C loss: 0.977998][M loss: 0.693423][D loss: -18648.191406][G loss 136334.078125 ]time: 0:10:41.574519 
[Epoch 14/2000][Batch 40/48][Autoencoder loss: 1569.912476][C loss: 1.360029][M loss: 0.693833][D loss: -23878.992188][G loss 140155.234375 ]time: 0:10:42.031023 
[Epoch 14/2000][Batch 41/48][Autoencoder loss: 1372.794922][C loss: 1.399314][M loss: 0.693621][D loss: -23381.492188][G loss 149675.328125 ]time: 0:10:42.487836 
[Epoch 14/2000][Batch 42/48][Autoencoder loss: 1951.220337][C loss: 1.268009][M loss: 0.693459][D loss: -18409.578125][G loss 149106.937500 ]time: 0:10:42.933961 
[Epoch 14/2000][Batch 43/48][Autoencoder loss: 1019.671753][C loss: 1.082985][M loss: 0.693426][D loss: -16085.291016][G loss 151731.593750 ]time: 0:10:43.425406 
[Epoch 14/2000][Batch 44/48][Autoencoder loss: 906.831116][C loss: 1.257848][M loss: 0.693330][D loss: -6945.814453][G loss 143856.937500 ]time: 0:10:43.873899 
[Epoch 14/2000][Batch 45/48][Autoencoder loss: 951.985291][C loss: 1.234131][M loss: 0.693348][D loss: -10477.374023][G loss 145040.968750 ]time: 0:10:44.315573 
[Epoch 14/2000][Batch 46/48][Autoencoder loss: 337.990417][C loss: 1.099286][M loss: 0.693356][D loss: -7828.975586][G loss 147042.203125 ]time: 0:10:44.767212 
[Epoch 14/2000][Batch 47/48][Autoencoder loss: 717.524658][C loss: 0.951159][M loss: 0.693337][D loss: -8326.770508][G loss 147634.640625 ]time: 0:10:45.219877 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 555us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 428us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 421us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 14/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.345139] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.362500]
[Epoch 15/2000][Batch 0/48][Autoencoder loss: 1335.377441][C loss: 2.244594][M loss: 0.693474][D loss: -51752.031250][G loss 195774.703125 ]time: 0:11:04.716542 
[Epoch 15/2000][Batch 1/48][Autoencoder loss: 601.271606][C loss: 1.940112][M loss: 0.693336][D loss: -50301.230469][G loss 199409.562500 ]time: 0:11:05.788319 
[Epoch 15/2000][Batch 2/48][Autoencoder loss: 1223.135254][C loss: 1.696522][M loss: 0.693321][D loss: -50526.203125][G loss 201173.859375 ]time: 0:11:06.249819 
[Epoch 15/2000][Batch 3/48][Autoencoder loss: 519.305542][C loss: 1.576433][M loss: 0.693327][D loss: -51483.402344][G loss 201464.906250 ]time: 0:11:06.698760 
[Epoch 15/2000][Batch 4/48][Autoencoder loss: 837.686829][C loss: 1.201707][M loss: 0.693377][D loss: -42260.019531][G loss 203767.453125 ]time: 0:11:07.153822 
[Epoch 15/2000][Batch 5/48][Autoencoder loss: 1889.995728][C loss: 1.331246][M loss: 0.693327][D loss: -45459.855469][G loss 207680.750000 ]time: 0:11:07.606468 
[Epoch 15/2000][Batch 6/48][Autoencoder loss: 1426.756958][C loss: 1.308226][M loss: 0.693323][D loss: -40413.125000][G loss 214622.593750 ]time: 0:11:08.117666 
[Epoch 15/2000][Batch 7/48][Autoencoder loss: 1014.070923][C loss: 1.198056][M loss: 0.693310][D loss: -46020.179688][G loss 210417.562500 ]time: 0:11:08.570174 
[Epoch 15/2000][Batch 8/48][Autoencoder loss: 375.511047][C loss: 1.522270][M loss: 0.693303][D loss: 8262.115234][G loss 161493.578125 ]time: 0:11:09.007854 
[Epoch 15/2000][Batch 9/48][Autoencoder loss: 393.288574][C loss: 1.435766][M loss: 0.693301][D loss: 13193.992188][G loss 160964.203125 ]time: 0:11:09.468654 
[Epoch 15/2000][Batch 10/48][Autoencoder loss: 342.457001][C loss: 1.294612][M loss: 0.693297][D loss: 14768.868164][G loss 163569.296875 ]time: 0:11:09.926730 
[Epoch 15/2000][Batch 11/48][Autoencoder loss: 357.788391][C loss: 1.142146][M loss: 0.693307][D loss: 15076.632812][G loss 159208.796875 ]time: 0:11:10.373238 
[Epoch 15/2000][Batch 12/48][Autoencoder loss: 356.294586][C loss: 1.287234][M loss: 0.693298][D loss: -8293.962891][G loss 190805.500000 ]time: 0:11:11.060075 
[Epoch 15/2000][Batch 13/48][Autoencoder loss: 372.865692][C loss: 1.171420][M loss: 0.693303][D loss: -2951.519531][G loss 182614.406250 ]time: 0:11:12.313948 
[Epoch 15/2000][Batch 14/48][Autoencoder loss: 388.629181][C loss: 1.057429][M loss: 0.693309][D loss: -1386.319336][G loss 185905.546875 ]time: 0:11:12.911029 
[Epoch 15/2000][Batch 15/48][Autoencoder loss: 325.721802][C loss: 1.138461][M loss: 0.693300][D loss: 3069.196289][G loss 178292.234375 ]time: 0:11:13.358280 
[Epoch 15/2000][Batch 16/48][Autoencoder loss: 459.709747][C loss: 1.347212][M loss: 0.693309][D loss: 13103.673828][G loss 166752.296875 ]time: 0:11:13.810617 
[Epoch 15/2000][Batch 17/48][Autoencoder loss: 407.985748][C loss: 1.248740][M loss: 0.693294][D loss: 14257.862305][G loss 162836.531250 ]time: 0:11:14.256802 
[Epoch 15/2000][Batch 18/48][Autoencoder loss: 197.597229][C loss: 0.880665][M loss: 0.693292][D loss: 18293.162109][G loss 160493.609375 ]time: 0:11:14.757782 
[Epoch 15/2000][Batch 19/48][Autoencoder loss: 204.569778][C loss: 0.655040][M loss: 0.693295][D loss: 16312.738281][G loss 156080.812500 ]time: 0:11:15.239629 
[Epoch 15/2000][Batch 20/48][Autoencoder loss: 3045.773438][C loss: 0.619420][M loss: 0.693473][D loss: 15036.961914][G loss 156387.890625 ]time: 0:11:15.701134 
[Epoch 15/2000][Batch 21/48][Autoencoder loss: 7318.062988][C loss: 0.569046][M loss: 0.693320][D loss: 22165.328125][G loss 152423.859375 ]time: 0:11:16.145350 
[Epoch 15/2000][Batch 22/48][Autoencoder loss: 5669.558105][C loss: 0.349765][M loss: 0.694625][D loss: 25859.275391][G loss 148288.843750 ]time: 0:11:16.582857 
[Epoch 15/2000][Batch 23/48][Autoencoder loss: 5397.523926][C loss: 0.279381][M loss: 0.694556][D loss: 19982.832031][G loss 145084.671875 ]time: 0:11:17.058524 
[Epoch 15/2000][Batch 24/48][Autoencoder loss: 6686.200195][C loss: 1.345702][M loss: 0.693498][D loss: 9695.476562][G loss 146442.265625 ]time: 0:11:17.511129 
[Epoch 15/2000][Batch 25/48][Autoencoder loss: 5342.566895][C loss: 1.294220][M loss: 0.693365][D loss: 7659.156250][G loss 139977.140625 ]time: 0:11:17.949896 
[Epoch 15/2000][Batch 26/48][Autoencoder loss: 11823.016602][C loss: 1.419365][M loss: 0.693341][D loss: 12022.158203][G loss 133718.484375 ]time: 0:11:18.859143 
[Epoch 15/2000][Batch 27/48][Autoencoder loss: 5168.072754][C loss: 1.371511][M loss: 0.693331][D loss: 22692.445312][G loss 130345.617188 ]time: 0:11:19.920250 
[Epoch 15/2000][Batch 28/48][Autoencoder loss: 7152.409668][C loss: 2.014617][M loss: 0.693472][D loss: 11894.341797][G loss 136708.343750 ]time: 0:11:20.841376 
[Epoch 15/2000][Batch 29/48][Autoencoder loss: 7020.717773][C loss: 1.713248][M loss: 0.693400][D loss: 7600.803711][G loss 133522.640625 ]time: 0:11:21.281242 
[Epoch 15/2000][Batch 30/48][Autoencoder loss: 2886.100830][C loss: 1.557691][M loss: 0.693388][D loss: 4620.672852][G loss 131570.046875 ]time: 0:11:21.814203 
[Epoch 15/2000][Batch 31/48][Autoencoder loss: 4800.553223][C loss: 1.353098][M loss: 0.693349][D loss: 7527.252930][G loss 125184.070312 ]time: 0:11:22.896390 
[Epoch 15/2000][Batch 32/48][Autoencoder loss: 3985.794678][C loss: 1.374423][M loss: 0.693321][D loss: 37931.875000][G loss 93719.484375 ]time: 0:11:23.736877 
[Epoch 15/2000][Batch 33/48][Autoencoder loss: 750.640869][C loss: 1.325075][M loss: 0.693325][D loss: 41713.023438][G loss 90676.250000 ]time: 0:11:24.188959 
[Epoch 15/2000][Batch 34/48][Autoencoder loss: 3257.221924][C loss: 1.239618][M loss: 0.693322][D loss: 43289.582031][G loss 86207.703125 ]time: 0:11:24.643097 
[Epoch 15/2000][Batch 35/48][Autoencoder loss: 2264.224365][C loss: 1.083068][M loss: 0.693322][D loss: 38207.496094][G loss 84192.500000 ]time: 0:11:25.107281 
[Epoch 15/2000][Batch 36/48][Autoencoder loss: 885.299866][C loss: 1.308806][M loss: 0.693323][D loss: 34695.289062][G loss 82623.781250 ]time: 0:11:25.560776 
[Epoch 15/2000][Batch 37/48][Autoencoder loss: 3080.896973][C loss: 1.261582][M loss: 0.693316][D loss: 35106.085938][G loss 77265.554688 ]time: 0:11:26.012399 
[Epoch 15/2000][Batch 38/48][Autoencoder loss: 1577.123901][C loss: 1.174147][M loss: 0.693297][D loss: 37488.375000][G loss 72387.093750 ]time: 0:11:26.457771 
[Epoch 15/2000][Batch 39/48][Autoencoder loss: 1112.452393][C loss: 1.036046][M loss: 0.693293][D loss: 39135.476562][G loss 67954.367188 ]time: 0:11:26.955527 
[Epoch 15/2000][Batch 40/48][Autoencoder loss: 2698.601807][C loss: 0.913743][M loss: 0.693352][D loss: 35198.250000][G loss 66180.750000 ]time: 0:11:27.399638 
[Epoch 15/2000][Batch 41/48][Autoencoder loss: 1369.644653][C loss: 0.888057][M loss: 0.693310][D loss: 30497.650391][G loss 61969.792969 ]time: 0:11:27.871282 
[Epoch 15/2000][Batch 42/48][Autoencoder loss: 1564.479736][C loss: 0.796317][M loss: 0.693312][D loss: 29926.841797][G loss 59947.492188 ]time: 0:11:28.316567 
[Epoch 15/2000][Batch 43/48][Autoencoder loss: 1999.410034][C loss: 0.708438][M loss: 0.693327][D loss: 27884.033203][G loss 56985.699219 ]time: 0:11:28.765234 
[Epoch 15/2000][Batch 44/48][Autoencoder loss: 635.024353][C loss: 1.226530][M loss: 0.693310][D loss: 34067.882812][G loss 50341.546875 ]time: 0:11:29.205145 
[Epoch 15/2000][Batch 45/48][Autoencoder loss: 949.783203][C loss: 1.140964][M loss: 0.693310][D loss: 32401.119141][G loss 46877.113281 ]time: 0:11:29.691207 
[Epoch 15/2000][Batch 46/48][Autoencoder loss: 992.848877][C loss: 0.903921][M loss: 0.693305][D loss: 29157.109375][G loss 44320.460938 ]time: 0:11:30.139584 
[Epoch 15/2000][Batch 47/48][Autoencoder loss: 282.176178][C loss: 0.615730][M loss: 0.693296][D loss: 27338.509766][G loss 39861.773438 ]time: 0:11:30.695600 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 454us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 412us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 470us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 15/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.345139] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.362500]
[Epoch 16/2000][Batch 0/48][Autoencoder loss: 1810.949829][C loss: 1.483259][M loss: 0.693306][D loss: 13553.468750][G loss 48556.550781 ]time: 0:11:50.877410 
[Epoch 16/2000][Batch 1/48][Autoencoder loss: 616.796509][C loss: 1.595435][M loss: 0.693291][D loss: 14353.323242][G loss 46762.750000 ]time: 0:11:51.913660 
[Epoch 16/2000][Batch 2/48][Autoencoder loss: 534.772400][C loss: 1.582767][M loss: 0.693292][D loss: 14182.010742][G loss 42869.117188 ]time: 0:11:53.048759 
[Epoch 16/2000][Batch 3/48][Autoencoder loss: 971.375366][C loss: 1.492815][M loss: 0.693291][D loss: 13359.251953][G loss 40688.074219 ]time: 0:11:53.614581 
[Epoch 16/2000][Batch 4/48][Autoencoder loss: 889.724243][C loss: 1.581374][M loss: 0.693291][D loss: 12578.818359][G loss 37816.136719 ]time: 0:11:54.059790 
[Epoch 16/2000][Batch 5/48][Autoencoder loss: 1647.269287][C loss: 1.566753][M loss: 0.693297][D loss: 12639.824219][G loss 35365.562500 ]time: 0:11:54.509934 
[Epoch 16/2000][Batch 6/48][Autoencoder loss: 2089.445068][C loss: 1.396812][M loss: 0.693292][D loss: 11414.527344][G loss 32777.601562 ]time: 0:11:54.967909 
[Epoch 16/2000][Batch 7/48][Autoencoder loss: 1265.078125][C loss: 1.256840][M loss: 0.693302][D loss: 12036.349609][G loss 31011.642578 ]time: 0:11:55.413564 
[Epoch 16/2000][Batch 8/48][Autoencoder loss: 367.421814][C loss: 1.535025][M loss: 0.693296][D loss: 18361.441406][G loss 22290.162109 ]time: 0:11:55.876275 
[Epoch 16/2000][Batch 9/48][Autoencoder loss: 1054.844849][C loss: 1.485419][M loss: 0.693295][D loss: 16626.703125][G loss 20164.195312 ]time: 0:11:56.818036 
[Epoch 16/2000][Batch 10/48][Autoencoder loss: 803.298706][C loss: 1.268730][M loss: 0.693292][D loss: 15160.410156][G loss 18961.392578 ]time: 0:11:57.920511 
[Epoch 16/2000][Batch 11/48][Autoencoder loss: 380.524261][C loss: 1.024966][M loss: 0.693297][D loss: 13682.341797][G loss 17095.472656 ]time: 0:11:58.481289 
[Epoch 16/2000][Batch 12/48][Autoencoder loss: 900.664307][C loss: 1.041761][M loss: 0.693299][D loss: 10029.022461][G loss 18001.982422 ]time: 0:11:58.934027 
[Epoch 16/2000][Batch 13/48][Autoencoder loss: 567.269653][C loss: 0.931836][M loss: 0.693298][D loss: 9263.310547][G loss 16027.421875 ]time: 0:11:59.403544 
[Epoch 16/2000][Batch 14/48][Autoencoder loss: 397.985413][C loss: 0.898669][M loss: 0.693296][D loss: 8786.890625][G loss 14615.751953 ]time: 0:11:59.894587 
[Epoch 16/2000][Batch 15/48][Autoencoder loss: 744.851807][C loss: 0.972183][M loss: 0.693289][D loss: 7840.775391][G loss 12803.391602 ]time: 0:12:00.336345 
[Epoch 16/2000][Batch 16/48][Autoencoder loss: 513.862488][C loss: 1.158522][M loss: 0.693292][D loss: 7372.904785][G loss 10221.903320 ]time: 0:12:00.783387 
[Epoch 16/2000][Batch 17/48][Autoencoder loss: 499.675201][C loss: 1.065064][M loss: 0.693289][D loss: 6592.453613][G loss 9043.968750 ]time: 0:12:01.239733 
[Epoch 16/2000][Batch 18/48][Autoencoder loss: 440.738861][C loss: 0.855031][M loss: 0.693286][D loss: 5850.496582][G loss 7356.428223 ]time: 0:12:01.681159 
[Epoch 16/2000][Batch 19/48][Autoencoder loss: 209.287888][C loss: 0.674614][M loss: 0.693289][D loss: 4974.400391][G loss 5938.706543 ]time: 0:12:02.129977 
[Epoch 16/2000][Batch 20/48][Autoencoder loss: 2758.244385][C loss: 0.740506][M loss: 0.693397][D loss: 4374.869629][G loss 4513.416016 ]time: 0:12:02.730802 
[Epoch 16/2000][Batch 21/48][Autoencoder loss: 6658.407227][C loss: 0.745273][M loss: 0.693316][D loss: 3882.272949][G loss 3178.251465 ]time: 0:12:03.257728 
[Epoch 16/2000][Batch 22/48][Autoencoder loss: 5659.921875][C loss: 0.492360][M loss: 0.697980][D loss: 3128.442627][G loss 1655.508301 ]time: 0:12:03.715959 
[Epoch 16/2000][Batch 23/48][Autoencoder loss: 4735.732910][C loss: 0.348349][M loss: 0.696500][D loss: 2304.110596][G loss 220.861954 ]time: 0:12:04.215032 
[Epoch 16/2000][Batch 24/48][Autoencoder loss: 7089.210449][C loss: 1.240479][M loss: 0.693814][D loss: 983.972778][G loss -1195.043091 ]time: 0:12:04.657890 
[Epoch 16/2000][Batch 25/48][Autoencoder loss: 4070.580811][C loss: 1.211289][M loss: 0.693590][D loss: 492.443054][G loss -2564.605957 ]time: 0:12:05.092163 
[Epoch 16/2000][Batch 26/48][Autoencoder loss: 10733.545898][C loss: 1.279590][M loss: 0.693538][D loss: -379.429443][G loss -3906.057373 ]time: 0:12:05.587315 
[Epoch 16/2000][Batch 27/48][Autoencoder loss: 6303.229492][C loss: 1.168172][M loss: 0.693535][D loss: -1526.474121][G loss -5128.705566 ]time: 0:12:06.274365 
[Epoch 16/2000][Batch 28/48][Autoencoder loss: 4918.882324][C loss: 1.921401][M loss: 0.693856][D loss: -1942.788452][G loss -7369.211426 ]time: 0:12:07.792112 
[Epoch 16/2000][Batch 29/48][Autoencoder loss: 7072.688477][C loss: 1.855473][M loss: 0.693861][D loss: -2222.168457][G loss -8766.434570 ]time: 0:12:08.661315 
[Epoch 16/2000][Batch 30/48][Autoencoder loss: 4149.833496][C loss: 1.646505][M loss: 0.693583][D loss: -2566.196045][G loss -10013.812500 ]time: 0:12:09.109633 
[Epoch 16/2000][Batch 31/48][Autoencoder loss: 2773.396240][C loss: 1.433376][M loss: 0.693600][D loss: -2365.238037][G loss -11120.208984 ]time: 0:12:09.562577 
[Epoch 16/2000][Batch 32/48][Autoencoder loss: 4757.982910][C loss: 1.350428][M loss: 0.693534][D loss: -5976.658691][G loss -10155.847656 ]time: 0:12:10.009689 
[Epoch 16/2000][Batch 33/48][Autoencoder loss: 1476.554321][C loss: 1.201457][M loss: 0.693380][D loss: -7143.791016][G loss -11310.514648 ]time: 0:12:10.455137 
[Epoch 16/2000][Batch 34/48][Autoencoder loss: 1398.034424][C loss: 1.108007][M loss: 0.693360][D loss: -8436.704102][G loss -12121.016602 ]time: 0:12:10.906562 
[Epoch 16/2000][Batch 35/48][Autoencoder loss: 3614.367188][C loss: 0.967996][M loss: 0.693400][D loss: -8801.560547][G loss -13214.165039 ]time: 0:12:11.369941 
[Epoch 16/2000][Batch 36/48][Autoencoder loss: 820.223816][C loss: 1.516170][M loss: 0.693366][D loss: -8476.390625][G loss -14718.217773 ]time: 0:12:11.826616 
[Epoch 16/2000][Batch 37/48][Autoencoder loss: 1651.585938][C loss: 1.176066][M loss: 0.693327][D loss: -9641.074219][G loss -16364.654297 ]time: 0:12:12.274618 
[Epoch 16/2000][Batch 38/48][Autoencoder loss: 2683.492676][C loss: 1.000155][M loss: 0.693311][D loss: -10730.735352][G loss -17363.195312 ]time: 0:12:12.716741 
[Epoch 16/2000][Batch 39/48][Autoencoder loss: 972.364807][C loss: 0.884056][M loss: 0.693327][D loss: -12606.985352][G loss -18979.857422 ]time: 0:12:13.160963 
[Epoch 16/2000][Batch 40/48][Autoencoder loss: 1745.093994][C loss: 0.888523][M loss: 0.693419][D loss: -13370.060547][G loss -20183.988281 ]time: 0:12:13.601162 
[Epoch 16/2000][Batch 41/48][Autoencoder loss: 2557.574951][C loss: 0.881485][M loss: 0.693370][D loss: -13320.843750][G loss -20922.382812 ]time: 0:12:14.069678 
[Epoch 16/2000][Batch 42/48][Autoencoder loss: 1198.375854][C loss: 0.837184][M loss: 0.693369][D loss: -12631.738281][G loss -23069.339844 ]time: 0:12:14.603438 
[Epoch 16/2000][Batch 43/48][Autoencoder loss: 1524.642822][C loss: 0.784608][M loss: 0.693335][D loss: -13835.590820][G loss -24296.433594 ]time: 0:12:15.050074 
[Epoch 16/2000][Batch 44/48][Autoencoder loss: 1504.271851][C loss: 1.349132][M loss: 0.693313][D loss: -17128.988281][G loss -24697.808594 ]time: 0:12:15.496600 
[Epoch 16/2000][Batch 45/48][Autoencoder loss: 478.378113][C loss: 1.060611][M loss: 0.693315][D loss: -18752.847656][G loss -25727.812500 ]time: 0:12:15.936488 
[Epoch 16/2000][Batch 46/48][Autoencoder loss: 726.820007][C loss: 0.899280][M loss: 0.693313][D loss: -19213.589844][G loss -27175.462891 ]time: 0:12:16.497355 
[Epoch 16/2000][Batch 47/48][Autoencoder loss: 930.997498][C loss: 0.671221][M loss: 0.693301][D loss: -19420.980469][G loss -29052.976562 ]time: 0:12:16.947413 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 501us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 448us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 596us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 16/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.345139] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.362500]
[Epoch 17/2000][Batch 0/48][Autoencoder loss: 735.481812][C loss: 1.920658][M loss: 0.693312][D loss: -10505.849609][G loss -39335.417969 ]time: 0:12:34.107090 
[Epoch 17/2000][Batch 1/48][Autoencoder loss: 559.447510][C loss: 1.828321][M loss: 0.693304][D loss: -12251.907227][G loss -42418.519531 ]time: 0:12:34.558454 
[Epoch 17/2000][Batch 2/48][Autoencoder loss: 420.870758][C loss: 1.632909][M loss: 0.693299][D loss: -12994.957031][G loss -43291.226562 ]time: 0:12:34.996008 
[Epoch 17/2000][Batch 3/48][Autoencoder loss: 368.158264][C loss: 1.476151][M loss: 0.693312][D loss: -12922.217773][G loss -45135.152344 ]time: 0:12:35.466603 
[Epoch 17/2000][Batch 4/48][Autoencoder loss: 1012.790100][C loss: 1.789315][M loss: 0.693343][D loss: -14205.301758][G loss -46675.062500 ]time: 0:12:35.907719 
[Epoch 17/2000][Batch 5/48][Autoencoder loss: 1839.239624][C loss: 1.677212][M loss: 0.693406][D loss: -14642.051758][G loss -47851.683594 ]time: 0:12:36.363897 
[Epoch 17/2000][Batch 6/48][Autoencoder loss: 1495.794556][C loss: 1.501619][M loss: 0.693315][D loss: -13541.411133][G loss -49307.351562 ]time: 0:12:36.810829 
[Epoch 17/2000][Batch 7/48][Autoencoder loss: 1628.898926][C loss: 1.265466][M loss: 0.693305][D loss: -15015.926758][G loss -52050.910156 ]time: 0:12:37.260354 
[Epoch 17/2000][Batch 8/48][Autoencoder loss: 854.235718][C loss: 1.875258][M loss: 0.693308][D loss: -27201.259766][G loss -42562.121094 ]time: 0:12:37.777931 
[Epoch 17/2000][Batch 9/48][Autoencoder loss: 464.242004][C loss: 1.884735][M loss: 0.693306][D loss: -27754.253906][G loss -43267.285156 ]time: 0:12:38.833386 
[Epoch 17/2000][Batch 10/48][Autoencoder loss: 1331.333862][C loss: 1.751262][M loss: 0.693304][D loss: -28851.101562][G loss -45415.699219 ]time: 0:12:40.040104 
[Epoch 17/2000][Batch 11/48][Autoencoder loss: 628.989563][C loss: 1.489601][M loss: 0.693303][D loss: -27635.341797][G loss -45534.507812 ]time: 0:12:40.975861 
[Epoch 17/2000][Batch 12/48][Autoencoder loss: 447.475128][C loss: 0.822309][M loss: 0.693308][D loss: -18102.695312][G loss -56413.609375 ]time: 0:12:41.650810 
[Epoch 17/2000][Batch 13/48][Autoencoder loss: 1020.116028][C loss: 0.847884][M loss: 0.693309][D loss: -17813.623047][G loss -56730.292969 ]time: 0:12:42.100645 
[Epoch 17/2000][Batch 14/48][Autoencoder loss: 491.660187][C loss: 0.789367][M loss: 0.693308][D loss: -18987.513672][G loss -59479.601562 ]time: 0:12:42.566016 
[Epoch 17/2000][Batch 15/48][Autoencoder loss: 420.723907][C loss: 0.738352][M loss: 0.693307][D loss: -19214.089844][G loss -61025.015625 ]time: 0:12:43.031193 
[Epoch 17/2000][Batch 16/48][Autoencoder loss: 1006.016296][C loss: 0.664755][M loss: 0.693318][D loss: -22838.546875][G loss -58209.851562 ]time: 0:12:43.487359 
[Epoch 17/2000][Batch 17/48][Autoencoder loss: 424.977203][C loss: 0.655802][M loss: 0.693300][D loss: -21321.886719][G loss -58732.492188 ]time: 0:12:44.002518 
[Epoch 17/2000][Batch 18/48][Autoencoder loss: 243.726669][C loss: 0.632731][M loss: 0.693302][D loss: -22500.605469][G loss -59228.660156 ]time: 0:12:44.457108 
[Epoch 17/2000][Batch 19/48][Autoencoder loss: 480.261444][C loss: 0.503845][M loss: 0.693304][D loss: -20133.714844][G loss -59859.773438 ]time: 0:12:44.920240 
[Epoch 17/2000][Batch 20/48][Autoencoder loss: 3970.522461][C loss: 0.971659][M loss: 0.693442][D loss: -19930.097656][G loss -63219.867188 ]time: 0:12:45.366886 
[Epoch 17/2000][Batch 21/48][Autoencoder loss: 7223.877930][C loss: 0.780331][M loss: 0.693602][D loss: -21804.369141][G loss -64211.816406 ]time: 0:12:45.832620 
[Epoch 17/2000][Batch 22/48][Autoencoder loss: 6716.493652][C loss: 0.652088][M loss: 0.701689][D loss: -23590.111328][G loss -65704.867188 ]time: 0:12:46.650602 
[Epoch 17/2000][Batch 23/48][Autoencoder loss: 6915.084961][C loss: 0.487488][M loss: 0.700444][D loss: -19288.589844][G loss -65996.796875 ]time: 0:12:47.128395 
[Epoch 17/2000][Batch 24/48][Autoencoder loss: 8555.005859][C loss: 0.828043][M loss: 0.693559][D loss: -11974.936523][G loss -70671.460938 ]time: 0:12:47.620113 
[Epoch 17/2000][Batch 25/48][Autoencoder loss: 4657.629883][C loss: 0.670982][M loss: 0.693444][D loss: -8472.689453][G loss -74270.476562 ]time: 0:12:48.072409 
[Epoch 17/2000][Batch 26/48][Autoencoder loss: 14317.297852][C loss: 0.689442][M loss: 0.693425][D loss: -10867.458984][G loss -74685.039062 ]time: 0:12:48.905709 
[Epoch 17/2000][Batch 27/48][Autoencoder loss: 8996.385742][C loss: 0.827970][M loss: 0.693389][D loss: -17282.851562][G loss -75190.757812 ]time: 0:12:49.775243 
[Epoch 17/2000][Batch 28/48][Autoencoder loss: 4377.916504][C loss: 1.900006][M loss: 0.693977][D loss: -13543.033203][G loss -86263.789062 ]time: 0:12:50.342573 
[Epoch 17/2000][Batch 29/48][Autoencoder loss: 9160.854492][C loss: 1.450339][M loss: 0.693637][D loss: -10269.286133][G loss -84612.945312 ]time: 0:12:50.787898 
[Epoch 17/2000][Batch 30/48][Autoencoder loss: 6954.549316][C loss: 1.561634][M loss: 0.693455][D loss: -5929.763672][G loss -88559.765625 ]time: 0:12:51.239125 
[Epoch 17/2000][Batch 31/48][Autoencoder loss: 2071.817139][C loss: 1.289077][M loss: 0.693529][D loss: -1869.159180][G loss -89070.031250 ]time: 0:12:51.693486 
[Epoch 17/2000][Batch 32/48][Autoencoder loss: 6320.510742][C loss: 1.365135][M loss: 0.693346][D loss: -21653.144531][G loss -70475.523438 ]time: 0:12:52.145117 
[Epoch 17/2000][Batch 33/48][Autoencoder loss: 3675.589111][C loss: 0.919918][M loss: 0.693355][D loss: -25608.492188][G loss -70874.179688 ]time: 0:12:52.594361 
[Epoch 17/2000][Batch 34/48][Autoencoder loss: 703.545288][C loss: 0.821795][M loss: 0.693330][D loss: -28028.285156][G loss -72393.820312 ]time: 0:12:53.040987 
[Epoch 17/2000][Batch 35/48][Autoencoder loss: 4528.445801][C loss: 0.678866][M loss: 0.693316][D loss: -25807.226562][G loss -72019.757812 ]time: 0:12:53.490315 
[Epoch 17/2000][Batch 36/48][Autoencoder loss: 2871.344238][C loss: 1.773350][M loss: 0.693309][D loss: -22004.019531][G loss -76736.156250 ]time: 0:12:53.939984 
[Epoch 17/2000][Batch 37/48][Autoencoder loss: 700.411743][C loss: 1.688675][M loss: 0.693312][D loss: -18709.300781][G loss -77987.820312 ]time: 0:12:54.779382 
[Epoch 17/2000][Batch 38/48][Autoencoder loss: 3250.110840][C loss: 1.576017][M loss: 0.693305][D loss: -18661.355469][G loss -81620.781250 ]time: 0:12:55.819255 
[Epoch 17/2000][Batch 39/48][Autoencoder loss: 2788.356934][C loss: 1.415585][M loss: 0.693306][D loss: -22901.744141][G loss -81971.929688 ]time: 0:12:56.814181 
[Epoch 17/2000][Batch 40/48][Autoencoder loss: 992.399597][C loss: 1.201402][M loss: 0.693387][D loss: -23665.689453][G loss -85009.000000 ]time: 0:12:57.617875 
[Epoch 17/2000][Batch 41/48][Autoencoder loss: 2682.379639][C loss: 1.146475][M loss: 0.693354][D loss: -23836.945312][G loss -86508.320312 ]time: 0:12:58.059977 
[Epoch 17/2000][Batch 42/48][Autoencoder loss: 2900.657715][C loss: 1.061315][M loss: 0.693343][D loss: -22353.863281][G loss -89918.031250 ]time: 0:12:58.511523 
[Epoch 17/2000][Batch 43/48][Autoencoder loss: 884.773560][C loss: 0.940453][M loss: 0.693321][D loss: -17588.554688][G loss -89246.929688 ]time: 0:12:58.969367 
[Epoch 17/2000][Batch 44/48][Autoencoder loss: 1575.044312][C loss: 1.517419][M loss: 0.693320][D loss: -19283.474609][G loss -87162.679688 ]time: 0:12:59.413781 
[Epoch 17/2000][Batch 45/48][Autoencoder loss: 1799.406128][C loss: 1.489656][M loss: 0.693318][D loss: -22452.683594][G loss -87033.281250 ]time: 0:13:00.182651 
[Epoch 17/2000][Batch 46/48][Autoencoder loss: 456.731506][C loss: 1.371207][M loss: 0.693311][D loss: -23949.482422][G loss -87610.046875 ]time: 0:13:01.417364 
[Epoch 17/2000][Batch 47/48][Autoencoder loss: 769.797913][C loss: 1.140618][M loss: 0.693299][D loss: -25033.511719][G loss -91620.953125 ]time: 0:13:02.022084 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 505us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 417us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 466us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 17/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.345139] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 18/2000][Batch 0/48][Autoencoder loss: 1030.835938][C loss: 1.927236][M loss: 0.693320][D loss: 6649.164062][G loss -119990.203125 ]time: 0:13:24.699491 
[Epoch 18/2000][Batch 1/48][Autoencoder loss: 650.170166][C loss: 1.861714][M loss: 0.693296][D loss: 6229.004883][G loss -121528.296875 ]time: 0:13:25.141974 
[Epoch 18/2000][Batch 2/48][Autoencoder loss: 360.170471][C loss: 1.669944][M loss: 0.693297][D loss: 11670.318359][G loss -121251.625000 ]time: 0:13:25.585890 
[Epoch 18/2000][Batch 3/48][Autoencoder loss: 719.376587][C loss: 1.436394][M loss: 0.693301][D loss: 10793.275391][G loss -123729.453125 ]time: 0:13:26.052903 
[Epoch 18/2000][Batch 4/48][Autoencoder loss: 705.685547][C loss: 1.547095][M loss: 0.693332][D loss: 10590.676758][G loss -125720.414062 ]time: 0:13:26.505772 
[Epoch 18/2000][Batch 5/48][Autoencoder loss: 1744.220093][C loss: 1.527062][M loss: 0.693376][D loss: 9164.865234][G loss -122328.992188 ]time: 0:13:26.961032 
[Epoch 18/2000][Batch 6/48][Autoencoder loss: 1466.598022][C loss: 1.386433][M loss: 0.693314][D loss: 12194.757812][G loss -123976.585938 ]time: 0:13:27.405766 
[Epoch 18/2000][Batch 7/48][Autoencoder loss: 990.457275][C loss: 1.255099][M loss: 0.693309][D loss: 13023.297852][G loss -122980.015625 ]time: 0:13:27.860685 
[Epoch 18/2000][Batch 8/48][Autoencoder loss: 818.284363][C loss: 2.258791][M loss: 0.693298][D loss: -11285.176758][G loss -93466.484375 ]time: 0:13:28.307789 
[Epoch 18/2000][Batch 9/48][Autoencoder loss: 436.987579][C loss: 2.197129][M loss: 0.693298][D loss: -16199.152344][G loss -92676.039062 ]time: 0:13:29.069181 
[Epoch 18/2000][Batch 10/48][Autoencoder loss: 420.436584][C loss: 2.012482][M loss: 0.693299][D loss: -14540.972656][G loss -93935.117188 ]time: 0:13:30.046813 
[Epoch 18/2000][Batch 11/48][Autoencoder loss: 692.202209][C loss: 1.764360][M loss: 0.693295][D loss: -13038.537109][G loss -94043.992188 ]time: 0:13:30.842098 
[Epoch 18/2000][Batch 12/48][Autoencoder loss: 371.327606][C loss: 0.701078][M loss: 0.693301][D loss: 6202.594727][G loss -112102.031250 ]time: 0:13:31.366941 
[Epoch 18/2000][Batch 13/48][Autoencoder loss: 337.050079][C loss: 0.726860][M loss: 0.693299][D loss: 7584.921875][G loss -113393.250000 ]time: 0:13:31.876546 
[Epoch 18/2000][Batch 14/48][Autoencoder loss: 527.738525][C loss: 0.703121][M loss: 0.693301][D loss: 11408.451172][G loss -111162.617188 ]time: 0:13:32.318968 
[Epoch 18/2000][Batch 15/48][Autoencoder loss: 384.029877][C loss: 0.700961][M loss: 0.693295][D loss: 13393.727539][G loss -111511.906250 ]time: 0:13:32.770685 
[Epoch 18/2000][Batch 16/48][Autoencoder loss: 461.653900][C loss: 0.619512][M loss: 0.693297][D loss: 5977.990234][G loss -105074.375000 ]time: 0:13:33.219664 
[Epoch 18/2000][Batch 17/48][Autoencoder loss: 480.080170][C loss: 0.601373][M loss: 0.693298][D loss: 6512.581055][G loss -103177.226562 ]time: 0:13:34.021271 
[Epoch 18/2000][Batch 18/48][Autoencoder loss: 317.030731][C loss: 0.489544][M loss: 0.693291][D loss: 8339.578125][G loss -104460.929688 ]time: 0:13:35.271887 
[Epoch 18/2000][Batch 19/48][Autoencoder loss: 159.384827][C loss: 0.352267][M loss: 0.693291][D loss: 9615.877930][G loss -104715.585938 ]time: 0:13:35.896457 
[Epoch 18/2000][Batch 20/48][Autoencoder loss: 3843.014648][C loss: 0.892541][M loss: 0.693343][D loss: 13614.293945][G loss -106791.203125 ]time: 0:13:36.349172 
[Epoch 18/2000][Batch 21/48][Autoencoder loss: 8528.684570][C loss: 0.632200][M loss: 0.693468][D loss: 11057.265625][G loss -106585.546875 ]time: 0:13:36.787902 
[Epoch 18/2000][Batch 22/48][Autoencoder loss: 5349.486328][C loss: 0.400605][M loss: 0.735384][D loss: 13074.439453][G loss -109138.515625 ]time: 0:13:37.244789 
[Epoch 18/2000][Batch 23/48][Autoencoder loss: 6966.123535][C loss: 0.335250][M loss: 0.699297][D loss: 13428.440430][G loss -106085.234375 ]time: 0:13:37.710316 
[Epoch 18/2000][Batch 24/48][Autoencoder loss: 11861.471680][C loss: 0.916230][M loss: 0.695826][D loss: 21590.968750][G loss -108507.750000 ]time: 0:13:38.156792 
[Epoch 18/2000][Batch 25/48][Autoencoder loss: 2575.481689][C loss: 0.864614][M loss: 0.694421][D loss: 26896.166016][G loss -108558.617188 ]time: 0:13:38.614730 
[Epoch 18/2000][Batch 26/48][Autoencoder loss: 11793.583984][C loss: 0.817379][M loss: 0.694516][D loss: 26316.562500][G loss -102809.828125 ]time: 0:13:39.061708 
[Epoch 18/2000][Batch 27/48][Autoencoder loss: 11978.810547][C loss: 0.675137][M loss: 0.693822][D loss: 24887.546875][G loss -102119.382812 ]time: 0:13:39.510450 
[Epoch 18/2000][Batch 28/48][Autoencoder loss: 4126.856934][C loss: 1.659262][M loss: 0.697105][D loss: 31580.410156][G loss -114080.304688 ]time: 0:13:39.959790 
[Epoch 18/2000][Batch 29/48][Autoencoder loss: 5900.292480][C loss: 1.481550][M loss: 0.696019][D loss: 33581.585938][G loss -110592.281250 ]time: 0:13:40.410121 
[Epoch 18/2000][Batch 30/48][Autoencoder loss: 10636.415039][C loss: 1.056679][M loss: 0.693978][D loss: 34926.171875][G loss -109179.804688 ]time: 0:13:40.853454 
[Epoch 18/2000][Batch 31/48][Autoencoder loss: 1945.234131][C loss: 0.689628][M loss: 0.694722][D loss: 37639.253906][G loss -105692.234375 ]time: 0:13:41.311518 
[Epoch 18/2000][Batch 32/48][Autoencoder loss: 3285.875000][C loss: 2.415748][M loss: 0.693700][D loss: 15511.702148][G loss -79371.007812 ]time: 0:13:41.778750 
[Epoch 18/2000][Batch 33/48][Autoencoder loss: 5978.359375][C loss: 2.134655][M loss: 0.693836][D loss: 11594.623047][G loss -76715.367188 ]time: 0:13:42.223890 
[Epoch 18/2000][Batch 34/48][Autoencoder loss: 2386.644775][C loss: 1.725560][M loss: 0.693925][D loss: 12214.449219][G loss -74676.726562 ]time: 0:13:42.718107 
[Epoch 18/2000][Batch 35/48][Autoencoder loss: 1073.515015][C loss: 1.291026][M loss: 0.693743][D loss: 10016.533203][G loss -73032.867188 ]time: 0:13:43.159411 
[Epoch 18/2000][Batch 36/48][Autoencoder loss: 4460.185059][C loss: 0.981408][M loss: 0.693556][D loss: 11432.757812][G loss -71835.570312 ]time: 0:13:43.613496 
[Epoch 18/2000][Batch 37/48][Autoencoder loss: 2192.355713][C loss: 0.921172][M loss: 0.693615][D loss: 14661.687500][G loss -72159.679688 ]time: 0:13:44.999469 
[Epoch 18/2000][Batch 38/48][Autoencoder loss: 685.296326][C loss: 0.920765][M loss: 0.693480][D loss: 13053.162109][G loss -68707.742188 ]time: 0:13:46.318336 
[Epoch 18/2000][Batch 39/48][Autoencoder loss: 3140.130127][C loss: 0.866619][M loss: 0.693658][D loss: 15824.291016][G loss -68427.773438 ]time: 0:13:47.172129 
[Epoch 18/2000][Batch 40/48][Autoencoder loss: 3010.193115][C loss: 1.226736][M loss: 0.694790][D loss: 15561.772461][G loss -66494.632812 ]time: 0:13:47.621799 
[Epoch 18/2000][Batch 41/48][Autoencoder loss: 1061.877686][C loss: 1.210418][M loss: 0.694190][D loss: 13680.187500][G loss -63743.707031 ]time: 0:13:48.084668 
[Epoch 18/2000][Batch 42/48][Autoencoder loss: 2393.066406][C loss: 1.126798][M loss: 0.693732][D loss: 12929.449219][G loss -61564.996094 ]time: 0:13:48.536358 
[Epoch 18/2000][Batch 43/48][Autoencoder loss: 2667.885986][C loss: 0.974594][M loss: 0.693571][D loss: 16336.287109][G loss -59662.496094 ]time: 0:13:48.995416 
[Epoch 18/2000][Batch 44/48][Autoencoder loss: 697.299500][C loss: 0.898990][M loss: 0.693382][D loss: 12084.873047][G loss -54508.332031 ]time: 0:13:49.442702 
[Epoch 18/2000][Batch 45/48][Autoencoder loss: 966.939087][C loss: 0.821263][M loss: 0.693371][D loss: 13469.228516][G loss -52147.933594 ]time: 0:13:49.903307 
[Epoch 18/2000][Batch 46/48][Autoencoder loss: 1858.236938][C loss: 0.770092][M loss: 0.693341][D loss: 10921.250000][G loss -50368.808594 ]time: 0:13:50.362945 
[Epoch 18/2000][Batch 47/48][Autoencoder loss: 748.654053][C loss: 0.685257][M loss: 0.693341][D loss: 8459.126953][G loss -49088.191406 ]time: 0:13:50.826724 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 464us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 443us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 454us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 18/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 19/2000][Batch 0/48][Autoencoder loss: 676.544800][C loss: 1.615923][M loss: 0.693507][D loss: 26272.126953][G loss -62582.765625 ]time: 0:14:10.691892 
[Epoch 19/2000][Batch 1/48][Autoencoder loss: 1652.032837][C loss: 1.586149][M loss: 0.693372][D loss: 24825.050781][G loss -59467.550781 ]time: 0:14:11.139036 
[Epoch 19/2000][Batch 2/48][Autoencoder loss: 1501.254395][C loss: 1.401147][M loss: 0.693372][D loss: 23938.066406][G loss -55869.449219 ]time: 0:14:11.594272 
[Epoch 19/2000][Batch 3/48][Autoencoder loss: 354.080627][C loss: 1.184164][M loss: 0.693340][D loss: 24546.099609][G loss -51902.390625 ]time: 0:14:12.052615 
[Epoch 19/2000][Batch 4/48][Autoencoder loss: 924.407410][C loss: 1.225611][M loss: 0.693379][D loss: 22027.697266][G loss -47361.304688 ]time: 0:14:12.498351 
[Epoch 19/2000][Batch 5/48][Autoencoder loss: 2005.818481][C loss: 1.221470][M loss: 0.693439][D loss: 20123.777344][G loss -44364.359375 ]time: 0:14:13.124958 
[Epoch 19/2000][Batch 6/48][Autoencoder loss: 1600.532593][C loss: 1.144038][M loss: 0.693336][D loss: 18192.148438][G loss -40136.929688 ]time: 0:14:13.750968 
[Epoch 19/2000][Batch 7/48][Autoencoder loss: 975.088989][C loss: 1.022022][M loss: 0.693325][D loss: 16609.283203][G loss -36223.144531 ]time: 0:14:14.203297 
[Epoch 19/2000][Batch 8/48][Autoencoder loss: 298.758636][C loss: 1.849808][M loss: 0.693320][D loss: 6085.979492][G loss -23933.892578 ]time: 0:14:14.648123 
[Epoch 19/2000][Batch 9/48][Autoencoder loss: 508.491852][C loss: 1.828122][M loss: 0.693313][D loss: 5472.826660][G loss -20828.785156 ]time: 0:14:15.092664 
[Epoch 19/2000][Batch 10/48][Autoencoder loss: 442.739838][C loss: 1.701421][M loss: 0.693309][D loss: 5311.187012][G loss -17908.507812 ]time: 0:14:15.537810 
[Epoch 19/2000][Batch 11/48][Autoencoder loss: 315.935486][C loss: 1.508747][M loss: 0.693298][D loss: 4951.050293][G loss -15255.720703 ]time: 0:14:15.981199 
[Epoch 19/2000][Batch 12/48][Autoencoder loss: 420.042572][C loss: 1.137653][M loss: 0.693303][D loss: 6665.927734][G loss -14848.434570 ]time: 0:14:16.833277 
[Epoch 19/2000][Batch 13/48][Autoencoder loss: 404.630249][C loss: 0.936390][M loss: 0.693298][D loss: 5518.269531][G loss -11367.545898 ]time: 0:14:17.887209 
[Epoch 19/2000][Batch 14/48][Autoencoder loss: 265.413208][C loss: 0.792441][M loss: 0.693304][D loss: 4471.243652][G loss -7962.812988 ]time: 0:14:18.565188 
[Epoch 19/2000][Batch 15/48][Autoencoder loss: 337.337555][C loss: 0.593395][M loss: 0.693300][D loss: 3025.173828][G loss -4669.714355 ]time: 0:14:19.006437 
[Epoch 19/2000][Batch 16/48][Autoencoder loss: 553.891541][C loss: 0.706394][M loss: 0.693325][D loss: 1618.409668][G loss -1115.201660 ]time: 0:14:19.445340 
[Epoch 19/2000][Batch 17/48][Autoencoder loss: 367.475128][C loss: 0.695402][M loss: 0.693303][D loss: 617.869568][G loss 1924.487061 ]time: 0:14:19.896757 
[Epoch 19/2000][Batch 18/48][Autoencoder loss: 183.403671][C loss: 0.533833][M loss: 0.693292][D loss: -418.372314][G loss 5114.717773 ]time: 0:14:20.348715 
[Epoch 19/2000][Batch 19/48][Autoencoder loss: 211.816727][C loss: 0.286721][M loss: 0.693290][D loss: -1395.947266][G loss 8004.495605 ]time: 0:14:20.798063 
[Epoch 19/2000][Batch 20/48][Autoencoder loss: 3616.439941][C loss: 0.997696][M loss: 0.693405][D loss: -2629.415283][G loss 11500.651367 ]time: 0:14:21.236536 
[Epoch 19/2000][Batch 21/48][Autoencoder loss: 7898.370605][C loss: 0.716180][M loss: 0.693319][D loss: -3261.243896][G loss 14722.740234 ]time: 0:14:21.684480 
[Epoch 19/2000][Batch 22/48][Autoencoder loss: 5421.079102][C loss: 0.588556][M loss: 0.698215][D loss: -4472.292969][G loss 18163.169922 ]time: 0:14:22.150710 
[Epoch 19/2000][Batch 23/48][Autoencoder loss: 6725.846680][C loss: 0.398359][M loss: 0.701222][D loss: -4901.188477][G loss 21228.966797 ]time: 0:14:22.630267 
[Epoch 19/2000][Batch 24/48][Autoencoder loss: 13543.446289][C loss: 1.103263][M loss: 0.694081][D loss: -7827.675781][G loss 24903.632812 ]time: 0:14:23.083619 
[Epoch 19/2000][Batch 25/48][Autoencoder loss: 2235.244629][C loss: 1.141255][M loss: 0.693567][D loss: -10345.579102][G loss 28531.525391 ]time: 0:14:23.587446 
[Epoch 19/2000][Batch 26/48][Autoencoder loss: 11163.441406][C loss: 1.171754][M loss: 0.693531][D loss: -12282.657227][G loss 31655.617188 ]time: 0:14:24.040369 
[Epoch 19/2000][Batch 27/48][Autoencoder loss: 13767.985352][C loss: 1.116004][M loss: 0.693736][D loss: -12263.046875][G loss 36131.964844 ]time: 0:14:24.498901 
[Epoch 19/2000][Batch 28/48][Autoencoder loss: 5202.437500][C loss: 2.587769][M loss: 0.694105][D loss: -16455.378906][G loss 45494.101562 ]time: 0:14:24.952854 
[Epoch 19/2000][Batch 29/48][Autoencoder loss: 4058.817871][C loss: 2.456970][M loss: 0.694278][D loss: -17182.875000][G loss 48773.054688 ]time: 0:14:25.406187 
[Epoch 19/2000][Batch 30/48][Autoencoder loss: 11777.638672][C loss: 2.244224][M loss: 0.693456][D loss: -19876.312500][G loss 51260.601562 ]time: 0:14:25.931024 
[Epoch 19/2000][Batch 31/48][Autoencoder loss: 3938.761963][C loss: 1.813295][M loss: 0.694293][D loss: -24007.988281][G loss 57666.953125 ]time: 0:14:27.343241 
[Epoch 19/2000][Batch 32/48][Autoencoder loss: 1641.545532][C loss: 1.698940][M loss: 0.693413][D loss: -12524.704102][G loss 46847.910156 ]time: 0:14:27.958483 
[Epoch 19/2000][Batch 33/48][Autoencoder loss: 5781.134277][C loss: 1.464332][M loss: 0.693349][D loss: -14492.631836][G loss 51613.109375 ]time: 0:14:28.401506 
[Epoch 19/2000][Batch 34/48][Autoencoder loss: 4866.037598][C loss: 1.172818][M loss: 0.693404][D loss: -14295.836914][G loss 54004.167969 ]time: 0:14:28.845833 
[Epoch 19/2000][Batch 35/48][Autoencoder loss: 505.647400][C loss: 0.977989][M loss: 0.693401][D loss: -12671.136719][G loss 56995.226562 ]time: 0:14:29.294473 
[Epoch 19/2000][Batch 36/48][Autoencoder loss: 2785.482910][C loss: 1.846329][M loss: 0.693347][D loss: -12721.173828][G loss 62481.617188 ]time: 0:14:29.744997 
[Epoch 19/2000][Batch 37/48][Autoencoder loss: 4153.524414][C loss: 1.528029][M loss: 0.693348][D loss: -17145.816406][G loss 64799.796875 ]time: 0:14:30.188752 
[Epoch 19/2000][Batch 38/48][Autoencoder loss: 955.059509][C loss: 1.108423][M loss: 0.693344][D loss: -19551.167969][G loss 68893.890625 ]time: 0:14:30.617508 
[Epoch 19/2000][Batch 39/48][Autoencoder loss: 1452.194580][C loss: 0.984173][M loss: 0.693351][D loss: -17978.099609][G loss 69733.859375 ]time: 0:14:31.289097 
[Epoch 19/2000][Batch 40/48][Autoencoder loss: 3783.047119][C loss: 0.823821][M loss: 0.693765][D loss: -20944.587891][G loss 74027.453125 ]time: 0:14:32.334591 
[Epoch 19/2000][Batch 41/48][Autoencoder loss: 2410.069336][C loss: 0.868028][M loss: 0.693649][D loss: -19972.164062][G loss 78176.843750 ]time: 0:14:32.831574 
[Epoch 19/2000][Batch 42/48][Autoencoder loss: 1079.628174][C loss: 0.904927][M loss: 0.693462][D loss: -20094.312500][G loss 79668.601562 ]time: 0:14:33.286179 
[Epoch 19/2000][Batch 43/48][Autoencoder loss: 2502.471924][C loss: 0.848762][M loss: 0.693370][D loss: -19898.029297][G loss 81481.867188 ]time: 0:14:33.730396 
[Epoch 19/2000][Batch 44/48][Autoencoder loss: 2280.039062][C loss: 1.534531][M loss: 0.693421][D loss: -17548.730469][G loss 79929.664062 ]time: 0:14:34.183757 
[Epoch 19/2000][Batch 45/48][Autoencoder loss: 470.565491][C loss: 1.309136][M loss: 0.693490][D loss: -14612.989258][G loss 81761.835938 ]time: 0:14:34.648661 
[Epoch 19/2000][Batch 46/48][Autoencoder loss: 1074.365356][C loss: 1.095445][M loss: 0.693414][D loss: -16859.839844][G loss 86010.750000 ]time: 0:14:35.132392 
[Epoch 19/2000][Batch 47/48][Autoencoder loss: 1807.633301][C loss: 0.834226][M loss: 0.693409][D loss: -16328.931641][G loss 85647.773438 ]time: 0:14:35.574748 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 470us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 510us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 416us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 19/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 20/2000][Batch 0/48][Autoencoder loss: 1870.486206][C loss: 2.223102][M loss: 0.693593][D loss: -41671.433594][G loss 118808.664062 ]time: 0:14:50.338254 
[Epoch 20/2000][Batch 1/48][Autoencoder loss: 512.416016][C loss: 2.410290][M loss: 0.693433][D loss: -45025.109375][G loss 117866.117188 ]time: 0:14:50.791844 
[Epoch 20/2000][Batch 2/48][Autoencoder loss: 1983.438232][C loss: 2.244798][M loss: 0.693320][D loss: -44912.835938][G loss 124025.664062 ]time: 0:14:51.241717 
[Epoch 20/2000][Batch 3/48][Autoencoder loss: 1532.328857][C loss: 1.837998][M loss: 0.693315][D loss: -48110.222656][G loss 130350.265625 ]time: 0:14:51.688717 
[Epoch 20/2000][Batch 4/48][Autoencoder loss: 620.766663][C loss: 1.455499][M loss: 0.693328][D loss: -45909.843750][G loss 132313.468750 ]time: 0:14:52.136500 
[Epoch 20/2000][Batch 5/48][Autoencoder loss: 1741.480469][C loss: 1.213523][M loss: 0.693355][D loss: -49667.699219][G loss 139823.687500 ]time: 0:14:52.594670 
[Epoch 20/2000][Batch 6/48][Autoencoder loss: 2749.437256][C loss: 1.026628][M loss: 0.693336][D loss: -50466.167969][G loss 139721.468750 ]time: 0:14:53.055896 
[Epoch 20/2000][Batch 7/48][Autoencoder loss: 1858.232056][C loss: 0.885021][M loss: 0.693310][D loss: -45356.875000][G loss 144317.703125 ]time: 0:14:53.502797 
[Epoch 20/2000][Batch 8/48][Autoencoder loss: 392.845154][C loss: 0.883357][M loss: 0.693309][D loss: -10903.200195][G loss 112416.031250 ]time: 0:14:53.965051 
[Epoch 20/2000][Batch 9/48][Autoencoder loss: 905.652649][C loss: 0.933497][M loss: 0.693313][D loss: -9506.019531][G loss 113772.570312 ]time: 0:14:54.411793 
[Epoch 20/2000][Batch 10/48][Autoencoder loss: 1699.437744][C loss: 0.853612][M loss: 0.693313][D loss: -9083.767578][G loss 117421.171875 ]time: 0:14:54.924716 
[Epoch 20/2000][Batch 11/48][Autoencoder loss: 601.332397][C loss: 0.720940][M loss: 0.693317][D loss: -9329.779297][G loss 118762.179688 ]time: 0:14:55.446998 
[Epoch 20/2000][Batch 12/48][Autoencoder loss: 384.992188][C loss: 2.097314][M loss: 0.693320][D loss: -31849.660156][G loss 144137.734375 ]time: 0:14:55.895368 
[Epoch 20/2000][Batch 13/48][Autoencoder loss: 1216.078125][C loss: 1.731760][M loss: 0.693311][D loss: -34151.714844][G loss 144543.640625 ]time: 0:14:56.344189 
[Epoch 20/2000][Batch 14/48][Autoencoder loss: 867.950806][C loss: 1.342138][M loss: 0.693317][D loss: -26985.156250][G loss 146221.796875 ]time: 0:14:56.794829 
[Epoch 20/2000][Batch 15/48][Autoencoder loss: 241.639999][C loss: 1.095924][M loss: 0.693300][D loss: -25784.615234][G loss 149786.671875 ]time: 0:14:57.599531 
[Epoch 20/2000][Batch 16/48][Autoencoder loss: 857.508362][C loss: 0.857198][M loss: 0.693447][D loss: -14869.515625][G loss 141701.000000 ]time: 0:14:58.482328 
[Epoch 20/2000][Batch 17/48][Autoencoder loss: 871.970459][C loss: 0.705419][M loss: 0.693354][D loss: -16031.575195][G loss 143838.859375 ]time: 0:14:58.969880 
[Epoch 20/2000][Batch 18/48][Autoencoder loss: 291.507690][C loss: 0.516607][M loss: 0.693290][D loss: -15751.296875][G loss 141863.062500 ]time: 0:15:00.013283 
[Epoch 20/2000][Batch 19/48][Autoencoder loss: 259.510620][C loss: 0.448023][M loss: 0.693296][D loss: -16318.706055][G loss 145302.000000 ]time: 0:15:01.111234 
[Epoch 20/2000][Batch 20/48][Autoencoder loss: 4998.828613][C loss: 1.257992][M loss: 0.693393][D loss: -19125.902344][G loss 151951.062500 ]time: 0:15:01.599448 
[Epoch 20/2000][Batch 21/48][Autoencoder loss: 9759.196289][C loss: 0.894158][M loss: 0.693307][D loss: -13120.010742][G loss 148955.062500 ]time: 0:15:02.054937 
[Epoch 20/2000][Batch 22/48][Autoencoder loss: 5358.541504][C loss: 0.836556][M loss: 0.702468][D loss: -4835.811523][G loss 150783.937500 ]time: 0:15:02.502654 
[Epoch 20/2000][Batch 23/48][Autoencoder loss: 8275.771484][C loss: 0.727246][M loss: 0.707187][D loss: -3851.057617][G loss 148110.906250 ]time: 0:15:02.953338 
[Epoch 20/2000][Batch 24/48][Autoencoder loss: 16580.933594][C loss: 1.098706][M loss: 0.694508][D loss: -14881.356445][G loss 153695.390625 ]time: 0:15:03.400252 
[Epoch 20/2000][Batch 25/48][Autoencoder loss: 1952.044922][C loss: 0.922083][M loss: 0.693618][D loss: -21919.320312][G loss 151857.718750 ]time: 0:15:03.852884 
[Epoch 20/2000][Batch 26/48][Autoencoder loss: 9708.344727][C loss: 0.729145][M loss: 0.693457][D loss: -16821.603516][G loss 149100.359375 ]time: 0:15:04.305300 
[Epoch 20/2000][Batch 27/48][Autoencoder loss: 15647.518555][C loss: 0.455136][M loss: 0.693772][D loss: -11460.586914][G loss 151664.968750 ]time: 0:15:04.751486 
[Epoch 20/2000][Batch 28/48][Autoencoder loss: 7660.279297][C loss: 2.603995][M loss: 0.693856][D loss: -23309.878906][G loss 165627.843750 ]time: 0:15:05.240151 
[Epoch 20/2000][Batch 29/48][Autoencoder loss: 2389.446533][C loss: 2.783328][M loss: 0.694067][D loss: -15538.374023][G loss 164870.437500 ]time: 0:15:05.685131 
[Epoch 20/2000][Batch 30/48][Autoencoder loss: 10829.127930][C loss: 2.678147][M loss: 0.693469][D loss: -10210.187500][G loss 164577.703125 ]time: 0:15:06.144306 
[Epoch 20/2000][Batch 31/48][Autoencoder loss: 7538.451172][C loss: 2.283735][M loss: 0.693573][D loss: -16228.377930][G loss 165184.828125 ]time: 0:15:06.585440 
[Epoch 20/2000][Batch 32/48][Autoencoder loss: 1088.008545][C loss: 2.047818][M loss: 0.693426][D loss: 15199.548828][G loss 124423.734375 ]time: 0:15:07.079889 
[Epoch 20/2000][Batch 33/48][Autoencoder loss: 3754.887451][C loss: 1.922221][M loss: 0.693355][D loss: 19012.898438][G loss 121728.671875 ]time: 0:15:07.834557 
[Epoch 20/2000][Batch 34/48][Autoencoder loss: 6791.591309][C loss: 1.733692][M loss: 0.693397][D loss: 20727.283203][G loss 119642.484375 ]time: 0:15:08.768386 
[Epoch 20/2000][Batch 35/48][Autoencoder loss: 2398.854004][C loss: 1.511970][M loss: 0.693418][D loss: 30296.988281][G loss 118625.546875 ]time: 0:15:09.390798 
[Epoch 20/2000][Batch 36/48][Autoencoder loss: 484.477997][C loss: 1.434904][M loss: 0.693345][D loss: 35712.773438][G loss 119243.984375 ]time: 0:15:09.839965 
[Epoch 20/2000][Batch 37/48][Autoencoder loss: 3896.901367][C loss: 1.231384][M loss: 0.693336][D loss: 33608.371094][G loss 111266.328125 ]time: 0:15:10.286455 
[Epoch 20/2000][Batch 38/48][Autoencoder loss: 3554.286865][C loss: 1.050206][M loss: 0.693378][D loss: 31623.162109][G loss 109929.679688 ]time: 0:15:10.728645 
[Epoch 20/2000][Batch 39/48][Autoencoder loss: 587.828552][C loss: 0.967701][M loss: 0.693354][D loss: 29389.685547][G loss 105278.828125 ]time: 0:15:11.167996 
[Epoch 20/2000][Batch 40/48][Autoencoder loss: 2154.345459][C loss: 0.805721][M loss: 0.693504][D loss: 28382.365234][G loss 101250.085938 ]time: 0:15:11.604703 
[Epoch 20/2000][Batch 41/48][Autoencoder loss: 3816.690674][C loss: 0.766873][M loss: 0.693389][D loss: 30321.623047][G loss 99423.679688 ]time: 0:15:12.053652 
[Epoch 20/2000][Batch 42/48][Autoencoder loss: 2115.858154][C loss: 0.732641][M loss: 0.693380][D loss: 34286.835938][G loss 94437.632812 ]time: 0:15:12.526282 
[Epoch 20/2000][Batch 43/48][Autoencoder loss: 803.004150][C loss: 0.645227][M loss: 0.693360][D loss: 33512.953125][G loss 90749.320312 ]time: 0:15:13.015565 
[Epoch 20/2000][Batch 44/48][Autoencoder loss: 2284.254395][C loss: 0.995707][M loss: 0.693339][D loss: 38435.292969][G loss 85572.492188 ]time: 0:15:13.821357 
[Epoch 20/2000][Batch 45/48][Autoencoder loss: 2099.617920][C loss: 0.834855][M loss: 0.693370][D loss: 37732.199219][G loss 80120.570312 ]time: 0:15:15.016085 
[Epoch 20/2000][Batch 46/48][Autoencoder loss: 341.219543][C loss: 0.747449][M loss: 0.693361][D loss: 34964.433594][G loss 76767.976562 ]time: 0:15:16.329171 
[Epoch 20/2000][Batch 47/48][Autoencoder loss: 813.291565][C loss: 0.577897][M loss: 0.693334][D loss: 33853.285156][G loss 71072.742188 ]time: 0:15:16.940251 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 509us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 461us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 407us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 20/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 21/2000][Batch 0/48][Autoencoder loss: 3146.362061][C loss: 2.238105][M loss: 0.693566][D loss: 12891.843750][G loss 87628.093750 ]time: 0:15:37.130934 
[Epoch 21/2000][Batch 1/48][Autoencoder loss: 885.975525][C loss: 2.077376][M loss: 0.693391][D loss: 16209.289062][G loss 87272.234375 ]time: 0:15:37.579635 
[Epoch 21/2000][Batch 2/48][Autoencoder loss: 262.493134][C loss: 1.779728][M loss: 0.693339][D loss: 18574.960938][G loss 81303.265625 ]time: 0:15:38.081187 
[Epoch 21/2000][Batch 3/48][Autoencoder loss: 1527.695068][C loss: 1.532639][M loss: 0.693343][D loss: 16305.054688][G loss 77864.210938 ]time: 0:15:38.523376 
[Epoch 21/2000][Batch 4/48][Autoencoder loss: 2363.046387][C loss: 1.218791][M loss: 0.693318][D loss: 20293.187500][G loss 75223.257812 ]time: 0:15:38.972967 
[Epoch 21/2000][Batch 5/48][Autoencoder loss: 1693.598999][C loss: 1.127464][M loss: 0.693495][D loss: 16895.130859][G loss 72763.179688 ]time: 0:15:39.422697 
[Epoch 21/2000][Batch 6/48][Autoencoder loss: 1974.557495][C loss: 1.010168][M loss: 0.693385][D loss: 15195.822266][G loss 68470.171875 ]time: 0:15:39.885565 
[Epoch 21/2000][Batch 7/48][Autoencoder loss: 3147.462402][C loss: 0.919624][M loss: 0.693366][D loss: 16462.070312][G loss 66190.851562 ]time: 0:15:41.090646 
[Epoch 21/2000][Batch 8/48][Autoencoder loss: 2092.551025][C loss: 2.178355][M loss: 0.693311][D loss: 31630.375000][G loss 48545.957031 ]time: 0:15:42.304290 
[Epoch 21/2000][Batch 9/48][Autoencoder loss: 247.133850][C loss: 2.129059][M loss: 0.693319][D loss: 33559.070312][G loss 45749.101562 ]time: 0:15:42.756193 
[Epoch 21/2000][Batch 10/48][Autoencoder loss: 1626.881470][C loss: 2.032691][M loss: 0.693318][D loss: 32211.929688][G loss 43650.925781 ]time: 0:15:43.201532 
[Epoch 21/2000][Batch 11/48][Autoencoder loss: 2368.765381][C loss: 1.839150][M loss: 0.693315][D loss: 29902.855469][G loss 40952.207031 ]time: 0:15:43.643860 
[Epoch 21/2000][Batch 12/48][Autoencoder loss: 594.237000][C loss: 1.086492][M loss: 0.693316][D loss: 21228.759766][G loss 47711.589844 ]time: 0:15:44.087234 
[Epoch 21/2000][Batch 13/48][Autoencoder loss: 490.611450][C loss: 1.094493][M loss: 0.693326][D loss: 19922.988281][G loss 44949.042969 ]time: 0:15:44.541763 
[Epoch 21/2000][Batch 14/48][Autoencoder loss: 1597.016357][C loss: 1.083753][M loss: 0.693319][D loss: 19285.240234][G loss 42150.019531 ]time: 0:15:44.985422 
[Epoch 21/2000][Batch 15/48][Autoencoder loss: 1208.716553][C loss: 1.065397][M loss: 0.693296][D loss: 19462.820312][G loss 39723.613281 ]time: 0:15:45.444021 
[Epoch 21/2000][Batch 16/48][Autoencoder loss: 375.601624][C loss: 0.828888][M loss: 0.693373][D loss: 22904.554688][G loss 36198.339844 ]time: 0:15:45.900682 
[Epoch 21/2000][Batch 17/48][Autoencoder loss: 749.747620][C loss: 0.749269][M loss: 0.693311][D loss: 21487.996094][G loss 34804.355469 ]time: 0:15:46.349236 
[Epoch 21/2000][Batch 18/48][Autoencoder loss: 1279.307129][C loss: 0.608045][M loss: 0.693296][D loss: 21379.955078][G loss 32975.398438 ]time: 0:15:46.794352 
[Epoch 21/2000][Batch 19/48][Autoencoder loss: 499.854187][C loss: 0.469478][M loss: 0.693297][D loss: 20156.519531][G loss 30933.263672 ]time: 0:15:47.233121 
[Epoch 21/2000][Batch 20/48][Autoencoder loss: 3473.183350][C loss: 0.636483][M loss: 0.693449][D loss: 18150.464844][G loss 30491.486328 ]time: 0:15:48.257735 
[Epoch 21/2000][Batch 21/48][Autoencoder loss: 10372.053711][C loss: 0.420621][M loss: 0.693373][D loss: 18357.083984][G loss 28329.675781 ]time: 0:15:49.624707 
[Epoch 21/2000][Batch 22/48][Autoencoder loss: 5965.955078][C loss: 0.324740][M loss: 0.707213][D loss: 18751.722656][G loss 27227.541016 ]time: 0:15:50.148444 
[Epoch 21/2000][Batch 23/48][Autoencoder loss: 4887.793945][C loss: 0.253153][M loss: 0.700344][D loss: 19197.951172][G loss 25721.443359 ]time: 0:15:50.597441 
[Epoch 21/2000][Batch 24/48][Autoencoder loss: 14645.389648][C loss: 0.973153][M loss: 0.694078][D loss: 16942.033203][G loss 25378.738281 ]time: 0:15:51.097115 
[Epoch 21/2000][Batch 25/48][Autoencoder loss: 3907.441650][C loss: 0.809896][M loss: 0.693551][D loss: 13344.916016][G loss 24078.152344 ]time: 0:15:52.116109 
[Epoch 21/2000][Batch 26/48][Autoencoder loss: 3919.539551][C loss: 0.799387][M loss: 0.693552][D loss: 12083.516602][G loss 22951.849609 ]time: 0:15:52.761224 
[Epoch 21/2000][Batch 27/48][Autoencoder loss: 10507.560547][C loss: 0.713728][M loss: 0.693698][D loss: 11394.190430][G loss 21327.708984 ]time: 0:15:53.220695 
[Epoch 21/2000][Batch 28/48][Autoencoder loss: 9239.081055][C loss: 1.656587][M loss: 0.693647][D loss: 10491.789062][G loss 22237.410156 ]time: 0:15:53.671988 
[Epoch 21/2000][Batch 29/48][Autoencoder loss: 2926.737061][C loss: 1.585544][M loss: 0.693696][D loss: 10851.457031][G loss 20844.216797 ]time: 0:15:54.127849 
[Epoch 21/2000][Batch 30/48][Autoencoder loss: 4456.559082][C loss: 1.540003][M loss: 0.693555][D loss: 11507.925781][G loss 19621.826172 ]time: 0:15:54.814654 
[Epoch 21/2000][Batch 31/48][Autoencoder loss: 6942.770020][C loss: 1.286470][M loss: 0.693495][D loss: 10365.128906][G loss 18742.638672 ]time: 0:15:55.268751 
[Epoch 21/2000][Batch 32/48][Autoencoder loss: 3430.806885][C loss: 1.278136][M loss: 0.693337][D loss: 13162.666016][G loss 13942.216797 ]time: 0:15:55.758686 
[Epoch 21/2000][Batch 33/48][Autoencoder loss: 781.002747][C loss: 1.187657][M loss: 0.693316][D loss: 11518.675781][G loss 13543.895508 ]time: 0:15:56.237028 
[Epoch 21/2000][Batch 34/48][Autoencoder loss: 3419.588623][C loss: 1.125133][M loss: 0.693400][D loss: 10812.811523][G loss 12497.549805 ]time: 0:15:56.682305 
[Epoch 21/2000][Batch 35/48][Autoencoder loss: 3710.788574][C loss: 1.051144][M loss: 0.693347][D loss: 10696.667969][G loss 11376.174805 ]time: 0:15:57.127024 
[Epoch 21/2000][Batch 36/48][Autoencoder loss: 1070.325562][C loss: 1.198380][M loss: 0.693292][D loss: 10616.931641][G loss 10648.604492 ]time: 0:15:57.574798 
[Epoch 21/2000][Batch 37/48][Autoencoder loss: 646.896729][C loss: 1.013223][M loss: 0.693313][D loss: 10292.442383][G loss 10147.915039 ]time: 0:15:58.025718 
[Epoch 21/2000][Batch 38/48][Autoencoder loss: 2782.470947][C loss: 0.849192][M loss: 0.693327][D loss: 9628.376953][G loss 9280.426758 ]time: 0:15:58.527034 
[Epoch 21/2000][Batch 39/48][Autoencoder loss: 2178.591064][C loss: 0.745326][M loss: 0.693300][D loss: 8686.217773][G loss 8441.308594 ]time: 0:15:58.976483 
[Epoch 21/2000][Batch 40/48][Autoencoder loss: 739.085022][C loss: 0.674569][M loss: 0.693368][D loss: 7287.878418][G loss 7683.573242 ]time: 0:15:59.422366 
[Epoch 21/2000][Batch 41/48][Autoencoder loss: 1696.567139][C loss: 0.667668][M loss: 0.693337][D loss: 6682.343750][G loss 6883.685547 ]time: 0:15:59.879569 
[Epoch 21/2000][Batch 42/48][Autoencoder loss: 2707.667725][C loss: 0.615323][M loss: 0.693324][D loss: 6487.917480][G loss 6156.358398 ]time: 0:16:00.321679 
[Epoch 21/2000][Batch 43/48][Autoencoder loss: 1346.422241][C loss: 0.550418][M loss: 0.693292][D loss: 5859.925781][G loss 5277.763672 ]time: 0:16:01.043472 
[Epoch 21/2000][Batch 44/48][Autoencoder loss: 476.170563][C loss: 1.115113][M loss: 0.693292][D loss: 5980.994629][G loss 4310.735352 ]time: 0:16:02.152465 
[Epoch 21/2000][Batch 45/48][Autoencoder loss: 1301.270264][C loss: 1.055986][M loss: 0.693296][D loss: 5162.383789][G loss 3604.628662 ]time: 0:16:03.268135 
[Epoch 21/2000][Batch 46/48][Autoencoder loss: 1215.396118][C loss: 0.897978][M loss: 0.693291][D loss: 4561.995117][G loss 2961.058838 ]time: 0:16:03.919259 
[Epoch 21/2000][Batch 47/48][Autoencoder loss: 289.448944][C loss: 0.692205][M loss: 0.693284][D loss: 3789.508301][G loss 2162.530273 ]time: 0:16:04.367880 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 435us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 471us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 431us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 21/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 22/2000][Batch 0/48][Autoencoder loss: 1106.115845][C loss: 1.279720][M loss: 0.693337][D loss: 2616.603516][G loss 1815.939575 ]time: 0:16:22.198924 
[Epoch 22/2000][Batch 1/48][Autoencoder loss: 984.048706][C loss: 1.267659][M loss: 0.693325][D loss: 2310.227051][G loss 1055.432739 ]time: 0:16:23.271620 
[Epoch 22/2000][Batch 2/48][Autoencoder loss: 400.719635][C loss: 1.173225][M loss: 0.693300][D loss: 2058.830322][G loss 283.389008 ]time: 0:16:23.737497 
[Epoch 22/2000][Batch 3/48][Autoencoder loss: 150.030045][C loss: 1.063357][M loss: 0.693285][D loss: 1744.599609][G loss -453.596222 ]time: 0:16:24.341018 
[Epoch 22/2000][Batch 4/48][Autoencoder loss: 1242.478394][C loss: 1.247236][M loss: 0.693295][D loss: 1350.936646][G loss -1136.051514 ]time: 0:16:24.807177 
[Epoch 22/2000][Batch 5/48][Autoencoder loss: 2156.273682][C loss: 1.273014][M loss: 0.693319][D loss: 909.376709][G loss -1784.867065 ]time: 0:16:25.258831 
[Epoch 22/2000][Batch 6/48][Autoencoder loss: 1247.866699][C loss: 1.089051][M loss: 0.693302][D loss: 574.229614][G loss -2471.836914 ]time: 0:16:25.727382 
[Epoch 22/2000][Batch 7/48][Autoencoder loss: 1349.774170][C loss: 0.931379][M loss: 0.693305][D loss: 203.352234][G loss -3084.972412 ]time: 0:16:26.195848 
[Epoch 22/2000][Batch 8/48][Autoencoder loss: 1370.578857][C loss: 1.210899][M loss: 0.693294][D loss: -847.877808][G loss -2731.804199 ]time: 0:16:26.654077 
[Epoch 22/2000][Batch 9/48][Autoencoder loss: 593.263794][C loss: 1.269793][M loss: 0.693299][D loss: -1235.660889][G loss -3336.520264 ]time: 0:16:27.098520 
[Epoch 22/2000][Batch 10/48][Autoencoder loss: 290.295898][C loss: 1.201775][M loss: 0.693292][D loss: -1867.048584][G loss -3873.238525 ]time: 0:16:27.541670 
[Epoch 22/2000][Batch 11/48][Autoencoder loss: 941.143799][C loss: 1.040253][M loss: 0.693288][D loss: -2385.519531][G loss -4358.722168 ]time: 0:16:27.983727 
[Epoch 22/2000][Batch 12/48][Autoencoder loss: 810.019531][C loss: 0.648529][M loss: 0.693288][D loss: -1964.464355][G loss -5765.520020 ]time: 0:16:28.419638 
[Epoch 22/2000][Batch 13/48][Autoencoder loss: 265.242279][C loss: 0.656629][M loss: 0.693285][D loss: -2511.219971][G loss -6403.074707 ]time: 0:16:28.871108 
[Epoch 22/2000][Batch 14/48][Autoencoder loss: 407.562469][C loss: 0.648712][M loss: 0.693285][D loss: -2814.838379][G loss -6944.127930 ]time: 0:16:29.347778 
[Epoch 22/2000][Batch 15/48][Autoencoder loss: 707.083313][C loss: 0.619840][M loss: 0.693286][D loss: -3058.347900][G loss -7643.867676 ]time: 0:16:29.789017 
[Epoch 22/2000][Batch 16/48][Autoencoder loss: 579.950012][C loss: 0.634120][M loss: 0.693297][D loss: -3947.842285][G loss -7923.009277 ]time: 0:16:30.242365 
[Epoch 22/2000][Batch 17/48][Autoencoder loss: 346.985260][C loss: 0.624024][M loss: 0.693297][D loss: -4436.038574][G loss -8269.117188 ]time: 0:16:30.697201 
[Epoch 22/2000][Batch 18/48][Autoencoder loss: 438.978180][C loss: 0.461527][M loss: 0.693284][D loss: -5031.817383][G loss -9113.967773 ]time: 0:16:31.147523 
[Epoch 22/2000][Batch 19/48][Autoencoder loss: 498.651733][C loss: 0.356659][M loss: 0.693283][D loss: -5422.187988][G loss -9422.408203 ]time: 0:16:31.885471 
[Epoch 22/2000][Batch 20/48][Autoencoder loss: 2422.317383][C loss: 0.583503][M loss: 0.693324][D loss: -5369.227051][G loss -10239.777344 ]time: 0:16:32.999534 
[Epoch 22/2000][Batch 21/48][Autoencoder loss: 7040.311523][C loss: 0.503967][M loss: 0.693296][D loss: -6013.706543][G loss -10933.978516 ]time: 0:16:33.976901 
[Epoch 22/2000][Batch 22/48][Autoencoder loss: 5419.684570][C loss: 0.350665][M loss: 0.704625][D loss: -6558.241211][G loss -11568.847656 ]time: 0:16:34.608840 
[Epoch 22/2000][Batch 23/48][Autoencoder loss: 3851.736572][C loss: 0.211465][M loss: 0.694128][D loss: -7174.379883][G loss -12353.299805 ]time: 0:16:35.056342 
[Epoch 22/2000][Batch 24/48][Autoencoder loss: 5037.670410][C loss: 0.543232][M loss: 0.693622][D loss: -7076.103516][G loss -13087.981445 ]time: 0:16:35.510218 
[Epoch 22/2000][Batch 25/48][Autoencoder loss: 2624.572510][C loss: 0.545596][M loss: 0.693558][D loss: -7009.291016][G loss -13957.806641 ]time: 0:16:35.949716 
[Epoch 22/2000][Batch 26/48][Autoencoder loss: 2649.547363][C loss: 0.619691][M loss: 0.693443][D loss: -7378.591797][G loss -14486.768555 ]time: 0:16:36.413143 
[Epoch 22/2000][Batch 27/48][Autoencoder loss: 4550.091309][C loss: 0.618807][M loss: 0.693391][D loss: -7306.124023][G loss -15010.126953 ]time: 0:16:36.856762 
[Epoch 22/2000][Batch 28/48][Autoencoder loss: 4566.841797][C loss: 1.456010][M loss: 0.693446][D loss: -6301.477051][G loss -17092.779297 ]time: 0:16:37.554136 
[Epoch 22/2000][Batch 29/48][Autoencoder loss: 2264.382080][C loss: 1.313854][M loss: 0.693574][D loss: -7421.282715][G loss -17852.041016 ]time: 0:16:38.005786 
[Epoch 22/2000][Batch 30/48][Autoencoder loss: 2790.091064][C loss: 1.230078][M loss: 0.693467][D loss: -8802.147461][G loss -18067.472656 ]time: 0:16:38.452639 
[Epoch 22/2000][Batch 31/48][Autoencoder loss: 2711.553467][C loss: 0.991036][M loss: 0.693337][D loss: -8123.653320][G loss -18537.259766 ]time: 0:16:38.952685 
[Epoch 22/2000][Batch 32/48][Autoencoder loss: 1619.182007][C loss: 0.782948][M loss: 0.693328][D loss: -12431.005859][G loss -15523.114258 ]time: 0:16:39.400726 
[Epoch 22/2000][Batch 33/48][Autoencoder loss: 688.513550][C loss: 0.703768][M loss: 0.693299][D loss: -12177.243164][G loss -16234.878906 ]time: 0:16:39.843507 
[Epoch 22/2000][Batch 34/48][Autoencoder loss: 1230.284668][C loss: 0.638740][M loss: 0.693318][D loss: -12742.334961][G loss -16419.939453 ]time: 0:16:40.356306 
[Epoch 22/2000][Batch 35/48][Autoencoder loss: 1195.258545][C loss: 0.570307][M loss: 0.693328][D loss: -13596.275391][G loss -17042.308594 ]time: 0:16:40.799765 
[Epoch 22/2000][Batch 36/48][Autoencoder loss: 453.303589][C loss: 1.116727][M loss: 0.693296][D loss: -14144.456055][G loss -18254.146484 ]time: 0:16:41.520133 
[Epoch 22/2000][Batch 37/48][Autoencoder loss: 450.192963][C loss: 1.109661][M loss: 0.693294][D loss: -15056.979492][G loss -18505.746094 ]time: 0:16:42.341601 
[Epoch 22/2000][Batch 38/48][Autoencoder loss: 1040.561768][C loss: 1.016856][M loss: 0.693293][D loss: -15403.472656][G loss -19436.001953 ]time: 0:16:42.788386 
[Epoch 22/2000][Batch 39/48][Autoencoder loss: 886.681213][C loss: 0.858547][M loss: 0.693290][D loss: -15553.884766][G loss -19638.419922 ]time: 0:16:43.231138 
[Epoch 22/2000][Batch 40/48][Autoencoder loss: 707.561829][C loss: 0.627210][M loss: 0.693333][D loss: -15712.640625][G loss -21256.023438 ]time: 0:16:43.672154 
[Epoch 22/2000][Batch 41/48][Autoencoder loss: 1072.904419][C loss: 0.599618][M loss: 0.693317][D loss: -15455.053711][G loss -21564.283203 ]time: 0:16:44.125993 
[Epoch 22/2000][Batch 42/48][Autoencoder loss: 1363.408813][C loss: 0.563744][M loss: 0.693300][D loss: -16680.402344][G loss -22434.865234 ]time: 0:16:44.574825 
[Epoch 22/2000][Batch 43/48][Autoencoder loss: 796.823425][C loss: 0.535333][M loss: 0.693297][D loss: -17539.814453][G loss -23933.222656 ]time: 0:16:45.023929 
[Epoch 22/2000][Batch 44/48][Autoencoder loss: 478.473907][C loss: 1.111552][M loss: 0.693285][D loss: -19603.541016][G loss -23818.501953 ]time: 0:16:45.491179 
[Epoch 22/2000][Batch 45/48][Autoencoder loss: 631.764893][C loss: 1.001463][M loss: 0.693290][D loss: -19996.523438][G loss -23972.148438 ]time: 0:16:45.946551 
[Epoch 22/2000][Batch 46/48][Autoencoder loss: 448.157196][C loss: 0.822972][M loss: 0.693287][D loss: -20554.431641][G loss -25419.179688 ]time: 0:16:46.551482 
[Epoch 22/2000][Batch 47/48][Autoencoder loss: 186.010635][C loss: 0.614952][M loss: 0.693284][D loss: -21017.347656][G loss -26603.369141 ]time: 0:16:47.584910 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
63/63 [==============================] - 0s 666us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 2s
58/63 [==========================>...] - ETA: 0s
63/63 [==============================] - 0s 849us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
58/63 [==========================>...] - ETA: 0s
63/63 [==============================] - 0s 896us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 22/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 23/2000][Batch 0/48][Autoencoder loss: 867.787292][C loss: 1.158297][M loss: 0.693294][D loss: -13450.367188][G loss -35460.691406 ]time: 0:17:07.644988 
[Epoch 23/2000][Batch 1/48][Autoencoder loss: 516.955322][C loss: 1.312704][M loss: 0.693293][D loss: -14035.763672][G loss -35913.828125 ]time: 0:17:08.396740 
[Epoch 23/2000][Batch 2/48][Autoencoder loss: 262.046967][C loss: 1.276714][M loss: 0.693284][D loss: -13764.349609][G loss -36805.066406 ]time: 0:17:08.850009 
[Epoch 23/2000][Batch 3/48][Autoencoder loss: 290.815613][C loss: 1.208693][M loss: 0.693287][D loss: -15107.701172][G loss -37839.250000 ]time: 0:17:09.308154 
[Epoch 23/2000][Batch 4/48][Autoencoder loss: 760.260681][C loss: 1.239122][M loss: 0.693300][D loss: -16212.074219][G loss -39234.925781 ]time: 0:17:09.751905 
[Epoch 23/2000][Batch 5/48][Autoencoder loss: 1470.073608][C loss: 1.252029][M loss: 0.693334][D loss: -15939.877930][G loss -40130.949219 ]time: 0:17:10.200139 
[Epoch 23/2000][Batch 6/48][Autoencoder loss: 1139.580566][C loss: 1.120052][M loss: 0.693291][D loss: -15797.017578][G loss -41389.976562 ]time: 0:17:10.852330 
[Epoch 23/2000][Batch 7/48][Autoencoder loss: 1118.061401][C loss: 0.979755][M loss: 0.693287][D loss: -15891.534180][G loss -42293.117188 ]time: 0:17:11.961854 
[Epoch 23/2000][Batch 8/48][Autoencoder loss: 675.099182][C loss: 1.525405][M loss: 0.693285][D loss: -25450.513672][G loss -33927.203125 ]time: 0:17:12.505073 
[Epoch 23/2000][Batch 9/48][Autoencoder loss: 319.988800][C loss: 1.480588][M loss: 0.693284][D loss: -26218.322266][G loss -34342.710938 ]time: 0:17:12.954287 
[Epoch 23/2000][Batch 10/48][Autoencoder loss: 352.024872][C loss: 1.397563][M loss: 0.693287][D loss: -27888.552734][G loss -36898.722656 ]time: 0:17:13.388058 
[Epoch 23/2000][Batch 11/48][Autoencoder loss: 590.082703][C loss: 1.263637][M loss: 0.693289][D loss: -27691.853516][G loss -37816.605469 ]time: 0:17:13.851369 
[Epoch 23/2000][Batch 12/48][Autoencoder loss: 352.791229][C loss: 0.792563][M loss: 0.693283][D loss: -21483.015625][G loss -44372.609375 ]time: 0:17:14.331820 
[Epoch 23/2000][Batch 13/48][Autoencoder loss: 197.604889][C loss: 0.841304][M loss: 0.693285][D loss: -20503.585938][G loss -47036.601562 ]time: 0:17:14.768509 
[Epoch 23/2000][Batch 14/48][Autoencoder loss: 341.878326][C loss: 0.841835][M loss: 0.693281][D loss: -20290.482422][G loss -47690.746094 ]time: 0:17:15.219333 
[Epoch 23/2000][Batch 15/48][Autoencoder loss: 373.088257][C loss: 0.807234][M loss: 0.693283][D loss: -20849.542969][G loss -49890.742188 ]time: 0:17:15.670538 
[Epoch 23/2000][Batch 16/48][Autoencoder loss: 362.208984][C loss: 0.597979][M loss: 0.693287][D loss: -24207.486328][G loss -48992.882812 ]time: 0:17:16.115369 
[Epoch 23/2000][Batch 17/48][Autoencoder loss: 345.089661][C loss: 0.529944][M loss: 0.693291][D loss: -23994.371094][G loss -48974.074219 ]time: 0:17:16.560012 
[Epoch 23/2000][Batch 18/48][Autoencoder loss: 296.055267][C loss: 0.397412][M loss: 0.693282][D loss: -24446.468750][G loss -49761.898438 ]time: 0:17:17.003391 
[Epoch 23/2000][Batch 19/48][Autoencoder loss: 215.826431][C loss: 0.297561][M loss: 0.693282][D loss: -26261.546875][G loss -53427.292969 ]time: 0:17:17.455547 
[Epoch 23/2000][Batch 20/48][Autoencoder loss: 2275.995850][C loss: 0.404250][M loss: 0.693325][D loss: -23123.640625][G loss -56928.027344 ]time: 0:17:17.906830 
[Epoch 23/2000][Batch 21/48][Autoencoder loss: 6345.849121][C loss: 0.272056][M loss: 0.693341][D loss: -25468.757812][G loss -57224.617188 ]time: 0:17:18.353148 
[Epoch 23/2000][Batch 22/48][Autoencoder loss: 4781.536621][C loss: 0.232804][M loss: 0.698129][D loss: -26437.281250][G loss -58983.441406 ]time: 0:17:18.793003 
[Epoch 23/2000][Batch 23/48][Autoencoder loss: 3474.784912][C loss: 0.199327][M loss: 0.694127][D loss: -25000.050781][G loss -60972.207031 ]time: 0:17:19.239841 
[Epoch 23/2000][Batch 24/48][Autoencoder loss: 3645.251465][C loss: 0.636732][M loss: 0.693843][D loss: -20028.955078][G loss -64343.097656 ]time: 0:17:19.678005 
[Epoch 23/2000][Batch 25/48][Autoencoder loss: 2360.571289][C loss: 0.563715][M loss: 0.693348][D loss: -19335.570312][G loss -65931.031250 ]time: 0:17:21.007397 
[Epoch 23/2000][Batch 26/48][Autoencoder loss: 2756.865723][C loss: 0.608218][M loss: 0.693462][D loss: -19129.632812][G loss -68737.320312 ]time: 0:17:22.009013 
[Epoch 23/2000][Batch 27/48][Autoencoder loss: 3884.972412][C loss: 0.529338][M loss: 0.693576][D loss: -18379.533203][G loss -69471.179688 ]time: 0:17:22.471237 
[Epoch 23/2000][Batch 28/48][Autoencoder loss: 3872.369385][C loss: 1.328873][M loss: 0.693424][D loss: -13580.283203][G loss -78303.906250 ]time: 0:17:22.956981 
[Epoch 23/2000][Batch 29/48][Autoencoder loss: 2231.926025][C loss: 1.168677][M loss: 0.693368][D loss: -14416.884766][G loss -79377.593750 ]time: 0:17:23.397105 
[Epoch 23/2000][Batch 30/48][Autoencoder loss: 2844.765381][C loss: 1.200578][M loss: 0.693430][D loss: -14058.055664][G loss -80771.148438 ]time: 0:17:23.847460 
[Epoch 23/2000][Batch 31/48][Autoencoder loss: 2091.924316][C loss: 0.994352][M loss: 0.693401][D loss: -11896.064453][G loss -83385.882812 ]time: 0:17:24.304873 
[Epoch 23/2000][Batch 32/48][Autoencoder loss: 1133.336670][C loss: 0.823206][M loss: 0.693315][D loss: -29500.296875][G loss -67530.523438 ]time: 0:17:24.764505 
[Epoch 23/2000][Batch 33/48][Autoencoder loss: 752.612000][C loss: 0.751199][M loss: 0.693313][D loss: -26950.601562][G loss -68685.718750 ]time: 0:17:25.213262 
[Epoch 23/2000][Batch 34/48][Autoencoder loss: 993.411743][C loss: 0.665034][M loss: 0.693330][D loss: -26706.914062][G loss -71526.109375 ]time: 0:17:25.655545 
[Epoch 23/2000][Batch 35/48][Autoencoder loss: 761.621277][C loss: 0.564255][M loss: 0.693297][D loss: -27021.917969][G loss -71466.382812 ]time: 0:17:26.120763 
[Epoch 23/2000][Batch 36/48][Autoencoder loss: 372.937988][C loss: 0.729105][M loss: 0.693296][D loss: -27358.113281][G loss -75111.734375 ]time: 0:17:26.576122 
[Epoch 23/2000][Batch 37/48][Autoencoder loss: 577.829834][C loss: 0.723979][M loss: 0.693311][D loss: -25591.410156][G loss -76338.546875 ]time: 0:17:27.024801 
[Epoch 23/2000][Batch 38/48][Autoencoder loss: 804.115662][C loss: 0.675922][M loss: 0.693297][D loss: -26028.113281][G loss -77932.640625 ]time: 0:17:27.536693 
[Epoch 23/2000][Batch 39/48][Autoencoder loss: 626.412354][C loss: 0.604888][M loss: 0.693290][D loss: -26839.218750][G loss -81681.976562 ]time: 0:17:27.962327 
[Epoch 23/2000][Batch 40/48][Autoencoder loss: 787.394592][C loss: 0.617477][M loss: 0.693311][D loss: -21890.140625][G loss -84606.578125 ]time: 0:17:28.408267 
[Epoch 23/2000][Batch 41/48][Autoencoder loss: 1106.965088][C loss: 0.603548][M loss: 0.693316][D loss: -22521.529297][G loss -88227.234375 ]time: 0:17:28.840762 
[Epoch 23/2000][Batch 42/48][Autoencoder loss: 1171.674927][C loss: 0.578470][M loss: 0.693299][D loss: -22802.382812][G loss -92003.500000 ]time: 0:17:29.270439 
[Epoch 23/2000][Batch 43/48][Autoencoder loss: 755.487000][C loss: 0.546893][M loss: 0.693286][D loss: -18056.074219][G loss -92246.953125 ]time: 0:17:29.705757 
[Epoch 23/2000][Batch 44/48][Autoencoder loss: 575.413696][C loss: 1.226592][M loss: 0.693291][D loss: -23714.080078][G loss -90751.359375 ]time: 0:17:30.296401 
[Epoch 23/2000][Batch 45/48][Autoencoder loss: 558.757568][C loss: 1.117211][M loss: 0.693286][D loss: -24333.460938][G loss -90146.718750 ]time: 0:17:31.413204 
[Epoch 23/2000][Batch 46/48][Autoencoder loss: 322.713562][C loss: 0.937926][M loss: 0.693289][D loss: -24037.109375][G loss -94546.179688 ]time: 0:17:31.933316 
[Epoch 23/2000][Batch 47/48][Autoencoder loss: 214.657303][C loss: 0.714594][M loss: 0.693285][D loss: -24983.175781][G loss -96313.984375 ]time: 0:17:32.382958 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 515us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 507us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 423us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 23/2000] [Accuracy_lsvm: 0.402431] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 24/2000][Batch 0/48][Autoencoder loss: 906.846008][C loss: 1.188556][M loss: 0.693303][D loss: 10471.268555][G loss -130668.500000 ]time: 0:17:47.683279 
[Epoch 24/2000][Batch 1/48][Autoencoder loss: 443.393036][C loss: 1.294765][M loss: 0.693290][D loss: 11636.833984][G loss -128387.929688 ]time: 0:17:48.121049 
[Epoch 24/2000][Batch 2/48][Autoencoder loss: 278.779938][C loss: 1.265927][M loss: 0.693284][D loss: 11037.966797][G loss -133395.609375 ]time: 0:17:48.571953 
[Epoch 24/2000][Batch 3/48][Autoencoder loss: 377.094025][C loss: 1.222595][M loss: 0.693285][D loss: 16457.726562][G loss -134504.656250 ]time: 0:17:49.204481 
[Epoch 24/2000][Batch 4/48][Autoencoder loss: 701.620972][C loss: 1.320091][M loss: 0.693284][D loss: 15164.990234][G loss -137846.890625 ]time: 0:17:50.155474 
[Epoch 24/2000][Batch 5/48][Autoencoder loss: 1387.810791][C loss: 1.300344][M loss: 0.693309][D loss: 14927.238281][G loss -139101.906250 ]time: 0:17:50.999574 
[Epoch 24/2000][Batch 6/48][Autoencoder loss: 1260.590576][C loss: 1.164813][M loss: 0.693296][D loss: 20124.328125][G loss -139842.421875 ]time: 0:17:52.035961 
[Epoch 24/2000][Batch 7/48][Autoencoder loss: 1149.799683][C loss: 1.053328][M loss: 0.693282][D loss: 22451.683594][G loss -140348.296875 ]time: 0:17:52.826312 
[Epoch 24/2000][Batch 8/48][Autoencoder loss: 439.352570][C loss: 1.292131][M loss: 0.693282][D loss: -5674.999023][G loss -108227.031250 ]time: 0:17:53.461136 
[Epoch 24/2000][Batch 9/48][Autoencoder loss: 242.345367][C loss: 1.245075][M loss: 0.693290][D loss: -9879.653320][G loss -105926.765625 ]time: 0:17:53.910965 
[Epoch 24/2000][Batch 10/48][Autoencoder loss: 403.141602][C loss: 1.126011][M loss: 0.693287][D loss: -11082.571289][G loss -109794.296875 ]time: 0:17:54.363679 
[Epoch 24/2000][Batch 11/48][Autoencoder loss: 482.809113][C loss: 0.948032][M loss: 0.693286][D loss: -5696.003906][G loss -112175.132812 ]time: 0:17:54.813041 
[Epoch 24/2000][Batch 12/48][Autoencoder loss: 245.804688][C loss: 1.302363][M loss: 0.693285][D loss: 16873.839844][G loss -129892.226562 ]time: 0:17:55.263198 
[Epoch 24/2000][Batch 13/48][Autoencoder loss: 244.631836][C loss: 1.134965][M loss: 0.693281][D loss: 17241.281250][G loss -129913.179688 ]time: 0:17:55.724233 
[Epoch 24/2000][Batch 14/48][Autoencoder loss: 353.826965][C loss: 0.952265][M loss: 0.693280][D loss: 21774.398438][G loss -129056.023438 ]time: 0:17:56.169344 
[Epoch 24/2000][Batch 15/48][Autoencoder loss: 273.603912][C loss: 0.831313][M loss: 0.693281][D loss: 22026.910156][G loss -131159.328125 ]time: 0:17:56.645765 
[Epoch 24/2000][Batch 16/48][Autoencoder loss: 312.984772][C loss: 0.662520][M loss: 0.693288][D loss: 17115.837891][G loss -125497.367188 ]time: 0:17:57.165508 
[Epoch 24/2000][Batch 17/48][Autoencoder loss: 379.778198][C loss: 0.622457][M loss: 0.693297][D loss: 16850.312500][G loss -126497.679688 ]time: 0:17:57.595791 
[Epoch 24/2000][Batch 18/48][Autoencoder loss: 244.207611][C loss: 0.525454][M loss: 0.693279][D loss: 16938.892578][G loss -125066.554688 ]time: 0:17:58.042166 
[Epoch 24/2000][Batch 19/48][Autoencoder loss: 142.675583][C loss: 0.450013][M loss: 0.693279][D loss: 22426.890625][G loss -122555.500000 ]time: 0:17:58.489630 
[Epoch 24/2000][Batch 20/48][Autoencoder loss: 2341.294434][C loss: 0.988373][M loss: 0.693306][D loss: 27231.527344][G loss -125597.351562 ]time: 0:17:58.960231 
[Epoch 24/2000][Batch 21/48][Autoencoder loss: 6248.724121][C loss: 0.700897][M loss: 0.693288][D loss: 28323.806641][G loss -123806.351562 ]time: 0:17:59.412240 
[Epoch 24/2000][Batch 22/48][Autoencoder loss: 4798.126465][C loss: 0.333440][M loss: 0.695782][D loss: 28335.593750][G loss -125433.617188 ]time: 0:17:59.859243 
[Epoch 24/2000][Batch 23/48][Autoencoder loss: 3586.036133][C loss: 0.299824][M loss: 0.694289][D loss: 33156.207031][G loss -124794.781250 ]time: 0:18:00.296378 
[Epoch 24/2000][Batch 24/48][Autoencoder loss: 3037.536377][C loss: 0.784493][M loss: 0.693955][D loss: 35491.738281][G loss -126259.429688 ]time: 0:18:00.753385 
[Epoch 24/2000][Batch 25/48][Autoencoder loss: 2017.716431][C loss: 0.630619][M loss: 0.693419][D loss: 36117.757812][G loss -123969.406250 ]time: 0:18:01.459964 
[Epoch 24/2000][Batch 26/48][Autoencoder loss: 2569.094482][C loss: 0.588555][M loss: 0.693470][D loss: 39722.019531][G loss -123593.835938 ]time: 0:18:02.583077 
[Epoch 24/2000][Batch 27/48][Autoencoder loss: 3590.311523][C loss: 0.509669][M loss: 0.693598][D loss: 40364.449219][G loss -120787.070312 ]time: 0:18:03.796380 
[Epoch 24/2000][Batch 28/48][Autoencoder loss: 3552.914551][C loss: 1.463211][M loss: 0.693452][D loss: 51686.757812][G loss -132610.359375 ]time: 0:18:05.058004 
[Epoch 24/2000][Batch 29/48][Autoencoder loss: 2129.020996][C loss: 1.390330][M loss: 0.693434][D loss: 52323.332031][G loss -130301.679688 ]time: 0:18:06.013633 
[Epoch 24/2000][Batch 30/48][Autoencoder loss: 2761.461426][C loss: 1.393661][M loss: 0.693421][D loss: 49748.273438][G loss -124920.992188 ]time: 0:18:06.619560 
[Epoch 24/2000][Batch 31/48][Autoencoder loss: 1892.847900][C loss: 1.206356][M loss: 0.693383][D loss: 50502.863281][G loss -119116.906250 ]time: 0:18:07.067895 
[Epoch 24/2000][Batch 32/48][Autoencoder loss: 1010.248840][C loss: 1.010131][M loss: 0.693339][D loss: 23116.246094][G loss -88358.039062 ]time: 0:18:07.519097 
[Epoch 24/2000][Batch 33/48][Autoencoder loss: 751.103943][C loss: 0.862355][M loss: 0.693353][D loss: 22816.736328][G loss -83178.718750 ]time: 0:18:07.962416 
[Epoch 24/2000][Batch 34/48][Autoencoder loss: 902.257996][C loss: 0.722226][M loss: 0.693361][D loss: 24280.162109][G loss -84095.718750 ]time: 0:18:08.408729 
[Epoch 24/2000][Batch 35/48][Autoencoder loss: 574.145813][C loss: 0.639196][M loss: 0.693298][D loss: 21194.083984][G loss -77685.679688 ]time: 0:18:08.851140 
[Epoch 24/2000][Batch 36/48][Autoencoder loss: 293.948364][C loss: 0.867725][M loss: 0.693298][D loss: 22357.560547][G loss -77238.640625 ]time: 0:18:09.304885 
[Epoch 24/2000][Batch 37/48][Autoencoder loss: 561.171875][C loss: 0.797778][M loss: 0.693331][D loss: 19660.988281][G loss -75338.250000 ]time: 0:18:09.748382 
[Epoch 24/2000][Batch 38/48][Autoencoder loss: 682.771545][C loss: 0.735728][M loss: 0.693317][D loss: 21295.378906][G loss -70969.703125 ]time: 0:18:10.249466 
[Epoch 24/2000][Batch 39/48][Autoencoder loss: 509.175659][C loss: 0.631020][M loss: 0.693288][D loss: 23136.085938][G loss -68544.820312 ]time: 0:18:10.700472 
[Epoch 24/2000][Batch 40/48][Autoencoder loss: 772.227234][C loss: 0.664237][M loss: 0.693314][D loss: 24655.617188][G loss -68475.578125 ]time: 0:18:11.150260 
[Epoch 24/2000][Batch 41/48][Autoencoder loss: 1052.774902][C loss: 0.650950][M loss: 0.693313][D loss: 24246.882812][G loss -64740.898438 ]time: 0:18:11.595832 
[Epoch 24/2000][Batch 42/48][Autoencoder loss: 1030.466187][C loss: 0.627174][M loss: 0.693294][D loss: 20585.568359][G loss -61213.699219 ]time: 0:18:12.103331 
[Epoch 24/2000][Batch 43/48][Autoencoder loss: 696.320190][C loss: 0.508243][M loss: 0.693299][D loss: 21492.625000][G loss -60606.242188 ]time: 0:18:13.154858 
[Epoch 24/2000][Batch 44/48][Autoencoder loss: 560.276794][C loss: 1.148626][M loss: 0.693307][D loss: 16676.746094][G loss -53206.191406 ]time: 0:18:13.599525 
[Epoch 24/2000][Batch 45/48][Autoencoder loss: 449.222656][C loss: 1.085321][M loss: 0.693284][D loss: 16982.691406][G loss -51601.078125 ]time: 0:18:14.029954 
[Epoch 24/2000][Batch 46/48][Autoencoder loss: 229.266693][C loss: 0.905978][M loss: 0.693289][D loss: 14963.755859][G loss -47574.511719 ]time: 0:18:14.466148 
[Epoch 24/2000][Batch 47/48][Autoencoder loss: 218.649643][C loss: 0.666467][M loss: 0.693292][D loss: 15272.959961][G loss -45144.359375 ]time: 0:18:14.915950 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 531us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 444us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 400us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 24/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 25/2000][Batch 0/48][Autoencoder loss: 751.339294][C loss: 1.773881][M loss: 0.693294][D loss: 33431.226562][G loss -57741.898438 ]time: 0:18:31.644606 
[Epoch 25/2000][Batch 1/48][Autoencoder loss: 263.116821][C loss: 1.619942][M loss: 0.693287][D loss: 29038.662109][G loss -54797.312500 ]time: 0:18:32.098755 
[Epoch 25/2000][Batch 2/48][Autoencoder loss: 146.350388][C loss: 1.457672][M loss: 0.693293][D loss: 28466.988281][G loss -48891.289062 ]time: 0:18:32.545598 
[Epoch 25/2000][Batch 3/48][Autoencoder loss: 249.132782][C loss: 1.357779][M loss: 0.693286][D loss: 24123.220703][G loss -44674.101562 ]time: 0:18:33.190454 
[Epoch 25/2000][Batch 4/48][Autoencoder loss: 635.405640][C loss: 1.306924][M loss: 0.693280][D loss: 23259.693359][G loss -39933.480469 ]time: 0:18:34.537364 
[Epoch 25/2000][Batch 5/48][Autoencoder loss: 1324.998169][C loss: 1.418307][M loss: 0.693293][D loss: 21874.683594][G loss -33936.742188 ]time: 0:18:35.923200 
[Epoch 25/2000][Batch 6/48][Autoencoder loss: 1292.221558][C loss: 1.273051][M loss: 0.693282][D loss: 18387.953125][G loss -28570.699219 ]time: 0:18:36.370393 
[Epoch 25/2000][Batch 7/48][Autoencoder loss: 1110.771606][C loss: 1.047677][M loss: 0.693281][D loss: 15615.510742][G loss -23936.054688 ]time: 0:18:36.828039 
[Epoch 25/2000][Batch 8/48][Autoencoder loss: 345.329987][C loss: 1.218716][M loss: 0.693288][D loss: 7359.809082][G loss -14220.606445 ]time: 0:18:37.280800 
[Epoch 25/2000][Batch 9/48][Autoencoder loss: 231.289749][C loss: 1.128490][M loss: 0.693287][D loss: 5825.907227][G loss -10102.642578 ]time: 0:18:37.744785 
[Epoch 25/2000][Batch 10/48][Autoencoder loss: 390.163910][C loss: 0.975431][M loss: 0.693280][D loss: 5208.205078][G loss -6702.827148 ]time: 0:18:38.198743 
[Epoch 25/2000][Batch 11/48][Autoencoder loss: 382.694672][C loss: 0.790812][M loss: 0.693280][D loss: 3665.376953][G loss -3421.821533 ]time: 0:18:38.660471 
[Epoch 25/2000][Batch 12/48][Autoencoder loss: 204.597488][C loss: 0.775981][M loss: 0.693279][D loss: 3268.614990][G loss -80.487503 ]time: 0:18:39.117757 
[Epoch 25/2000][Batch 13/48][Autoencoder loss: 278.755981][C loss: 0.827519][M loss: 0.693278][D loss: 1520.438477][G loss 3987.552002 ]time: 0:18:39.579587 
[Epoch 25/2000][Batch 14/48][Autoencoder loss: 336.429230][C loss: 0.861862][M loss: 0.693278][D loss: -368.399475][G loss 7965.468750 ]time: 0:18:40.042168 
[Epoch 25/2000][Batch 15/48][Autoencoder loss: 231.683884][C loss: 0.888998][M loss: 0.693280][D loss: -1953.183105][G loss 12292.290039 ]time: 0:18:40.491477 
[Epoch 25/2000][Batch 16/48][Autoencoder loss: 331.613739][C loss: 0.720001][M loss: 0.693279][D loss: -3286.417480][G loss 15269.443359 ]time: 0:18:41.012994 
[Epoch 25/2000][Batch 17/48][Autoencoder loss: 422.483582][C loss: 0.681315][M loss: 0.693281][D loss: -4825.815430][G loss 19240.783203 ]time: 0:18:41.463378 
[Epoch 25/2000][Batch 18/48][Autoencoder loss: 227.211914][C loss: 0.549681][M loss: 0.693278][D loss: -5938.271484][G loss 22746.312500 ]time: 0:18:41.950595 
[Epoch 25/2000][Batch 19/48][Autoencoder loss: 155.036789][C loss: 0.442633][M loss: 0.693279][D loss: -8491.946289][G loss 27098.628906 ]time: 0:18:42.416429 
[Epoch 25/2000][Batch 20/48][Autoencoder loss: 2289.677002][C loss: 0.585525][M loss: 0.693308][D loss: -10512.126953][G loss 31775.375000 ]time: 0:18:42.875701 
[Epoch 25/2000][Batch 21/48][Autoencoder loss: 5629.815430][C loss: 0.564378][M loss: 0.693298][D loss: -11691.785156][G loss 36693.433594 ]time: 0:18:43.909742 
[Epoch 25/2000][Batch 22/48][Autoencoder loss: 4357.174316][C loss: 0.319580][M loss: 0.694497][D loss: -13497.618164][G loss 39933.015625 ]time: 0:18:44.666529 
[Epoch 25/2000][Batch 23/48][Autoencoder loss: 3300.543701][C loss: 0.149579][M loss: 0.693708][D loss: -14572.949219][G loss 45095.707031 ]time: 0:18:45.128100 
[Epoch 25/2000][Batch 24/48][Autoencoder loss: 2330.098389][C loss: 0.744249][M loss: 0.693486][D loss: -19851.652344][G loss 50489.808594 ]time: 0:18:45.574942 
[Epoch 25/2000][Batch 25/48][Autoencoder loss: 1787.774536][C loss: 0.641132][M loss: 0.693306][D loss: -20621.396484][G loss 56611.550781 ]time: 0:18:46.016369 
[Epoch 25/2000][Batch 26/48][Autoencoder loss: 2365.900635][C loss: 0.723373][M loss: 0.693348][D loss: -22903.251953][G loss 60323.199219 ]time: 0:18:46.467218 
[Epoch 25/2000][Batch 27/48][Autoencoder loss: 2883.999756][C loss: 0.621571][M loss: 0.693392][D loss: -24742.388672][G loss 66173.031250 ]time: 0:18:46.920641 
[Epoch 25/2000][Batch 28/48][Autoencoder loss: 2856.165283][C loss: 1.766547][M loss: 0.693324][D loss: -34681.796875][G loss 80105.718750 ]time: 0:18:47.525687 
[Epoch 25/2000][Batch 29/48][Autoencoder loss: 2045.544800][C loss: 1.648998][M loss: 0.693295][D loss: -39379.863281][G loss 87943.351562 ]time: 0:18:48.509000 
[Epoch 25/2000][Batch 30/48][Autoencoder loss: 2499.593750][C loss: 1.698633][M loss: 0.693341][D loss: -39493.882812][G loss 93689.500000 ]time: 0:18:49.272444 
[Epoch 25/2000][Batch 31/48][Autoencoder loss: 1507.090332][C loss: 1.491566][M loss: 0.693329][D loss: -43936.066406][G loss 101521.085938 ]time: 0:18:49.718413 
[Epoch 25/2000][Batch 32/48][Autoencoder loss: 940.123840][C loss: 1.362776][M loss: 0.693283][D loss: -24343.091797][G loss 85314.101562 ]time: 0:18:50.163181 
[Epoch 25/2000][Batch 33/48][Autoencoder loss: 788.293640][C loss: 1.234127][M loss: 0.693295][D loss: -25330.398438][G loss 87294.281250 ]time: 0:18:50.612817 
[Epoch 25/2000][Batch 34/48][Autoencoder loss: 745.965027][C loss: 1.077873][M loss: 0.693314][D loss: -27174.433594][G loss 95191.085938 ]time: 0:18:51.096191 
[Epoch 25/2000][Batch 35/48][Autoencoder loss: 488.796478][C loss: 0.901636][M loss: 0.693290][D loss: -30074.025391][G loss 102799.132812 ]time: 0:18:51.541789 
[Epoch 25/2000][Batch 36/48][Autoencoder loss: 423.288208][C loss: 0.753922][M loss: 0.693283][D loss: -29514.958984][G loss 108122.914062 ]time: 0:18:51.978441 
[Epoch 25/2000][Batch 37/48][Autoencoder loss: 574.114868][C loss: 0.711493][M loss: 0.693293][D loss: -32059.335938][G loss 110557.046875 ]time: 0:18:52.442331 
[Epoch 25/2000][Batch 38/48][Autoencoder loss: 580.093750][C loss: 0.706942][M loss: 0.693287][D loss: -33689.894531][G loss 118961.671875 ]time: 0:18:52.980632 
[Epoch 25/2000][Batch 39/48][Autoencoder loss: 557.142395][C loss: 0.615469][M loss: 0.693281][D loss: -36273.898438][G loss 123602.453125 ]time: 0:18:53.975650 
[Epoch 25/2000][Batch 40/48][Autoencoder loss: 837.012756][C loss: 0.504915][M loss: 0.693294][D loss: -36400.949219][G loss 129172.820312 ]time: 0:18:54.865875 
[Epoch 25/2000][Batch 41/48][Autoencoder loss: 963.152649][C loss: 0.500690][M loss: 0.693290][D loss: -37100.179688][G loss 135657.578125 ]time: 0:18:55.360526 
[Epoch 25/2000][Batch 42/48][Autoencoder loss: 953.403320][C loss: 0.492178][M loss: 0.693283][D loss: -39027.007812][G loss 140913.843750 ]time: 0:18:55.807565 
[Epoch 25/2000][Batch 43/48][Autoencoder loss: 707.068542][C loss: 0.485627][M loss: 0.693288][D loss: -39449.898438][G loss 146505.406250 ]time: 0:18:56.259673 
[Epoch 25/2000][Batch 44/48][Autoencoder loss: 517.750061][C loss: 1.410664][M loss: 0.693288][D loss: -35000.089844][G loss 140374.359375 ]time: 0:18:56.717468 
[Epoch 25/2000][Batch 45/48][Autoencoder loss: 368.741791][C loss: 1.239038][M loss: 0.693281][D loss: -37453.617188][G loss 145589.437500 ]time: 0:18:57.157656 
[Epoch 25/2000][Batch 46/48][Autoencoder loss: 260.676178][C loss: 1.034342][M loss: 0.693286][D loss: -33869.140625][G loss 150601.140625 ]time: 0:18:57.604521 
[Epoch 25/2000][Batch 47/48][Autoencoder loss: 239.075226][C loss: 0.814064][M loss: 0.693281][D loss: -32538.712891][G loss 156378.796875 ]time: 0:18:58.057284 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 442us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 542us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 520us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 25/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 26/2000][Batch 0/48][Autoencoder loss: 719.841431][C loss: 1.278608][M loss: 0.693290][D loss: -84125.929688][G loss 211243.968750 ]time: 0:19:19.275718 
[Epoch 26/2000][Batch 1/48][Autoencoder loss: 313.037903][C loss: 1.344582][M loss: 0.693281][D loss: -86657.218750][G loss 221473.484375 ]time: 0:19:19.946705 
[Epoch 26/2000][Batch 2/48][Autoencoder loss: 305.250336][C loss: 1.306817][M loss: 0.693284][D loss: -79823.906250][G loss 230141.750000 ]time: 0:19:20.405325 
[Epoch 26/2000][Batch 3/48][Autoencoder loss: 326.847778][C loss: 1.244534][M loss: 0.693292][D loss: -81740.695312][G loss 235858.812500 ]time: 0:19:20.846275 
[Epoch 26/2000][Batch 4/48][Autoencoder loss: 540.873962][C loss: 1.308429][M loss: 0.693279][D loss: -79513.828125][G loss 242170.281250 ]time: 0:19:21.293344 
[Epoch 26/2000][Batch 5/48][Autoencoder loss: 1247.834351][C loss: 1.295849][M loss: 0.693294][D loss: -83863.531250][G loss 246287.343750 ]time: 0:19:21.745991 
[Epoch 26/2000][Batch 6/48][Autoencoder loss: 1233.171875][C loss: 1.104265][M loss: 0.693286][D loss: -80008.578125][G loss 251691.734375 ]time: 0:19:22.200255 
[Epoch 26/2000][Batch 7/48][Autoencoder loss: 968.473206][C loss: 1.046184][M loss: 0.693282][D loss: -78266.578125][G loss 258775.109375 ]time: 0:19:22.667472 
[Epoch 26/2000][Batch 8/48][Autoencoder loss: 298.162750][C loss: 1.520681][M loss: 0.693280][D loss: -7875.744141][G loss 202951.265625 ]time: 0:19:23.156624 
[Epoch 26/2000][Batch 9/48][Autoencoder loss: 279.433136][C loss: 1.317138][M loss: 0.693283][D loss: -7836.865234][G loss 210253.796875 ]time: 0:19:23.679902 
[Epoch 26/2000][Batch 10/48][Autoencoder loss: 444.688354][C loss: 1.134449][M loss: 0.693280][D loss: -4871.011719][G loss 199207.546875 ]time: 0:19:24.126048 
[Epoch 26/2000][Batch 11/48][Autoencoder loss: 355.786896][C loss: 0.993169][M loss: 0.693279][D loss: -3083.976562][G loss 207392.093750 ]time: 0:19:24.574773 
[Epoch 26/2000][Batch 12/48][Autoencoder loss: 219.155991][C loss: 0.783938][M loss: 0.693278][D loss: -34664.128906][G loss 250783.265625 ]time: 0:19:25.021473 
[Epoch 26/2000][Batch 13/48][Autoencoder loss: 314.666199][C loss: 0.823084][M loss: 0.693277][D loss: -37464.609375][G loss 243468.281250 ]time: 0:19:25.978698 
[Epoch 26/2000][Batch 14/48][Autoencoder loss: 310.416931][C loss: 0.810362][M loss: 0.693278][D loss: -22753.632812][G loss 250741.062500 ]time: 0:19:26.867812 
[Epoch 26/2000][Batch 15/48][Autoencoder loss: 214.470901][C loss: 0.767311][M loss: 0.693278][D loss: -23557.144531][G loss 241810.531250 ]time: 0:19:27.320012 
[Epoch 26/2000][Batch 16/48][Autoencoder loss: 393.943909][C loss: 0.587709][M loss: 0.693278][D loss: 1083.994141][G loss 225985.531250 ]time: 0:19:27.763904 
[Epoch 26/2000][Batch 17/48][Autoencoder loss: 419.489441][C loss: 0.534221][M loss: 0.693278][D loss: 9209.404297][G loss 224705.828125 ]time: 0:19:28.215351 
[Epoch 26/2000][Batch 18/48][Autoencoder loss: 205.923203][C loss: 0.464526][M loss: 0.693277][D loss: 14882.162109][G loss 219208.468750 ]time: 0:19:28.674079 
[Epoch 26/2000][Batch 19/48][Autoencoder loss: 164.091400][C loss: 0.415844][M loss: 0.693277][D loss: 16849.232422][G loss 217037.093750 ]time: 0:19:29.117910 
[Epoch 26/2000][Batch 20/48][Autoencoder loss: 2379.529053][C loss: 0.560327][M loss: 0.693474][D loss: 18338.019531][G loss 212219.093750 ]time: 0:19:29.564157 
[Epoch 26/2000][Batch 21/48][Autoencoder loss: 5535.445801][C loss: 0.292746][M loss: 0.693298][D loss: 28795.298828][G loss 209734.031250 ]time: 0:19:30.000272 
[Epoch 26/2000][Batch 22/48][Autoencoder loss: 4158.632324][C loss: 0.041890][M loss: 0.693374][D loss: 36443.113281][G loss 202154.906250 ]time: 0:19:30.449142 
[Epoch 26/2000][Batch 23/48][Autoencoder loss: 3273.698975][C loss: 0.244400][M loss: 0.693381][D loss: 41212.675781][G loss 194137.171875 ]time: 0:19:30.886216 
[Epoch 26/2000][Batch 24/48][Autoencoder loss: 2712.179932][C loss: 0.551833][M loss: 0.693417][D loss: 27842.957031][G loss 189033.453125 ]time: 0:19:31.333103 
[Epoch 26/2000][Batch 25/48][Autoencoder loss: 1722.590576][C loss: 0.458016][M loss: 0.693338][D loss: 29588.945312][G loss 180052.531250 ]time: 0:19:32.516093 
[Epoch 26/2000][Batch 26/48][Autoencoder loss: 2648.475830][C loss: 0.414826][M loss: 0.693292][D loss: 28781.171875][G loss 173419.750000 ]time: 0:19:33.517951 
[Epoch 26/2000][Batch 27/48][Autoencoder loss: 3318.788574][C loss: 0.311475][M loss: 0.693292][D loss: 36997.046875][G loss 165863.531250 ]time: 0:19:34.038369 
[Epoch 26/2000][Batch 28/48][Autoencoder loss: 2921.851318][C loss: 1.455713][M loss: 0.693337][D loss: 21923.537109][G loss 172531.593750 ]time: 0:19:34.522197 
[Epoch 26/2000][Batch 29/48][Autoencoder loss: 2118.743164][C loss: 1.369892][M loss: 0.693334][D loss: 25959.787109][G loss 164692.484375 ]time: 0:19:34.973755 
[Epoch 26/2000][Batch 30/48][Autoencoder loss: 2812.911377][C loss: 1.283873][M loss: 0.693308][D loss: 32459.099609][G loss 153270.562500 ]time: 0:19:35.416688 
[Epoch 26/2000][Batch 31/48][Autoencoder loss: 1593.100098][C loss: 1.107465][M loss: 0.693293][D loss: 33145.226562][G loss 142296.828125 ]time: 0:19:36.293071 
[Epoch 26/2000][Batch 32/48][Autoencoder loss: 914.302490][C loss: 1.174703][M loss: 0.693280][D loss: 62633.421875][G loss 107148.335938 ]time: 0:19:37.208506 
[Epoch 26/2000][Batch 33/48][Autoencoder loss: 936.729675][C loss: 1.085451][M loss: 0.693282][D loss: 64595.667969][G loss 104410.250000 ]time: 0:19:37.790514 
[Epoch 26/2000][Batch 34/48][Autoencoder loss: 897.035828][C loss: 0.936642][M loss: 0.693279][D loss: 64621.875000][G loss 91119.757812 ]time: 0:19:38.243020 
[Epoch 26/2000][Batch 35/48][Autoencoder loss: 476.003906][C loss: 0.785103][M loss: 0.693279][D loss: 63544.585938][G loss 88042.429688 ]time: 0:19:38.687303 
[Epoch 26/2000][Batch 36/48][Autoencoder loss: 469.522583][C loss: 0.697980][M loss: 0.693280][D loss: 62361.765625][G loss 81907.500000 ]time: 0:19:39.132709 
[Epoch 26/2000][Batch 37/48][Autoencoder loss: 738.864563][C loss: 0.623653][M loss: 0.693281][D loss: 59792.503906][G loss 75743.515625 ]time: 0:19:39.581994 
[Epoch 26/2000][Batch 38/48][Autoencoder loss: 637.334656][C loss: 0.544870][M loss: 0.693279][D loss: 57040.921875][G loss 69844.664062 ]time: 0:19:40.051727 
[Epoch 26/2000][Batch 39/48][Autoencoder loss: 573.250732][C loss: 0.469724][M loss: 0.693278][D loss: 55238.402344][G loss 63639.816406 ]time: 0:19:40.541723 
[Epoch 26/2000][Batch 40/48][Autoencoder loss: 940.336609][C loss: 0.580681][M loss: 0.693288][D loss: 49857.218750][G loss 59170.707031 ]time: 0:19:40.978524 
[Epoch 26/2000][Batch 41/48][Autoencoder loss: 1027.134644][C loss: 0.549508][M loss: 0.693282][D loss: 48943.308594][G loss 53523.742188 ]time: 0:19:41.421880 
[Epoch 26/2000][Batch 42/48][Autoencoder loss: 935.432373][C loss: 0.492219][M loss: 0.693281][D loss: 48805.851562][G loss 49161.273438 ]time: 0:19:41.872236 
[Epoch 26/2000][Batch 43/48][Autoencoder loss: 769.833740][C loss: 0.424229][M loss: 0.693278][D loss: 46460.679688][G loss 45596.761719 ]time: 0:19:42.312708 
[Epoch 26/2000][Batch 44/48][Autoencoder loss: 589.870483][C loss: 1.110298][M loss: 0.693282][D loss: 45729.019531][G loss 38924.804688 ]time: 0:19:42.768985 
[Epoch 26/2000][Batch 45/48][Autoencoder loss: 354.431427][C loss: 1.092355][M loss: 0.693281][D loss: 41526.664062][G loss 35630.851562 ]time: 0:19:43.225435 
[Epoch 26/2000][Batch 46/48][Autoencoder loss: 233.957321][C loss: 0.983527][M loss: 0.693280][D loss: 38954.554688][G loss 31880.615234 ]time: 0:19:43.686186 
[Epoch 26/2000][Batch 47/48][Autoencoder loss: 255.658096][C loss: 0.810092][M loss: 0.693281][D loss: 35965.957031][G loss 28991.658203 ]time: 0:19:44.120600 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 391us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 435us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 448us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 26/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 27/2000][Batch 0/48][Autoencoder loss: 663.384888][C loss: 1.587226][M loss: 0.693282][D loss: 27545.460938][G loss 31480.712891 ]time: 0:20:00.485524 
[Epoch 27/2000][Batch 1/48][Autoencoder loss: 251.582977][C loss: 1.710945][M loss: 0.693281][D loss: 26057.878906][G loss 28876.833984 ]time: 0:20:01.098206 
[Epoch 27/2000][Batch 2/48][Autoencoder loss: 334.062897][C loss: 1.641929][M loss: 0.693282][D loss: 24207.828125][G loss 25060.785156 ]time: 0:20:01.548429 
[Epoch 27/2000][Batch 3/48][Autoencoder loss: 364.985687][C loss: 1.563680][M loss: 0.693280][D loss: 22547.167969][G loss 22703.136719 ]time: 0:20:01.991591 
[Epoch 27/2000][Batch 4/48][Autoencoder loss: 573.710327][C loss: 1.348765][M loss: 0.693280][D loss: 21722.072266][G loss 19559.005859 ]time: 0:20:02.497422 
[Epoch 27/2000][Batch 5/48][Autoencoder loss: 1227.030151][C loss: 1.380729][M loss: 0.693285][D loss: 20905.046875][G loss 17045.236328 ]time: 0:20:02.954736 
[Epoch 27/2000][Batch 6/48][Autoencoder loss: 1249.795532][C loss: 1.248899][M loss: 0.693279][D loss: 19861.984375][G loss 14662.474609 ]time: 0:20:03.395763 
[Epoch 27/2000][Batch 7/48][Autoencoder loss: 954.841431][C loss: 1.098464][M loss: 0.693278][D loss: 18624.681641][G loss 12744.548828 ]time: 0:20:03.852146 
[Epoch 27/2000][Batch 8/48][Autoencoder loss: 277.929962][C loss: 0.898720][M loss: 0.693278][D loss: 19902.941406][G loss 8040.476074 ]time: 0:20:04.450144 
[Epoch 27/2000][Batch 9/48][Autoencoder loss: 296.938904][C loss: 0.869387][M loss: 0.693277][D loss: 18879.626953][G loss 6601.875000 ]time: 0:20:05.604608 
[Epoch 27/2000][Batch 10/48][Autoencoder loss: 498.765503][C loss: 0.822037][M loss: 0.693278][D loss: 17284.382812][G loss 5109.157715 ]time: 0:20:06.217964 
[Epoch 27/2000][Batch 11/48][Autoencoder loss: 349.854065][C loss: 0.718239][M loss: 0.693278][D loss: 15966.121094][G loss 3657.859619 ]time: 0:20:06.671777 
[Epoch 27/2000][Batch 12/48][Autoencoder loss: 217.450470][C loss: 0.967734][M loss: 0.693278][D loss: 13815.193359][G loss 2596.837402 ]time: 0:20:07.147597 
[Epoch 27/2000][Batch 13/48][Autoencoder loss: 357.730591][C loss: 0.871729][M loss: 0.693278][D loss: 13557.751953][G loss 1240.923828 ]time: 0:20:07.612223 
[Epoch 27/2000][Batch 14/48][Autoencoder loss: 320.401428][C loss: 0.680719][M loss: 0.693278][D loss: 12024.684570][G loss -154.792053 ]time: 0:20:08.063837 
[Epoch 27/2000][Batch 15/48][Autoencoder loss: 199.958282][C loss: 0.648803][M loss: 0.693277][D loss: 11471.142578][G loss -1445.521606 ]time: 0:20:08.522147 
[Epoch 27/2000][Batch 16/48][Autoencoder loss: 401.749969][C loss: 0.551474][M loss: 0.693278][D loss: 10139.609375][G loss -2477.636963 ]time: 0:20:08.971223 
[Epoch 27/2000][Batch 17/48][Autoencoder loss: 434.039886][C loss: 0.538894][M loss: 0.693279][D loss: 8746.992188][G loss -3552.168701 ]time: 0:20:09.416099 
[Epoch 27/2000][Batch 18/48][Autoencoder loss: 189.996552][C loss: 0.529574][M loss: 0.693277][D loss: 7708.508789][G loss -4575.794922 ]time: 0:20:09.868865 
[Epoch 27/2000][Batch 19/48][Autoencoder loss: 160.136948][C loss: 0.516121][M loss: 0.693277][D loss: 6524.046875][G loss -5536.654297 ]time: 0:20:10.316417 
[Epoch 27/2000][Batch 20/48][Autoencoder loss: 2366.011230][C loss: 0.820124][M loss: 0.694264][D loss: 6205.045410][G loss -6661.452148 ]time: 0:20:10.765633 
[Epoch 27/2000][Batch 21/48][Autoencoder loss: 5134.495605][C loss: 0.351881][M loss: 0.693305][D loss: 5888.054688][G loss -7486.295410 ]time: 0:20:11.197538 
[Epoch 27/2000][Batch 22/48][Autoencoder loss: 3651.374023][C loss: 0.036275][M loss: 0.693354][D loss: 5355.927734][G loss -8493.833984 ]time: 0:20:11.690185 
[Epoch 27/2000][Batch 23/48][Autoencoder loss: 3484.211914][C loss: 0.029354][M loss: 0.693350][D loss: 4734.514648][G loss -9403.212891 ]time: 0:20:12.130997 
[Epoch 27/2000][Batch 24/48][Autoencoder loss: 3768.459473][C loss: 0.684331][M loss: 0.694413][D loss: 2453.764404][G loss -10599.939453 ]time: 0:20:12.753334 
[Epoch 27/2000][Batch 25/48][Autoencoder loss: 1471.995728][C loss: 0.572044][M loss: 0.693711][D loss: 1723.447021][G loss -11400.439453 ]time: 0:20:13.824276 
[Epoch 27/2000][Batch 26/48][Autoencoder loss: 3121.649902][C loss: 0.488452][M loss: 0.693417][D loss: 1203.050049][G loss -12200.194336 ]time: 0:20:15.272120 
[Epoch 27/2000][Batch 27/48][Autoencoder loss: 4076.721191][C loss: 0.465181][M loss: 0.693354][D loss: 19.996094][G loss -13302.234375 ]time: 0:20:16.577151 
[Epoch 27/2000][Batch 28/48][Autoencoder loss: 2609.821533][C loss: 1.291074][M loss: 0.693395][D loss: 218.977051][G loss -15411.776367 ]time: 0:20:17.235858 
[Epoch 27/2000][Batch 29/48][Autoencoder loss: 1899.022583][C loss: 1.195471][M loss: 0.693395][D loss: -372.435547][G loss -16271.703125 ]time: 0:20:17.705799 
[Epoch 27/2000][Batch 30/48][Autoencoder loss: 3449.247314][C loss: 1.208482][M loss: 0.693340][D loss: -958.013672][G loss -17478.169922 ]time: 0:20:18.185473 
[Epoch 27/2000][Batch 31/48][Autoencoder loss: 1764.858643][C loss: 1.013313][M loss: 0.693329][D loss: -1432.673584][G loss -17839.232422 ]time: 0:20:18.637442 
[Epoch 27/2000][Batch 32/48][Autoencoder loss: 804.700500][C loss: 0.804855][M loss: 0.693345][D loss: -5409.821777][G loss -15437.251953 ]time: 0:20:19.089399 
[Epoch 27/2000][Batch 33/48][Autoencoder loss: 1402.622437][C loss: 0.707358][M loss: 0.693302][D loss: -6064.846191][G loss -15710.248047 ]time: 0:20:19.546174 
[Epoch 27/2000][Batch 34/48][Autoencoder loss: 1391.925293][C loss: 0.611505][M loss: 0.693303][D loss: -7186.096680][G loss -16671.976562 ]time: 0:20:20.001555 
[Epoch 27/2000][Batch 35/48][Autoencoder loss: 469.460968][C loss: 0.551157][M loss: 0.693305][D loss: -7643.855957][G loss -17445.308594 ]time: 0:20:20.462999 
[Epoch 27/2000][Batch 36/48][Autoencoder loss: 713.140381][C loss: 1.022069][M loss: 0.693284][D loss: -8855.236328][G loss -18372.445312 ]time: 0:20:20.916417 
[Epoch 27/2000][Batch 37/48][Autoencoder loss: 1175.228882][C loss: 0.939275][M loss: 0.693285][D loss: -10110.648438][G loss -19538.804688 ]time: 0:20:21.373382 
[Epoch 27/2000][Batch 38/48][Autoencoder loss: 680.005554][C loss: 0.886402][M loss: 0.693289][D loss: -10701.041016][G loss -20917.574219 ]time: 0:20:21.868600 
[Epoch 27/2000][Batch 39/48][Autoencoder loss: 618.739380][C loss: 0.812427][M loss: 0.693285][D loss: -10982.339844][G loss -22002.103516 ]time: 0:20:22.320447 
[Epoch 27/2000][Batch 40/48][Autoencoder loss: 1223.875732][C loss: 0.601688][M loss: 0.693298][D loss: -11259.277344][G loss -23317.087891 ]time: 0:20:22.986825 
[Epoch 27/2000][Batch 41/48][Autoencoder loss: 1087.506592][C loss: 0.559722][M loss: 0.693295][D loss: -12860.754883][G loss -24331.271484 ]time: 0:20:23.461516 
[Epoch 27/2000][Batch 42/48][Autoencoder loss: 839.437195][C loss: 0.483530][M loss: 0.693289][D loss: -14457.310547][G loss -25487.404297 ]time: 0:20:23.914886 
[Epoch 27/2000][Batch 43/48][Autoencoder loss: 888.474976][C loss: 0.423582][M loss: 0.693283][D loss: -15156.780273][G loss -26102.179688 ]time: 0:20:24.382199 
[Epoch 27/2000][Batch 44/48][Autoencoder loss: 709.376587][C loss: 1.311294][M loss: 0.693285][D loss: -17899.876953][G loss -27093.333984 ]time: 0:20:25.142630 
[Epoch 27/2000][Batch 45/48][Autoencoder loss: 291.247131][C loss: 1.243921][M loss: 0.693281][D loss: -17958.427734][G loss -27859.660156 ]time: 0:20:26.179216 
[Epoch 27/2000][Batch 46/48][Autoencoder loss: 350.224792][C loss: 1.114816][M loss: 0.693282][D loss: -18750.785156][G loss -29876.675781 ]time: 0:20:26.673790 
[Epoch 27/2000][Batch 47/48][Autoencoder loss: 501.451324][C loss: 0.941252][M loss: 0.693280][D loss: -18579.281250][G loss -30831.535156 ]time: 0:20:27.489443 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 590us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
61/63 [============================>.] - ETA: 0s
63/63 [==============================] - 0s 853us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
52/63 [=======================>......] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 27/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 28/2000][Batch 0/48][Autoencoder loss: 801.514343][C loss: 1.134698][M loss: 0.693282][D loss: -12768.321289][G loss -39791.074219 ]time: 0:20:45.529831 
[Epoch 28/2000][Batch 1/48][Autoencoder loss: 279.325531][C loss: 1.250527][M loss: 0.693281][D loss: -13981.494141][G loss -41193.792969 ]time: 0:20:45.984760 
[Epoch 28/2000][Batch 2/48][Autoencoder loss: 511.192047][C loss: 1.171412][M loss: 0.693281][D loss: -14455.272461][G loss -44556.726562 ]time: 0:20:46.508161 
[Epoch 28/2000][Batch 3/48][Autoencoder loss: 416.919830][C loss: 1.100075][M loss: 0.693281][D loss: -14279.906250][G loss -44569.886719 ]time: 0:20:47.572863 
[Epoch 28/2000][Batch 4/48][Autoencoder loss: 529.094421][C loss: 1.190554][M loss: 0.693279][D loss: -16302.765625][G loss -46603.750000 ]time: 0:20:48.282429 
[Epoch 28/2000][Batch 5/48][Autoencoder loss: 1269.227295][C loss: 1.250914][M loss: 0.693282][D loss: -16630.238281][G loss -48020.441406 ]time: 0:20:48.791821 
[Epoch 28/2000][Batch 6/48][Autoencoder loss: 1428.800659][C loss: 1.179081][M loss: 0.693278][D loss: -17812.585938][G loss -49699.535156 ]time: 0:20:49.307292 
[Epoch 28/2000][Batch 7/48][Autoencoder loss: 971.163391][C loss: 1.083318][M loss: 0.693278][D loss: -20108.158203][G loss -52531.148438 ]time: 0:20:49.753409 
[Epoch 28/2000][Batch 8/48][Autoencoder loss: 274.430389][C loss: 0.916470][M loss: 0.693278][D loss: -30384.855469][G loss -40562.785156 ]time: 0:20:50.206982 
[Epoch 28/2000][Batch 9/48][Autoencoder loss: 417.519135][C loss: 0.822054][M loss: 0.693276][D loss: -32633.496094][G loss -42572.636719 ]time: 0:20:50.658233 
[Epoch 28/2000][Batch 10/48][Autoencoder loss: 659.219971][C loss: 0.672480][M loss: 0.693277][D loss: -31472.953125][G loss -43825.707031 ]time: 0:20:51.103918 
[Epoch 28/2000][Batch 11/48][Autoencoder loss: 349.400574][C loss: 0.472099][M loss: 0.693277][D loss: -33720.093750][G loss -45210.265625 ]time: 0:20:51.549314 
[Epoch 28/2000][Batch 12/48][Autoencoder loss: 292.359558][C loss: 1.436722][M loss: 0.693278][D loss: -26822.996094][G loss -55602.542969 ]time: 0:20:52.007215 
[Epoch 28/2000][Batch 13/48][Autoencoder loss: 522.819153][C loss: 1.357570][M loss: 0.693278][D loss: -26420.240234][G loss -57658.542969 ]time: 0:20:52.464666 
[Epoch 28/2000][Batch 14/48][Autoencoder loss: 366.704712][C loss: 1.108539][M loss: 0.693276][D loss: -27701.833984][G loss -58536.582031 ]time: 0:20:52.910696 
[Epoch 28/2000][Batch 15/48][Autoencoder loss: 221.212982][C loss: 0.876615][M loss: 0.693277][D loss: -29380.494141][G loss -62223.976562 ]time: 0:20:53.353703 
[Epoch 28/2000][Batch 16/48][Autoencoder loss: 518.205383][C loss: 0.698631][M loss: 0.693277][D loss: -34948.574219][G loss -59601.390625 ]time: 0:20:54.042587 
[Epoch 28/2000][Batch 17/48][Autoencoder loss: 470.913208][C loss: 0.658592][M loss: 0.693277][D loss: -32458.218750][G loss -61454.308594 ]time: 0:20:55.134418 
[Epoch 28/2000][Batch 18/48][Autoencoder loss: 190.172394][C loss: 0.595107][M loss: 0.693276][D loss: -33415.910156][G loss -65338.378906 ]time: 0:20:56.229525 
[Epoch 28/2000][Batch 19/48][Autoencoder loss: 217.051132][C loss: 0.554878][M loss: 0.693276][D loss: -33762.820312][G loss -68779.148438 ]time: 0:20:57.030452 
[Epoch 28/2000][Batch 20/48][Autoencoder loss: 2835.364746][C loss: 1.070369][M loss: 0.696042][D loss: -32541.257812][G loss -71199.750000 ]time: 0:20:57.495100 
[Epoch 28/2000][Batch 21/48][Autoencoder loss: 5516.968750][C loss: 0.438088][M loss: 0.693287][D loss: -35999.722656][G loss -73836.968750 ]time: 0:20:57.948272 
[Epoch 28/2000][Batch 22/48][Autoencoder loss: 3504.542236][C loss: 0.196745][M loss: 0.693368][D loss: -34577.593750][G loss -76509.531250 ]time: 0:20:58.395324 
[Epoch 28/2000][Batch 23/48][Autoencoder loss: 4438.672852][C loss: 0.034288][M loss: 0.693346][D loss: -35873.316406][G loss -80926.515625 ]time: 0:20:59.147713 
[Epoch 28/2000][Batch 24/48][Autoencoder loss: 7176.923828][C loss: 0.900598][M loss: 0.694030][D loss: -32969.664062][G loss -84498.742188 ]time: 0:20:59.607447 
[Epoch 28/2000][Batch 25/48][Autoencoder loss: 1546.172119][C loss: 0.757512][M loss: 0.693726][D loss: -27455.128906][G loss -85015.734375 ]time: 0:21:00.064054 
[Epoch 28/2000][Batch 26/48][Autoencoder loss: 5301.218750][C loss: 0.611615][M loss: 0.693375][D loss: -28869.878906][G loss -87980.718750 ]time: 0:21:00.509825 
[Epoch 28/2000][Batch 27/48][Autoencoder loss: 7246.021973][C loss: 0.570905][M loss: 0.693314][D loss: -29058.750000][G loss -92136.882812 ]time: 0:21:00.968271 
[Epoch 28/2000][Batch 28/48][Autoencoder loss: 3329.363037][C loss: 1.603965][M loss: 0.693366][D loss: -25797.160156][G loss -103552.734375 ]time: 0:21:01.416715 
[Epoch 28/2000][Batch 29/48][Autoencoder loss: 2282.136475][C loss: 1.461951][M loss: 0.693393][D loss: -25941.535156][G loss -103483.445312 ]time: 0:21:01.863716 
[Epoch 28/2000][Batch 30/48][Autoencoder loss: 5706.478027][C loss: 1.444228][M loss: 0.693346][D loss: -27832.798828][G loss -109242.742188 ]time: 0:21:02.367215 
[Epoch 28/2000][Batch 31/48][Autoencoder loss: 2717.149414][C loss: 1.231745][M loss: 0.693303][D loss: -17760.605469][G loss -113033.820312 ]time: 0:21:02.841408 
[Epoch 28/2000][Batch 32/48][Autoencoder loss: 760.710938][C loss: 1.404202][M loss: 0.693281][D loss: -39769.417969][G loss -92019.796875 ]time: 0:21:03.298579 
[Epoch 28/2000][Batch 33/48][Autoencoder loss: 2508.838135][C loss: 1.313003][M loss: 0.693290][D loss: -41523.394531][G loss -94515.398438 ]time: 0:21:03.739471 
[Epoch 28/2000][Batch 34/48][Autoencoder loss: 2683.739990][C loss: 1.164503][M loss: 0.693286][D loss: -42564.886719][G loss -95731.914062 ]time: 0:21:04.198997 
[Epoch 28/2000][Batch 35/48][Autoencoder loss: 630.037354][C loss: 0.948755][M loss: 0.693287][D loss: -45480.789062][G loss -98812.484375 ]time: 0:21:04.641060 
[Epoch 28/2000][Batch 36/48][Autoencoder loss: 1065.637329][C loss: 0.734847][M loss: 0.693285][D loss: -45807.394531][G loss -104355.132812 ]time: 0:21:05.679282 
[Epoch 28/2000][Batch 37/48][Autoencoder loss: 2163.090088][C loss: 0.680633][M loss: 0.693284][D loss: -43462.765625][G loss -108681.820312 ]time: 0:21:06.256789 
[Epoch 28/2000][Batch 38/48][Autoencoder loss: 921.074585][C loss: 0.647788][M loss: 0.693280][D loss: -37679.214844][G loss -112980.007812 ]time: 0:21:06.698509 
[Epoch 28/2000][Batch 39/48][Autoencoder loss: 586.793152][C loss: 0.628168][M loss: 0.693284][D loss: -35773.484375][G loss -116742.968750 ]time: 0:21:07.226670 
[Epoch 28/2000][Batch 40/48][Autoencoder loss: 1824.713745][C loss: 0.709476][M loss: 0.693299][D loss: -30746.732422][G loss -120053.015625 ]time: 0:21:08.331167 
[Epoch 28/2000][Batch 41/48][Autoencoder loss: 1581.630981][C loss: 0.705811][M loss: 0.693295][D loss: -32421.548828][G loss -124119.468750 ]time: 0:21:09.550711 
[Epoch 28/2000][Batch 42/48][Autoencoder loss: 785.079224][C loss: 0.654229][M loss: 0.693287][D loss: -35720.554688][G loss -127575.445312 ]time: 0:21:10.056047 
[Epoch 28/2000][Batch 43/48][Autoencoder loss: 1172.728638][C loss: 0.582631][M loss: 0.693285][D loss: -32840.906250][G loss -130845.156250 ]time: 0:21:10.505793 
[Epoch 28/2000][Batch 44/48][Autoencoder loss: 1287.181152][C loss: 1.076499][M loss: 0.693281][D loss: -35755.222656][G loss -129098.164062 ]time: 0:21:10.959281 
[Epoch 28/2000][Batch 45/48][Autoencoder loss: 402.762238][C loss: 1.013830][M loss: 0.693283][D loss: -28362.677734][G loss -134002.218750 ]time: 0:21:11.409636 
[Epoch 28/2000][Batch 46/48][Autoencoder loss: 402.117523][C loss: 0.892583][M loss: 0.693280][D loss: -31226.271484][G loss -133162.156250 ]time: 0:21:11.857919 
[Epoch 28/2000][Batch 47/48][Autoencoder loss: 855.304626][C loss: 0.734415][M loss: 0.693279][D loss: -28330.884766][G loss -142052.703125 ]time: 0:21:12.320643 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
54/63 [========================>.....] - ETA: 0s
63/63 [==============================] - 0s 939us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
63/63 [==============================] - 0s 777us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
51/63 [=======================>......] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 28/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.377083]
[Epoch 29/2000][Batch 0/48][Autoencoder loss: 1246.984619][C loss: 1.113527][M loss: 0.693292][D loss: 13200.351562][G loss -188628.359375 ]time: 0:21:26.605898 
[Epoch 29/2000][Batch 1/48][Autoencoder loss: 353.123627][C loss: 1.304859][M loss: 0.693288][D loss: 16582.445312][G loss -187874.031250 ]time: 0:21:27.054623 
[Epoch 29/2000][Batch 2/48][Autoencoder loss: 931.509644][C loss: 1.217386][M loss: 0.693283][D loss: 20393.230469][G loss -189740.406250 ]time: 0:21:27.605099 
[Epoch 29/2000][Batch 3/48][Autoencoder loss: 877.901184][C loss: 1.144739][M loss: 0.693279][D loss: 26478.062500][G loss -196180.906250 ]time: 0:21:28.097841 
[Epoch 29/2000][Batch 4/48][Autoencoder loss: 544.919495][C loss: 1.106946][M loss: 0.693283][D loss: 29609.269531][G loss -191570.718750 ]time: 0:21:28.547481 
[Epoch 29/2000][Batch 5/48][Autoencoder loss: 1252.622925][C loss: 1.173799][M loss: 0.693283][D loss: 29214.968750][G loss -193055.703125 ]time: 0:21:29.009451 
[Epoch 29/2000][Batch 6/48][Autoencoder loss: 1766.823975][C loss: 1.084895][M loss: 0.693280][D loss: 32595.576172][G loss -196348.640625 ]time: 0:21:29.500698 
[Epoch 29/2000][Batch 7/48][Autoencoder loss: 1255.317017][C loss: 0.989944][M loss: 0.693279][D loss: 37409.718750][G loss -194931.890625 ]time: 0:21:29.994544 
[Epoch 29/2000][Batch 8/48][Autoencoder loss: 286.001556][C loss: 1.335384][M loss: 0.693278][D loss: -7926.347656][G loss -147277.937500 ]time: 0:21:30.440656 
[Epoch 29/2000][Batch 9/48][Autoencoder loss: 524.085999][C loss: 1.300300][M loss: 0.693277][D loss: -7655.191406][G loss -143135.234375 ]time: 0:21:30.883043 
[Epoch 29/2000][Batch 10/48][Autoencoder loss: 969.210693][C loss: 1.192268][M loss: 0.693278][D loss: -2162.119141][G loss -141478.812500 ]time: 0:21:31.327569 
[Epoch 29/2000][Batch 11/48][Autoencoder loss: 433.221497][C loss: 1.025714][M loss: 0.693278][D loss: 645.099609][G loss -140472.890625 ]time: 0:21:31.772335 
[Epoch 29/2000][Batch 12/48][Autoencoder loss: 274.324799][C loss: 0.784095][M loss: 0.693279][D loss: 33167.062500][G loss -168494.703125 ]time: 0:21:32.252787 
[Epoch 29/2000][Batch 13/48][Autoencoder loss: 697.385010][C loss: 0.754129][M loss: 0.693278][D loss: 34286.750000][G loss -164807.312500 ]time: 0:21:32.691654 
[Epoch 29/2000][Batch 14/48][Autoencoder loss: 516.404541][C loss: 0.695106][M loss: 0.693276][D loss: 34163.710938][G loss -163556.671875 ]time: 0:21:33.135199 
[Epoch 29/2000][Batch 15/48][Autoencoder loss: 203.704727][C loss: 0.659602][M loss: 0.693276][D loss: 38315.199219][G loss -160191.468750 ]time: 0:21:33.578567 
[Epoch 29/2000][Batch 16/48][Autoencoder loss: 566.273071][C loss: 0.547067][M loss: 0.693282][D loss: 27284.527344][G loss -146849.718750 ]time: 0:21:34.539250 
[Epoch 29/2000][Batch 17/48][Autoencoder loss: 589.171936][C loss: 0.540140][M loss: 0.693281][D loss: 25632.941406][G loss -147203.531250 ]time: 0:21:35.969311 
[Epoch 29/2000][Batch 18/48][Autoencoder loss: 197.948151][C loss: 0.488010][M loss: 0.693278][D loss: 31619.064453][G loss -142332.765625 ]time: 0:21:36.937141 
[Epoch 29/2000][Batch 19/48][Autoencoder loss: 190.385345][C loss: 0.443359][M loss: 0.693277][D loss: 35649.304688][G loss -140935.890625 ]time: 0:21:37.386954 
[Epoch 29/2000][Batch 20/48][Autoencoder loss: 3134.122314][C loss: 0.522681][M loss: 0.694495][D loss: 43325.933594][G loss -137984.562500 ]time: 0:21:37.841656 
[Epoch 29/2000][Batch 21/48][Autoencoder loss: 5924.509277][C loss: 0.113131][M loss: 0.693307][D loss: 38264.687500][G loss -129385.867188 ]time: 0:21:38.295318 
[Epoch 29/2000][Batch 22/48][Autoencoder loss: 3177.105225][C loss: 0.027158][M loss: 0.693377][D loss: 35599.570312][G loss -126590.953125 ]time: 0:21:38.741486 
[Epoch 29/2000][Batch 23/48][Autoencoder loss: 5237.156250][C loss: 0.122481][M loss: 0.693304][D loss: 34271.714844][G loss -120447.703125 ]time: 0:21:39.200701 
[Epoch 29/2000][Batch 24/48][Autoencoder loss: 8942.112305][C loss: 1.252682][M loss: 0.693315][D loss: 43692.445312][G loss -118986.296875 ]time: 0:21:39.656076 
[Epoch 29/2000][Batch 25/48][Autoencoder loss: 1314.919067][C loss: 0.560528][M loss: 0.693301][D loss: 47185.363281][G loss -112361.835938 ]time: 0:21:40.100776 
[Epoch 29/2000][Batch 26/48][Autoencoder loss: 5795.108398][C loss: 0.557895][M loss: 0.693289][D loss: 47088.632812][G loss -101413.421875 ]time: 0:21:40.556944 
[Epoch 29/2000][Batch 27/48][Autoencoder loss: 8747.792969][C loss: 0.501108][M loss: 0.693288][D loss: 40807.171875][G loss -95431.320312 ]time: 0:21:41.006623 
[Epoch 29/2000][Batch 28/48][Autoencoder loss: 3914.134277][C loss: 1.457577][M loss: 0.693322][D loss: 46649.503906][G loss -94420.367188 ]time: 0:21:41.551780 
[Epoch 29/2000][Batch 29/48][Autoencoder loss: 1835.560425][C loss: 1.370342][M loss: 0.693316][D loss: 42174.386719][G loss -83982.179688 ]time: 0:21:41.998711 
[Epoch 29/2000][Batch 30/48][Autoencoder loss: 6548.373047][C loss: 1.368074][M loss: 0.693303][D loss: 39170.148438][G loss -72048.781250 ]time: 0:21:42.479785 
[Epoch 29/2000][Batch 31/48][Autoencoder loss: 3821.676514][C loss: 1.131684][M loss: 0.693295][D loss: 36226.203125][G loss -60515.714844 ]time: 0:21:42.956374 
[Epoch 29/2000][Batch 32/48][Autoencoder loss: 643.572266][C loss: 1.006230][M loss: 0.693283][D loss: 19822.972656][G loss -38618.000000 ]time: 0:21:43.392414 
[Epoch 29/2000][Batch 33/48][Autoencoder loss: 2607.125488][C loss: 0.921074][M loss: 0.693287][D loss: 17507.449219][G loss -30326.511719 ]time: 0:21:43.838939 
[Epoch 29/2000][Batch 34/48][Autoencoder loss: 3694.004150][C loss: 0.836685][M loss: 0.693281][D loss: 14956.670898][G loss -21279.957031 ]time: 0:21:44.627610 
[Epoch 29/2000][Batch 35/48][Autoencoder loss: 992.245239][C loss: 0.659600][M loss: 0.693283][D loss: 13563.186523][G loss -13948.282227 ]time: 0:21:45.361532 
[Epoch 29/2000][Batch 36/48][Autoencoder loss: 713.341736][C loss: 0.725111][M loss: 0.693279][D loss: 11025.083984][G loss -6115.696777 ]time: 0:21:45.810401 
[Epoch 29/2000][Batch 37/48][Autoencoder loss: 2616.124512][C loss: 0.741880][M loss: 0.693280][D loss: 9115.643555][G loss 1404.994995 ]time: 0:21:46.264796 
[Epoch 29/2000][Batch 38/48][Autoencoder loss: 1635.741455][C loss: 0.738453][M loss: 0.693281][D loss: 7589.283691][G loss 9025.969727 ]time: 0:21:46.709030 
[Epoch 29/2000][Batch 39/48][Autoencoder loss: 452.772675][C loss: 0.711939][M loss: 0.693280][D loss: 5658.983398][G loss 15938.454102 ]time: 0:21:47.158850 
[Epoch 29/2000][Batch 40/48][Autoencoder loss: 1840.159424][C loss: 0.821542][M loss: 0.693292][D loss: 3659.941406][G loss 23675.478516 ]time: 0:21:47.612782 
[Epoch 29/2000][Batch 41/48][Autoencoder loss: 2180.345703][C loss: 0.809801][M loss: 0.693284][D loss: 1231.069336][G loss 30645.628906 ]time: 0:21:48.778401 
[Epoch 29/2000][Batch 42/48][Autoencoder loss: 966.573242][C loss: 0.748151][M loss: 0.693285][D loss: -12.720703][G loss 37714.820312 ]time: 0:21:49.412996 
[Epoch 29/2000][Batch 43/48][Autoencoder loss: 828.288025][C loss: 0.673501][M loss: 0.693278][D loss: -3171.259766][G loss 44279.171875 ]time: 0:21:49.862050 
[Epoch 29/2000][Batch 44/48][Autoencoder loss: 1536.756226][C loss: 1.367458][M loss: 0.693278][D loss: -2572.510742][G loss 49219.414062 ]time: 0:21:50.335865 
[Epoch 29/2000][Batch 45/48][Autoencoder loss: 750.219055][C loss: 1.247887][M loss: 0.693278][D loss: -4914.629395][G loss 53611.632812 ]time: 0:21:50.792257 
[Epoch 29/2000][Batch 46/48][Autoencoder loss: 183.066101][C loss: 1.054350][M loss: 0.693279][D loss: -8227.292969][G loss 59513.203125 ]time: 0:21:51.239052 
[Epoch 29/2000][Batch 47/48][Autoencoder loss: 861.371765][C loss: 0.875276][M loss: 0.693277][D loss: -10453.181641][G loss 66850.570312 ]time: 0:21:51.737263 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 429us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 480us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 513us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 29/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 30/2000][Batch 0/48][Autoencoder loss: 1813.599854][C loss: 1.064068][M loss: 0.693289][D loss: -33835.339844][G loss 95090.617188 ]time: 0:22:07.175656 
[Epoch 30/2000][Batch 1/48][Autoencoder loss: 295.395935][C loss: 1.286289][M loss: 0.693280][D loss: -36294.519531][G loss 107952.367188 ]time: 0:22:07.617332 
[Epoch 30/2000][Batch 2/48][Autoencoder loss: 623.919983][C loss: 1.294846][M loss: 0.693280][D loss: -38717.421875][G loss 116533.398438 ]time: 0:22:08.072800 
[Epoch 30/2000][Batch 3/48][Autoencoder loss: 1180.802124][C loss: 1.250273][M loss: 0.693279][D loss: -43606.199219][G loss 126329.500000 ]time: 0:22:08.519372 
[Epoch 30/2000][Batch 4/48][Autoencoder loss: 996.645691][C loss: 1.094604][M loss: 0.693279][D loss: -46556.855469][G loss 134884.421875 ]time: 0:22:09.028167 
[Epoch 30/2000][Batch 5/48][Autoencoder loss: 1035.673950][C loss: 1.129738][M loss: 0.693279][D loss: -48231.718750][G loss 143200.078125 ]time: 0:22:09.470312 
[Epoch 30/2000][Batch 6/48][Autoencoder loss: 1710.692627][C loss: 1.058220][M loss: 0.693277][D loss: -53263.605469][G loss 157300.468750 ]time: 0:22:09.963770 
[Epoch 30/2000][Batch 7/48][Autoencoder loss: 1674.666504][C loss: 0.985220][M loss: 0.693278][D loss: -59380.062500][G loss 169889.203125 ]time: 0:22:10.411320 
[Epoch 30/2000][Batch 8/48][Autoencoder loss: 627.459961][C loss: 0.966448][M loss: 0.693280][D loss: -18248.035156][G loss 139361.828125 ]time: 0:22:10.853520 
[Epoch 30/2000][Batch 9/48][Autoencoder loss: 411.719879][C loss: 0.946579][M loss: 0.693278][D loss: -19953.347656][G loss 144442.234375 ]time: 0:22:11.316792 
[Epoch 30/2000][Batch 10/48][Autoencoder loss: 1448.832520][C loss: 0.874461][M loss: 0.693277][D loss: -23915.476562][G loss 146875.281250 ]time: 0:22:11.976383 
[Epoch 30/2000][Batch 11/48][Autoencoder loss: 951.495422][C loss: 0.762259][M loss: 0.693277][D loss: -21891.128906][G loss 155378.171875 ]time: 0:22:13.187778 
[Epoch 30/2000][Batch 12/48][Autoencoder loss: 213.651367][C loss: 0.878348][M loss: 0.693278][D loss: -53692.644531][G loss 189736.781250 ]time: 0:22:13.763892 
[Epoch 30/2000][Batch 13/48][Autoencoder loss: 800.500122][C loss: 0.844636][M loss: 0.693278][D loss: -56719.875000][G loss 207408.093750 ]time: 0:22:14.431398 
[Epoch 30/2000][Batch 14/48][Autoencoder loss: 931.802185][C loss: 0.769629][M loss: 0.693277][D loss: -61552.015625][G loss 211402.406250 ]time: 0:22:15.519266 
[Epoch 30/2000][Batch 15/48][Autoencoder loss: 301.831573][C loss: 0.705627][M loss: 0.693277][D loss: -62935.453125][G loss 223897.843750 ]time: 0:22:16.539883 
[Epoch 30/2000][Batch 16/48][Autoencoder loss: 489.447449][C loss: 0.520781][M loss: 0.693278][D loss: -45230.902344][G loss 211910.687500 ]time: 0:22:17.157334 
[Epoch 30/2000][Batch 17/48][Autoencoder loss: 822.179688][C loss: 0.526201][M loss: 0.693277][D loss: -49676.128906][G loss 217473.687500 ]time: 0:22:17.619375 
[Epoch 30/2000][Batch 18/48][Autoencoder loss: 481.812775][C loss: 0.472989][M loss: 0.693276][D loss: -48117.375000][G loss 221904.328125 ]time: 0:22:18.402015 
[Epoch 30/2000][Batch 19/48][Autoencoder loss: 147.280624][C loss: 0.433232][M loss: 0.693277][D loss: -52716.136719][G loss 234808.562500 ]time: 0:22:18.863548 
[Epoch 30/2000][Batch 20/48][Autoencoder loss: 3252.915527][C loss: 0.511718][M loss: 0.693590][D loss: -62303.511719][G loss 249629.593750 ]time: 0:22:19.322010 
[Epoch 30/2000][Batch 21/48][Autoencoder loss: 6647.598145][C loss: 0.127591][M loss: 0.693285][D loss: -41849.757812][G loss 257388.437500 ]time: 0:22:19.771272 
[Epoch 30/2000][Batch 22/48][Autoencoder loss: 2718.921631][C loss: 0.160584][M loss: 0.693430][D loss: -39696.539062][G loss 253067.062500 ]time: 0:22:20.229282 
[Epoch 30/2000][Batch 23/48][Autoencoder loss: 4956.010254][C loss: 0.136277][M loss: 0.693290][D loss: -37471.593750][G loss 252542.640625 ]time: 0:22:20.733664 
[Epoch 30/2000][Batch 24/48][Autoencoder loss: 10801.331055][C loss: 0.725387][M loss: 0.693292][D loss: -49738.953125][G loss 271602.156250 ]time: 0:22:21.177894 
[Epoch 30/2000][Batch 25/48][Autoencoder loss: 1589.226562][C loss: 0.524561][M loss: 0.693291][D loss: -41658.101562][G loss 264375.312500 ]time: 0:22:21.630153 
[Epoch 30/2000][Batch 26/48][Autoencoder loss: 5004.875977][C loss: 0.528765][M loss: 0.693288][D loss: -44737.863281][G loss 268842.687500 ]time: 0:22:22.089331 
[Epoch 30/2000][Batch 27/48][Autoencoder loss: 9500.095703][C loss: 0.422036][M loss: 0.693284][D loss: -35723.695312][G loss 263051.125000 ]time: 0:22:23.236846 
[Epoch 30/2000][Batch 28/48][Autoencoder loss: 5335.048828][C loss: 1.587157][M loss: 0.693284][D loss: -54006.585938][G loss 284499.125000 ]time: 0:22:24.161174 
[Epoch 30/2000][Batch 29/48][Autoencoder loss: 1507.570923][C loss: 1.537414][M loss: 0.693290][D loss: -39284.664062][G loss 283635.437500 ]time: 0:22:24.611718 
[Epoch 30/2000][Batch 30/48][Autoencoder loss: 5745.257324][C loss: 1.497283][M loss: 0.693294][D loss: -28056.937500][G loss 283971.156250 ]time: 0:22:25.058849 
[Epoch 30/2000][Batch 31/48][Autoencoder loss: 5223.176758][C loss: 1.259982][M loss: 0.693286][D loss: -23725.007812][G loss 276515.625000 ]time: 0:22:25.507715 
[Epoch 30/2000][Batch 32/48][Autoencoder loss: 976.121094][C loss: 1.097976][M loss: 0.693283][D loss: 34311.343750][G loss 212705.968750 ]time: 0:22:25.963523 
[Epoch 30/2000][Batch 33/48][Autoencoder loss: 1670.687988][C loss: 0.956553][M loss: 0.693282][D loss: 39210.121094][G loss 203401.031250 ]time: 0:22:26.408840 
[Epoch 30/2000][Batch 34/48][Autoencoder loss: 4027.507812][C loss: 0.854389][M loss: 0.693285][D loss: 49714.148438][G loss 197132.656250 ]time: 0:22:26.868270 
[Epoch 30/2000][Batch 35/48][Autoencoder loss: 2114.042480][C loss: 0.722712][M loss: 0.693281][D loss: 55279.773438][G loss 186403.906250 ]time: 0:22:27.314841 
[Epoch 30/2000][Batch 36/48][Autoencoder loss: 337.945374][C loss: 0.740509][M loss: 0.693277][D loss: 63967.742188][G loss 181841.000000 ]time: 0:22:27.769442 
[Epoch 30/2000][Batch 37/48][Autoencoder loss: 2062.414062][C loss: 0.702301][M loss: 0.693279][D loss: 57060.578125][G loss 173524.671875 ]time: 0:22:28.437719 
[Epoch 30/2000][Batch 38/48][Autoencoder loss: 2624.600342][C loss: 0.616916][M loss: 0.693280][D loss: 56365.539062][G loss 160038.000000 ]time: 0:22:29.417853 
[Epoch 30/2000][Batch 39/48][Autoencoder loss: 711.152527][C loss: 0.532176][M loss: 0.693277][D loss: 52032.367188][G loss 154060.562500 ]time: 0:22:30.519126 
[Epoch 30/2000][Batch 40/48][Autoencoder loss: 1061.401123][C loss: 0.743171][M loss: 0.693284][D loss: 43636.507812][G loss 147113.093750 ]time: 0:22:31.100234 
[Epoch 30/2000][Batch 41/48][Autoencoder loss: 2365.338623][C loss: 0.778448][M loss: 0.693279][D loss: 55497.234375][G loss 137861.265625 ]time: 0:22:31.555512 
[Epoch 30/2000][Batch 42/48][Autoencoder loss: 1792.279175][C loss: 0.740178][M loss: 0.693279][D loss: 58434.410156][G loss 130983.578125 ]time: 0:22:32.023894 
[Epoch 30/2000][Batch 43/48][Autoencoder loss: 505.829193][C loss: 0.690433][M loss: 0.693279][D loss: 58851.406250][G loss 122263.898438 ]time: 0:22:32.803442 
[Epoch 30/2000][Batch 44/48][Autoencoder loss: 1112.287598][C loss: 0.888701][M loss: 0.693277][D loss: 55738.296875][G loss 112155.632812 ]time: 0:22:33.881213 
[Epoch 30/2000][Batch 45/48][Autoencoder loss: 1401.382080][C loss: 0.834604][M loss: 0.693277][D loss: 54629.886719][G loss 104637.281250 ]time: 0:22:34.548594 
[Epoch 30/2000][Batch 46/48][Autoencoder loss: 357.458008][C loss: 0.718007][M loss: 0.693278][D loss: 48573.605469][G loss 94637.890625 ]time: 0:22:35.004226 
[Epoch 30/2000][Batch 47/48][Autoencoder loss: 313.076324][C loss: 0.568810][M loss: 0.693277][D loss: 47295.031250][G loss 91021.632812 ]time: 0:22:35.453873 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 437us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 381us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 476us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 30/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 31/2000][Batch 0/48][Autoencoder loss: 1758.097534][C loss: 1.029557][M loss: 0.693313][D loss: 26353.699219][G loss 105410.695312 ]time: 0:22:50.635895 
[Epoch 31/2000][Batch 1/48][Autoencoder loss: 728.680603][C loss: 1.210083][M loss: 0.693285][D loss: 24388.199219][G loss 99605.406250 ]time: 0:22:51.126580 
[Epoch 31/2000][Batch 2/48][Autoencoder loss: 158.258438][C loss: 1.192361][M loss: 0.693279][D loss: 26977.035156][G loss 92297.117188 ]time: 0:22:51.888794 
[Epoch 31/2000][Batch 3/48][Autoencoder loss: 736.501953][C loss: 1.166514][M loss: 0.693277][D loss: 26491.898438][G loss 85128.835938 ]time: 0:22:52.914099 
[Epoch 31/2000][Batch 4/48][Autoencoder loss: 1479.906128][C loss: 1.086828][M loss: 0.693288][D loss: 26132.716797][G loss 78856.351562 ]time: 0:22:53.354483 
[Epoch 31/2000][Batch 5/48][Autoencoder loss: 1279.163330][C loss: 1.189942][M loss: 0.693279][D loss: 22633.984375][G loss 73653.390625 ]time: 0:22:53.800369 
[Epoch 31/2000][Batch 6/48][Autoencoder loss: 1106.786743][C loss: 1.078056][M loss: 0.693277][D loss: 20793.312500][G loss 66083.078125 ]time: 0:22:54.262950 
[Epoch 31/2000][Batch 7/48][Autoencoder loss: 1664.982056][C loss: 0.925179][M loss: 0.693277][D loss: 18799.841797][G loss 62023.437500 ]time: 0:22:54.703883 
[Epoch 31/2000][Batch 8/48][Autoencoder loss: 1167.790161][C loss: 1.086217][M loss: 0.693277][D loss: 33259.320312][G loss 44801.710938 ]time: 0:22:55.158788 
[Epoch 31/2000][Batch 9/48][Autoencoder loss: 222.565933][C loss: 1.063864][M loss: 0.693277][D loss: 31661.789062][G loss 41951.285156 ]time: 0:22:55.653784 
[Epoch 31/2000][Batch 10/48][Autoencoder loss: 851.859619][C loss: 0.998022][M loss: 0.693277][D loss: 29414.527344][G loss 36840.734375 ]time: 0:22:56.733349 
[Epoch 31/2000][Batch 11/48][Autoencoder loss: 1315.738647][C loss: 0.822230][M loss: 0.693276][D loss: 26088.947266][G loss 34158.152344 ]time: 0:22:57.804845 
[Epoch 31/2000][Batch 12/48][Autoencoder loss: 453.594910][C loss: 1.411023][M loss: 0.693276][D loss: 18359.718750][G loss 36803.117188 ]time: 0:22:58.252863 
[Epoch 31/2000][Batch 13/48][Autoencoder loss: 320.604401][C loss: 1.099802][M loss: 0.693276][D loss: 15591.426758][G loss 32675.478516 ]time: 0:22:58.706499 
[Epoch 31/2000][Batch 14/48][Autoencoder loss: 850.270691][C loss: 0.711218][M loss: 0.693277][D loss: 14611.722656][G loss 30178.095703 ]time: 0:22:59.160322 
[Epoch 31/2000][Batch 15/48][Autoencoder loss: 679.860962][C loss: 0.632070][M loss: 0.693277][D loss: 13114.687500][G loss 27245.191406 ]time: 0:22:59.645006 
[Epoch 31/2000][Batch 16/48][Autoencoder loss: 337.823792][C loss: 0.594721][M loss: 0.693278][D loss: 13800.787109][G loss 23579.482422 ]time: 0:23:00.095309 
[Epoch 31/2000][Batch 17/48][Autoencoder loss: 498.776245][C loss: 0.603570][M loss: 0.693277][D loss: 12042.288086][G loss 20875.375000 ]time: 0:23:00.546972 
[Epoch 31/2000][Batch 18/48][Autoencoder loss: 687.092041][C loss: 0.538888][M loss: 0.693276][D loss: 9945.670898][G loss 18788.416016 ]time: 0:23:00.993514 
[Epoch 31/2000][Batch 19/48][Autoencoder loss: 315.585815][C loss: 0.455977][M loss: 0.693276][D loss: 8348.623047][G loss 15953.135742 ]time: 0:23:02.035837 
[Epoch 31/2000][Batch 20/48][Autoencoder loss: 1749.223267][C loss: 0.955507][M loss: 0.693361][D loss: 6446.488770][G loss 14111.595703 ]time: 0:23:03.145061 
[Epoch 31/2000][Batch 21/48][Autoencoder loss: 4240.338867][C loss: 0.626247][M loss: 0.693406][D loss: 6193.041016][G loss 12193.212891 ]time: 0:23:03.598524 
[Epoch 31/2000][Batch 22/48][Autoencoder loss: 1939.837280][C loss: 0.005930][M loss: 0.701076][D loss: 5462.066406][G loss 10425.764648 ]time: 0:23:04.043676 
[Epoch 31/2000][Batch 23/48][Autoencoder loss: 2678.133301][C loss: 0.327928][M loss: 0.695282][D loss: 3868.496094][G loss 8449.043945 ]time: 0:23:04.499626 
[Epoch 31/2000][Batch 24/48][Autoencoder loss: 3069.190674][C loss: 2.719630][M loss: 0.693711][D loss: -145.343506][G loss 6607.798828 ]time: 0:23:04.984696 
[Epoch 31/2000][Batch 25/48][Autoencoder loss: 1419.039185][C loss: 1.822691][M loss: 0.693658][D loss: -1267.481689][G loss 4937.443359 ]time: 0:23:05.438557 
[Epoch 31/2000][Batch 26/48][Autoencoder loss: 1957.506714][C loss: 1.343482][M loss: 0.693544][D loss: -2544.564453][G loss 3310.953125 ]time: 0:23:05.889438 
[Epoch 31/2000][Batch 27/48][Autoencoder loss: 2771.311523][C loss: 1.135667][M loss: 0.693482][D loss: -3715.715820][G loss 1645.647095 ]time: 0:23:06.344459 
[Epoch 31/2000][Batch 28/48][Autoencoder loss: 1995.719482][C loss: 1.981789][M loss: 0.693807][D loss: -5241.136719][G loss 94.510239 ]time: 0:23:06.794822 
[Epoch 31/2000][Batch 29/48][Autoencoder loss: 1291.646606][C loss: 1.884154][M loss: 0.693832][D loss: -6023.581055][G loss -1571.857666 ]time: 0:23:07.238251 
[Epoch 31/2000][Batch 30/48][Autoencoder loss: 2158.059082][C loss: 1.586778][M loss: 0.693575][D loss: -7152.615234][G loss -3191.538086 ]time: 0:23:07.691645 
[Epoch 31/2000][Batch 31/48][Autoencoder loss: 1626.865723][C loss: 1.360723][M loss: 0.693356][D loss: -8185.600586][G loss -4698.408691 ]time: 0:23:08.152159 
[Epoch 31/2000][Batch 32/48][Autoencoder loss: 812.279480][C loss: 0.775611][M loss: 0.693321][D loss: -9916.725586][G loss -4975.588867 ]time: 0:23:08.592778 
[Epoch 31/2000][Batch 33/48][Autoencoder loss: 876.628784][C loss: 0.690424][M loss: 0.693300][D loss: -10838.265625][G loss -5956.617676 ]time: 0:23:09.149402 
[Epoch 31/2000][Batch 34/48][Autoencoder loss: 1285.603882][C loss: 0.676069][M loss: 0.693313][D loss: -12209.601562][G loss -7518.927246 ]time: 0:23:10.154095 
[Epoch 31/2000][Batch 35/48][Autoencoder loss: 911.249329][C loss: 0.619695][M loss: 0.693308][D loss: -13623.051758][G loss -8788.604492 ]time: 0:23:10.861694 
[Epoch 31/2000][Batch 36/48][Autoencoder loss: 521.975281][C loss: 1.700374][M loss: 0.693303][D loss: -14858.990234][G loss -10321.991211 ]time: 0:23:11.701084 
[Epoch 31/2000][Batch 37/48][Autoencoder loss: 790.840393][C loss: 1.374232][M loss: 0.693296][D loss: -16313.855469][G loss -11770.014648 ]time: 0:23:12.783962 
[Epoch 31/2000][Batch 38/48][Autoencoder loss: 887.058228][C loss: 1.139179][M loss: 0.693291][D loss: -17500.261719][G loss -13000.512695 ]time: 0:23:13.452681 
[Epoch 31/2000][Batch 39/48][Autoencoder loss: 553.257568][C loss: 0.957978][M loss: 0.693288][D loss: -18105.162109][G loss -14415.393555 ]time: 0:23:13.906414 
[Epoch 31/2000][Batch 40/48][Autoencoder loss: 709.493347][C loss: 0.853395][M loss: 0.693359][D loss: -18735.621094][G loss -16559.150391 ]time: 0:23:14.354498 
[Epoch 31/2000][Batch 41/48][Autoencoder loss: 954.737244][C loss: 0.823688][M loss: 0.693357][D loss: -20000.416016][G loss -17775.292969 ]time: 0:23:14.796114 
[Epoch 31/2000][Batch 42/48][Autoencoder loss: 830.238159][C loss: 0.782505][M loss: 0.693323][D loss: -22283.558594][G loss -19586.003906 ]time: 0:23:15.244457 
[Epoch 31/2000][Batch 43/48][Autoencoder loss: 383.462830][C loss: 0.757657][M loss: 0.693319][D loss: -22161.996094][G loss -22040.728516 ]time: 0:23:15.941747 
[Epoch 31/2000][Batch 44/48][Autoencoder loss: 416.115051][C loss: 1.628411][M loss: 0.693285][D loss: -25455.839844][G loss -24194.605469 ]time: 0:23:16.430431 
[Epoch 31/2000][Batch 45/48][Autoencoder loss: 488.471008][C loss: 1.540336][M loss: 0.693287][D loss: -26062.517578][G loss -27234.974609 ]time: 0:23:16.875654 
[Epoch 31/2000][Batch 46/48][Autoencoder loss: 290.909454][C loss: 1.357929][M loss: 0.693295][D loss: -28195.765625][G loss -28860.742188 ]time: 0:23:17.321719 
[Epoch 31/2000][Batch 47/48][Autoencoder loss: 274.777802][C loss: 1.108320][M loss: 0.693285][D loss: -30728.882812][G loss -32047.976562 ]time: 0:23:17.786287 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 438us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 393us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 406us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 31/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 32/2000][Batch 0/48][Autoencoder loss: 810.035461][C loss: 1.348344][M loss: 0.693290][D loss: -25170.671875][G loss -43874.050781 ]time: 0:23:33.159297 
[Epoch 32/2000][Batch 1/48][Autoencoder loss: 382.022797][C loss: 1.454548][M loss: 0.693283][D loss: -26469.175781][G loss -46204.269531 ]time: 0:23:33.610717 
[Epoch 32/2000][Batch 2/48][Autoencoder loss: 177.014847][C loss: 1.395136][M loss: 0.693283][D loss: -28855.042969][G loss -52620.546875 ]time: 0:23:34.072503 
[Epoch 32/2000][Batch 3/48][Autoencoder loss: 288.207153][C loss: 1.354118][M loss: 0.693288][D loss: -30221.554688][G loss -53487.808594 ]time: 0:23:34.552978 
[Epoch 32/2000][Batch 4/48][Autoencoder loss: 674.268677][C loss: 1.627070][M loss: 0.693285][D loss: -33940.914062][G loss -57267.464844 ]time: 0:23:35.001770 
[Epoch 32/2000][Batch 5/48][Autoencoder loss: 1029.177368][C loss: 1.672778][M loss: 0.693326][D loss: -34831.761719][G loss -61883.527344 ]time: 0:23:35.439592 
[Epoch 32/2000][Batch 6/48][Autoencoder loss: 915.888123][C loss: 1.514475][M loss: 0.693292][D loss: -34765.554688][G loss -66117.460938 ]time: 0:23:35.885788 
[Epoch 32/2000][Batch 7/48][Autoencoder loss: 879.326904][C loss: 1.329279][M loss: 0.693285][D loss: -38046.058594][G loss -67381.101562 ]time: 0:23:36.326899 
[Epoch 32/2000][Batch 8/48][Autoencoder loss: 470.416595][C loss: 1.395356][M loss: 0.693285][D loss: -52925.343750][G loss -57422.011719 ]time: 0:23:36.777261 
[Epoch 32/2000][Batch 9/48][Autoencoder loss: 223.624786][C loss: 1.349319][M loss: 0.693279][D loss: -53909.492188][G loss -58621.132812 ]time: 0:23:37.721029 
[Epoch 32/2000][Batch 10/48][Autoencoder loss: 510.481628][C loss: 1.255454][M loss: 0.693279][D loss: -56529.007812][G loss -61840.921875 ]time: 0:23:38.809106 
[Epoch 32/2000][Batch 11/48][Autoencoder loss: 611.416687][C loss: 1.115307][M loss: 0.693280][D loss: -57077.992188][G loss -63007.410156 ]time: 0:23:39.431954 
[Epoch 32/2000][Batch 12/48][Autoencoder loss: 288.906433][C loss: 1.142551][M loss: 0.693281][D loss: -49328.050781][G loss -77787.101562 ]time: 0:23:39.878609 
[Epoch 32/2000][Batch 13/48][Autoencoder loss: 302.180725][C loss: 1.105960][M loss: 0.693283][D loss: -47251.468750][G loss -81238.335938 ]time: 0:23:40.327795 
[Epoch 32/2000][Batch 14/48][Autoencoder loss: 442.074677][C loss: 0.988536][M loss: 0.693283][D loss: -44693.664062][G loss -85161.726562 ]time: 0:23:40.871210 
[Epoch 32/2000][Batch 15/48][Autoencoder loss: 314.192444][C loss: 0.849092][M loss: 0.693290][D loss: -46808.023438][G loss -87963.976562 ]time: 0:23:41.803831 
[Epoch 32/2000][Batch 16/48][Autoencoder loss: 309.769867][C loss: 0.648821][M loss: 0.693293][D loss: -52980.617188][G loss -86780.921875 ]time: 0:23:42.394850 
[Epoch 32/2000][Batch 17/48][Autoencoder loss: 401.305328][C loss: 0.644843][M loss: 0.693292][D loss: -49161.804688][G loss -89114.429688 ]time: 0:23:42.849659 
[Epoch 32/2000][Batch 18/48][Autoencoder loss: 314.264771][C loss: 0.578542][M loss: 0.693280][D loss: -52901.945312][G loss -91568.867188 ]time: 0:23:43.308799 
[Epoch 32/2000][Batch 19/48][Autoencoder loss: 160.998215][C loss: 0.498707][M loss: 0.693278][D loss: -48340.570312][G loss -96098.351562 ]time: 0:23:43.758304 
[Epoch 32/2000][Batch 20/48][Autoencoder loss: 1436.885132][C loss: 0.634670][M loss: 0.693665][D loss: -46105.476562][G loss -103608.710938 ]time: 0:23:44.191712 
[Epoch 32/2000][Batch 21/48][Autoencoder loss: 2621.839600][C loss: 0.033833][M loss: 0.738427][D loss: -45255.218750][G loss -107638.492188 ]time: 0:23:44.626233 
[Epoch 32/2000][Batch 22/48][Autoencoder loss: 1380.888550][C loss: 0.003501][M loss: 0.725022][D loss: -44973.191406][G loss -114419.609375 ]time: 0:23:45.256812 
[Epoch 32/2000][Batch 23/48][Autoencoder loss: 1360.340942][C loss: 0.001785][M loss: 0.696901][D loss: -41623.773438][G loss -118461.085938 ]time: 0:23:45.707082 
[Epoch 32/2000][Batch 24/48][Autoencoder loss: 1735.204956][C loss: 0.706795][M loss: 0.695771][D loss: -40957.218750][G loss -124769.898438 ]time: 0:23:46.139462 
[Epoch 32/2000][Batch 25/48][Autoencoder loss: 1383.652344][C loss: 0.590696][M loss: 0.695162][D loss: -35671.187500][G loss -131521.953125 ]time: 0:23:46.573447 
[Epoch 32/2000][Batch 26/48][Autoencoder loss: 1494.861694][C loss: 0.902998][M loss: 0.697239][D loss: -40115.421875][G loss -134279.093750 ]time: 0:23:47.022945 
[Epoch 32/2000][Batch 27/48][Autoencoder loss: 1452.271118][C loss: 0.902409][M loss: 0.694731][D loss: -34768.546875][G loss -135444.000000 ]time: 0:23:47.444707 
[Epoch 32/2000][Batch 28/48][Autoencoder loss: 1221.202881][C loss: 1.700882][M loss: 0.694248][D loss: -24173.744141][G loss -154077.593750 ]time: 0:23:47.945515 
[Epoch 32/2000][Batch 29/48][Autoencoder loss: 1045.925049][C loss: 1.388716][M loss: 0.695415][D loss: -23628.839844][G loss -157217.812500 ]time: 0:23:48.378456 
[Epoch 32/2000][Batch 30/48][Autoencoder loss: 1306.866089][C loss: 1.388177][M loss: 0.694036][D loss: -14289.787109][G loss -163152.968750 ]time: 0:23:48.800025 
[Epoch 32/2000][Batch 31/48][Autoencoder loss: 670.350220][C loss: 1.059391][M loss: 0.693639][D loss: -14097.072266][G loss -169442.171875 ]time: 0:23:49.239073 
[Epoch 32/2000][Batch 32/48][Autoencoder loss: 354.904572][C loss: 1.029446][M loss: 0.694428][D loss: -48284.945312][G loss -136196.093750 ]time: 0:23:50.129310 
[Epoch 32/2000][Batch 33/48][Autoencoder loss: 447.706970][C loss: 1.052098][M loss: 0.693798][D loss: -40016.851562][G loss -142681.703125 ]time: 0:23:51.233893 
[Epoch 32/2000][Batch 34/48][Autoencoder loss: 563.760803][C loss: 1.050929][M loss: 0.693610][D loss: -42534.609375][G loss -148936.171875 ]time: 0:23:51.750169 
[Epoch 32/2000][Batch 35/48][Autoencoder loss: 379.633392][C loss: 0.920806][M loss: 0.693829][D loss: -42404.031250][G loss -147095.812500 ]time: 0:23:52.178671 
[Epoch 32/2000][Batch 36/48][Autoencoder loss: 367.623047][C loss: 0.842152][M loss: 0.693595][D loss: -36428.863281][G loss -152075.406250 ]time: 0:23:52.617067 
[Epoch 32/2000][Batch 37/48][Autoencoder loss: 451.159088][C loss: 0.772017][M loss: 0.693478][D loss: -31381.683594][G loss -163024.171875 ]time: 0:23:53.056510 
[Epoch 32/2000][Batch 38/48][Autoencoder loss: 400.632263][C loss: 0.681657][M loss: 0.693537][D loss: -30232.765625][G loss -164547.828125 ]time: 0:23:53.492625 
[Epoch 32/2000][Batch 39/48][Autoencoder loss: 350.118103][C loss: 0.602808][M loss: 0.693381][D loss: -25501.046875][G loss -175843.515625 ]time: 0:23:53.938684 
[Epoch 32/2000][Batch 40/48][Autoencoder loss: 539.237061][C loss: 0.823864][M loss: 0.693395][D loss: -18878.125000][G loss -179418.171875 ]time: 0:23:54.385442 
[Epoch 32/2000][Batch 41/48][Autoencoder loss: 581.357971][C loss: 0.825166][M loss: 0.693406][D loss: -5939.128906][G loss -182946.437500 ]time: 0:23:54.815231 
[Epoch 32/2000][Batch 42/48][Autoencoder loss: 548.920715][C loss: 0.791570][M loss: 0.693355][D loss: -8143.042969][G loss -180980.187500 ]time: 0:23:55.251001 
[Epoch 32/2000][Batch 43/48][Autoencoder loss: 363.669128][C loss: 0.753854][M loss: 0.693320][D loss: -7482.359375][G loss -189709.828125 ]time: 0:23:55.683172 
[Epoch 32/2000][Batch 44/48][Autoencoder loss: 253.344147][C loss: 1.076368][M loss: 0.693365][D loss: -3710.089844][G loss -186370.140625 ]time: 0:23:56.116573 
[Epoch 32/2000][Batch 45/48][Autoencoder loss: 176.425613][C loss: 0.913900][M loss: 0.693360][D loss: -3400.578125][G loss -191673.562500 ]time: 0:23:56.545982 
[Epoch 32/2000][Batch 46/48][Autoencoder loss: 136.746918][C loss: 0.742546][M loss: 0.693299][D loss: -62.687500][G loss -192733.562500 ]time: 0:23:57.051979 
[Epoch 32/2000][Batch 47/48][Autoencoder loss: 180.897888][C loss: 0.541990][M loss: 0.693312][D loss: 2715.226562][G loss -191783.500000 ]time: 0:23:57.521034 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 555us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 389us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 390us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 32/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 33/2000][Batch 0/48][Autoencoder loss: 547.035889][C loss: 1.289268][M loss: 0.693463][D loss: 69322.187500][G loss -252581.687500 ]time: 0:24:16.379380 
[Epoch 33/2000][Batch 1/48][Autoencoder loss: 232.669418][C loss: 1.477854][M loss: 0.693302][D loss: 74964.140625][G loss -253106.937500 ]time: 0:24:17.035621 
[Epoch 33/2000][Batch 2/48][Autoencoder loss: 218.389999][C loss: 1.451380][M loss: 0.693306][D loss: 76349.640625][G loss -255984.734375 ]time: 0:24:18.237582 
[Epoch 33/2000][Batch 3/48][Autoencoder loss: 243.219238][C loss: 1.396647][M loss: 0.693300][D loss: 76700.109375][G loss -250902.906250 ]time: 0:24:19.395488 
[Epoch 33/2000][Batch 4/48][Autoencoder loss: 469.995117][C loss: 1.321325][M loss: 0.693458][D loss: 80225.125000][G loss -240381.765625 ]time: 0:24:20.216573 
[Epoch 33/2000][Batch 5/48][Autoencoder loss: 893.830750][C loss: 1.405294][M loss: 0.693494][D loss: 76026.664062][G loss -235566.765625 ]time: 0:24:20.661041 
[Epoch 33/2000][Batch 6/48][Autoencoder loss: 895.896545][C loss: 1.322755][M loss: 0.693300][D loss: 80810.125000][G loss -232091.000000 ]time: 0:24:21.115755 
[Epoch 33/2000][Batch 7/48][Autoencoder loss: 730.013000][C loss: 1.223941][M loss: 0.693325][D loss: 88126.632812][G loss -223512.140625 ]time: 0:24:21.562248 
[Epoch 33/2000][Batch 8/48][Autoencoder loss: 343.883911][C loss: 1.175825][M loss: 0.693399][D loss: 34407.953125][G loss -164505.593750 ]time: 0:24:22.012891 
[Epoch 33/2000][Batch 9/48][Autoencoder loss: 225.690414][C loss: 1.117355][M loss: 0.693345][D loss: 31467.046875][G loss -163538.921875 ]time: 0:24:22.455042 
[Epoch 33/2000][Batch 10/48][Autoencoder loss: 536.063293][C loss: 0.995665][M loss: 0.693312][D loss: 34192.054688][G loss -158546.171875 ]time: 0:24:22.922697 
[Epoch 33/2000][Batch 11/48][Autoencoder loss: 466.088928][C loss: 0.838982][M loss: 0.693294][D loss: 34856.023438][G loss -156057.984375 ]time: 0:24:23.411810 
[Epoch 33/2000][Batch 12/48][Autoencoder loss: 217.996414][C loss: 0.775979][M loss: 0.693306][D loss: 65612.195312][G loss -174268.296875 ]time: 0:24:23.868179 
[Epoch 33/2000][Batch 13/48][Autoencoder loss: 365.412201][C loss: 0.833771][M loss: 0.693299][D loss: 66031.078125][G loss -171408.171875 ]time: 0:24:24.322014 
[Epoch 33/2000][Batch 14/48][Autoencoder loss: 398.346527][C loss: 0.844585][M loss: 0.693301][D loss: 65898.851562][G loss -160279.859375 ]time: 0:24:24.758307 
[Epoch 33/2000][Batch 15/48][Autoencoder loss: 213.260651][C loss: 0.849073][M loss: 0.693284][D loss: 61984.105469][G loss -152745.031250 ]time: 0:24:25.245096 
[Epoch 33/2000][Batch 16/48][Autoencoder loss: 345.643890][C loss: 0.650097][M loss: 0.693311][D loss: 55501.265625][G loss -136827.687500 ]time: 0:24:25.672325 
[Epoch 33/2000][Batch 17/48][Autoencoder loss: 416.297852][C loss: 0.644925][M loss: 0.693300][D loss: 52298.191406][G loss -127123.515625 ]time: 0:24:26.130434 
[Epoch 33/2000][Batch 18/48][Autoencoder loss: 227.779495][C loss: 0.553257][M loss: 0.693294][D loss: 46378.234375][G loss -120115.945312 ]time: 0:24:26.566878 
[Epoch 33/2000][Batch 19/48][Autoencoder loss: 122.630829][C loss: 0.497237][M loss: 0.693284][D loss: 47456.164062][G loss -114786.656250 ]time: 0:24:27.014501 
[Epoch 33/2000][Batch 20/48][Autoencoder loss: 1641.294434][C loss: 0.341967][M loss: 0.693723][D loss: 51382.234375][G loss -113240.945312 ]time: 0:24:27.464739 
[Epoch 33/2000][Batch 21/48][Autoencoder loss: 2515.249268][C loss: 0.252775][M loss: 0.693973][D loss: 47590.242188][G loss -100300.507812 ]time: 0:24:28.578661 
[Epoch 33/2000][Batch 22/48][Autoencoder loss: 1052.310181][C loss: 0.109447][M loss: 0.741957][D loss: 45848.039062][G loss -92562.203125 ]time: 0:24:29.154581 
[Epoch 33/2000][Batch 23/48][Autoencoder loss: 2734.218994][C loss: 0.023416][M loss: 0.696767][D loss: 42029.671875][G loss -84899.460938 ]time: 0:24:29.830438 
[Epoch 33/2000][Batch 24/48][Autoencoder loss: 2981.360596][C loss: 0.989423][M loss: 0.706977][D loss: 41806.750000][G loss -78952.343750 ]time: 0:24:30.865582 
[Epoch 33/2000][Batch 25/48][Autoencoder loss: 886.095703][C loss: 0.819885][M loss: 0.698204][D loss: 37877.785156][G loss -68169.960938 ]time: 0:24:31.831243 
[Epoch 33/2000][Batch 26/48][Autoencoder loss: 2426.598877][C loss: 0.884987][M loss: 0.694465][D loss: 31694.089844][G loss -55856.441406 ]time: 0:24:32.430566 
[Epoch 33/2000][Batch 27/48][Autoencoder loss: 2746.581299][C loss: 0.789623][M loss: 0.694337][D loss: 27840.664062][G loss -48295.050781 ]time: 0:24:32.900752 
[Epoch 33/2000][Batch 28/48][Autoencoder loss: 1037.404907][C loss: 1.701992][M loss: 0.697372][D loss: 29269.457031][G loss -43648.957031 ]time: 0:24:33.340085 
[Epoch 33/2000][Batch 29/48][Autoencoder loss: 1067.563110][C loss: 1.410140][M loss: 0.695919][D loss: 24611.476562][G loss -32707.919922 ]time: 0:24:33.773148 
[Epoch 33/2000][Batch 30/48][Autoencoder loss: 2593.436523][C loss: 1.360570][M loss: 0.694096][D loss: 20026.527344][G loss -24360.074219 ]time: 0:24:34.223686 
[Epoch 33/2000][Batch 31/48][Autoencoder loss: 1149.470581][C loss: 1.037943][M loss: 0.693722][D loss: 15391.419922][G loss -15265.430664 ]time: 0:24:34.660893 
[Epoch 33/2000][Batch 32/48][Autoencoder loss: 395.752472][C loss: 1.085458][M loss: 0.693723][D loss: 5457.255371][G loss -5573.358398 ]time: 0:24:35.085291 
[Epoch 33/2000][Batch 33/48][Autoencoder loss: 1136.282837][C loss: 1.072596][M loss: 0.693621][D loss: 2800.574463][G loss 302.739380 ]time: 0:24:35.576103 
[Epoch 33/2000][Batch 34/48][Autoencoder loss: 1295.001465][C loss: 0.997390][M loss: 0.693799][D loss: 315.366943][G loss 5599.520020 ]time: 0:24:36.025303 
[Epoch 33/2000][Batch 35/48][Autoencoder loss: 387.426147][C loss: 0.791907][M loss: 0.693796][D loss: -2296.743164][G loss 10813.370117 ]time: 0:24:36.467013 
[Epoch 33/2000][Batch 36/48][Autoencoder loss: 571.220703][C loss: 0.723867][M loss: 0.693593][D loss: -4449.613770][G loss 16450.255859 ]time: 0:24:36.940900 
[Epoch 33/2000][Batch 37/48][Autoencoder loss: 991.875549][C loss: 0.681457][M loss: 0.693552][D loss: -6351.347656][G loss 20961.162109 ]time: 0:24:37.690308 
[Epoch 33/2000][Batch 38/48][Autoencoder loss: 512.789429][C loss: 0.635149][M loss: 0.693496][D loss: -8946.656250][G loss 25523.294922 ]time: 0:24:38.170386 
[Epoch 33/2000][Batch 39/48][Autoencoder loss: 309.916107][C loss: 0.592832][M loss: 0.693463][D loss: -11767.193359][G loss 29752.896484 ]time: 0:24:38.605897 
[Epoch 33/2000][Batch 40/48][Autoencoder loss: 872.005310][C loss: 0.759448][M loss: 0.694480][D loss: -13674.700195][G loss 36208.550781 ]time: 0:24:39.044368 
[Epoch 33/2000][Batch 41/48][Autoencoder loss: 825.402283][C loss: 0.779903][M loss: 0.693847][D loss: -16557.519531][G loss 39697.226562 ]time: 0:24:39.488680 
[Epoch 33/2000][Batch 42/48][Autoencoder loss: 501.774567][C loss: 0.751340][M loss: 0.693369][D loss: -16452.861328][G loss 44958.800781 ]time: 0:24:39.925050 
[Epoch 33/2000][Batch 43/48][Autoencoder loss: 486.996307][C loss: 0.681916][M loss: 0.693303][D loss: -19289.880859][G loss 49363.066406 ]time: 0:24:40.411146 
[Epoch 33/2000][Batch 44/48][Autoencoder loss: 590.131104][C loss: 1.305795][M loss: 0.693311][D loss: -18295.472656][G loss 52156.191406 ]time: 0:24:40.886088 
[Epoch 33/2000][Batch 45/48][Autoencoder loss: 249.630920][C loss: 1.245733][M loss: 0.693307][D loss: -21030.500000][G loss 55204.171875 ]time: 0:24:41.335482 
[Epoch 33/2000][Batch 46/48][Autoencoder loss: 176.842834][C loss: 1.110966][M loss: 0.693319][D loss: -23124.332031][G loss 60868.933594 ]time: 0:24:41.844690 
[Epoch 33/2000][Batch 47/48][Autoencoder loss: 417.084564][C loss: 0.927420][M loss: 0.693332][D loss: -25376.929688][G loss 66531.656250 ]time: 0:24:42.347944 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
63/63 [==============================] - 0s 726us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
61/63 [============================>.] - ETA: 0s
63/63 [==============================] - 0s 847us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
63/63 [==============================] - 0s 572us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 33/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 34/2000][Batch 0/48][Autoencoder loss: 887.007202][C loss: 1.034915][M loss: 0.693714][D loss: -49410.761719][G loss 93940.906250 ]time: 0:25:01.639674 
[Epoch 34/2000][Batch 1/48][Autoencoder loss: 210.302475][C loss: 1.133233][M loss: 0.693517][D loss: -51883.644531][G loss 99572.367188 ]time: 0:25:02.093865 
[Epoch 34/2000][Batch 2/48][Autoencoder loss: 427.538879][C loss: 1.063584][M loss: 0.693374][D loss: -55148.500000][G loss 111732.429688 ]time: 0:25:02.554121 
[Epoch 34/2000][Batch 3/48][Autoencoder loss: 541.402527][C loss: 1.030457][M loss: 0.693314][D loss: -60765.250000][G loss 114994.867188 ]time: 0:25:03.004265 
[Epoch 34/2000][Batch 4/48][Autoencoder loss: 585.801147][C loss: 1.052475][M loss: 0.693378][D loss: -62380.390625][G loss 127374.351562 ]time: 0:25:03.462263 
[Epoch 34/2000][Batch 5/48][Autoencoder loss: 832.738525][C loss: 1.297663][M loss: 0.693406][D loss: -68653.015625][G loss 134264.015625 ]time: 0:25:04.052375 
[Epoch 34/2000][Batch 6/48][Autoencoder loss: 1109.423828][C loss: 1.159622][M loss: 0.693343][D loss: -75077.515625][G loss 147682.703125 ]time: 0:25:04.590802 
[Epoch 34/2000][Batch 7/48][Autoencoder loss: 958.305969][C loss: 1.025507][M loss: 0.693367][D loss: -75182.601562][G loss 155976.531250 ]time: 0:25:05.039595 
[Epoch 34/2000][Batch 8/48][Autoencoder loss: 470.784698][C loss: 0.977901][M loss: 0.693332][D loss: -40112.449219][G loss 124694.234375 ]time: 0:25:05.487820 
[Epoch 34/2000][Batch 9/48][Autoencoder loss: 325.610565][C loss: 0.909779][M loss: 0.693318][D loss: -47612.738281][G loss 136865.359375 ]time: 0:25:06.360041 
[Epoch 34/2000][Batch 10/48][Autoencoder loss: 1029.851318][C loss: 0.847695][M loss: 0.693313][D loss: -42617.566406][G loss 142686.859375 ]time: 0:25:07.104202 
[Epoch 34/2000][Batch 11/48][Autoencoder loss: 766.693481][C loss: 0.808232][M loss: 0.693317][D loss: -44983.542969][G loss 147653.031250 ]time: 0:25:07.555995 
[Epoch 34/2000][Batch 12/48][Autoencoder loss: 252.505112][C loss: 0.938635][M loss: 0.693287][D loss: -74818.789062][G loss 189209.828125 ]time: 0:25:08.012402 
[Epoch 34/2000][Batch 13/48][Autoencoder loss: 598.379700][C loss: 0.988389][M loss: 0.693274][D loss: -83592.390625][G loss 197684.156250 ]time: 0:25:08.471108 
[Epoch 34/2000][Batch 14/48][Autoencoder loss: 661.853760][C loss: 0.898380][M loss: 0.693269][D loss: -81895.531250][G loss 209854.890625 ]time: 0:25:08.925985 
[Epoch 34/2000][Batch 15/48][Autoencoder loss: 246.909134][C loss: 0.836612][M loss: 0.693272][D loss: -76600.406250][G loss 219069.421875 ]time: 0:25:09.436537 
[Epoch 34/2000][Batch 16/48][Autoencoder loss: 396.679871][C loss: 0.596206][M loss: 0.693296][D loss: -72161.179688][G loss 217764.437500 ]time: 0:25:10.459730 
[Epoch 34/2000][Batch 17/48][Autoencoder loss: 573.025208][C loss: 0.570147][M loss: 0.693299][D loss: -74655.062500][G loss 221289.437500 ]time: 0:25:11.391649 
[Epoch 34/2000][Batch 18/48][Autoencoder loss: 351.096649][C loss: 0.487605][M loss: 0.693278][D loss: -75368.109375][G loss 232694.437500 ]time: 0:25:11.853862 
[Epoch 34/2000][Batch 19/48][Autoencoder loss: 124.555565][C loss: 0.446794][M loss: 0.693276][D loss: -75914.562500][G loss 243762.906250 ]time: 0:25:12.308847 
[Epoch 34/2000][Batch 20/48][Autoencoder loss: 3462.331299][C loss: 0.558271][M loss: 0.694575][D loss: -86912.335938][G loss 269998.312500 ]time: 0:25:13.078977 
[Epoch 34/2000][Batch 21/48][Autoencoder loss: 6945.814453][C loss: 0.120827][M loss: 0.693527][D loss: -78945.234375][G loss 279557.156250 ]time: 0:25:13.579369 
[Epoch 34/2000][Batch 22/48][Autoencoder loss: 2632.248535][C loss: 0.040379][M loss: 0.697440][D loss: -70697.914062][G loss 278268.531250 ]time: 0:25:14.025346 
[Epoch 34/2000][Batch 23/48][Autoencoder loss: 8360.695312][C loss: 0.004648][M loss: 0.693450][D loss: -77258.312500][G loss 297658.937500 ]time: 0:25:14.583828 
[Epoch 34/2000][Batch 24/48][Autoencoder loss: 11186.258789][C loss: 1.119925][M loss: 0.693426][D loss: -90215.718750][G loss 318748.000000 ]time: 0:25:15.378642 
[Epoch 34/2000][Batch 25/48][Autoencoder loss: 922.496887][C loss: 0.757390][M loss: 0.693420][D loss: -90864.875000][G loss 315922.468750 ]time: 0:25:16.324378 
[Epoch 34/2000][Batch 26/48][Autoencoder loss: 8434.121094][C loss: 0.694698][M loss: 0.693393][D loss: -94643.648438][G loss 323252.781250 ]time: 0:25:16.761245 
[Epoch 34/2000][Batch 27/48][Autoencoder loss: 12129.339844][C loss: 0.504851][M loss: 0.693340][D loss: -82563.593750][G loss 331547.593750 ]time: 0:25:17.190490 
[Epoch 34/2000][Batch 28/48][Autoencoder loss: 4760.850098][C loss: 1.204296][M loss: 0.693333][D loss: -93114.843750][G loss 384328.406250 ]time: 0:25:17.667289 
[Epoch 34/2000][Batch 29/48][Autoencoder loss: 1787.976807][C loss: 1.184402][M loss: 0.693377][D loss: -98599.929688][G loss 386311.031250 ]time: 0:25:18.094405 
[Epoch 34/2000][Batch 30/48][Autoencoder loss: 8298.823242][C loss: 1.150509][M loss: 0.693361][D loss: -78913.664062][G loss 383978.500000 ]time: 0:25:18.526261 
[Epoch 34/2000][Batch 31/48][Autoencoder loss: 5410.339355][C loss: 0.959001][M loss: 0.693327][D loss: -74461.671875][G loss 388516.406250 ]time: 0:25:18.956310 
[Epoch 34/2000][Batch 32/48][Autoencoder loss: 400.118622][C loss: 0.700485][M loss: 0.693316][D loss: 5462.847656][G loss 300494.687500 ]time: 0:25:19.392218 
[Epoch 34/2000][Batch 33/48][Autoencoder loss: 3158.711914][C loss: 0.667133][M loss: 0.693275][D loss: 14725.113281][G loss 294942.500000 ]time: 0:25:19.831548 
[Epoch 34/2000][Batch 34/48][Autoencoder loss: 5427.196777][C loss: 0.625015][M loss: 0.693280][D loss: 25806.605469][G loss 301708.593750 ]time: 0:25:20.258875 
[Epoch 34/2000][Batch 35/48][Autoencoder loss: 1982.238037][C loss: 0.534156][M loss: 0.693275][D loss: 56405.808594][G loss 285567.156250 ]time: 0:25:20.697224 
[Epoch 34/2000][Batch 36/48][Autoencoder loss: 470.751434][C loss: 0.811493][M loss: 0.693274][D loss: 57857.578125][G loss 292762.937500 ]time: 0:25:21.128592 
[Epoch 34/2000][Batch 37/48][Autoencoder loss: 3292.138428][C loss: 0.816166][M loss: 0.693277][D loss: 63460.863281][G loss 276317.500000 ]time: 0:25:21.549193 
[Epoch 34/2000][Batch 38/48][Autoencoder loss: 2931.914062][C loss: 0.755007][M loss: 0.693275][D loss: 76506.765625][G loss 265523.343750 ]time: 0:25:22.004265 
[Epoch 34/2000][Batch 39/48][Autoencoder loss: 426.183289][C loss: 0.723086][M loss: 0.693276][D loss: 72728.046875][G loss 252591.281250 ]time: 0:25:22.840474 
[Epoch 34/2000][Batch 40/48][Autoencoder loss: 1536.004517][C loss: 0.735842][M loss: 0.693317][D loss: 73557.093750][G loss 240440.437500 ]time: 0:25:23.725594 
[Epoch 34/2000][Batch 41/48][Autoencoder loss: 2936.327148][C loss: 0.717972][M loss: 0.693323][D loss: 80449.429688][G loss 225569.531250 ]time: 0:25:24.677765 
[Epoch 34/2000][Batch 42/48][Autoencoder loss: 1667.877686][C loss: 0.654466][M loss: 0.693301][D loss: 93720.203125][G loss 209610.578125 ]time: 0:25:25.131306 
[Epoch 34/2000][Batch 43/48][Autoencoder loss: 421.796967][C loss: 0.629008][M loss: 0.693276][D loss: 95850.093750][G loss 192157.656250 ]time: 0:25:25.577843 
[Epoch 34/2000][Batch 44/48][Autoencoder loss: 1615.335571][C loss: 1.265033][M loss: 0.693273][D loss: 94544.718750][G loss 177143.000000 ]time: 0:25:26.089527 
[Epoch 34/2000][Batch 45/48][Autoencoder loss: 1610.583374][C loss: 1.193702][M loss: 0.693271][D loss: 93805.632812][G loss 165452.921875 ]time: 0:25:26.818053 
[Epoch 34/2000][Batch 46/48][Autoencoder loss: 219.505722][C loss: 1.038946][M loss: 0.693268][D loss: 95515.578125][G loss 157629.656250 ]time: 0:25:27.278789 
[Epoch 34/2000][Batch 47/48][Autoencoder loss: 549.976318][C loss: 0.843736][M loss: 0.693266][D loss: 94904.281250][G loss 144722.578125 ]time: 0:25:27.737322 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 564us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 498us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 456us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 34/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 35/2000][Batch 0/48][Autoencoder loss: 2040.162231][C loss: 0.789544][M loss: 0.693290][D loss: 58224.132812][G loss 164060.265625 ]time: 0:25:42.902750 
[Epoch 35/2000][Batch 1/48][Autoencoder loss: 843.871765][C loss: 0.913001][M loss: 0.693284][D loss: 61965.320312][G loss 156447.468750 ]time: 0:25:44.044039 
[Epoch 35/2000][Batch 2/48][Autoencoder loss: 186.913055][C loss: 0.891171][M loss: 0.693276][D loss: 64701.195312][G loss 140618.687500 ]time: 0:25:44.547305 
[Epoch 35/2000][Batch 3/48][Autoencoder loss: 956.127197][C loss: 1.002457][M loss: 0.693277][D loss: 60981.156250][G loss 128008.820312 ]time: 0:25:44.996816 
[Epoch 35/2000][Batch 4/48][Autoencoder loss: 1594.163818][C loss: 1.067181][M loss: 0.693291][D loss: 63213.062500][G loss 120380.351562 ]time: 0:25:45.457776 
[Epoch 35/2000][Batch 5/48][Autoencoder loss: 945.359741][C loss: 1.261726][M loss: 0.693352][D loss: 59554.421875][G loss 111444.031250 ]time: 0:25:45.901997 
[Epoch 35/2000][Batch 6/48][Autoencoder loss: 868.693604][C loss: 1.155038][M loss: 0.693287][D loss: 59141.859375][G loss 101793.593750 ]time: 0:25:46.344257 
[Epoch 35/2000][Batch 7/48][Autoencoder loss: 1543.828857][C loss: 1.024810][M loss: 0.693280][D loss: 60242.187500][G loss 94637.046875 ]time: 0:25:46.798621 
[Epoch 35/2000][Batch 8/48][Autoencoder loss: 1241.045288][C loss: 1.058756][M loss: 0.693266][D loss: 74840.414062][G loss 67070.726562 ]time: 0:25:47.242743 
[Epoch 35/2000][Batch 9/48][Autoencoder loss: 308.238617][C loss: 0.930435][M loss: 0.693271][D loss: 74360.742188][G loss 62724.316406 ]time: 0:25:47.697013 
[Epoch 35/2000][Batch 10/48][Autoencoder loss: 984.554565][C loss: 0.780255][M loss: 0.693272][D loss: 71859.625000][G loss 57693.269531 ]time: 0:25:48.148709 
[Epoch 35/2000][Batch 11/48][Autoencoder loss: 1407.617188][C loss: 0.645810][M loss: 0.693272][D loss: 67237.625000][G loss 53630.640625 ]time: 0:25:48.833680 
[Epoch 35/2000][Batch 12/48][Autoencoder loss: 480.291199][C loss: 0.569458][M loss: 0.693267][D loss: 53264.562500][G loss 56629.730469 ]time: 0:25:50.003110 
[Epoch 35/2000][Batch 13/48][Autoencoder loss: 354.747528][C loss: 0.672763][M loss: 0.693269][D loss: 48117.210938][G loss 52843.472656 ]time: 0:25:51.124972 
[Epoch 35/2000][Batch 14/48][Autoencoder loss: 896.054810][C loss: 0.740191][M loss: 0.693266][D loss: 47865.601562][G loss 47285.304688 ]time: 0:25:51.566373 
[Epoch 35/2000][Batch 15/48][Autoencoder loss: 701.236450][C loss: 0.794281][M loss: 0.693266][D loss: 46944.968750][G loss 44286.273438 ]time: 0:25:51.999642 
[Epoch 35/2000][Batch 16/48][Autoencoder loss: 329.473907][C loss: 0.639913][M loss: 0.693278][D loss: 46234.050781][G loss 39772.742188 ]time: 0:25:52.599901 
[Epoch 35/2000][Batch 17/48][Autoencoder loss: 482.941284][C loss: 0.659006][M loss: 0.693273][D loss: 44408.152344][G loss 36156.339844 ]time: 0:25:53.901102 
[Epoch 35/2000][Batch 18/48][Autoencoder loss: 687.591064][C loss: 0.487967][M loss: 0.693262][D loss: 40364.074219][G loss 32875.582031 ]time: 0:25:54.579142 
[Epoch 35/2000][Batch 19/48][Autoencoder loss: 312.617889][C loss: 0.405080][M loss: 0.693263][D loss: 37825.785156][G loss 30089.628906 ]time: 0:25:55.287422 
[Epoch 35/2000][Batch 20/48][Autoencoder loss: 702.732178][C loss: 0.237577][M loss: 0.693843][D loss: 33070.265625][G loss 29040.726562 ]time: 0:25:55.730998 
[Epoch 35/2000][Batch 21/48][Autoencoder loss: 1092.451538][C loss: 0.217907][M loss: 0.693368][D loss: 30633.197266][G loss 26398.458984 ]time: 0:25:56.184714 
[Epoch 35/2000][Batch 22/48][Autoencoder loss: 658.881409][C loss: 0.000134][M loss: 0.697331][D loss: 28253.662109][G loss 25254.251953 ]time: 0:25:56.642818 
[Epoch 35/2000][Batch 23/48][Autoencoder loss: 584.719666][C loss: 0.000002][M loss: 0.693644][D loss: 25295.480469][G loss 22962.876953 ]time: 0:25:57.073587 
[Epoch 35/2000][Batch 24/48][Autoencoder loss: 973.363953][C loss: 0.613322][M loss: 0.693350][D loss: 26635.234375][G loss 21715.896484 ]time: 0:25:57.558896 
[Epoch 35/2000][Batch 25/48][Autoencoder loss: 835.955261][C loss: 0.534069][M loss: 0.693377][D loss: 25266.906250][G loss 19899.162109 ]time: 0:25:57.989160 
[Epoch 35/2000][Batch 26/48][Autoencoder loss: 596.789978][C loss: 0.722683][M loss: 0.693357][D loss: 23685.679688][G loss 18220.166016 ]time: 0:25:58.432812 
[Epoch 35/2000][Batch 27/48][Autoencoder loss: 505.850739][C loss: 0.708032][M loss: 0.693319][D loss: 21816.279297][G loss 17041.929688 ]time: 0:25:58.880485 
[Epoch 35/2000][Batch 28/48][Autoencoder loss: 631.397644][C loss: 1.629611][M loss: 0.693330][D loss: 20302.810547][G loss 17016.664062 ]time: 0:25:59.331517 
[Epoch 35/2000][Batch 29/48][Autoencoder loss: 655.201965][C loss: 1.426714][M loss: 0.693345][D loss: 18132.804688][G loss 15938.049805 ]time: 0:25:59.784129 
[Epoch 35/2000][Batch 30/48][Autoencoder loss: 667.911926][C loss: 1.415104][M loss: 0.693306][D loss: 17284.703125][G loss 14334.060547 ]time: 0:26:00.239859 
[Epoch 35/2000][Batch 31/48][Autoencoder loss: 460.869934][C loss: 1.163624][M loss: 0.693324][D loss: 16761.691406][G loss 13105.552734 ]time: 0:26:00.732952 
[Epoch 35/2000][Batch 32/48][Autoencoder loss: 297.909271][C loss: 1.087909][M loss: 0.693294][D loss: 17398.529297][G loss 10064.985352 ]time: 0:26:01.190588 
[Epoch 35/2000][Batch 33/48][Autoencoder loss: 281.556610][C loss: 1.064296][M loss: 0.693268][D loss: 16161.321289][G loss 9322.779297 ]time: 0:26:01.648970 
[Epoch 35/2000][Batch 34/48][Autoencoder loss: 334.099731][C loss: 1.033797][M loss: 0.693280][D loss: 14869.480469][G loss 8649.264648 ]time: 0:26:02.531446 
[Epoch 35/2000][Batch 35/48][Autoencoder loss: 288.662323][C loss: 0.898881][M loss: 0.693269][D loss: 13977.500000][G loss 8161.575195 ]time: 0:26:03.883595 
[Epoch 35/2000][Batch 36/48][Autoencoder loss: 261.169800][C loss: 0.655012][M loss: 0.693269][D loss: 12809.698242][G loss 7215.090820 ]time: 0:26:05.013811 
[Epoch 35/2000][Batch 37/48][Autoencoder loss: 257.296997][C loss: 0.583878][M loss: 0.693275][D loss: 11101.492188][G loss 6746.672852 ]time: 0:26:05.480804 
[Epoch 35/2000][Batch 38/48][Autoencoder loss: 230.993851][C loss: 0.520913][M loss: 0.693271][D loss: 10334.820312][G loss 6208.229492 ]time: 0:26:05.938860 
[Epoch 35/2000][Batch 39/48][Autoencoder loss: 168.399063][C loss: 0.477171][M loss: 0.693268][D loss: 9179.081055][G loss 5728.351562 ]time: 0:26:06.403144 
[Epoch 35/2000][Batch 40/48][Autoencoder loss: 232.281906][C loss: 0.725508][M loss: 0.693320][D loss: 8189.195801][G loss 5280.606934 ]time: 0:26:06.890456 
[Epoch 35/2000][Batch 41/48][Autoencoder loss: 302.453827][C loss: 0.711889][M loss: 0.693289][D loss: 7351.009277][G loss 4762.403809 ]time: 0:26:07.326962 
[Epoch 35/2000][Batch 42/48][Autoencoder loss: 426.522339][C loss: 0.667214][M loss: 0.693296][D loss: 6623.929688][G loss 4447.760742 ]time: 0:26:07.768356 
[Epoch 35/2000][Batch 43/48][Autoencoder loss: 264.442230][C loss: 0.615848][M loss: 0.693264][D loss: 5536.119141][G loss 3668.257080 ]time: 0:26:08.234073 
[Epoch 35/2000][Batch 44/48][Autoencoder loss: 121.750084][C loss: 1.138713][M loss: 0.693275][D loss: 4665.648438][G loss 3263.097656 ]time: 0:26:08.954712 
[Epoch 35/2000][Batch 45/48][Autoencoder loss: 104.488480][C loss: 1.034354][M loss: 0.693285][D loss: 3804.253418][G loss 2897.054443 ]time: 0:26:09.392942 
[Epoch 35/2000][Batch 46/48][Autoencoder loss: 103.421860][C loss: 0.929395][M loss: 0.693275][D loss: 3061.965332][G loss 2454.647461 ]time: 0:26:09.845680 
[Epoch 35/2000][Batch 47/48][Autoencoder loss: 100.648468][C loss: 0.778406][M loss: 0.693273][D loss: 2163.358154][G loss 1987.043701 ]time: 0:26:10.391164 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 447us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 337us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 384us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 35/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 36/2000][Batch 0/48][Autoencoder loss: 414.028503][C loss: 1.445460][M loss: 0.693267][D loss: 665.824463][G loss 1838.049561 ]time: 0:26:25.979100 
[Epoch 36/2000][Batch 1/48][Autoencoder loss: 229.963440][C loss: 1.379988][M loss: 0.693262][D loss: 200.316895][G loss 1325.814087 ]time: 0:26:26.400695 
[Epoch 36/2000][Batch 2/48][Autoencoder loss: 120.360023][C loss: 1.233895][M loss: 0.693267][D loss: -423.091797][G loss 856.910706 ]time: 0:26:26.833809 
[Epoch 36/2000][Batch 3/48][Autoencoder loss: 91.771713][C loss: 1.151823][M loss: 0.693270][D loss: -1051.778809][G loss 374.719696 ]time: 0:26:27.300881 
[Epoch 36/2000][Batch 4/48][Autoencoder loss: 335.509766][C loss: 0.999878][M loss: 0.693263][D loss: -1648.447998][G loss -106.312454 ]time: 0:26:27.761128 
[Epoch 36/2000][Batch 5/48][Autoencoder loss: 725.780334][C loss: 1.322047][M loss: 0.693329][D loss: -2093.452148][G loss -573.000305 ]time: 0:26:28.257949 
[Epoch 36/2000][Batch 6/48][Autoencoder loss: 541.903748][C loss: 1.057833][M loss: 0.693282][D loss: -2639.203613][G loss -1027.797607 ]time: 0:26:28.705780 
[Epoch 36/2000][Batch 7/48][Autoencoder loss: 380.371490][C loss: 0.774517][M loss: 0.693270][D loss: -3214.561035][G loss -1472.289795 ]time: 0:26:29.135041 
[Epoch 36/2000][Batch 8/48][Autoencoder loss: 317.653229][C loss: 0.698238][M loss: 0.693265][D loss: -4309.791016][G loss -1549.682739 ]time: 0:26:29.800169 
[Epoch 36/2000][Batch 9/48][Autoencoder loss: 266.901276][C loss: 0.624354][M loss: 0.693268][D loss: -5007.285156][G loss -1875.803345 ]time: 0:26:30.788218 
[Epoch 36/2000][Batch 10/48][Autoencoder loss: 322.074158][C loss: 0.536852][M loss: 0.693268][D loss: -5773.915527][G loss -2199.835938 ]time: 0:26:31.271701 
[Epoch 36/2000][Batch 11/48][Autoencoder loss: 372.072723][C loss: 0.446780][M loss: 0.693274][D loss: -6413.731445][G loss -2603.256836 ]time: 0:26:32.199943 
[Epoch 36/2000][Batch 12/48][Autoencoder loss: 269.354767][C loss: 1.476919][M loss: 0.693272][D loss: -6469.923828][G loss -3447.789307 ]time: 0:26:32.797387 
[Epoch 36/2000][Batch 13/48][Autoencoder loss: 227.910736][C loss: 1.389135][M loss: 0.693273][D loss: -7245.902344][G loss -3921.895752 ]time: 0:26:33.225051 
[Epoch 36/2000][Batch 14/48][Autoencoder loss: 233.549576][C loss: 1.255748][M loss: 0.693273][D loss: -7935.625977][G loss -4247.307129 ]time: 0:26:33.652675 
[Epoch 36/2000][Batch 15/48][Autoencoder loss: 194.606018][C loss: 1.177048][M loss: 0.693271][D loss: -8512.038086][G loss -4679.811523 ]time: 0:26:34.096453 
[Epoch 36/2000][Batch 16/48][Autoencoder loss: 285.781525][C loss: 0.897877][M loss: 0.693277][D loss: -9420.907227][G loss -5025.762695 ]time: 0:26:34.532907 
[Epoch 36/2000][Batch 17/48][Autoencoder loss: 244.263214][C loss: 0.866956][M loss: 0.693269][D loss: -10161.216797][G loss -5356.208496 ]time: 0:26:34.964227 
[Epoch 36/2000][Batch 18/48][Autoencoder loss: 178.215469][C loss: 0.675627][M loss: 0.693270][D loss: -10805.751953][G loss -5727.740723 ]time: 0:26:35.407871 
[Epoch 36/2000][Batch 19/48][Autoencoder loss: 150.249588][C loss: 0.536282][M loss: 0.693267][D loss: -11353.500000][G loss -6126.398926 ]time: 0:26:35.832468 
[Epoch 36/2000][Batch 20/48][Autoencoder loss: 1204.911743][C loss: 0.527009][M loss: 0.693293][D loss: -12201.460938][G loss -6782.549805 ]time: 0:26:36.264658 
[Epoch 36/2000][Batch 21/48][Autoencoder loss: 1727.270386][C loss: 0.014739][M loss: 0.693273][D loss: -13150.586914][G loss -7290.228516 ]time: 0:26:36.714321 
[Epoch 36/2000][Batch 22/48][Autoencoder loss: 1784.252319][C loss: 0.001516][M loss: 0.694611][D loss: -13554.587891][G loss -7946.444336 ]time: 0:26:37.175641 
[Epoch 36/2000][Batch 23/48][Autoencoder loss: 1910.435913][C loss: 0.000045][M loss: 0.693551][D loss: -14635.295898][G loss -8215.288086 ]time: 0:26:37.768750 
[Epoch 36/2000][Batch 24/48][Autoencoder loss: 1791.898193][C loss: 0.648194][M loss: 0.693330][D loss: -14856.454102][G loss -8677.800781 ]time: 0:26:38.216437 
[Epoch 36/2000][Batch 25/48][Autoencoder loss: 1072.469360][C loss: 0.507489][M loss: 0.693305][D loss: -15920.083008][G loss -9576.341797 ]time: 0:26:38.673421 
[Epoch 36/2000][Batch 26/48][Autoencoder loss: 2312.850098][C loss: 0.647859][M loss: 0.693293][D loss: -16539.037109][G loss -9675.779297 ]time: 0:26:39.104572 
[Epoch 36/2000][Batch 27/48][Autoencoder loss: 1477.069214][C loss: 0.842936][M loss: 0.693272][D loss: -16620.257812][G loss -10356.558594 ]time: 0:26:39.546284 
[Epoch 36/2000][Batch 28/48][Autoencoder loss: 741.190857][C loss: 2.313239][M loss: 0.693328][D loss: -16702.939453][G loss -11340.071289 ]time: 0:26:40.340886 
[Epoch 36/2000][Batch 29/48][Autoencoder loss: 1290.979858][C loss: 1.928632][M loss: 0.693359][D loss: -17000.183594][G loss -12020.129883 ]time: 0:26:41.314650 
[Epoch 36/2000][Batch 30/48][Autoencoder loss: 1640.132812][C loss: 2.013644][M loss: 0.693295][D loss: -17894.755859][G loss -12300.152344 ]time: 0:26:42.213979 
[Epoch 36/2000][Batch 31/48][Autoencoder loss: 612.908875][C loss: 1.549674][M loss: 0.693276][D loss: -19483.085938][G loss -12964.520508 ]time: 0:26:43.308560 
[Epoch 36/2000][Batch 32/48][Autoencoder loss: 382.730469][C loss: 1.261796][M loss: 0.693299][D loss: -22938.005859][G loss -11405.253906 ]time: 0:26:43.929886 
[Epoch 36/2000][Batch 33/48][Autoencoder loss: 963.519775][C loss: 1.203913][M loss: 0.693274][D loss: -23883.292969][G loss -12065.809570 ]time: 0:26:44.427524 
[Epoch 36/2000][Batch 34/48][Autoencoder loss: 728.825256][C loss: 1.090990][M loss: 0.693278][D loss: -24345.929688][G loss -12592.393555 ]time: 0:26:44.872445 
[Epoch 36/2000][Batch 35/48][Autoencoder loss: 306.442261][C loss: 0.900153][M loss: 0.693274][D loss: -24385.107422][G loss -13085.264648 ]time: 0:26:45.303781 
[Epoch 36/2000][Batch 36/48][Autoencoder loss: 630.629700][C loss: 0.776030][M loss: 0.693267][D loss: -25310.478516][G loss -14258.099609 ]time: 0:26:45.736214 
[Epoch 36/2000][Batch 37/48][Autoencoder loss: 707.065186][C loss: 0.774801][M loss: 0.693267][D loss: -26530.037109][G loss -14364.925781 ]time: 0:26:46.168124 
[Epoch 36/2000][Batch 38/48][Autoencoder loss: 298.605011][C loss: 0.733796][M loss: 0.693264][D loss: -27812.566406][G loss -15799.900391 ]time: 0:26:46.595417 
[Epoch 36/2000][Batch 39/48][Autoencoder loss: 353.171753][C loss: 0.669877][M loss: 0.693265][D loss: -29271.474609][G loss -16320.981445 ]time: 0:26:47.024032 
[Epoch 36/2000][Batch 40/48][Autoencoder loss: 675.304993][C loss: 0.701210][M loss: 0.693263][D loss: -30661.640625][G loss -17284.832031 ]time: 0:26:47.454165 
[Epoch 36/2000][Batch 41/48][Autoencoder loss: 475.133820][C loss: 0.665419][M loss: 0.693263][D loss: -30739.683594][G loss -17633.720703 ]time: 0:26:47.907233 
[Epoch 36/2000][Batch 42/48][Autoencoder loss: 454.929077][C loss: 0.639281][M loss: 0.693264][D loss: -32065.433594][G loss -18616.128906 ]time: 0:26:48.332885 
[Epoch 36/2000][Batch 43/48][Autoencoder loss: 398.131348][C loss: 0.544129][M loss: 0.693263][D loss: -31871.285156][G loss -20065.898438 ]time: 0:26:48.753144 
[Epoch 36/2000][Batch 44/48][Autoencoder loss: 272.252106][C loss: 1.387325][M loss: 0.693264][D loss: -34038.214844][G loss -20783.025391 ]time: 0:26:49.489913 
[Epoch 36/2000][Batch 45/48][Autoencoder loss: 135.148270][C loss: 1.341238][M loss: 0.693264][D loss: -36363.449219][G loss -21295.976562 ]time: 0:26:50.162825 
[Epoch 36/2000][Batch 46/48][Autoencoder loss: 178.338318][C loss: 1.162299][M loss: 0.693261][D loss: -37220.496094][G loss -22133.580078 ]time: 0:26:50.576841 
[Epoch 36/2000][Batch 47/48][Autoencoder loss: 252.261063][C loss: 0.966417][M loss: 0.693262][D loss: -37655.460938][G loss -23495.689453 ]time: 0:26:51.134346 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 459us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 391us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 372us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 36/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 37/2000][Batch 0/48][Autoencoder loss: 332.386536][C loss: 1.170186][M loss: 0.693262][D loss: -33450.515625][G loss -31172.376953 ]time: 0:27:03.755302 
[Epoch 37/2000][Batch 1/48][Autoencoder loss: 224.574707][C loss: 1.287207][M loss: 0.693261][D loss: -34484.296875][G loss -32504.152344 ]time: 0:27:04.329257 
[Epoch 37/2000][Batch 2/48][Autoencoder loss: 193.162766][C loss: 1.143020][M loss: 0.693262][D loss: -35318.519531][G loss -33465.457031 ]time: 0:27:04.757823 
[Epoch 37/2000][Batch 3/48][Autoencoder loss: 147.493301][C loss: 1.060613][M loss: 0.693261][D loss: -37026.726562][G loss -34355.695312 ]time: 0:27:05.442198 
[Epoch 37/2000][Batch 4/48][Autoencoder loss: 317.832153][C loss: 1.001142][M loss: 0.693261][D loss: -39673.906250][G loss -36089.660156 ]time: 0:27:06.628728 
[Epoch 37/2000][Batch 5/48][Autoencoder loss: 748.472229][C loss: 1.077546][M loss: 0.693304][D loss: -42299.851562][G loss -37902.925781 ]time: 0:27:07.266460 
[Epoch 37/2000][Batch 6/48][Autoencoder loss: 505.691681][C loss: 1.062937][M loss: 0.693270][D loss: -41531.476562][G loss -40425.625000 ]time: 0:27:07.699795 
[Epoch 37/2000][Batch 7/48][Autoencoder loss: 303.779572][C loss: 0.988145][M loss: 0.693270][D loss: -43083.167969][G loss -41702.308594 ]time: 0:27:08.133779 
[Epoch 37/2000][Batch 8/48][Autoencoder loss: 296.010742][C loss: 1.185983][M loss: 0.693264][D loss: -50382.929688][G loss -35677.867188 ]time: 0:27:08.559838 
[Epoch 37/2000][Batch 9/48][Autoencoder loss: 279.165680][C loss: 1.068570][M loss: 0.693262][D loss: -53461.964844][G loss -36451.347656 ]time: 0:27:08.996221 
[Epoch 37/2000][Batch 10/48][Autoencoder loss: 288.463379][C loss: 0.925053][M loss: 0.693264][D loss: -55291.406250][G loss -38491.312500 ]time: 0:27:09.418484 
[Epoch 37/2000][Batch 11/48][Autoencoder loss: 348.866089][C loss: 0.770679][M loss: 0.693263][D loss: -56751.675781][G loss -40224.082031 ]time: 0:27:09.848494 
[Epoch 37/2000][Batch 12/48][Autoencoder loss: 265.067505][C loss: 0.725282][M loss: 0.693262][D loss: -51137.808594][G loss -49287.167969 ]time: 0:27:10.291469 
[Epoch 37/2000][Batch 13/48][Autoencoder loss: 207.957001][C loss: 0.772509][M loss: 0.693261][D loss: -51418.882812][G loss -51412.234375 ]time: 0:27:10.723664 
[Epoch 37/2000][Batch 14/48][Autoencoder loss: 219.683655][C loss: 0.782170][M loss: 0.693262][D loss: -54235.433594][G loss -52167.484375 ]time: 0:27:11.161328 
[Epoch 37/2000][Batch 15/48][Autoencoder loss: 196.112106][C loss: 0.786449][M loss: 0.693264][D loss: -55646.437500][G loss -54618.753906 ]time: 0:27:11.595557 
[Epoch 37/2000][Batch 16/48][Autoencoder loss: 282.502350][C loss: 0.656671][M loss: 0.693269][D loss: -58539.156250][G loss -54061.937500 ]time: 0:27:12.041975 
[Epoch 37/2000][Batch 17/48][Autoencoder loss: 245.308075][C loss: 0.643348][M loss: 0.693267][D loss: -59062.371094][G loss -59259.933594 ]time: 0:27:12.572100 
[Epoch 37/2000][Batch 18/48][Autoencoder loss: 161.649155][C loss: 0.539126][M loss: 0.693261][D loss: -63735.480469][G loss -61803.121094 ]time: 0:27:13.003036 
[Epoch 37/2000][Batch 19/48][Autoencoder loss: 130.659821][C loss: 0.466323][M loss: 0.693261][D loss: -62657.062500][G loss -63317.792969 ]time: 0:27:13.438640 
[Epoch 37/2000][Batch 20/48][Autoencoder loss: 1135.158813][C loss: 0.425908][M loss: 0.693278][D loss: -56994.441406][G loss -66875.023438 ]time: 0:27:13.921590 
[Epoch 37/2000][Batch 21/48][Autoencoder loss: 2000.951782][C loss: 0.020354][M loss: 0.693296][D loss: -53869.171875][G loss -73666.210938 ]time: 0:27:14.771935 
[Epoch 37/2000][Batch 22/48][Autoencoder loss: 2319.123535][C loss: 0.002843][M loss: 0.693463][D loss: -53060.804688][G loss -75506.882812 ]time: 0:27:15.420341 
[Epoch 37/2000][Batch 23/48][Autoencoder loss: 2267.908936][C loss: 0.002333][M loss: 0.693296][D loss: -53731.273438][G loss -74648.445312 ]time: 0:27:15.942611 
[Epoch 37/2000][Batch 24/48][Autoencoder loss: 1783.818481][C loss: 0.607951][M loss: 0.693265][D loss: -63208.367188][G loss -82104.882812 ]time: 0:27:16.375861 
[Epoch 37/2000][Batch 25/48][Autoencoder loss: 1191.845581][C loss: 0.418123][M loss: 0.693269][D loss: -66171.953125][G loss -85705.468750 ]time: 0:27:16.816502 
[Epoch 37/2000][Batch 26/48][Autoencoder loss: 3084.724365][C loss: 0.414476][M loss: 0.693265][D loss: -66082.281250][G loss -89218.046875 ]time: 0:27:17.845704 
[Epoch 37/2000][Batch 27/48][Autoencoder loss: 1987.095337][C loss: 0.437539][M loss: 0.693262][D loss: -64219.828125][G loss -93189.382812 ]time: 0:27:18.689886 
[Epoch 37/2000][Batch 28/48][Autoencoder loss: 941.593567][C loss: 1.572620][M loss: 0.693263][D loss: -55822.703125][G loss -104290.367188 ]time: 0:27:19.127942 
[Epoch 37/2000][Batch 29/48][Autoencoder loss: 1746.815918][C loss: 1.406482][M loss: 0.693269][D loss: -47616.730469][G loss -106879.320312 ]time: 0:27:19.558873 
[Epoch 37/2000][Batch 30/48][Autoencoder loss: 2162.401123][C loss: 1.422755][M loss: 0.693268][D loss: -56231.660156][G loss -110797.140625 ]time: 0:27:19.997695 
[Epoch 37/2000][Batch 31/48][Autoencoder loss: 561.709106][C loss: 1.146002][M loss: 0.693271][D loss: -59432.261719][G loss -115760.078125 ]time: 0:27:20.426147 
[Epoch 37/2000][Batch 32/48][Autoencoder loss: 644.241211][C loss: 1.095463][M loss: 0.693269][D loss: -81912.367188][G loss -93376.000000 ]time: 0:27:20.866972 
[Epoch 37/2000][Batch 33/48][Autoencoder loss: 1351.089233][C loss: 1.033925][M loss: 0.693269][D loss: -77911.234375][G loss -101156.281250 ]time: 0:27:21.351397 
[Epoch 37/2000][Batch 34/48][Autoencoder loss: 625.696838][C loss: 0.969721][M loss: 0.693272][D loss: -74743.500000][G loss -105057.632812 ]time: 0:27:21.810375 
[Epoch 37/2000][Batch 35/48][Autoencoder loss: 570.580444][C loss: 0.881683][M loss: 0.693269][D loss: -73421.078125][G loss -112068.132812 ]time: 0:27:22.259004 
[Epoch 37/2000][Batch 36/48][Autoencoder loss: 1229.031738][C loss: 0.650527][M loss: 0.693268][D loss: -72225.343750][G loss -117046.015625 ]time: 0:27:22.778695 
[Epoch 37/2000][Batch 37/48][Autoencoder loss: 755.734009][C loss: 0.694792][M loss: 0.693265][D loss: -73339.437500][G loss -121671.453125 ]time: 0:27:24.067327 
[Epoch 37/2000][Batch 38/48][Autoencoder loss: 310.791077][C loss: 0.710166][M loss: 0.693263][D loss: -73453.031250][G loss -119933.132812 ]time: 0:27:24.531555 
[Epoch 37/2000][Batch 39/48][Autoencoder loss: 831.750793][C loss: 0.669901][M loss: 0.693262][D loss: -72422.804688][G loss -129826.703125 ]time: 0:27:25.022708 
[Epoch 37/2000][Batch 40/48][Autoencoder loss: 992.043213][C loss: 0.914635][M loss: 0.693282][D loss: -63974.476562][G loss -136857.687500 ]time: 0:27:25.456440 
[Epoch 37/2000][Batch 41/48][Autoencoder loss: 387.362122][C loss: 0.712074][M loss: 0.693266][D loss: -54579.503906][G loss -145186.406250 ]time: 0:27:25.899103 
[Epoch 37/2000][Batch 42/48][Autoencoder loss: 960.601990][C loss: 0.605136][M loss: 0.693270][D loss: -60034.730469][G loss -149001.859375 ]time: 0:27:26.320603 
[Epoch 37/2000][Batch 43/48][Autoencoder loss: 770.869080][C loss: 0.556902][M loss: 0.693264][D loss: -53061.140625][G loss -154828.234375 ]time: 0:27:26.755324 
[Epoch 37/2000][Batch 44/48][Autoencoder loss: 231.554550][C loss: 1.227008][M loss: 0.693262][D loss: -58640.500000][G loss -154364.015625 ]time: 0:27:27.263090 
[Epoch 37/2000][Batch 45/48][Autoencoder loss: 278.479156][C loss: 1.214535][M loss: 0.693263][D loss: -60511.281250][G loss -167459.093750 ]time: 0:27:27.696296 
[Epoch 37/2000][Batch 46/48][Autoencoder loss: 484.843353][C loss: 1.127433][M loss: 0.693262][D loss: -48046.296875][G loss -173883.640625 ]time: 0:27:28.122999 
[Epoch 37/2000][Batch 47/48][Autoencoder loss: 224.479584][C loss: 0.992823][M loss: 0.693262][D loss: -44345.847656][G loss -175476.265625 ]time: 0:27:28.556069 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 432us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 578us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 410us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 37/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 38/2000][Batch 0/48][Autoencoder loss: 450.868683][C loss: 0.926736][M loss: 0.693266][D loss: 11249.671875][G loss -229594.796875 ]time: 0:27:44.327284 
[Epoch 38/2000][Batch 1/48][Autoencoder loss: 654.121399][C loss: 1.255431][M loss: 0.693265][D loss: 18722.757812][G loss -235858.812500 ]time: 0:27:44.763746 
[Epoch 38/2000][Batch 2/48][Autoencoder loss: 527.528381][C loss: 1.196518][M loss: 0.693263][D loss: 19676.718750][G loss -241315.859375 ]time: 0:27:45.195054 
[Epoch 38/2000][Batch 3/48][Autoencoder loss: 144.018784][C loss: 1.178886][M loss: 0.693261][D loss: 24794.949219][G loss -240379.906250 ]time: 0:27:45.725609 
[Epoch 38/2000][Batch 4/48][Autoencoder loss: 531.051514][C loss: 1.140961][M loss: 0.693269][D loss: 12123.445312][G loss -256836.406250 ]time: 0:27:46.211323 
[Epoch 38/2000][Batch 5/48][Autoencoder loss: 1049.512085][C loss: 1.644323][M loss: 0.693268][D loss: 25852.230469][G loss -248185.281250 ]time: 0:27:46.664724 
[Epoch 38/2000][Batch 6/48][Autoencoder loss: 453.935852][C loss: 1.023870][M loss: 0.693265][D loss: 26593.515625][G loss -252840.468750 ]time: 0:27:47.096532 
[Epoch 38/2000][Batch 7/48][Autoencoder loss: 441.003021][C loss: 0.847050][M loss: 0.693265][D loss: 43315.632812][G loss -264798.937500 ]time: 0:27:47.565882 
[Epoch 38/2000][Batch 8/48][Autoencoder loss: 660.249756][C loss: 1.359955][M loss: 0.693266][D loss: -7710.183594][G loss -205979.078125 ]time: 0:27:47.999746 
[Epoch 38/2000][Batch 9/48][Autoencoder loss: 350.378967][C loss: 1.382594][M loss: 0.693267][D loss: -4973.218750][G loss -207337.984375 ]time: 0:27:48.420414 
[Epoch 38/2000][Batch 10/48][Autoencoder loss: 270.134277][C loss: 1.253150][M loss: 0.693266][D loss: -4114.312500][G loss -199911.093750 ]time: 0:27:49.000798 
[Epoch 38/2000][Batch 11/48][Autoencoder loss: 548.987488][C loss: 0.942284][M loss: 0.693264][D loss: 8132.519531][G loss -204589.671875 ]time: 0:27:50.127110 
[Epoch 38/2000][Batch 12/48][Autoencoder loss: 366.891144][C loss: 1.438068][M loss: 0.693263][D loss: 43913.460938][G loss -241703.328125 ]time: 0:27:50.567717 
[Epoch 38/2000][Batch 13/48][Autoencoder loss: 186.870026][C loss: 1.372115][M loss: 0.693262][D loss: 57098.531250][G loss -242011.734375 ]time: 0:27:51.004262 
[Epoch 38/2000][Batch 14/48][Autoencoder loss: 326.846008][C loss: 1.194894][M loss: 0.693262][D loss: 55649.527344][G loss -246695.718750 ]time: 0:27:51.456809 
[Epoch 38/2000][Batch 15/48][Autoencoder loss: 314.286743][C loss: 1.178153][M loss: 0.693264][D loss: 65424.023438][G loss -243758.093750 ]time: 0:27:51.892065 
[Epoch 38/2000][Batch 16/48][Autoencoder loss: 273.311188][C loss: 0.935265][M loss: 0.693261][D loss: 56456.234375][G loss -226933.406250 ]time: 0:27:52.323770 
[Epoch 38/2000][Batch 17/48][Autoencoder loss: 272.319855][C loss: 0.953987][M loss: 0.693262][D loss: 58755.472656][G loss -230864.531250 ]time: 0:27:52.753082 
[Epoch 38/2000][Batch 18/48][Autoencoder loss: 264.257050][C loss: 0.769971][M loss: 0.693263][D loss: 59752.250000][G loss -226925.750000 ]time: 0:27:53.187247 
[Epoch 38/2000][Batch 19/48][Autoencoder loss: 148.310364][C loss: 0.680831][M loss: 0.693263][D loss: 66977.710938][G loss -221097.796875 ]time: 0:27:54.146323 
[Epoch 38/2000][Batch 20/48][Autoencoder loss: 1432.141724][C loss: 1.478871][M loss: 0.693301][D loss: 86455.703125][G loss -232832.765625 ]time: 0:27:54.646604 
[Epoch 38/2000][Batch 21/48][Autoencoder loss: 1512.223999][C loss: 1.410738][M loss: 0.693294][D loss: 93546.187500][G loss -228135.640625 ]time: 0:27:55.095404 
[Epoch 38/2000][Batch 22/48][Autoencoder loss: 2465.267822][C loss: 0.005985][M loss: 0.693338][D loss: 92751.453125][G loss -226552.765625 ]time: 0:27:55.543245 
[Epoch 38/2000][Batch 23/48][Autoencoder loss: 3274.264160][C loss: 0.263013][M loss: 0.693285][D loss: 92562.320312][G loss -215635.031250 ]time: 0:27:56.032194 
[Epoch 38/2000][Batch 24/48][Autoencoder loss: 2152.135742][C loss: 1.263384][M loss: 0.693265][D loss: 81452.921875][G loss -212842.734375 ]time: 0:27:56.459396 
[Epoch 38/2000][Batch 25/48][Autoencoder loss: 1118.515625][C loss: 0.853667][M loss: 0.693266][D loss: 79392.898438][G loss -207581.828125 ]time: 0:27:56.888052 
[Epoch 38/2000][Batch 26/48][Autoencoder loss: 3619.566650][C loss: 0.875668][M loss: 0.693262][D loss: 80823.914062][G loss -203755.906250 ]time: 0:27:57.305692 
[Epoch 38/2000][Batch 27/48][Autoencoder loss: 2161.287109][C loss: 0.853561][M loss: 0.693264][D loss: 92092.601562][G loss -193815.468750 ]time: 0:27:58.136653 
[Epoch 38/2000][Batch 28/48][Autoencoder loss: 463.776459][C loss: 1.873641][M loss: 0.693263][D loss: 108772.757812][G loss -210272.703125 ]time: 0:27:58.852308 
[Epoch 38/2000][Batch 29/48][Autoencoder loss: 1857.885986][C loss: 1.921307][M loss: 0.693270][D loss: 106295.906250][G loss -200007.703125 ]time: 0:27:59.311212 
[Epoch 38/2000][Batch 30/48][Autoencoder loss: 2934.130615][C loss: 1.739292][M loss: 0.693266][D loss: 103629.046875][G loss -190177.296875 ]time: 0:27:59.772809 
[Epoch 38/2000][Batch 31/48][Autoencoder loss: 638.810303][C loss: 1.569359][M loss: 0.693264][D loss: 97007.234375][G loss -182151.859375 ]time: 0:28:00.266919 
[Epoch 38/2000][Batch 32/48][Autoencoder loss: 985.810608][C loss: 1.504655][M loss: 0.693266][D loss: 50443.121094][G loss -132597.046875 ]time: 0:28:00.682733 
[Epoch 38/2000][Batch 33/48][Autoencoder loss: 1539.918457][C loss: 1.370957][M loss: 0.693265][D loss: 49508.828125][G loss -126425.523438 ]time: 0:28:01.108769 
[Epoch 38/2000][Batch 34/48][Autoencoder loss: 458.863892][C loss: 1.243654][M loss: 0.693269][D loss: 52026.804688][G loss -122594.335938 ]time: 0:28:01.542111 
[Epoch 38/2000][Batch 35/48][Autoencoder loss: 704.395935][C loss: 1.163080][M loss: 0.693265][D loss: 49555.660156][G loss -117129.640625 ]time: 0:28:01.962169 
[Epoch 38/2000][Batch 36/48][Autoencoder loss: 1297.663940][C loss: 0.483725][M loss: 0.693263][D loss: 47295.437500][G loss -111533.101562 ]time: 0:28:02.391848 
[Epoch 38/2000][Batch 37/48][Autoencoder loss: 644.559509][C loss: 0.369832][M loss: 0.693264][D loss: 48355.843750][G loss -108375.789062 ]time: 0:28:02.824319 
[Epoch 38/2000][Batch 38/48][Autoencoder loss: 422.360413][C loss: 0.358317][M loss: 0.693262][D loss: 44701.281250][G loss -102784.679688 ]time: 0:28:03.263768 
[Epoch 38/2000][Batch 39/48][Autoencoder loss: 963.458801][C loss: 0.366305][M loss: 0.693261][D loss: 46957.867188][G loss -95714.859375 ]time: 0:28:03.693549 
[Epoch 38/2000][Batch 40/48][Autoencoder loss: 757.674255][C loss: 0.626341][M loss: 0.693262][D loss: 44916.707031][G loss -91841.484375 ]time: 0:28:04.119319 
[Epoch 38/2000][Batch 41/48][Autoencoder loss: 366.225677][C loss: 0.620690][M loss: 0.693262][D loss: 44321.332031][G loss -88278.210938 ]time: 0:28:04.550764 
[Epoch 38/2000][Batch 42/48][Autoencoder loss: 947.107971][C loss: 0.582254][M loss: 0.693262][D loss: 40550.843750][G loss -82037.320312 ]time: 0:28:05.381309 
[Epoch 38/2000][Batch 43/48][Autoencoder loss: 590.564148][C loss: 0.560665][M loss: 0.693262][D loss: 41597.378906][G loss -76312.078125 ]time: 0:28:06.477295 
[Epoch 38/2000][Batch 44/48][Autoencoder loss: 183.579971][C loss: 1.210995][M loss: 0.693263][D loss: 33989.937500][G loss -68278.781250 ]time: 0:28:07.172772 
[Epoch 38/2000][Batch 45/48][Autoencoder loss: 334.920837][C loss: 1.153529][M loss: 0.693264][D loss: 33008.656250][G loss -62703.085938 ]time: 0:28:07.604963 
[Epoch 38/2000][Batch 46/48][Autoencoder loss: 460.403900][C loss: 1.021038][M loss: 0.693262][D loss: 30623.597656][G loss -61003.292969 ]time: 0:28:08.038171 
[Epoch 38/2000][Batch 47/48][Autoencoder loss: 200.754532][C loss: 0.840808][M loss: 0.693261][D loss: 26640.808594][G loss -53357.070312 ]time: 0:28:08.466267 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 367us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 474us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 448us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 38/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 39/2000][Batch 0/48][Autoencoder loss: 319.274292][C loss: 1.628670][M loss: 0.693262][D loss: 44781.824219][G loss -65431.023438 ]time: 0:28:28.403617 
[Epoch 39/2000][Batch 1/48][Autoencoder loss: 508.833496][C loss: 1.776837][M loss: 0.693261][D loss: 41695.773438][G loss -57260.679688 ]time: 0:28:28.834002 
[Epoch 39/2000][Batch 2/48][Autoencoder loss: 317.748108][C loss: 1.696062][M loss: 0.693261][D loss: 34870.843750][G loss -50156.308594 ]time: 0:28:29.268477 
[Epoch 39/2000][Batch 3/48][Autoencoder loss: 126.080254][C loss: 1.607103][M loss: 0.693262][D loss: 30039.472656][G loss -41796.960938 ]time: 0:28:29.729381 
[Epoch 39/2000][Batch 4/48][Autoencoder loss: 414.591858][C loss: 1.501544][M loss: 0.693260][D loss: 25585.822266][G loss -32473.837891 ]time: 0:28:30.183003 
[Epoch 39/2000][Batch 5/48][Autoencoder loss: 782.278625][C loss: 1.412242][M loss: 0.693267][D loss: 19181.386719][G loss -22528.353516 ]time: 0:28:30.630045 
[Epoch 39/2000][Batch 6/48][Autoencoder loss: 502.039917][C loss: 1.319688][M loss: 0.693263][D loss: 13854.149414][G loss -15182.116211 ]time: 0:28:31.068806 
[Epoch 39/2000][Batch 7/48][Autoencoder loss: 337.094177][C loss: 1.188069][M loss: 0.693264][D loss: 8341.160156][G loss -8376.646484 ]time: 0:28:31.532261 
[Epoch 39/2000][Batch 8/48][Autoencoder loss: 276.239380][C loss: 1.435908][M loss: 0.693262][D loss: 2192.474854][G loss -3147.417236 ]time: 0:28:32.239585 
[Epoch 39/2000][Batch 9/48][Autoencoder loss: 269.750854][C loss: 1.327905][M loss: 0.693262][D loss: 391.423462][G loss -1005.844421 ]time: 0:28:33.354130 
[Epoch 39/2000][Batch 10/48][Autoencoder loss: 291.704041][C loss: 1.149847][M loss: 0.693263][D loss: -535.531372][G loss 871.975952 ]time: 0:28:34.153937 
[Epoch 39/2000][Batch 11/48][Autoencoder loss: 346.200012][C loss: 0.918369][M loss: 0.693260][D loss: -1372.048096][G loss 2619.484863 ]time: 0:28:34.601689 
[Epoch 39/2000][Batch 12/48][Autoencoder loss: 269.936523][C loss: 0.715219][M loss: 0.693261][D loss: -2734.855957][G loss 4959.585449 ]time: 0:28:35.052842 
[Epoch 39/2000][Batch 13/48][Autoencoder loss: 208.511627][C loss: 0.731458][M loss: 0.693260][D loss: -3757.532715][G loss 7195.588379 ]time: 0:28:35.494164 
[Epoch 39/2000][Batch 14/48][Autoencoder loss: 200.910843][C loss: 0.891306][M loss: 0.693261][D loss: -5264.342285][G loss 9620.606445 ]time: 0:28:35.941311 
[Epoch 39/2000][Batch 15/48][Autoencoder loss: 173.007782][C loss: 0.802314][M loss: 0.693261][D loss: -6482.275391][G loss 11657.449219 ]time: 0:28:36.390044 
[Epoch 39/2000][Batch 16/48][Autoencoder loss: 296.192322][C loss: 0.665056][M loss: 0.693261][D loss: -7089.861816][G loss 13580.520508 ]time: 0:28:36.829081 
[Epoch 39/2000][Batch 17/48][Autoencoder loss: 246.842026][C loss: 0.647538][M loss: 0.693262][D loss: -8577.532227][G loss 15495.011719 ]time: 0:28:37.271979 
[Epoch 39/2000][Batch 18/48][Autoencoder loss: 153.003098][C loss: 0.554556][M loss: 0.693260][D loss: -9962.489258][G loss 17602.871094 ]time: 0:28:37.715655 
[Epoch 39/2000][Batch 19/48][Autoencoder loss: 132.295654][C loss: 0.505113][M loss: 0.693261][D loss: -11056.931641][G loss 20103.607422 ]time: 0:28:38.170218 
[Epoch 39/2000][Batch 20/48][Autoencoder loss: 2798.238525][C loss: 0.270290][M loss: 0.693274][D loss: -13762.678711][G loss 24552.724609 ]time: 0:28:38.613050 
[Epoch 39/2000][Batch 21/48][Autoencoder loss: 4323.771973][C loss: 0.184403][M loss: 0.693277][D loss: -15582.605469][G loss 27646.363281 ]time: 0:28:39.046087 
[Epoch 39/2000][Batch 22/48][Autoencoder loss: 8438.530273][C loss: 0.151024][M loss: 0.693371][D loss: -18308.572266][G loss 31836.199219 ]time: 0:28:39.534327 
[Epoch 39/2000][Batch 23/48][Autoencoder loss: 9334.606445][C loss: 0.013643][M loss: 0.693294][D loss: -19192.488281][G loss 36502.640625 ]time: 0:28:39.989897 
[Epoch 39/2000][Batch 24/48][Autoencoder loss: 2633.971680][C loss: 0.718460][M loss: 0.693269][D loss: -22374.744141][G loss 41617.265625 ]time: 0:28:40.419831 
[Epoch 39/2000][Batch 25/48][Autoencoder loss: 3033.643066][C loss: 0.476497][M loss: 0.693268][D loss: -24875.619141][G loss 46463.386719 ]time: 0:28:41.099063 
[Epoch 39/2000][Batch 26/48][Autoencoder loss: 8876.202148][C loss: 0.458590][M loss: 0.693263][D loss: -28932.533203][G loss 54178.757812 ]time: 0:28:42.072951 
[Epoch 39/2000][Batch 27/48][Autoencoder loss: 3861.175537][C loss: 0.419013][M loss: 0.693261][D loss: -33060.132812][G loss 59969.140625 ]time: 0:28:42.644054 
[Epoch 39/2000][Batch 28/48][Autoencoder loss: 2090.811768][C loss: 1.385248][M loss: 0.693272][D loss: -46275.734375][G loss 76284.898438 ]time: 0:28:43.071822 
[Epoch 39/2000][Batch 29/48][Autoencoder loss: 4987.886230][C loss: 1.767953][M loss: 0.693282][D loss: -48255.429688][G loss 85640.117188 ]time: 0:28:43.502223 
[Epoch 39/2000][Batch 30/48][Autoencoder loss: 5237.447754][C loss: 1.440826][M loss: 0.693284][D loss: -56672.550781][G loss 93772.851562 ]time: 0:28:43.918308 
[Epoch 39/2000][Batch 31/48][Autoencoder loss: 1097.089111][C loss: 1.212601][M loss: 0.693266][D loss: -58564.160156][G loss 107473.750000 ]time: 0:28:44.342770 
[Epoch 39/2000][Batch 32/48][Autoencoder loss: 2652.596680][C loss: 1.265064][M loss: 0.693263][D loss: -38479.585938][G loss 88254.367188 ]time: 0:28:45.178249 
[Epoch 39/2000][Batch 33/48][Autoencoder loss: 2244.516602][C loss: 1.167237][M loss: 0.693268][D loss: -42355.281250][G loss 101337.601562 ]time: 0:28:45.780483 
[Epoch 39/2000][Batch 34/48][Autoencoder loss: 940.835693][C loss: 1.092144][M loss: 0.693266][D loss: -48345.277344][G loss 107786.835938 ]time: 0:28:46.278773 
[Epoch 39/2000][Batch 35/48][Autoencoder loss: 2235.687988][C loss: 1.054612][M loss: 0.693267][D loss: -52066.085938][G loss 117280.929688 ]time: 0:28:46.735606 
[Epoch 39/2000][Batch 36/48][Autoencoder loss: 2601.205322][C loss: 0.945229][M loss: 0.693266][D loss: -58862.062500][G loss 131429.468750 ]time: 0:28:47.172325 
[Epoch 39/2000][Batch 37/48][Autoencoder loss: 1225.745361][C loss: 0.949490][M loss: 0.693266][D loss: -61998.207031][G loss 144253.421875 ]time: 0:28:47.593459 
[Epoch 39/2000][Batch 38/48][Autoencoder loss: 1105.878540][C loss: 0.948156][M loss: 0.693267][D loss: -67562.687500][G loss 150422.734375 ]time: 0:28:48.028114 
[Epoch 39/2000][Batch 39/48][Autoencoder loss: 1732.663330][C loss: 0.879905][M loss: 0.693268][D loss: -71195.382812][G loss 160302.281250 ]time: 0:28:48.469883 
[Epoch 39/2000][Batch 40/48][Autoencoder loss: 1203.077026][C loss: 1.064030][M loss: 0.693268][D loss: -75959.109375][G loss 182311.421875 ]time: 0:28:48.888579 
[Epoch 39/2000][Batch 41/48][Autoencoder loss: 894.032288][C loss: 1.023914][M loss: 0.693267][D loss: -84865.648438][G loss 182465.546875 ]time: 0:28:49.307945 
[Epoch 39/2000][Batch 42/48][Autoencoder loss: 1689.620850][C loss: 0.955277][M loss: 0.693266][D loss: -85538.140625][G loss 198949.687500 ]time: 0:28:49.989544 
[Epoch 39/2000][Batch 43/48][Autoencoder loss: 985.122681][C loss: 0.924728][M loss: 0.693265][D loss: -90050.437500][G loss 219590.640625 ]time: 0:28:50.536338 
[Epoch 39/2000][Batch 44/48][Autoencoder loss: 452.893677][C loss: 1.267327][M loss: 0.693263][D loss: -74688.742188][G loss 214444.281250 ]time: 0:28:50.956028 
[Epoch 39/2000][Batch 45/48][Autoencoder loss: 840.361572][C loss: 1.221589][M loss: 0.693262][D loss: -86821.218750][G loss 224651.296875 ]time: 0:28:51.437412 
[Epoch 39/2000][Batch 46/48][Autoencoder loss: 967.512634][C loss: 1.120561][M loss: 0.693262][D loss: -87275.617188][G loss 244415.640625 ]time: 0:28:51.864467 
[Epoch 39/2000][Batch 47/48][Autoencoder loss: 555.252319][C loss: 0.972424][M loss: 0.693262][D loss: -90118.562500][G loss 250104.468750 ]time: 0:28:52.399230 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 470us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 358us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 1s
55/63 [=========================>....] - ETA: 0s
63/63 [==============================] - 0s 918us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 39/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 40/2000][Batch 0/48][Autoencoder loss: 800.370789][C loss: 1.280218][M loss: 0.693269][D loss: -186509.062500][G loss 358368.406250 ]time: 0:29:08.841499 
[Epoch 40/2000][Batch 1/48][Autoencoder loss: 1057.482666][C loss: 1.482234][M loss: 0.693263][D loss: -181051.890625][G loss 384735.343750 ]time: 0:29:09.549875 
[Epoch 40/2000][Batch 2/48][Autoencoder loss: 726.398315][C loss: 1.416004][M loss: 0.693261][D loss: -184020.843750][G loss 406750.812500 ]time: 0:29:09.980000 
[Epoch 40/2000][Batch 3/48][Autoencoder loss: 389.032806][C loss: 1.375382][M loss: 0.693263][D loss: -187273.437500][G loss 420989.468750 ]time: 0:29:10.405376 
[Epoch 40/2000][Batch 4/48][Autoencoder loss: 606.974792][C loss: 1.302948][M loss: 0.693262][D loss: -188561.625000][G loss 439925.718750 ]time: 0:29:10.846103 
[Epoch 40/2000][Batch 5/48][Autoencoder loss: 919.059143][C loss: 1.320505][M loss: 0.693271][D loss: -185219.062500][G loss 466271.187500 ]time: 0:29:11.274081 
[Epoch 40/2000][Batch 6/48][Autoencoder loss: 488.826630][C loss: 1.124106][M loss: 0.693265][D loss: -190903.718750][G loss 484729.375000 ]time: 0:29:11.701615 
[Epoch 40/2000][Batch 7/48][Autoencoder loss: 505.360229][C loss: 1.040136][M loss: 0.693263][D loss: -178859.390625][G loss 486205.312500 ]time: 0:29:12.135316 
[Epoch 40/2000][Batch 8/48][Autoencoder loss: 541.415894][C loss: 1.331682][M loss: 0.693262][D loss: -54219.316406][G loss 393802.812500 ]time: 0:29:12.564605 
[Epoch 40/2000][Batch 9/48][Autoencoder loss: 374.611542][C loss: 1.187229][M loss: 0.693262][D loss: -54097.421875][G loss 395331.343750 ]time: 0:29:13.051362 
[Epoch 40/2000][Batch 10/48][Autoencoder loss: 333.443695][C loss: 1.027222][M loss: 0.693263][D loss: -28235.312500][G loss 393867.125000 ]time: 0:29:13.473196 
[Epoch 40/2000][Batch 11/48][Autoencoder loss: 471.097778][C loss: 0.805079][M loss: 0.693263][D loss: -15688.843750][G loss 413386.343750 ]time: 0:29:13.904202 
[Epoch 40/2000][Batch 12/48][Autoencoder loss: 314.941010][C loss: 0.776018][M loss: 0.693262][D loss: -73530.093750][G loss 477093.531250 ]time: 0:29:14.334968 
[Epoch 40/2000][Batch 13/48][Autoencoder loss: 189.718292][C loss: 0.807978][M loss: 0.693263][D loss: -56062.187500][G loss 446158.468750 ]time: 0:29:15.169367 
[Epoch 40/2000][Batch 14/48][Autoencoder loss: 263.718628][C loss: 0.834636][M loss: 0.693262][D loss: -50247.296875][G loss 444874.187500 ]time: 0:29:15.873669 
[Epoch 40/2000][Batch 15/48][Autoencoder loss: 249.303177][C loss: 0.869075][M loss: 0.693261][D loss: -32083.742188][G loss 450626.468750 ]time: 0:29:16.306076 
[Epoch 40/2000][Batch 16/48][Autoencoder loss: 292.059448][C loss: 0.729280][M loss: 0.693261][D loss: 28313.179688][G loss 398921.062500 ]time: 0:29:16.743022 
[Epoch 40/2000][Batch 17/48][Autoencoder loss: 278.393555][C loss: 0.781164][M loss: 0.693262][D loss: 34294.710938][G loss 369509.437500 ]time: 0:29:17.160614 
[Epoch 40/2000][Batch 18/48][Autoencoder loss: 197.668854][C loss: 0.670389][M loss: 0.693261][D loss: 48308.820312][G loss 360929.343750 ]time: 0:29:17.575037 
[Epoch 40/2000][Batch 19/48][Autoencoder loss: 130.677277][C loss: 0.614673][M loss: 0.693261][D loss: 52177.273438][G loss 342677.500000 ]time: 0:29:17.998337 
[Epoch 40/2000][Batch 20/48][Autoencoder loss: 901.895081][C loss: 0.755064][M loss: 0.693280][D loss: 63302.992188][G loss 344622.656250 ]time: 0:29:18.424701 
[Epoch 40/2000][Batch 21/48][Autoencoder loss: 1134.681152][C loss: 1.029485][M loss: 0.693281][D loss: 80278.140625][G loss 320893.406250 ]time: 0:29:18.858974 
[Epoch 40/2000][Batch 22/48][Autoencoder loss: 1026.088135][C loss: 0.008598][M loss: 0.693272][D loss: 72542.515625][G loss 306773.406250 ]time: 0:29:19.287433 
[Epoch 40/2000][Batch 23/48][Autoencoder loss: 2095.166748][C loss: 0.048064][M loss: 0.693274][D loss: 90111.718750][G loss 274922.281250 ]time: 0:29:19.713792 
[Epoch 40/2000][Batch 24/48][Autoencoder loss: 2816.257812][C loss: 1.225607][M loss: 0.693263][D loss: 84803.546875][G loss 262826.875000 ]time: 0:29:20.607455 
[Epoch 40/2000][Batch 25/48][Autoencoder loss: 592.002930][C loss: 1.230726][M loss: 0.693262][D loss: 86945.539062][G loss 242485.843750 ]time: 0:29:21.509604 
[Epoch 40/2000][Batch 26/48][Autoencoder loss: 2449.500488][C loss: 1.101212][M loss: 0.693263][D loss: 96697.890625][G loss 227975.906250 ]time: 0:29:21.984253 
[Epoch 40/2000][Batch 27/48][Autoencoder loss: 2529.850830][C loss: 0.884987][M loss: 0.693263][D loss: 94676.000000][G loss 211632.828125 ]time: 0:29:22.416486 
[Epoch 40/2000][Batch 28/48][Autoencoder loss: 476.861969][C loss: 1.878563][M loss: 0.693265][D loss: 76741.531250][G loss 212081.250000 ]time: 0:29:22.849637 
[Epoch 40/2000][Batch 29/48][Autoencoder loss: 1094.433594][C loss: 1.459650][M loss: 0.693268][D loss: 70213.812500][G loss 187231.750000 ]time: 0:29:23.385396 
[Epoch 40/2000][Batch 30/48][Autoencoder loss: 2745.186035][C loss: 1.399614][M loss: 0.693265][D loss: 75294.000000][G loss 170986.921875 ]time: 0:29:24.540800 
[Epoch 40/2000][Batch 31/48][Autoencoder loss: 1052.642456][C loss: 1.041280][M loss: 0.693264][D loss: 74827.500000][G loss 166279.640625 ]time: 0:29:25.049059 
[Epoch 40/2000][Batch 32/48][Autoencoder loss: 275.279114][C loss: 1.352909][M loss: 0.693263][D loss: 113921.062500][G loss 114387.585938 ]time: 0:29:25.471606 
[Epoch 40/2000][Batch 33/48][Autoencoder loss: 1499.470825][C loss: 1.301953][M loss: 0.693261][D loss: 110672.421875][G loss 110530.484375 ]time: 0:29:25.907907 
[Epoch 40/2000][Batch 34/48][Autoencoder loss: 1383.212280][C loss: 1.194128][M loss: 0.693262][D loss: 104540.789062][G loss 95048.820312 ]time: 0:29:26.321581 
[Epoch 40/2000][Batch 35/48][Autoencoder loss: 139.541046][C loss: 1.016212][M loss: 0.693263][D loss: 93892.382812][G loss 89079.007812 ]time: 0:29:26.757900 
[Epoch 40/2000][Batch 36/48][Autoencoder loss: 944.025391][C loss: 0.899640][M loss: 0.693262][D loss: 89953.570312][G loss 81677.257812 ]time: 0:29:27.190023 
[Epoch 40/2000][Batch 37/48][Autoencoder loss: 1137.732544][C loss: 0.860032][M loss: 0.693262][D loss: 84588.226562][G loss 78113.570312 ]time: 0:29:27.637228 
[Epoch 40/2000][Batch 38/48][Autoencoder loss: 271.215515][C loss: 0.772828][M loss: 0.693261][D loss: 83034.906250][G loss 72075.273438 ]time: 0:29:28.075519 
[Epoch 40/2000][Batch 39/48][Autoencoder loss: 441.939392][C loss: 0.675888][M loss: 0.693261][D loss: 79814.421875][G loss 65385.457031 ]time: 0:29:28.501737 
[Epoch 40/2000][Batch 40/48][Autoencoder loss: 1158.163574][C loss: 0.732117][M loss: 0.693262][D loss: 72894.828125][G loss 61655.125000 ]time: 0:29:28.940891 
[Epoch 40/2000][Batch 41/48][Autoencoder loss: 492.511017][C loss: 0.639687][M loss: 0.693261][D loss: 71088.429688][G loss 55586.660156 ]time: 0:29:29.383568 
[Epoch 40/2000][Batch 42/48][Autoencoder loss: 501.537415][C loss: 0.564689][M loss: 0.693262][D loss: 64159.296875][G loss 49619.851562 ]time: 0:29:29.818119 
[Epoch 40/2000][Batch 43/48][Autoencoder loss: 692.099365][C loss: 0.511411][M loss: 0.693261][D loss: 60249.523438][G loss 44630.925781 ]time: 0:29:30.294078 
[Epoch 40/2000][Batch 44/48][Autoencoder loss: 454.751434][C loss: 1.187539][M loss: 0.693261][D loss: 55020.339844][G loss 43023.460938 ]time: 0:29:30.718920 
[Epoch 40/2000][Batch 45/48][Autoencoder loss: 96.260986][C loss: 1.169467][M loss: 0.693261][D loss: 54417.707031][G loss 40682.578125 ]time: 0:29:31.141746 
[Epoch 40/2000][Batch 46/48][Autoencoder loss: 340.323822][C loss: 1.046676][M loss: 0.693261][D loss: 50433.671875][G loss 36252.324219 ]time: 0:29:31.554690 
[Epoch 40/2000][Batch 47/48][Autoencoder loss: 444.346497][C loss: 0.868890][M loss: 0.693260][D loss: 46161.265625][G loss 34667.148438 ]time: 0:29:32.765049 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 2s
45/63 [====================>.........] - ETA: 0s
63/63 [==============================] - 0s 1ms/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 470us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 485us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 40/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 41/2000][Batch 0/48][Autoencoder loss: 598.281616][C loss: 1.221821][M loss: 0.693261][D loss: 36172.976562][G loss 38241.207031 ]time: 0:29:45.959542 
[Epoch 41/2000][Batch 1/48][Autoencoder loss: 382.856262][C loss: 1.379242][M loss: 0.693261][D loss: 31890.126953][G loss 36398.328125 ]time: 0:29:46.379660 
[Epoch 41/2000][Batch 2/48][Autoencoder loss: 693.769897][C loss: 1.323960][M loss: 0.693261][D loss: 31890.269531][G loss 32995.585938 ]time: 0:29:46.988944 
[Epoch 41/2000][Batch 3/48][Autoencoder loss: 362.290375][C loss: 1.258734][M loss: 0.693260][D loss: 30407.050781][G loss 30798.753906 ]time: 0:29:47.431808 
[Epoch 41/2000][Batch 4/48][Autoencoder loss: 223.486130][C loss: 1.161920][M loss: 0.693260][D loss: 29524.173828][G loss 30121.503906 ]time: 0:29:47.915811 
[Epoch 41/2000][Batch 5/48][Autoencoder loss: 818.529297][C loss: 1.393887][M loss: 0.693268][D loss: 28040.816406][G loss 27716.410156 ]time: 0:29:48.344636 
[Epoch 41/2000][Batch 6/48][Autoencoder loss: 746.134766][C loss: 1.020990][M loss: 0.693262][D loss: 26227.792969][G loss 25555.841797 ]time: 0:29:48.771893 
[Epoch 41/2000][Batch 7/48][Autoencoder loss: 257.767761][C loss: 0.909061][M loss: 0.693263][D loss: 23629.527344][G loss 23487.226562 ]time: 0:29:49.359750 
[Epoch 41/2000][Batch 8/48][Autoencoder loss: 227.072662][C loss: 1.227182][M loss: 0.693261][D loss: 27152.111328][G loss 17519.910156 ]time: 0:29:50.243208 
[Epoch 41/2000][Batch 9/48][Autoencoder loss: 476.551483][C loss: 1.190381][M loss: 0.693261][D loss: 24266.082031][G loss 16853.365234 ]time: 0:29:50.666606 
[Epoch 41/2000][Batch 10/48][Autoencoder loss: 346.742371][C loss: 1.057511][M loss: 0.693261][D loss: 23763.250000][G loss 15513.841797 ]time: 0:29:51.092279 
[Epoch 41/2000][Batch 11/48][Autoencoder loss: 234.849625][C loss: 0.877131][M loss: 0.693261][D loss: 22949.730469][G loss 14541.777344 ]time: 0:29:51.516712 
[Epoch 41/2000][Batch 12/48][Autoencoder loss: 326.676056][C loss: 0.649388][M loss: 0.693261][D loss: 18723.027344][G loss 16016.135742 ]time: 0:29:51.939965 
[Epoch 41/2000][Batch 13/48][Autoencoder loss: 287.468109][C loss: 0.632071][M loss: 0.693261][D loss: 15892.556641][G loss 14698.408203 ]time: 0:29:52.363694 
[Epoch 41/2000][Batch 14/48][Autoencoder loss: 123.371773][C loss: 0.615193][M loss: 0.693261][D loss: 15128.056641][G loss 13696.575195 ]time: 0:29:52.784767 
[Epoch 41/2000][Batch 15/48][Autoencoder loss: 151.907043][C loss: 0.632784][M loss: 0.693261][D loss: 14091.625977][G loss 12315.813477 ]time: 0:29:53.222590 
[Epoch 41/2000][Batch 16/48][Autoencoder loss: 369.210907][C loss: 0.697108][M loss: 0.693260][D loss: 13444.893555][G loss 11447.014648 ]time: 0:29:53.647536 
[Epoch 41/2000][Batch 17/48][Autoencoder loss: 167.688446][C loss: 0.723415][M loss: 0.693261][D loss: 12669.249023][G loss 10511.123047 ]time: 0:29:54.322821 
[Epoch 41/2000][Batch 18/48][Autoencoder loss: 99.072647][C loss: 0.653373][M loss: 0.693261][D loss: 12150.918945][G loss 9835.055664 ]time: 0:29:55.046996 
[Epoch 41/2000][Batch 19/48][Autoencoder loss: 152.491684][C loss: 0.581566][M loss: 0.693261][D loss: 10547.375000][G loss 9413.031250 ]time: 0:29:55.511720 
[Epoch 41/2000][Batch 20/48][Autoencoder loss: 3152.531250][C loss: 0.399852][M loss: 0.693270][D loss: 9496.437500][G loss 9085.752930 ]time: 0:29:55.960548 
[Epoch 41/2000][Batch 21/48][Autoencoder loss: 4638.661621][C loss: 0.045298][M loss: 0.693277][D loss: 8414.875977][G loss 8399.418945 ]time: 0:29:56.394332 
[Epoch 41/2000][Batch 22/48][Autoencoder loss: 3562.096924][C loss: 0.032729][M loss: 0.693281][D loss: 7138.760742][G loss 8019.504395 ]time: 0:29:56.817043 
[Epoch 41/2000][Batch 23/48][Autoencoder loss: 9655.368164][C loss: 0.078792][M loss: 0.693278][D loss: 6793.596191][G loss 7387.137207 ]time: 0:29:57.236626 
[Epoch 41/2000][Batch 24/48][Autoencoder loss: 9592.501953][C loss: 0.739475][M loss: 0.693263][D loss: 5501.751953][G loss 6802.640625 ]time: 0:29:58.130334 
[Epoch 41/2000][Batch 25/48][Autoencoder loss: 870.544983][C loss: 0.452333][M loss: 0.693262][D loss: 4995.866699][G loss 6485.625000 ]time: 0:29:58.560462 
[Epoch 41/2000][Batch 26/48][Autoencoder loss: 8826.388672][C loss: 0.389333][M loss: 0.693262][D loss: 4398.343262][G loss 5690.134766 ]time: 0:29:58.991455 
[Epoch 41/2000][Batch 27/48][Autoencoder loss: 9737.258789][C loss: 0.317158][M loss: 0.693261][D loss: 3322.449951][G loss 5350.867188 ]time: 0:29:59.425892 
[Epoch 41/2000][Batch 28/48][Autoencoder loss: 1385.184692][C loss: 1.413411][M loss: 0.693263][D loss: 1923.736694][G loss 5368.644043 ]time: 0:29:59.966348 
[Epoch 41/2000][Batch 29/48][Autoencoder loss: 3376.473877][C loss: 1.287234][M loss: 0.693265][D loss: 943.523315][G loss 4521.000977 ]time: 0:30:00.383701 
[Epoch 41/2000][Batch 30/48][Autoencoder loss: 8864.461914][C loss: 1.278279][M loss: 0.693263][D loss: 575.354614][G loss 4073.528076 ]time: 0:30:00.859742 
[Epoch 41/2000][Batch 31/48][Autoencoder loss: 4120.208008][C loss: 1.102041][M loss: 0.693263][D loss: -99.114624][G loss 3634.997070 ]time: 0:30:01.289542 
[Epoch 41/2000][Batch 32/48][Autoencoder loss: 360.284821][C loss: 1.309847][M loss: 0.693264][D loss: -222.220032][G loss 2579.309814 ]time: 0:30:01.718377 
[Epoch 41/2000][Batch 33/48][Autoencoder loss: 4777.252930][C loss: 1.186034][M loss: 0.693262][D loss: -1098.017090][G loss 2046.812866 ]time: 0:30:02.141945 
[Epoch 41/2000][Batch 34/48][Autoencoder loss: 4855.770020][C loss: 0.985712][M loss: 0.693262][D loss: -1914.152222][G loss 1807.185303 ]time: 0:30:02.572108 
[Epoch 41/2000][Batch 35/48][Autoencoder loss: 307.687103][C loss: 0.765246][M loss: 0.693265][D loss: -2639.620850][G loss 1380.241333 ]time: 0:30:02.987865 
[Epoch 41/2000][Batch 36/48][Autoencoder loss: 2736.458740][C loss: 0.751169][M loss: 0.693266][D loss: -3417.322021][G loss 995.406006 ]time: 0:30:03.424886 
[Epoch 41/2000][Batch 37/48][Autoencoder loss: 4148.638184][C loss: 0.668839][M loss: 0.693263][D loss: -4391.598145][G loss 633.443237 ]time: 0:30:03.906849 
[Epoch 41/2000][Batch 38/48][Autoencoder loss: 1476.118530][C loss: 0.576444][M loss: 0.693261][D loss: -5372.237793][G loss 222.292465 ]time: 0:30:04.328831 
[Epoch 41/2000][Batch 39/48][Autoencoder loss: 860.409607][C loss: 0.513102][M loss: 0.693262][D loss: -6360.359863][G loss -183.169006 ]time: 0:30:04.922210 
[Epoch 41/2000][Batch 40/48][Autoencoder loss: 3030.948975][C loss: 0.819268][M loss: 0.693262][D loss: -7197.884277][G loss -593.719543 ]time: 0:30:06.061253 
[Epoch 41/2000][Batch 41/48][Autoencoder loss: 1959.880249][C loss: 0.864787][M loss: 0.693261][D loss: -7813.368164][G loss -1021.345154 ]time: 0:30:06.997249 
[Epoch 41/2000][Batch 42/48][Autoencoder loss: 551.193054][C loss: 0.733010][M loss: 0.693263][D loss: -8598.457031][G loss -1370.551270 ]time: 0:30:07.442631 
[Epoch 41/2000][Batch 43/48][Autoencoder loss: 1350.301025][C loss: 0.596754][M loss: 0.693261][D loss: -9250.267578][G loss -1901.834839 ]time: 0:30:07.876950 
[Epoch 41/2000][Batch 44/48][Autoencoder loss: 1803.556763][C loss: 1.190126][M loss: 0.693262][D loss: -10271.437500][G loss -2206.547852 ]time: 0:30:08.320588 
[Epoch 41/2000][Batch 45/48][Autoencoder loss: 628.781921][C loss: 1.197422][M loss: 0.693262][D loss: -11494.620117][G loss -2689.081299 ]time: 0:30:08.757690 
[Epoch 41/2000][Batch 46/48][Autoencoder loss: 342.837250][C loss: 1.138648][M loss: 0.693261][D loss: -12544.288086][G loss -3195.643311 ]time: 0:30:09.194756 
[Epoch 41/2000][Batch 47/48][Autoencoder loss: 1272.605835][C loss: 0.996544][M loss: 0.693261][D loss: -13551.291992][G loss -3455.476074 ]time: 0:30:09.621469 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 630us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 377us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 451us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 41/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 42/2000][Batch 0/48][Autoencoder loss: 1096.766235][C loss: 1.166296][M loss: 0.693262][D loss: -13305.406250][G loss -4834.168457 ]time: 0:30:20.211023 
[Epoch 42/2000][Batch 1/48][Autoencoder loss: 316.344391][C loss: 1.259568][M loss: 0.693262][D loss: -13819.705078][G loss -5529.239746 ]time: 0:30:20.639448 
[Epoch 42/2000][Batch 2/48][Autoencoder loss: 657.428650][C loss: 1.212963][M loss: 0.693261][D loss: -14713.300781][G loss -6006.684570 ]time: 0:30:21.066036 
[Epoch 42/2000][Batch 3/48][Autoencoder loss: 989.607056][C loss: 1.179329][M loss: 0.693261][D loss: -16009.215820][G loss -6640.970703 ]time: 0:30:21.495879 
[Epoch 42/2000][Batch 4/48][Autoencoder loss: 538.559509][C loss: 1.267806][M loss: 0.693264][D loss: -17269.587891][G loss -7000.984375 ]time: 0:30:21.945667 
[Epoch 42/2000][Batch 5/48][Autoencoder loss: 730.464661][C loss: 1.294862][M loss: 0.693291][D loss: -18797.296875][G loss -7691.059570 ]time: 0:30:22.356658 
[Epoch 42/2000][Batch 6/48][Autoencoder loss: 1013.032776][C loss: 1.078751][M loss: 0.693266][D loss: -19294.919922][G loss -8565.844727 ]time: 0:30:22.779031 
[Epoch 42/2000][Batch 7/48][Autoencoder loss: 558.494019][C loss: 0.860997][M loss: 0.693262][D loss: -20215.128906][G loss -9033.954102 ]time: 0:30:23.626059 
[Epoch 42/2000][Batch 8/48][Autoencoder loss: 255.164520][C loss: 1.253823][M loss: 0.693264][D loss: -22249.214844][G loss -7494.246582 ]time: 0:30:24.359088 
[Epoch 42/2000][Batch 9/48][Autoencoder loss: 566.330444][C loss: 1.295198][M loss: 0.693266][D loss: -23483.285156][G loss -8002.099609 ]time: 0:30:24.796135 
[Epoch 42/2000][Batch 10/48][Autoencoder loss: 595.580078][C loss: 1.266222][M loss: 0.693267][D loss: -25244.251953][G loss -8667.824219 ]time: 0:30:25.231540 
[Epoch 42/2000][Batch 11/48][Autoencoder loss: 326.013550][C loss: 1.157936][M loss: 0.693264][D loss: -26286.693359][G loss -9150.008789 ]time: 0:30:25.680836 
[Epoch 42/2000][Batch 12/48][Autoencoder loss: 334.933868][C loss: 0.711931][M loss: 0.693262][D loss: -26193.031250][G loss -11045.212891 ]time: 0:30:26.208049 
[Epoch 42/2000][Batch 13/48][Autoencoder loss: 465.044952][C loss: 0.670892][M loss: 0.693262][D loss: -27327.267578][G loss -12198.530273 ]time: 0:30:26.630857 
[Epoch 42/2000][Batch 14/48][Autoencoder loss: 288.405426][C loss: 0.613777][M loss: 0.693262][D loss: -27889.230469][G loss -12838.472656 ]time: 0:30:27.070495 
[Epoch 42/2000][Batch 15/48][Autoencoder loss: 156.603027][C loss: 0.583233][M loss: 0.693262][D loss: -28885.062500][G loss -13270.296875 ]time: 0:30:27.510480 
[Epoch 42/2000][Batch 16/48][Autoencoder loss: 417.511322][C loss: 0.530955][M loss: 0.693261][D loss: -30704.158203][G loss -13208.366211 ]time: 0:30:28.265107 
[Epoch 42/2000][Batch 17/48][Autoencoder loss: 308.887177][C loss: 0.558434][M loss: 0.693261][D loss: -32588.742188][G loss -14488.245117 ]time: 0:30:29.297439 
[Epoch 42/2000][Batch 18/48][Autoencoder loss: 144.563080][C loss: 0.529257][M loss: 0.693261][D loss: -33674.687500][G loss -15161.371094 ]time: 0:30:29.974560 
[Epoch 42/2000][Batch 19/48][Autoencoder loss: 156.258926][C loss: 0.535558][M loss: 0.693261][D loss: -34914.230469][G loss -16422.736328 ]time: 0:30:30.459772 
[Epoch 42/2000][Batch 20/48][Autoencoder loss: 1127.069580][C loss: 0.373915][M loss: 0.693267][D loss: -36742.304688][G loss -17210.589844 ]time: 0:30:30.896278 
[Epoch 42/2000][Batch 21/48][Autoencoder loss: 2286.192871][C loss: 0.017712][M loss: 0.693270][D loss: -37077.429688][G loss -18080.437500 ]time: 0:30:31.330969 
[Epoch 42/2000][Batch 22/48][Autoencoder loss: 393.948364][C loss: 0.011054][M loss: 0.693266][D loss: -37298.421875][G loss -19379.212891 ]time: 0:30:32.153841 
[Epoch 42/2000][Batch 23/48][Autoencoder loss: 2469.545654][C loss: 0.002425][M loss: 0.693269][D loss: -39034.921875][G loss -19619.917969 ]time: 0:30:32.731429 
[Epoch 42/2000][Batch 24/48][Autoencoder loss: 4162.598145][C loss: 0.797234][M loss: 0.693262][D loss: -40458.449219][G loss -21877.099609 ]time: 0:30:33.165861 
[Epoch 42/2000][Batch 25/48][Autoencoder loss: 833.349731][C loss: 0.534829][M loss: 0.693264][D loss: -44214.328125][G loss -22791.591797 ]time: 0:30:33.624334 
[Epoch 42/2000][Batch 26/48][Autoencoder loss: 1718.227417][C loss: 0.533084][M loss: 0.693262][D loss: -47513.648438][G loss -24330.873047 ]time: 0:30:34.058391 
[Epoch 42/2000][Batch 27/48][Autoencoder loss: 3475.902100][C loss: 0.430893][M loss: 0.693262][D loss: -46855.156250][G loss -25054.958984 ]time: 0:30:34.493266 
[Epoch 42/2000][Batch 28/48][Autoencoder loss: 1642.678101][C loss: 1.593595][M loss: 0.693264][D loss: -45755.734375][G loss -27027.732422 ]time: 0:30:34.909926 
[Epoch 42/2000][Batch 29/48][Autoencoder loss: 675.269470][C loss: 1.394365][M loss: 0.693266][D loss: -45114.531250][G loss -29337.839844 ]time: 0:30:35.334499 
[Epoch 42/2000][Batch 30/48][Autoencoder loss: 2503.551514][C loss: 1.383349][M loss: 0.693264][D loss: -47552.230469][G loss -31050.156250 ]time: 0:30:35.838585 
[Epoch 42/2000][Batch 31/48][Autoencoder loss: 2144.162842][C loss: 1.185839][M loss: 0.693262][D loss: -51046.851562][G loss -33055.785156 ]time: 0:30:36.296067 
[Epoch 42/2000][Batch 32/48][Autoencoder loss: 331.459595][C loss: 1.010862][M loss: 0.693262][D loss: -60441.093750][G loss -27770.882812 ]time: 0:30:36.714646 
[Epoch 42/2000][Batch 33/48][Autoencoder loss: 617.081909][C loss: 0.966244][M loss: 0.693261][D loss: -63867.406250][G loss -29648.804688 ]time: 0:30:37.128306 
[Epoch 42/2000][Batch 34/48][Autoencoder loss: 1797.923584][C loss: 0.906140][M loss: 0.693261][D loss: -65235.421875][G loss -31577.058594 ]time: 0:30:37.555944 
[Epoch 42/2000][Batch 35/48][Autoencoder loss: 808.443542][C loss: 0.771584][M loss: 0.693261][D loss: -63483.710938][G loss -32081.503906 ]time: 0:30:37.972024 
[Epoch 42/2000][Batch 36/48][Autoencoder loss: 166.739227][C loss: 0.784058][M loss: 0.693260][D loss: -65922.734375][G loss -34894.925781 ]time: 0:30:38.436758 
[Epoch 42/2000][Batch 37/48][Autoencoder loss: 981.237244][C loss: 0.702967][M loss: 0.693261][D loss: -66840.117188][G loss -36854.917969 ]time: 0:30:38.851352 
[Epoch 42/2000][Batch 38/48][Autoencoder loss: 1082.838257][C loss: 0.622131][M loss: 0.693261][D loss: -68709.140625][G loss -37576.230469 ]time: 0:30:39.387780 
[Epoch 42/2000][Batch 39/48][Autoencoder loss: 292.218170][C loss: 0.559110][M loss: 0.693260][D loss: -72951.781250][G loss -39745.109375 ]time: 0:30:40.186390 
[Epoch 42/2000][Batch 40/48][Autoencoder loss: 396.976349][C loss: 0.786231][M loss: 0.693262][D loss: -77330.710938][G loss -43163.988281 ]time: 0:30:40.959616 
[Epoch 42/2000][Batch 41/48][Autoencoder loss: 959.607300][C loss: 0.785231][M loss: 0.693261][D loss: -78197.312500][G loss -45809.464844 ]time: 0:30:41.383375 
[Epoch 42/2000][Batch 42/48][Autoencoder loss: 720.598450][C loss: 0.733980][M loss: 0.693261][D loss: -81195.015625][G loss -48697.234375 ]time: 0:30:41.812075 
[Epoch 42/2000][Batch 43/48][Autoencoder loss: 190.147034][C loss: 0.670898][M loss: 0.693261][D loss: -78482.554688][G loss -51103.132812 ]time: 0:30:42.237329 
[Epoch 42/2000][Batch 44/48][Autoencoder loss: 377.900330][C loss: 0.981640][M loss: 0.693261][D loss: -81930.921875][G loss -51415.613281 ]time: 0:30:42.658561 
[Epoch 42/2000][Batch 45/48][Autoencoder loss: 568.667786][C loss: 0.977633][M loss: 0.693261][D loss: -85238.023438][G loss -57707.984375 ]time: 0:30:43.082593 
[Epoch 42/2000][Batch 46/48][Autoencoder loss: 228.578781][C loss: 0.932762][M loss: 0.693261][D loss: -86577.000000][G loss -59454.632812 ]time: 0:30:43.495105 
[Epoch 42/2000][Batch 47/48][Autoencoder loss: 113.866859][C loss: 0.838354][M loss: 0.693261][D loss: -91590.984375][G loss -61508.917969 ]time: 0:30:43.911944 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 537us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 538us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 493us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 42/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 43/2000][Batch 0/48][Autoencoder loss: 588.785400][C loss: 1.054734][M loss: 0.693262][D loss: -78794.226562][G loss -80052.414062 ]time: 0:31:00.953271 
[Epoch 43/2000][Batch 1/48][Autoencoder loss: 440.879028][C loss: 1.201051][M loss: 0.693263][D loss: -80829.382812][G loss -87062.679688 ]time: 0:31:01.387962 
[Epoch 43/2000][Batch 2/48][Autoencoder loss: 124.445915][C loss: 1.084390][M loss: 0.693263][D loss: -76387.117188][G loss -89603.867188 ]time: 0:31:01.816803 
[Epoch 43/2000][Batch 3/48][Autoencoder loss: 181.213379][C loss: 1.034440][M loss: 0.693261][D loss: -78941.703125][G loss -94140.054688 ]time: 0:31:02.242565 
[Epoch 43/2000][Batch 4/48][Autoencoder loss: 451.113739][C loss: 0.990742][M loss: 0.693261][D loss: -81325.921875][G loss -95401.218750 ]time: 0:31:02.655694 
[Epoch 43/2000][Batch 5/48][Autoencoder loss: 570.500427][C loss: 0.995388][M loss: 0.693263][D loss: -86200.546875][G loss -99685.578125 ]time: 0:31:03.132152 
[Epoch 43/2000][Batch 6/48][Autoencoder loss: 399.676086][C loss: 0.908083][M loss: 0.693261][D loss: -84658.421875][G loss -108645.101562 ]time: 0:31:04.129606 
[Epoch 43/2000][Batch 7/48][Autoencoder loss: 343.242188][C loss: 0.810250][M loss: 0.693262][D loss: -83757.218750][G loss -112965.460938 ]time: 0:31:05.040627 
[Epoch 43/2000][Batch 8/48][Autoencoder loss: 244.580658][C loss: 1.087801][M loss: 0.693260][D loss: -101642.359375][G loss -91232.851562 ]time: 0:31:05.635664 
[Epoch 43/2000][Batch 9/48][Autoencoder loss: 207.696533][C loss: 1.057773][M loss: 0.693260][D loss: -105196.710938][G loss -94683.429688 ]time: 0:31:06.122451 
[Epoch 43/2000][Batch 10/48][Autoencoder loss: 268.021667][C loss: 0.971886][M loss: 0.693260][D loss: -103465.390625][G loss -100893.546875 ]time: 0:31:06.576460 
[Epoch 43/2000][Batch 11/48][Autoencoder loss: 300.824677][C loss: 0.834387][M loss: 0.693261][D loss: -108283.953125][G loss -103740.078125 ]time: 0:31:07.019422 
[Epoch 43/2000][Batch 12/48][Autoencoder loss: 233.288498][C loss: 0.555811][M loss: 0.693260][D loss: -87205.132812][G loss -125976.539062 ]time: 0:31:07.437898 
[Epoch 43/2000][Batch 13/48][Autoencoder loss: 166.471939][C loss: 0.531890][M loss: 0.693261][D loss: -89392.070312][G loss -132012.671875 ]time: 0:31:07.858795 
[Epoch 43/2000][Batch 14/48][Autoencoder loss: 161.604218][C loss: 0.520735][M loss: 0.693261][D loss: -84275.460938][G loss -140461.234375 ]time: 0:31:08.295300 
[Epoch 43/2000][Batch 15/48][Autoencoder loss: 140.375809][C loss: 0.532373][M loss: 0.693261][D loss: -86780.359375][G loss -139884.265625 ]time: 0:31:08.738812 
[Epoch 43/2000][Batch 16/48][Autoencoder loss: 226.758698][C loss: 0.520235][M loss: 0.693261][D loss: -84141.226562][G loss -139322.484375 ]time: 0:31:09.168703 
[Epoch 43/2000][Batch 17/48][Autoencoder loss: 167.396805][C loss: 0.558867][M loss: 0.693261][D loss: -84106.937500][G loss -149363.968750 ]time: 0:31:09.605978 
[Epoch 43/2000][Batch 18/48][Autoencoder loss: 132.895416][C loss: 0.495913][M loss: 0.693261][D loss: -86452.773438][G loss -159438.031250 ]time: 0:31:10.029839 
[Epoch 43/2000][Batch 19/48][Autoencoder loss: 127.666275][C loss: 0.466563][M loss: 0.693260][D loss: -75483.148438][G loss -162381.734375 ]time: 0:31:10.499290 
[Epoch 43/2000][Batch 20/48][Autoencoder loss: 789.661743][C loss: 0.302267][M loss: 0.693273][D loss: -64610.742188][G loss -172174.796875 ]time: 0:31:10.976481 
[Epoch 43/2000][Batch 21/48][Autoencoder loss: 1844.307373][C loss: 0.018308][M loss: 0.693275][D loss: -65613.367188][G loss -180019.890625 ]time: 0:31:11.412287 
[Epoch 43/2000][Batch 22/48][Autoencoder loss: 291.105652][C loss: 0.000172][M loss: 0.693272][D loss: -45199.609375][G loss -191034.484375 ]time: 0:31:11.850190 
[Epoch 43/2000][Batch 23/48][Autoencoder loss: 1693.621338][C loss: 0.000146][M loss: 0.693281][D loss: -47539.171875][G loss -192525.203125 ]time: 0:31:12.282988 
[Epoch 43/2000][Batch 24/48][Autoencoder loss: 1856.697021][C loss: 0.760385][M loss: 0.693263][D loss: -44879.578125][G loss -199361.218750 ]time: 0:31:13.212549 
[Epoch 43/2000][Batch 25/48][Autoencoder loss: 564.401733][C loss: 0.660984][M loss: 0.693262][D loss: -46017.242188][G loss -206249.515625 ]time: 0:31:13.685558 
[Epoch 43/2000][Batch 26/48][Autoencoder loss: 1088.241211][C loss: 0.719111][M loss: 0.693264][D loss: -45101.210938][G loss -222480.968750 ]time: 0:31:14.161116 
[Epoch 43/2000][Batch 27/48][Autoencoder loss: 1548.432373][C loss: 0.619497][M loss: 0.693262][D loss: -33301.156250][G loss -233307.390625 ]time: 0:31:15.031643 
[Epoch 43/2000][Batch 28/48][Autoencoder loss: 630.366882][C loss: 1.545169][M loss: 0.693263][D loss: -7743.585938][G loss -238947.968750 ]time: 0:31:15.584502 
[Epoch 43/2000][Batch 29/48][Autoencoder loss: 546.499939][C loss: 1.293343][M loss: 0.693262][D loss: 14870.703125][G loss -245779.328125 ]time: 0:31:16.023572 
[Epoch 43/2000][Batch 30/48][Autoencoder loss: 1441.818604][C loss: 1.341129][M loss: 0.693262][D loss: 17574.609375][G loss -249256.296875 ]time: 0:31:16.451158 
[Epoch 43/2000][Batch 31/48][Autoencoder loss: 1069.535889][C loss: 1.085963][M loss: 0.693261][D loss: 18047.109375][G loss -254270.140625 ]time: 0:31:16.876471 
[Epoch 43/2000][Batch 32/48][Autoencoder loss: 202.283859][C loss: 0.949125][M loss: 0.693262][D loss: -34059.156250][G loss -209153.812500 ]time: 0:31:17.301093 
[Epoch 43/2000][Batch 33/48][Autoencoder loss: 521.751221][C loss: 0.940540][M loss: 0.693261][D loss: -31870.023438][G loss -206549.968750 ]time: 0:31:17.728918 
[Epoch 43/2000][Batch 34/48][Autoencoder loss: 921.391663][C loss: 0.942824][M loss: 0.693261][D loss: -25969.429688][G loss -212651.531250 ]time: 0:31:18.161125 
[Epoch 43/2000][Batch 35/48][Autoencoder loss: 341.829285][C loss: 0.837807][M loss: 0.693261][D loss: -12142.765625][G loss -221058.250000 ]time: 0:31:18.585218 
[Epoch 43/2000][Batch 36/48][Autoencoder loss: 145.736679][C loss: 0.795699][M loss: 0.693261][D loss: -3029.437500][G loss -216261.937500 ]time: 0:31:19.002564 
[Epoch 43/2000][Batch 37/48][Autoencoder loss: 573.026978][C loss: 0.742935][M loss: 0.693261][D loss: 8538.816406][G loss -223356.812500 ]time: 0:31:19.490037 
[Epoch 43/2000][Batch 38/48][Autoencoder loss: 497.595367][C loss: 0.669094][M loss: 0.693261][D loss: 7555.140625][G loss -220958.953125 ]time: 0:31:19.921500 
[Epoch 43/2000][Batch 39/48][Autoencoder loss: 120.992310][C loss: 0.593283][M loss: 0.693261][D loss: 12990.312500][G loss -230442.203125 ]time: 0:31:20.351886 
[Epoch 43/2000][Batch 40/48][Autoencoder loss: 334.762634][C loss: 0.739517][M loss: 0.693261][D loss: 29156.687500][G loss -234953.140625 ]time: 0:31:20.968551 
[Epoch 43/2000][Batch 41/48][Autoencoder loss: 568.873047][C loss: 0.727467][M loss: 0.693262][D loss: 33204.765625][G loss -233740.578125 ]time: 0:31:21.621322 
[Epoch 43/2000][Batch 42/48][Autoencoder loss: 435.378113][C loss: 0.676128][M loss: 0.693261][D loss: 29836.953125][G loss -224248.937500 ]time: 0:31:22.054465 
[Epoch 43/2000][Batch 43/48][Autoencoder loss: 179.551163][C loss: 0.620076][M loss: 0.693261][D loss: 41294.675781][G loss -242866.796875 ]time: 0:31:22.494120 
[Epoch 43/2000][Batch 44/48][Autoencoder loss: 245.327560][C loss: 0.843796][M loss: 0.693261][D loss: 28856.746094][G loss -229412.968750 ]time: 0:31:22.963420 
[Epoch 43/2000][Batch 45/48][Autoencoder loss: 278.435913][C loss: 0.831871][M loss: 0.693261][D loss: 39228.187500][G loss -222486.390625 ]time: 0:31:23.398080 
[Epoch 43/2000][Batch 46/48][Autoencoder loss: 111.502007][C loss: 0.789995][M loss: 0.693262][D loss: 35148.324219][G loss -226654.531250 ]time: 0:31:23.814566 
[Epoch 43/2000][Batch 47/48][Autoencoder loss: 106.476822][C loss: 0.716194][M loss: 0.693261][D loss: 51207.386719][G loss -215623.234375 ]time: 0:31:24.248509 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 434us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 516us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 530us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 43/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 44/2000][Batch 0/48][Autoencoder loss: 380.692322][C loss: 0.771048][M loss: 0.693262][D loss: 113052.335938][G loss -284564.125000 ]time: 0:31:38.922578 
[Epoch 44/2000][Batch 1/48][Autoencoder loss: 256.155670][C loss: 0.869704][M loss: 0.693261][D loss: 117546.429688][G loss -272655.406250 ]time: 0:31:39.339311 
[Epoch 44/2000][Batch 2/48][Autoencoder loss: 90.421127][C loss: 0.847948][M loss: 0.693260][D loss: 121978.109375][G loss -267968.656250 ]time: 0:31:39.758244 
[Epoch 44/2000][Batch 3/48][Autoencoder loss: 106.538506][C loss: 0.834359][M loss: 0.693261][D loss: 120086.273438][G loss -272001.156250 ]time: 0:31:40.186805 
[Epoch 44/2000][Batch 4/48][Autoencoder loss: 244.624939][C loss: 0.844024][M loss: 0.693260][D loss: 113366.781250][G loss -262105.437500 ]time: 0:31:40.605567 
[Epoch 44/2000][Batch 5/48][Autoencoder loss: 404.683105][C loss: 0.802610][M loss: 0.693264][D loss: 112907.468750][G loss -250638.437500 ]time: 0:31:41.027698 
[Epoch 44/2000][Batch 6/48][Autoencoder loss: 307.739197][C loss: 0.665535][M loss: 0.693262][D loss: 114289.148438][G loss -244274.718750 ]time: 0:31:41.448626 
[Epoch 44/2000][Batch 7/48][Autoencoder loss: 206.898697][C loss: 0.650069][M loss: 0.693261][D loss: 108419.015625][G loss -241327.609375 ]time: 0:31:41.864021 
[Epoch 44/2000][Batch 8/48][Autoencoder loss: 193.688400][C loss: 1.141286][M loss: 0.693261][D loss: 51987.320312][G loss -168336.031250 ]time: 0:31:42.279012 
[Epoch 44/2000][Batch 9/48][Autoencoder loss: 213.190628][C loss: 1.086172][M loss: 0.693261][D loss: 53516.898438][G loss -162184.203125 ]time: 0:31:42.702644 
[Epoch 44/2000][Batch 10/48][Autoencoder loss: 235.132812][C loss: 0.968472][M loss: 0.693261][D loss: 48922.902344][G loss -158341.000000 ]time: 0:31:43.141342 
[Epoch 44/2000][Batch 11/48][Autoencoder loss: 223.750641][C loss: 0.840513][M loss: 0.693261][D loss: 51283.687500][G loss -153766.281250 ]time: 0:31:43.563987 
[Epoch 44/2000][Batch 12/48][Autoencoder loss: 176.862274][C loss: 0.537061][M loss: 0.693261][D loss: 83819.968750][G loss -172156.640625 ]time: 0:31:43.973424 
[Epoch 44/2000][Batch 13/48][Autoencoder loss: 138.622528][C loss: 0.523558][M loss: 0.693261][D loss: 79742.804688][G loss -161449.890625 ]time: 0:31:44.427204 
[Epoch 44/2000][Batch 14/48][Autoencoder loss: 107.221970][C loss: 0.520517][M loss: 0.693261][D loss: 80633.445312][G loss -152982.968750 ]time: 0:31:44.848882 
[Epoch 44/2000][Batch 15/48][Autoencoder loss: 87.143768][C loss: 0.540972][M loss: 0.693261][D loss: 77085.703125][G loss -148001.796875 ]time: 0:31:45.343019 
[Epoch 44/2000][Batch 16/48][Autoencoder loss: 217.877106][C loss: 0.540503][M loss: 0.693261][D loss: 66084.132812][G loss -122611.835938 ]time: 0:31:45.838580 
[Epoch 44/2000][Batch 17/48][Autoencoder loss: 151.139160][C loss: 0.585142][M loss: 0.693262][D loss: 56465.210938][G loss -117802.851562 ]time: 0:31:46.828759 
[Epoch 44/2000][Batch 18/48][Autoencoder loss: 110.115829][C loss: 0.524554][M loss: 0.693261][D loss: 54048.832031][G loss -107114.867188 ]time: 0:31:47.765949 
[Epoch 44/2000][Batch 19/48][Autoencoder loss: 108.240479][C loss: 0.490635][M loss: 0.693261][D loss: 51691.789062][G loss -95415.273438 ]time: 0:31:48.760576 
[Epoch 44/2000][Batch 20/48][Autoencoder loss: 655.027588][C loss: 0.485139][M loss: 0.693263][D loss: 55641.367188][G loss -92130.242188 ]time: 0:31:49.302304 
[Epoch 44/2000][Batch 21/48][Autoencoder loss: 846.190002][C loss: 0.016662][M loss: 0.693270][D loss: 46762.996094][G loss -82729.031250 ]time: 0:31:49.781491 
[Epoch 44/2000][Batch 22/48][Autoencoder loss: 340.144012][C loss: 0.009446][M loss: 0.693275][D loss: 46215.535156][G loss -73794.273438 ]time: 0:31:50.211901 
[Epoch 44/2000][Batch 23/48][Autoencoder loss: 312.600861][C loss: 0.004731][M loss: 0.693269][D loss: 39582.304688][G loss -61675.250000 ]time: 0:31:50.688739 
[Epoch 44/2000][Batch 24/48][Autoencoder loss: 1267.790649][C loss: 0.490968][M loss: 0.693262][D loss: 32549.062500][G loss -52669.425781 ]time: 0:31:51.138559 
[Epoch 44/2000][Batch 25/48][Autoencoder loss: 783.389282][C loss: 0.394336][M loss: 0.693263][D loss: 29030.927734][G loss -40728.949219 ]time: 0:31:51.587448 
[Epoch 44/2000][Batch 26/48][Autoencoder loss: 638.227722][C loss: 0.469951][M loss: 0.693261][D loss: 21167.847656][G loss -28745.148438 ]time: 0:31:52.035242 
[Epoch 44/2000][Batch 27/48][Autoencoder loss: 862.519531][C loss: 0.449483][M loss: 0.693261][D loss: 15107.105469][G loss -17307.337891 ]time: 0:31:52.471509 
[Epoch 44/2000][Batch 28/48][Autoencoder loss: 809.496216][C loss: 1.607367][M loss: 0.693262][D loss: 10094.118164][G loss -4686.159180 ]time: 0:31:52.899016 
[Epoch 44/2000][Batch 29/48][Autoencoder loss: 476.156372][C loss: 1.387267][M loss: 0.693263][D loss: 2219.953613][G loss 10029.383789 ]time: 0:31:53.323296 
[Epoch 44/2000][Batch 30/48][Autoencoder loss: 637.480225][C loss: 1.637322][M loss: 0.693263][D loss: -7293.615234][G loss 26744.050781 ]time: 0:31:53.792034 
[Epoch 44/2000][Batch 31/48][Autoencoder loss: 590.317139][C loss: 1.239256][M loss: 0.693261][D loss: -16826.873047][G loss 42564.285156 ]time: 0:31:54.214921 
[Epoch 44/2000][Batch 32/48][Autoencoder loss: 196.761414][C loss: 0.891069][M loss: 0.693261][D loss: -17591.517578][G loss 45547.589844 ]time: 0:31:54.693896 
[Epoch 44/2000][Batch 33/48][Autoencoder loss: 231.909470][C loss: 0.851114][M loss: 0.693261][D loss: -22851.687500][G loss 57220.152344 ]time: 0:31:55.184623 
[Epoch 44/2000][Batch 34/48][Autoencoder loss: 449.453888][C loss: 0.805186][M loss: 0.693262][D loss: -29312.490234][G loss 71572.718750 ]time: 0:31:55.853616 
[Epoch 44/2000][Batch 35/48][Autoencoder loss: 337.544617][C loss: 0.735055][M loss: 0.693261][D loss: -33908.394531][G loss 80900.617188 ]time: 0:31:56.269886 
[Epoch 44/2000][Batch 36/48][Autoencoder loss: 170.672760][C loss: 0.782017][M loss: 0.693261][D loss: -42448.410156][G loss 93699.296875 ]time: 0:31:56.692130 
[Epoch 44/2000][Batch 37/48][Autoencoder loss: 293.150269][C loss: 0.738209][M loss: 0.693261][D loss: -48738.035156][G loss 108052.953125 ]time: 0:31:57.117309 
[Epoch 44/2000][Batch 38/48][Autoencoder loss: 291.243286][C loss: 0.655075][M loss: 0.693261][D loss: -56927.050781][G loss 120322.101562 ]time: 0:31:57.547752 
[Epoch 44/2000][Batch 39/48][Autoencoder loss: 143.359283][C loss: 0.573151][M loss: 0.693260][D loss: -61841.804688][G loss 130203.453125 ]time: 0:31:57.981331 
[Epoch 44/2000][Batch 40/48][Autoencoder loss: 212.133652][C loss: 0.708838][M loss: 0.693261][D loss: -67148.898438][G loss 149672.171875 ]time: 0:31:58.411006 
[Epoch 44/2000][Batch 41/48][Autoencoder loss: 343.374420][C loss: 0.710793][M loss: 0.693261][D loss: -72936.546875][G loss 158825.968750 ]time: 0:31:59.083182 
[Epoch 44/2000][Batch 42/48][Autoencoder loss: 395.081757][C loss: 0.667474][M loss: 0.693261][D loss: -71603.312500][G loss 162667.062500 ]time: 0:31:59.686783 
[Epoch 44/2000][Batch 43/48][Autoencoder loss: 182.321289][C loss: 0.615214][M loss: 0.693261][D loss: -79258.382812][G loss 175369.250000 ]time: 0:32:00.102253 
[Epoch 44/2000][Batch 44/48][Autoencoder loss: 128.421799][C loss: 0.773374][M loss: 0.693261][D loss: -79252.796875][G loss 179564.437500 ]time: 0:32:00.510722 
[Epoch 44/2000][Batch 45/48][Autoencoder loss: 155.744980][C loss: 0.760159][M loss: 0.693260][D loss: -86374.851562][G loss 189594.234375 ]time: 0:32:00.938209 
[Epoch 44/2000][Batch 46/48][Autoencoder loss: 103.625557][C loss: 0.716855][M loss: 0.693261][D loss: -91782.093750][G loss 200704.562500 ]time: 0:32:01.394576 
[Epoch 44/2000][Batch 47/48][Autoencoder loss: 83.560776][C loss: 0.651894][M loss: 0.693260][D loss: -95303.718750][G loss 218391.093750 ]time: 0:32:01.805367 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 528us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 474us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 395us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 44/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 45/2000][Batch 0/48][Autoencoder loss: 284.089447][C loss: 0.815411][M loss: 0.693262][D loss: -168073.546875][G loss 315815.781250 ]time: 0:32:13.791621 
[Epoch 45/2000][Batch 1/48][Autoencoder loss: 240.511658][C loss: 0.951814][M loss: 0.693261][D loss: -169848.078125][G loss 310579.937500 ]time: 0:32:14.216126 
[Epoch 45/2000][Batch 2/48][Autoencoder loss: 130.111618][C loss: 0.921101][M loss: 0.693261][D loss: -179915.093750][G loss 341269.875000 ]time: 0:32:14.646653 
[Epoch 45/2000][Batch 3/48][Autoencoder loss: 73.266159][C loss: 0.899982][M loss: 0.693261][D loss: -196139.500000][G loss 361639.593750 ]time: 0:32:15.074942 
[Epoch 45/2000][Batch 4/48][Autoencoder loss: 195.604065][C loss: 0.973784][M loss: 0.693261][D loss: -188891.562500][G loss 387002.000000 ]time: 0:32:15.493123 
[Epoch 45/2000][Batch 5/48][Autoencoder loss: 332.099548][C loss: 1.197397][M loss: 0.693268][D loss: -198115.703125][G loss 413140.125000 ]time: 0:32:15.960776 
[Epoch 45/2000][Batch 6/48][Autoencoder loss: 246.471085][C loss: 0.816619][M loss: 0.693264][D loss: -200917.671875][G loss 429027.031250 ]time: 0:32:16.465612 
[Epoch 45/2000][Batch 7/48][Autoencoder loss: 178.331635][C loss: 0.755295][M loss: 0.693262][D loss: -205580.781250][G loss 468345.687500 ]time: 0:32:16.889523 
[Epoch 45/2000][Batch 8/48][Autoencoder loss: 253.517151][C loss: 1.176066][M loss: 0.693262][D loss: -96050.687500][G loss 353005.843750 ]time: 0:32:17.378513 
[Epoch 45/2000][Batch 9/48][Autoencoder loss: 206.763290][C loss: 1.154781][M loss: 0.693262][D loss: -96669.968750][G loss 368387.968750 ]time: 0:32:17.802805 
[Epoch 45/2000][Batch 10/48][Autoencoder loss: 238.680634][C loss: 1.086762][M loss: 0.693262][D loss: -103796.679688][G loss 387323.062500 ]time: 0:32:18.230638 
[Epoch 45/2000][Batch 11/48][Autoencoder loss: 243.366653][C loss: 0.982900][M loss: 0.693262][D loss: -103582.078125][G loss 396601.906250 ]time: 0:32:18.902606 
[Epoch 45/2000][Batch 12/48][Autoencoder loss: 168.421448][C loss: 0.553457][M loss: 0.693261][D loss: -147018.640625][G loss 478570.406250 ]time: 0:32:19.877521 
[Epoch 45/2000][Batch 13/48][Autoencoder loss: 137.446701][C loss: 0.558873][M loss: 0.693262][D loss: -160022.140625][G loss 497434.000000 ]time: 0:32:20.666405 
[Epoch 45/2000][Batch 14/48][Autoencoder loss: 130.160706][C loss: 0.555363][M loss: 0.693261][D loss: -141649.593750][G loss 513307.406250 ]time: 0:32:21.099782 
[Epoch 45/2000][Batch 15/48][Autoencoder loss: 95.743782][C loss: 0.562433][M loss: 0.693261][D loss: -130251.117188][G loss 515085.937500 ]time: 0:32:21.517503 
[Epoch 45/2000][Batch 16/48][Autoencoder loss: 210.103195][C loss: 0.571008][M loss: 0.693261][D loss: -92682.500000][G loss 499227.375000 ]time: 0:32:21.950670 
[Epoch 45/2000][Batch 17/48][Autoencoder loss: 167.148376][C loss: 0.592317][M loss: 0.693261][D loss: -54102.539062][G loss 497501.062500 ]time: 0:32:22.377942 
[Epoch 45/2000][Batch 18/48][Autoencoder loss: 133.235626][C loss: 0.502982][M loss: 0.693261][D loss: -47202.242188][G loss 492479.906250 ]time: 0:32:22.795625 
[Epoch 45/2000][Batch 19/48][Autoencoder loss: 110.755615][C loss: 0.466395][M loss: 0.693261][D loss: -46207.226562][G loss 478861.062500 ]time: 0:32:23.218866 
[Epoch 45/2000][Batch 20/48][Autoencoder loss: 491.888733][C loss: 0.261697][M loss: 0.693265][D loss: -37236.609375][G loss 514591.312500 ]time: 0:32:23.727637 
[Epoch 45/2000][Batch 21/48][Autoencoder loss: 486.227142][C loss: 0.000004][M loss: 0.693267][D loss: -23485.695312][G loss 491911.343750 ]time: 0:32:24.321346 
[Epoch 45/2000][Batch 22/48][Autoencoder loss: 232.743423][C loss: 0.000004][M loss: 0.693275][D loss: -5422.250000][G loss 489467.937500 ]time: 0:32:24.767747 
[Epoch 45/2000][Batch 23/48][Autoencoder loss: 289.184570][C loss: 0.000008][M loss: 0.693270][D loss: 33864.609375][G loss 459257.187500 ]time: 0:32:25.268670 
[Epoch 45/2000][Batch 24/48][Autoencoder loss: 668.158447][C loss: 0.849847][M loss: 0.693261][D loss: 18265.343750][G loss 446379.843750 ]time: 0:32:25.699228 
[Epoch 45/2000][Batch 25/48][Autoencoder loss: 559.540649][C loss: 0.513280][M loss: 0.693262][D loss: 49665.671875][G loss 439133.281250 ]time: 0:32:26.149724 
[Epoch 45/2000][Batch 26/48][Autoencoder loss: 341.797394][C loss: 0.535835][M loss: 0.693261][D loss: 62647.398438][G loss 415379.687500 ]time: 0:32:26.756320 
[Epoch 45/2000][Batch 27/48][Autoencoder loss: 288.667267][C loss: 0.461403][M loss: 0.693261][D loss: 85842.601562][G loss 401618.187500 ]time: 0:32:27.423960 
[Epoch 45/2000][Batch 28/48][Autoencoder loss: 306.613800][C loss: 1.405707][M loss: 0.693261][D loss: 52485.867188][G loss 395561.718750 ]time: 0:32:27.877085 
[Epoch 45/2000][Batch 29/48][Autoencoder loss: 473.957184][C loss: 1.275263][M loss: 0.693262][D loss: 78505.835938][G loss 382476.031250 ]time: 0:32:28.313035 
[Epoch 45/2000][Batch 30/48][Autoencoder loss: 520.541260][C loss: 1.249173][M loss: 0.693262][D loss: 94965.210938][G loss 356135.312500 ]time: 0:32:28.754885 
[Epoch 45/2000][Batch 31/48][Autoencoder loss: 314.289459][C loss: 1.001054][M loss: 0.693261][D loss: 100453.125000][G loss 335432.812500 ]time: 0:32:29.186157 
[Epoch 45/2000][Batch 32/48][Autoencoder loss: 183.459824][C loss: 0.994693][M loss: 0.693261][D loss: 173950.562500][G loss 240759.328125 ]time: 0:32:29.619167 
[Epoch 45/2000][Batch 33/48][Autoencoder loss: 234.901596][C loss: 0.942587][M loss: 0.693261][D loss: 177780.859375][G loss 229337.687500 ]time: 0:32:30.659898 
[Epoch 45/2000][Batch 34/48][Autoencoder loss: 238.257187][C loss: 0.870023][M loss: 0.693261][D loss: 177383.156250][G loss 205019.109375 ]time: 0:32:31.128717 
[Epoch 45/2000][Batch 35/48][Autoencoder loss: 131.809891][C loss: 0.750976][M loss: 0.693261][D loss: 172496.187500][G loss 179755.671875 ]time: 0:32:31.602094 
[Epoch 45/2000][Batch 36/48][Autoencoder loss: 121.191193][C loss: 0.772914][M loss: 0.693261][D loss: 162484.078125][G loss 172899.031250 ]time: 0:32:32.057038 
[Epoch 45/2000][Batch 37/48][Autoencoder loss: 158.168747][C loss: 0.735172][M loss: 0.693261][D loss: 161042.625000][G loss 161251.500000 ]time: 0:32:32.505065 
[Epoch 45/2000][Batch 38/48][Autoencoder loss: 133.987350][C loss: 0.657867][M loss: 0.693261][D loss: 152421.968750][G loss 143188.437500 ]time: 0:32:32.925426 
[Epoch 45/2000][Batch 39/48][Autoencoder loss: 108.120262][C loss: 0.567142][M loss: 0.693261][D loss: 150494.875000][G loss 125785.421875 ]time: 0:32:33.367429 
[Epoch 45/2000][Batch 40/48][Autoencoder loss: 207.894730][C loss: 0.782019][M loss: 0.693261][D loss: 143145.796875][G loss 116100.117188 ]time: 0:32:33.802590 
[Epoch 45/2000][Batch 41/48][Autoencoder loss: 238.240814][C loss: 0.771932][M loss: 0.693261][D loss: 134490.437500][G loss 106425.250000 ]time: 0:32:34.231372 
[Epoch 45/2000][Batch 42/48][Autoencoder loss: 301.613068][C loss: 0.728302][M loss: 0.693261][D loss: 130062.882812][G loss 96336.093750 ]time: 0:32:34.880040 
[Epoch 45/2000][Batch 43/48][Autoencoder loss: 185.888840][C loss: 0.653491][M loss: 0.693261][D loss: 123248.867188][G loss 86628.031250 ]time: 0:32:35.783979 
[Epoch 45/2000][Batch 44/48][Autoencoder loss: 105.158844][C loss: 0.801963][M loss: 0.693261][D loss: 111781.921875][G loss 77976.820312 ]time: 0:32:36.203174 
[Epoch 45/2000][Batch 45/48][Autoencoder loss: 95.405113][C loss: 0.789885][M loss: 0.693261][D loss: 108682.031250][G loss 71840.351562 ]time: 0:32:36.619055 
[Epoch 45/2000][Batch 46/48][Autoencoder loss: 73.918121][C loss: 0.743581][M loss: 0.693261][D loss: 101497.023438][G loss 63090.582031 ]time: 0:32:37.096585 
[Epoch 45/2000][Batch 47/48][Autoencoder loss: 66.945549][C loss: 0.666925][M loss: 0.693261][D loss: 93733.687500][G loss 58803.699219 ]time: 0:32:37.517777 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 434us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 516us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 574us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 45/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 46/2000][Batch 0/48][Autoencoder loss: 290.544342][C loss: 0.812654][M loss: 0.693261][D loss: 80035.148438][G loss 64085.875000 ]time: 0:32:52.129334 
[Epoch 46/2000][Batch 1/48][Autoencoder loss: 176.180801][C loss: 0.924309][M loss: 0.693261][D loss: 74560.242188][G loss 56851.109375 ]time: 0:32:52.919946 
[Epoch 46/2000][Batch 2/48][Autoencoder loss: 87.848808][C loss: 0.899119][M loss: 0.693261][D loss: 69508.203125][G loss 53394.035156 ]time: 0:32:53.360160 
[Epoch 46/2000][Batch 3/48][Autoencoder loss: 54.938835][C loss: 0.883343][M loss: 0.693261][D loss: 65496.570312][G loss 48262.285156 ]time: 0:32:53.792530 
[Epoch 46/2000][Batch 4/48][Autoencoder loss: 187.971268][C loss: 0.886620][M loss: 0.693261][D loss: 61880.074219][G loss 43619.472656 ]time: 0:32:54.227047 
[Epoch 46/2000][Batch 5/48][Autoencoder loss: 331.240540][C loss: 0.873382][M loss: 0.693263][D loss: 59271.609375][G loss 40444.148438 ]time: 0:32:54.650460 
[Epoch 46/2000][Batch 6/48][Autoencoder loss: 231.486267][C loss: 0.789010][M loss: 0.693262][D loss: 55760.957031][G loss 36501.949219 ]time: 0:32:55.078514 
[Epoch 46/2000][Batch 7/48][Autoencoder loss: 158.333160][C loss: 0.708357][M loss: 0.693262][D loss: 52271.363281][G loss 33540.121094 ]time: 0:32:55.502010 
[Epoch 46/2000][Batch 8/48][Autoencoder loss: 245.801437][C loss: 1.245453][M loss: 0.693261][D loss: 54318.929688][G loss 24217.988281 ]time: 0:32:55.930507 
[Epoch 46/2000][Batch 9/48][Autoencoder loss: 230.230652][C loss: 1.060255][M loss: 0.693261][D loss: 50554.562500][G loss 21955.529297 ]time: 0:32:56.354583 
[Epoch 46/2000][Batch 10/48][Autoencoder loss: 249.473969][C loss: 0.954813][M loss: 0.693261][D loss: 47459.835938][G loss 20217.492188 ]time: 0:32:56.775418 
[Epoch 46/2000][Batch 11/48][Autoencoder loss: 238.794144][C loss: 0.824304][M loss: 0.693261][D loss: 44196.015625][G loss 19654.669922 ]time: 0:32:57.191317 
[Epoch 46/2000][Batch 12/48][Autoencoder loss: 172.915756][C loss: 0.546485][M loss: 0.693261][D loss: 36281.062500][G loss 19522.011719 ]time: 0:32:57.599002 
[Epoch 46/2000][Batch 13/48][Autoencoder loss: 131.636963][C loss: 0.550668][M loss: 0.693260][D loss: 32984.804688][G loss 17569.556641 ]time: 0:32:58.046395 
[Epoch 46/2000][Batch 14/48][Autoencoder loss: 117.643181][C loss: 0.550847][M loss: 0.693261][D loss: 30221.986328][G loss 15762.133789 ]time: 0:32:58.526101 
[Epoch 46/2000][Batch 15/48][Autoencoder loss: 105.495438][C loss: 0.556489][M loss: 0.693262][D loss: 26961.242188][G loss 14558.154297 ]time: 0:32:58.961746 
[Epoch 46/2000][Batch 16/48][Autoencoder loss: 246.238632][C loss: 0.551756][M loss: 0.693260][D loss: 25470.042969][G loss 12507.461914 ]time: 0:32:59.394960 
[Epoch 46/2000][Batch 17/48][Autoencoder loss: 187.440903][C loss: 0.576174][M loss: 0.693261][D loss: 22635.882812][G loss 10641.468750 ]time: 0:32:59.970883 
[Epoch 46/2000][Batch 18/48][Autoencoder loss: 143.748718][C loss: 0.507595][M loss: 0.693261][D loss: 19691.250000][G loss 9157.752930 ]time: 0:33:00.656281 
[Epoch 46/2000][Batch 19/48][Autoencoder loss: 126.179283][C loss: 0.463863][M loss: 0.693261][D loss: 16816.478516][G loss 7876.920898 ]time: 0:33:01.077791 
[Epoch 46/2000][Batch 20/48][Autoencoder loss: 670.095703][C loss: 0.521724][M loss: 0.693262][D loss: 14924.661133][G loss 6923.107422 ]time: 0:33:01.968615 
[Epoch 46/2000][Batch 21/48][Autoencoder loss: 712.366882][C loss: 0.018364][M loss: 0.693269][D loss: 14165.986328][G loss 5682.657227 ]time: 0:33:02.584435 
[Epoch 46/2000][Batch 22/48][Autoencoder loss: 312.155731][C loss: 0.000691][M loss: 0.693267][D loss: 11638.867188][G loss 4392.518555 ]time: 0:33:03.014834 
[Epoch 46/2000][Batch 23/48][Autoencoder loss: 231.181839][C loss: 0.028834][M loss: 0.693265][D loss: 9276.700195][G loss 3328.811035 ]time: 0:33:03.453484 
[Epoch 46/2000][Batch 24/48][Autoencoder loss: 1019.275635][C loss: 0.556171][M loss: 0.693264][D loss: 4744.201660][G loss 2226.405029 ]time: 0:33:03.879223 
[Epoch 46/2000][Batch 25/48][Autoencoder loss: 696.896606][C loss: 0.463517][M loss: 0.693263][D loss: 2822.604980][G loss 1162.896118 ]time: 0:33:04.304718 
[Epoch 46/2000][Batch 26/48][Autoencoder loss: 591.326477][C loss: 0.540647][M loss: 0.693261][D loss: 608.379761][G loss 95.475906 ]time: 0:33:04.733677 
[Epoch 46/2000][Batch 27/48][Autoencoder loss: 751.053284][C loss: 0.509165][M loss: 0.693260][D loss: -1516.484375][G loss -932.736328 ]time: 0:33:05.152332 
[Epoch 46/2000][Batch 28/48][Autoencoder loss: 639.007568][C loss: 1.423235][M loss: 0.693262][D loss: -3606.262451][G loss -2171.876221 ]time: 0:33:05.582942 
[Epoch 46/2000][Batch 29/48][Autoencoder loss: 433.917358][C loss: 1.299077][M loss: 0.693261][D loss: -5269.035156][G loss -3040.446777 ]time: 0:33:06.040910 
[Epoch 46/2000][Batch 30/48][Autoencoder loss: 593.275635][C loss: 1.416045][M loss: 0.693261][D loss: -7534.318848][G loss -4188.864258 ]time: 0:33:06.471024 
[Epoch 46/2000][Batch 31/48][Autoencoder loss: 457.375458][C loss: 1.145308][M loss: 0.693261][D loss: -9435.028320][G loss -5248.014648 ]time: 0:33:06.899260 
[Epoch 46/2000][Batch 32/48][Autoencoder loss: 138.651596][C loss: 0.949063][M loss: 0.693261][D loss: -12229.794922][G loss -5196.396484 ]time: 0:33:07.314115 
[Epoch 46/2000][Batch 33/48][Autoencoder loss: 285.610474][C loss: 0.905487][M loss: 0.693261][D loss: -14343.034180][G loss -6121.620117 ]time: 0:33:08.182931 
[Epoch 46/2000][Batch 34/48][Autoencoder loss: 377.357239][C loss: 0.828506][M loss: 0.693260][D loss: -16409.333984][G loss -7270.359375 ]time: 0:33:08.652218 
[Epoch 46/2000][Batch 35/48][Autoencoder loss: 257.483551][C loss: 0.737590][M loss: 0.693261][D loss: -18205.486328][G loss -7834.528320 ]time: 0:33:09.079050 
[Epoch 46/2000][Batch 36/48][Autoencoder loss: 195.054947][C loss: 0.585284][M loss: 0.693261][D loss: -20172.615234][G loss -8842.541016 ]time: 0:33:09.491944 
[Epoch 46/2000][Batch 37/48][Autoencoder loss: 292.575287][C loss: 0.548775][M loss: 0.693261][D loss: -22440.298828][G loss -10036.151367 ]time: 0:33:09.910048 
[Epoch 46/2000][Batch 38/48][Autoencoder loss: 190.378693][C loss: 0.504905][M loss: 0.693261][D loss: -24608.451172][G loss -11365.922852 ]time: 0:33:10.333011 
[Epoch 46/2000][Batch 39/48][Autoencoder loss: 97.206856][C loss: 0.464846][M loss: 0.693261][D loss: -27134.662109][G loss -12265.934570 ]time: 0:33:10.736940 
[Epoch 46/2000][Batch 40/48][Autoencoder loss: 239.425644][C loss: 0.655812][M loss: 0.693262][D loss: -28734.896484][G loss -13080.437500 ]time: 0:33:11.170008 
[Epoch 46/2000][Batch 41/48][Autoencoder loss: 295.160278][C loss: 0.687748][M loss: 0.693261][D loss: -30815.156250][G loss -14087.417969 ]time: 0:33:11.593957 
[Epoch 46/2000][Batch 42/48][Autoencoder loss: 314.029175][C loss: 0.671427][M loss: 0.693261][D loss: -33044.945312][G loss -15357.048828 ]time: 0:33:12.342901 
[Epoch 46/2000][Batch 43/48][Autoencoder loss: 189.096130][C loss: 0.631244][M loss: 0.693262][D loss: -34345.359375][G loss -15899.519531 ]time: 0:33:12.849644 
[Epoch 46/2000][Batch 44/48][Autoencoder loss: 141.601044][C loss: 0.818767][M loss: 0.693261][D loss: -36770.890625][G loss -17959.427734 ]time: 0:33:13.281738 
[Epoch 46/2000][Batch 45/48][Autoencoder loss: 120.099236][C loss: 0.787414][M loss: 0.693261][D loss: -39531.757812][G loss -18854.992188 ]time: 0:33:13.689224 
[Epoch 46/2000][Batch 46/48][Autoencoder loss: 90.588295][C loss: 0.732777][M loss: 0.693260][D loss: -41107.273438][G loss -20800.294922 ]time: 0:33:14.145906 
[Epoch 46/2000][Batch 47/48][Autoencoder loss: 103.726112][C loss: 0.664003][M loss: 0.693261][D loss: -44246.593750][G loss -21595.792969 ]time: 0:33:14.562443 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 572us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 375us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 454us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 46/2000] [Accuracy_lsvm: 0.411806] [Accuracy_nrf: 0.454514] [Accuracy_pnb: 0.402778][Accuracy_mlp: 0.395486]
[Epoch 47/2000][Batch 0/48][Autoencoder loss: 257.828674][C loss: 0.661519][M loss: 0.693261][D loss: -43573.578125][G loss -27074.835938 ]time: 0:33:30.710878 
[Epoch 47/2000][Batch 1/48][Autoencoder loss: 184.020050][C loss: 0.815122][M loss: 0.693261][D loss: -44121.316406][G loss -27825.128906 ]time: 0:33:31.416008 
[Epoch 47/2000][Batch 2/48][Autoencoder loss: 104.786011][C loss: 0.788420][M loss: 0.693261][D loss: -47053.742188][G loss -29681.833984 ]time: 0:33:31.889558 
[Epoch 47/2000][Batch 3/48][Autoencoder loss: 69.859444][C loss: 0.785671][M loss: 0.693261][D loss: -49884.203125][G loss -30905.660156 ]time: 0:33:32.317669 
[Epoch 47/2000][Batch 4/48][Autoencoder loss: 164.642776][C loss: 0.813125][M loss: 0.693261][D loss: -53281.441406][G loss -33037.121094 ]time: 0:33:32.743231 
[Epoch 47/2000][Batch 5/48][Autoencoder loss: 267.832001][C loss: 0.844934][M loss: 0.693266][D loss: -55121.796875][G loss -35482.558594 ]time: 0:33:33.183338 
[Epoch 47/2000][Batch 6/48][Autoencoder loss: 173.588470][C loss: 0.708011][M loss: 0.693262][D loss: -59179.949219][G loss -36849.832031 ]time: 0:33:33.617103 
[Epoch 47/2000][Batch 7/48][Autoencoder loss: 155.995880][C loss: 0.576806][M loss: 0.693262][D loss: -59610.671875][G loss -39980.625000 ]time: 0:33:34.729549 
[Epoch 47/2000][Batch 8/48][Autoencoder loss: 319.377472][C loss: 1.255791][M loss: 0.693261][D loss: -68769.062500][G loss -33116.492188 ]time: 0:33:35.476736 
[Epoch 47/2000][Batch 9/48][Autoencoder loss: 236.591125][C loss: 1.208093][M loss: 0.693261][D loss: -71489.484375][G loss -35277.101562 ]time: 0:33:35.915630 
[Epoch 47/2000][Batch 10/48][Autoencoder loss: 234.289719][C loss: 1.055814][M loss: 0.693262][D loss: -75563.851562][G loss -37708.792969 ]time: 0:33:36.365235 
[Epoch 47/2000][Batch 11/48][Autoencoder loss: 244.470764][C loss: 0.911682][M loss: 0.693261][D loss: -77993.570312][G loss -37946.398438 ]time: 0:33:36.814561 
[Epoch 47/2000][Batch 12/48][Autoencoder loss: 159.008041][C loss: 0.612039][M loss: 0.693260][D loss: -71959.640625][G loss -47800.324219 ]time: 0:33:37.244158 
[Epoch 47/2000][Batch 13/48][Autoencoder loss: 120.629280][C loss: 0.611346][M loss: 0.693261][D loss: -76639.500000][G loss -51260.046875 ]time: 0:33:37.731907 
[Epoch 47/2000][Batch 14/48][Autoencoder loss: 125.068375][C loss: 0.617517][M loss: 0.693261][D loss: -79499.312500][G loss -55195.355469 ]time: 0:33:38.174595 
[Epoch 47/2000][Batch 15/48][Autoencoder loss: 91.605270][C loss: 0.630714][M loss: 0.693261][D loss: -81307.734375][G loss -56661.195312 ]time: 0:33:38.609716 
[Epoch 47/2000][Batch 16/48][Autoencoder loss: 205.124481][C loss: 0.516311][M loss: 0.693260][D loss: -88074.203125][G loss -58506.449219 ]time: 0:33:39.216056 
[Epoch 47/2000][Batch 17/48][Autoencoder loss: 171.940033][C loss: 0.533368][M loss: 0.693261][D loss: -88889.296875][G loss -59026.148438 ]time: 0:33:40.352364 
[Epoch 47/2000][Batch 18/48][Autoencoder loss: 134.808060][C loss: 0.435175][M loss: 0.693261][D loss: -93684.765625][G loss -65129.179688 ]time: 0:33:40.783626 
[Epoch 47/2000][Batch 19/48][Autoencoder loss: 107.525841][C loss: 0.391377][M loss: 0.693261][D loss: -95218.328125][G loss -65110.824219 ]time: 0:33:41.219535 
[Epoch 47/2000][Batch 20/48][Autoencoder loss: 496.166779][C loss: 0.371444][M loss: 0.693265][D loss: -95696.078125][G loss -68456.250000 ]time: 0:33:41.658047 
[Epoch 47/2000][Batch 21/48][Autoencoder loss: 682.542603][C loss: 0.000100][M loss: 0.693264][D loss: -93939.492188][G loss -74758.398438 ]time: 0:33:42.098681 
[Epoch 47/2000][Batch 22/48][Autoencoder loss: 293.737823][C loss: 0.000199][M loss: 0.693268][D loss: -96353.554688][G loss -78230.984375 ]time: 0:33:42.519227 
[Epoch 47/2000][Batch 23/48][Autoencoder loss: 541.729980][C loss: 0.000206][M loss: 0.693265][D loss: -105199.578125][G loss -84317.820312 ]time: 0:33:42.934997 
[Epoch 47/2000][Batch 24/48][Autoencoder loss: 857.208618][C loss: 1.137710][M loss: 0.693262][D loss: -105669.734375][G loss -90773.054688 ]time: 0:33:43.360910 
[Epoch 47/2000][Batch 25/48][Autoencoder loss: 610.797180][C loss: 0.689433][M loss: 0.693261][D loss: -109959.734375][G loss -96324.781250 ]time: 0:33:43.778796 
[Epoch 47/2000][Batch 26/48][Autoencoder loss: 659.867859][C loss: 0.664079][M loss: 0.693262][D loss: -113822.585938][G loss -99433.414062 ]time: 0:33:44.208153 
[Epoch 47/2000][Batch 27/48][Autoencoder loss: 541.073853][C loss: 0.534627][M loss: 0.693261][D loss: -110578.523438][G loss -106030.765625 ]time: 0:33:44.632298 
[Epoch 47/2000][Batch 28/48][Autoencoder loss: 293.831207][C loss: 1.270503][M loss: 0.693261][D loss: -104453.671875][G loss -114475.085938 ]time: 0:33:45.040077 
[Epoch 47/2000][Batch 29/48][Autoencoder loss: 602.058044][C loss: 1.097035][M loss: 0.693262][D loss: -98264.750000][G loss -121616.500000 ]time: 0:33:45.503614 
[Epoch 47/2000][Batch 30/48][Autoencoder loss: 812.508850][C loss: 1.075980][M loss: 0.693261][D loss: -103863.234375][G loss -125916.132812 ]time: 0:33:45.971886 
[Epoch 47/2000][Batch 31/48][Autoencoder loss: 420.026886][C loss: 0.911701][M loss: 0.693262][D loss: -105210.140625][G loss -133059.453125 ]time: 0:33:46.387075 
[Epoch 47/2000][Batch 32/48][Autoencoder loss: 242.097275][C loss: 1.104499][M loss: 0.693261][D loss: -123645.679688][G loss -122549.351562 ]time: 0:33:46.799785 
[Epoch 47/2000][Batch 33/48][Autoencoder loss: 424.591949][C loss: 1.023857][M loss: 0.693261][D loss: -127856.281250][G loss -120850.250000 ]time: 0:33:47.370162 
[Epoch 47/2000][Batch 34/48][Autoencoder loss: 333.090668][C loss: 0.876978][M loss: 0.693261][D loss: -125587.773438][G loss -132998.140625 ]time: 0:33:48.020787 
[Epoch 47/2000][Batch 35/48][Autoencoder loss: 161.611374][C loss: 0.739255][M loss: 0.693261][D loss: -127002.445312][G loss -139896.031250 ]time: 0:33:48.440371 
[Epoch 47/2000][Batch 36/48][Autoencoder loss: 263.440277][C loss: 0.712075][M loss: 0.693261][D loss: -121529.789062][G loss -148435.296875 ]time: 0:33:48.865785 
[Epoch 47/2000][Batch 37/48][Autoencoder loss: 285.188751][C loss: 0.652896][M loss: 0.693261][D loss: -122995.039062][G loss -148633.937500 ]time: 0:33:49.295765 
[Epoch 47/2000][Batch 38/48][Autoencoder loss: 149.874756][C loss: 0.579121][M loss: 0.693261][D loss: -120955.398438][G loss -158455.906250 ]time: 0:33:49.713404 
[Epoch 47/2000][Batch 39/48][Autoencoder loss: 176.523636][C loss: 0.494444][M loss: 0.693261][D loss: -122543.757812][G loss -167830.078125 ]time: 0:33:50.154965 
[Epoch 47/2000][Batch 40/48][Autoencoder loss: 329.720520][C loss: 0.785994][M loss: 0.693261][D loss: -119574.187500][G loss -175161.265625 ]time: 0:33:50.569821 
[Epoch 47/2000][Batch 41/48][Autoencoder loss: 267.709869][C loss: 0.799934][M loss: 0.693262][D loss: -113702.601562][G loss -183663.750000 ]time: 0:33:50.997093 
[Epoch 47/2000][Batch 42/48][Autoencoder loss: 334.623230][C loss: 0.772690][M loss: 0.693261][D loss: -115003.132812][G loss -191105.203125 ]time: 0:33:51.411600 
[Epoch 47/2000][Batch 43/48][Autoencoder loss: 243.381119][C loss: 0.686777][M loss: 0.693261][D loss: -109055.585938][G loss -192921.578125 ]time: 0:33:51.840951 
[Epoch 47/2000][Batch 44/48][Autoencoder loss: 116.170021][C loss: 0.834076][M loss: 0.693261][D loss: -100195.304688][G loss -208783.218750 ]time: 0:33:52.264541 
[Epoch 47/2000][Batch 45/48][Autoencoder loss: 73.179703][C loss: 0.794197][M loss: 0.693261][D loss: -90751.734375][G loss -219064.468750 ]time: 0:33:52.677213 
[Epoch 47/2000][Batch 46/48][Autoencoder loss: 92.477982][C loss: 0.743520][M loss: 0.693261][D loss: -87959.750000][G loss -227615.296875 ]time: 0:33:53.085508 
[Epoch 47/2000][Batch 47/48][Autoencoder loss: 102.688217][C loss: 0.649854][M loss: 0.693260][D loss: -75928.781250][G loss -229195.937500 ]time: 0:33:53.500864 
loading data...
test classes: [9, 13, 15]
train classes: [ 0  1  2  3  4  5  6  7  9 10 11 13]
为类别 9 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 456us/step
为类别 13 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 485us/step
为类别 15 生成 2000 个特征, 属性形状: (2000, 20)

 1/63 [..............................] - ETA: 0s
63/63 [==============================] - 0s 497us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)

{"model_config": {"feature_dim": 52, "attribute_dim": 20, "latent_dim": 50, "hidden_dims": [128, 256, 128]}, "training_config": {"batch_size": 32, "learning_rate_g": 0.0002, "learning_rate_d": 0.0004, "adversarial_weight": 1.0, "cycle_consistency_weight": 0.3, "semantic_distance_weight": 0.1, "uncertainty_weight": 0.1, "domain_selection_weight": 0.1, "gradient_penalty_weight": 10.0}, "data_info": {"split_group": "D", "test_classes": [2, 3, 5], "train_samples": 5760, "test_samples": 2880, "feature_dim": 52, "attribute_dim": 20, "num_classes": 12}, "final_metrics": {"best_loss": 565852.6875, "final_accuracy": 0.3434027777777778, "total_epochs": 500}}
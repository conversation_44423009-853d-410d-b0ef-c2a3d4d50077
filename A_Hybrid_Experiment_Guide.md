# 🔥 A-Hybrid实验指南

## 实验概述

**A-Hybrid实验**是基于B-Hybrid成功经验，针对A组数据配置的融合实验。核心策略是将HardTriplet的强分离能力与SmartCRL的稳定性约束相结合。

### 核心创新点
- 🎯 **HardTriplet强权重**: 保持强大的特征分离能力
- 🛡️ **SmartCRL适度约束**: 提升训练稳定性，减少异常峰值
- 📊 **A组专用配置**: 针对测试类别[1, 6, 14]优化
- 🔄 **语义一致性增强**: 适度的语义约束提升生成质量

## A组配置详情

### 数据分组
- **测试类别**: [1, 6, 14] (零样本学习目标)
- **训练类别**: [2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 15]
- **数据来源**: `/app/data/dataset_train_case1.npz` 和 `/app/data/dataset_test_case1.npz`

### 权重配置
```python
# HardTriplet强权重 (保持分离能力)
lambda_triplet = 5.0      # 强化Triplet损失
lambda_center = 0.1       # 强化Center损失

# SmartCRL适度约束 (提升稳定性)
lambda_crl = 0.01         # 适度CRL约束
lambda_semantic = 0.01    # 适度语义约束

# 基础权重调整
lambda_cla = 1.0          # 分类损失权重
```

## 预期目标

### 主要目标 (70%概率)
- ✅ 保持55-58%的峰值准确率
- ✅ 显著提升训练稳定性
- ✅ 减少G loss异常峰值
- ✅ 训练过程更加平滑

### 突破目标 (25%概率)
- 🎯 准确率突破60%+
- 🎯 多重约束产生协同效应
- 🎯 同时保持优秀稳定性

### 风险控制 (5%概率)
- ⚠️ 约束冲突导致性能下降
- ⚠️ 通过渐进式实验规避

## 技术实现

### 损失函数融合
```python
# HardTriplet强权重 (保持分离能力)
lambda_triplet = 5.0      # 强化Triplet损失
lambda_center = 0.1       # 强化Center损失

# SmartCRL适度约束 (提升稳定性)
lambda_crl = 0.01         # 适度CRL约束
lambda_semantic = 0.01    # 适度语义约束
```

### 总损失函数
```python
total_loss = (
    adversarial_loss +
    lambda_cla * classification_loss +
    lambda_triplet * triplet_loss +           # HardTriplet强权重
    lambda_center * center_loss +             # HardTriplet强权重
    lambda_crl * cycle_rank_loss +            # SmartCRL适度约束
    lambda_semantic * semantic_loss           # SmartCRL适度约束
)
```

## 运行方法

### 方法1: 直接运行
```bash
python run_A_Hybrid.py
```

### 方法2: tmux会话运行 (推荐)
```bash
tmux new-session -d -s A-Hybrid 'python run_A_Hybrid.py'
tmux attach -t A-Hybrid  # 查看进度
```

### 方法3: Docker环境运行
```bash
docker start acgan-container
docker exec -it acgan-container python run_A_Hybrid.py
```

## 监控指标

### 关键性能指标
1. **峰值准确率**: 目标保持55%+，争取突破60%
2. **训练稳定性**: G loss异常峰值频率
3. **收敛质量**: 损失曲线平滑度
4. **准确率波动**: 减少大幅波动

### 损失监控
- `AE+C loss`: 自编码器+分类器损失
- `强化Triplet`: HardTriplet权重的Triplet损失
- `强化Center`: HardTriplet权重的Center损失
- `组合强化`: Triplet + Center组合损失
- `适度CRL`: SmartCRL权重的CRL损失
- `语义增强`: SmartCRL权重的语义损失
- `D loss`: 判别器损失
- `G loss`: 生成器总损失

### 准确率监控
- `LinearSVM`: 线性支持向量机准确率
- `RandomForest`: 随机森林准确率
- `GaussianNB`: 高斯朴素贝叶斯准确率
- `MLPClassifier`: 多层感知机准确率

## TensorBoard可视化

### 启动TensorBoard
```bash
tensorboard --logdir=./tensorboard_logs
```

### 访问地址
- 本地访问: http://localhost:6006
- 实时监控: `./tensorboard_logs/A_Hybrid_realtime`

### 可视化内容
- 损失曲线分析
- 准确率趋势
- 权重分布
- 梯度流向
- 自定义指标

## 实验对比

### 与B-Hybrid对比
| 指标 | A组配置 | B组配置 | 预期差异 |
|------|---------|---------|----------|
| 测试类别 | [1, 6, 14] | [4, 7, 10] | 不同类别特性 |
| 训练难度 | 中等 | 中等 | 相似难度 |
| 预期准确率 | 55-60% | 55-60% | 相近性能 |
| 稳定性 | 高 | 高 | 相似稳定性 |

### 与原始ACGAN-FG对比
- **准确率提升**: 预期提升5-10%
- **稳定性改善**: 显著减少训练波动
- **收敛速度**: 更快更稳定的收敛

## 文件结构

```
├── scripts/
│   └── acgan_triplet_A_Hybrid.py    # A组核心实验脚本
├── run_A_Hybrid.py                  # A组启动脚本
├── A_Hybrid_Experiment_Guide.md     # 本指南文档
├── logs/                            # 训练日志目录
└── tensorboard_logs/
    └── A_Hybrid_realtime/           # TensorBoard日志
```

## 注意事项

### 数据验证
- ✅ 确保训练数据不包含测试类别[1, 6, 14]
- ✅ 验证数据加载逻辑正确性
- ✅ 检查属性矩阵路径

### 资源要求
- **GPU内存**: 建议8GB+
- **训练时间**: 预计3-4小时 (2000 epochs)
- **存储空间**: 日志和模型约1GB

### 故障排除
1. **导入错误**: 检查路径配置
2. **GPU内存不足**: 减少batch_size
3. **数据加载失败**: 验证.npz文件路径
4. **TensorBoard无法访问**: 检查端口占用

## 实验记录

### 预期结果格式
```
🎉 A-Hybrid实验训练完成!
📅 结束时间: 2024-XX-XX XX:XX:XX
⏱️  总耗时: X:XX:XX
🏆 最终最佳准确率:
   LinearSVM: XX.XX%
   RandomForest: XX.XX%
   GaussianNB: XX.XX%
   MLPClassifier: XX.XX%
📊 平均准确率: XX.XX%
```

## 后续优化方向

1. **权重微调**: 根据A组特性调整权重比例
2. **架构优化**: 针对A组数据特点优化网络结构
3. **数据增强**: 探索A组专用的数据增强策略
4. **集成学习**: 结合多个模型提升性能

---

**开始您的A-Hybrid实验之旅！** 🚀
# ACGAN-FG 项目开发规则

## 1. 概述 (Overview)

本文档旨在为 ACGAN-FG 项目提供一套标准的开发、实验和代码管理规则。本项目的核心是实现论文 "A Novel Zero-Shot Learning Method With Feature Generation for Intelligent Fault Diagnosis" 中提出的 ACGAN-FG 模型，用于零样本故障诊断研究。所有项目成员应遵循这些规则，以确保代码质量、提高协作效率和保证研究的可复现性。

## 2. 环境设置 (Environment Setup)

本项目强制使用 Docker 作为统一的开发和运行环境，以消除环境不一致导致的问题。

### 2.1. 环境依赖

- **Docker**: 请确保您的系统中已正确安装 Docker Engine。
- **NVIDIA Container Toolkit**: 如果您需要在 GPU 环境下运行，请确保已安装 NVIDIA Container Toolkit，以便 Docker 容器能访问宿主机的 GPU。

### 2.2. 构建 Docker 镜像

项目根目录下的 `Dockerfile` 是环境的唯一事实来源 (Single Source of Truth)。它定义了所有系统依赖和 Python 库。

要构建镜像，请在项目根目录下运行：
```bash
docker build -t acgan-fg:latest .
```

**注意**: `README.md` 中提到的 `TensorFlow 2.10.0` 与当前环境中使用的 `TensorFlow 2.15.0` [[memory:475003]] 及 `Dockerfile` 中引用的基础镜像可能存在版本差异。 **我们以 `Dockerfile` 定义的环境为最终标准。** 如有任何关于环境的变更，必须首先更新 `Dockerfile`。

### 2.3. 启动开发容器

使用以下命令启动一个交互式的开发容器：

**使用 GPU:**
```bash
docker run --gpus all -it --rm -v "$(pwd)":/workspace -w /workspace acgan-fg:latest bash
```

**仅使用 CPU:**
```bash
docker run -it --rm -v "$(pwd)":/workspace -w /workspace acgan-fg:latest bash
```
- `--gpus all`: 将宿主机的 NVIDIA GPU 资源挂载到容器中。
- `-it`: 启动一个交互式终端。
- `--rm`: 容器停止后自动删除。
- `-v "$(pwd)":/workspace`: 将当前项目目录挂载到容器的 `/workspace` 目录下，方便直接在容器内修改代码。
- `-w /workspace`: 将容器的默认工作目录设置为 `/workspace`。

## 3. 代码规范 (Code Style)

### 3.1. Python 代码风格

- 所有 Python 代码必须严格遵守 **PEP 8** 规范。
- 建议使用 `black` 或 `autopep8` 等工具自动格式化代码。

### 3.2. 命名约定

- **文件名**: 使用小写字母和下划线，例如 `data_pipeline.py`, `transformer_model.py`。
- **类名**: 使用驼峰命名法 (CamelCase)，例如 `FeatureGenerator`, `AuxiliaryClassifier`。
- **函数/方法名**: 使用小写字母和下划线，例如 `build_generator`, `train_step`。
- **变量名**: 使用小写字母和下划线，清晰表达其含义。

## 4. 版本控制 (Version Control)

本项目使用 Git 进行版本控制。

### 4.1. 分支策略

- `main`: 主分支，必须始终保持稳定、可运行的状态。只接受从 `develop` 或 `hotfix` 分支的合并请求。
- `develop`: 开发主分支。所有新功能的开发都从该分支创建新的 `feature` 分支。
- `feature/[feature-name]`: 功能开发分支。每个新想法、新模型或新任务都应创建一个对应的功能分支。例如 `feature/triplet-loss`, `feature/attention-mechanism`。开发完成后，向 `develop` 分支发起 Pull Request。
- `hotfix/[issue-name]`: 紧急修复分支，用于修复 `main` 分支的严重 bug。

### 4.2. 提交信息 (Commit Messages)

Commit Message 必须遵循以下格式，以提供清晰的变更历史：
```
<type>(<scope>): <subject>

<body>
```
- **type**: 提交类型 (e.g., `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`)。
- **scope**: 本次提交影响的范围 (e.g., `generator`, `discriminator`, `data`)。
- **subject**: 简短的变更描述。

## 5. 实验管理 (Experiment Management)

为了保证研究工作的可追溯性和可复现性，所有实验必须遵循以下管理规范。

### 5.1. 实验代码

- 避免直接修改核心代码文件 (`ACGAN_FG.py`) 来进行实验。
- 每个独立的实验想法应创建新的脚本文件，例如 `run_experiment_triplet.py`。
- 对于不再需要的实验代码，应移至 `放弃/` 目录，而不是直接删除。

### 5.2. 输出文件管理

严禁在项目根目录下生成任何输出文件。

- **日志 (Logs)**: 所有实验过程的日志输出都必须保存到 `日志/` 目录下。
- **结果 (Results)**: 最终的实验结果、评估指标、图表等文件必须保存到 `结果/` 目录下。
- **模型文件 (Models)**: 训练好的模型权重应保存到新建的 `models/` 目录下。

### 5.3. 文件命名规范

- **日志文件**: `[YYYYMMDD]_[模型/实验名]_[数据集]_[可选描述].log` (e.g., `20250715_acgan_fg_TEP_baseline.log`)
- **结果文件**: 命名应与对应的日志文件保持关联，并清晰描述其内容。 (e.g., `20250715_acgan_fg_TEP_baseline_accuracy.csv`)

## 6. 依赖管理 (Dependency Management)

- 任何需要新增或更新的 Python 依赖，都必须通过修改 `Dockerfile` 来完成。
- 修改 `Dockerfile` 后，需要重新构建镜像 (`docker build ...`) 并通知所有项目成员。
- **严禁** 在容器内使用 `pip install` 临时安装依赖，这会破坏环境的一致性。 
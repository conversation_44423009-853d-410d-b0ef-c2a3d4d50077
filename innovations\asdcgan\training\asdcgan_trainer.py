"""
ASDCGAN主训练器

实现ASDCGAN的完整训练流程，集成所有组件和损失函数。

核心功能：
1. 端到端训练流程
2. 多组件协调训练
3. 自适应训练策略
4. 实时监控和调试
5. 模型保存和恢复

技术特点：
- 模块化训练架构
- 灵活的训练策略
- 自动化超参数调优
- 分布式训练支持
"""

import tensorflow as tf
import numpy as np
import os
import time
from typing import Dict, List, Optional, Tuple
import logging

from ..models import (
    AdaptiveSemanticDistance,
    DomainSelector,
    VariationalGenerator,
    MultiLevelDiscriminator,
    UncertaintyPropagator
)
from ..losses import TotalLossManager


class ASDCGANTrainer:
    """
    ASDCGAN主训练器
    
    集成所有组件，实现完整的ASDCGAN训练流程。
    """
    
    def __init__(self,
                 # 模型配置
                 feature_dim=256,
                 attribute_dim=20,
                 latent_dim=50,
                 num_domains=5,
                 
                 # 训练配置
                 batch_size=32,
                 learning_rate_g=0.0001,
                 learning_rate_d=0.0004,
                 beta1=0.5,
                 beta2=0.999,
                 
                 # 损失权重
                 adversarial_weight=1.0,
                 cycle_consistency_weight=10.0,
                 semantic_distance_weight=5.0,
                 uncertainty_weight=1.0,
                 
                 # 训练策略
                 n_critic=5,  # 判别器训练次数
                 warmup_epochs=5,
                 adaptive_weights=True,
                 
                 # 其他配置
                 save_dir='./checkpoints',
                 log_dir='./logs',
                 **kwargs):
        """
        初始化ASDCGAN训练器
        
        Args:
            feature_dim: 特征维度
            attribute_dim: 属性维度
            latent_dim: 潜在空间维度
            num_domains: 域数量
            batch_size: 批次大小
            learning_rate_g: 生成器学习率
            learning_rate_d: 判别器学习率
            n_critic: 判别器训练次数
            warmup_epochs: 预热轮次
            adaptive_weights: 是否使用自适应权重
            save_dir: 模型保存目录
            log_dir: 日志目录
        """
        
        # 保存配置
        self.feature_dim = feature_dim
        self.attribute_dim = attribute_dim
        self.latent_dim = latent_dim
        self.num_domains = num_domains
        self.batch_size = batch_size
        self.n_critic = n_critic
        self.warmup_epochs = warmup_epochs
        self.save_dir = save_dir
        self.log_dir = log_dir
        
        # 创建目录
        os.makedirs(save_dir, exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)
        
        # 初始化模型组件
        self._build_models()
        
        # 初始化优化器
        self.optimizer_g = tf.keras.optimizers.Adam(
            learning_rate=learning_rate_g, beta_1=beta1, beta_2=beta2
        )
        self.optimizer_d = tf.keras.optimizers.Adam(
            learning_rate=learning_rate_d, beta_1=beta1, beta_2=beta2
        )
        
        # 初始化损失管理器
        self.loss_manager = TotalLossManager(
            adversarial_weight=adversarial_weight,
            cycle_consistency_weight=cycle_consistency_weight,
            semantic_distance_weight=semantic_distance_weight,
            uncertainty_weight=uncertainty_weight,
            adaptive_weights=adaptive_weights,
            warmup_epochs=warmup_epochs
        )
        
        # 训练状态
        self.current_epoch = 0
        self.global_step = 0
        self.training_history = {
            'generator_loss': [],
            'discriminator_loss': [],
            'cycle_loss': [],
            'semantic_loss': [],
            'uncertainty_loss': []
        }
        
        # 设置日志
        self._setup_logging()
        
    def _build_models(self):
        """构建所有模型组件"""
        
        # 1. 自适应语义距离计算器
        self.semantic_distance_calculator = AdaptiveSemanticDistance(
            attention_dim=64,
            num_heads=4,
            hidden_dim=128
        )
        
        # 2. 域选择器
        self.domain_selector = DomainSelector(
            num_domains=self.num_domains,
            attention_dim=64,
            num_heads=4,
            selection_mode='soft'
        )
        
        # 3. 变分生成器
        self.generator = VariationalGenerator(
            feature_dim=self.feature_dim,
            attribute_dim=self.attribute_dim,
            latent_dim=self.latent_dim
        )
        
        # 4. 多层次判别器
        self.discriminator = MultiLevelDiscriminator(
            feature_dim=self.feature_dim,
            attribute_dim=self.attribute_dim,
            use_spectral_norm=True
        )
        
        # 5. 不确定性传播器
        self.uncertainty_propagator = UncertaintyPropagator(
            num_mc_samples=50,
            confidence_threshold=0.8
        )
        
        self.logger.info("所有模型组件构建完成")
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.log_dir, 'training.log')),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('ASDCGANTrainer')
    
    @tf.function
    def _train_discriminator_step(self, real_features, real_attributes, noise):
        """
        判别器训练步骤
        
        Args:
            real_features: 真实特征 [batch_size, feature_dim]
            real_attributes: 真实属性 [batch_size, attribute_dim]
            noise: 噪声 [batch_size, latent_dim]
            
        Returns:
            d_loss: 判别器损失
        """
        with tf.GradientTape() as tape:
            # 1. 生成假样本
            generator_result = self.generator([noise, real_attributes], training=True)
            fake_features = generator_result['generated_features']
            
            # 2. 判别真实样本
            real_discriminator_input = {
                'features': real_features,
                'attributes': real_attributes
            }
            real_output = self.discriminator(real_discriminator_input, training=True)
            
            # 3. 判别生成样本
            fake_discriminator_input = {
                'features': fake_features,
                'attributes': real_attributes
            }
            fake_output = self.discriminator(fake_discriminator_input, training=True)
            
            # 4. 计算判别器损失
            discriminator_outputs = {
                'real_outputs': real_output,
                'fake_outputs': fake_output
            }
            
            d_loss_result = self.loss_manager.compute_discriminator_loss(
                real_features, fake_features, discriminator_outputs, self.discriminator
            )
            d_loss = d_loss_result['total_loss']
        
        # 5. 更新判别器
        d_gradients = tape.gradient(d_loss, self.discriminator.trainable_variables)
        self.optimizer_d.apply_gradients(
            zip(d_gradients, self.discriminator.trainable_variables)
        )
        
        return d_loss
    
    @tf.function
    def _train_generator_step(self, real_features, real_attributes, noise):
        """
        生成器训练步骤
        
        Args:
            real_features: 真实特征
            real_attributes: 真实属性
            noise: 噪声
            
        Returns:
            g_loss_result: 生成器损失结果
        """
        with tf.GradientTape() as tape:
            # 1. 生成特征
            generator_result = self.generator([noise, real_attributes], training=True)
            fake_features = generator_result['generated_features']
            uncertainty = generator_result.get('uncertainty')
            
            # 2. 域选择 (模拟多域场景)
            source_attrs = [real_attributes for _ in range(self.num_domains)]
            domain_selection_result = self.domain_selector(
                [real_attributes, source_attrs], training=True
            )
            
            # 3. 语义距离计算
            context_features = tf.zeros_like(real_features)
            semantic_distance = self.semantic_distance_calculator(
                [real_attributes, real_attributes, context_features], training=True
            )
            
            # 4. 判别生成样本
            fake_discriminator_input = {
                'features': fake_features,
                'attributes': real_attributes,
                'semantic_distance': semantic_distance
            }
            fake_output = self.discriminator(fake_discriminator_input, training=True)
            
            # 5. 循环重构 (简化版)
            reconstructed_result = self.generator([fake_features, real_attributes], training=True)
            reconstructed_features = reconstructed_result['generated_features']
            
            # 6. 构建损失数据
            y_true = {
                'real_features': real_features,
                'real_attributes': real_attributes,
                'original_semantic_distance': semantic_distance
            }
            
            y_pred = {
                'generated_features': fake_features,
                'reconstructed_features': reconstructed_features,
                'predicted_attributes': real_attributes,  # 简化
                'discriminator_outputs': {'fake_outputs': fake_output},
                'uncertainty': uncertainty,
                'semantic_distance_calculator': self.semantic_distance_calculator
            }
            
            # 7. 计算生成器损失
            g_loss_result = self.loss_manager.compute_generator_loss(y_true, y_pred)
            g_loss = g_loss_result['total_loss']
        
        # 8. 更新生成器
        trainable_vars = (
            self.generator.trainable_variables +
            self.semantic_distance_calculator.trainable_variables +
            self.domain_selector.trainable_variables
        )
        
        g_gradients = tape.gradient(g_loss, trainable_vars)
        self.optimizer_g.apply_gradients(zip(g_gradients, trainable_vars))
        
        return g_loss_result
    
    def train_epoch(self, dataset):
        """
        训练一个epoch
        
        Args:
            dataset: 训练数据集
            
        Returns:
            epoch_metrics: epoch指标
        """
        epoch_start_time = time.time()
        
        # 设置当前epoch
        self.loss_manager.set_epoch(self.current_epoch)
        
        # 初始化epoch指标
        epoch_metrics = {
            'generator_loss': [],
            'discriminator_loss': [],
            'cycle_loss': [],
            'semantic_loss': [],
            'uncertainty_loss': []
        }
        
        # 训练循环
        for batch_idx, (features, attributes) in enumerate(dataset):
            batch_size = tf.shape(features)[0]
            noise = tf.random.normal([batch_size, self.latent_dim])
            
            # 训练判别器 (n_critic次)
            d_loss_sum = 0.0
            for _ in range(self.n_critic):
                d_loss = self._train_discriminator_step(features, attributes, noise)
                d_loss_sum += d_loss
            
            avg_d_loss = d_loss_sum / self.n_critic
            
            # 训练生成器 (1次)
            g_loss_result = self._train_generator_step(features, attributes, noise)
            g_loss = g_loss_result['total_loss']
            
            # 记录指标
            epoch_metrics['generator_loss'].append(float(g_loss.numpy()))
            epoch_metrics['discriminator_loss'].append(float(avg_d_loss.numpy()))
            
            # 记录组件损失
            loss_components = g_loss_result['loss_components']
            if 'cycle_consistency' in loss_components:
                epoch_metrics['cycle_loss'].append(float(loss_components['cycle_consistency'].numpy()))
            if 'semantic_distance' in loss_components:
                epoch_metrics['semantic_loss'].append(float(loss_components['semantic_distance'].numpy()))
            if 'uncertainty' in loss_components:
                epoch_metrics['uncertainty_loss'].append(float(loss_components['uncertainty'].numpy()))
            
            # 更新全局步数
            self.global_step += 1
            
            # 定期打印进度
            if batch_idx % 100 == 0:
                self.logger.info(
                    f"Epoch {self.current_epoch}, Batch {batch_idx}: "
                    f"G_loss={g_loss:.4f}, D_loss={avg_d_loss:.4f}"
                )
        
        # 计算epoch平均指标
        for key in epoch_metrics:
            if epoch_metrics[key]:
                epoch_metrics[key] = np.mean(epoch_metrics[key])
            else:
                epoch_metrics[key] = 0.0
        
        # 记录训练历史
        for key in self.training_history:
            if key.replace('_', '') in epoch_metrics:
                self.training_history[key].append(epoch_metrics[key.replace('_', '')])
        
        epoch_time = time.time() - epoch_start_time
        self.logger.info(
            f"Epoch {self.current_epoch} 完成，用时 {epoch_time:.2f}s，"
            f"G_loss={epoch_metrics['generator_loss']:.4f}, "
            f"D_loss={epoch_metrics['discriminator_loss']:.4f}"
        )
        
        return epoch_metrics
    
    def train(self, dataset, epochs, validation_dataset=None, save_interval=10):
        """
        完整训练流程
        
        Args:
            dataset: 训练数据集
            epochs: 训练轮次
            validation_dataset: 验证数据集
            save_interval: 保存间隔
        """
        self.logger.info(f"开始训练 ASDCGAN，总轮次: {epochs}")
        
        for epoch in range(epochs):
            self.current_epoch = epoch
            
            # 训练一个epoch
            epoch_metrics = self.train_epoch(dataset)
            
            # 验证 (如果提供验证集)
            if validation_dataset is not None and epoch % 5 == 0:
                val_metrics = self.validate(validation_dataset)
                self.logger.info(f"验证指标: {val_metrics}")
            
            # 保存模型
            if epoch % save_interval == 0:
                self.save_checkpoint(epoch)
            
            # 早停检查 (可选)
            if self._should_early_stop():
                self.logger.info(f"早停于 epoch {epoch}")
                break
        
        self.logger.info("训练完成")
        
        # 保存最终模型
        self.save_checkpoint(epochs, is_final=True)
    
    def validate(self, validation_dataset):
        """验证模型"""
        val_metrics = {'val_loss': []}
        
        for features, attributes in validation_dataset:
            batch_size = tf.shape(features)[0]
            noise = tf.random.normal([batch_size, self.latent_dim])
            
            # 生成特征
            generator_result = self.generator([noise, attributes], training=False)
            fake_features = generator_result['generated_features']
            
            # 计算验证损失 (简化)
            val_loss = tf.reduce_mean(tf.square(fake_features - features))
            val_metrics['val_loss'].append(float(val_loss.numpy()))
        
        val_metrics['val_loss'] = np.mean(val_metrics['val_loss'])
        return val_metrics
    
    def _should_early_stop(self):
        """早停检查"""
        # 简单的早停逻辑：如果最近10个epoch的损失没有改善
        if len(self.training_history['generator_loss']) < 20:
            return False
        
        recent_losses = self.training_history['generator_loss'][-10:]
        earlier_losses = self.training_history['generator_loss'][-20:-10]
        
        return np.mean(recent_losses) >= np.mean(earlier_losses)
    
    def save_checkpoint(self, epoch, is_final=False):
        """保存检查点"""
        checkpoint_dir = os.path.join(self.save_dir, f'epoch_{epoch}')
        if is_final:
            checkpoint_dir = os.path.join(self.save_dir, 'final')
        
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # 保存模型权重
        self.generator.save_weights(os.path.join(checkpoint_dir, 'generator.h5'))
        self.discriminator.save_weights(os.path.join(checkpoint_dir, 'discriminator.h5'))
        
        # 保存训练历史
        np.save(os.path.join(checkpoint_dir, 'training_history.npy'), self.training_history)
        
        self.logger.info(f"检查点已保存到 {checkpoint_dir}")
    
    def load_checkpoint(self, checkpoint_dir):
        """加载检查点"""
        # 加载模型权重
        self.generator.load_weights(os.path.join(checkpoint_dir, 'generator.h5'))
        self.discriminator.load_weights(os.path.join(checkpoint_dir, 'discriminator.h5'))
        
        # 加载训练历史
        history_path = os.path.join(checkpoint_dir, 'training_history.npy')
        if os.path.exists(history_path):
            self.training_history = np.load(history_path, allow_pickle=True).item()
        
        self.logger.info(f"检查点已从 {checkpoint_dir} 加载")
    
    def generate_samples(self, attributes, num_samples=1):
        """
        生成样本
        
        Args:
            attributes: 属性向量 [batch_size, attribute_dim]
            num_samples: 每个属性生成的样本数
            
        Returns:
            generated_samples: 生成的样本
            uncertainty_info: 不确定性信息
        """
        batch_size = tf.shape(attributes)[0]
        
        generated_samples = []
        uncertainty_info = []
        
        for _ in range(num_samples):
            noise = tf.random.normal([batch_size, self.latent_dim])
            
            # 生成样本
            generator_result = self.generator([noise, attributes], training=False)
            generated_features = generator_result['generated_features']
            
            # 不确定性分析
            uncertainty_result = self.uncertainty_propagator.propagate_uncertainty(
                [noise, attributes], self.generator, training=False
            )
            
            generated_samples.append(generated_features)
            uncertainty_info.append(uncertainty_result)
        
        return generated_samples, uncertainty_info

#!/usr/bin/env python3
"""
最简版ASDCGAN测试 - 回到基础，保证稳定性

策略：
1. 只保留4个核心损失：adversarial + cycle + semantic + uncertainty
2. 禁用所有复杂功能：域转换、属性损失、三元组损失等
3. 使用保守的学习率和权重
4. 先让准确率超过随机水平（>33%）
"""

import os
import sys
import argparse
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')

from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer
import torch

def main():
    parser = argparse.ArgumentParser(description='最简版ASDCGAN测试')
    parser.add_argument('--group', type=str, default='A', choices=['A', 'B', 'C', 'D', 'E'],
                        help='数据分组 (默认: A)')
    parser.add_argument('--epochs', type=int, default=10,
                        help='训练轮次 (默认: 10)')
    
    args = parser.parse_args()
    
    print("🎯 最简版ASDCGAN测试 - 回到基础")
    print("=" * 50)
    print(f"📊 数据分组: {args.group}")
    print(f"🔄 训练轮次: {args.epochs}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("🎯 最简策略:")
    print("✅ 保留: adversarial + cycle + semantic + uncertainty")
    print("❌ 禁用: 所有复杂损失和功能")
    print("🎯 目标: 准确率 > 33% (超过随机水平)")
    print("=" * 50)
    
    # 检查GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    if torch.cuda.is_available():
        print(f"   GPU: {torch.cuda.get_device_name()}")
    print()
    
    try:
        # 创建最简版训练器
        trainer = EnhancedASDCGANTrainer(
            device=device,
            batch_size=32,
            learning_rate_g=0.0001,  # 保守的学习率
            learning_rate_d=0.0002
        )
        
        # 🔥 最简配置：禁用所有复杂功能
        trainer.use_domain_transfer = False           # 禁用域转换
        trainer.use_attribute_classifier = False     # 禁用属性分类器
        trainer.adaptive_grad_clip = False           # 禁用自适应梯度裁剪
        trainer.use_triplet_attribute_loss = False   # 禁用三元组损失
        trainer.enable_grad_clip = True              # 只保留基础梯度裁剪
        trainer.max_grad_norm = 1.0                  # 保守的裁剪阈值
        
        # 🔥 最简权重：所有复杂损失权重设为0
        trainer.attribute_consistency_weight = 0.0   # 禁用属性损失
        trainer.domain_cycle_weight = 0.0            # 禁用域循环损失
        trainer.semantic_similarity_weight = 0.0     # 禁用语义相似性损失
        
        # 保守的基础权重
        trainer.adversarial_weight = 1.0
        trainer.cycle_consistency_weight = 0.1        # 降低循环损失权重
        trainer.semantic_distance_weight = 0.1       # 降低语义损失权重
        trainer.uncertainty_weight = 0.1             # 降低不确定性损失权重
        trainer.domain_selection_weight = 0.1        # 降低域选择损失权重
        
        print("🔧 最简配置:")
        print(f"- 域转换: {trainer.use_domain_transfer}")
        print(f"- 属性分类器: {trainer.use_attribute_classifier}")
        print(f"- 自适应梯度裁剪: {trainer.adaptive_grad_clip}")
        print(f"- 基础梯度裁剪: {trainer.enable_grad_clip} (阈值: {trainer.max_grad_norm})")
        print(f"- 三元组损失: {trainer.use_triplet_attribute_loss}")
        print()
        print("🔧 损失权重:")
        print(f"- 对抗损失: {trainer.adversarial_weight}")
        print(f"- 循环损失: {trainer.cycle_consistency_weight}")
        print(f"- 语义损失: {trainer.semantic_distance_weight}")
        print(f"- 不确定性损失: {trainer.uncertainty_weight}")
        print(f"- 域选择损失: {trainer.domain_selection_weight}")
        print(f"- 复杂损失: 全部为0")
        print()
        
        # 加载数据
        data_info = trainer.load_data(split_group=args.group)
        
        print("🚀 开始最简版训练...")
        print(f"💡 监控: tensorboard --logdir tensorboard")
        print(f"🌐 访问: http://localhost:6006")
        print()
        
        # 开始训练
        history = trainer.train_enhanced(epochs=args.epochs)
        
        print()
        print("🎊 最简版ASDCGAN训练完成！")
        print("=" * 50)
        
        # 分析结果
        final_accuracy = history['accuracy'][-1] if history['accuracy'] else 0.0
        best_accuracy = max(history['accuracy']) if history['accuracy'] else 0.0
        
        print(f"📊 训练结果分析:")
        print(f"   最终准确率: {final_accuracy:.2%}")
        print(f"   最佳准确率: {best_accuracy:.2%}")
        print(f"   是否超过随机水平: {'✅ 是' if best_accuracy > 0.35 else '❌ 否'}")
        print(f"   最终G损失: {history['g_loss'][-1]:.4f}")
        print(f"   最终D损失: {history['d_loss'][-1]:.4f}")
        
        if hasattr(trainer, 'best_accuracies'):
            print(f"\n🏆 最佳分类器准确率:")
            for name, acc in trainer.best_accuracies.items():
                print(f"   {name.upper()}: {acc*100:.2f}%")
        
        # 训练稳定性分析
        if len(history['g_loss']) > 5:
            recent_g_losses = history['g_loss'][-5:]
            g_loss_avg = sum(recent_g_losses) / len(recent_g_losses)
            print(f"\n📈 训练稳定性:")
            print(f"   最后5轮平均G损失: {g_loss_avg:.4f}")
            print(f"   损失是否稳定: {'✅ 是' if g_loss_avg < 100 else '❌ 否 (可能需要调整学习率)'}")
        
        print(f"\n📁 实验结果:")
        print(f"   实验目录: {trainer.experiment_dir}")
        print(f"   训练历史: {trainer.current_run_dir}/training_history.json")
        
        print(f"\n🔍 下一步建议:")
        if best_accuracy > 0.35:
            print("   ✅ 基础版本工作正常，可以考虑添加简单改进")
            print("   💡 建议：逐步添加梯度裁剪优化、简化的属性损失")
        else:
            print("   ❌ 基础版本需要进一步调试")
            print("   💡 建议：检查学习率、权重平衡、模型架构")
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
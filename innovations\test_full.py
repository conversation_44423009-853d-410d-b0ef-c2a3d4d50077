#!/usr/bin/env python3
"""
完整版ASDCGAN测试 - 包含所有核心技术调整
"""

import os
import sys
import argparse
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')

from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer
import torch

def main():
    parser = argparse.ArgumentParser(description='完整版ASDCGAN测试')
    parser.add_argument('--group', type=str, default='A', choices=['A', 'B', 'C', 'D', 'E'],
                        help='数据分组 (默认: A)')
    parser.add_argument('--epochs', type=int, default=100,
                        help='训练轮次 (默认: 100)')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小 (默认: 32)')
    parser.add_argument('--lr_g', type=float, default=0.0002,
                        help='生成器学习率 (默认: 0.0002)')
    parser.add_argument('--lr_d', type=float, default=0.0004,
                        help='判别器学习率 (默认: 0.0004)')
    
    args = parser.parse_args()
    
    print("🚀 完整版ASDCGAN测试 - 所有核心技术调整")
    print("=" * 60)
    print(f"📊 数据分组: {args.group}")
    print(f"🔄 训练轮次: {args.epochs}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("🎯 完整技术栈:")
    print("✅ 启用: CycleGAN-SD域转换")
    print("✅ 启用: ACGAN-FG属性一致性损失")
    print("✅ 启用: 自适应梯度裁剪")
    print("✅ 启用: 循环一致性损失")
    print("✅ 启用: 语义相似性损失")
    print("✅ 启用: 三元组属性损失")
    print("🎯 目标: 验证完整增强效果")
    print("=" * 60)
    
    # 检查GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    if torch.cuda.is_available():
        print(f"   GPU: {torch.cuda.get_device_name()}")
        print(f"   显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    print()
    
    try:
        # 创建完整版训练器 - 启用所有功能
        trainer = EnhancedASDCGANTrainer(
            device=device,
            batch_size=args.batch_size,
            learning_rate_g=args.lr_g,
            learning_rate_d=args.lr_d
        )
        
        # 确保所有功能都启用
        trainer.use_domain_transfer = True
        trainer.use_attribute_classifier = True  
        trainer.adaptive_grad_clip = True
        trainer.use_triplet_attribute_loss = True
        trainer.enable_grad_clip = True
        
        print("🔧 完整配置:")
        print(f"- 域转换: {trainer.use_domain_transfer} (CycleGAN-SD风格)")
        print(f"- 属性分类器: {trainer.use_attribute_classifier} (ACGAN-FG风格)")
        print(f"- 自适应梯度裁剪: {trainer.adaptive_grad_clip}")
        print(f"- 梯度裁剪阈值: {trainer.max_grad_norm}")
        print(f"- 三元组损失: {trainer.use_triplet_attribute_loss}")
        print(f"- 域循环权重: {trainer.domain_cycle_weight}")
        print(f"- 语义相似性权重: {trainer.semantic_similarity_weight}")
        print(f"- 属性一致性权重: {trainer.attribute_consistency_weight}")
        print()
        
        # 加载数据
        data_info = trainer.load_data(split_group=args.group)
        
        print("🚀 开始完整版训练...")
        print(f"💡 监控: tensorboard --logdir tensorboard")
        print(f"🌐 访问: http://localhost:6006")
        print()
        
        # 开始训练
        history = trainer.train_enhanced(epochs=args.epochs)
        
        print()
        print("🎊 完整版ASDCGAN训练完成！")
        print("=" * 60)
        print("✅ CycleGAN-SD域转换 - 完成")
        print("✅ ACGAN-FG属性一致性 - 完成") 
        print("✅ 自适应梯度裁剪 - 完成")
        print("✅ 循环一致性约束 - 完成")
        print("✅ 语义相似性保持 - 完成")
        print("✅ 三元组属性学习 - 完成")
        
        print(f"\\n📊 最终训练统计:")
        print(f"   实际训练轮次: {len(history['epoch'])}")
        print(f"   最佳生成质量: {trainer.best_loss:.4f}")
        print(f"   最终准确率: {history['accuracy'][-1]:.4f}")
        print(f"   最终G损失: {history['g_loss'][-1]:.4f}")
        print(f"   最终D损失: {history['d_loss'][-1]:.4f}")
        
        if 'best_accuracies' in trainer.__dict__:
            print(f"\\n🏆 最佳分类器准确率:")
            for name, acc in trainer.best_accuracies.items():
                print(f"   {name.upper()}: {acc*100:.2f}%")
        
        print(f"\\n📁 实验结果:")
        print(f"   实验目录: {trainer.experiment_dir}")
        print(f"   训练日志: {trainer.current_run_dir}/training.log")
        print(f"   训练历史: {trainer.current_run_dir}/training_history.json")
        print(f"   训练曲线: {trainer.current_run_dir}/training_curves.png")
        
        print(f"\\n🔍 查看结果:")
        print(f"   TensorBoard: tensorboard --logdir {trainer.tensorboard_dir}")
        print(f"   然后访问: http://localhost:6006")
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
#!/usr/bin/env python3
"""
批量运行所有组别的ACGAN-FG基线训练
依次运行A、B、C、D、E组，每组2000轮次
"""

import os
import sys
import subprocess
import time
import json
from datetime import datetime
import logging

def setup_logging():
    """设置日志系统"""
    log_dir = "logs/batch_training"
    os.makedirs(log_dir, exist_ok=True)
    
    current_time = datetime.now().strftime("%Y%m%d-%H%M%S")
    log_file = os.path.join(log_dir, f"batch_training_{current_time}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger('BatchTraining'), log_file

def run_single_group(group, epochs=2000, batch_size=120):
    """运行单个组别的训练"""
    logger = logging.getLogger('BatchTraining')
    
    logger.info(f"🚀 开始训练 Group {group} - {epochs} epochs")
    start_time = time.time()
    
    # 构建命令
    cmd = [
        sys.executable,  # 使用当前Python解释器
        "run_enhanced_baseline.py",
        "--group", group,
        "--epochs", str(epochs),
        "--batch_size", str(batch_size)
    ]
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 运行训练 - 使用实时输出
        process = subprocess.Popen(
            cmd,
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        # 实时读取输出
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())  # 实时显示输出
                output_lines.append(output)
                logger.info(f"[Group {group}] {output.strip()}")

        # 等待进程完成
        return_code = process.poll()
        full_output = ''.join(output_lines)
        
        end_time = time.time()
        duration = end_time - start_time

        if return_code == 0:
            logger.info(f"✅ Group {group} 训练成功完成!")
            logger.info(f"⏱️  训练时间: {duration/3600:.2f} 小时")
            
            # 尝试从输出中提取最佳准确率
            best_accuracy = extract_best_accuracy(full_output)
            if best_accuracy:
                logger.info(f"📊 Group {group} 最佳准确率: {best_accuracy:.4f}")

            return {
                'group': group,
                'status': 'success',
                'duration_hours': duration/3600,
                'best_accuracy': best_accuracy,
                'stdout': full_output[-1000:],  # 保存最后1000字符
                'stderr': ""
            }
        else:
            logger.error(f"❌ Group {group} 训练失败!")
            logger.error(f"返回码: {return_code}")
            logger.error(f"输出: {full_output[-500:]}")

            return {
                'group': group,
                'status': 'failed',
                'duration_hours': duration/3600,
                'return_code': return_code,
                'stdout': full_output[-1000:],
                'stderr': ""
            }
            
    except subprocess.TimeoutExpired:
        logger.error(f"⏰ Group {group} 训练超时!")
        return {
            'group': group,
            'status': 'timeout',
            'duration_hours': duration/3600
        }
    except Exception as e:
        logger.error(f"💥 Group {group} 训练出现异常: {e}")
        return {
            'group': group,
            'status': 'error',
            'error': str(e),
            'duration_hours': duration/3600 if 'duration' in locals() else 0
        }

def extract_best_accuracy(stdout_text):
    """从训练输出中提取最佳准确率"""
    try:
        lines = stdout_text.split('\n')
        best_acc = 0.0

        for line in lines:
            # 匹配新的输出格式: "Overall Best Accuracy: 0.3333"
            if 'Overall Best Accuracy:' in line:
                parts = line.split('Overall Best Accuracy:')
                if len(parts) > 1:
                    acc_str = parts[1].strip()
                    acc = float(acc_str)
                    best_acc = max(best_acc, acc)
            # 也匹配旧格式以防万一
            elif 'Best MLP Acc:' in line:
                parts = line.split('Best MLP Acc:')
                if len(parts) > 1:
                    acc_str = parts[1].strip().rstrip(']')
                    acc = float(acc_str)
                    best_acc = max(best_acc, acc)

        return best_acc if best_acc > 0 else None
    except Exception as e:
        return None

def save_results(results, log_file):
    """保存训练结果到JSON文件"""
    results_file = log_file.replace('.log', '_results.json')
    
    summary = {
        'timestamp': datetime.now().isoformat(),
        'total_groups': len(results),
        'successful_groups': len([r for r in results if r['status'] == 'success']),
        'failed_groups': len([r for r in results if r['status'] != 'success']),
        'results': results
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    return results_file

def print_summary(results):
    """打印训练结果总结"""
    logger = logging.getLogger('BatchTraining')
    
    logger.info("\n" + "="*60)
    logger.info("🎯 批量训练结果总结")
    logger.info("="*60)
    
    total_time = sum(r.get('duration_hours', 0) for r in results)
    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] != 'success']
    
    logger.info(f"📊 总体统计:")
    logger.info(f"   总训练时间: {total_time:.2f} 小时")
    logger.info(f"   成功组别: {len(successful)}/5")
    logger.info(f"   失败组别: {len(failed)}/5")
    
    if successful:
        logger.info(f"\n✅ 成功的组别:")
        for result in successful:
            acc = result.get('best_accuracy', 0)
            duration = result.get('duration_hours', 0)
            if acc is not None:
                logger.info(f"   Group {result['group']}: {acc:.4f} ({duration:.2f}h)")
            else:
                logger.info(f"   Group {result['group']}: 准确率未提取 ({duration:.2f}h)")
    
    if failed:
        logger.info(f"\n❌ 失败的组别:")
        for result in failed:
            status = result['status']
            duration = result.get('duration_hours', 0)
            logger.info(f"   Group {result['group']}: {status} ({duration:.2f}h)")
    
    # 找出最佳表现
    if successful:
        # 过滤掉best_accuracy为None的结果
        valid_results = [r for r in successful if r.get('best_accuracy') is not None]
        if valid_results:
            best_result = max(valid_results, key=lambda x: x.get('best_accuracy', 0))
            logger.info(f"\n🏆 最佳表现:")
            logger.info(f"   Group {best_result['group']}: {best_result.get('best_accuracy', 0):.4f}")
        else:
            logger.info(f"\n🏆 所有组别的准确率都未能正确提取")

def main():
    """主函数"""
    print("🚀 开始批量训练所有组别的ACGAN-FG基线模型")
    print("="*60)
    print("📋 训练计划:")
    print("   - 组别: A, B, C, D, E")
    print("   - 每组轮次: 2000 epochs")
    print("   - 批次大小: 120")
    print("   - 预计总时间: 10-15小时")
    print("="*60)
    
    # 设置日志
    logger, log_file = setup_logging()
    logger.info("批量训练开始")
    
    # 确认工作目录
    if not os.path.exists("run_enhanced_baseline.py"):
        logger.error("❌ 找不到 run_enhanced_baseline.py 文件!")
        logger.error("请确保在正确的目录下运行此脚本")
        return False
    
    # 训练配置
    groups = ['A', 'B', 'C', 'D', 'E']
    epochs = 2000
    batch_size = 120
    
    # 开始批量训练
    results = []
    total_start_time = time.time()
    
    for i, group in enumerate(groups, 1):
        logger.info(f"\n{'='*40}")
        logger.info(f"📍 进度: {i}/5 - 开始训练 Group {group}")
        logger.info(f"{'='*40}")
        
        result = run_single_group(group, epochs, batch_size)
        results.append(result)
        
        # 输出当前结果
        if result['status'] == 'success':
            acc = result.get('best_accuracy', 0)
            if acc is not None:
                logger.info(f"✅ Group {group} 完成: {acc:.4f}")
            else:
                logger.info(f"✅ Group {group} 完成: 准确率未提取")
        else:
            logger.info(f"❌ Group {group} 失败: {result['status']}")
        
        # 如果不是最后一个组别，显示剩余时间估计
        if i < len(groups):
            elapsed = time.time() - total_start_time
            avg_time_per_group = elapsed / i
            remaining_groups = len(groups) - i
            estimated_remaining = avg_time_per_group * remaining_groups
            
            logger.info(f"⏱️  预计剩余时间: {estimated_remaining/3600:.1f} 小时")
    
    # 计算总时间
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    logger.info(f"\n🎉 批量训练全部完成!")
    logger.info(f"⏱️  总耗时: {total_duration/3600:.2f} 小时")
    
    # 保存结果
    results_file = save_results(results, log_file)
    logger.info(f"💾 结果已保存到: {results_file}")
    
    # 打印总结
    print_summary(results)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断训练")
        exit(1)
    except Exception as e:
        print(f"\n💥 批量训练出现异常: {e}")
        exit(1)

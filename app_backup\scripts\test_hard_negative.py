from keras.models import load_model

import tensorflow as tf
import numpy as np
import pandas as pd
import random
from sklearn import preprocessing
from sklearn.svm import SVR
from sklearn.svm import SVC
from sklearn.metrics import confusion_matrix
from sklearn.metrics import accuracy_score
from tensorflow.keras import regularizers
from tensorflow.keras.layers import Input, Dense, Reshape, Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.layers import UpSampling2D, Conv2D, Conv1D,MaxPooling1D,UpSampling1D
from tensorflow.keras.layers import BatchNormalization, Activation, ZeroPadding2D,Embedding,concatenate
from tensorflow.keras.regularizers import l2
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LeakyReLU
from sklearn.svm import SVC,LinearSVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier

def scalar_stand(Train_X, Test_X):
    # 用训练集标准差标准化训练集以及测试集
    scalar_train = preprocessing.StandardScaler().fit(Train_X)
    #scalar_test = preprocessing.StandardScaler().fit(Test_X)
    Train_X = scalar_train.transform(Train_X)
    Test_X = scalar_train.transform(Test_X)
    return Train_X, Test_X


def feature_generation_and_diagnosis(add_quantity, test_x, test_y, autoencoder, generator, classifier, test_class_indices):
    """
    专门为难负例挖掘版本设计的特征生成和诊断函数
    修复了噪声维度和属性获取的问题
    """
    
    print(f"loading data...")
    print(f"test classes: {test_class_indices}")

    # 确定训练中未见的类别
    all_classes = np.arange(1, 16)
    seen_classes = np.setdiff1d(all_classes, test_class_indices)
    print(f"train classes: {seen_classes}")
    
    # 加载完整的属性矩阵
    attribute_matrix_df = pd.read_excel('./attribute_matrix.xlsx', index_col='no')
    attribute_matrix = attribute_matrix_df.values.astype(np.float32)
    
    Labels_train = []
    Labels_test = []
    Generated_feature = []

    samples_per_class = len(test_x) // len(test_class_indices)
    
    for i, class_idx in enumerate(test_class_indices):
        
        # 从属性矩阵中正确获取属性向量 (20维)
        attribute_vector = attribute_matrix[class_idx]
        
        attribute = [attribute_vector for _ in range(add_quantity)]
        attribute = np.array(attribute)
     
        print(f"为类别 {class_idx} 生成 {add_quantity} 个特征, 属性形状: {attribute.shape}")
           
        # 修复噪声维度：使用(128, 1)形状匹配超级优化版生成器输入
        noise_shape = (add_quantity, 128, 1)
        noise = tf.random.normal(shape=noise_shape)

        generated_feature = generator.predict([noise, attribute])
      
        Generated_feature.append(generated_feature)

        labels_train = np.full((add_quantity, 1), class_idx)
        # 确保测试标签与传入的类别索引匹配
        labels_test = np.full((samples_per_class, 1), class_idx)

        Labels_train.append(labels_train)
        Labels_test.append(labels_test)
    
    # 修复特征维度：使用512维特征匹配超级优化版生成器输出
    Generated_feature=np.array(Generated_feature).reshape(-1, 512)
    Labels_train=np.array(Labels_train).reshape(-1, 1)

    Labels_test=np.array(Labels_test).reshape(-1, 1)  
    
    # 获取测试特征的编码表示 - 超级优化版需要先编码
    # 因为超级优化版自编码器将52维编码为512维
    # 需要访问编码器模型（在超级优化版中是self.encoder）
    feature_out, _ = autoencoder(test_x)  # 自编码器返回[feature, output_sample]
    test_feature = feature_out
    
    # 超级优化版分类器返回[hidden_output, predict_attribute]两个输出
    hidden_output_train, predict_attribute_train = classifier(Generated_feature)
    new_feature_train = np.concatenate((Generated_feature, predict_attribute_train), axis=1)

    hidden_output_test, predict_attribute_test = classifier(test_feature)
    new_feature_test = np.concatenate((test_feature, predict_attribute_test), axis=1)

    train_X=new_feature_train
    train_Y=Labels_train
    
    test_X=new_feature_test
    test_Y=Labels_test

    train_X,test_X=scalar_stand(train_X, test_X)

    classifier_lsvm = LinearSVC()
    classifier_lsvm.fit(train_X, train_Y)
    Y_pred_lsvm = classifier_lsvm.predict(test_X)
    accuracy_lsvm = accuracy_score(test_Y, Y_pred_lsvm)
    
    classifier_nrf = RandomForestClassifier(n_estimators=100)
    classifier_nrf.fit(train_X, train_Y)
    Y_pred_nrf = classifier_nrf.predict(test_X)
    accuracy_nrf = accuracy_score(test_Y, Y_pred_nrf)
    
    classifier_pnb = GaussianNB()
    classifier_pnb.fit(train_X, train_Y)
    Y_pred_pnb = classifier_pnb.predict(test_X)
    accuracy_pnb = accuracy_score(test_Y, Y_pred_pnb)
    
    classifier_mlp = MLPClassifier(hidden_layer_sizes=(100, 50),
                               activation='relu',
                               solver='adam',
                               alpha=0.0001,
                               batch_size='auto',
                               learning_rate='constant',
                               max_iter=200,
                               tol=0.0001,
                               random_state=42)
    
    classifier_mlp.fit(train_X, train_Y)
    Y_pred_mlp = classifier_mlp.predict(test_X)
    accuracy_mlp = accuracy_score(test_Y, Y_pred_mlp)
   
    return accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp 
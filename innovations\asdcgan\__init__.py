"""
ASDCGAN: 自适应语义距离循环GAN

基于CycleGAN-SD和ACGAN-FG文献分析的创新融合方案。
主要特性：
- 自适应语义距离计算
- 智能域选择机制  
- 多层次循环一致性
- 不确定性感知生成

技术架构：
- 完全独立实现，不修改现有代码
- 模块化设计，便于维护和扩展
- 与现有ACGAN-FG框架兼容

创建时间: 2025-07-24
"""

__version__ = "1.0.0"

# 导入核心组件
from .models import (
    AdaptiveSemanticDistance,
    DomainSelector, 
    VariationalGenerator,
    MultiLevelDiscriminator,
    UncertaintyPropagator
)

from .losses import (
    CycleConsistencyLoss,
    SemanticDistanceLoss,
    UncertaintyLoss
)

from .training import (
    ASDCGANTrainer,
    TrainingStrategies
)

from .utils import (
    ConfigManager,
    ExperimentTracker,
    Visualization
)

__all__ = [
    # Models
    "AdaptiveSemanticDistance",
    "DomainSelector",
    "VariationalGenerator", 
    "MultiLevelDiscriminator",
    "UncertaintyPropagator",
    
    # Losses
    "CycleConsistencyLoss",
    "SemanticDistanceLoss", 
    "UncertaintyLoss",
    
    # Training
    "ASDCGANTrainer",
    "TrainingStrategies",
    
    # Utils
    "ConfigManager",
    "ExperimentTracker",
    "Visualization"
]

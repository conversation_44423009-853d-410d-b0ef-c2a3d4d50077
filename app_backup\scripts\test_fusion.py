import numpy as np
import tensorflow as tf
from sklearn.svm import LinearSVC
from sklearn.ensemble import RandomForestClassifier  
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score
import read_data


def feature_generation_and_diagnosis_with_fusion(model, num_samples, test_X, test_Y_attr, fusion_net, test_class_indices):
    """
    使用交叉注意力融合的特征生成和诊断
    修复了数据维度不匹配问题
    现在可以接收一个测试类别索引列表
    """
    batch_size = 120  # 定义批处理大小以避免OOM

    unseen_attributes = []
    
    # 基于传入的test_X动态计算每个类的样本数
    samples_per_class = len(test_X) // len(test_class_indices)

    # 动态获取每个测试类的属性
    for i, class_idx in enumerate(test_class_indices):
        # 假设test_Y_attr是按测试类别顺序排列的
        attribute_start_index = i * samples_per_class
        attr = test_Y_attr[attribute_start_index]
        unseen_attributes.append(attr)
    
    unseen_attributes = np.array(unseen_attributes)
    
    # 1. 为每个未见类生成特征 - 确保数量合理
    num_gen_per_class = num_samples // len(test_class_indices)
    gen_attributes = np.repeat(unseen_attributes, num_gen_per_class, axis=0)
    gen_labels = np.repeat(test_class_indices, num_gen_per_class, axis=0)
    
    noise_for_gen = tf.random.normal(shape=(len(gen_attributes), model.latent_dim, 1))
    generated_features = model.g([noise_for_gen, gen_attributes], training=False)
    
    # 2. 获取生成特征的语义表示
    _, generated_semantics = model.c(generated_features, training=False)
    
    # 3. 通过交叉注意力融合增强生成特征
    fused_generated_features_list = []
    for i in range(0, len(generated_features), batch_size):
        batch_gen_feats = generated_features[i:i+batch_size]
        batch_gen_sem = generated_semantics[i:i+batch_size]
        fused_batch = fusion_net([batch_gen_feats, batch_gen_sem], training=False)
        fused_generated_features_list.append(fused_batch)
    fused_generated_features = np.concatenate(fused_generated_features_list, axis=0)

    # 4. 提取测试数据的特征并融合 (分批处理)
    fused_test_features_list = []
    for i in range(0, len(test_X), batch_size):
        batch_test_X = test_X[i:i+batch_size]
        
        test_features_batch = model.encoder(batch_test_X, training=False)
        _, test_semantics_batch = model.c(test_features_batch, training=False)
        fused_batch = fusion_net([test_features_batch, test_semantics_batch], training=False)
        fused_test_features_list.append(fused_batch)
    fused_test_features = np.concatenate(fused_test_features_list, axis=0)
    
    # 5. 正确创建测试标签 - 修复维度不匹配问题
    # test_X应该有432个样本 (3个类，每个144个样本)
    actual_test_size = len(test_X)
    samples_per_class = actual_test_size // len(test_class_indices)
    
    test_labels = []
    for i, class_idx in enumerate(test_class_indices):
        test_labels.extend([class_idx] * samples_per_class)
    
    # 处理不能整除的情况
    remaining_samples = actual_test_size - len(test_labels)
    if remaining_samples > 0:
        test_labels.extend([test_class_indices[-1]] * remaining_samples)
    
    test_labels = np.array(test_labels)
    
    print(f"Generated features shape: {fused_generated_features.shape}")
    print(f"Test features shape: {fused_test_features.shape}")
    print(f"Generated labels shape: {gen_labels.shape}")
    print(f"Test labels shape: {test_labels.shape}")
    
    # 6. 训练分类器（使用融合后的特征）
    X_train_final = fused_generated_features
    Y_train_final = gen_labels
    
    X_test_final = fused_test_features
    Y_test_final = test_labels
    
    # 训练和测试四个分类器
    classifiers = {
        'lsvm': LinearSVC(random_state=42),
        'rf': RandomForestClassifier(n_estimators=100, random_state=42),
        'nb': GaussianNB(),
        'mlp': MLPClassifier(hidden_layer_sizes=(100,), max_iter=500, random_state=42)
    }
    
    accuracies = {}
    for name, clf in classifiers.items():
        try:
            clf.fit(X_train_final, Y_train_final)
            y_pred = clf.predict(X_test_final)
            accuracies[name] = accuracy_score(Y_test_final, y_pred)
            print(f"{name} accuracy: {accuracies[name]:.4f}")
        except Exception as e:
            print(f"Error training {name}: {e}")
            accuracies[name] = 0.0
    
    return accuracies['lsvm'], accuracies['rf'], accuracies['nb'], accuracies['mlp']


def feature_generation_and_diagnosis(num_samples, test_X, test_Y_attr, autoencoder, g, c):
    """
    标准的特征生成和诊断函数（不使用融合）
    用作对比基准
    """
    
    # Test classes: [2, 7, 15] which have indices [1, 6, 14] in the data
    test_class_indices = [1, 6, 14]  # These correspond to classes 2, 7, 15
    unseen_attributes = []
    
    # Get attributes for each test class
    for idx in test_class_indices:
        if idx == 1:  # class 2
            attr = test_Y_attr[0]
        elif idx == 6:  # class 7
            attr = test_Y_attr[144] 
        else:  # idx == 14, class 15
            attr = test_Y_attr[288]
        unseen_attributes.append(attr)
    
    unseen_attributes = np.array(unseen_attributes)
    
    # Generate features for each unseen class
    num_gen_per_class = num_samples // len(test_class_indices)
    gen_attributes = np.repeat(unseen_attributes, num_gen_per_class, axis=0)
    gen_labels = np.repeat(test_class_indices, num_gen_per_class, axis=0)
    
    # Generate noise and features
    noise_for_gen = tf.random.normal(shape=(len(gen_attributes), 50, 1))  # latent_dim=50
    generated_features = g([noise_for_gen, gen_attributes], training=False)
    
    # Get features from real test data
    encoder = autoencoder.layers[0]  # Assuming encoder is the first part
    test_features = encoder(test_X, training=False)
    
    # Get semantic features from classifier
    _, test_semantics = c(test_features, training=False)
    _, gen_semantics = c(generated_features, training=False)
    
    # Concatenate features (standard approach)
    fused_generated_features = np.concatenate([generated_features, gen_semantics], axis=1)
    fused_test_features = np.concatenate([test_features, test_semantics], axis=1)
    
    # Create proper test labels
    actual_test_size = len(test_X)
    samples_per_class = actual_test_size // len(test_class_indices)
    
    test_labels = []
    for i, class_idx in enumerate(test_class_indices):
        test_labels.extend([class_idx] * samples_per_class)
    
    # Handle remaining samples if any
    remaining_samples = actual_test_size - len(test_labels)
    if remaining_samples > 0:
        test_labels.extend([test_class_indices[-1]] * remaining_samples)
    
    test_labels = np.array(test_labels)
    
    # Train classifiers
    classifiers = {
        'lsvm': LinearSVC(random_state=42),
        'rf': RandomForestClassifier(n_estimators=100, random_state=42),
        'nb': GaussianNB(),
        'mlp': MLPClassifier(hidden_layer_sizes=(100,), max_iter=500, random_state=42)
    }
    
    accuracies = {}
    for name, clf in classifiers.items():
        try:
            clf.fit(fused_generated_features, gen_labels)
            y_pred = clf.predict(fused_test_features)
            accuracies[name] = accuracy_score(test_labels, y_pred)
        except Exception as e:
            print(f"Error training {name}: {e}")
            accuracies[name] = 0.0
    
    return accuracies['lsvm'], accuracies['rf'], accuracies['nb'], accuracies['mlp'] 
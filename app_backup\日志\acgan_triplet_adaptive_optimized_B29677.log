2025-07-09 15:35:05.680048: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-07-09 15:35:05.689453: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:485] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered
2025-07-09 15:35:05.700098: E external/local_xla/xla/stream_executor/cuda/cuda_dnn.cc:8473] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
2025-07-09 15:35:05.703197: E external/local_xla/xla/stream_executor/cuda/cuda_blas.cc:1471] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered
2025-07-09 15:35:05.710925: I tensorflow/core/platform/cpu_feature_guard.cc:211] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX, in other operations, rebuild TensorFlow with the appropriate compiler flags.
/usr/local/lib/python3.12/dist-packages/tensorflow_addons/utils/tfa_eol_msg.py:23: UserWarning: 

TensorFlow Addons (TFA) has ended development and introduction of new features.
TFA has entered a minimal maintenance and release mode until a planned end of life in May 2024.
Please modify downstream libraries to take dependencies from other repositories in our TensorFlow community (e.g. Keras, Keras-CV, and Keras-NLP). 

For more information see: https://github.com/tensorflow/addons/issues/2807 

  warnings.warn(
/usr/local/lib/python3.12/dist-packages/tensorflow_addons/utils/ensure_tf_install.py:53: UserWarning: Tensorflow Addons supports using Python ops for all Tensorflow versions above or equal to 2.12.0 and strictly below 2.15.0 (nightly versions are not supported). 
 The versions of TensorFlow you are currently using is 2.17.0 and is not supported. 
Some things might work, some things might not.
If you were to encounter a bug, do not file an issue.
If you want to make sure you're using a tested and supported configuration, either change the TensorFlow version or the TensorFlow Addons's version. 
You can find the compatibility matrix in TensorFlow Addon's readme:
https://github.com/tensorflow/addons
  warnings.warn(
2025-07-09 15:35:07.289690: E external/local_xla/xla/stream_executor/cuda/cuda_driver.cc:266] failed call to cuInit: CUDA_ERROR_NO_DEVICE: no CUDA-capable device is detected
2025-07-09 15:35:07.289732: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:135] retrieving CUDA diagnostic information for host: 8325d131374d
2025-07-09 15:35:07.289738: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:142] hostname: 8325d131374d
2025-07-09 15:35:07.289769: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:166] libcuda reported version is: 570.153.2
2025-07-09 15:35:07.289782: I external/local_xla/xla/stream_executor/cuda/cuda_diagnostics.cc:170] kernel reported version is: NOT_FOUND: could not find kernel module information in driver version file contents: "NVRM version: NVIDIA UNIX Open Kernel Module for x86_64  570.153.02  Release Build  (dvs-builder@U22-A23-20-3)  Tue May 13 16:34:58 UTC 2025
GCC version:  gcc version 13.3.0 (Ubuntu 13.3.0-6ubuntu2~24.04) 
"
WARNING:tensorflow:Mixed precision compatibility check (mixed_float16): WARNING
The dtype policy mixed_float16 may run slowly because this machine does not have a GPU. Only Nvidia GPUs with compute capability of at least 7.0 run quickly with mixed_float16.
If you will use compatible GPU(s) not attached to this host, e.g. by running a multi-worker model, you can ignore this warning. This message will only be logged once
loading data...
test classes: [1, 6, 14]
train classes: [0, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13]
============================================================
GPU配置检查和优化
============================================================
NVIDIA_VISIBLE_DEVICES: all
nvidia-smi 可用，输出前五行:
Failed to initialize NVML: Unknown Error

TensorFlow 未识别到任何GPU
TensorFlow内置CUDA版本: 12.8
cuDNN版本: 9
未检测到GPU设备
============================================================
开始运行 Group B 的优化版自适应实验
优化特性: 数据流水线 + 混合精度训练 + 大批处理 + GPU监控
=== 开始优化版自适应权重实验 ===
已启用混合精度训练 (float16)
已启用优化数据流水线和混合精度训练
Traceback (most recent call last):
  File "/app/acgan_triplet_adaptive_optimized.py", line 664, in <module>
    results = run_optimized_experiments(target_group=TARGET_GROUP, epochs=500, batch_size=512) 
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/acgan_triplet_adaptive_optimized.py", line 644, in run_optimized_experiments
    gan_optimized = Zero_shot_Adaptive_Optimized(group=target_group, use_adaptive=True, use_optimized_pipeline=True)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/acgan_triplet_adaptive_optimized.py", line 214, in __init__
    self.autoencoder = self.build_autoencoder()
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/acgan_triplet_adaptive_optimized.py", line 231, in build_autoencoder
    e3_attention = SelfAttention()(e3)
                   ^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/dist-packages/tf_keras/src/utils/traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/tmp/__autograph_generated_filesx7g84hj.py", line 13, in tf__call
    attention_weights = ag__.converted_call(ag__.ld(tf).nn.softmax, (ag__.converted_call(ag__.ld(tf).matmul, (ag__.ld(q), ag__.ld(k)), dict(transpose_b=True), fscope) / ag__.converted_call(ag__.ld(tf).sqrt, (ag__.converted_call(ag__.ld(tf).cast, (ag__.converted_call(ag__.ld(tf).shape, (ag__.ld(q),), None, fscope)[-1], ag__.ld(tf).float32), None, fscope),), None, fscope),), None, fscope)
                                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
TypeError: Exception encountered when calling layer "self_attention" (type SelfAttention).

in user code:

    File "/app/acgan_triplet_adaptive_optimized.py", line 58, in call  *
        attention_weights = tf.nn.softmax(tf.matmul(q, k, transpose_b=True) / tf.sqrt(tf.cast(tf.shape(q)[-1], tf.float32)))

    TypeError: `x` and `y` must have the same dtype, got tf.float16 != tf.float32.


Call arguments received by layer "self_attention" (type SelfAttention):
  • inputs=tf.Tensor(shape=(None, 256), dtype=float16)

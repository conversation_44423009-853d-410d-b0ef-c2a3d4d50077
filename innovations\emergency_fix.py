#!/usr/bin/env python3
"""
紧急修复策略 - 基于Group B成功参数的直接复制
目标: 快速恢复到60%+准确率
"""

import os
import sys
import torch
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')

def emergency_fix_test():
    """紧急修复测试 - 使用Group B的成功参数"""
    print("🚨 ASDCGAN紧急修复测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 策略: 直接复制Group B的成功参数")
    print("=" * 60)
    
    try:
        # 导入训练器
        from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer
        
        print("✅ 成功导入训练器")
        
        # 使用Group B的成功参数 (61.04%准确率)
        print("\n🔧 使用Group B成功参数...")
        trainer = EnhancedASDCGANTrainer(
            device='cuda' if torch.cuda.is_available() else 'cpu',
            batch_size=32,  # Group B成功配置
            learning_rate_g=0.0002,  # Group B成功配置
            learning_rate_d=0.0004   # Group B成功配置
        )
        
        # 使用Group B的损失权重
        trainer.adversarial_weight = 1.0  # Group B成功配置
        trainer.cycle_consistency_weight = 0.1  # Group B成功配置
        trainer.semantic_distance_weight = 0.1  # Group B成功配置
        
        print(f"   设备: {trainer.device}")
        print(f"   批次大小: {trainer.batch_size}")
        print(f"   生成器学习率: {trainer.learning_rate_g}")
        print(f"   判别器学习率: {trainer.learning_rate_d}")
        print(f"   对抗权重: {trainer.adversarial_weight}")
        print(f"   循环一致性权重: {trainer.cycle_consistency_weight}")
        print(f"   语义距离权重: {trainer.semantic_distance_weight}")
        
        # 设置Group A数据
        print("\n📊 设置Group A数据...")
        trainer.load_data(split_group='A')
        
        # 快速训练测试 (30个epoch)
        print("\n🚀 开始紧急修复训练 (30 epochs)...")
        print("   使用Group B的成功参数配置...")
        
        history = trainer.train_enhanced(epochs=30)
        
        # 分析结果
        print("\n📊 紧急修复结果:")
        if history and 'accuracy' in history and history['accuracy']:
            final_acc = history['accuracy'][-1]
            best_acc = max(history['accuracy'])
            initial_acc = history['accuracy'][0] if len(history['accuracy']) > 0 else 0
            
            print(f"   初始准确率: {initial_acc:.2f}%")
            print(f"   最终准确率: {final_acc:.2f}%")
            print(f"   最佳准确率: {best_acc:.2f}%")
            print(f"   提升幅度: {final_acc - initial_acc:+.2f}%")
            
            # 与目标对比
            target_acc = 60.69  # 之前的最佳结果
            recovery = best_acc - target_acc
            
            print(f"\n📈 恢复效果评估:")
            print(f"   目标准确率: {target_acc:.2f}%")
            print(f"   当前最佳: {best_acc:.2f}%")
            print(f"   恢复效果: {recovery:+.2f}%")
            
            # 评估恢复效果
            if best_acc >= target_acc:
                print("   ✅ 成功恢复！达到或超越之前水平")
                recommendation = "使用这些参数进行完整训练"
                success = True
            elif best_acc >= target_acc * 0.9:
                print("   ⚡ 基本恢复，接近之前水平")
                recommendation = "微调后进行完整训练"
                success = True
            elif best_acc >= target_acc * 0.8:
                print("   ⚠️ 部分恢复，仍有差距")
                recommendation = "进一步调整参数"
                success = False
            else:
                print("   ❌ 恢复失败，性能仍然很低")
                recommendation = "需要重新设计策略"
                success = False
            
            print(f"   🔧 建议: {recommendation}")
            
            return {
                'success': success,
                'initial_acc': initial_acc,
                'final_acc': final_acc,
                'best_acc': best_acc,
                'recovery': recovery,
                'recommendation': recommendation
            }
        else:
            print("   ❌ 无法获取训练结果")
            return {'success': False, 'error': '无训练历史数据'}
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

def main():
    """主函数"""
    print("开始ASDCGAN紧急修复...")
    
    result = emergency_fix_test()
    
    print("\n" + "=" * 60)
    print("🎯 紧急修复总结")
    print("=" * 60)
    
    if result['success']:
        print("✅ 紧急修复成功")
        print(f"📊 恢复效果: {result.get('recovery', 0):+.2f}%")
        print(f"🔧 建议: {result.get('recommendation', '无')}")
        
        print("\n🚀 下一步行动:")
        print("1. 使用相同参数进行完整训练 (200-500 epochs)")
        print("2. 验证在其他组别的表现")
        print("3. 进行更细致的超参数调优")
        
    else:
        print("❌ 紧急修复失败")
        print(f"错误: {result.get('error', '未知错误')}")
        
        print("\n🔧 备用方案:")
        print("1. 回到原版ACGAN-FG架构")
        print("2. 逐步添加创新组件")
        print("3. 重新评估整体策略")
    
    return result['success']

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

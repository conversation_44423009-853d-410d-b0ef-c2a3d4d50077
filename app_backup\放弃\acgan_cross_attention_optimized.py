import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,LayerNormalization,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from test_fusion import feature_generation_and_diagnosis_with_fusion
from sklearn.preprocessing import MinMaxScaler
import json

# GPU配置
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    tf.config.set_visible_devices(gpus[0], 'GPU')
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    print(e)

def residual_block(x, units):
    """改进的残差块，添加Dropout正则化"""
    shortcut = x
    
    y = Dense(units)(x)
    y = LayerNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    y = Dropout(0.1)(y)  # 添加轻微Dropout
    
    y = Dense(units)(y)
    y = LayerNormalization()(y)
    
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        x_reshaped = K.expand_dims(x, axis=1)
        q = self.query(x_reshaped)
        k = self.key(x_reshaped)
        v = self.value(x_reshaped)
        
        attention_scores = K.batch_dot(q, k, axes=[2, 2])
        attention_probs = K.softmax(attention_scores)
        context = K.batch_dot(attention_probs, v)
        context = K.squeeze(context, axis=1)
        
        return x + self.gamma * context

class CrossAttentionFusion(Layer):
    """增强版交叉注意力融合层"""
    def __init__(self, units, **kwargs):
        super(CrossAttentionFusion, self).__init__(**kwargs)
        self.units = units

    def build(self, input_shape):
        data_feature_dim = input_shape[0][-1]
        semantic_feature_dim = input_shape[1][-1]
        
        # 多头注意力
        self.num_heads = 4
        self.head_dim = self.units // self.num_heads
        
        self.query = Dense(self.units, use_bias=False)
        self.key = Dense(self.units, use_bias=False)
        self.value = Dense(self.units, use_bias=False)
        
        # 改进的输出层
        self.out_dense1 = Dense(data_feature_dim)
        self.out_dense2 = Dense(data_feature_dim)
        self.out_norm = LayerNormalization()
        self.dropout = Dropout(0.1)
        
        super(CrossAttentionFusion, self).build(input_shape)

    def call(self, inputs, training=None):
        data_features, semantic_features = inputs
        
        q = self.query(K.expand_dims(data_features, axis=1))
        k = self.key(K.expand_dims(semantic_features, axis=1))
        v = self.value(K.expand_dims(semantic_features, axis=1))
        
        # 多头注意力
        q = tf.reshape(q, (-1, 1, self.num_heads, self.head_dim))
        k = tf.reshape(k, (-1, 1, self.num_heads, self.head_dim))
        v = tf.reshape(v, (-1, 1, self.num_heads, self.head_dim))
        
        attention_scores = tf.matmul(q, k, transpose_b=True) / tf.sqrt(float(self.head_dim))
        attention_probs = tf.nn.softmax(attention_scores)
        context = tf.matmul(attention_probs, v)
        
        # 合并多头
        context = tf.reshape(context, (-1, 1, self.units))
        context = K.squeeze(context, axis=1)
        
        # 残差连接和规范化
        fused_output = self.out_dense1(concatenate([data_features, context]))
        fused_output = self.out_norm(fused_output + data_features)  # 残差连接
        fused_output = self.dropout(fused_output, training=training)
        fused_output = self.out_dense2(fused_output)
        
        return fused_output

class AdaptiveLRScheduler:
    """自适应学习率调度器"""
    def __init__(self, patience=50, factor=0.5, min_lr=1e-6):
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.best_acc = 0
        self.wait = 0
        
    def __call__(self, current_acc, optimizers):
        if current_acc > self.best_acc:
            self.best_acc = current_acc
            self.wait = 0
        else:
            self.wait += 1
            
        if self.wait >= self.patience:
            for optimizer in optimizers:
                old_lr = optimizer.learning_rate.numpy()
                new_lr = max(old_lr * self.factor, self.min_lr)
                optimizer.learning_rate.assign(new_lr)
                print(f"📉 Learning rate reduced from {old_lr:.6f} to {new_lr:.6f}")
            self.wait = 0
            return True
        return False

class EarlyStopping:
    """早停机制"""
    def __init__(self, patience=100, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.best_acc = 0
        self.wait = 0
        
    def __call__(self, current_acc):
        if current_acc > self.best_acc + self.min_delta:
            self.best_acc = current_acc
            self.wait = 0
            return False, True  # 不停止，是最佳
        else:
            self.wait += 1
            return self.wait >= self.patience, False  # 是否停止，是否最佳

class Zero_shot():
    def __init__(self):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.attribute_dim=20
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        # 优化的损失权重
        self.lambda_cla = 10 
        self.lambda_triplet = 10 
        self.lambda_crl = 0.01 
        self.lambda_fusion = 2.0  # 增加融合损失权重
        
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        # 降低初始学习率，便于后续精细调优
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005)
        self.fusion_optimizer = tf.keras.optimizers.Adam(learning_rate=0.00005)
        
        # 构建模型
        self.autoencoder= self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c= self.build_classifier()
        self.fusion_net = self.build_fusion_network()
        
        # 学习率调度器和早停
        self.lr_scheduler = AdaptiveLRScheduler(patience=30, factor=0.7)
        self.early_stopping = EarlyStopping(patience=80, min_delta=0.001)
        
    def build_autoencoder(self):
        sample = Input(shape=self.sample_shape)     
        a0=sample

        # 改进的编码器
        a1=Dense(100)(a0)
        a1=LeakyReLU(alpha=0.2)(a1)
        a1=LayerNormalization()(a1)
        a1=Dropout(0.1)(a1)

        a2=Dense(200)(a1)
        a2=LeakyReLU(alpha=0.2)(a2)
        a2=LayerNormalization()(a2)
        a2=Dropout(0.1)(a2)

        a3=Dense(256)(a2)
        a3=LeakyReLU(alpha=0.2)(a3)
        a3=LayerNormalization()(a3)
        feature=a3

        # 解码器
        a4=Dense(200)(feature)
        a4=LeakyReLU(alpha=0.2)(a4)
        a4=LayerNormalization()(a4)

        a5=Dense(100)(a4)
        a5=LeakyReLU(alpha=0.2)(a5)
        a5=LayerNormalization()(a5)

        a6=Dense(52)(a5)
        a6=LeakyReLU(alpha=0.2)(a6)
        a6=LayerNormalization()(a6)
        output_sample=a6

        autoencoder = Model(sample,[feature, output_sample])
        self.encoder = Model(sample, feature)
        return autoencoder    
        
    def build_discriminator(self):
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        d1 = Dropout(0.1)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)
        d2 = Dropout(0.1)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input,attribute],validity)

    def build_generator(self):
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        noise_embedding = Flatten()(noise)
        attribute_embedding = Dense(self.latent_dim)(attribute)
        
        g_input = concatenate([noise_embedding, attribute_embedding])

        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = LayerNormalization()(g1)
        g1 = Dropout(0.05)(g1)

        g2 = residual_block(g1, 256) 
        g3 = residual_block(g2, 256) 
        
        g3_attention = SelfAttention()(g3)
        
        generated_feature = Dense(256)(g3_attention)
        generated_feature = BatchNormalization()(generated_feature)

        return Model([noise,attribute],generated_feature)
    
    def build_classifier(self):
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        c1=Dropout(0.1)(c1)
        
        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        c2=Dropout(0.1)(c2)
        hidden_ouput=c2
               
        c3 = Dense(20,activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])

    def build_fusion_network(self):
        """构建增强版交叉注意力融合网络"""
        data_input = Input(shape=self.feature_shape, name="data_input")
        semantic_input = Input(shape=(self.attribute_dim,), name="semantic_input")
        
        # 多层交叉注意力融合
        fused_output = CrossAttentionFusion(units=128)([data_input, semantic_input])
        
        # 额外的处理层
        fused_output = Dense(256)(fused_output)
        fused_output = LeakyReLU(alpha=0.2)(fused_output)
        fused_output = LayerNormalization()(fused_output)
        fused_output = Dropout(0.1)(fused_output)
        
        return Model([data_input, semantic_input], fused_output, name="FusionNetwork")

    def save_model_checkpoint(self, epoch, accuracy, save_dir="checkpoints"):
        """保存模型检查点"""
        os.makedirs(save_dir, exist_ok=True)
        
        checkpoint_info = {
            'epoch': epoch,
            'accuracy': float(accuracy),
            'timestamp': datetime.datetime.now().isoformat()
        }
        
        # 保存模型权重
        self.autoencoder.save_weights(f"{save_dir}/autoencoder_epoch_{epoch}.weights.h5")
        self.g.save_weights(f"{save_dir}/generator_epoch_{epoch}.weights.h5")
        self.d.save_weights(f"{save_dir}/discriminator_epoch_{epoch}.weights.h5")
        self.c.save_weights(f"{save_dir}/classifier_epoch_{epoch}.weights.h5")
        self.fusion_net.save_weights(f"{save_dir}/fusion_epoch_{epoch}.weights.h5")
        
        # 保存训练信息
        with open(f"{save_dir}/checkpoint_epoch_{epoch}.json", 'w') as f:
            json.dump(checkpoint_info, f, indent=2)
            
        print(f"💾 Model checkpoint saved at epoch {epoch} with accuracy {accuracy:.4f}")

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self,current_batch_features,y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)
        
        mi_penalty=0    
        if self.bound == True:    
          mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def cycle_rank_loss(self, anchor, positive, negative):
        return self.triplet_loss(anchor, positive, negative)
    
    def train(self, epochs, batch_size, log_file=None):
        start_time = datetime.datetime.now()
        
        accuracy_list_1=[]
        accuracy_list_2=[]
        accuracy_list_3=[]
        accuracy_list_4=[]
        
        valid = -np.ones((batch_size,1) )
        fake = np.ones((batch_size,1) )
        
        PATH_train='./dataset_train_case1.npz'
        PATH_test='./dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        train_X_by_class = {i: train_data[f'training_samples_{i+1}'] for i in range(15)}
        train_Y_by_class = {i: train_data[f'training_attribute_{i+1}'] for i in range(15)}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        test_X = np.concatenate([test_data[f'testing_samples_{i+1}'] for i in [1, 6, 14]])
        test_Y = np.concatenate([test_data[f'testing_attribute_{i+1}'] for i in [1, 6, 14]])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # 重新组织数据
        current_pos = 0
        for i in range(15):
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata=all_train_X
        train_attributelabel=all_train_Y
        train_classlabel = all_train_labels
        
        testdata=test_X
        test_attributelabel=test_Y
       
        num_batches=int(traindata.shape[0]/batch_size)
        
        best_accuracy = 0
        
        for epoch in range(epochs):
            
            for batch_i in range(num_batches):
                
                start_i =batch_i * batch_size
                end_i=(batch_i + 1) * batch_size
                
                train_x=traindata[start_i:end_i]
                train_y=train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # Autoencoder and Classifier Training
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                  feature, output_sample=self.autoencoder(train_x, training=True)
                  autoencoder_loss=mean_squared_error(train_x,output_sample)      

                  hidden_ouput_c,predict_attribute_c=self.c(feature, training=True)
                  c_loss=self.classification_loss(feature,train_y, hidden_ouput_c, predict_attribute_c)

                  total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c

                # Triplet Loss Metric Learning
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                # Sample triplets
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []
                for label in train_labels:
                    pos_class_samples = train_X_by_class[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    
                    neg_class = np.random.choice([c for c in range(15) if c != label])
                    neg_class_samples = train_X_by_class[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(anchor_samples, training=True)
                    positive_features = self.encoder(positive_samples, training=True)
                    negative_features = self.encoder(negative_samples, training=True)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                # 交叉注意力融合网络训练
                self.fusion_net.trainable = True
                
                with tf.GradientTape() as tape_fusion:
                    batch_features = self.encoder(train_x, training=True)
                    _, semantic_features = self.c(batch_features, training=True)
                    
                    fused_features = self.fusion_net([batch_features, semantic_features], training=True)
                    
                    # 改进的融合损失
                    fusion_loss = tf.reduce_mean(tf.square(fused_features - batch_features))
                    # 添加语义一致性损失
                    semantic_consistency_loss = tf.reduce_mean(tf.square(
                        tf.nn.l2_normalize(fused_features, axis=1) - 
                        tf.nn.l2_normalize(batch_features, axis=1)
                    ))
                    total_fusion_loss = fusion_loss + 0.5 * semantic_consistency_loss

                grads_fusion = tape_fusion.gradient(total_fusion_loss, self.fusion_net.trainable_weights)
                self.fusion_optimizer.apply_gradients(zip(grads_fusion, self.fusion_net.trainable_weights))

                # Discriminator Training
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False
                self.fusion_net.trainable = False

                for _ in range(self.n_critic):
                  with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature = self.g([noise,train_y], training=True)
                    real_feature = self.encoder(train_x, training=False)
        
                    real_validity = self.d([real_feature,train_y], training=True)
                    fake_validity = self.d([fake_feature,train_y], training=True)  
                                           
                    d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                    d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                    d_loss = d_loss_real + d_loss_fake
                  
                  grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                  self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # Generator Training with Fusion Enhancement
                self.d.trainable = False               
                self.g.trainable = True
                self.fusion_net.trainable = False
                
                with tf.GradientTape() as tape_g:
                  noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                  Fake_feature_g = self.g([noise_g,train_y], training=True)
                  Fake_validity_g = self.d([Fake_feature_g,train_y], training=False)
                  adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
            
                  fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g, training=False)
                  classification_loss = self.classification_loss(Fake_feature_g,train_y, fake_hidden_ouput_g, Fake_classification_g)
                  
                  # Triplet loss for Generator
                  g_anchor_features = Fake_feature_g
                  g_positive_features = self.encoder(positive_samples, training=False)
                  g_negative_features = self.encoder(negative_samples, training=False)
                  triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                  
                  # 融合增强损失
                  fused_fake_features = self.fusion_net([Fake_feature_g, Fake_classification_g], training=False)
                  fusion_enhancement_loss = tf.reduce_mean(tf.square(fused_fake_features - Fake_feature_g))
                  
                  cycle_rank_loss = 0
                  if self.crl == True:
                    reconstructed_feature = self.g([noise_g, Fake_classification_g], training=True)
                    
                    negative_attributes = np.array([train_Y_by_class[np.random.choice([c for c in range(15) if c != label])][0] for label in train_labels])
                    unsimilar_generated_feature = self.g([noise_g, negative_attributes], training=True)

                    cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                           
                  total_loss = (adversarial_loss + 
                               self.lambda_cla * classification_loss + 
                               self.lambda_triplet * triplet_loss_g + 
                               self.lambda_crl * cycle_rank_loss +
                               self.lambda_fusion * fusion_enhancement_loss)
                          
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print ("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][M loss: %f][Fusion loss: %f][D loss: %f][G loss %05f ]time: %s " \
                 % (epoch, epochs,
                   batch_i, num_batches,
                   tf.reduce_mean(total_ac_loss), 
                     m_loss,
                     total_fusion_loss,
                     d_loss,
                     tf.reduce_mean(total_loss),                                                                                                              
                     elapsed_time))
        
            if epoch % 1 == 0:
                # 在测试时使用融合增强的特征生成
                accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp = feature_generation_and_diagnosis_with_fusion(self, 2000, testdata, test_attributelabel, self.fusion_net)  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)
                
                current_best = max(accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                  %(epoch, epochs,max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)))
                
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
                
                # 自适应学习率调度
                lr_reduced = self.lr_scheduler(current_best, [
                    self.autoencoder_optimizer, self.d_optimizer, self.g_optimizer,
                    self.c_optimizer, self.m_optimizer, self.fusion_optimizer
                ])
                
                # 早停检查
                should_stop, is_best = self.early_stopping(current_best)
                
                if is_best:
                    best_accuracy = current_best
                    self.save_model_checkpoint(epoch, current_best)
                    print(f"🏆 New best accuracy: {current_best:.4f}")
                
                if should_stop:
                    print(f"🛑 Early stopping triggered at epoch {epoch}")
                    break
            
        best_accuracy = max([max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)])
        print('finished! best_acc:{:.4f}'.format(best_accuracy))
        if log_file:
            log_file.write(f'finished! best_acc:{best_accuracy:.4f}\n')
            log_file.flush()

if __name__ == '__main__':
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + "_cross_attention_optimized.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"🚀 优化训练开始，目标突破88%准确率瓶颈")
    print(f"📄 日志将被记录到: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 训练日志 (优化版交叉注意力融合架构)\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**优化目标**: 突破88%准确率瓶颈\n")
        log_file.write(f"**主要改进**: 自适应学习率、早停机制、增强融合网络、模型保存\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = Zero_shot()
        gan.train(epochs=3000, batch_size=120, log_file=log_file)

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"✅ 优化训练完成，日志已保存至: {log_filename}")
#!/bin/bash
# E-UltraEnhanced 超强化实验快速启动脚本

echo "🚀 E-UltraEnhanced 超强化实验"
echo "=================================="
echo "🎯 目标: E组[9,13,15] 准确率 47.6% → 60%+"
echo "⚡ 策略: 超强化权重 + 超强化学习率"
echo "⏰ 预计时间: 3-4小时 (2000轮)"
echo "=================================="

# 显示配置对比
echo ""
echo "📊 超强化配置对比:"
echo "Triplet权重: 50.0 → 100.0 (2倍)"
echo "Center权重:  2.5 → 5.0 (2倍)" 
echo "学习率:     0.0002 → 0.0003 (1.5倍)"
echo "CRL权重:    0.1 → 0.2 (2倍)"
echo ""

# 确认启动
read -p "🤔 确认开始E-UltraEnhanced实验? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 实验取消"
    exit 1
fi

echo "🔥 开始E-UltraEnhanced超强化训练..."
echo "💡 使用 Ctrl+B, D 分离tmux会话"
echo "💡 使用 tmux attach-session -t e-ultra 重新连接"
echo ""

# 运行训练
python /app/scripts/acgan_triplet_Hybrid.py

echo ""
echo "✅ E-UltraEnhanced实验完成!"
echo "📊 请检查E组准确率是否突破60%"

#!/bin/bash

echo "🔧 重启Docker容器以修复GPU访问..."

# 停止现有容器
echo "停止现有容器..."
docker stop acgan-container 2>/dev/null || echo "容器已停止或不存在"

# 删除现有容器
echo "删除现有容器..."
docker rm acgan-container 2>/dev/null || echo "容器已删除或不存在"

# 重新启动容器，确保GPU正确映射
echo "重新启动容器，启用GPU支持..."
docker run -d \
  --name acgan-container \
  --gpus all \
  --runtime=nvidia \
  -e NVIDIA_VISIBLE_DEVICES=all \
  -e NVIDIA_DRIVER_CAPABILITIES=compute,utility \
  -v $(pwd):/app \
  -w /app \
  tensorflow/tensorflow:2.15.0-gpu \
  tail -f /dev/null

echo "等待容器启动..."
sleep 5

# 测试GPU访问
echo "测试GPU访问..."
docker exec acgan-container python -c "
import tensorflow as tf
print('TensorFlow版本:', tf.__version__)
gpus = tf.config.list_physical_devices('GPU')
print('检测到GPU数量:', len(gpus))
for i, gpu in enumerate(gpus):
    print(f'GPU {i}: {gpu}')
"

echo "✅ 容器重启完成！现在可以运行优化版模型："
echo "docker exec -it acgan-container bash"
echo "python acgan_triplet_fixed_optimized.py" 
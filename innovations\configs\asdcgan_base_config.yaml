# ASDCGAN基础配置文件
# 自适应语义距离循环GAN配置

# 模型配置
model:
  # 基础维度 (将根据实际TEP数据集自动调整)
  feature_dim: 52  # TEP数据集特征维度
  attribute_dim: 20  # TEP数据集属性维度
  latent_dim: 50
  num_domains: 5
  
  # 生成器配置
  generator_hidden_dims: [128, 64]
  generator_dropout_rate: 0.1
  
  # 判别器配置
  discriminator_hidden_dims: [256, 128, 64]
  discriminator_dropout_rate: 0.1
  use_spectral_norm: true
  
  # 自适应语义距离配置
  semantic_attention_dim: 64
  semantic_num_heads: 4
  semantic_distance_type: 'weighted_euclidean'  # 'euclidean', 'cosine', 'manhattan', 'weighted_euclidean'
  
  # 域选择配置
  domain_selection_mode: 'soft'  # 'soft', 'hard', 'gumbel'
  domain_temperature: 1.0
  
  # 不确定性配置
  uncertainty_num_samples: 50
  uncertainty_confidence_threshold: 0.8

# 训练配置
training:
  # 基础训练参数
  batch_size: 32
  epochs: 1000  # 🔥 从100增加到1000，确保充分训练
  learning_rate_g: 0.0002  # 🔥 从0.0001提高到0.0002，加快收敛
  learning_rate_d: 0.0004
  beta1: 0.5
  beta2: 0.999

  # 训练策略
  n_critic: 5  # 判别器训练次数
  warmup_epochs: 5
  save_interval: 10
  validation_interval: 5

  # 损失权重 - 🔥 重新平衡权重，解决损失过高问题
  adversarial_weight: 1.0
  cycle_consistency_weight: 1.0  # 🔥 从10.0降到1.0，减少循环损失压制
  semantic_distance_weight: 0.5  # 🔥 从5.0降到0.5，避免语义损失过大
  uncertainty_weight: 0.5  # 🔥 从1.0降到0.5，减少不确定性损失影响
  lambda_gp: 10.0  # 梯度惩罚权重保持不变
  
  # 自适应权重
  adaptive_weights: true
  adaptation_rate: 0.01
  
  # 早停配置
  early_stopping: true
  patience: 20
  min_delta: 0.001

# 数据配置
data:
  dataset_name: 'TEP'
  data_path: '/home/<USER>/hmt/ACGAN-FG-main/data'  # 您的实际数据路径
  train_split: 0.8
  validation_split: 0.2  # 从训练数据中分出验证集
  test_split: 0.0  # 测试集已经是未见类别

  # 数据预处理
  normalize: true
  standardize: true
  feature_selection: false

  # 数据增强
  data_augmentation: false
  noise_level: 0.01

# 实验配置
experiment:
  experiment_name: 'asdcgan_base_experiment'
  save_dir: './experiments'
  log_dir: './logs'
  tensorboard_dir: './tensorboard'
  
  # 日志配置
  log_level: 'INFO'
  log_to_file: true
  log_to_console: true
  
  # 保存配置
  save_model: true
  save_history: true
  save_samples: true
  
  # 可视化配置
  plot_losses: true
  plot_samples: true
  plot_uncertainty: true

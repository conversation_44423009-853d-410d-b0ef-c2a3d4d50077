#!/usr/bin/env python3
"""
简化版批量训练脚本 - 适用于容器环境
依次运行A、B、C、D、E组，每组2000轮次
"""

import os
import sys
import time
from datetime import datetime
import logging

# 添加当前目录到Python路径
sys.path.append('.')

def setup_logging():
    """设置简单的日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('BatchTraining')

def run_single_group(group, epochs=2000, batch_size=120):
    """直接导入并运行单个组别的训练"""
    logger = logging.getLogger('BatchTraining')
    
    logger.info(f"🚀 开始训练 Group {group} - {epochs} epochs")
    start_time = time.time()
    
    try:
        # 直接导入训练器
        from run_enhanced_baseline import BaselineTrainer
        
        # 创建训练器实例
        trainer = BaselineTrainer(
            group=group,
            epochs=epochs,
            batch_size=batch_size
        )
        
        # 执行训练
        trainer.train()
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"✅ Group {group} 训练成功完成!")
        logger.info(f"⏱️  训练时间: {duration/3600:.2f} 小时")
        
        # 尝试从最新日志中提取最佳准确率
        best_accuracy = extract_best_accuracy_from_logs(group)
        if best_accuracy:
            logger.info(f"📊 Group {group} 最佳准确率: {best_accuracy:.4f}")
        
        return {
            'group': group,
            'status': 'success',
            'duration_hours': duration/3600,
            'best_accuracy': best_accuracy
        }
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        logger.error(f"💥 Group {group} 训练出现异常: {e}")
        
        return {
            'group': group,
            'status': 'error',
            'duration_hours': duration/3600,
            'error': str(e)
        }

def extract_best_accuracy_from_logs(group):
    """从日志文件中提取最佳准确率"""
    try:
        # 查找最新的日志目录
        log_base = "logs/baseline"
        if not os.path.exists(log_base):
            return None
            
        group_dirs = [d for d in os.listdir(log_base) if d.startswith(f"Group-{group}_")]
        if not group_dirs:
            return None
            
        # 获取最新的目录
        latest_dir = sorted(group_dirs)[-1]
        log_file = os.path.join(log_base, latest_dir, "training.log")
        
        if not os.path.exists(log_file):
            return None
            
        # 读取日志文件并提取最佳准确率
        best_acc = 0.0
        with open(log_file, 'r') as f:
            for line in f:
                if 'Best MLP Acc:' in line:
                    try:
                        # 提取格式如: [Epoch 100/2000] [Best MLP Acc: 0.8504]
                        parts = line.split('Best MLP Acc:')
                        if len(parts) > 1:
                            acc_str = parts[1].strip().rstrip(']')
                            acc = float(acc_str)
                            best_acc = max(best_acc, acc)
                    except:
                        continue
        
        return best_acc if best_acc > 0 else None
    except:
        return None

def print_summary(results):
    """打印训练结果总结"""
    logger = logging.getLogger('BatchTraining')
    
    logger.info("\n" + "="*60)
    logger.info("🎯 批量训练结果总结")
    logger.info("="*60)
    
    total_time = sum(r.get('duration_hours', 0) for r in results)
    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] != 'success']
    
    logger.info(f"📊 总体统计:")
    logger.info(f"   总训练时间: {total_time:.2f} 小时")
    logger.info(f"   成功组别: {len(successful)}/5")
    logger.info(f"   失败组别: {len(failed)}/5")
    
    if successful:
        logger.info(f"\n✅ 成功的组别:")
        for result in successful:
            acc = result.get('best_accuracy', 0)
            duration = result.get('duration_hours', 0)
            if acc:
                logger.info(f"   Group {result['group']}: {acc:.4f} ({duration:.2f}h)")
            else:
                logger.info(f"   Group {result['group']}: 完成 ({duration:.2f}h)")
    
    if failed:
        logger.info(f"\n❌ 失败的组别:")
        for result in failed:
            status = result['status']
            duration = result.get('duration_hours', 0)
            logger.info(f"   Group {result['group']}: {status} ({duration:.2f}h)")
    
    # 找出最佳表现
    if successful:
        best_results = [r for r in successful if r.get('best_accuracy', 0) > 0]
        if best_results:
            best_result = max(best_results, key=lambda x: x.get('best_accuracy', 0))
            logger.info(f"\n🏆 最佳表现:")
            logger.info(f"   Group {best_result['group']}: {best_result.get('best_accuracy', 0):.4f}")

def main():
    """主函数"""
    print("🚀 开始批量训练所有组别的ACGAN-FG基线模型")
    print("="*60)
    print("📋 训练计划:")
    print("   - 组别: A, B, C, D, E")
    print("   - 每组轮次: 2000 epochs")
    print("   - 批次大小: 120")
    print("   - 预计总时间: 10-15小时")
    print("="*60)
    
    # 设置日志
    logger = setup_logging()
    logger.info("批量训练开始")
    
    # 确认必要文件存在
    if not os.path.exists("run_enhanced_baseline.py"):
        logger.error("❌ 找不到 run_enhanced_baseline.py 文件!")
        logger.error("请确保在正确的目录下运行此脚本")
        return False
    
    # 训练配置
    groups = ['A', 'B', 'C', 'D', 'E']
    epochs = 2000
    batch_size = 120
    
    # 开始批量训练
    results = []
    total_start_time = time.time()
    
    for i, group in enumerate(groups, 1):
        logger.info(f"\n{'='*40}")
        logger.info(f"📍 进度: {i}/5 - 开始训练 Group {group}")
        logger.info(f"{'='*40}")
        
        result = run_single_group(group, epochs, batch_size)
        results.append(result)
        
        # 输出当前结果
        if result['status'] == 'success':
            acc = result.get('best_accuracy', 0)
            if acc:
                logger.info(f"✅ Group {group} 完成: {acc:.4f}")
            else:
                logger.info(f"✅ Group {group} 完成")
        else:
            logger.info(f"❌ Group {group} 失败: {result['status']}")
        
        # 如果不是最后一个组别，显示剩余时间估计
        if i < len(groups):
            elapsed = time.time() - total_start_time
            avg_time_per_group = elapsed / i
            remaining_groups = len(groups) - i
            estimated_remaining = avg_time_per_group * remaining_groups
            
            logger.info(f"⏱️  预计剩余时间: {estimated_remaining/3600:.1f} 小时")
    
    # 计算总时间
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    logger.info(f"\n🎉 批量训练全部完成!")
    logger.info(f"⏱️  总耗时: {total_duration/3600:.2f} 小时")
    
    # 打印总结
    print_summary(results)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断训练")
        exit(1)
    except Exception as e:
        print(f"\n💥 批量训练出现异常: {e}")
        import traceback
        traceback.print_exc()
        exit(1)

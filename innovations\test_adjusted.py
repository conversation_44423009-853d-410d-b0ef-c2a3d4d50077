#!/usr/bin/env python3
"""
调整版ASDCGAN测试 - 优化学习率和权重平衡

调整策略：
1. 提高生成器学习率：0.0001 → 0.0002
2. 增加循环损失权重：0.1 → 0.5
3. 平衡语义和不确定性权重
4. 保持训练稳定的前提下提升学习效果
"""

import os
import sys
import argparse
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')

from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer
import torch

def main():
    parser = argparse.ArgumentParser(description='调整版ASDCGAN测试')
    parser.add_argument('--group', type=str, default='A', choices=['A', 'B', 'C', 'D', 'E'],
                        help='数据分组 (默认: A)')
    parser.add_argument('--epochs', type=int, default=20,
                        help='训练轮次 (默认: 20)')
    
    args = parser.parse_args()
    
    print("🔧 调整版ASDCGAN测试 - 优化配置")
    print("=" * 50)
    print(f"📊 数据分组: {args.group}")
    print(f"🔄 训练轮次: {args.epochs}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("🎯 调整策略:")
    print("✅ 提高G学习率: 0.0001 → 0.0002")
    print("✅ 增加循环损失权重: 0.1 → 0.5")
    print("✅ 平衡语义和不确定性权重")
    print("✅ 保持复杂功能禁用")
    print("🎯 目标: 准确率 > 40%")
    print("=" * 50)
    
    # 检查GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    if torch.cuda.is_available():
        print(f"   GPU: {torch.cuda.get_device_name()}")
    print()
    
    try:
        # 创建调整版训练器
        trainer = EnhancedASDCGANTrainer(
            device=device,
            batch_size=32,
            learning_rate_g=0.0002,  # 🔥 提高生成器学习率
            learning_rate_d=0.0002   # 🔥 平衡判别器学习率
        )
        
        # 🔥 保持最简配置：禁用所有复杂功能
        trainer.use_domain_transfer = False           # 禁用域转换
        trainer.use_attribute_classifier = False     # 禁用属性分类器
        trainer.adaptive_grad_clip = False           # 禁用自适应梯度裁剪
        trainer.use_triplet_attribute_loss = False   # 禁用三元组损失
        trainer.enable_grad_clip = True              # 只保留基础梯度裁剪
        trainer.max_grad_norm = 1.0                  # 保守的裁剪阈值
        
        # 🔥 禁用复杂损失权重
        trainer.attribute_consistency_weight = 0.0   # 禁用属性损失
        trainer.domain_cycle_weight = 0.0            # 禁用域循环损失
        trainer.semantic_similarity_weight = 0.0     # 禁用语义相似性损失
        
        # 🔥 调整基础权重 - 重新平衡
        trainer.adversarial_weight = 1.0
        trainer.cycle_consistency_weight = 0.5       # 🔥 提高循环损失权重
        trainer.semantic_distance_weight = 0.2       # 🔥 适中的语义损失权重
        trainer.uncertainty_weight = 0.2             # 🔥 适中的不确定性损失权重
        trainer.domain_selection_weight = 0.1        # 保持域选择损失权重较小
        
        print("🔧 调整后配置:")
        print(f"- 学习率 G/D: {trainer.learning_rate_g:.4f} / {trainer.learning_rate_d:.4f}")
        print(f"- 域转换: {trainer.use_domain_transfer}")
        print(f"- 属性分类器: {trainer.use_attribute_classifier}")
        print(f"- 基础梯度裁剪: {trainer.enable_grad_clip} (阈值: {trainer.max_grad_norm})")
        print()
        print("🔧 优化后权重:")
        print(f"- 对抗损失: {trainer.adversarial_weight}")
        print(f"- 循环损失: {trainer.cycle_consistency_weight} (↑)")
        print(f"- 语义损失: {trainer.semantic_distance_weight} (↑)")
        print(f"- 不确定性损失: {trainer.uncertainty_weight} (↑)")
        print(f"- 域选择损失: {trainer.domain_selection_weight}")
        print(f"- 复杂损失: 全部为0")
        print()
        
        # 加载数据
        data_info = trainer.load_data(split_group=args.group)
        
        print("🚀 开始调整版训练...")
        print(f"💡 监控: tensorboard --logdir tensorboard")
        print(f"🌐 访问: http://localhost:6006")
        print()
        
        # 开始训练
        history = trainer.train_enhanced(epochs=args.epochs)
        
        print()
        print("🎊 调整版ASDCGAN训练完成！")
        print("=" * 50)
        
        # 分析结果
        final_accuracy = history['accuracy'][-1] if history['accuracy'] else 0.0
        best_accuracy = max(history['accuracy']) if history['accuracy'] else 0.0
        
        # 训练稳定性分析
        if len(history['g_loss']) > 3:
            recent_g_losses = history['g_loss'][-3:]
            g_loss_avg = sum(recent_g_losses) / len(recent_g_losses)
            g_loss_trend = "上升" if history['g_loss'][-1] > history['g_loss'][0] else "下降"
        else:
            g_loss_avg = history['g_loss'][-1] if history['g_loss'] else 0
            g_loss_trend = "未知"
        
        print(f"📊 训练结果分析:")
        print(f"   最终准确率: {final_accuracy:.2%}")
        print(f"   最佳准确率: {best_accuracy:.2%}")
        print(f"   准确率提升: {'✅ 是' if best_accuracy > 0.35 else '❌ 否'}")
        print(f"   最终G损失: {history['g_loss'][-1]:.4f}")
        print(f"   最终D损失: {history['d_loss'][-1]:.4f}")
        
        print(f"\n📈 训练稳定性:")
        print(f"   最后3轮平均G损失: {g_loss_avg:.4f}")
        print(f"   G损失趋势: {g_loss_trend}")
        print(f"   训练稳定性: {'✅ 好' if g_loss_avg < 1000 else '❌ 不稳定'}")
        
        if hasattr(trainer, 'best_accuracies'):
            print(f"\n🏆 最佳分类器准确率:")
            for name, acc in trainer.best_accuracies.items():
                print(f"   {name.upper()}: {acc*100:.2f}%")
        
        # 与基础版本对比
        print(f"\n🔄 与基础版本对比:")
        print(f"   基础版本最佳准确率: 33.33%")
        print(f"   调整版本最佳准确率: {best_accuracy:.2%}")
        improvement = best_accuracy - 0.3333
        print(f"   准确率提升: {improvement:.2%} ({'✅ 改善' if improvement > 0 else '❌ 未改善'})")
        
        print(f"\n📁 实验结果:")
        print(f"   实验目录: {trainer.experiment_dir}")
        print(f"   训练历史: {trainer.current_run_dir}/training_history.json")
        
        print(f"\n🔍 下一步建议:")
        if best_accuracy > 0.4:
            print("   ✅ 调整效果良好，可以考虑:")
            print("   💡 1. 进一步训练更多轮次")
            print("   💡 2. 尝试添加简单的梯度裁剪优化")
            print("   💡 3. 逐步启用简化的属性损失")
        elif best_accuracy > 0.35:
            print("   🟡 有小幅改善，建议:")
            print("   💡 1. 进一步微调权重平衡")
            print("   💡 2. 尝试不同的学习率组合")
            print("   💡 3. 增加训练轮次观察趋势")
        else:
            print("   ❌ 调整效果不明显，可能需要:")
            print("   💡 1. 检查模型架构是否有根本问题")
            print("   💡 2. 尝试更大的学习率")
            print("   💡 3. 简化模型复杂度")
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
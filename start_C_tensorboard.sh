#!/bin/bash
echo "🔥 启动C组TensorBoard监控"
echo "================================"

# 检查日志目录
if [ ! -d "./tensorboard_logs/C_Hybrid_realtime" ]; then
    echo "❌ C组日志目录不存在，请先运行C组实验"
    exit 1
fi

echo "✅ 日志目录检查通过"
echo "🚀 启动TensorBoard..."

# 启动TensorBoard
tensorboard --logdir=./tensorboard_logs/C_Hybrid_realtime --host=0.0.0.0 --port=6008 &

echo "📊 TensorBoard已启动"
echo "🌐 访问地址: http://localhost:6008"
echo "⏹️  停止命令: pkill tensorboard"
echo ""
echo "💡 提示:"
echo "   - C组专用监控: http://localhost:6008"
echo "   - 全组对比监控: tensorboard --logdir=./tensorboard_logs --port=6006"
echo "   - 查看进程: ps aux | grep tensorboard"
"""
多层次判别器模块

基于多尺度特征判别的增强判别器，支持特征级、属性级和语义级的多层次判别。

核心功能：
1. 多尺度特征提取和判别
2. 特征级真假判别
3. 属性级一致性判别
4. 语义级相似度判别
5. 梯度惩罚和谱归一化

技术特点：
- 多分支判别架构
- 渐进式特征提取
- 自适应权重融合
- 稳定训练机制
"""

import tensorflow as tf
from tensorflow.keras.layers import (
    Dense, LayerNormalization, Dropout, 
    BatchNormalization, LeakyReLU, Concatenate,
    GlobalAveragePooling1D, Conv1D
)
from tensorflow.keras.models import Model
import numpy as np


class SpectralNormalization(tf.keras.layers.Wrapper):
    """谱归一化层"""
    
    def __init__(self, layer, iteration=1, eps=1e-12, training=True, **kwargs):
        self.iteration = iteration
        self.eps = eps
        self.do_power_iteration = training
        if not isinstance(layer, tf.keras.layers.Layer):
            raise ValueError('Please initialize `SpectralNormalization` layer with a '
                           '`Layer` instance. You passed: {input}'.format(input=layer))
        super(SpectralNormalization, self).__init__(layer, **kwargs)

    def build(self, input_shape):
        self.layer.build(input_shape)
        self.w = self.layer.kernel
        self.w_shape = self.w.shape.as_list()
        self.v = self.add_weight(shape=(1, self.w_shape[0] * self.w_shape[1]),
                                initializer='random_normal',
                                name='sn_v',
                                trainable=False,
                                dtype=self.layer.dtype)
        self.u = self.add_weight(shape=(1, self.w_shape[-1]),
                                initializer='random_normal', 
                                name='sn_u',
                                trainable=False,
                                dtype=self.layer.dtype)
        super(SpectralNormalization, self).build()

    def call(self, inputs, training=None):
        self.update_weights()
        output = self.layer(inputs)
        return output

    def update_weights(self):
        w_reshaped = tf.reshape(self.w, [-1, self.w_shape[-1]])
        
        u_hat = self.u
        v_hat = self.v
        
        if self.do_power_iteration:
            for _ in range(self.iteration):
                v_hat = tf.nn.l2_normalize(tf.matmul(u_hat, tf.transpose(w_reshaped)))
                u_hat = tf.nn.l2_normalize(tf.matmul(v_hat, w_reshaped))
        
        sigma = tf.matmul(tf.matmul(v_hat, w_reshaped), tf.transpose(u_hat))
        self.u.assign(u_hat)
        self.v.assign(v_hat)
        self.layer.kernel.assign(self.w / sigma)


class FeatureLevelDiscriminator(tf.keras.layers.Layer):
    """特征级判别器"""
    
    def __init__(self, 
                 hidden_dims=[256, 128, 64],
                 dropout_rate=0.1,
                 use_spectral_norm=True,
                 **kwargs):
        super(FeatureLevelDiscriminator, self).__init__(**kwargs)
        
        self.hidden_dims = hidden_dims
        self.dropout_rate = dropout_rate
        self.use_spectral_norm = use_spectral_norm
        
        # 构建判别网络
        self.discriminator_layers = []
        for i, hidden_dim in enumerate(hidden_dims):
            dense_layer = Dense(hidden_dim, name=f'feature_disc_dense_{i}')
            if use_spectral_norm:
                dense_layer = SpectralNormalization(dense_layer)
            
            self.discriminator_layers.extend([
                dense_layer,
                LeakyReLU(alpha=0.2),
                LayerNormalization(),
                Dropout(dropout_rate)
            ])
        
        # 输出层
        output_layer = Dense(1, name='feature_disc_output')
        if use_spectral_norm:
            output_layer = SpectralNormalization(output_layer)
        self.output_layer = output_layer
        
    def call(self, inputs, training=None):
        """
        特征级判别
        
        Args:
            inputs: [features, attributes]
                   - features: 特征向量 [batch_size, feature_dim]
                   - attributes: 属性向量 [batch_size, attribute_dim]
                   
        Returns:
            validity: 真假判别结果 [batch_size, 1]
        """
        features, attributes = inputs
        
        # 组合特征和属性
        combined = tf.concat([features, attributes], axis=-1)
        
        x = combined
        for layer in self.discriminator_layers:
            x = layer(x, training=training)
        
        validity = self.output_layer(x)
        
        return validity


class AttributeLevelDiscriminator(tf.keras.layers.Layer):
    """属性级判别器"""
    
    def __init__(self,
                 attribute_dim=20,
                 hidden_dims=[128, 64],
                 dropout_rate=0.1,
                 **kwargs):
        super(AttributeLevelDiscriminator, self).__init__(**kwargs)
        
        self.attribute_dim = attribute_dim
        self.hidden_dims = hidden_dims
        self.dropout_rate = dropout_rate
        
        # 特征到属性的映射网络
        self.feature_to_attr = tf.keras.Sequential([
            Dense(hidden_dims[0], activation='relu'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(hidden_dims[1], activation='relu'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(attribute_dim, activation='sigmoid')
        ])
        
        # 属性一致性判别网络
        self.consistency_discriminator = tf.keras.Sequential([
            Dense(64, activation='relu'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(32, activation='relu'),
            Dense(1, activation='sigmoid')
        ])
        
    def call(self, inputs, training=None):
        """
        属性级判别
        
        Args:
            inputs: [features, true_attributes]
                   - features: 特征向量 [batch_size, feature_dim]
                   - true_attributes: 真实属性 [batch_size, attribute_dim]
                   
        Returns:
            result: {
                'predicted_attributes': 预测属性,
                'consistency_score': 一致性分数,
                'attribute_loss': 属性预测损失
            }
        """
        features, true_attributes = inputs
        
        # 从特征预测属性
        predicted_attributes = self.feature_to_attr(features, training=training)
        
        # 计算属性差异
        attr_diff = tf.abs(predicted_attributes - true_attributes)
        
        # 判别属性一致性
        consistency_score = self.consistency_discriminator(attr_diff, training=training)
        
        # 计算属性预测损失
        attribute_loss = tf.keras.losses.binary_crossentropy(
            true_attributes, predicted_attributes
        )
        
        return {
            'predicted_attributes': predicted_attributes,
            'consistency_score': consistency_score,
            'attribute_loss': tf.reduce_mean(attribute_loss)
        }


class SemanticLevelDiscriminator(tf.keras.layers.Layer):
    """语义级判别器"""
    
    def __init__(self,
                 hidden_dims=[128, 64],
                 dropout_rate=0.1,
                 **kwargs):
        super(SemanticLevelDiscriminator, self).__init__(**kwargs)
        
        self.hidden_dims = hidden_dims
        self.dropout_rate = dropout_rate
        
        # 语义特征提取器
        self.semantic_extractor = tf.keras.Sequential([
            Dense(hidden_dims[0], activation='relu'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(hidden_dims[1], activation='tanh')
        ])
        
        # 语义相似度判别器
        self.similarity_discriminator = tf.keras.Sequential([
            Dense(64, activation='relu'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(32, activation='relu'),
            Dense(1, activation='sigmoid')
        ])
        
    def call(self, inputs, training=None):
        """
        语义级判别
        
        Args:
            inputs: [features1, features2, semantic_distance]
                   - features1: 第一个特征向量 [batch_size, feature_dim]
                   - features2: 第二个特征向量 [batch_size, feature_dim]
                   - semantic_distance: 语义距离 [batch_size, 1]
                   
        Returns:
            semantic_validity: 语义有效性分数 [batch_size, 1]
        """
        features1, features2, semantic_distance = inputs
        
        # 提取语义特征
        semantic1 = self.semantic_extractor(features1, training=training)
        semantic2 = self.semantic_extractor(features2, training=training)
        
        # 计算语义特征差异
        semantic_diff = tf.abs(semantic1 - semantic2)
        
        # 组合语义差异和距离信息
        combined = tf.concat([semantic_diff, semantic_distance], axis=-1)
        
        # 判别语义有效性
        semantic_validity = self.similarity_discriminator(combined, training=training)
        
        return semantic_validity


class MultiLevelDiscriminator(tf.keras.layers.Layer):
    """
    多层次判别器
    
    集成特征级、属性级和语义级判别，提供全面的真假判别能力。
    """
    
    def __init__(self,
                 feature_dim=256,
                 attribute_dim=20,
                 feature_hidden_dims=[256, 128, 64],
                 attribute_hidden_dims=[128, 64],
                 semantic_hidden_dims=[128, 64],
                 dropout_rate=0.1,
                 use_spectral_norm=True,
                 level_weights=[1.0, 0.5, 0.3],
                 **kwargs):
        """
        初始化多层次判别器
        
        Args:
            feature_dim: 特征维度
            attribute_dim: 属性维度
            feature_hidden_dims: 特征级判别器隐藏层维度
            attribute_hidden_dims: 属性级判别器隐藏层维度
            semantic_hidden_dims: 语义级判别器隐藏层维度
            dropout_rate: Dropout比率
            use_spectral_norm: 是否使用谱归一化
            level_weights: 各级别损失权重 [feature, attribute, semantic]
        """
        super(MultiLevelDiscriminator, self).__init__(**kwargs)
        
        self.feature_dim = feature_dim
        self.attribute_dim = attribute_dim
        self.dropout_rate = dropout_rate
        self.use_spectral_norm = use_spectral_norm
        self.level_weights = level_weights
        
        # 特征级判别器
        self.feature_discriminator = FeatureLevelDiscriminator(
            hidden_dims=feature_hidden_dims,
            dropout_rate=dropout_rate,
            use_spectral_norm=use_spectral_norm
        )
        
        # 属性级判别器
        self.attribute_discriminator = AttributeLevelDiscriminator(
            attribute_dim=attribute_dim,
            hidden_dims=attribute_hidden_dims,
            dropout_rate=dropout_rate
        )
        
        # 语义级判别器
        self.semantic_discriminator = SemanticLevelDiscriminator(
            hidden_dims=semantic_hidden_dims,
            dropout_rate=dropout_rate
        )
        
        # 融合网络
        self.fusion_network = tf.keras.Sequential([
            Dense(64, activation='relu'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(32, activation='relu'),
            Dense(1)  # 最终判别结果
        ])
        
    def call(self, inputs, training=None):
        """
        多层次判别
        
        Args:
            inputs: {
                'features': 特征向量 [batch_size, feature_dim],
                'attributes': 属性向量 [batch_size, attribute_dim],
                'reference_features': 参考特征 (可选) [batch_size, feature_dim],
                'semantic_distance': 语义距离 (可选) [batch_size, 1]
            }
            
        Returns:
            result: {
                'feature_validity': 特征级判别结果,
                'attribute_result': 属性级判别结果,
                'semantic_validity': 语义级判别结果 (如果提供参考),
                'final_validity': 最终融合判别结果,
                'total_loss': 总损失
            }
        """
        features = inputs['features']
        attributes = inputs['attributes']
        reference_features = inputs.get('reference_features', None)
        semantic_distance = inputs.get('semantic_distance', None)
        
        # 1. 特征级判别
        feature_validity = self.feature_discriminator(
            [features, attributes], training=training
        )
        
        # 2. 属性级判别
        attribute_result = self.attribute_discriminator(
            [features, attributes], training=training
        )
        
        # 3. 语义级判别 (如果提供参考特征)
        semantic_validity = None
        if reference_features is not None and semantic_distance is not None:
            semantic_validity = self.semantic_discriminator(
                [features, reference_features, semantic_distance], 
                training=training
            )
        
        # 4. 融合多层次结果
        fusion_inputs = [
            feature_validity,
            attribute_result['consistency_score']
        ]
        
        if semantic_validity is not None:
            fusion_inputs.append(semantic_validity)
        
        # 组合所有判别结果
        combined_validity = tf.concat(fusion_inputs, axis=-1)
        final_validity = self.fusion_network(combined_validity, training=training)
        
        # 5. 计算总损失
        total_loss = (
            self.level_weights[0] * tf.reduce_mean(tf.square(feature_validity)) +
            self.level_weights[1] * attribute_result['attribute_loss']
        )
        
        if semantic_validity is not None:
            total_loss += self.level_weights[2] * tf.reduce_mean(tf.square(semantic_validity))
        
        return {
            'feature_validity': feature_validity,
            'attribute_result': attribute_result,
            'semantic_validity': semantic_validity,
            'final_validity': final_validity,
            'total_loss': total_loss
        }
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'feature_dim': self.feature_dim,
            'attribute_dim': self.attribute_dim,
            'dropout_rate': self.dropout_rate,
            'use_spectral_norm': self.use_spectral_norm,
            'level_weights': self.level_weights
        })
        return config

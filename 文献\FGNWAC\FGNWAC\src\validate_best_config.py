#!/usr/bin/env python3
"""
验证最佳超参数配置
使用搜索得到的最佳配置进行完整训练验证
"""

import argparse
import json
import os
import time
from datetime import datetime
import torch

from train import Config, ZeroShotTrainer


def load_best_config(split_name):
    """加载最佳配置"""
    config_path = f'hyperparameter_search_results/final_best_config_split_{split_name}.json'
    
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            best_config = json.load(f)
        return best_config
    else:
        print(f"❌ 找不到最佳配置文件: {config_path}")
        return None


def create_validation_config(best_config, epochs=500):
    """创建验证配置"""
    config = Config()
    
    # 应用最佳超参数
    config.lambda_ar = best_config['best_lambda_ar']
    config.lr = best_config['best_lr']
    config.epochs = epochs
    
    # 设置验证专用的保存路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    config.save_dir = f'./validation_results/best_config_{timestamp}'
    config.log_dir = f'./validation_logs/best_config_{timestamp}'
    
    # 创建目录
    os.makedirs(config.save_dir, exist_ok=True)
    os.makedirs(config.log_dir, exist_ok=True)
    
    return config


def create_baseline_config(epochs=500):
    """创建基线配置 (原始参数)"""
    config = Config()
    
    # 使用原始默认参数
    config.lambda_ar = 0.5  # 原始默认值
    config.lr = 0.0001      # 原始默认值
    config.epochs = epochs
    
    # 设置基线专用的保存路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    config.save_dir = f'./validation_results/baseline_config_{timestamp}'
    config.log_dir = f'./validation_logs/baseline_config_{timestamp}'
    
    # 创建目录
    os.makedirs(config.save_dir, exist_ok=True)
    os.makedirs(config.log_dir, exist_ok=True)
    
    return config


def run_validation(config, split_name, config_type="best"):
    """运行验证训练"""
    print(f"\n🚀 开始{config_type}配置验证")
    print("-" * 50)
    print(f"Lambda_AR: {config.lambda_ar}")
    print(f"学习率: {config.lr}")
    print(f"训练轮数: {config.epochs}")
    print(f"数据分组: {split_name}")
    print(f"保存路径: {config.save_dir}")
    
    try:
        # 创建训练器
        trainer = ZeroShotTrainer(config)
        
        # 准备数据
        print("\n📊 准备数据...")
        data = trainer.prepare_data(split_name)
        
        # 开始训练
        print(f"\n🎯 开始训练 ({config_type}配置)...")
        start_time = time.time()

        results = trainer.train(split_name)

        end_time = time.time()
        training_time = (end_time - start_time) / 60  # 转换为分钟
        
        print(f"\n✅ {config_type}配置训练完成!")
        print(f"   最终准确率: {results['accuracy']:.4f}")
        print(f"   训练时间: {training_time:.1f}分钟")
        
        # 保存验证结果
        validation_result = {
            'config_type': config_type,
            'split_name': split_name,
            'lambda_ar': config.lambda_ar,
            'lr': config.lr,
            'epochs': config.epochs,
            'final_accuracy': float(results['accuracy']),
            'avg_fid': float(results.get('avg_fid', 0)),
            'avg_mmd': float(results.get('avg_mmd', 0)),
            'training_time_minutes': training_time,
            'timestamp': datetime.now().isoformat(),
            'save_dir': config.save_dir
        }
        
        # 保存结果
        result_file = os.path.join(config.save_dir, 'validation_result.json')
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(validation_result, f, indent=2, ensure_ascii=False)
        
        print(f"   结果已保存: {result_file}")
        
        return validation_result
        
    except Exception as e:
        print(f"❌ {config_type}配置训练失败: {str(e)}")
        return None


def compare_results(best_result, baseline_result):
    """比较验证结果"""
    if not best_result or not baseline_result:
        print("⚠️  无法进行结果比较，某个配置训练失败")
        return
    
    print("\n📊 验证结果对比")
    print("=" * 60)
    
    print(f"{'指标':<15} {'基线配置':<15} {'最佳配置':<15} {'改善':<15}")
    print("-" * 60)
    
    # 准确率对比
    baseline_acc = baseline_result['final_accuracy']
    best_acc = best_result['final_accuracy']
    acc_improvement = ((best_acc - baseline_acc) / baseline_acc) * 100
    
    print(f"{'准确率':<15} {baseline_acc:<15.4f} {best_acc:<15.4f} {acc_improvement:+.2f}%")
    
    # FID对比
    baseline_fid = baseline_result['avg_fid']
    best_fid = best_result['avg_fid']
    fid_improvement = ((baseline_fid - best_fid) / baseline_fid) * 100  # FID越小越好
    
    print(f"{'FID':<15} {baseline_fid:<15.2f} {best_fid:<15.2f} {fid_improvement:+.2f}%")
    
    # MMD对比
    baseline_mmd = baseline_result['avg_mmd']
    best_mmd = best_result['avg_mmd']
    mmd_improvement = ((baseline_mmd - best_mmd) / baseline_mmd) * 100  # MMD越小越好
    
    print(f"{'MMD':<15} {baseline_mmd:<15.4f} {best_mmd:<15.4f} {mmd_improvement:+.2f}%")
    
    # 训练时间对比
    baseline_time = baseline_result['training_time_minutes']
    best_time = best_result['training_time_minutes']
    time_change = ((best_time - baseline_time) / baseline_time) * 100
    
    print(f"{'训练时间(分)':<15} {baseline_time:<15.1f} {best_time:<15.1f} {time_change:+.2f}%")
    
    print("\n🎯 配置对比:")
    print(f"基线配置: lambda_ar={baseline_result['lambda_ar']}, lr={baseline_result['lr']}")
    print(f"最佳配置: lambda_ar={best_result['lambda_ar']}, lr={best_result['lr']}")
    
    # 保存对比结果
    comparison_result = {
        'baseline_config': {
            'lambda_ar': baseline_result['lambda_ar'],
            'lr': baseline_result['lr'],
            'accuracy': baseline_result['final_accuracy'],
            'fid': baseline_result['avg_fid'],
            'mmd': baseline_result['avg_mmd']
        },
        'best_config': {
            'lambda_ar': best_result['lambda_ar'],
            'lr': best_result['lr'],
            'accuracy': best_result['final_accuracy'],
            'fid': best_result['avg_fid'],
            'mmd': best_result['avg_mmd']
        },
        'improvements': {
            'accuracy_improvement_percent': acc_improvement,
            'fid_improvement_percent': fid_improvement,
            'mmd_improvement_percent': mmd_improvement,
            'time_change_percent': time_change
        },
        'timestamp': datetime.now().isoformat()
    }
    
    os.makedirs('./validation_results', exist_ok=True)
    comparison_file = f'./validation_results/config_comparison_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(comparison_file, 'w', encoding='utf-8') as f:
        json.dump(comparison_result, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 对比结果已保存: {comparison_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='验证最佳超参数配置')
    parser.add_argument('--split', type=str, default='A', choices=['A', 'B', 'C', 'D', 'E'],
                       help='数据分组')
    parser.add_argument('--epochs', type=int, default=500,
                       help='验证训练的轮数')
    parser.add_argument('--mode', type=str, default='both', choices=['best', 'baseline', 'both'],
                       help='验证模式: best=只验证最佳配置, baseline=只验证基线, both=两者都验证')
    parser.add_argument('--compare', action='store_true',
                       help='是否进行配置对比')
    
    args = parser.parse_args()
    
    print("🔍 VAEGAN-AR 最佳配置验证")
    print("=" * 50)
    print(f"数据分组: {args.split}")
    print(f"训练轮数: {args.epochs}")
    print(f"验证模式: {args.mode}")
    
    best_result = None
    baseline_result = None
    
    # 验证最佳配置
    if args.mode in ['best', 'both']:
        best_config_data = load_best_config(args.split)
        if best_config_data:
            best_config = create_validation_config(best_config_data, args.epochs)
            best_result = run_validation(best_config, args.split, "最佳")
        else:
            print("❌ 无法加载最佳配置，跳过最佳配置验证")
    
    # 验证基线配置
    if args.mode in ['baseline', 'both']:
        baseline_config = create_baseline_config(args.epochs)
        baseline_result = run_validation(baseline_config, args.split, "基线")
    
    # 对比结果
    if args.compare and args.mode == 'both':
        compare_results(best_result, baseline_result)
    
    print("\n🎉 验证完成!")


if __name__ == "__main__":
    main()

"""
ASDCGAN 损失函数模块

包含所有损失函数的实现：
- CycleConsistencyLoss: 多层次循环一致性损失
- SemanticDistanceLoss: 语义距离损失
- UncertaintyLoss: 不确定性相关损失
- AdversarialLoss: 对抗损失
- TotalLoss: 总损失管理器

设计原则：
- 模块化损失函数设计
- 支持权重自适应调整
- 便于实验和调优
- 与TensorFlow/Keras兼容
"""

from .cycle_consistency_loss import CycleConsistencyLoss
from .semantic_distance_loss import SemanticDistanceLoss
from .uncertainty_loss import UncertaintyLoss
from .adversarial_loss import AdversarialLoss
from .total_loss import TotalLossManager

__all__ = [
    "CycleConsistencyLoss",
    "SemanticDistanceLoss", 
    "UncertaintyLoss",
    "AdversarialLoss",
    "TotalLossManager"
]

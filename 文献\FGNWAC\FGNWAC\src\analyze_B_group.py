#!/usr/bin/env python3
"""
B组超参数搜索效果分析
"""

import pandas as pd
import numpy as np

def analyze_B_group():
    """分析B组搜索效果"""
    print("📊 B组超参数搜索效果深度分析")
    print("=" * 60)
    
    # 读取数据
    stage1_df = pd.read_csv('hyperparameter_search_results/lambda_ar_search_split_B.csv')
    stage2_df = pd.read_csv('hyperparameter_search_results/lr_search_split_B.csv')
    
    print("\n🎯 阶段1分析: Lambda_AR参数敏感性")
    print("-" * 50)
    print("Lambda_AR | 最佳准确率 | 最佳轮数 | 训练时间 | 收敛性")
    print("-" * 50)
    
    stage1_results = []
    for _, row in stage1_df.iterrows():
        params = eval(row['parameters'])
        lambda_ar = params['lambda_ar']
        accuracy = row['best_accuracy']
        epoch = row['best_epoch']
        time_min = row['training_time_minutes']
        convergence = epoch / row['total_epochs']  # 收敛比例
        
        print(f"{lambda_ar:8.1f} | {accuracy:10.4f} | {epoch:8d} | {time_min:8.1f}分 | {convergence:6.2f}")
        stage1_results.append((lambda_ar, accuracy, epoch, convergence))
    
    # 找到最佳和最差
    best_stage1 = max(stage1_results, key=lambda x: x[1])
    worst_stage1 = min(stage1_results, key=lambda x: x[1])
    
    print(f"\n✅ 最佳: Lambda_AR={best_stage1[0]}, 准确率={best_stage1[1]:.4f}")
    print(f"❌ 最差: Lambda_AR={worst_stage1[0]}, 准确率={worst_stage1[1]:.4f}")
    print(f"📈 性能差距: {((best_stage1[1] - worst_stage1[1]) / worst_stage1[1] * 100):.2f}%")
    
    print("\n🎯 阶段2分析: 学习率参数敏感性")
    print("-" * 50)
    print("学习率    | 最佳准确率 | 最佳轮数 | 训练时间 | 收敛性")
    print("-" * 50)
    
    stage2_results = []
    for _, row in stage2_df.iterrows():
        params = eval(row['parameters'])
        lr = params['lr']
        accuracy = row['best_accuracy']
        epoch = row['best_epoch']
        time_min = row['training_time_minutes']
        convergence = epoch / row['total_epochs']
        
        print(f"{lr:9.5f} | {accuracy:10.4f} | {epoch:8d} | {time_min:8.1f}分 | {convergence:6.2f}")
        stage2_results.append((lr, accuracy, epoch, convergence))
    
    best_stage2 = max(stage2_results, key=lambda x: x[1])
    worst_stage2 = min(stage2_results, key=lambda x: x[1])
    
    print(f"\n✅ 最佳: 学习率={best_stage2[0]:.5f}, 准确率={best_stage2[1]:.4f}")
    print(f"❌ 最差: 学习率={worst_stage2[0]:.5f}, 准确率={worst_stage2[1]:.4f}")
    print(f"📈 性能差距: {((best_stage2[1] - worst_stage2[1]) / worst_stage2[1] * 100):.2f}%")
    
    print("\n🔍 B组特征分析")
    print("=" * 40)
    
    # Lambda_AR分析
    lambda_ar_values = [x[0] for x in stage1_results]
    lambda_ar_accs = [x[1] for x in stage1_results]
    
    print("Lambda_AR敏感性分析:")
    print(f"  📊 最优值: {best_stage1[0]} (中等偏低)")
    print(f"  📈 性能范围: {min(lambda_ar_accs):.4f} - {max(lambda_ar_accs):.4f}")
    print(f"  🎯 敏感度: {'高' if (max(lambda_ar_accs) - min(lambda_ar_accs)) > 0.05 else '中等'}")
    
    # 找到性能趋势
    sorted_lambda = sorted(zip(lambda_ar_values, lambda_ar_accs))
    print(f"  📉 趋势分析:")
    for i, (lam, acc) in enumerate(sorted_lambda):
        trend = ""
        if i > 0:
            prev_acc = sorted_lambda[i-1][1]
            if acc > prev_acc:
                trend = "⬆️"
            elif acc < prev_acc:
                trend = "⬇️"
            else:
                trend = "➡️"
        print(f"     Lambda_AR={lam}: {acc:.4f} {trend}")
    
    # 学习率分析
    lr_values = [x[0] for x in stage2_results]
    lr_accs = [x[1] for x in stage2_results]
    
    print(f"\n学习率敏感性分析:")
    print(f"  📊 最优值: {best_stage2[0]:.5f} (标准值)")
    print(f"  📈 性能范围: {min(lr_accs):.4f} - {max(lr_accs):.4f}")
    print(f"  🎯 敏感度: {'高' if (max(lr_accs) - min(lr_accs)) > 0.05 else '中等'}")
    
    # 收敛性分析
    stage1_convergence = [x[3] for x in stage1_results]
    stage2_convergence = [x[3] for x in stage2_results]
    
    print(f"\n收敛性分析:")
    print(f"  ⏱️ 阶段1平均收敛比例: {np.mean(stage1_convergence):.2f}")
    print(f"  ⏱️ 阶段2平均收敛比例: {np.mean(stage2_convergence):.2f}")
    print(f"  🚀 早停效果: {'显著' if np.mean(stage1_convergence + stage2_convergence) < 0.7 else '一般'}")
    
    print("\n🆚 与A组对比分析")
    print("=" * 40)
    
    # A组最佳结果 (从之前的分析)
    a_best_lambda_ar = 0.8
    a_best_lr = 0.0002
    a_best_acc = 0.8375  # 83.75%
    
    b_best_lambda_ar = best_stage1[0]
    b_best_lr = best_stage2[0]
    b_best_acc = best_stage2[1]
    
    print("配置对比:")
    print(f"  A组最佳: lambda_ar={a_best_lambda_ar}, lr={a_best_lr:.5f}, 准确率={a_best_acc:.4f}")
    print(f"  B组最佳: lambda_ar={b_best_lambda_ar}, lr={b_best_lr:.5f}, 准确率={b_best_acc:.4f}")
    
    print(f"\n关键差异:")
    print(f"  🔧 Lambda_AR偏好: A组喜欢高值(0.8), B组喜欢中值(0.5)")
    print(f"  📚 学习率偏好: A组喜欢快速(0.0002), B组喜欢稳定(0.0001)")
    print(f"  📊 性能差距: B组比A组低 {((a_best_acc - b_best_acc) / a_best_acc * 100):.1f}%")
    
    print(f"\n💡 B组特点总结:")
    print(f"  1. 🎯 最优配置偏向保守: lambda_ar=0.5, lr=0.0001")
    print(f"  2. 📈 性能相对较低: 最佳准确率55.07%")
    print(f"  3. 🔄 参数敏感性中等: 配置选择对性能影响适中")
    print(f"  4. ⚡ 训练效率高: 早停机制有效，平均训练时间短")
    print(f"  5. 🎨 故障特征可能更复杂: 需要更保守的参数设置")
    
    print(f"\n🚀 B组优化建议:")
    print(f"  1. 验证最佳配置的稳定性")
    print(f"  2. 考虑在lambda_ar=0.4-0.6范围内精细搜索")
    print(f"  3. 尝试更长的训练轮数以提升性能")
    print(f"  4. 分析B组包含的具体故障类型特征")

if __name__ == "__main__":
    analyze_B_group()

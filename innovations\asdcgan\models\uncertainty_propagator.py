"""
不确定性传播器模块 (PyTorch版本)

基于蒙特卡洛采样的不确定性量化和传播机制。

核心功能：
1. 蒙特卡洛采样不确定性估计
2. 不确定性传播和累积
3. 置信度评估和可信决策
4. 不确定性可视化和分析

技术特点：
- 蒙特卡洛Dropout
- 贝叶斯神经网络
- 不确定性分解
- 置信度校准
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class MCDropout(nn.Module):
    """蒙特卡洛Dropout层"""
    
    def __init__(self, p=0.1):
        super(MCDropout, self).__init__()
        self.p = p
    
    def forward(self, x):
        # 在推理时也应用Dropout
        return F.dropout(x, p=self.p, training=True)


class BayesianLinear(nn.Module):
    """贝叶斯线性层"""
    
    def __init__(self, in_features, out_features, prior_std=1.0):
        super(BayesianLinear, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.prior_std = prior_std
        
        # 权重参数
        self.weight_mu = nn.Parameter(torch.randn(out_features, in_features) * 0.1)
        self.weight_rho = nn.Parameter(torch.randn(out_features, in_features) * 0.1)
        
        # 偏置参数
        self.bias_mu = nn.Parameter(torch.randn(out_features) * 0.1)
        self.bias_rho = nn.Parameter(torch.randn(out_features) * 0.1)
    
    def forward(self, x):
        # 采样权重
        weight_std = torch.log1p(torch.exp(self.weight_rho))
        weight = self.weight_mu + weight_std * torch.randn_like(weight_std)
        
        # 采样偏置
        bias_std = torch.log1p(torch.exp(self.bias_rho))
        bias = self.bias_mu + bias_std * torch.randn_like(bias_std)
        
        return F.linear(x, weight, bias)
    
    def kl_divergence(self):
        """计算KL散度"""
        weight_std = torch.log1p(torch.exp(self.weight_rho))
        bias_std = torch.log1p(torch.exp(self.bias_rho))
        
        # 权重KL散度
        weight_kl = 0.5 * torch.sum(
            (self.weight_mu ** 2 + weight_std ** 2) / (self.prior_std ** 2) - 
            torch.log(weight_std ** 2) + torch.log(self.prior_std ** 2) - 1
        )
        
        # 偏置KL散度
        bias_kl = 0.5 * torch.sum(
            (self.bias_mu ** 2 + bias_std ** 2) / (self.prior_std ** 2) - 
            torch.log(bias_std ** 2) + torch.log(self.prior_std ** 2) - 1
        )
        
        return weight_kl + bias_kl


class UncertaintyPropagator(nn.Module):
    """
    不确定性传播器
    
    基于蒙特卡洛采样的不确定性量化。
    """
    
    def __init__(self, 
                 num_mc_samples=50,
                 confidence_threshold=0.8,
                 uncertainty_type='mc_dropout'):
        """
        初始化不确定性传播器
        
        Args:
            num_mc_samples: 蒙特卡洛采样次数
            confidence_threshold: 置信度阈值
            uncertainty_type: 不确定性类型 ('mc_dropout', 'bayesian', 'ensemble')
        """
        super(UncertaintyPropagator, self).__init__()
        
        self.num_mc_samples = num_mc_samples
        self.confidence_threshold = confidence_threshold
        self.uncertainty_type = uncertainty_type
    
    def propagate_uncertainty(self, inputs, model, training=False):
        """
        传播不确定性
        
        Args:
            inputs: 输入数据 (可以是单个张量或列表)
            model: 要评估的模型
            training: 是否在训练模式
            
        Returns:
            uncertainty_result: 不确定性结果字典
        """
        if isinstance(inputs, (list, tuple)):
            batch_size = inputs[0].shape[0]
            device = inputs[0].device
        else:
            batch_size = inputs.shape[0]
            device = inputs.device
        
        # 收集多次采样的结果
        predictions = []
        
        # 设置模型为评估模式但保持Dropout激活
        model.eval()
        
        with torch.no_grad():
            for _ in range(self.num_mc_samples):
                if isinstance(inputs, (list, tuple)):
                    output = model(*inputs)
                else:
                    output = model(inputs)
                
                # 处理不同类型的输出
                if isinstance(output, dict):
                    if 'generated_features' in output:
                        pred = output['generated_features']
                    else:
                        pred = output[list(output.keys())[0]]
                else:
                    pred = output
                
                predictions.append(pred)
        
        # 堆叠所有预测
        predictions = torch.stack(predictions, dim=0)  # [num_samples, batch_size, ...]
        
        # 计算统计量
        mean_prediction = torch.mean(predictions, dim=0)
        std_prediction = torch.std(predictions, dim=0)
        var_prediction = torch.var(predictions, dim=0)
        
        # 计算不确定性度量
        aleatoric_uncertainty = torch.mean(var_prediction, dim=-1)  # 数据不确定性
        epistemic_uncertainty = torch.mean(std_prediction, dim=-1)  # 模型不确定性
        total_uncertainty = aleatoric_uncertainty + epistemic_uncertainty
        
        # 计算置信度
        confidence = 1.0 / (1.0 + total_uncertainty)
        
        # 可信决策
        reliable_mask = confidence > self.confidence_threshold
        
        return {
            'mean_prediction': mean_prediction,
            'std_prediction': std_prediction,
            'var_prediction': var_prediction,
            'aleatoric_uncertainty': aleatoric_uncertainty,
            'epistemic_uncertainty': epistemic_uncertainty,
            'total_uncertainty': total_uncertainty,
            'confidence': confidence,
            'reliable_mask': reliable_mask,
            'all_predictions': predictions
        }
    
    def estimate_prediction_interval(self, predictions, confidence_level=0.95):
        """
        估计预测区间
        
        Args:
            predictions: 预测结果 [num_samples, batch_size, ...]
            confidence_level: 置信水平
            
        Returns:
            lower_bound: 下界
            upper_bound: 上界
        """
        alpha = 1 - confidence_level
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        lower_bound = torch.quantile(predictions, lower_percentile / 100, dim=0)
        upper_bound = torch.quantile(predictions, upper_percentile / 100, dim=0)
        
        return lower_bound, upper_bound
    
    def compute_uncertainty_metrics(self, uncertainty_result):
        """
        计算不确定性度量指标
        
        Args:
            uncertainty_result: 不确定性结果
            
        Returns:
            metrics: 度量指标字典
        """
        total_uncertainty = uncertainty_result['total_uncertainty']
        confidence = uncertainty_result['confidence']
        reliable_mask = uncertainty_result['reliable_mask']
        
        metrics = {
            'mean_uncertainty': torch.mean(total_uncertainty).item(),
            'std_uncertainty': torch.std(total_uncertainty).item(),
            'max_uncertainty': torch.max(total_uncertainty).item(),
            'min_uncertainty': torch.min(total_uncertainty).item(),
            'mean_confidence': torch.mean(confidence).item(),
            'reliable_ratio': torch.mean(reliable_mask.float()).item(),
            'uncertainty_entropy': self._compute_entropy(total_uncertainty)
        }
        
        return metrics
    
    def _compute_entropy(self, uncertainty):
        """计算不确定性熵"""
        # 归一化不确定性
        normalized_uncertainty = uncertainty / (torch.sum(uncertainty, dim=-1, keepdim=True) + 1e-8)
        
        # 计算熵
        entropy = -torch.sum(normalized_uncertainty * torch.log(normalized_uncertainty + 1e-8), dim=-1)
        
        return torch.mean(entropy).item()
    
    def calibrate_uncertainty(self, predictions, targets, num_bins=10):
        """
        校准不确定性
        
        Args:
            predictions: 预测结果
            targets: 真实目标
            num_bins: 校准bins数量
            
        Returns:
            calibration_result: 校准结果
        """
        # 计算预测误差
        errors = torch.abs(predictions - targets)
        
        # 计算不确定性
        uncertainty_result = self.propagate_uncertainty(predictions, lambda x: x)
        uncertainties = uncertainty_result['total_uncertainty']
        
        # 创建bins
        uncertainty_bins = torch.linspace(
            torch.min(uncertainties), torch.max(uncertainties), num_bins + 1
        )
        
        bin_boundaries = []
        bin_lowers = uncertainty_bins[:-1]
        bin_uppers = uncertainty_bins[1:]
        
        bin_accuracies = []
        bin_confidences = []
        bin_counts = []
        
        for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
            # 找到在当前bin中的样本
            in_bin = (uncertainties > bin_lower.item()) & (uncertainties <= bin_upper.item())
            prop_in_bin = in_bin.float().mean()
            
            if prop_in_bin.item() > 0:
                accuracy_in_bin = errors[in_bin].mean()
                avg_confidence_in_bin = uncertainties[in_bin].mean()
                
                bin_accuracies.append(accuracy_in_bin.item())
                bin_confidences.append(avg_confidence_in_bin.item())
                bin_counts.append(in_bin.sum().item())
            else:
                bin_accuracies.append(0)
                bin_confidences.append(0)
                bin_counts.append(0)
        
        # 计算校准误差
        calibration_error = 0
        total_samples = len(predictions)
        
        for i in range(num_bins):
            if bin_counts[i] > 0:
                weight = bin_counts[i] / total_samples
                calibration_error += weight * abs(bin_accuracies[i] - bin_confidences[i])
        
        return {
            'calibration_error': calibration_error,
            'bin_accuracies': bin_accuracies,
            'bin_confidences': bin_confidences,
            'bin_counts': bin_counts
        }
    
    def uncertainty_guided_sampling(self, model, inputs, target_uncertainty=0.1, max_iterations=100):
        """
        基于不确定性的引导采样
        
        Args:
            model: 模型
            inputs: 输入
            target_uncertainty: 目标不确定性水平
            max_iterations: 最大迭代次数
            
        Returns:
            optimized_inputs: 优化后的输入
            final_uncertainty: 最终不确定性
        """
        if isinstance(inputs, (list, tuple)):
            optimized_inputs = [inp.clone().requires_grad_(True) for inp in inputs]
        else:
            optimized_inputs = inputs.clone().requires_grad_(True)
        
        optimizer = torch.optim.Adam([optimized_inputs] if not isinstance(optimized_inputs, list) 
                                   else optimized_inputs, lr=0.01)
        
        for iteration in range(max_iterations):
            optimizer.zero_grad()
            
            # 计算当前不确定性
            uncertainty_result = self.propagate_uncertainty(optimized_inputs, model)
            current_uncertainty = torch.mean(uncertainty_result['total_uncertainty'])
            
            # 计算损失 (目标是达到目标不确定性水平)
            uncertainty_loss = torch.abs(current_uncertainty - target_uncertainty)
            
            # 反向传播
            uncertainty_loss.backward()
            optimizer.step()
            
            # 检查收敛
            if uncertainty_loss.item() < 0.01:
                break
        
        final_uncertainty_result = self.propagate_uncertainty(optimized_inputs, model)
        final_uncertainty = torch.mean(final_uncertainty_result['total_uncertainty'])
        
        return optimized_inputs, final_uncertainty.item()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 修复所有组别文件的seen_class_map硬编码问题
一键修复A、B、C、D组的配置问题
"""

import os
import shutil
from datetime import datetime

def backup_file(filepath):
    """备份原文件"""
    backup_path = f"{filepath}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(filepath, backup_path)
    print(f"✅ 已备份: {filepath} -> {backup_path}")
    return backup_path

def create_universal_train_method():
    """创建通用的train方法代码"""
    return '''    def train(self, epochs=2000, batch_size=256, log_file=None, test_classes=None, group_name="AUTO"):
        """🔥 通用训练函数 - 支持任意组别配置
        
        Args:
            epochs: 训练轮数
            batch_size: 批次大小
            log_file: 日志文件路径
            test_classes: 测试类别列表，如 [8, 11, 12] 为C组
            group_name: 组别名称，用于日志显示。"AUTO"表示从文件名自动推断
        """
        from universal_group_config import UniversalGroupConfig
        
        start_time = datetime.datetime.now()

        # 🔥 自动推断组别（如果group_name为"AUTO"）
        if group_name == "AUTO":
            import inspect
            current_file = inspect.getfile(self.__class__)
            if "_A_" in current_file:
                group_name = "A"
            elif "_B_" in current_file:
                group_name = "B"
            elif "_C_" in current_file:
                group_name = "C"
            elif "_D_" in current_file:
                group_name = "D"
            else:
                group_name = "Unknown"

        # 🔥 使用通用配置工具
        test_classes, train_classes, seen_class_map, centers, num_seen_classes = \\
            UniversalGroupConfig.setup_group_config(
                group_name=group_name, 
                test_classes=test_classes, 
                feature_dim=self.feature_dim
            )
        
        # 设置实例变量
        self.seen_class_map = seen_class_map
        self.centers = centers
        
        # 打印配置信息
        UniversalGroupConfig.print_config_info(group_name, test_classes, train_classes, seen_class_map)

        print(f"🔥 开始{group_name.upper()}组实验训练...")
        print(f"📅 开始时间: {start_time}")
        print(f"🎯 测试类别: {test_classes} ({group_name.upper()}组)")
        
        # 继续原有的训练逻辑...'''

def fix_group_file(filepath, group_letter):
    """修复单个组别文件"""
    print(f"\n🔧 修复文件: {filepath}")
    
    # 备份原文件
    backup_path = backup_file(filepath)
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经修复过
        if "from universal_group_config import UniversalGroupConfig" in content:
            print(f"⚠️ 文件 {filepath} 似乎已经修复过，跳过")
            return
        
        # 修复步骤
        modifications = []
        
        # 1. 添加导入
        if "import datetime" in content:
            content = content.replace(
                "import datetime",
                "import datetime\nfrom universal_group_config import UniversalGroupConfig, safe_center_loss, safe_update_centers"
            )
            modifications.append("添加通用配置导入")
        
        # 2. 移除错误的initialize_centers调用
        if "self.initialize_centers()" in content:
            content = content.replace(
                "        # 初始化中心\n        self.initialize_centers()",
                "        # 🔥 中心初始化已移到train方法中\n        # self.initialize_centers()  # 已移除硬编码问题"
            )
            modifications.append("移除错误的initialize_centers调用")
        
        # 3. 修复initialize_centers方法
        if "def initialize_centers(self):" in content:
            old_method = content[content.find("def initialize_centers(self):"):content.find("def initialize_centers(self):") + 500]
            if "seen_class_map = {" in old_method:
                new_method = '''def initialize_centers(self):
        """❌ 已废弃：硬编码seen_class_map导致严重问题
        
        原问题：硬编码seen_class_map只包含少数类别，导致Center Loss失效
        修复：中心初始化已移到train方法中，使用UniversalGroupConfig动态配置
        """
        print("⚠️ initialize_centers已废弃，请使用train方法中的动态初始化")
        pass'''
                
                # 找到完整的方法定义
                method_start = content.find("def initialize_centers(self):")
                method_end = content.find("\n    def ", method_start + 1)
                if method_end == -1:
                    method_end = content.find("\n\nclass", method_start + 1)
                if method_end == -1:
                    method_end = len(content)
                
                content = content[:method_start] + new_method + content[method_end:]
                modifications.append("修复initialize_centers方法")
        
        # 4. 替换center_loss方法调用
        if "def center_loss(self, features, labels):" in content:
            content = content.replace(
                "def center_loss(self, features, labels):",
                "def center_loss(self, features, labels):\n        \"\"\"使用通用安全的Center Loss\"\"\"\n        return safe_center_loss(features, labels, self.centers, self.seen_class_map)\n    \n    def center_loss_old(self, features, labels):"
            )
            modifications.append("替换center_loss方法")
        
        # 5. 替换update_centers方法调用
        if "def update_centers(self, features, labels):" in content:
            content = content.replace(
                "def update_centers(self, features, labels):",
                "def update_centers(self, features, labels):\n        \"\"\"使用通用安全的中心更新\"\"\"\n        safe_update_centers(features, labels, self.centers, self.seen_class_map, self.center_optimizer)\n    \n    def update_centers_old(self, features, labels):"
            )
            modifications.append("替换update_centers方法")
        
        # 6. 添加通用train方法的开头部分（如果需要）
        if "def train(self, epochs=" in content and "UniversalGroupConfig" not in content:
            # 这里需要更复杂的逻辑来替换train方法
            # 暂时添加注释提示
            content = content.replace(
                "def train(self,",
                f"# 🔥 建议使用通用配置: UniversalGroupConfig.setup_group_config(group_name='{group_letter}')\n    def train(self,"
            )
            modifications.append("添加通用配置提示")
        
        # 写入修复后的文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {filepath}")
        print(f"   修改项目: {', '.join(modifications)}")
        
    except Exception as e:
        print(f"❌ 修复失败: {filepath}")
        print(f"   错误: {e}")
        # 恢复备份
        shutil.copy2(backup_path, filepath)
        print(f"   已恢复备份")

def main():
    """主函数"""
    print("🔥 开始修复所有组别文件的seen_class_map硬编码问题")
    print("=" * 60)
    
    # 要修复的文件列表
    files_to_fix = [
        ("scripts/acgan_triplet_A_Hybrid.py", "A"),
        ("scripts/acgan_triplet_B_HardTriplet.py", "B"),
        ("scripts/acgan_triplet_B_Hybrid.py", "B"),
        ("scripts/acgan_triplet_B_SmartCRL.py", "B"),
        ("scripts/acgan_triplet_C_Hybrid.py", "C"),
        # 可以添加更多文件...
    ]
    
    success_count = 0
    total_count = len(files_to_fix)
    
    for filepath, group_letter in files_to_fix:
        if os.path.exists(filepath):
            try:
                fix_group_file(filepath, group_letter)
                success_count += 1
            except Exception as e:
                print(f"❌ 处理文件 {filepath} 时出错: {e}")
        else:
            print(f"⚠️ 文件不存在: {filepath}")
    
    print("\n" + "=" * 60)
    print(f"🎉 修复完成! 成功: {success_count}/{total_count}")
    print("\n📋 后续步骤:")
    print("1. 测试修复后的文件是否正常运行")
    print("2. 验证seen_class_map是否包含所有训练类别")
    print("3. 观察Center Loss是否对所有类别生效")
    print("4. 如有问题，可使用备份文件恢复")
    
    print("\n🚀 使用示例:")
    print("# A组测试")
    print("model.train(epochs=100, group_name='A')")
    print("# B组测试") 
    print("model.train(epochs=100, group_name='B')")
    print("# 自定义测试类别")
    print("model.train(epochs=100, test_classes=[1,2,3], group_name='Custom')")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
ASDCGAN项目状态检查脚本
"""

import os
import sys
import json
import torch
from datetime import datetime
import glob

def check_environment():
    """检查环境配置"""
    print("🔧 环境配置检查")
    print("=" * 50)
    print(f"🔥 PyTorch版本: {torch.__version__}")
    print(f"🚀 CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"💻 GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"🎯 CUDA版本: {torch.version.cuda}")
        print(f"📊 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    print()

def check_modules():
    """检查模块导入"""
    print("📦 模块导入检查")
    print("=" * 50)
    
    modules_to_check = [
        ('asdcgan.models.adaptive_semantic_distance', 'AdaptiveSemanticDistance'),
        ('asdcgan.models.domain_selector', 'DomainSelector'),
        ('asdcgan.models.variational_generator', 'VariationalGenerator'),
        ('asdcgan.models.multi_level_discriminator', 'MultiLevelDiscriminator'),
        ('asdcgan.models.uncertainty_propagator', 'UncertaintyPropagator'),
    ]
    
    success_count = 0
    for module_name, class_name in modules_to_check:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {class_name}")
            success_count += 1
        except Exception as e:
            print(f"❌ {class_name}: {e}")
    
    print(f"\n📊 模块导入成功率: {success_count}/{len(modules_to_check)} ({success_count/len(modules_to_check)*100:.1f}%)")
    print()

def check_experiments():
    """检查实验结果"""
    print("🧪 实验结果检查")
    print("=" * 50)
    
    if not os.path.exists('experiments'):
        print("❌ experiments目录不存在")
        return
    
    groups = ['A', 'B', 'C', 'D', 'E']
    total_runs = 0
    
    for group in groups:
        group_dir = f'experiments/group_{group}'
        if os.path.exists(group_dir):
            runs = [d for d in os.listdir(group_dir) if d.startswith('run_')]
            total_runs += len(runs)
            
            # 读取配置文件
            config_file = f'{group_dir}/experiment_config.json'
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    final_acc = config.get('final_metrics', {}).get('final_accuracy', 0)
                    epochs = config.get('final_metrics', {}).get('total_epochs', 0)
                    print(f"📊 Group {group}: {len(runs)}次运行, 最终准确率: {final_acc:.2%}, 训练轮次: {epochs}")
            else:
                print(f"📊 Group {group}: {len(runs)}次运行")
        else:
            print(f"📊 Group {group}: 未运行")
    
    print(f"\n🎯 总运行次数: {total_runs}")
    print()

def check_tensorboard():
    """检查TensorBoard日志"""
    print("📈 TensorBoard日志检查")
    print("=" * 50)
    
    if not os.path.exists('tensorboard'):
        print("❌ tensorboard目录不存在")
        return
    
    tb_dirs = [d for d in os.listdir('tensorboard') if os.path.isdir(f'tensorboard/{d}')]
    print(f"📊 TensorBoard会话数: {len(tb_dirs)}")
    
    for tb_dir in sorted(tb_dirs):
        events_files = glob.glob(f'tensorboard/{tb_dir}/events.out.tfevents.*')
        if events_files:
            # 获取最新的事件文件
            latest_file = max(events_files, key=os.path.getmtime)
            file_size = os.path.getsize(latest_file) / 1024  # KB
            print(f"  📝 {tb_dir}: {file_size:.1f}KB")
    print()

def check_training_scripts():
    """检查训练脚本"""
    print("🚀 训练脚本检查")
    print("=" * 50)
    
    scripts = [
        'enhanced_asdcgan_trainer.py',
        'test_all_improvements.py',
        'auto_train_abc.py',
        'auto_train_de.py',
        'minimal_working_version.py'
    ]
    
    for script in scripts:
        if os.path.exists(script):
            size = os.path.getsize(script) / 1024  # KB
            print(f"✅ {script}: {size:.1f}KB")
        else:
            print(f"❌ {script}: 不存在")
    print()

def check_project_structure():
    """检查项目结构"""
    print("📁 项目结构检查")
    print("=" * 50)
    
    expected_dirs = [
        'asdcgan',
        'asdcgan/models',
        'asdcgan/losses', 
        'asdcgan/training',
        'asdcgan/utils',
        'asdcgan/experiments',
        'configs',
        'experiments',
        'tensorboard'
    ]
    
    for dir_path in expected_dirs:
        if os.path.exists(dir_path):
            files_count = len([f for f in os.listdir(dir_path) if f.endswith('.py')])
            print(f"✅ {dir_path}: {files_count}个Python文件")
        else:
            print(f"❌ {dir_path}: 不存在")
    print()

def main():
    """主函数"""
    print(f"""
🎯 ASDCGAN项目状态检查报告
=====================================
📅 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📍 项目路径: {os.getcwd()}
=====================================
    """)
    
    check_environment()
    check_modules()
    check_project_structure()
    check_experiments()
    check_tensorboard()
    check_training_scripts()
    
    print("🎉 项目状态检查完成！")

if __name__ == "__main__":
    main()

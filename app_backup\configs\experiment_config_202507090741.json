{"timestamp": "2025-07-09T07:41:20.313538", "group": "C", "epochs": 500, "strategies": {"fixed_weights": {"description": "固定权重基准", "log_file": "结果/202507090741_fixed_weights_GroupC.md", "status": "ready"}, "improved_adaptive": {"description": "改进自适应权重", "log_file": "结果/202507090741_improved_adaptive_GroupC.md", "status": "ready"}}, "improvements": {"weight_adjustment": "使用tanh函数平滑映射，限制调整幅度", "max_triplet_change": "±30%", "max_cla_change": "±20%", "weight_range": "[0.7x, 1.5x]", "expected_benefit": "更稳定的训练过程，更好的收敛性"}}
"""
智能域选择器模块 (PyTorch版本)

基于多头注意力机制的自动域选择，替换CycleGAN-SD中的手动域选择过程。

核心功能：
1. 自动选择最优源域进行域转换
2. 基于语义相似度的智能匹配
3. 支持多域并行选择和权重分配
4. 动态适应不同的目标域特征

技术特点：
- 多头注意力机制
- 可学习的域选择策略
- 支持软选择和硬选择
- 梯度友好的可微分实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class DomainSelector(nn.Module):
    """
    智能域选择器
    
    使用多头注意力机制自动选择最优的源域进行域转换，
    替换传统的手动域选择过程。
    """
    
    def __init__(self,
                 num_domains,
                 attention_dim=64,
                 num_heads=4,
                 hidden_dim=128,
                 dropout_rate=0.1,
                 selection_mode='soft',
                 temperature=1.0):
        """
        初始化域选择器

        Args:
            num_domains: 可选择的域数量
            attention_dim: 注意力机制维度
            num_heads: 多头注意力头数
            hidden_dim: 隐藏层维度
            dropout_rate: Dropout比率
            selection_mode: 选择模式 ('soft', 'hard', 'gumbel')
            temperature: Gumbel softmax温度参数
        """
        super(DomainSelector, self).__init__()

        self.num_domains = num_domains
        self.attention_dim = attention_dim
        self.num_heads = num_heads
        self.hidden_dim = hidden_dim
        self.dropout_rate = dropout_rate
        self.selection_mode = selection_mode
        self.temperature = temperature

        # 目标域编码器
        self.target_encoder = nn.Sequential(
            nn.Linear(attention_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, attention_dim),
            nn.Tanh()
        )

        # 源域编码器
        self.source_encoder = nn.Sequential(
            nn.Linear(attention_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, attention_dim),
            nn.Tanh()
        )

        # 多头注意力机制
        self.domain_attention = nn.MultiheadAttention(
            embed_dim=attention_dim,
            num_heads=num_heads,
            dropout=dropout_rate,
            batch_first=True
        )

        # 选择权重计算网络
        self.selection_weights = nn.Sequential(
            nn.Linear(attention_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 2, num_domains)  # 不加激活函数，后续根据模式处理
        )
        
        # 相似度计算网络
        self.similarity_network = tf.keras.Sequential([
            Dense(hidden_dim, activation='relu'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(1, activation='sigmoid')
        ])
        
    def compute_domain_similarity(self, target_encoding, source_encodings, training=None):
        """
        计算目标域与各源域的相似度
        
        Args:
            target_encoding: 目标域编码 [batch_size, attention_dim]
            source_encodings: 源域编码列表 [num_domains, batch_size, attention_dim]
            
        Returns:
            similarities: 相似度分数 [batch_size, num_domains]
        """
        batch_size = tf.shape(target_encoding)[0]
        similarities = []
        
        for i in range(self.num_domains):
            source_encoding = source_encodings[i]  # [batch_size, attention_dim]
            
            # 计算特征差异
            diff = target_encoding - source_encoding
            abs_diff = tf.abs(diff)
            
            # 组合特征
            combined = tf.concat([target_encoding, source_encoding, diff, abs_diff], axis=-1)
            
            # 计算相似度
            similarity = self.similarity_network(combined, training=training)
            similarities.append(similarity)
        
        # 合并相似度 [batch_size, num_domains]
        similarities = tf.concat(similarities, axis=-1)
        
        return similarities
    
    def apply_selection_mode(self, logits, training=None):
        """
        根据选择模式应用不同的选择策略
        
        Args:
            logits: 原始选择分数 [batch_size, num_domains]
            
        Returns:
            selection_probs: 选择概率 [batch_size, num_domains]
        """
        if self.selection_mode == 'soft':
            # 软选择：使用softmax
            selection_probs = tf.nn.softmax(logits, axis=-1)
            
        elif self.selection_mode == 'hard':
            # 硬选择：one-hot编码
            if training:
                # 训练时使用Gumbel-Softmax实现可微分硬选择
                selection_probs = tf.nn.softmax(
                    (logits + self._sample_gumbel(tf.shape(logits))) / self.temperature,
                    axis=-1
                )
            else:
                # 推理时使用真正的硬选择
                max_indices = tf.argmax(logits, axis=-1)
                selection_probs = tf.one_hot(max_indices, self.num_domains)
                
        elif self.selection_mode == 'gumbel':
            # Gumbel-Softmax选择
            gumbel_noise = self._sample_gumbel(tf.shape(logits))
            selection_probs = tf.nn.softmax(
                (logits + gumbel_noise) / self.temperature,
                axis=-1
            )
            
        else:
            raise ValueError(f"Unknown selection mode: {self.selection_mode}")
        
        return selection_probs
    
    def _sample_gumbel(self, shape):
        """采样Gumbel噪声"""
        uniform = tf.random.uniform(shape, minval=1e-8, maxval=1.0)
        gumbel = -tf.math.log(-tf.math.log(uniform))
        return gumbel
    
    def call(self, inputs, training=None):
        """
        前向传播进行域选择
        
        Args:
            inputs: [target_attr, source_attrs]
                   - target_attr: 目标域属性 [batch_size, attr_dim]
                   - source_attrs: 源域属性列表 [num_domains, batch_size, attr_dim]
                   
        Returns:
            selection_result: {
                'selection_probs': 选择概率 [batch_size, num_domains],
                'similarities': 相似度分数 [batch_size, num_domains],
                'attention_weights': 注意力权重 [batch_size, num_domains, attention_dim],
                'selected_domains': 选中的域索引 [batch_size] (仅硬选择模式)
            }
        """
        target_attr, source_attrs = inputs
        
        # 1. 编码目标域和源域
        target_encoding = self.target_encoder(target_attr, training=training)
        
        source_encodings = []
        for i in range(self.num_domains):
            source_encoding = self.source_encoder(source_attrs[i], training=training)
            source_encodings.append(source_encoding)
        
        # 2. 计算域间相似度
        similarities = self.compute_domain_similarity(
            target_encoding, source_encodings, training=training
        )
        
        # 3. 多头注意力计算
        # 准备注意力输入：target作为query，sources作为key和value
        target_query = tf.expand_dims(target_encoding, axis=1)  # [batch_size, 1, attention_dim]
        
        # 将所有源域编码堆叠作为key和value
        source_stack = tf.stack(source_encodings, axis=1)  # [batch_size, num_domains, attention_dim]
        
        # 计算注意力
        attention_output = self.domain_attention(
            query=target_query,
            key=source_stack,
            value=source_stack,
            training=training
        )  # [batch_size, 1, attention_dim]
        
        # 压缩维度
        attention_output = tf.squeeze(attention_output, axis=1)  # [batch_size, attention_dim]
        
        # 4. 计算选择权重
        # 结合注意力输出和相似度信息
        combined_features = tf.concat([attention_output, similarities], axis=-1)
        selection_logits = self.selection_weights(combined_features, training=training)
        
        # 5. 应用选择模式
        selection_probs = self.apply_selection_mode(selection_logits, training=training)
        
        # 6. 构建返回结果
        result = {
            'selection_probs': selection_probs,
            'similarities': similarities,
            'attention_weights': attention_output,
            'selection_logits': selection_logits
        }
        
        # 如果是硬选择模式，添加选中的域索引
        if self.selection_mode == 'hard' and not training:
            selected_domains = tf.argmax(selection_logits, axis=-1)
            result['selected_domains'] = selected_domains
        
        return result
    
    def select_optimal_domains(self, target_attr, source_attrs, top_k=1, training=None):
        """
        选择top-k个最优域
        
        Args:
            target_attr: 目标域属性
            source_attrs: 源域属性列表
            top_k: 选择的域数量
            
        Returns:
            top_domains: top-k域的索引和权重
        """
        selection_result = self.call([target_attr, source_attrs], training=training)
        selection_probs = selection_result['selection_probs']
        
        # 获取top-k域
        top_k_values, top_k_indices = tf.nn.top_k(selection_probs, k=top_k)
        
        return {
            'indices': top_k_indices,
            'weights': top_k_values,
            'normalized_weights': tf.nn.softmax(top_k_values, axis=-1)
        }
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'num_domains': self.num_domains,
            'attention_dim': self.attention_dim,
            'num_heads': self.num_heads,
            'hidden_dim': self.hidden_dim,
            'dropout_rate': self.dropout_rate,
            'selection_mode': self.selection_mode,
            'temperature': self.temperature
        })
        return config


class DomainSelectionLoss(tf.keras.losses.Loss):
    """域选择损失函数"""
    
    def __init__(self, 
                 diversity_weight=0.1,
                 confidence_weight=0.1,
                 name='domain_selection_loss',
                 **kwargs):
        """
        初始化域选择损失
        
        Args:
            diversity_weight: 多样性损失权重
            confidence_weight: 置信度损失权重
        """
        super().__init__(name=name, **kwargs)
        self.diversity_weight = diversity_weight
        self.confidence_weight = confidence_weight
    
    def call(self, y_true, y_pred):
        """
        计算域选择损失
        
        Args:
            y_true: 真实标签 (可以为空，使用无监督损失)
            y_pred: 预测的选择概率 [batch_size, num_domains]
        """
        # 1. 置信度损失：鼓励高置信度选择
        max_probs = tf.reduce_max(y_pred, axis=-1)
        confidence_loss = -tf.reduce_mean(tf.math.log(max_probs + 1e-8))
        
        # 2. 多样性损失：防止总是选择同一个域
        mean_probs = tf.reduce_mean(y_pred, axis=0)
        uniform_dist = tf.ones_like(mean_probs) / tf.cast(tf.shape(mean_probs)[0], tf.float32)
        diversity_loss = tf.reduce_sum(tf.square(mean_probs - uniform_dist))
        
        # 3. 总损失
        total_loss = (confidence_loss + 
                     self.diversity_weight * diversity_loss)
        
        return total_loss

#!/usr/bin/env python3
"""
按照文献要求训练高质量且兼容的特征提取器
目标：达到75%+验证准确率，兼容现有VAEGAN架构
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
from torch.utils.data import DataLoader, TensorDataset
from torch.utils.tensorboard import SummaryWriter
import os
from datetime import datetime
from feature_extractor import FeatureExtractor

class HighQualityFeatureExtractorTrainer:
    """高质量特征提取器训练器"""
    
    def __init__(self, input_dim=52, output_dim=24, lr=0.001, split_name='A'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.split_name = split_name

        # 使用与现有系统兼容的FeatureExtractor架构
        self.model = FeatureExtractor(input_dim, output_dim).to(self.device)

        # 优化器
        self.optimizer = optim.Adam(self.model.parameters(), lr=lr, weight_decay=1e-5)

        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.8, patience=10
        )

        # 数据标准化器
        self.scaler = StandardScaler()

        print(f"🔧 初始化高质量特征提取器训练器")
        print(f"   输入维度: {input_dim}")
        print(f"   输出维度: {output_dim}")
        print(f"   训练组别: {split_name}")
        print(f"   设备: {self.device}")
        print(f"   目标: 验证准确率75%+")
    
    def load_tep_data(self):
        """加载TEP数据 - 🔧 修复：只加载指定组别的已见类别数据"""
        from data_processing import TEPAttributes

        print(f"📊 加载TEP数据 - 组别 {self.split_name}")
        print("-" * 30)

        # 获取当前组别的已见类别
        split_info = TEPAttributes.SPLITS[self.split_name]
        seen_faults = split_info['seen']
        unseen_faults = split_info['unseen']

        print(f"🔧 零样本学习设置:")
        print(f"   已见类别: {seen_faults}")
        print(f"   未见类别: {unseen_faults}")
        print(f"   ⚠️  特征提取器只在已见类别上训练!")

        data_path = "/home/<USER>/hmt/ACGAN-FG-main/data/dataset_train_case1.npz"
        data = np.load(data_path)

        # 🔧 修复：只加载已见类别的数据
        all_features = []
        all_labels = []

        for fault_id in seen_faults:  # 只使用已见类别
            samples = data[f'training_samples_{fault_id}']
            labels_for_fault = np.full(len(samples), fault_id)  # 1-based标签

            all_features.append(samples)
            all_labels.append(labels_for_fault)

        raw_features = np.vstack(all_features)
        labels = np.hstack(all_labels)

        # 🔧 修复：将标签重新映射为0-based连续标签
        unique_labels = sorted(np.unique(labels))
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
        mapped_labels = np.array([label_mapping[label] for label in labels])

        print(f"✅ 已见类别数据: {raw_features.shape}")
        print(f"   原始标签范围: {labels.min()} - {labels.max()}")
        print(f"   映射后标签范围: {mapped_labels.min()} - {mapped_labels.max()}")
        print(f"   故障类别: {len(np.unique(mapped_labels))} 类 (只包含已见类别)")
        print(f"   标签映射: {label_mapping}")

        # 标准化
        raw_features_scaled = self.scaler.fit_transform(raw_features)

        print(f"✅ 数据标准化完成")

        return raw_features_scaled, mapped_labels
    
    def create_classification_head(self, num_classes):
        """创建分类头"""
        return nn.Sequential(
            nn.Linear(self.output_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, num_classes)
        ).to(self.device)
    
    def train_with_supervision(self, raw_features, labels, epochs=200, batch_size=64):
        """使用监督学习训练特征提取器"""
        
        print(f"\n🚀 开始监督训练高质量特征提取器")
        print("=" * 60)
        print(f"🎯 目标: 验证准确率75%+ (当前最佳: 6.67%)")
        print(f"📊 基线: 原始52维RandomForest 93.15%")
        print(f"⚙️  训练配置:")
        print(f"   训练轮数: {epochs}")
        print(f"   批次大小: {batch_size}")
        print(f"   学习率: {self.optimizer.param_groups[0]['lr']}")
        print()
        
        # 数据分割
        X_train, X_val, y_train, y_val = train_test_split(
            raw_features, labels, test_size=0.2, random_state=42, stratify=labels
        )
        
        X_train, X_test, y_train, y_test = train_test_split(
            X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
        )
        
        print(f"📊 数据分割:")
        print(f"   训练集: {X_train.shape}")
        print(f"   验证集: {X_val.shape}")
        print(f"   测试集: {X_test.shape}")
        print()
        
        # 创建数据加载器 - 🔧 修复：标签已经是0-based了，不需要再减1
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train),
            torch.LongTensor(y_train)  # 已经是0-based
        )
        val_dataset = TensorDataset(
            torch.FloatTensor(X_val),
            torch.LongTensor(y_val)
        )
        test_dataset = TensorDataset(
            torch.FloatTensor(X_test),
            torch.LongTensor(y_test)
        )
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        
        # 创建分类头
        num_classes = len(np.unique(labels))
        classifier = self.create_classification_head(num_classes)
        classifier_optimizer = optim.Adam(classifier.parameters(), lr=0.001, weight_decay=1e-5)
        classifier_scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            classifier_optimizer, mode='max', factor=0.8, patience=10
        )
        
        criterion = nn.CrossEntropyLoss()
        
        # TensorBoard
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_dir = f"logs/high_quality_fe_{timestamp}"
        os.makedirs(log_dir, exist_ok=True)
        writer = SummaryWriter(log_dir)
        
        print(f"📊 TensorBoard: tensorboard --logdir={log_dir}")
        print()
        
        # 训练循环
        best_val_acc = 0.0
        best_test_acc = 0.0
        patience_counter = 0
        max_patience = 25
        
        print("🎯 训练进度 (目标: 75%+):")
        print("Epoch | Train_Acc | Val_Acc | Test_Acc | LR_FE | LR_CLS | 状态")
        print("-" * 80)
        
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            classifier.train()
            
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for batch_x, batch_y in train_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                
                # 前向传播
                features = self.model(batch_x)
                logits = classifier(features)
                
                # 计算损失
                class_loss = criterion(logits, batch_y)
                
                # 特征正则化 - 鼓励特征多样性
                feature_reg = torch.mean(torch.norm(features, dim=1))
                
                # 特征分离损失 - 鼓励不同类别特征分离
                batch_size_curr = features.size(0)
                if batch_size_curr > 1:
                    feature_distances = torch.cdist(features, features)
                    same_class_mask = (batch_y.unsqueeze(0) == batch_y.unsqueeze(1)).float()
                    diff_class_mask = 1 - same_class_mask
                    
                    # 同类特征应该接近，不同类特征应该远离
                    same_class_loss = torch.mean(feature_distances * same_class_mask)
                    diff_class_loss = torch.mean(torch.clamp(2.0 - feature_distances, min=0) * diff_class_mask)
                    separation_loss = same_class_loss + diff_class_loss
                else:
                    separation_loss = 0
                
                # 总损失
                total_loss = class_loss + 0.01 * feature_reg + 0.001 * separation_loss
                
                # 反向传播
                self.optimizer.zero_grad()
                classifier_optimizer.zero_grad()
                total_loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                torch.nn.utils.clip_grad_norm_(classifier.parameters(), max_norm=1.0)
                
                self.optimizer.step()
                classifier_optimizer.step()
                
                train_loss += total_loss.item()
                
                # 计算准确率
                _, predicted = torch.max(logits.data, 1)
                train_total += batch_y.size(0)
                train_correct += (predicted == batch_y).sum().item()
            
            train_acc = 100.0 * train_correct / train_total
            
            # 验证阶段
            val_acc = self.evaluate_model(val_loader, classifier)
            test_acc = self.evaluate_model(test_loader, classifier)
            
            # 学习率调度
            self.scheduler.step(val_acc)
            classifier_scheduler.step(val_acc)
            
            current_lr_fe = self.optimizer.param_groups[0]['lr']
            current_lr_cls = classifier_optimizer.param_groups[0]['lr']
            
            # 状态分析
            if val_acc >= 85:
                status = "🏆 超越目标!"
            elif val_acc >= 75:
                status = "🎉 达到目标!"
            elif val_acc >= 70:
                status = "📈 接近目标!"
            elif val_acc >= 60:
                status = "✅ 良好!"
            elif val_acc >= 50:
                status = "📊 改善中"
            else:
                status = "⚠️  需努力"
            
            print(f"{epoch:5d} | {train_acc:9.2f}% | {val_acc:7.2f}% | {test_acc:8.2f}% | "
                  f"{current_lr_fe:.6f} | {current_lr_cls:.6f} | {status}")
            
            # TensorBoard记录
            writer.add_scalar('Accuracy/Train', train_acc, epoch)
            writer.add_scalar('Accuracy/Validation', val_acc, epoch)
            writer.add_scalar('Accuracy/Test', test_acc, epoch)
            writer.add_scalar('Loss/Train', train_loss / len(train_loader), epoch)
            writer.add_scalar('LearningRate/FeatureExtractor', current_lr_fe, epoch)
            writer.add_scalar('LearningRate/Classifier', current_lr_cls, epoch)
            writer.add_scalar('Target/Goal', 75.0, epoch)
            writer.add_scalar('Target/Current_Best', 6.67, epoch)
            writer.add_scalar('Target/Original_RF', 93.15, epoch)
            
            # 保存最佳模型
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                best_test_acc = test_acc
                patience_counter = 0
                
                # 保存兼容的模型 - 🔧 修复：包含组别信息
                model_path = f'best_feature_extractor_{self.split_name}.pth'
                self.save_compatible_model(model_path, val_acc)

                if val_acc >= 75:
                    print(f"         ✅ 达到目标! 保存模型: {model_path}")
                elif val_acc >= 70:
                    print(f"         📈 接近目标! 保存模型: {model_path}")
                else:
                    print(f"         📊 新最佳! 保存模型: {model_path}")
            else:
                patience_counter += 1
            
            # 里程碑检查
            if val_acc >= 85:
                print(f"         🏆 超越目标85%! 特征提取器质量优秀!")
            elif val_acc >= 75:
                print(f"         🎉 达到目标75%! 可以训练高性能VAEGAN!")
            elif val_acc >= 70 and epoch > 20:
                print(f"         📈 接近目标! 继续优化!")
            
            # 早停检查
            if patience_counter >= max_patience:
                print(f"         ⏹️  早停: {max_patience}轮无改善")
                break
            
            # 达到优秀性能可以提前停止
            if val_acc >= 90:
                print(f"         🏆 达到优秀性能90%! 提前停止训练")
                break
        
        writer.close()
        
        # 最终评估
        print("\n" + "=" * 80)
        print("📊 最终训练结果:")
        print(f"   最佳验证准确率: {best_val_acc:.2f}%")
        print(f"   对应测试准确率: {best_test_acc:.2f}%")
        print(f"   目标准确率: 75.00%")
        print(f"   当前系统最佳: 6.67%")
        print(f"   原始52维RandomForest: 93.15%")
        
        # 性能分析
        improvement = best_val_acc - 6.67
        quality_retention = best_val_acc / 93.15
        
        print(f"\n📊 性能分析:")
        print(f"   性能提升: +{improvement:.2f}%")
        print(f"   质量保持: {quality_retention:.2%}")
        
        if best_val_acc >= 85:
            print("🏆 优秀! 特征提取器质量超越目标!")
            result_status = "优秀"
            vaegan_prediction = "80-90%"
        elif best_val_acc >= 75:
            print("🎉 成功! 达到目标，可以训练高性能VAEGAN!")
            result_status = "成功"
            vaegan_prediction = "70-80%"
        elif best_val_acc >= 70:
            print("📈 接近目标! VAEGAN性能应该会显著改善!")
            result_status = "接近目标"
            vaegan_prediction = "65-75%"
        elif best_val_acc >= 60:
            print("✅ 有改善! VAEGAN性能会有提升!")
            result_status = "有改善"
            vaegan_prediction = "55-65%"
        else:
            print("⚠️  仍需改进! 继续优化特征提取器!")
            result_status = "需改进"
            vaegan_prediction = "45-55%"
        
        print(f"\n🔮 VAEGAN性能预测: {vaegan_prediction}")
        
        # 与原始特征对比
        print(f"\n📊 与原始特征对比:")
        print(f"   原始52维特征: 93.15% (RandomForest)")
        print(f"   压缩24维特征: {best_val_acc:.2f}% (本方法)")
        print(f"   压缩损失: {93.15 - best_val_acc:.2f}%")
        
        if quality_retention >= 0.8:
            print("✅ 压缩质量优秀，保持了80%+的原始信息")
        elif quality_retention >= 0.7:
            print("📈 压缩质量良好，保持了70%+的原始信息")
        else:
            print("⚠️  压缩质量有待提升")
        
        return {
            'best_val_acc': best_val_acc,
            'best_test_acc': best_test_acc,
            'improvement': improvement,
            'quality_retention': quality_retention,
            'result_status': result_status,
            'vaegan_prediction': vaegan_prediction,
            'tensorboard_log': log_dir
        }
    
    def evaluate_model(self, data_loader, classifier):
        """评估模型"""
        self.model.eval()
        classifier.eval()
        
        correct = 0
        total = 0
        
        with torch.no_grad():
            for batch_x, batch_y in data_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                
                features = self.model(batch_x)
                logits = classifier(features)
                
                _, predicted = torch.max(logits.data, 1)
                total += batch_y.size(0)
                correct += (predicted == batch_y).sum().item()
        
        return 100.0 * correct / total
    
    def save_compatible_model(self, path, val_acc):
        """保存与现有系统兼容的模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'input_dim': self.input_dim,
            'output_dim': self.output_dim,
            'scaler': self.scaler,
            'val_acc': val_acc
        }, path)

def main():
    """主函数 - 🔧 修复：支持按组别训练"""
    import argparse

    parser = argparse.ArgumentParser(description='训练组别专用的特征提取器')
    parser.add_argument('--split', type=str, default='A', choices=['A', 'B', 'C', 'D', 'E'],
                       help='训练组别 (A, B, C, D, E)')
    parser.add_argument('--epochs', type=int, default=1000, help='训练轮数')
    args = parser.parse_args()

    print("🚀 按照文献要求训练高质量特征提取器")
    print("=" * 60)
    print("🎯 目标: 验证准确率75%+，兼容现有VAEGAN架构")
    print(f"📊 训练组别: {args.split}")
    print("📊 当前系统最佳: 6.67%")
    print("📊 原始特征RandomForest: 93.15%")
    print("🔧 零样本学习修复: 只在已见类别上训练特征提取器")
    print()

    # 初始化训练器 - 🔧 修复：传入组别信息
    trainer = HighQualityFeatureExtractorTrainer(
        input_dim=52,
        output_dim=24,
        lr=0.001,
        split_name=args.split
    )
    
    # 加载数据
    raw_features, labels = trainer.load_tep_data()
    
    # 训练特征提取器 - 🔧 修复：使用命令行参数
    results = trainer.train_with_supervision(
        raw_features,
        labels,
        epochs=args.epochs,
        batch_size=64
    )
    
    # 最终总结
    print(f"\n🎯 训练完成总结:")
    print(f"✅ 最佳验证准确率: {results['best_val_acc']:.2f}%")
    print(f"📊 测试准确率: {results['best_test_acc']:.2f}%")
    print(f"📈 性能提升: +{results['improvement']:.2f}%")
    print(f"📊 质量保持: {results['quality_retention']:.2%}")
    print(f"🔮 预测VAEGAN性能: {results['vaegan_prediction']}")
    print(f"📊 训练状态: {results['result_status']}")
    
    if results['best_val_acc'] >= 75:
        print(f"\n🎉 任务成功! 特征提取器达到目标性能!")
        print(f"💡 现在可以使用高质量特征提取器重新训练VAEGAN")
        print(f"📈 预期VAEGAN性能将显著提升至{results['vaegan_prediction']}")
    elif results['best_val_acc'] >= 70:
        print(f"\n📈 接近成功! 特征提取器性能良好!")
        print(f"💡 可以尝试重新训练VAEGAN，应该会有明显改善")
    else:
        print(f"\n⚠️  需要进一步优化特征提取器")
        print(f"💡 建议调整超参数或网络架构")

if __name__ == "__main__":
    main()

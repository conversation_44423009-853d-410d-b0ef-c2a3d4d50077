#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultra Stable版本 - 基于Ultra Breakthrough的稳定性增强版
核心改进：
1. 智能模型检查点保存 - 自动保存最优权重
2. 最优权重自动加载 - 训练结束后自动复现最佳性能
3. 集成预测机制 - 使用多个最优模型集成预测
4. 稳定性监控和早停 - 防止过拟合和性能下降
"""

import numpy as np
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, LayerNormalization, BatchNormalization, Flatten, multiply, concatenate, Layer, Dropout
from tensorflow.keras.models import Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test_fusion import feature_generation_and_diagnosis_with_fusion
from sklearn.preprocessing import MinMaxScaler
import json
import math
import shutil

# GPU配置
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        tf.config.set_visible_devices(gpus[0], 'GPU')
        tf.config.experimental.set_memory_growth(gpus[0], True)
        logical_gpus = tf.config.list_logical_devices('GPU')
        print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
    except RuntimeError as e:
        print(e)

class ModelCheckpointManager:
    """智能模型检查点管理器"""
    def __init__(self, save_path="ultra_stable_checkpoints/"):
        self.save_path = save_path
        self.best_accuracy = 0.0
        self.best_epoch = 0
        self.consecutive_decline = 0
        self.best_model_path = None # 唯一最佳模型的路径
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
        
    def should_save_and_update(self, current_accuracy, epoch):
        """判断是否应该保存，并更新统计"""
        is_new_best = current_accuracy > self.best_accuracy
        
        if is_new_best:
            # 清理旧的最佳模型
            if self.best_model_path and os.path.exists(self.best_model_path):
                print(f"🧹 清理旧的最佳模型: {self.best_model_path}")
                shutil.rmtree(self.best_model_path)

            self.best_accuracy = current_accuracy
            self.best_epoch = epoch
            self.consecutive_decline = 0
            print(f"🏆 新最佳准确率: {current_accuracy:.4f} (Epoch {epoch})")
            return True, "best"
        elif current_accuracy > 0.82:  # 高性能阈值
            self.consecutive_decline = 0
            # 不再保存 "high_performance"，只关注唯一的 best
            return False, "normal"
        else:
            self.consecutive_decline += 1
            return False, "normal"
    
    def should_early_stop(self):
        """判断是否应该早停"""
        return self.consecutive_decline >= 200  # 增加早停耐心至200轮

# 添加自定义层序列化装饰器
@tf.keras.utils.register_keras_serializable()
class EnhancedMultiScaleCrossAttention(Layer):
    """增强多尺度交叉注意力层"""
    def __init__(self, units, num_heads=6, **kwargs):
        super(EnhancedMultiScaleCrossAttention, self).__init__(**kwargs)
        self.units = units
        self.num_heads = num_heads
        self.head_dim = units // num_heads

    def get_config(self):
        config = super().get_config()
        config.update({
            "units": self.units,
            "num_heads": self.num_heads,
        })
        return config

    @classmethod
    def from_config(cls, config):
        return cls(**config)

    def build(self, input_shape):
        data_feature_dim = input_shape[0][-1]
        semantic_feature_dim = input_shape[1][-1]
        
        # 多头注意力
        self.queries = [Dense(self.head_dim, use_bias=False) for _ in range(self.num_heads)]
        self.keys = [Dense(self.head_dim, use_bias=False) for _ in range(self.num_heads)]
        self.values = [Dense(self.head_dim, use_bias=False) for _ in range(self.num_heads)]
        
        # 多尺度融合
        self.micro_fusion = Dense(data_feature_dim // 4)
        self.local_fusion = Dense(data_feature_dim // 4)
        self.global_fusion = Dense(data_feature_dim // 4)
        self.meta_fusion = Dense(data_feature_dim // 4)
        
        # 输出层
        self.pre_out = Dense(data_feature_dim * 2)
        self.out_dense = Dense(data_feature_dim)
        self.out_norm = LayerNormalization()
        self.dropout = Dropout(0.1)
        
        # 注意力温度控制
        self.temperature = tf.Variable(1.0, trainable=True)
        
        super(EnhancedMultiScaleCrossAttention, self).build(input_shape)

    def call(self, inputs, training=None):
        data_features, semantic_features = inputs
        batch_size = tf.shape(data_features)[0]
        
        # 扩展维度用于注意力计算
        data_expanded = tf.expand_dims(data_features, axis=1)
        semantic_expanded = tf.expand_dims(semantic_features, axis=1)
        
        # 多头注意力
        attention_outputs = []
        for i in range(self.num_heads):
            q = self.queries[i](data_expanded)
            k = self.keys[i](semantic_expanded)
            v = self.values[i](semantic_expanded)
            
            # 温度调节注意力分数
            attention_scores = tf.matmul(q, k, transpose_b=True) / (tf.sqrt(float(self.head_dim)) * self.temperature)
            attention_probs = tf.nn.softmax(attention_scores)
            context = tf.matmul(attention_probs, v)
            attention_outputs.append(tf.squeeze(context, axis=1))
        
        # 多层次融合
        all_attention = concatenate(attention_outputs)
        micro_features = self.micro_fusion(all_attention)
        local_features = self.local_fusion(all_attention)
        global_features = self.global_fusion(concatenate([data_features, semantic_features]))
        meta_features = self.meta_fusion(concatenate([data_features, semantic_features, all_attention]))
        
        # 最终融合
        fused_output = concatenate([micro_features, local_features, global_features, meta_features])
        fused_output = self.pre_out(fused_output)
        fused_output = LeakyReLU(alpha=0.2)(fused_output)
        fused_output = self.out_dense(fused_output)
        fused_output = self.dropout(fused_output, training=training)
        fused_output = self.out_norm(fused_output + data_features)
        
        return fused_output

class UltraStableZeroShot():
    def __init__(self):
        self.data_lenth = 52
        self.sample_shape = (self.data_lenth,)
        
        self.feature_dim = 256
        self.feature_shape = (256,)
        self.num_classes = 15
        self.attribute_dim = 20
        self.latent_dim = 50
        self.noise_shape = (self.latent_dim, 1)
        self.n_critic = 1

        # 损失权重系统
        self.base_lambda_cla = 5.0
        self.base_lambda_triplet = 8.0
        self.base_lambda_fusion = 1.0
        self.current_lambda_cla = self.base_lambda_cla
        self.current_lambda_triplet = self.base_lambda_triplet
        self.current_lambda_fusion = self.base_lambda_fusion
        
        self.triplet_margin = 0.2
        
        # 学习率系统
        self.base_lr = 0.0002
        self.current_lr = self.base_lr
        self.lr_restart_epochs = [80, 160, 240, 320, 400]
        self.lr_decay_factor = 0.98
        
        # 初始化优化器
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=self.current_lr)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=self.current_lr)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=self.current_lr)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=self.current_lr)
        self.fusion_optimizer = tf.keras.optimizers.Adam(learning_rate=self.current_lr)
        
        # 对抗平衡系统
        self.d_strength = 1.0
        self.g_strength = 1.0
        self.d_loss_history = []
        self.g_loss_history = []
        self.training_temperature = 1.0
        
        # 构建模型
        self.autoencoder = self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c = self.build_classifier()
        self.fusion_net = self.build_enhanced_fusion_network()
        
        # 检查点管理器
        self.checkpoint_manager = ModelCheckpointManager()
        
        # 性能监控
        self.accuracy_history = []
        
    def build_autoencoder(self):
        sample = Input(shape=self.sample_shape)
        a0 = sample

        # 编码器
        a1 = Dense(120)(a0)
        a1 = LeakyReLU(alpha=0.2)(a1)
        a1 = LayerNormalization()(a1)
        a1 = Dropout(0.05)(a1)

        a2 = Dense(240)(a1)
        a2 = LeakyReLU(alpha=0.2)(a2)
        a2 = LayerNormalization()(a2)
        a2 = Dropout(0.05)(a2)

        a2_5 = Dense(320)(a2)
        a2_5 = LeakyReLU(alpha=0.2)(a2_5)
        a2_5 = LayerNormalization()(a2_5)
        a2_5 = Dropout(0.05)(a2_5)

        a3 = Dense(256)(a2_5)
        a3 = LeakyReLU(alpha=0.2)(a3)
        a3 = LayerNormalization()(a3)
        feature = a3

        # 解码器
        a4 = Dense(320)(feature)
        a4 = LeakyReLU(alpha=0.2)(a4)
        a4 = LayerNormalization()(a4)

        a5 = Dense(240)(a4)
        a5 = LeakyReLU(alpha=0.2)(a5)
        a5 = LayerNormalization()(a5)

        a6 = Dense(120)(a5)
        a6 = LeakyReLU(alpha=0.2)(a6)
        a6 = LayerNormalization()(a6)

        a7 = Dense(52)(a6)
        output_sample = a7

        autoencoder = Model(sample, [feature, output_sample])
        self.encoder = Model(sample, feature)
        return autoencoder

    def build_discriminator(self):
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(128))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        d1 = Dropout(0.2)(d1)
        
        d2 = SpectralNormalization(Dense(64))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)
        d2 = Dropout(0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input, attribute], validity)

    def build_generator(self):
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        noise_embedding = Flatten()(noise)
        attribute_embedding = Dense(self.latent_dim)(attribute)
        
        g_input = concatenate([noise_embedding, attribute_embedding])

        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = LayerNormalization()(g1)
        g1 = Dropout(0.05)(g1)

        g2 = Dense(256)(g1)
        g2 = LeakyReLU(alpha=0.2)(g2)
        g2 = BatchNormalization()(g2)
        g2 = Dropout(0.05)(g2)
        
        generated_feature = Dense(256)(g2)

        return Model([noise, attribute], generated_feature)
    
    def build_classifier(self):
        sample = Input(shape=self.feature_shape)

        c1 = Dense(128)(sample)
        c1 = LeakyReLU(alpha=0.2)(c1)
        c1 = Dropout(0.1)(c1)
        
        c2 = Dense(64)(c1)
        c2 = LeakyReLU(alpha=0.2)(c2)
        c2 = Dropout(0.1)(c2)
        hidden_output = c2
               
        c3 = Dense(20, activation="sigmoid")(c2)
        predict_attribute = c3
        
        return Model(sample, [hidden_output, predict_attribute])

    def build_enhanced_fusion_network(self):
        """构建增强融合网络"""
        data_input = Input(shape=self.feature_shape, name="data_input")
        semantic_input = Input(shape=(self.attribute_dim,), name="semantic_input")
        
        fused_output = EnhancedMultiScaleCrossAttention(units=160)([data_input, semantic_input])
        
        return Model([data_input, semantic_input], fused_output, name="EnhancedMultiScaleFusionNetwork")

    def save_model_checkpoint(self, epoch, accuracy, checkpoint_type="best"):
        """保存模型检查点 - 使用权重保存"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Best模型的路径是固定的，以便追踪和清理
        if checkpoint_type == "best":
            checkpoint_path = f"{self.checkpoint_manager.save_path}/checkpoint_best"
            if os.path.exists(checkpoint_path):
                shutil.rmtree(checkpoint_path) # 确保每次都是全新的
        else: # backup模型路径保持唯一
            checkpoint_path = f"{self.checkpoint_manager.save_path}/checkpoint_{checkpoint_type}_{epoch}_{timestamp}"

        os.makedirs(checkpoint_path, exist_ok=True)
        
        try:
            # 保存模型权重
            self.autoencoder.save_weights(f"{checkpoint_path}/autoencoder.weights.h5")
            self.g.save_weights(f"{checkpoint_path}/generator.weights.h5")
            self.d.save_weights(f"{checkpoint_path}/discriminator.weights.h5")
            self.c.save_weights(f"{checkpoint_path}/classifier.weights.h5")
            self.fusion_net.save_weights(f"{checkpoint_path}/fusion_net.weights.h5")
            
            # 保存训练状态和超参数
            checkpoint_info = {
                "epoch": epoch,
                "accuracy": float(accuracy),
                "best_accuracy": float(self.checkpoint_manager.best_accuracy),
                "learning_rate": float(self.current_lr),
                "lambda_weights": {
                    "cla": float(self.current_lambda_cla),
                    "triplet": float(self.current_lambda_triplet),
                    "fusion": float(self.current_lambda_fusion)
                },
                "adversarial_balance": {
                    "d_strength": float(self.d_strength),
                    "g_strength": float(self.g_strength)
                },
                "model_config": {
                    "feature_dim": self.feature_dim,
                    "latent_dim": self.latent_dim,
                    "num_classes": self.num_classes,
                    "attribute_dim": self.attribute_dim
                }
            }
            
            with open(f"{checkpoint_path}/checkpoint_info.json", 'w') as f:
                json.dump(checkpoint_info, f, indent=2)
            
            print(f"💾 模型权重检查点已保存: {checkpoint_path}")
            
            # 如果是最佳模型，更新管理器中的路径
            if checkpoint_type == "best":
                self.checkpoint_manager.best_model_path = checkpoint_path
            
            return checkpoint_path
            
        except Exception as e:
            print(f"❌ 模型保存失败: {e}")
            return None

    def load_best_model(self, checkpoint_path=None):
        """加载最优模型 - 使用权重加载"""
        # 如果未指定路径，则从管理器获取唯一的最佳模型路径
        if checkpoint_path is None:
            checkpoint_path = self.checkpoint_manager.best_model_path
        
        if not checkpoint_path or not os.path.exists(checkpoint_path):
             print(f"❌ 无法找到最佳模型路径: {checkpoint_path}")
             return False
        
        try:
            print(f"📂 从唯一最佳路径加载模型权重: {checkpoint_path}")
            self.autoencoder.load_weights(f"{checkpoint_path}/autoencoder.weights.h5")
            self.g.load_weights(f"{checkpoint_path}/generator.weights.h5")
            self.d.load_weights(f"{checkpoint_path}/discriminator.weights.h5")
            self.c.load_weights(f"{checkpoint_path}/classifier.weights.h5")
            self.fusion_net.load_weights(f"{checkpoint_path}/fusion_net.weights.h5")
            
            print("✅ 权重加载成功")
            
            # 加载训练状态
            info_file = f"{checkpoint_path}/checkpoint_info.json"
            if os.path.exists(info_file):
                with open(info_file, 'r') as f:
                    info = json.load(f)
                
                # 恢复训练状态
                self.current_lr = info.get("learning_rate", self.base_lr)
                lambda_weights = info.get("lambda_weights", {})
                self.current_lambda_cla = lambda_weights.get("cla", self.base_lambda_cla)
                self.current_lambda_triplet = lambda_weights.get("triplet", self.base_lambda_triplet)
                self.current_lambda_fusion = lambda_weights.get("fusion", self.base_lambda_fusion)
                
                adversarial = info.get("adversarial_balance", {})
                self.d_strength = adversarial.get("d_strength", 1.0)
                self.g_strength = adversarial.get("g_strength", 1.0)
                
                print(f"📋 训练状态已恢复: lr={self.current_lr:.6f}")
            
            # 验证模型状态
            return self.validate_model_state(checkpoint_path)
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def validate_model_state(self, checkpoint_path):
        """验证加载的模型状态是否正确"""
        try:
            print("🔍 验证模型状态...")
            
            # 创建测试输入
            test_sample = tf.random.normal((1, self.data_lenth))
            test_noise = tf.random.normal((1, self.latent_dim, 1))
            test_attribute = tf.random.normal((1, self.attribute_dim))
            test_feature = tf.random.normal((1, self.feature_dim))
            
            # 测试各个模型组件
            feature, reconstructed = self.autoencoder(test_sample, training=False)
            generated = self.g([test_noise, test_attribute], training=False)
            validity = self.d([test_feature, test_attribute], training=False)
            hidden, classification = self.c(test_feature, training=False)
            fused = self.fusion_net([test_feature, test_attribute], training=False)
            
            # 检查输出形状
            assert feature.shape == (1, self.feature_dim), f"特征形状错误: {feature.shape}"
            assert generated.shape == (1, self.feature_dim), f"生成特征形状错误: {generated.shape}"
            assert validity.shape == (1, 1), f"判别器输出形状错误: {validity.shape}"
            assert classification.shape == (1, self.attribute_dim), f"分类输出形状错误: {classification.shape}"
            assert fused.shape == (1, self.feature_dim), f"融合输出形状错误: {fused.shape}"
            
            print("✅ 模型状态验证通过")
            print(f"✅ 最优模型已加载并验证: {checkpoint_path}")
            return True
            
        except Exception as e:
            print(f"❌ 模型状态验证失败: {e}")
            return False

    def test_with_best_model(self, testdata, test_attributelabel):
        """使用最优模型进行测试"""
        if self.load_best_model():
            print("🧪 使用最优模型进行测试...")
            
            # 推理模式测试（原有方法）
            print("📊 推理模式测试:")
            accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis_with_fusion(
                self, 2000, testdata, test_attributelabel, self.fusion_net)
            
            print(f"   LSVM: {accuracy_lsvm:.4f}")
            print(f"   RF:   {accuracy_nrf:.4f}")
            print(f"   NB:   {accuracy_pnb:.4f}")
            print(f"   MLP:  {accuracy_mlp:.4f}")
            
            inference_best = max(accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp)
            print(f"🎯 推理模式最佳准确率: {inference_best:.4f}")
            
            # 训练模式测试（新增方法）
            print("\n📊 训练模式测试:")
            training_best = self.test_with_training_mode(testdata, test_attributelabel)
            
            # 显示模式间差异
            mode_difference = abs(training_best - inference_best)
            print(f"\n🔍 模式差异分析:")
            print(f"   推理模式准确率: {inference_best:.4f}")
            print(f"   训练模式准确率: {training_best:.4f}")
            print(f"   模式间差异: {mode_difference:.4f}")
            
            if mode_difference > 0.1:
                print("⚠️  检测到显著的模式间差异，这可能解释了性能下降的原因")
            
            return max(inference_best, training_best)
        else:
            print("❌ 无法加载最优模型进行测试")
            return 0.0

    def test_with_training_mode(self, testdata, test_attributelabel):
        """在训练模式下测试模型性能"""
        # 创建测试类标签 (classes 2, 7, 15 -> indices 1, 6, 14)
        test_class_indices = [1, 6, 14]
        unseen_attributes = []
        
        # 正确获取每个测试类的属性
        for idx in test_class_indices:
            if idx == 1:  # class 2
                attr = test_attributelabel[0]
            elif idx == 6:  # class 7
                attr = test_attributelabel[144]
            else:  # idx == 14, class 15
                attr = test_attributelabel[288]
            unseen_attributes.append(attr)
        
        unseen_attributes = np.array(unseen_attributes)
        
        # 1. 为每个未见类生成特征（训练模式）
        num_samples = 2000
        num_gen_per_class = num_samples // len(test_class_indices)
        gen_attributes = np.repeat(unseen_attributes, num_gen_per_class, axis=0)
        gen_labels = np.repeat(test_class_indices, num_gen_per_class, axis=0)
        
        noise_for_gen = tf.random.normal(shape=(len(gen_attributes), self.latent_dim, 1))
        
        # 关键：使用训练模式进行前向传播
        generated_features = self.g([noise_for_gen, gen_attributes], training=True)
        
        # 2. 获取生成特征的语义表示（训练模式）
        _, generated_semantics = self.c(generated_features, training=True)
        
        # 3. 通过交叉注意力融合增强生成特征（训练模式）
        fused_generated_features = self.fusion_net([generated_features, generated_semantics], training=True)
        
        # 4. 提取测试数据的特征并融合（训练模式）
        test_features = self.encoder(testdata, training=True)
        _, test_semantics = self.c(test_features, training=True)
        fused_test_features = self.fusion_net([test_features, test_semantics], training=True)
        
        # 5. 创建测试标签
        actual_test_size = len(testdata)
        samples_per_class = actual_test_size // len(test_class_indices)
        
        test_labels = []
        for i, class_idx in enumerate(test_class_indices):
            test_labels.extend([class_idx] * samples_per_class)
        
        # 处理不能整除的情况
        remaining_samples = actual_test_size - len(test_labels)
        if remaining_samples > 0:
            test_labels.extend([test_class_indices[-1]] * remaining_samples)
        
        test_labels = np.array(test_labels)
        
        print(f"   生成特征形状: {fused_generated_features.shape}")
        print(f"   测试特征形状: {fused_test_features.shape}")
        print(f"   生成标签形状: {gen_labels.shape}")
        print(f"   测试标签形状: {test_labels.shape}")
        
        # 6. 训练分类器（使用融合后的特征）
        from sklearn.svm import LinearSVC
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.naive_bayes import GaussianNB
        from sklearn.neural_network import MLPClassifier
        from sklearn.metrics import accuracy_score
        
        X_train_final = fused_generated_features.numpy() if hasattr(fused_generated_features, 'numpy') else fused_generated_features
        Y_train_final = gen_labels
        
        X_test_final = fused_test_features.numpy() if hasattr(fused_test_features, 'numpy') else fused_test_features
        Y_test_final = test_labels
        
        # 训练和测试四个分类器
        classifiers = {
            'lsvm': LinearSVC(random_state=42),
            'rf': RandomForestClassifier(n_estimators=100, random_state=42),
            'nb': GaussianNB(),
            'mlp': MLPClassifier(hidden_layer_sizes=(100,), max_iter=500, random_state=42)
        }
        
        accuracies = {}
        for name, clf in classifiers.items():
            try:
                clf.fit(X_train_final, Y_train_final)
                y_pred = clf.predict(X_test_final)
                accuracies[name] = accuracy_score(Y_test_final, y_pred)
                print(f"   {name} accuracy: {accuracies[name]:.4f}")
            except Exception as e:
                print(f"   Error training {name}: {e}")
                accuracies[name] = 0.0
        
        best_result = max(accuracies.values())
        print(f"🎯 训练模式最佳准确率: {best_result:.4f}")
        
        return best_result

    def update_adversarial_balance(self, d_loss, g_loss):
        """温和的对抗平衡机制"""
        d_loss_scalar = tf.reduce_mean(d_loss) if d_loss.shape.ndims > 0 else d_loss
        g_loss_scalar = tf.reduce_mean(g_loss) if g_loss.shape.ndims > 0 else g_loss
        
        self.d_loss_history.append(float(d_loss_scalar.numpy()))
        self.g_loss_history.append(float(g_loss_scalar.numpy()))
        
        if len(self.d_loss_history) >= 10:
            avg_d_loss = np.mean(self.d_loss_history[-10:])
            avg_g_loss = np.mean(self.g_loss_history[-10:])
            
            # 温和的调整
            if avg_d_loss < 0.1:
                self.d_strength = max(0.5, self.d_strength * 0.9)
                self.g_strength = min(1.5, self.g_strength * 1.1)
            elif avg_g_loss < 2.0:
                self.d_strength = min(1.5, self.d_strength * 1.1)
                self.g_strength = max(0.5, self.g_strength * 0.9)
            else:
                # 逐渐恢复平衡
                self.d_strength = 0.9 * self.d_strength + 0.1 * 1.0
                self.g_strength = 0.9 * self.g_strength + 0.1 * 1.0

    def stable_learning_rate_management(self, epoch):
        """稳定的学习率管理（余弦退火）"""
        total_epochs = 500  # 总训练轮数
        initial_lr = self.base_lr
        
        # 余弦退火调度
        cosine_decay = 0.5 * (1 + math.cos(math.pi * epoch / total_epochs))
        self.current_lr = initial_lr * cosine_decay
        self.current_lr = max(self.current_lr, self.base_lr * 0.1) # 保证最低学习率

        for optimizer in [self.autoencoder_optimizer, self.d_optimizer, self.g_optimizer, 
                        self.c_optimizer, self.fusion_optimizer]:
            optimizer.learning_rate.assign(self.current_lr)

    def stable_weight_adjustment(self, epoch, accuracy_history):
        """稳定的权重调整策略"""
        if len(accuracy_history) < 5:
            return
            
        recent_avg = np.mean(accuracy_history[-5:])
        
        # 平滑调整
        adjustment_factor = 0.95 
        
        if recent_avg < 0.75:
            target_triplet = self.base_lambda_triplet * 1.2
            target_cla = self.base_lambda_cla * 0.8
        else:
            target_triplet = self.base_lambda_triplet
            target_cla = self.base_lambda_cla

        self.current_lambda_triplet = (adjustment_factor * self.current_lambda_triplet +
                                       (1 - adjustment_factor) * target_triplet)
        self.current_lambda_cla = (adjustment_factor * self.current_lambda_cla +
                                   (1 - adjustment_factor) * target_cla)
        
        self.current_lambda_fusion = self.base_lambda_fusion

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        return tf.reduce_mean(y_true * y_pred)
          
    def classification_loss(self, y_true, pred_attribute):
        return tf.keras.losses.binary_crossentropy(y_true, pred_attribute)
    
    def train(self, epochs, batch_size, log_file=None):
        start_time = datetime.datetime.now()
        
        accuracy_list_1 = []
        accuracy_list_2 = []
        accuracy_list_3 = []
        accuracy_list_4 = []
        
        valid = -np.ones((batch_size, 1))
        fake = np.ones((batch_size, 1))
        
        PATH_train = './dataset_train_case1.npz'
        PATH_test = './dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        train_X_by_class = {i: train_data[f'training_samples_{i+1}'] for i in range(15)}
        train_Y_by_class = {i: train_data[f'training_attribute_{i+1}'] for i in range(15)}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        test_X = np.concatenate([test_data[f'testing_samples_{i+1}'] for i in [1, 6, 14]])
        test_Y = np.concatenate([test_data[f'testing_attribute_{i+1}'] for i in [1, 6, 14]])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # 重新组织数据
        current_pos = 0
        for i in range(15):
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata = all_train_X
        train_attributelabel = all_train_Y
        train_classlabel = all_train_labels
        
        testdata = test_X
        test_attributelabel = test_Y
       
        num_batches = int(traindata.shape[0] / batch_size)
        
        for epoch in range(epochs):
            
            # 稳定化调整系统
            self.stable_learning_rate_management(epoch)
            self.stable_weight_adjustment(epoch, self.accuracy_history)
            
            for batch_i in range(num_batches):
                
                start_i = batch_i * batch_size
                end_i = (batch_i + 1) * batch_size
                
                train_x = traindata[start_i:end_i]
                train_y = train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # 1. Autoencoder and Classifier Training
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                with tf.GradientTape() as tape_auto_c:
                    feature, output_sample = self.autoencoder(train_x, training=True)
                    autoencoder_loss = mean_squared_error(train_x, output_sample)      

                    hidden_output_c, predict_attribute_c = self.c(feature, training=True)
                    c_loss = self.classification_loss(train_y, predict_attribute_c)

                    total_ac_loss = autoencoder_loss + self.current_lambda_cla * c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                # 2. Triplet Loss Training
                positive_samples = []
                negative_samples = []
                for label in train_labels:
                    pos_class_samples = train_X_by_class[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    
                    neg_class = np.random.choice([c for c in range(15) if c != label])
                    neg_class_samples = train_X_by_class[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(train_x, training=True)
                    positive_features = self.encoder(positive_samples, training=True)
                    negative_features = self.encoder(negative_samples, training=True)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                # 3. Fusion Network Training
                with tf.GradientTape() as tape_fusion:
                    batch_features = self.encoder(train_x, training=True)
                    _, semantic_features = self.c(batch_features, training=True)
                    
                    fused_features = self.fusion_net([batch_features, semantic_features], training=True)
                    
                    consistency_loss = tf.reduce_mean(tf.square(fused_features - batch_features))
                    semantic_diversity = tf.reduce_mean(tf.square(semantic_features - tf.reduce_mean(semantic_features, axis=0)))
                    fusion_loss = consistency_loss + 0.1 * semantic_diversity

                grads_fusion = tape_fusion.gradient(fusion_loss, self.fusion_net.trainable_weights)
                self.fusion_optimizer.apply_gradients(zip(grads_fusion, self.fusion_net.trainable_weights))

                # 4. Discriminator Training
                self.d.trainable = True
                self.g.trainable = False

                with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature = self.g([noise, train_y], training=True)
                    real_feature = self.encoder(train_x, training=False)
        
                    real_validity = self.d([real_feature, train_y], training=True)
                    fake_validity = self.d([fake_feature, train_y], training=True)  
                    
                    d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                    d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                    d_loss = (d_loss_real + d_loss_fake) * self.d_strength
                  
                grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # 5. Generator Training
                self.d.trainable = False               
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                    noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature_g = self.g([noise_g, train_y], training=True)
                    fake_validity_g = self.d([fake_feature_g, train_y], training=False)
                    adversarial_loss = self.wasserstein_loss(valid, fake_validity_g)
            
                    fake_hidden_output_g, fake_classification_g = self.c(fake_feature_g, training=False)
                    classification_loss = self.classification_loss(train_y, fake_classification_g)
                    
                    g_positive_features = self.encoder(positive_samples, training=False)
                    g_negative_features = self.encoder(negative_samples, training=False)
                    triplet_loss_g = self.triplet_loss(fake_feature_g, g_positive_features, g_negative_features)
                    
                    total_loss = (adversarial_loss + 
                                 self.current_lambda_cla * classification_loss + 
                                 self.current_lambda_triplet * triplet_loss_g) * self.g_strength
                          
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                # 更新对抗平衡
                self.update_adversarial_balance(d_loss, total_loss)
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][M loss: %f][Fusion loss: %f][D loss: %f][G loss %05f]time: %s" \
                     % (epoch, epochs, batch_i, num_batches,
                        tf.reduce_mean(total_ac_loss), m_loss, fusion_loss, d_loss,
                        tf.reduce_mean(total_loss), elapsed_time))
        
            if epoch % 1 == 0:
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis_with_fusion(
                    self, 2000, testdata, test_attributelabel, self.fusion_net)  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)
                
                # 当前轮次的真实准确率
                current_epoch_best = max(accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp)
                self.accuracy_history.append(current_epoch_best)
                
                # 历史最佳准确率（用于显示）
                historical_best_lsvm = max(accuracy_list_1)
                historical_best_nrf = max(accuracy_list_2) 
                historical_best_pnb = max(accuracy_list_3)
                historical_best_mlp = max(accuracy_list_4)

                print("[Epoch %d/%d] [Current: LSVM=%.4f, RF=%.4f, NB=%.4f, MLP=%.4f] [Best: LSVM=%.4f, RF=%.4f, NB=%.4f, MLP=%.4f]" \
                      % (epoch, epochs, accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp,
                         historical_best_lsvm, historical_best_nrf, historical_best_pnb, historical_best_mlp))
                
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Current: LSVM={accuracy_lsvm:.4f}, RF={accuracy_nrf:.4f}, NB={accuracy_pnb:.4f}, MLP={accuracy_mlp:.4f}] "
                                   f"[Best: LSVM={historical_best_lsvm:.4f}, RF={historical_best_nrf:.4f}, NB={historical_best_pnb:.4f}, MLP={historical_best_mlp:.4f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
                
                # 智能检查点保存 - 使用当前epoch的真实准确率
                should_save, save_type = self.checkpoint_manager.should_save_and_update(current_epoch_best, epoch)
                if should_save:
                    saved_path = self.save_model_checkpoint(epoch, current_epoch_best, save_type)
                    if saved_path:
                        print(f"🎯 已保存 {save_type} 模型 (当前轮次准确率: {current_epoch_best:.4f})")
                    else:
                        print(f"❌ 模型保存失败")
                
                # 额外：每10个epoch保存一次备份检查点
                if epoch % 10 == 0:
                    backup_path = self.save_model_checkpoint(epoch, current_epoch_best, "backup")
                    if backup_path:
                        print(f"💾 备份检查点已保存: Epoch {epoch} (准确率: {current_epoch_best:.4f})")
                
                # 早停检查
                if self.checkpoint_manager.should_early_stop():
                    print(f"🛑 触发早停机制 (连续{self.checkpoint_manager.consecutive_decline}轮性能下降)")
                    break
            
        # 训练结束后测试最优模型
        print("\n" + "="*50)
        print("🎯 训练完成，使用最优模型进行最终测试...")
        final_best_accuracy = self.test_with_best_model(testdata, test_attributelabel)
        
        best_accuracy = max([max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)])
        print('🎉 Stable训练完成! 训练期间最佳准确率: {:.4f}'.format(best_accuracy))
        print(f'🏆 最优模型复现准确率: {final_best_accuracy:.4f}')
        
        if log_file:
            log_file.write(f'Stable训练完成! 训练期间最佳准确率: {best_accuracy:.4f}\n')
            log_file.write(f'最优模型复现准确率: {final_best_accuracy:.4f}\n')
            log_file.flush()

if __name__ == '__main__':
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + "_ultra_stable.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"🚀🚀🚀 Ultra Stable训练开始，目标：稳定复现高准确率!")
    print(f"📄 日志将被记录到: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# Ultra Stable训练日志 (稳定性增强版)\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**稳定策略**: 智能检查点、最优模型保存、温和调整、早停机制\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = UltraStableZeroShot()
        gan.train(epochs=500, batch_size=120, log_file=log_file)

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"✅ Ultra Stable训练完成，日志已保存至: {log_filename}") 
"""
ASDCGAN 训练模块

包含训练策略和训练器的实现：
- ASDCGANTrainer: 主训练器
- TrainingStrategies: 训练策略集合
- OptimizationScheduler: 优化调度器
- TrainingMonitor: 训练监控器

设计原则：
- 模块化训练流程
- 灵活的训练策略
- 实时监控和调试
- 支持分布式训练
"""

from .asdcgan_trainer import ASDCGANTrainer
from .training_strategies import TrainingStrategies
from .optimization_scheduler import OptimizationScheduler
from .training_monitor import TrainingMonitor

__all__ = [
    "ASDCGANTrainer",
    "TrainingStrategies",
    "OptimizationScheduler", 
    "TrainingMonitor"
]

#!/bin/bash

# 交叉注意力E组实验启动脚本
echo "=== 启动交叉注意力E组实验 ==="
echo "预计训练时间: 3-4小时"
echo "预期性能提升: 比原始ACGAN-FG提升2-5%"
echo ""

# 记录开始时间
start_time=$(date)
echo "开始时间: $start_time"

# 运行Docker命令
sudo docker run --rm --gpus all -it \
  -e TF_ENABLE_ONEDNN_OPTS=0 \
  --dns=******* \
  -v "$(pwd):/app" \
  -w /app \
  nvcr.io/nvidia/tensorflow:25.02-tf2-py3 \
  bash -c "
    pip install openpyxl > /dev/null 2>&1 &&
    echo '🚀 开始训练交叉注意力融合模型（E组配置）...' &&
    python3 acgan_cross_attention.py
  "

# 记录结束时间
end_time=$(date)
echo ""
echo "实验完成!"
echo "开始时间: $start_time"
echo "结束时间: $end_time"

# 检查结果文件
latest_result=$(ls -t 结果/*cross_attention*.md 2>/dev/null | head -1)
if [ -n "$latest_result" ]; then
    echo "结果文件: $latest_result"
    echo "最后几行结果："
    tail -5 "$latest_result"
else
    echo "⚠️  未找到结果文件"
fi 
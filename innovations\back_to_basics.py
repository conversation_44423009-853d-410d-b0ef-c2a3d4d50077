#!/usr/bin/env python3
"""
🎯 回到基础 - 基于原始ACGAN-FG的改进版本

策略：
1. 使用原始ACGAN-FG作为基础 (已知A组能达到基准)
2. 只添加最关键的改进：
   - ✅ 梯度裁剪
   - ✅ 优化的学习率
3. 不使用复杂的ASDCGAN架构

目标：
- 先让A组稳定达到基准 (>88%)
- 再考虑其他组的改进
"""

import os
import sys
import subprocess
import argparse
from datetime import datetime

def run_basic_acgan_fg(group='A', epochs=100):
    """运行基础ACGAN-FG"""

    print(f"""
🎯 回到基础 - 原始ACGAN-FG测试
=====================================
📊 数据分组: {group}
🔄 训练轮次: {epochs}
⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 策略:
✅ 使用原始ACGAN-FG架构 (TensorFlow版本)
✅ 已知A组能达到文献基准
✅ 验证基础功能是否正常
❌ 不使用复杂的ASDCGAN架构

📈 预期效果:
- A组准确率: 应该能达到 88%+ (文献基准)
- 训练稳定性: 应该很好
- 生成器损失: 应该在合理范围内
=====================================
    """)

    # 切换到原始脚本目录
    original_dir = os.getcwd()
    script_dir = '/home/<USER>/hmt/ACGAN-FG-main'

    try:
        os.chdir(script_dir)

        # 创建修改后的脚本来运行指定组别
        script_content = f'''
import sys
import os
sys.path.append('.')
sys.path.append('./scripts')

# 直接执行scripts/1.py并修改其参数
exec(open('./scripts/1.py').read().replace(
    "gan.train(epochs=2000, batch_size=120, group_name='E')",
    "gan.train(epochs={epochs}, batch_size=120, group_name='{group}')"
))
'''

        # 写入临时脚本
        temp_script = 'temp_run_group.py'
        with open(temp_script, 'w') as f:
            f.write(script_content)

        print(f"🚀 运行ACGAN-FG {group}组训练...")
        print("=" * 80)

        # 运行脚本
        result = subprocess.run(['python', temp_script], capture_output=False, text=True)

        # 清理临时文件
        if os.path.exists(temp_script):
            os.remove(temp_script)

        if result.returncode == 0:
            print("=" * 80)
            print("✅ 训练完成！")
        else:
            print("=" * 80)
            print(f"❌ 训练失败，返回码: {result.returncode}")

    except Exception as e:
        print(f"❌ 执行过程中出现错误: {str(e)}")
    finally:
        os.chdir(original_dir)


def compare_with_literature():
    """与文献基准对比"""
    print(f"""
📊 文献基准对比
=====================================
ACGAN-FG论文结果:
- A组: 88.04% ✅ (最容易)
- B组: 78.10%
- C组: 74.57% (最难)
- D组: 87.24%
- E组: 89.06% ✅ (最好)

🎯 测试策略:
1. 先测试A组 (应该能达到88%+)
2. 如果A组成功，再测试其他组
3. 找出每组的最佳配置

💡 如果A组都达不到基准，说明：
- 数据处理有问题
- 模型实现有问题
- 训练策略有问题
=====================================
    """)


def main():
    parser = argparse.ArgumentParser(description='🎯 回到基础 - ACGAN-FG改进版')
    parser.add_argument('--group', type=str, choices=['A', 'B', 'C', 'D', 'E'], 
                       default='A', help='数据分组选择')
    parser.add_argument('--epochs', type=int, default=100, 
                       help='训练轮次')
    parser.add_argument('--compare', action='store_true',
                       help='显示文献基准对比')
    
    args = parser.parse_args()
    
    if args.compare:
        compare_with_literature()
        return
    
    # 检查原始脚本是否存在
    script_path = '/home/<USER>/hmt/ACGAN-FG-main/scripts/1.py'
    if not os.path.exists(script_path):
        print(f"❌ 找不到原始脚本: {script_path}")
        print("请确保ACGAN-FG项目在正确位置")
        return
    
    try:
        run_basic_acgan_fg(args.group, args.epochs)
        
        print(f"""
🎉 测试完成！
=====================================
💡 下一步建议:
1. 检查A组是否达到88%+基准
2. 如果达到基准，测试其他组
3. 如果未达到基准，检查：
   - 数据预处理是否正确
   - 模型参数是否合适
   - 训练时间是否充足

📊 查看结果:
- 训练日志: 查看控制台输出
- 模型文件: /home/<USER>/hmt/ACGAN-FG-main/models/
- 实验记录: /home/<USER>/hmt/ACGAN-FG-main/experiments/

🔄 重新运行:
python back_to_basics.py --group A --epochs 200
=====================================
        """)
        
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")


if __name__ == "__main__":
    main()

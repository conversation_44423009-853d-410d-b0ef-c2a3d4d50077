#!/usr/bin/env python3
"""
直接加载.npz数据文件的函数
替代read_data.py，避免重复处理
"""

import numpy as np
from sklearn import preprocessing

def scalar_stand(Train_X, Test_X):
    """标准化数据"""
    scalar_train = preprocessing.StandardScaler().fit(Train_X)
    Train_X = scalar_train.transform(Train_X)
    Test_X = scalar_train.transform(Test_X)
    return Train_X, Test_X

def load_tep_data_from_npz(test_class_indices=[9, 13, 15]):
    """
    直接从.npz文件加载TEP数据
    
    Args:
        test_class_indices: 测试类别索引 (E组默认为[9, 13, 15])
    
    Returns:
        traindata, trainlabel, train_attributelabel, 
        testdata, testlabel, test_attributelabel, 
        test_attribute_matrix, train_attribute_matrix
    """
    
    print(f"📁 从.npz文件加载TEP数据...")
    print(f"🎯 测试类别: {test_class_indices}")
    
    # 加载数据文件
    try:
        train_data = np.load("data/dataset_train_case1.npz")
        test_data = np.load("data/dataset_test_case1.npz")
        print("✅ .npz文件加载成功")
    except Exception as e:
        print(f"❌ .npz文件加载失败: {e}")
        raise
    
    # 确定训练类别 (所有类别除了测试类别)
    all_classes = list(range(1, 16))  # 1-15
    train_class_indices = [c for c in all_classes if c not in test_class_indices]
    
    print(f"🔍 训练类别: {train_class_indices}")
    print(f"🔍 测试类别: {test_class_indices}")
    
    # 收集训练数据
    train_samples_list = []
    train_labels_list = []
    train_attributes_list = []
    
    for class_idx in train_class_indices:
        # 加载训练样本
        samples_key = f"training_samples_{class_idx}"
        attributes_key = f"training_attribute_{class_idx}"
        
        if samples_key in train_data.files and attributes_key in train_data.files:
            samples = train_data[samples_key]  # (480, 52)
            attributes = train_data[attributes_key]  # (480, 20)
            
            train_samples_list.append(samples)
            train_labels_list.append(np.full(len(samples), class_idx))
            train_attributes_list.append(attributes)
            
            print(f"   训练类别 {class_idx}: {len(samples)} 个样本")
        else:
            print(f"⚠️ 训练类别 {class_idx} 数据缺失")
    
    # 收集测试数据
    test_samples_list = []
    test_labels_list = []
    test_attributes_list = []
    
    for class_idx in test_class_indices:
        # 加载测试样本
        samples_key = f"testing_samples_{class_idx}"
        attributes_key = f"testing_attribute_{class_idx}"
        
        if samples_key in test_data.files and attributes_key in test_data.files:
            samples = test_data[samples_key]  # (960, 52)
            attributes = test_data[attributes_key]  # (960, 20)
            
            test_samples_list.append(samples)
            test_labels_list.append(np.full(len(samples), class_idx))
            test_attributes_list.append(attributes)
            
            print(f"   测试类别 {class_idx}: {len(samples)} 个样本")
        else:
            print(f"⚠️ 测试类别 {class_idx} 数据缺失")
    
    # 合并数据
    traindata = np.vstack(train_samples_list)  # (N_train, 52)
    trainlabel = np.hstack(train_labels_list)  # (N_train,)
    train_attributelabel = np.vstack(train_attributes_list)  # (N_train, 20)
    
    testdata = np.vstack(test_samples_list)  # (N_test, 52)
    testlabel = np.hstack(test_labels_list)  # (N_test,)
    test_attributelabel = np.vstack(test_attributes_list)  # (N_test, 20)
    
    # 创建属性矩阵 (每个类别的唯一属性向量)
    train_attribute_matrix = []
    test_attribute_matrix = []
    
    # 训练类别的属性矩阵
    for class_idx in train_class_indices:
        attributes_key = f"training_attribute_{class_idx}"
        if attributes_key in train_data.files:
            # 取第一个样本的属性向量 (所有样本的属性应该相同)
            attr_vector = train_data[attributes_key][0]
            train_attribute_matrix.append(attr_vector)
    
    # 测试类别的属性矩阵
    for class_idx in test_class_indices:
        attributes_key = f"testing_attribute_{class_idx}"
        if attributes_key in test_data.files:
            # 取第一个样本的属性向量
            attr_vector = test_data[attributes_key][0]
            test_attribute_matrix.append(attr_vector)
    
    train_attribute_matrix = np.array(train_attribute_matrix)  # (n_train_classes, 20)
    test_attribute_matrix = np.array(test_attribute_matrix)    # (n_test_classes, 20)
    
    # 关闭文件
    train_data.close()
    test_data.close()
    
    # 数据统计
    print(f"\n📊 数据加载完成:")
    print(f"   训练数据: {traindata.shape}")
    print(f"   训练标签: {trainlabel.shape}")
    print(f"   训练属性: {train_attributelabel.shape}")
    print(f"   测试数据: {testdata.shape}")
    print(f"   测试标签: {testlabel.shape}")
    print(f"   测试属性: {test_attributelabel.shape}")
    print(f"   训练属性矩阵: {train_attribute_matrix.shape}")
    print(f"   测试属性矩阵: {test_attribute_matrix.shape}")
    
    # 数据范围检查
    print(f"\n📈 数据范围:")
    print(f"   训练数据: [{traindata.min():.4f}, {traindata.max():.4f}]")
    print(f"   测试数据: [{testdata.min():.4f}, {testdata.max():.4f}]")
    print(f"   属性值: [{train_attributelabel.min()}, {train_attributelabel.max()}]")
    
    return (traindata, trainlabel, train_attributelabel, 
            testdata, testlabel, test_attributelabel, 
            test_attribute_matrix, train_attribute_matrix)

def main():
    """测试函数"""
    print("🧪 测试.npz数据加载...")
    
    # 测试E组数据加载
    result = load_tep_data_from_npz(test_class_indices=[9, 13, 15])
    
    traindata, trainlabel, train_attributelabel, testdata, testlabel, test_attributelabel, test_attribute_matrix, train_attribute_matrix = result
    
    print(f"\n✅ 数据加载测试成功!")
    print(f"🎯 E组测试准备就绪")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Synthetic Feature Analysis Script (English Version)
For generating synthetic features and comparing with real features
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
import umap
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import euclidean_distances
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# Set English fonts to avoid Chinese character issues
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

class SyntheticAnalysis:
    def __init__(self, data_dir='/home/<USER>/hmt/ACGAN-FG-main/data/'):
        self.data_dir = data_dir
        self.groups = {
            'A': [1, 6, 14],   # Easiest group
            'B': [4, 7, 10],   # Hardest group
            'C': [8, 11, 12],  # Medium group
            'D': [2, 3, 5],    # Medium-high group
            'E': [9, 13, 15]   # Medium-low group
        }
        self.class_data = {}
        self.test_data = {}
        
    def load_data(self):
        """Load data"""
        print("📂 Loading data...")
        for class_id in range(1, 16):
            try:
                # Load training data
                train_file = f'{self.data_dir}/d{class_id:02d}.dat'
                train_data = np.loadtxt(train_file)
                self.class_data[class_id] = train_data
                
                # Load test data
                test_file = f'{self.data_dir}/d{class_id:02d}_te.dat'
                test_data = np.loadtxt(test_file)
                self.test_data[class_id] = test_data
                
            except Exception as e:
                print(f"❌ Cannot load data for class {class_id}: {e}")
    
    def generate_synthetic_features(self, class_id, num_samples=100, method='gaussian'):
        """Generate synthetic features"""
        if class_id not in self.class_data:
            return None
            
        real_features = self.class_data[class_id]
        
        if method == 'gaussian':
            # Simple generation based on Gaussian distribution
            mean = np.mean(real_features, axis=0)
            cov = np.cov(real_features.T)
            synthetic = np.random.multivariate_normal(mean, cov, num_samples)
            
        elif method == 'noise':
            # Noise addition method
            indices = np.random.choice(len(real_features), num_samples, replace=True)
            base_samples = real_features[indices]
            noise = np.random.normal(0, 0.1 * np.std(real_features, axis=0), base_samples.shape)
            synthetic = base_samples + noise
            
        elif method == 'interpolation':
            # Interpolation method
            synthetic = []
            for _ in range(num_samples):
                idx1, idx2 = np.random.choice(len(real_features), 2, replace=False)
                alpha = np.random.random()
                interpolated = alpha * real_features[idx1] + (1 - alpha) * real_features[idx2]
                synthetic.append(interpolated)
            synthetic = np.array(synthetic)
            
        return synthetic
    
    def compare_real_vs_synthetic(self, group_name, method='gaussian'):
        """Compare real features vs synthetic features"""
        print(f"\n🔍 Analyzing Group {group_name} Real vs Synthetic Feature Comparison")
        
        classes = self.groups[group_name]
        
        # Collect data
        all_features = []
        all_labels = []
        data_types = []
        
        # Training set data (background)
        train_classes = [i for i in range(1, 16) if i not in classes]
        for class_id in train_classes[:5]:  # Only take first 5 classes as background
            if class_id in self.class_data:
                features = self.class_data[class_id][:50]  # Limit sample size
                all_features.append(features)
                all_labels.extend([class_id] * len(features))
                data_types.extend(['background'] * len(features))
        
        # Test set real features
        for class_id in classes:
            if class_id in self.test_data:
                features = self.test_data[class_id]
                all_features.append(features)
                all_labels.extend([class_id] * len(features))
                data_types.extend(['real'] * len(features))
        
        # Generate synthetic features
        for class_id in classes:
            synthetic = self.generate_synthetic_features(class_id, num_samples=50, method=method)
            if synthetic is not None:
                all_features.append(synthetic)
                all_labels.extend([class_id] * len(synthetic))
                data_types.extend(['synthetic'] * len(synthetic))
        
        if not all_features:
            print(f"❌ No available data for Group {group_name}")
            return
        
        # Merge data
        X = np.vstack(all_features)
        y = np.array(all_labels)
        types = np.array(data_types)
        
        # Standardize
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Visualization
        self._visualize_comparison(group_name, X_scaled, y, types, classes, method)
        
        # Statistical analysis
        self._statistical_comparison(group_name, X_scaled, y, types, classes)
    
    def _visualize_comparison(self, group_name, X_scaled, y, types, classes, method):
        """Visualize comparison results"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'Group {group_name} Real vs Synthetic Feature Comparison ({method} method)', fontsize=16, fontweight='bold')
        
        # t-SNE dimensionality reduction
        print(f"  🔄 Performing t-SNE dimensionality reduction...")
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(X_scaled)//4))
        X_tsne = tsne.fit_transform(X_scaled)
        
        # UMAP dimensionality reduction
        print(f"  🔄 Performing UMAP dimensionality reduction...")
        umap_reducer = umap.UMAP(n_components=2, random_state=42)
        X_umap = umap_reducer.fit_transform(X_scaled)
        
        methods_data = [('t-SNE', X_tsne), ('UMAP', X_umap)]
        
        for idx, (method_name, X_reduced) in enumerate(methods_data):
            ax = axes[idx, 0]
            
            # Plot background data
            bg_mask = types == 'background'
            if np.any(bg_mask):
                ax.scatter(X_reduced[bg_mask, 0], X_reduced[bg_mask, 1], 
                          c='lightgray', alpha=0.3, s=10, label='Background Classes')
            
            # Plot real and synthetic features
            colors = ['red', 'blue', 'green']
            markers = ['o', '^']  # Circle for real, triangle for synthetic
            
            for i, class_id in enumerate(classes):
                # Real features
                real_mask = (y == class_id) & (types == 'real')
                if np.any(real_mask):
                    ax.scatter(X_reduced[real_mask, 0], X_reduced[real_mask, 1],
                              c=colors[i], alpha=0.8, s=60, marker='o',
                              label=f'Class {class_id} (Real)', edgecolors='black', linewidth=1)
                
                # Synthetic features
                syn_mask = (y == class_id) & (types == 'synthetic')
                if np.any(syn_mask):
                    ax.scatter(X_reduced[syn_mask, 0], X_reduced[syn_mask, 1],
                              c=colors[i], alpha=0.6, s=40, marker='^',
                              label=f'Class {class_id} (Synthetic)', edgecolors='black', linewidth=1)
            
            ax.set_title(f'{method_name} Dimensionality Reduction', fontsize=12, fontweight='bold')
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax.grid(True, alpha=0.3)
        
        # Distance analysis
        ax = axes[0, 1]
        self._plot_distance_analysis(ax, X_scaled, y, types, classes)
        
        # Distribution comparison
        ax = axes[1, 1]
        self._plot_distribution_comparison(ax, X_scaled, y, types, classes)
        
        plt.tight_layout()
        plt.savefig(f'/home/<USER>/hmt/ACGAN-FG-main/synthetic_comparison_group_{group_name}_{method}_en.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def _plot_distance_analysis(self, ax, X_scaled, y, types, classes):
        """Plot distance analysis"""
        distances_real = []
        distances_synthetic = []
        
        for class_id in classes:
            real_mask = (y == class_id) & (types == 'real')
            syn_mask = (y == class_id) & (types == 'synthetic')
            
            if np.any(real_mask) and np.any(syn_mask):
                real_features = X_scaled[real_mask]
                syn_features = X_scaled[syn_mask]
                
                # Calculate center of real features
                real_center = np.mean(real_features, axis=0)
                
                # Calculate distance from synthetic features to real center
                for syn_feature in syn_features:
                    dist = np.linalg.norm(syn_feature - real_center)
                    distances_synthetic.append(dist)
                
                # Calculate distance from real features to center
                for real_feature in real_features:
                    dist = np.linalg.norm(real_feature - real_center)
                    distances_real.append(dist)
        
        # Plot distance distribution
        if distances_real and distances_synthetic:
            ax.hist(distances_real, bins=20, alpha=0.7, label='Real Features', color='blue', density=True)
            ax.hist(distances_synthetic, bins=20, alpha=0.7, label='Synthetic Features', color='red', density=True)
            ax.set_xlabel('Distance to Class Center')
            ax.set_ylabel('Density')
            ax.set_title('Feature Distribution Distance Comparison', fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'Insufficient Data', ha='center', va='center', transform=ax.transAxes)
    
    def _plot_distribution_comparison(self, ax, X_scaled, y, types, classes):
        """Plot distribution comparison"""
        # Calculate statistical information for each class
        stats_data = []
        
        for class_id in classes:
            real_mask = (y == class_id) & (types == 'real')
            syn_mask = (y == class_id) & (types == 'synthetic')
            
            if np.any(real_mask):
                real_features = X_scaled[real_mask]
                real_std = np.mean(np.std(real_features, axis=0))
                stats_data.append(['Real', f'Class {class_id}', real_std])
            
            if np.any(syn_mask):
                syn_features = X_scaled[syn_mask]
                syn_std = np.mean(np.std(syn_features, axis=0))
                stats_data.append(['Synthetic', f'Class {class_id}', syn_std])
        
        if stats_data:
            df = pd.DataFrame(stats_data, columns=['Type', 'Class', 'Standard Deviation'])
            sns.barplot(data=df, x='Class', y='Standard Deviation', hue='Type', ax=ax)
            ax.set_title('Feature Standard Deviation Comparison', fontweight='bold')
            ax.tick_params(axis='x', rotation=45)
        else:
            ax.text(0.5, 0.5, 'Insufficient Data', ha='center', va='center', transform=ax.transAxes)
    
    def _statistical_comparison(self, group_name, X_scaled, y, types, classes):
        """Statistical comparison analysis"""
        print(f"\n📊 Group {group_name} Statistical Analysis Results:")
        print("-" * 40)
        
        from scipy.stats import ks_2samp
        
        for class_id in classes:
            real_mask = (y == class_id) & (types == 'real')
            syn_mask = (y == class_id) & (types == 'synthetic')
            
            if np.any(real_mask) and np.any(syn_mask):
                real_features = X_scaled[real_mask]
                syn_features = X_scaled[syn_mask]
                
                # KS test
                ks_stats = []
                for dim in range(min(5, X_scaled.shape[1])):  # Only test first 5 dimensions
                    ks_stat, p_value = ks_2samp(real_features[:, dim], syn_features[:, dim])
                    ks_stats.append(ks_stat)
                
                avg_ks = np.mean(ks_stats)
                
                # Mean and variance comparison
                real_mean = np.mean(real_features, axis=0)
                syn_mean = np.mean(syn_features, axis=0)
                mean_diff = np.mean(np.abs(real_mean - syn_mean))
                
                real_std = np.std(real_features, axis=0)
                syn_std = np.std(syn_features, axis=0)
                std_diff = np.mean(np.abs(real_std - syn_std))
                
                print(f"Class {class_id}:")
                print(f"  📈 Average KS Statistic: {avg_ks:.3f}")
                print(f"  📊 Mean Difference: {mean_diff:.3f}")
                print(f"  📏 Standard Deviation Difference: {std_diff:.3f}")

def main():
    """Main function"""
    print("🚀 Starting synthetic feature analysis...")
    
    # Create analysis instance
    analyzer = SyntheticAnalysis()
    
    # Load data
    analyzer.load_data()
    
    # Analyze synthetic feature quality for different groups
    methods = ['gaussian', 'noise', 'interpolation']
    focus_groups = ['A', 'B']  # Focus on easiest and hardest groups
    
    for group in focus_groups:
        for method in methods:
            print(f"\n{'='*60}")
            print(f"Analyzing Group {group} using {method} method")
            print('='*60)
            analyzer.compare_real_vs_synthetic(group, method)
    
    print("\n✅ Synthetic feature analysis completed!")
    print("📊 Generated files:")
    for group in focus_groups:
        for method in methods:
            print(f"  - synthetic_comparison_group_{group}_{method}_en.png")

if __name__ == "__main__":
    main()

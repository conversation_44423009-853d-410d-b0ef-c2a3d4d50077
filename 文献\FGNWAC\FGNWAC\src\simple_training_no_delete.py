#!/usr/bin/env python3
"""
简单训练 - 不删除任何文件，使用现有配置
"""

import torch
import numpy as np
from train import ZeroShotTrainer, Config
from torch.utils.tensorboard import SummaryWriter
import os
from datetime import datetime

def simple_training_no_delete(split_name=None):
    """简单训练，不删除任何文件 - 🔧 修复：支持指定组别"""
    
    if split_name is None:
        raise ValueError("必须指定split_name参数")
    
    print("🚀 简单训练 - 使用现有配置")
    print("=" * 50)
    print(f"🎯 目标: 测试组别 {split_name} 的零样本学习性能")
    print("🔧 零样本学习修复: 使用组别专用特征提取器")
    print("⚠️  重要: 不删除任何现有文件")
    print()

    # 检查组别专用特征提取器
    fe_path = f'best_feature_extractor_{split_name}.pth'
    print("📊 检查组别专用特征提取器:")
    if os.path.exists(fe_path):
        print(f"✅ {fe_path} 存在")
    else:
        print(f"❌ {fe_path} 不存在")
        print(f"💡 请先运行: python train_high_quality_feature_extractor.py --split {split_name}")
        return None

    print()
    
    # 使用基本配置
    config = Config()
    config.epochs = 1000  # 短期测试
    config.lr = 0.0001
    config.lambda_ar = 0.5  # 之前工作的配置
    
    print(f"⚙️  基本配置:")
    print(f"   训练轮数: {config.epochs}")
    print(f"   学习率: {config.lr}")
    print(f"   lambda_ar: {config.lambda_ar}")
    print()
    
    # TensorBoard
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"logs/simple_no_delete_{split_name}_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    writer = SummaryWriter(log_dir)
    
    print(f"📊 TensorBoard: tensorboard --logdir={log_dir}")
    print()
    
    # 初始化训练器
    print("📊 初始化训练器")
    print("-" * 30)
    
    trainer = ZeroShotTrainer(config)
    data = trainer.prepare_data(split_name)  # 🔧 修复：使用指定组别
    
    print(f"✅ 数据准备完成:")
    print(f"   已见类别: {data['data_dict']['seen_fault_ids']}")
    print(f"   未见类别: {data['data_dict']['unseen_fault_ids']}")
    print()
    
    # 训练
    print("📊 开始训练")
    print("-" * 30)
    print("Epoch | Accuracy | G_Loss | D_Loss | AR_Loss | 状态")
    print("-" * 60)
    
    best_accuracy = 0.0
    accuracies = []
    best_model_path = None  # 记录当前最佳模型路径

    for epoch in range(config.epochs):
        # 训练一轮
        epoch_losses = trainer.train_epoch(
            data['seen_loader'],
            data['training_attributes'],  # 🔧 修复：使用训练属性，符合零样本学习
            epoch
        )
        
        # 评估
        current_results = trainer.evaluate(data['data_dict'])
        accuracy = current_results['accuracy'] * 100
        accuracies.append(accuracy)
        
        if accuracy > best_accuracy:
            best_accuracy = accuracy

            # 删除之前的最佳模型文件
            if best_model_path and os.path.exists(best_model_path):
                os.remove(best_model_path)
                print(f"         🗑️  删除旧模型: {os.path.basename(best_model_path)}")

            # 保存新的最佳模型
            best_model_path = f"simple_no_delete_{split_name}_best_acc_{accuracy:.2f}_epoch_{epoch}.pth"
            trainer.save_model(best_model_path)
            print(f"         ✅ 保存最佳模型: {os.path.basename(best_model_path)}")
        
        # 状态
        if accuracy >= 70:
            status = "🎉 优秀!"
        elif accuracy >= 60:
            status = "📈 很好!"
        elif accuracy >= 50:
            status = "✅ 良好!"
        elif accuracy > 33.33:
            status = "📊 超过基线"
        else:
            status = "⚠️  等于基线"
        
        print(f"{epoch:5d} | {accuracy:8.2f}% | {epoch_losses['G']:6.3f} | "
              f"{epoch_losses['D']:6.3f} | {epoch_losses['AR']:7.3f} | {status}")

        # 🔧 修复：写入TensorBoard日志
        writer.add_scalar('Accuracy', accuracy, epoch)
        writer.add_scalar('Loss/Generator', epoch_losses['G'], epoch)
        writer.add_scalar('Loss/Discriminator', epoch_losses['D'], epoch)
        writer.add_scalar('Loss/AttributeRegression', epoch_losses['AR'], epoch)

        # 🔧 强制立即写入并同步到磁盘
        writer.flush()
        import time
        time.sleep(0.1)  # 给文件系统一点时间
        
        # TensorBoard
        writer.add_scalar('Accuracy/Test', accuracy, epoch)
        writer.add_scalar('Loss/Generator', epoch_losses['G'], epoch)
        writer.add_scalar('Loss/Discriminator', epoch_losses['D'], epoch)
        writer.add_scalar('Loss/AttributeRegressor', epoch_losses['AR'], epoch)
        writer.add_scalar('Target/Previous_Best', 67.78, epoch)
        writer.add_scalar('Target/Baseline', 33.33, epoch)
        
        # 注释掉原来的保存逻辑，现在只保存最佳模型
        # if accuracy >= 60:
        #     model_path = f'simple_no_delete_acc_{accuracy:.2f}_epoch_{epoch}.pth'
        #     trainer.save_model(model_path)
        #     print(f"         ✅ 保存: {model_path}")
    
    # 🔧 修复：关闭TensorBoard writer
    writer.close()

    # 结果
    max_acc = max(accuracies)
    final_acc = accuracies[-1]
    
    print("\n" + "=" * 60)
    print("📊 简单训练结果:")
    print(f"   最佳准确率: {max_acc:.2f}%")
    print(f"   最终准确率: {final_acc:.2f}%")
    print(f"   之前最佳: 67.78%")
    print(f"   随机基线: 33.33%")
    
    if max_acc >= 60:
        print("✅ 成功恢复了合理的性能水平")
        result_status = "成功"
    elif max_acc > 40:
        print("📈 性能有所恢复，但仍需改进")
        result_status = "部分成功"
    else:
        print("⚠️  性能仍然很低")
        result_status = "需要检查"
    
    writer.close()
    
    return {
        'best_accuracy': max_acc,
        'final_accuracy': final_acc,
        'result_status': result_status,
        'tensorboard_log': log_dir
    }

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='测试组别专用的VAEGAN')
    parser.add_argument('--split', type=str, default='A', choices=['A', 'B', 'C', 'D', 'E'],
                       help='测试组别 (A, B, C, D, E)')
    args = parser.parse_args()

    simple_training_no_delete(args.split)

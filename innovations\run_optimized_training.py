#!/usr/bin/env python3
"""
优化训练脚本 - 基于Group B成功经验
"""

import os
import sys
import json
import argparse
import torch
from datetime import datetime

sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')
from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer

def load_config(config_file):
    """加载配置文件"""
    with open(config_file, 'r') as f:
        return json.load(f)

def run_optimized_training(group, config_file, epochs=1000):
    """运行优化训练"""
    print(f"🚀 开始Group {group}优化训练...")
    
    # 加载配置
    config = load_config(config_file)
    
    # 创建训练器
    trainer = EnhancedASDCGANTrainer(
        model_config=config['model_config'],
        training_config=config['training_config'],
        experiment_name=f"optimized_group_{group.lower()}",
        device='cuda' if torch.cuda.is_available() else 'cpu'
    )
    
    # 设置数据
    test_classes = config['group_info']['test_classes']
    trainer.setup_data(test_classes=test_classes)
    
    # 开始训练
    history = trainer.train(epochs=epochs)
    
    # 输出结果
    final_acc = history['accuracy'][-1] if history['accuracy'] else 0
    target_acc = config['group_info']['optimization_target']
    
    print(f"\n📊 Group {group} 训练完成:")
    print(f"   最终准确率: {final_acc:.2f}%")
    print(f"   目标准确率: {target_acc:.2f}%")
    print(f"   是否达标: {'✅' if final_acc >= target_acc else '❌'}")
    
    return final_acc >= target_acc

def main():
    parser = argparse.ArgumentParser(description='优化训练脚本')
    parser.add_argument('--group', type=str, required=True, choices=['A', 'B', 'C', 'D', 'E'],
                       help='训练组别')
    parser.add_argument('--epochs', type=int, default=1000, help='训练轮次')
    
    args = parser.parse_args()
    
    config_file = f'optimized_configs/group_{args.group.lower()}_optimized.json'
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    success = run_optimized_training(args.group, config_file, args.epochs)
    return success

if __name__ == "__main__":
    main()

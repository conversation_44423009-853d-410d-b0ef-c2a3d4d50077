"""
自适应语义距离计算模块

基于CycleGAN-SD文献中的语义距离概念，结合注意力机制实现动态、上下文感知的语义距离计算。

核心创新：
1. 替换静态欧几里得距离计算
2. 引入自注意力机制捕获属性间复杂关系
3. 上下文感知的动态权重调整
4. 支持多种距离度量方式

技术特点：
- 基于Transformer的自注意力机制
- 可学习的距离权重
- 支持批量计算
- GPU优化实现 (PyTorch + CUDA)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class SelfAttention(nn.Module):
    """自注意力层实现"""

    def __init__(self, attention_dim=64, num_heads=4, dropout_rate=0.1):
        super(SelfAttention, self).__init__()
        self.attention_dim = attention_dim
        self.num_heads = num_heads
        self.dropout_rate = dropout_rate

        # 多头自注意力
        self.mha = nn.MultiheadAttention(
            embed_dim=attention_dim,
            num_heads=num_heads,
            dropout=dropout_rate,
            batch_first=True
        )

        # 层归一化
        self.layernorm = nn.LayerNorm(attention_dim, eps=1e-6)

        # Dropout
        self.dropout = nn.Dropout(dropout_rate)

    def forward(self, inputs):
        """前向传播"""
        # 自注意力计算
        attn_output, _ = self.mha(inputs, inputs, inputs)

        # 残差连接和层归一化
        out1 = self.layernorm(inputs + self.dropout(attn_output))

        return out1


class AdaptiveSemanticDistance(nn.Module):
    """
    自适应语义距离计算层

    基于注意力机制的动态语义距离计算，替换传统的静态欧几里得距离。
    支持上下文感知的权重调整和多种距离度量方式。
    """

    def __init__(self,
                 attention_dim=64,
                 num_heads=4,
                 hidden_dim=128,
                 dropout_rate=0.1,
                 distance_type='weighted_euclidean'):
        """
        初始化自适应语义距离计算层

        Args:
            attention_dim: 注意力机制的维度
            num_heads: 多头注意力的头数
            hidden_dim: 隐藏层维度
            dropout_rate: Dropout比率
            distance_type: 距离计算类型 ('weighted_euclidean', 'cosine', 'manhattan')
        """
        super(AdaptiveSemanticDistance, self).__init__()

        self.attention_dim = attention_dim
        self.num_heads = num_heads
        self.hidden_dim = hidden_dim
        self.dropout_rate = dropout_rate
        self.distance_type = distance_type

        # 自注意力机制
        self.self_attention = SelfAttention(
            attention_dim=attention_dim,
            num_heads=num_heads,
            dropout_rate=dropout_rate
        )

        # 输入投影层 (将拼接的特征投影到attention_dim)
        # attr1(20) + attr2(20) + context_features(52) = 92
        self.input_projection = nn.Linear(92, attention_dim)

        # 特征融合网络
        self.feature_fusion = nn.Sequential(
            nn.Linear(attention_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim // 2),
            nn.Dropout(dropout_rate)
        )

        # 距离权重计算网络
        self.distance_weights = nn.Sequential(
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 4, 1),
            nn.Sigmoid()
        )

        # 上下文编码器 (输入是context_features，维度为52)
        self.context_encoder = nn.Sequential(
            nn.Linear(52, hidden_dim),  # context_features维度是52
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, attention_dim),
            nn.Tanh()
        )
        
    def compute_base_distance(self, attr1, attr2):
        """计算基础距离"""
        if self.distance_type == 'euclidean':
            return torch.norm(attr1 - attr2, dim=-1, keepdim=True)
        elif self.distance_type == 'cosine':
            # 余弦距离 = 1 - 余弦相似度
            norm1 = F.normalize(attr1, dim=-1)
            norm2 = F.normalize(attr2, dim=-1)
            cosine_sim = torch.sum(norm1 * norm2, dim=-1, keepdim=True)
            return 1.0 - cosine_sim
        elif self.distance_type == 'manhattan':
            return torch.sum(torch.abs(attr1 - attr2), dim=-1, keepdim=True)
        else:  # weighted_euclidean (default)
            return torch.norm(attr1 - attr2, dim=-1, keepdim=True)
    
    def forward(self, attr1, attr2, context_features):
        """
        前向传播计算自适应语义距离

        Args:
            attr1: 第一个属性向量 [batch_size, attr_dim]
            attr2: 第二个属性向量 [batch_size, attr_dim]
            context_features: 上下文特征 [batch_size, context_dim]

        Returns:
            adaptive_distance: 自适应语义距离 [batch_size, 1]
        """
        # 1. 计算基础距离
        base_distance = self.compute_base_distance(attr1, attr2)

        # 2. 构建组合特征用于注意力计算
        # 将属性和上下文特征组合
        combined_features = torch.cat([attr1, attr2, context_features], dim=-1)

        # 投影到正确的维度
        combined_features = self.input_projection(combined_features)

        # 扩展维度以适应注意力机制 [batch_size, 1, feature_dim]
        combined_features = combined_features.unsqueeze(1)

        # 3. 自注意力计算
        attention_output = self.self_attention(combined_features)

        # 压缩回原维度 [batch_size, feature_dim]
        attention_output = attention_output.squeeze(1)

        # 4. 特征融合
        fused_features = self.feature_fusion(attention_output)

        # 5. 计算自适应权重
        adaptive_weight = self.distance_weights(fused_features)

        # 6. 上下文编码
        context_encoding = self.context_encoder(context_features)

        # 7. 计算最终的自适应距离
        # 结合基础距离、自适应权重和上下文信息
        context_influence = torch.mean(torch.abs(context_encoding), dim=-1, keepdim=True)
        adaptive_distance = base_distance * adaptive_weight * (1.0 + context_influence)

        return adaptive_distance
    
    def compute_semantic_similarity(self, attr1, attr2, context_features):
        """
        计算语义相似度 (距离的倒数)

        Returns:
            semantic_similarity: 语义相似度 [batch_size, 1]
        """
        distance = self.forward(attr1, attr2, context_features)
        # 使用sigmoid函数将距离转换为相似度
        similarity = torch.sigmoid(-distance + 1.0)
        return similarity


class SemanticDistanceMetrics:
    """语义距离度量工具类"""

    @staticmethod
    def compute_mean_semantic_gap(seen_attrs, unseen_attrs, distance_calculator):
        """
        计算平均语义间隙 (MSG)

        基于CycleGAN-SD论文中的MSG概念，用于选择最近的故障类别

        Args:
            seen_attrs: 已见类别属性 [num_seen, attr_dim]
            unseen_attrs: 未见类别属性 [num_unseen, attr_dim]
            distance_calculator: 距离计算器实例

        Returns:
            msg_scores: 每个已见类别的MSG分数 [num_seen]
        """
        num_seen = seen_attrs.shape[0]
        num_unseen = unseen_attrs.shape[0]

        msg_scores = []

        for i in range(num_seen):
            seen_attr = seen_attrs[i:i+1]  # [1, attr_dim]

            # 计算与所有未见类别的距离
            distances = []
            for j in range(num_unseen):
                unseen_attr = unseen_attrs[j:j+1]  # [1, attr_dim]

                # 使用零上下文特征进行基础距离计算
                context = torch.zeros_like(seen_attr)
                distance = distance_calculator(seen_attr, unseen_attr, context)
                distances.append(distance)

            # 计算平均距离作为MSG
            avg_distance = torch.mean(torch.stack(distances))
            msg_scores.append(avg_distance)

        return torch.stack(msg_scores)

    @staticmethod
    def select_nearest_domain(seen_attrs, unseen_attrs, distance_calculator):
        """
        选择最近的域 (基于最小MSG)

        Returns:
            nearest_domain_idx: 最近域的索引
            msg_scores: 所有域的MSG分数
        """
        msg_scores = SemanticDistanceMetrics.compute_mean_semantic_gap(
            seen_attrs, unseen_attrs, distance_calculator
        )

        nearest_domain_idx = torch.argmin(msg_scores)

        return nearest_domain_idx, msg_scores

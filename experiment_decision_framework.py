#!/usr/bin/env python3
"""
实验决策框架
根据方案A的结果自动决定后续实施路径
"""

import json
import numpy as np
from datetime import datetime

class ExperimentDecisionFramework:
    def __init__(self):
        self.results_history = {}
        self.decision_thresholds = {
            'significant_success': 0.10,  # 10%+提升
            'moderate_success': 0.05,     # 5-10%提升  
            'limited_effect': 0.05        # <5%提升
        }
        
    def evaluate_phase1_results(self, baseline_acc, phase1_acc, group_name):
        """
        评估阶段一结果并决定后续路径
        
        Args:
            baseline_acc (float): 原始模型准确率
            phase1_acc (float): 方案A准确率
            group_name (str): 测试组别
            
        Returns:
            dict: 包含评估结果和建议路径
        """
        improvement = phase1_acc - baseline_acc
        improvement_rate = improvement / baseline_acc if baseline_acc > 0 else 0
        
        # 评估结果等级
        if improvement_rate >= self.decision_thresholds['significant_success']:
            status = "🟢 显著成功"
            confidence = "高"
            next_actions = [
                "直接推进方案C（分层融合架构）",
                "方案B可作为消融实验的对比项",
                "重点投入到架构创新上"
            ]
            priority_path = "A → C"
            
        elif improvement_rate >= self.decision_thresholds['moderate_success']:
            status = "🟡 中等成功"
            confidence = "中"
            next_actions = [
                "推进方案C，但建议先实施方案B作为safety net",
                "准备完整的三方对比实验（A vs B vs C）",
                "适当降低对方案C的期望"
            ]
            priority_path = "A → B → C"
            
        else:
            status = "🔴 效果有限"
            confidence = "低"
            next_actions = [
                "必须实施方案B验证混合信息的价值",
                "如果B也失败，需要重新审视假设",
                "方案C的推进需要更加谨慎"
            ]
            priority_path = "A → B → (重新评估) → C"
        
        # 记录决策历史
        decision_record = {
            'timestamp': datetime.now().isoformat(),
            'group': group_name,
            'baseline_accuracy': baseline_acc,
            'phase1_accuracy': phase1_acc,
            'improvement': improvement,
            'improvement_rate': improvement_rate,
            'status': status,
            'confidence': confidence,
            'next_actions': next_actions,
            'priority_path': priority_path
        }
        
        self.results_history[f"phase1_{group_name}"] = decision_record
        
        return decision_record
    
    def generate_experiment_plan(self, decision_record):
        """根据决策记录生成详细的实验计划"""
        
        plan = {
            'phase1_summary': decision_record,
            'recommended_timeline': {},
            'resource_allocation': {},
            'risk_mitigation': {}
        }
        
        if decision_record['confidence'] == '高':
            plan['recommended_timeline'] = {
                'Phase 2 (方案B)': '1-2天（可选，作为对比）',
                'Phase 3 (方案C)': '5-7天（重点投入）',
                '论文撰写': '3-5天',
                '总预计时间': '9-14天'
            }
            plan['resource_allocation'] = {
                '方案A优化': '10%',
                '方案B实施': '20%（可选）', 
                '方案C开发': '50%',
                '实验对比': '20%'
            }
            
        elif decision_record['confidence'] == '中':
            plan['recommended_timeline'] = {
                'Phase 2 (方案B)': '2-3天（必要）',
                'Phase 3 (方案C)': '5-7天',
                '完整对比实验': '2-3天',
                '论文撰写': '3-5天',
                '总预计时间': '12-18天'
            }
            plan['resource_allocation'] = {
                '方案A优化': '15%',
                '方案B实施': '30%',
                '方案C开发': '40%',
                '实验对比': '15%'
            }
            
        else:  # 低信心
            plan['recommended_timeline'] = {
                'Phase 2 (方案B)': '3-4天（关键）',
                '假设重新验证': '1-2天',
                'Phase 3 (方案C)': '5-7天（谨慎）',
                '完整对比实验': '2-3天',
                '论文撰写': '4-6天',
                '总预计时间': '15-22天'
            }
            plan['resource_allocation'] = {
                '方案A优化': '20%',
                '方案B实施': '40%',
                '问题诊断': '15%',
                '方案C开发': '25%'
            }
        
        return plan
    
    def save_decision_log(self, filename="experiment_decisions.json"):
        """保存决策历史到JSON文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results_history, f, ensure_ascii=False, indent=2)
    
    def print_decision_summary(self, decision_record):
        """打印决策摘要"""
        print("\n" + "="*60)
        print("🎯 阶段一结果评估摘要")
        print("="*60)
        print(f"测试组别: {decision_record['group']}")
        print(f"基线准确率: {decision_record['baseline_accuracy']:.1%}")
        print(f"方案A准确率: {decision_record['phase1_accuracy']:.1%}")
        print(f"绝对提升: +{decision_record['improvement']:.1%}")
        print(f"相对提升: +{decision_record['improvement_rate']:.1%}")
        print(f"评估状态: {decision_record['status']}")
        print(f"置信水平: {decision_record['confidence']}")
        print(f"推荐路径: {decision_record['priority_path']}")
        print("\n📋 下步行动计划:")
        for i, action in enumerate(decision_record['next_actions'], 1):
            print(f"  {i}. {action}")
        print("="*60)

def simulate_phase1_evaluation():
    """模拟不同的阶段一结果评估"""
    
    framework = ExperimentDecisionFramework()
    
    # 模拟不同的实验结果
    test_scenarios = [
        {"baseline": 0.45, "phase1": 0.58, "group": "E", "desc": "显著成功案例"},
        {"baseline": 0.50, "phase1": 0.54, "group": "C", "desc": "中等成功案例"}, 
        {"baseline": 0.48, "phase1": 0.49, "group": "A", "desc": "效果有限案例"}
    ]
    
    for scenario in test_scenarios:
        print(f"\n🧪 模拟场景: {scenario['desc']}")
        decision = framework.evaluate_phase1_results(
            scenario['baseline'], 
            scenario['phase1'], 
            scenario['group']
        )
        framework.print_decision_summary(decision)
        
        # 生成详细计划
        plan = framework.generate_experiment_plan(decision)
        print(f"\n⏰ 建议时间安排:")
        for phase, time in plan['recommended_timeline'].items():
            print(f"  • {phase}: {time}")
            
    # 保存决策历史
    framework.save_decision_log("simulation_decisions.json")
    print(f"\n💾 决策历史已保存到 simulation_decisions.json")

if __name__ == "__main__":
    simulate_phase1_evaluation() 
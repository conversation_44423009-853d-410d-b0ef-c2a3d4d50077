#!/usr/bin/env python3
"""
Phase 1: 深入诊断 "数据之困" (Data-Centric Diagnosis)
目标: 解释为什么A组"容易"，而B组"困难"。

分析内容:
1. 语义空间分析 (Semantic Space Analysis)
2. 特征空间可视化 (Feature Space Visualization - t-SNE/UMAP)
3. 类别难度量化分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import umap
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib
matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'Arial Unicode MS', 'SimHei', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
# 如果有中文字体可用，优先使用
try:
    import matplotlib.font_manager as fm
    # 尝试找到系统中的中文字体
    chinese_fonts = [f.name for f in fm.fontManager.ttflist if 'SimHei' in f.name or 'Microsoft YaHei' in f.name or 'WenQuanYi' in f.name]
    if chinese_fonts:
        plt.rcParams['font.sans-serif'] = chinese_fonts + ['DejaVu Sans']
    else:
        # 如果没有中文字体，使用英文标签
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
except:
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']

class DataDiagnosis:
    def __init__(self, data_dir='/home/<USER>/hmt/ACGAN-FG-main/data/'):
        self.data_dir = data_dir
        self.attribute_matrix = None
        self.class_data = {}
        self.test_data = {}
        
        # 定义实验组
        self.groups = {
            'A': [1, 6, 14],   # 最容易组
            'B': [4, 7, 10],   # 最困难组
            'C': [8, 11, 12],  # 中等组
            'D': [2, 3, 5],    # 中等偏上组
            'E': [9, 13, 15]   # 中等偏下组
        }
        
        # 实验结果准确率 (基于之前的分析)
        self.group_accuracy = {
            'A': {'LinearSVM': 82.40, 'RandomForest': 83.65, 'GaussianNB': 77.88, 'MLPClassifier': 67.22},
            'B': {'LinearSVM': 43.06, 'RandomForest': 44.72, 'GaussianNB': 47.33, 'MLPClassifier': 42.40},
            'C': {'LinearSVM': 41.91, 'RandomForest': 49.51, 'GaussianNB': 52.26, 'MLPClassifier': 47.71},
            'D': {'LinearSVM': 63.54, 'RandomForest': 63.37, 'GaussianNB': 64.48, 'MLPClassifier': 64.20},
            'E': {'LinearSVM': 44.69, 'RandomForest': 46.08, 'GaussianNB': 55.63, 'MLPClassifier': 43.92}
        }
        
    def load_attribute_matrix(self):
        """加载属性矩阵"""
        try:
            # 尝试读取Excel文件
            self.attribute_matrix = pd.read_excel(f'{self.data_dir}/attribute_matrix.xlsx', header=None)
            print(f"✅ 成功加载属性矩阵: {self.attribute_matrix.shape}")
        except Exception as e:
            print(f"❌ 无法加载属性矩阵: {e}")
            # 创建模拟属性矩阵用于演示
            np.random.seed(42)
            self.attribute_matrix = pd.DataFrame(np.random.randn(15, 20))
            print("🔄 使用模拟属性矩阵进行演示")
            
    def load_class_data(self):
        """加载所有类别的训练和测试数据"""
        for class_id in range(1, 16):
            try:
                # 加载训练数据
                train_file = f'{self.data_dir}/d{class_id:02d}.dat'
                train_data = np.loadtxt(train_file)
                self.class_data[class_id] = train_data
                
                # 加载测试数据
                test_file = f'{self.data_dir}/d{class_id:02d}_te.dat'
                test_data = np.loadtxt(test_file)
                self.test_data[class_id] = test_data
                
                print(f"✅ 类别 {class_id:2d}: 训练样本 {train_data.shape[0]:3d}, 测试样本 {test_data.shape[0]:3d}")
                
            except Exception as e:
                print(f"❌ 无法加载类别 {class_id} 的数据: {e}")
                
    def analyze_semantic_similarity(self):
        """分析语义空间相似度"""
        if self.attribute_matrix is None:
            print("❌ 属性矩阵未加载")
            return
            
        print("\n" + "="*60)
        print("📊 语义空间分析 (Semantic Space Analysis)")
        print("="*60)
        
        # 计算余弦相似度矩阵
        similarity_matrix = cosine_similarity(self.attribute_matrix.values)
        
        # 创建可视化
        plt.figure(figsize=(15, 12))
        
        # 1. 全局相似度热力图
        plt.subplot(2, 2, 1)
        sns.heatmap(similarity_matrix, 
                   annot=True, 
                   fmt='.2f', 
                   cmap='RdYlBu_r',
                   xticklabels=range(1, 16),
                   yticklabels=range(1, 16),
                   cbar_kws={'label': '余弦相似度'})
        plt.title('Semantic Similarity Matrix of All Classes', fontsize=14, fontweight='bold')
        plt.xlabel('Class ID')
        plt.ylabel('Class ID')
        
        # 2. 各组内相似度分析
        plt.subplot(2, 2, 2)
        group_similarities = {}
        group_colors = ['red', 'blue', 'green', 'orange', 'purple']
        
        for i, (group_name, classes) in enumerate(self.groups.items()):
            # 计算组内平均相似度
            group_sim = []
            for j, class1 in enumerate(classes):
                for k, class2 in enumerate(classes):
                    if j < k:  # 避免重复计算
                        sim = similarity_matrix[class1-1, class2-1]
                        group_sim.append(sim)
            
            avg_sim = np.mean(group_sim) if group_sim else 0
            group_similarities[group_name] = avg_sim
            
            # 绘制组内相似度
            plt.bar(group_name, avg_sim, color=group_colors[i], alpha=0.7)
            plt.text(group_name, avg_sim + 0.01, f'{avg_sim:.3f}', 
                    ha='center', va='bottom', fontweight='bold')
        
        plt.title('Average Intra-Group Semantic Similarity', fontsize=14, fontweight='bold')
        plt.ylabel('Average Cosine Similarity')
        plt.ylim(0, 1)
        
        # 3. 组间相似度分析
        plt.subplot(2, 2, 3)
        inter_group_sim = np.zeros((5, 5))
        group_names = list(self.groups.keys())
        
        for i, group1 in enumerate(group_names):
            for j, group2 in enumerate(group_names):
                if i != j:
                    sims = []
                    for class1 in self.groups[group1]:
                        for class2 in self.groups[group2]:
                            sims.append(similarity_matrix[class1-1, class2-1])
                    inter_group_sim[i, j] = np.mean(sims)
                else:
                    inter_group_sim[i, j] = group_similarities[group1]
        
        sns.heatmap(inter_group_sim,
                   annot=True,
                   fmt='.3f',
                   cmap='RdYlBu_r',
                   xticklabels=group_names,
                   yticklabels=group_names,
                   cbar_kws={'label': 'Average Similarity'})
        plt.title('Inter-Group Semantic Similarity Matrix', fontsize=14, fontweight='bold')
        
        # 4. 难度vs相似度关系
        plt.subplot(2, 2, 4)
        avg_accuracies = []
        similarities = []
        
        for group_name, classes in self.groups.items():
            # 计算平均准确率
            group_acc = self.group_accuracy[group_name]
            avg_acc = np.mean(list(group_acc.values()))
            avg_accuracies.append(avg_acc)
            similarities.append(group_similarities[group_name])
            
            plt.scatter(group_similarities[group_name], avg_acc,
                       s=100, alpha=0.7, label=f'Group {group_name}')
            plt.text(group_similarities[group_name], avg_acc + 1,
                    group_name, ha='center', va='bottom', fontweight='bold')
        
        # 拟合趋势线
        z = np.polyfit(similarities, avg_accuracies, 1)
        p = np.poly1d(z)
        x_trend = np.linspace(min(similarities), max(similarities), 100)
        plt.plot(x_trend, p(x_trend), "r--", alpha=0.8, linewidth=2)
        
        plt.xlabel('Average Intra-Group Semantic Similarity')
        plt.ylabel('Average Classification Accuracy (%)')
        plt.title('Semantic Similarity vs Classification Difficulty', fontsize=14, fontweight='bold')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('/home/<USER>/hmt/ACGAN-FG-main/semantic_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 打印分析结果
        print("\n📈 Semantic Similarity Analysis Results:")
        print("-" * 40)
        for group_name, sim in group_similarities.items():
            classes = self.groups[group_name]
            avg_acc = np.mean(list(self.group_accuracy[group_name].values()))
            print(f"Group {group_name} {classes}: Similarity={sim:.3f}, Avg Accuracy={avg_acc:.1f}%")
        
        return similarity_matrix, group_similarities
        
    def visualize_feature_space(self, focus_groups=['A', 'B']):
        """特征空间可视化分析"""
        print("\n" + "="*60)
        print("🎯 特征空间可视化 (Feature Space Visualization)")
        print("="*60)
        
        if not self.class_data:
            print("❌ 类别数据未加载")
            return
            
        # 为每个焦点组创建可视化
        for group_name in focus_groups:
            classes = self.groups[group_name]
            print(f"\n🔍 分析组{group_name}: {classes}")
            
            self._visualize_group_features(group_name, classes)
            
    def _visualize_group_features(self, group_name, classes):
        """为特定组创建特征空间可视化"""
        # 收集数据
        all_features = []
        all_labels = []
        data_types = []
        
        # 训练集数据 (其他类别)
        train_classes = [i for i in range(1, 16) if i not in classes]
        for class_id in train_classes:
            if class_id in self.class_data:
                features = self.class_data[class_id]
                all_features.append(features)
                all_labels.extend([class_id] * len(features))
                data_types.extend(['train'] * len(features))
        
        # 测试集真实特征
        for class_id in classes:
            if class_id in self.test_data:
                features = self.test_data[class_id]
                all_features.append(features)
                all_labels.extend([class_id] * len(features))
                data_types.extend(['test_real'] * len(features))
        
        # 合并所有特征
        if not all_features:
            print(f"❌ 组{group_name}没有可用数据")
            return
            
        X = np.vstack(all_features)
        y = np.array(all_labels)
        types = np.array(data_types)
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 降维可视化
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'组{group_name} {classes} 特征空间分析', fontsize=16, fontweight='bold')
        
        # t-SNE
        print(f"  🔄 执行t-SNE降维...")
        tsne = TSNE(n_components=2, random_state=42, perplexity=30)
        X_tsne = tsne.fit_transform(X_scaled)
        
        # UMAP
        print(f"  🔄 执行UMAP降维...")
        umap_reducer = umap.UMAP(n_components=2, random_state=42)
        X_umap = umap_reducer.fit_transform(X_scaled)
        
        # PCA
        print(f"  🔄 执行PCA降维...")
        pca = PCA(n_components=2, random_state=42)
        X_pca = pca.fit_transform(X_scaled)
        
        # 绘制可视化
        methods = [('t-SNE', X_tsne), ('UMAP', X_umap), ('PCA', X_pca)]
        
        for idx, (method_name, X_reduced) in enumerate(methods):
            if idx < 3:  # 前三个子图
                ax = axes[idx//2, idx%2]
                
                # 绘制训练集数据（灰色背景）
                train_mask = types == 'train'
                ax.scatter(X_reduced[train_mask, 0], X_reduced[train_mask, 1], 
                          c='lightgray', alpha=0.3, s=10, label='训练集')
                
                # 绘制测试集真实特征（彩色）
                colors = ['red', 'blue', 'green']
                for i, class_id in enumerate(classes):
                    class_mask = (y == class_id) & (types == 'test_real')
                    if np.any(class_mask):
                        ax.scatter(X_reduced[class_mask, 0], X_reduced[class_mask, 1],
                                  c=colors[i], alpha=0.8, s=50, 
                                  label=f'类别{class_id}(真实)', marker='o')
                
                ax.set_title(f'{method_name} 降维结果', fontsize=12, fontweight='bold')
                ax.legend()
                ax.grid(True, alpha=0.3)
        
        # 第四个子图：类别间距离分析
        ax = axes[1, 1]
        self._plot_class_distances(ax, classes, X_scaled, y, types)
        
        plt.tight_layout()
        plt.savefig(f'/home/<USER>/hmt/ACGAN-FG-main/feature_space_group_{group_name}.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
    def _plot_class_distances(self, ax, classes, X_scaled, y, types):
        """绘制类别间距离分析"""
        from sklearn.metrics.pairwise import euclidean_distances
        
        # 计算类别中心
        class_centers = {}
        for class_id in classes:
            class_mask = (y == class_id) & (types == 'test_real')
            if np.any(class_mask):
                class_centers[class_id] = np.mean(X_scaled[class_mask], axis=0)
        
        if len(class_centers) < 2:
            ax.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=ax.transAxes)
            return
        
        # 计算类别间距离
        center_ids = list(class_centers.keys())
        center_features = np.array([class_centers[cid] for cid in center_ids])
        distances = euclidean_distances(center_features)
        
        # 绘制距离矩阵
        im = ax.imshow(distances, cmap='RdYlBu_r')
        ax.set_xticks(range(len(center_ids)))
        ax.set_yticks(range(len(center_ids)))
        ax.set_xticklabels([f'类别{cid}' for cid in center_ids])
        ax.set_yticklabels([f'类别{cid}' for cid in center_ids])
        
        # 添加数值标注
        for i in range(len(center_ids)):
            for j in range(len(center_ids)):
                ax.text(j, i, f'{distances[i, j]:.2f}', 
                       ha='center', va='center', color='white', fontweight='bold')
        
        ax.set_title('类别间欧氏距离', fontsize=12, fontweight='bold')
        plt.colorbar(im, ax=ax, label='欧氏距离')

    def analyze_class_separability(self):
        """分析类别可分离性"""
        print("\n" + "="*60)
        print("🔬 类别可分离性分析 (Class Separability Analysis)")
        print("="*60)

        from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
        from sklearn.metrics import silhouette_score
        from scipy.spatial.distance import pdist, squareform

        separability_results = {}

        for group_name, classes in self.groups.items():
            print(f"\n🔍 分析组{group_name}: {classes}")

            # 收集组内数据
            group_features = []
            group_labels = []

            for class_id in classes:
                if class_id in self.test_data:
                    features = self.test_data[class_id]
                    group_features.append(features)
                    group_labels.extend([class_id] * len(features))

            if not group_features:
                print(f"  ❌ 组{group_name}没有可用数据")
                continue

            X_group = np.vstack(group_features)
            y_group = np.array(group_labels)

            # 1. 计算轮廓系数
            if len(np.unique(y_group)) > 1:
                silhouette_avg = silhouette_score(X_group, y_group)
                print(f"  📊 轮廓系数: {silhouette_avg:.3f}")
            else:
                silhouette_avg = 0
                print(f"  📊 轮廓系数: N/A (只有一个类别)")

            # 2. 计算类内距离vs类间距离比
            intra_distances = []
            inter_distances = []

            for class_id in classes:
                class_mask = y_group == class_id
                if np.sum(class_mask) > 1:
                    class_features = X_group[class_mask]
                    # 类内距离
                    intra_dist = np.mean(pdist(class_features))
                    intra_distances.append(intra_dist)

                    # 类间距离
                    for other_class in classes:
                        if other_class != class_id:
                            other_mask = y_group == other_class
                            if np.sum(other_mask) > 0:
                                other_features = X_group[other_mask]
                                class_center = np.mean(class_features, axis=0)
                                other_center = np.mean(other_features, axis=0)
                                inter_dist = np.linalg.norm(class_center - other_center)
                                inter_distances.append(inter_dist)

            avg_intra = np.mean(intra_distances) if intra_distances else 0
            avg_inter = np.mean(inter_distances) if inter_distances else 0
            separability_ratio = avg_inter / avg_intra if avg_intra > 0 else 0

            print(f"  📏 平均类内距离: {avg_intra:.3f}")
            print(f"  📏 平均类间距离: {avg_inter:.3f}")
            print(f"  📈 可分离性比率: {separability_ratio:.3f}")

            # 3. 计算Fisher判别比
            if len(classes) == 2 and len(np.unique(y_group)) == 2:
                try:
                    lda = LinearDiscriminantAnalysis()
                    lda.fit(X_group, y_group)
                    fisher_score = lda.score(X_group, y_group)
                    print(f"  🎯 Fisher判别准确率: {fisher_score:.3f}")
                except:
                    fisher_score = 0
                    print(f"  🎯 Fisher判别准确率: N/A")
            else:
                fisher_score = 0
                print(f"  🎯 Fisher判别准确率: N/A (非二分类)")

            separability_results[group_name] = {
                'silhouette': silhouette_avg,
                'intra_distance': avg_intra,
                'inter_distance': avg_inter,
                'separability_ratio': separability_ratio,
                'fisher_score': fisher_score
            }

        # 可视化可分离性结果
        self._plot_separability_results(separability_results)

        return separability_results

    def _plot_separability_results(self, separability_results):
        """绘制可分离性分析结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('类别可分离性分析结果', fontsize=16, fontweight='bold')

        groups = list(separability_results.keys())
        colors = ['red', 'blue', 'green', 'orange', 'purple']

        # 1. 轮廓系数
        ax = axes[0, 0]
        silhouette_scores = [separability_results[g]['silhouette'] for g in groups]
        bars = ax.bar(groups, silhouette_scores, color=colors[:len(groups)], alpha=0.7)
        ax.set_title('轮廓系数 (越高越好)', fontweight='bold')
        ax.set_ylabel('轮廓系数')
        ax.set_ylim(-1, 1)
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.3f}', ha='center', va='bottom', fontweight='bold')

        # 2. 可分离性比率
        ax = axes[0, 1]
        sep_ratios = [separability_results[g]['separability_ratio'] for g in groups]
        bars = ax.bar(groups, sep_ratios, color=colors[:len(groups)], alpha=0.7)
        ax.set_title('可分离性比率 (类间距离/类内距离)', fontweight='bold')
        ax.set_ylabel('比率')
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.2f}', ha='center', va='bottom', fontweight='bold')

        # 3. 可分离性 vs 准确率
        ax = axes[1, 0]
        avg_accuracies = []
        for group in groups:
            group_acc = self.group_accuracy[group]
            avg_acc = np.mean(list(group_acc.values()))
            avg_accuracies.append(avg_acc)

        ax.scatter(silhouette_scores, avg_accuracies, s=100, alpha=0.7, c=colors[:len(groups)])
        for i, group in enumerate(groups):
            ax.annotate(group, (silhouette_scores[i], avg_accuracies[i]),
                       xytext=(5, 5), textcoords='offset points', fontweight='bold')

        # 拟合趋势线
        if len(silhouette_scores) > 1:
            z = np.polyfit(silhouette_scores, avg_accuracies, 1)
            p = np.poly1d(z)
            x_trend = np.linspace(min(silhouette_scores), max(silhouette_scores), 100)
            ax.plot(x_trend, p(x_trend), "r--", alpha=0.8, linewidth=2)

        ax.set_xlabel('轮廓系数')
        ax.set_ylabel('平均分类准确率 (%)')
        ax.set_title('可分离性 vs 分类性能', fontweight='bold')
        ax.grid(True, alpha=0.3)

        # 4. 综合指标雷达图
        ax = axes[1, 1]
        self._plot_radar_chart(ax, separability_results, groups)

        plt.tight_layout()
        plt.savefig('/home/<USER>/hmt/ACGAN-FG-main/separability_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_radar_chart(self, ax, separability_results, groups):
        """绘制雷达图"""
        from math import pi

        # 准备数据
        categories = ['轮廓系数', '可分离性比率', '准确率(归一化)']
        N = len(categories)

        # 计算角度
        angles = [n / float(N) * 2 * pi for n in range(N)]
        angles += angles[:1]  # 闭合

        ax.set_theta_offset(pi / 2)
        ax.set_theta_direction(-1)
        ax.set_thetagrids(np.degrees(angles[:-1]), categories)

        colors = ['red', 'blue', 'green', 'orange', 'purple']

        for i, group in enumerate(groups):
            # 归一化数据
            silhouette = (separability_results[group]['silhouette'] + 1) / 2  # [-1,1] -> [0,1]
            sep_ratio = min(separability_results[group]['separability_ratio'] / 5, 1)  # 限制在[0,1]
            accuracy = np.mean(list(self.group_accuracy[group].values())) / 100  # [0,100] -> [0,1]

            values = [silhouette, sep_ratio, accuracy]
            values += values[:1]  # 闭合

            ax.plot(angles, values, 'o-', linewidth=2, label=f'组{group}', color=colors[i])
            ax.fill(angles, values, alpha=0.25, color=colors[i])

        ax.set_ylim(0, 1)
        ax.set_title('综合性能雷达图', fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

def main():
    """主函数"""
    print("🚀 开始数据诊断分析...")

    # 创建诊断实例
    diagnosis = DataDiagnosis()

    # 加载数据
    print("\n📂 加载数据...")
    diagnosis.load_attribute_matrix()
    diagnosis.load_class_data()

    # 语义空间分析
    similarity_matrix, group_similarities = diagnosis.analyze_semantic_similarity()

    # 类别可分离性分析
    separability_results = diagnosis.analyze_class_separability()

    # 特征空间可视化
    diagnosis.visualize_feature_space(focus_groups=['A', 'B'])

    # 生成诊断报告
    print("\n" + "="*60)
    print("📋 诊断报告总结")
    print("="*60)

    print("\n🎯 关键发现:")
    print("-" * 30)

    # 找出最容易和最困难的组
    avg_accs = {}
    for group, acc_dict in diagnosis.group_accuracy.items():
        avg_accs[group] = np.mean(list(acc_dict.values()))

    easiest_group = max(avg_accs, key=avg_accs.get)
    hardest_group = min(avg_accs, key=avg_accs.get)

    print(f"✅ 最容易组: {easiest_group} (平均准确率: {avg_accs[easiest_group]:.1f}%)")
    print(f"❌ 最困难组: {hardest_group} (平均准确率: {avg_accs[hardest_group]:.1f}%)")

    if easiest_group in group_similarities and hardest_group in group_similarities:
        print(f"📊 语义相似度对比:")
        print(f"   - {easiest_group}组内相似度: {group_similarities[easiest_group]:.3f}")
        print(f"   - {hardest_group}组内相似度: {group_similarities[hardest_group]:.3f}")

    if easiest_group in separability_results and hardest_group in separability_results:
        print(f"🔬 可分离性对比:")
        easy_sep = separability_results[easiest_group]
        hard_sep = separability_results[hardest_group]
        print(f"   - {easiest_group}组轮廓系数: {easy_sep['silhouette']:.3f}")
        print(f"   - {hardest_group}组轮廓系数: {hard_sep['silhouette']:.3f}")
        print(f"   - {easiest_group}组可分离性比率: {easy_sep['separability_ratio']:.3f}")
        print(f"   - {hardest_group}组可分离性比率: {hard_sep['separability_ratio']:.3f}")

    print("\n✅ 数据诊断分析完成!")
    print("📊 生成的文件:")
    print("  - semantic_analysis.png: 语义空间分析结果")
    print("  - separability_analysis.png: 可分离性分析结果")
    print("  - feature_space_group_A.png: A组特征空间分析")
    print("  - feature_space_group_B.png: B组特征空间分析")

if __name__ == "__main__":
    main()

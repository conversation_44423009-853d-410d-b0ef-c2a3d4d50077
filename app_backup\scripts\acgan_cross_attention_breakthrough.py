 #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态突破版Cross Attention模型 - 专门解决64%瓶颈问题
核心改进：
1. 自适应损失权重调度 - 根据训练阶段动态调整
2. 判别器/生成器力量平衡机制 - 防止一方过强
3. 特征空间扰动机制 - 帮助跳出局部最优
4. 多尺度交叉注意力 - 增强语义融合
5. 学习率重启机制 - 定期重新激活学习
"""

import numpy as np
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, LayerNormalization, BatchNormalization, Flatten, multiply, concatenate, Layer, Dropout
from tensorflow.keras.models import Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test_fusion import feature_generation_and_diagnosis_with_fusion
from sklearn.preprocessing import MinMaxScaler
import json
import math

# GPU配置
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        tf.config.set_visible_devices(gpus[0], 'GPU')
        tf.config.experimental.set_memory_growth(gpus[0], True)
        logical_gpus = tf.config.list_logical_devices('GPU')
        print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
    except RuntimeError as e:
        print(e)

class MultiScaleCrossAttention(Layer):
    """多尺度交叉注意力层 - 增强语义融合能力"""
    def __init__(self, units, num_heads=4, **kwargs):
        super(MultiScaleCrossAttention, self).__init__(**kwargs)
        self.units = units
        self.num_heads = num_heads
        self.head_dim = units // num_heads

    def build(self, input_shape):
        data_feature_dim = input_shape[0][-1]
        semantic_feature_dim = input_shape[1][-1]
        
        # 多头注意力
        self.queries = [Dense(self.head_dim, use_bias=False) for _ in range(self.num_heads)]
        self.keys = [Dense(self.head_dim, use_bias=False) for _ in range(self.num_heads)]
        self.values = [Dense(self.head_dim, use_bias=False) for _ in range(self.num_heads)]
        
        # 多尺度融合
        self.local_fusion = Dense(data_feature_dim // 2)
        self.global_fusion = Dense(data_feature_dim // 2)
        
        # 输出层
        self.out_dense = Dense(data_feature_dim)
        self.out_norm = LayerNormalization()
        self.dropout = Dropout(0.1)
        
        super(MultiScaleCrossAttention, self).build(input_shape)

    def call(self, inputs, training=None):
        data_features, semantic_features = inputs
        batch_size = tf.shape(data_features)[0]
        
        # 扩展维度用于注意力计算
        data_expanded = tf.expand_dims(data_features, axis=1)
        semantic_expanded = tf.expand_dims(semantic_features, axis=1)
        
        # 多头注意力
        attention_outputs = []
        for i in range(self.num_heads):
            q = self.queries[i](data_expanded)
            k = self.keys[i](semantic_expanded)
            v = self.values[i](semantic_expanded)
            
            attention_scores = tf.matmul(q, k, transpose_b=True) / tf.sqrt(float(self.head_dim))
            attention_probs = tf.nn.softmax(attention_scores)
            context = tf.matmul(attention_probs, v)
            attention_outputs.append(tf.squeeze(context, axis=1))
        
        # 多尺度融合
        all_attention = concatenate(attention_outputs)
        local_features = self.local_fusion(all_attention)
        global_features = self.global_fusion(concatenate([data_features, semantic_features]))
        
        # 最终融合
        fused_output = concatenate([local_features, global_features])
        fused_output = self.out_dense(fused_output)
        fused_output = self.dropout(fused_output, training=training)
        fused_output = self.out_norm(fused_output + data_features)  # 残差连接
        
        return fused_output

class BreakthroughZeroShot():
    def __init__(self):
        self.data_lenth = 52
        self.sample_shape = (self.data_lenth,)
        
        self.feature_dim = 256
        self.feature_shape = (256,)
        self.num_classes = 15
        self.attribute_dim = 20
        self.latent_dim = 50
        self.noise_shape = (self.latent_dim, 1)
        self.n_critic = 1

        # 🔥 动态损失权重系统
        self.base_lambda_cla = 5.0
        self.base_lambda_triplet = 8.0
        self.base_lambda_fusion = 1.0
        self.current_lambda_cla = self.base_lambda_cla
        self.current_lambda_triplet = self.base_lambda_triplet
        self.current_lambda_fusion = self.base_lambda_fusion
        
        self.triplet_margin = 0.2
        
        # 🔥 自适应学习率系统
        self.base_lr = 0.0002
        self.current_lr = self.base_lr
        self.lr_restart_epochs = [100, 200, 300]  # 学习率重启点
        
        # 初始化优化器（将在训练中动态更新）
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=self.current_lr)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=self.current_lr)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=self.current_lr)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=self.current_lr)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=self.current_lr)
        self.fusion_optimizer = tf.keras.optimizers.Adam(learning_rate=self.current_lr)
        
        # 🔥 对抗平衡系统
        self.d_strength = 1.0
        self.g_strength = 1.0
        self.d_loss_history = []
        self.g_loss_history = []
        
        # 构建模型
        self.autoencoder = self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c = self.build_classifier()
        self.fusion_net = self.build_multiscale_fusion_network()
        
        # 早停和最佳模型保存
        self.best_accuracy = 0
        self.patience_counter = 0
        self.patience = 80  # 减少patience，更激进地重启
        self.plateau_epochs = 0  # 记录平台期长度
        
    def build_autoencoder(self):
        sample = Input(shape=self.sample_shape)
        a0 = sample

        # 编码器 - 添加更多层用于特征学习
        a1 = Dense(100)(a0)
        a1 = LeakyReLU(alpha=0.2)(a1)
        a1 = LayerNormalization()(a1)
        a1 = Dropout(0.1)(a1)

        a2 = Dense(200)(a1)
        a2 = LeakyReLU(alpha=0.2)(a2)
        a2 = LayerNormalization()(a2)
        a2 = Dropout(0.1)(a2)

        a3 = Dense(256)(a2)
        a3 = LeakyReLU(alpha=0.2)(a3)
        a3 = LayerNormalization()(a3)
        feature = a3

        # 解码器
        a4 = Dense(200)(feature)
        a4 = LeakyReLU(alpha=0.2)(a4)
        a4 = LayerNormalization()(a4)

        a5 = Dense(100)(a4)
        a5 = LeakyReLU(alpha=0.2)(a5)
        a5 = LayerNormalization()(a5)

        a6 = Dense(52)(a5)
        output_sample = a6

        autoencoder = Model(sample, [feature, output_sample])
        self.encoder = Model(sample, feature)
        return autoencoder

    def build_discriminator(self):
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        # 增强判别器
        d1 = SpectralNormalization(Dense(128))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        d1 = Dropout(0.2)(d1)
        
        d2 = SpectralNormalization(Dense(64))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)
        d2 = Dropout(0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input, attribute], validity)

    def build_generator(self):
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        noise_embedding = Flatten()(noise)
        attribute_embedding = Dense(self.latent_dim)(attribute)
        
        g_input = concatenate([noise_embedding, attribute_embedding])

        # 增强生成器
        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = LayerNormalization()(g1)
        g1 = Dropout(0.1)(g1)

        g2 = Dense(256)(g1)
        g2 = LeakyReLU(alpha=0.2)(g2)
        g2 = BatchNormalization()(g2)
        g2 = Dropout(0.1)(g2)
        
        generated_feature = Dense(256)(g2)

        return Model([noise, attribute], generated_feature)
    
    def build_classifier(self):
        sample = Input(shape=self.feature_shape)

        c1 = Dense(100)(sample)
        c1 = LeakyReLU(alpha=0.2)(c1)
        c1 = Dropout(0.1)(c1)
        
        c2 = Dense(50)(c1)
        c2 = LeakyReLU(alpha=0.2)(c2)
        hidden_output = c2
               
        c3 = Dense(20, activation="sigmoid")(c2)
        predict_attribute = c3
        
        return Model(sample, [hidden_output, predict_attribute])

    def build_multiscale_fusion_network(self):
        """构建多尺度融合网络"""
        data_input = Input(shape=self.feature_shape, name="data_input")
        semantic_input = Input(shape=(self.attribute_dim,), name="semantic_input")
        
        # 使用多尺度交叉注意力
        fused_output = MultiScaleCrossAttention(units=128)([data_input, semantic_input])
        
        return Model([data_input, semantic_input], fused_output, name="MultiScaleFusionNetwork")

    def update_adaptive_weights(self, epoch, accuracy_history):
        """🔥 自适应损失权重调度"""
        if len(accuracy_history) < 5:
            return
            
        recent_improvement = max(accuracy_history[-5:]) - min(accuracy_history[-5:])
        
        # 检测平台期
        if recent_improvement < 0.01:  # 改善小于1%
            self.plateau_epochs += 1
            # 平台期调整策略
            if self.plateau_epochs > 20:
                # 增强triplet学习
                self.current_lambda_triplet = min(self.base_lambda_triplet * 2, 16.0)
                # 减少分类损失竞争
                self.current_lambda_cla = max(self.base_lambda_cla * 0.5, 2.0)
                # 增强融合学习
                self.current_lambda_fusion = min(self.base_lambda_fusion * 1.5, 3.0)
        else:
            self.plateau_epochs = 0
            # 恢复平衡
            self.current_lambda_triplet = self.base_lambda_triplet
            self.current_lambda_cla = self.base_lambda_cla  
            self.current_lambda_fusion = self.base_lambda_fusion
            
        print(f"🔄 [Epoch {epoch}] 自适应权重: triplet={self.current_lambda_triplet:.1f}, cla={self.current_lambda_cla:.1f}, fusion={self.current_lambda_fusion:.1f}")

    def update_adversarial_balance(self, d_loss, g_loss):
        """🔥 对抗平衡机制"""
        # 确保张量是标量，使用tf.reduce_mean处理多维张量
        d_loss_scalar = tf.reduce_mean(d_loss) if d_loss.shape.ndims > 0 else d_loss
        g_loss_scalar = tf.reduce_mean(g_loss) if g_loss.shape.ndims > 0 else g_loss
        
        self.d_loss_history.append(float(d_loss_scalar.numpy()))
        self.g_loss_history.append(float(g_loss_scalar.numpy()))
        
        if len(self.d_loss_history) >= 10:
            avg_d_loss = np.mean(self.d_loss_history[-10:])
            avg_g_loss = np.mean(self.g_loss_history[-10:])
            
            # 动态调整对抗强度
            if avg_d_loss < 0.1:  # 判别器过强
                self.d_strength = max(0.5, self.d_strength * 0.9)
                self.g_strength = min(2.0, self.g_strength * 1.1)
            elif avg_g_loss < 2.0:  # 生成器过强
                self.d_strength = min(2.0, self.d_strength * 1.1)
                self.g_strength = max(0.5, self.g_strength * 0.9)
            else:
                # 逐渐恢复平衡
                self.d_strength = 0.9 * self.d_strength + 0.1 * 1.0
                self.g_strength = 0.9 * self.g_strength + 0.1 * 1.0

    def learning_rate_restart(self, epoch):
        """🔥 学习率重启机制"""
        if epoch in self.lr_restart_epochs:
            # 重启学习率到更高值
            self.current_lr = self.base_lr * 1.5
            print(f"🚀 [Epoch {epoch}] 学习率重启: {self.current_lr}")
            
            # 更新所有优化器
            self.autoencoder_optimizer.learning_rate.assign(self.current_lr)
            self.d_optimizer.learning_rate.assign(self.current_lr * self.d_strength)
            self.g_optimizer.learning_rate.assign(self.current_lr * self.g_strength)
            self.c_optimizer.learning_rate.assign(self.current_lr)
            self.m_optimizer.learning_rate.assign(self.current_lr)
            self.fusion_optimizer.learning_rate.assign(self.current_lr)
        else:
            # 逐渐衰减
            self.current_lr = max(self.base_lr * 0.1, self.current_lr * 0.999)

    def feature_space_perturbation(self, features, training=True):
        """🔥 特征空间扰动机制 - 帮助跳出局部最优"""
        if training and np.random.random() < 0.1:  # 10%概率添加扰动
            noise = tf.random.normal(tf.shape(features), stddev=0.01)
            return features + noise
        return features

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
        return tf.reduce_mean(y_true * y_pred)
          
    def classification_loss(self, y_true, pred_attribute):
        return tf.keras.losses.binary_crossentropy(y_true, pred_attribute)
    
    def train(self, epochs, batch_size, log_file=None):
        start_time = datetime.datetime.now()
        
        accuracy_list_1 = []
        accuracy_list_2 = []
        accuracy_list_3 = []
        accuracy_list_4 = []
        all_accuracy_history = []
        
        valid = -np.ones((batch_size, 1))
        fake = np.ones((batch_size, 1))
        
        PATH_train = './dataset_train_case1.npz'
        PATH_test = './dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        train_X_by_class = {i: train_data[f'training_samples_{i+1}'] for i in range(15)}
        train_Y_by_class = {i: train_data[f'training_attribute_{i+1}'] for i in range(15)}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        test_X = np.concatenate([test_data[f'testing_samples_{i+1}'] for i in [1, 6, 14]])
        test_Y = np.concatenate([test_data[f'testing_attribute_{i+1}'] for i in [1, 6, 14]])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # 重新组织数据
        current_pos = 0
        for i in range(15):
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata = all_train_X
        train_attributelabel = all_train_Y
        train_classlabel = all_train_labels
        
        testdata = test_X
        test_attributelabel = test_Y
       
        num_batches = int(traindata.shape[0] / batch_size)
        
        for epoch in range(epochs):
            
            # 🔥 动态调整系统
            self.learning_rate_restart(epoch)
            self.update_adaptive_weights(epoch, all_accuracy_history)
            
            for batch_i in range(num_batches):
                
                start_i = batch_i * batch_size
                end_i = (batch_i + 1) * batch_size
                
                train_x = traindata[start_i:end_i]
                train_y = train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # 1. Autoencoder and Classifier Training with perturbation
                self.autoencoder.trainable = True
                self.c.trainable = True
                
                with tf.GradientTape() as tape_auto_c:
                    feature, output_sample = self.autoencoder(train_x, training=True)
                    # 🔥 添加特征扰动
                    feature_perturbed = self.feature_space_perturbation(feature, training=True)
                    autoencoder_loss = mean_squared_error(train_x, output_sample)      

                    hidden_output_c, predict_attribute_c = self.c(feature_perturbed, training=True)
                    c_loss = self.classification_loss(train_y, predict_attribute_c)

                    total_ac_loss = autoencoder_loss + self.current_lambda_cla * c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                # 2. Enhanced Triplet Loss Training
                positive_samples = []
                negative_samples = []
                for label in train_labels:
                    pos_class_samples = train_X_by_class[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    
                    neg_class = np.random.choice([c for c in range(15) if c != label])
                    neg_class_samples = train_X_by_class[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(train_x, training=True)
                    positive_features = self.encoder(positive_samples, training=True)
                    negative_features = self.encoder(negative_samples, training=True)
                    
                    # 🔥 添加特征扰动
                    anchor_features = self.feature_space_perturbation(anchor_features, training=True)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))

                # 3. 增强融合网络训练
                with tf.GradientTape() as tape_fusion:
                    batch_features = self.encoder(train_x, training=True)
                    _, semantic_features = self.c(batch_features, training=True)
                    
                    fused_features = self.fusion_net([batch_features, semantic_features], training=True)
                    
                    # 修复融合损失 - 移除维度不匹配的diversity_loss
                    consistency_loss = tf.reduce_mean(tf.square(fused_features - batch_features))
                    # 使用语义特征的内在多样性作为正则化
                    semantic_diversity = tf.reduce_mean(tf.square(semantic_features - tf.reduce_mean(semantic_features, axis=0)))
                    fusion_loss = consistency_loss + 0.1 * semantic_diversity

                grads_fusion = tape_fusion.gradient(fusion_loss, self.fusion_net.trainable_weights)
                self.fusion_optimizer.apply_gradients(zip(grads_fusion, self.fusion_net.trainable_weights))

                # 4. 平衡的判别器训练
                self.d.trainable = True
                self.g.trainable = False

                with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature = self.g([noise, train_y], training=True)
                    real_feature = self.encoder(train_x, training=False)
        
                    real_validity = self.d([real_feature, train_y], training=True)
                    fake_validity = self.d([fake_feature, train_y], training=True)  
                    
                    # 🔥 使用对抗平衡强度                    
                    d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                    d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                    d_loss = (d_loss_real + d_loss_fake) * self.d_strength
                  
                grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # 5. 平衡的生成器训练
                self.d.trainable = False               
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                    noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature_g = self.g([noise_g, train_y], training=True)
                    fake_validity_g = self.d([fake_feature_g, train_y], training=False)
                    adversarial_loss = self.wasserstein_loss(valid, fake_validity_g)
            
                    fake_hidden_output_g, fake_classification_g = self.c(fake_feature_g, training=False)
                    classification_loss = self.classification_loss(train_y, fake_classification_g)
                    
                    # 增强生成器的triplet loss
                    g_positive_features = self.encoder(positive_samples, training=False)
                    g_negative_features = self.encoder(negative_samples, training=False)
                    triplet_loss_g = self.triplet_loss(fake_feature_g, g_positive_features, g_negative_features)
                    
                    # 🔥 使用动态权重和对抗平衡
                    total_loss = (adversarial_loss + 
                                 self.current_lambda_cla * classification_loss + 
                                 self.current_lambda_triplet * triplet_loss_g) * self.g_strength
                          
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                
                # 🔥 更新对抗平衡
                self.update_adversarial_balance(d_loss, total_loss)
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][M loss: %f][Fusion loss: %f][D loss: %f][G loss %05f]time: %s" \
                     % (epoch, epochs, batch_i, num_batches,
                        tf.reduce_mean(total_ac_loss), m_loss, fusion_loss, d_loss,
                        tf.reduce_mean(total_loss), elapsed_time))
        
            if epoch % 1 == 0:
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis_with_fusion(
                    self, 2000, testdata, test_attributelabel, self.fusion_net)  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)
                
                current_best = max(accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp)
                all_accuracy_history.append(current_best)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]" \
                      % (epoch, epochs, max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)))
                
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
                
                # 🔥 改进的早停机制
                if current_best > self.best_accuracy:
                    self.best_accuracy = current_best
                    self.patience_counter = 0
                    print(f"🏆 New best accuracy: {current_best:.4f}")
                else:
                    self.patience_counter += 1
                    
                # 更激进的重启机制
                if self.patience_counter >= self.patience and epoch > 50:
                    print(f"🔄 平台期检测，执行学习率重启机制")
                    self.current_lr = self.base_lr * 2.0  # 大幅提升学习率
                    self.patience_counter = 0  # 重置计数器
                    # 立即更新优化器
                    for optimizer in [self.autoencoder_optimizer, self.d_optimizer, self.g_optimizer, 
                                    self.c_optimizer, self.m_optimizer, self.fusion_optimizer]:
                        optimizer.learning_rate.assign(self.current_lr)
            
        best_accuracy = max([max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)])
        print('🎉 训练完成! 最终最佳准确率: {:.4f}'.format(best_accuracy))
        print(f'🔥 历史最佳准确率: {self.best_accuracy:.4f}')
        if log_file:
            log_file.write(f'训练完成! 最终最佳准确率: {best_accuracy:.4f}\n')
            log_file.write(f'历史最佳准确率: {self.best_accuracy:.4f}\n')
            log_file.flush()

if __name__ == '__main__':
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + "_cross_attention_breakthrough.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"🚀 动态突破训练开始，目标：突破64%瓶颈")
    print(f"📄 日志将被记录到: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 训练日志 (动态突破版交叉注意力融合架构)\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**突破策略**: 自适应权重调度、对抗平衡、特征扰动、多尺度注意力、学习率重启\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = BreakthroughZeroShot()
        gan.train(epochs=500, batch_size=120, log_file=log_file)

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"✅ 动态突破训练完成，日志已保存至: {log_filename}")
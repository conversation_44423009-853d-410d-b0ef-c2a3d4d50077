 import numpy as np
import pandas as pd
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test_hard_negative import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler
from data_pipeline import OptimizedDataPipeline, monitor_gpu, check_docker_gpu_config

# 配置GPU显存按需增长
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(e)

# 在训练开始前检查GPU配置
print("=" * 60)
print("GPU配置检查和优化")
print("=" * 60)
check_docker_gpu_config()
monitor_gpu()
print("=" * 60)

def residual_block(x, units):
    """残差块"""
    shortcut = x
    x = Dense(units, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dense(units)(x)
    
    # 确保shortcut和x的维度匹配
    if K.int_shape(shortcut)[-1] != units:
        shortcut = Dense(units, activation='linear')(shortcut)
    
    x = tf.keras.layers.Add()([x, shortcut])
    x = tf.keras.layers.Activation('relu')(x)
    return x

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        self.W_q = self.add_weight(shape=(input_shape[-1], input_shape[-1]),
                                   initializer='glorot_uniform',
                                   trainable=True)
        self.W_k = self.add_weight(shape=(input_shape[-1], input_shape[-1]),
                                   initializer='glorot_uniform',
                                   trainable=True)
        self.W_v = self.add_weight(shape=(input_shape[-1], input_shape[-1]),
                                   initializer='glorot_uniform',
                                   trainable=True)
        super(SelfAttention, self).build(input_shape)

    def call(self, inputs):
        q = tf.matmul(inputs, self.W_q)
        k = tf.matmul(inputs, self.W_k)
        v = tf.matmul(inputs, self.W_v)
        
        # 修复混合精度类型问题
        scale = tf.sqrt(tf.cast(tf.shape(q)[-1], inputs.dtype))
        attention_weights = tf.nn.softmax(tf.matmul(q, k, transpose_b=True) / scale)
        attention_output = tf.matmul(attention_weights, v)
        return attention_output

class Zero_shot_Hard_Negative_Optimized():
    def __init__(self, use_optimized_pipeline=True):
        self.num_classes = 15  # 已知类别数量
        self.num_attributes = 20  # 每个类别的属性向量维度 (从15修改为20，匹配真实数据)
        self.latent_dim = 128    # 噪声向量维度
        self.feature_dim = 52    # 特征维度 (从 128 修改为 52，匹配真实数据)
        
        # 指定物理设备
        self.device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
        
        # 编译器优化
        self.optimizer_g = tf.keras.optimizers.Adam(learning_rate=0.0001, beta_1=0.5)
        self.optimizer_d = tf.keras.optimizers.Adam(learning_rate=0.0001, beta_1=0.5)
        self.optimizer_c = tf.keras.optimizers.Adam(learning_rate=0.0001, beta_1=0.5)
        self.optimizer_ae = tf.keras.optimizers.Adam(learning_rate=0.0001, beta_1=0.5)
        
        # 损失权重
        self.lambda_triplet = 1.0      # Triplet损失权重
        self.lambda_classification = 1.0  # 分类损失权重
        self.lambda_reconstruction = 0.5   # 重构损失权重
        self.lambda_adversarial = 1.0     # 对抗损失权重
        self.lambda_cycle_rank = 0.1      # 循环排序损失权重
        self.lambda_mi = 0.1             # 互信息损失权重
        
        # 难负例挖掘参数
        self.hard_negative_ratio = 0.7    # 难负例占比
        self.top_k_candidates = 5         # 选择前K个最相似属性作为候选
        self.use_hard_negative = True     # 启用难负例挖掘
        
        # 初始化优化数据流水线
        self.use_optimized_pipeline = use_optimized_pipeline
        if self.use_optimized_pipeline:
            self.data_pipeline = OptimizedDataPipeline(use_mixed_precision=True)
            print("已启用优化数据流水线和混合精度训练")
        
        # 预处理器
        self.scaler = MinMaxScaler()
        
        # 构建模型
        self.build_autoencoder()
        self.discriminator = self.build_discriminator()
        self.generator = self.build_generator()
        self.classifier = self.build_classifier()
        
        print("优化版难负例挖掘Zero-shot模型初始化完成")

    def build_autoencoder(self):
        """构建自编码器"""
        input_data = Input(shape=(self.feature_dim,))
        
        # 编码器
        encoded = Dense(128, activation='relu')(input_data)
        encoded = BatchNormalization()(encoded)
        encoded = residual_block(encoded, 256)
        encoded = SelfAttention()(encoded)
        encoded = Dense(256, activation='relu')(encoded)
        encoded = BatchNormalization()(encoded)
        
        # 解码器
        decoded = Dense(128, activation='relu')(encoded)
        decoded = BatchNormalization()(decoded)
        decoded = Dense(64, activation='relu')(decoded)
        decoded = Dense(self.feature_dim, activation='linear')(decoded)
        
        self.autoencoder = Model(input_data, decoded)
        self.encoder = Model(input_data, encoded)
        
        return self.autoencoder

    def build_discriminator(self):
        """构建判别器"""
        feature_input = Input(shape=(256,))
        
        x = Dense(128, activation='relu')(feature_input)
        x = BatchNormalization()(x)
        x = residual_block(x, 128)
        x = Dense(64, activation='relu')(x)
        x = Dense(1, activation='sigmoid')(x)
        
        return Model(feature_input, x)

    def build_generator(self):
        """构建生成器"""
        noise_input = Input(shape=(self.latent_dim,))
        attribute_input = Input(shape=(self.num_attributes,))
        
        # 连接噪声和属性
        combined = concatenate([noise_input, attribute_input])
        
        x = Dense(256, activation='relu')(combined)
        x = BatchNormalization()(x)
        x = residual_block(x, 256)
        x = SelfAttention()(x)
        x = Dense(256, activation='relu')(x)
        x = BatchNormalization()(x)
        
        return Model([noise_input, attribute_input], x)

    def build_classifier(self):
        """构建分类器"""
        feature_input = Input(shape=(256,))
        
        x = Dense(128, activation='relu')(feature_input)
        x = BatchNormalization()(x)
        x = Dense(64, activation='relu')(x)
        x = Dense(self.num_attributes, activation='sigmoid')(x)
        
        return Model(feature_input, x)

    def compute_attribute_similarity(self, anchor_attributes, all_attributes):
        """计算属性相似度"""
        # 使用余弦相似度
        anchor_norm = tf.nn.l2_normalize(anchor_attributes, axis=1)
        all_norm = tf.nn.l2_normalize(all_attributes, axis=1)
        similarities = tf.matmul(anchor_norm, all_norm, transpose_b=True)
        return similarities

    def hard_negative_sampling(self, anchor_labels, all_attributes):
        """难负例采样策略"""
        batch_size = tf.shape(anchor_labels)[0]
        
        # 获取anchor的属性向量
        anchor_attributes = tf.gather(all_attributes, anchor_labels)
        
        # 计算与所有属性的相似度
        similarities = self.compute_attribute_similarity(anchor_attributes, all_attributes)
        
        # 创建mask，排除自身类别
        anchor_mask = tf.one_hot(anchor_labels, depth=self.num_classes)
        similarities = similarities - anchor_mask * 1e9  # 将自身类别的相似度设为负无穷
        
        # 获取最相似的K个候选类别
        _, top_k_indices = tf.nn.top_k(similarities, k=self.top_k_candidates)
        
        # 随机从top_k中选择负样本
        random_indices = tf.random.uniform([batch_size], maxval=self.top_k_candidates, dtype=tf.int32)
        batch_indices = tf.range(batch_size)
        gather_indices = tf.stack([batch_indices, random_indices], axis=1)
        hard_negatives = tf.gather_nd(top_k_indices, gather_indices)
        
        return hard_negatives

    def triplet_loss(self, anchor, positive, negative, margin=1.0):
        """改进的Triplet Loss with Hard Negative Mining"""
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=1)
        
        basic_loss = tf.maximum(0.0, margin + pos_dist - neg_dist)
        return tf.reduce_mean(basic_loss)

    def wasserstein_loss(self, y_true, y_pred):
        """Wasserstein损失"""
        return tf.reduce_mean(y_true * y_pred)

    def cycle_rank_loss(self, original_features, generated_features, reconstructed_features):
        """循环一致性损失"""
        reconstruction_loss = tf.reduce_mean(tf.square(original_features - reconstructed_features))
        consistency_loss = tf.reduce_mean(tf.square(generated_features - reconstructed_features))
        return reconstruction_loss + consistency_loss

    def mi_penalty_loss(self, features, noise):
        """互信息惩罚损失"""
        # 简化版互信息估计
        joint_samples = tf.concat([features, noise], axis=1)
        marginal_samples = tf.concat([features, tf.random.shuffle(noise)], axis=1)
        
        # 使用简单的统计网络估计互信息
        joint_score = tf.reduce_mean(tf.nn.sigmoid(tf.reduce_sum(joint_samples, axis=1)))
        marginal_score = tf.reduce_mean(tf.nn.sigmoid(tf.reduce_sum(marginal_samples, axis=1)))
        
        mi_estimate = joint_score - tf.math.log(marginal_score + 1e-8)
        return tf.maximum(0.0, mi_estimate - 1.0)  # 限制互信息不超过1

    def train_optimized(self, epochs, batch_size=512, log_file=None, test_class_indices=None):
        """使用优化数据流水线的训练方法"""
        print("开始优化版训练...")
        
        # 数据加载 - 使用正确的函数名和参数
        train_data, train_label, train_attr_label, test_data, test_label, test_attr_label, test_attr_matrix, train_attr_matrix = read_data.creat_dataset(test_class_indices)
        
        print(f"开始为测试类别 {test_class_indices} 准备优化数据流水线，批处理大小: {batch_size}")
        
        # 使用优化数据流水线准备数据
        PATH_train = './dataset_train_case1.npz'
        PATH_test = './dataset_test_case1.npz'
        
        train_data_npz = np.load(PATH_train)
        test_data_npz = np.load(PATH_test)
        
        data_info = self.data_pipeline.prepare_data_triplet(
            train_data_npz, test_data_npz, test_class_indices, batch_size=batch_size, shuffle_buffer=20000
        )
        
        train_dataset = data_info['train_dataset']
        
        # 数据预处理
        train_data = self.scaler.fit_transform(train_data)
        test_data = self.scaler.transform(test_data)
        
        # 转换为tensorflow张量
        train_data = tf.constant(train_data, dtype=tf.float32)
        train_label = tf.constant(train_label, dtype=tf.int32)
        
        # 使用真实的属性矩阵（从Excel文件加载）
        # 需要读取完整的属性矩阵，包含所有15个类别
        attribute_matrix_df = pd.read_excel('./attribute_matrix.xlsx', index_col='no')
        self.attribute_matrix = tf.constant(attribute_matrix_df.values.astype(np.float32))
        
        print(f"开始优化版训练，测试类别: {test_class_indices}")
        print(f"批处理大小: {batch_size}")
        print(f"混合精度训练: 已启用")
        print(f"难负例挖掘: {'已启用' if self.use_hard_negative else '已禁用'}")
        
        # 训练循环 - 使用优化的数据集
        for epoch in range(epochs):
            epoch_d_loss = 0
            epoch_g_loss = 0
            epoch_c_loss = 0
            epoch_ae_loss = 0
            num_batches = 0
            
            for batch_data in train_dataset:
                batch_features, batch_attributes, batch_labels = batch_data
                current_batch_size = tf.shape(batch_features)[0]
                num_batches += 1
                
                # 监控GPU使用情况（每100个批次）
                if num_batches % 100 == 0:
                    print(f"Epoch {epoch}, Batch {num_batches} - 监控GPU状态:")
                    monitor_gpu()
                
                with tf.device(self.device):
                    # 修正batch_labels的形状，从 (batch_size, 1) 到 (batch_size,)
                    if len(batch_labels.shape) > 1:
                        batch_labels = tf.squeeze(batch_labels, axis=-1)

                    # 训练自编码器
                    with tf.GradientTape() as ae_tape:
                        reconstructed = self.autoencoder(batch_features)
                        ae_loss = tf.reduce_mean(tf.square(batch_features - reconstructed))
                    
                    ae_gradients = ae_tape.gradient(ae_loss, self.autoencoder.trainable_variables)
                    self.optimizer_ae.apply_gradients(zip(ae_gradients, self.autoencoder.trainable_variables))
                    
                    # 生成噪声和获取属性
                    noise = tf.random.normal([current_batch_size, self.latent_dim])
                    
                    # 训练判别器
                    with tf.GradientTape() as d_tape:
                        generated_features = self.generator([noise, batch_attributes])
                        
                        real_validity = self.discriminator(self.encoder(batch_features))
                        fake_validity = self.discriminator(generated_features)
                        
                        d_loss_real = tf.reduce_mean(tf.square(real_validity - 1))
                        d_loss_fake = tf.reduce_mean(tf.square(fake_validity))
                        d_loss = (d_loss_real + d_loss_fake) / 2
                    
                    d_gradients = d_tape.gradient(d_loss, self.discriminator.trainable_variables)
                    self.optimizer_d.apply_gradients(zip(d_gradients, self.discriminator.trainable_variables))
                    
                    # 训练生成器和分类器
                    with tf.GradientTape() as g_tape, tf.GradientTape() as c_tape:
                        generated_features = self.generator([noise, batch_attributes])
                        
                        # 对抗损失
                        fake_validity = self.discriminator(generated_features)
                        g_loss_adv = tf.reduce_mean(tf.square(fake_validity - 1))
                        
                        # 分类损失
                        pred_attributes = self.classifier(generated_features)
                        c_loss = tf.reduce_mean(tf.square(pred_attributes - batch_attributes))
                        
                        # Triplet损失（使用优化的难负例采样）
                        if self.use_hard_negative:
                            # 使用难负例挖掘
                            negative_labels = self.hard_negative_sampling(batch_labels, self.attribute_matrix)
                        else:
                            # 传统随机负例采样
                            negative_labels = tf.random.shuffle(batch_labels)
                            # 确保负样本与锚点不同
                            same_mask = tf.equal(batch_labels, negative_labels)
                            different_labels = tf.random.uniform([current_batch_size], maxval=self.num_classes, dtype=tf.int32)
                            negative_labels = tf.where(same_mask, different_labels, negative_labels)
                        
                        negative_attributes = tf.gather(self.attribute_matrix, negative_labels)
                        negative_noise = tf.random.normal([current_batch_size, self.latent_dim])
                        negative_features = self.generator([negative_noise, negative_attributes])
                        
                        # 计算triplet损失
                        triplet_loss = self.triplet_loss(
                            anchor=self.encoder(batch_features),  # 真实特征作为anchor
                            positive=generated_features,  # 生成的同类特征作为positive
                            negative=negative_features  # 生成的负类特征作为negative
                        )
                        
                        # 循环一致性损失
                        reconstructed_features = self.autoencoder(generated_features)
                        cycle_loss = self.cycle_rank_loss(
                            original_features=self.encoder(batch_features),
                            generated_features=generated_features,
                            reconstructed_features=self.encoder(reconstructed_features)
                        )
                        
                        # 互信息损失
                        mi_loss = self.mi_penalty_loss(generated_features, noise)
                        
                        # 总损失
                        g_loss = (self.lambda_adversarial * g_loss_adv + 
                                 self.lambda_triplet * triplet_loss + 
                                 self.lambda_cycle_rank * cycle_loss +
                                 self.lambda_mi * mi_loss)
                    
                    # 更新生成器
                    g_gradients = g_tape.gradient(g_loss, self.generator.trainable_variables)
                    self.optimizer_g.apply_gradients(zip(g_gradients, self.generator.trainable_variables))
                    
                    # 更新分类器
                    c_gradients = c_tape.gradient(c_loss, self.classifier.trainable_variables)
                    self.optimizer_c.apply_gradients(zip(c_gradients, self.classifier.trainable_variables))
                
                # 累计损失
                epoch_d_loss += d_loss.numpy()
                epoch_g_loss += g_loss.numpy()
                epoch_c_loss += c_loss.numpy()
                epoch_ae_loss += ae_loss.numpy()
                
                # 每10个批次打印一次进度
                if num_batches % 10 == 0:
                    print(f"[Epoch {epoch}/{epochs}][Batch {num_batches}] D损失: {d_loss:.4f}, G损失: {g_loss:.4f}, C损失: {c_loss:.4f}, AE损失: {ae_loss:.4f}")
            
            # 每个epoch结束后计算平均损失
            if num_batches > 0:
                avg_d_loss = epoch_d_loss / num_batches
                avg_g_loss = epoch_g_loss / num_batches
                avg_c_loss = epoch_c_loss / num_batches
                avg_ae_loss = epoch_ae_loss / num_batches
                
                print(f"[Epoch {epoch}/{epochs}] 平均损失 - D: {avg_d_loss:.4f}, G: {avg_g_loss:.4f}, C: {avg_c_loss:.4f}, AE: {avg_ae_loss:.4f}")
                
                if log_file:
                    log_file.write(f"[Epoch {epoch}/{epochs}] 平均损失 - D: {avg_d_loss:.4f}, G: {avg_g_loss:.4f}, C: {avg_c_loss:.4f}, AE: {avg_ae_loss:.4f}\n")
                    log_file.flush()
            
            # 每10个epoch进行一次评估
            if epoch % 10 == 0:
                try:
                    accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis(
                        2000, test_data, test_attr_label, self.autoencoder, self.generator, self.classifier)
                    
                    print(f"[Epoch {epoch}] 准确率 - LSVM: {accuracy_lsvm:.4f}, NRF: {accuracy_nrf:.4f}, PNB: {accuracy_pnb:.4f}, MLP: {accuracy_mlp:.4f}")
                    
                    if log_file:
                        log_file.write(f"[Epoch {epoch}] 准确率 - LSVM: {accuracy_lsvm:.4f}, NRF: {accuracy_nrf:.4f}, PNB: {accuracy_pnb:.4f}, MLP: {accuracy_mlp:.4f}\n")
                        log_file.flush()
                except Exception as e:
                    print(f"评估时发生错误: {e}")
        
        print("优化版训练完成!")
        
        return {
            'best_accuracy': 0.0,  # 这里可以根据实际评估结果返回
            'avg_d_loss': avg_d_loss if 'avg_d_loss' in locals() else 0.0,
            'avg_g_loss': avg_g_loss if 'avg_g_loss' in locals() else 0.0,
            'avg_c_loss': avg_c_loss if 'avg_c_loss' in locals() else 0.0,
            'avg_ae_loss': avg_ae_loss if 'avg_ae_loss' in locals() else 0.0,
        }


def run_optimized_hard_negative_experiment(group_name='E', epochs=500, batch_size=512):
    """运行优化版难负例挖掘实验"""
    # 定义不同组别的测试类别
    group_configs = {
        'A': [1, 5, 14],
        'B': [4, 7, 10], 
        'C': [8, 11, 12],
        'D': [2, 3, 15],
        'E': [6, 9, 13]
    }
    
    test_class_indices = group_configs[group_name]
    
    # 创建日志文件名
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    log_file = f"结果/{timestamp}_optimized_triplet_hard_negative_Group{group_name}.md"
    
    # 确保结果目录存在
    os.makedirs("结果", exist_ok=True)
    
    # 写入日志头部
    with open(log_file, 'w') as f:
        f.write(f"# 优化版训练日志 (Triplet架构 + 难负例挖掘 - Group {group_name})\n\n")
        f.write(f"**开始时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**实验组别**: Group {group_name}\n")
        f.write(f"**测试类别**: {test_class_indices}\n")
        f.write(f"**优化特性**: 数据流水线优化 + 混合精度 + 增大批处理\n")
        f.write(f"**批处理大小**: {batch_size}\n")
        f.write(f"**改进策略**: 难负例挖掘 (Hard Negative Mining)\n")
        f.write(f"**难负例比例**: 70% (动态调整)\n")
        f.write(f"**属性相似度**: 余弦相似度\n")
        f.write(f"**候选负例数**: 5\n\n")
        f.write("---\n\n")
    
    # 创建并训练模型
    model = Zero_shot_Hard_Negative_Optimized(use_optimized_pipeline=True)
    
    print("开始训练优化版带有难负例挖掘的Triplet ACGAN模型...")
    print(f"实验组别: Group {group_name}")
    print(f"测试类别: {test_class_indices}")
    print(f"优化特性: 数据流水线 + 混合精度训练 + 大批处理 + GPU监控")
    print(f"日志文件: {log_file}")
    
    # 开始训练
    with open(log_file, 'a') as f:
        results = model.train_optimized(
            epochs=epochs, 
            batch_size=batch_size, 
            log_file=f,
            test_class_indices=test_class_indices
        )
    
    print(f"优化版训练完成！日志文件: {log_file}")
    return results

if __name__ == "__main__":
    import sys
    
    # 支持命令行参数选择组别
    if len(sys.argv) > 1:
        group_name = sys.argv[1].upper()
        print(f"使用命令行参数指定的组别: {group_name}")
    else:
        # 默认使用组别E（表现最差的组别）
        group_name = 'E'
        print("使用默认组别: E (可通过命令行参数指定其他组别，如: python acgan_triplet_hard_negative_optimized.py A)")
    
    print(f"开始运行 Group {group_name} 的优化版难负例挖掘实验")
    print("优化特性: 数据流水线 + 混合精度训练 + 大批处理 + GPU监控 + 难负例挖掘")
    results = run_optimized_hard_negative_experiment(group_name=group_name, epochs=500, batch_size=512)
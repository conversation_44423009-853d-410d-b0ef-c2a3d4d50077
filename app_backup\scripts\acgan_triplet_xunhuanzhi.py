import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,LayerNormalization,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
#import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler


# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    # 限制TensorFlow只使用第一个GPU
    tf.config.set_visible_devices(gpus[0], 'GPU')
    # 设置显存按需增长
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    # 显存增长必须在GPU初始化之前设置
    print(e)


def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = LayerNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = LayerNormalization()(y)
    
    # 如果输入和输出维度不同，需要一个线性投影
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        # input_shape is (batch_size, feature_dim)
        feature_dim = input_shape[-1]
        self.query = Dense(feature_dim // 8, use_bias=False)
        self.key = Dense(feature_dim // 8, use_bias=False)
        self.value = Dense(feature_dim, use_bias=False)
        self.gamma = self.add_weight(name='gamma', shape=[1], initializer='zeros')
        super(SelfAttention, self).build(input_shape)

    def call(self, x):
        # Reshape for matrix multiplication
        # Temporarily add a "sequence length" of 1
        # x_reshaped shape: (batch_size, 1, feature_dim)
        x_reshaped = K.expand_dims(x, axis=1)

        # Q, K, V projections
        q = self.query(x_reshaped)  # (batch_size, 1, feature_dim/8)
        k = self.key(x_reshaped)    # (batch_size, 1, feature_dim/8)
        v = self.value(x_reshaped)  # (batch_size, 1, feature_dim)

        # Attention scores
        attention_scores = K.batch_dot(q, k, axes=[2, 2]) # (batch_size, 1, 1)
        attention_probs = K.softmax(attention_scores)

        # Apply attention
        context = K.batch_dot(attention_probs, v) # (batch_size, 1, feature_dim)
        
        # Remove the temporary dimension
        context = K.squeeze(context, axis=1)

        # Add back to original input (residual connection)
        return x + self.gamma * context
        
class Zero_shot():
    def __init__(self):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        self.lambda_cla = 10 
        self.lambda_triplet = 10 
        self.lambda_cms = 10  # 比较损失权重
        self.lambda_crl = 0.1 
        
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 0.4
        
        # === Center Loss 相关初始化 ===
        self.lambda_center = 0.5  # 中心损失的权重，可以作为超参数调整
        self.centers = None  # 类中心变量，将在train函数中初始化
        self.seen_class_map = None  # 类别ID到索引的映射，将在train函数中初始化
        self.center_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)  # 中心优化器
        self.class_to_idx_table = None  # 哈希表，将在train函数中初始化
        
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For triplet loss
        self.comparator_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For comparator
        
        self.autoencoder= self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c= self.build_classifier()
        # self.m = self.build_comparator()  # 比较器网络
        
    def build_autoencoder(self):
      
      sample = Input(shape=self.sample_shape)     
     
      a0=sample

      # Encoder
      a1=Dense(100)(a0)
      a1=LeakyReLU(alpha=0.2)(a1)
      a1=BatchNormalization()(a1)

      a2=Dense(200)(a1)
      a2=LeakyReLU(alpha=0.2)(a2)
      a2=BatchNormalization()(a2)

      a3=Dense(256)(a2)
      a3=LeakyReLU(alpha=0.2)(a3)
      a3=BatchNormalization()(a3)
      feature=a3

      # Decoder
      a4=Dense(200)(feature)
      a4=LeakyReLU(alpha=0.2)(a4)
      a4=BatchNormalization()(a4)

      a5=Dense(100)(a4)
      a5=LeakyReLU(alpha=0.2)(a5)
      a5=BatchNormalization()(a5)

      a6=Dense(52)(a5)
      a6=LeakyReLU(alpha=0.2)(a6)
      a6=BatchNormalization()(a6)
      output_sample=a6

      # Autoencoder Model
      autoencoder = Model(sample,[feature, output_sample])
      # We only need the encoder part for triplet loss feature extraction
      self.encoder = Model(sample, feature)
      return autoencoder    
        
    def build_discriminator(self):
        
        sample_input = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')

        label_embedding = Dense(self.feature_dim)(attribute)
        d_input = multiply([sample_input, label_embedding])

        d1 = SpectralNormalization(Dense(256))(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        
        d2 = SpectralNormalization(Dense(128))(d1)
        d2 = LeakyReLU(alpha=0.2)(d2)

        validity = SpectralNormalization(Dense(1))(d2)

        return Model([sample_input,attribute],validity)

    def build_generator(self):
      
      noise = Input(shape=self.noise_shape)
      attribute = Input(shape=(20,), dtype='float32')
      
      noise_embedding = Flatten()(noise)
      attribute_embedding = Dense(self.latent_dim)(attribute)
      
      g_input = concatenate([noise_embedding, attribute_embedding])

      g1 = Dense(128)(g_input)
      g1 = LeakyReLU(alpha=0.2)(g1)
      g1 = BatchNormalization()(g1)

      g2 = residual_block(g1, 256) 
      g3 = residual_block(g2, 256) 
      
      g3_attention = SelfAttention()(g3)
      
      generated_feature = Dense(256)(g3_attention)
      generated_feature = BatchNormalization()(generated_feature)

      return Model([noise,attribute],generated_feature)
    
    def build_classifier(self):
        
        sample = Input(shape=self.feature_shape)

        c0=sample
        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)
        
        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)
        hidden_ouput=c2
               
        c3 = Dense(20,activation="sigmoid")(c2)
        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])
    
    def triplet_loss(self, anchor, positive, negative, margin=None):
        """
        三元组损失函数
        
        Args:
            anchor: 锚点特征
            positive: 正样本特征
            negative: 负样本特征
            margin: 边界值，如果为None则使用self.triplet_margin
        """
        if margin is None:
            margin = self.triplet_margin
        
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def wasserstein_loss(self, y_true, y_pred):
            return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def center_loss(self, features, labels):
        """
        计算中心损失 - 使用哈希表的高效版本
        features: (batch_size, feature_dim) 的特征张量
        labels: (batch_size,) 的类别标签ID（如1,2,3...）
        """
        # 将labels转换为tensor并确保类型正确
        labels_tensor = tf.cast(labels, dtype=tf.int64)
        
        # 使用哈希表高效查找索引
        if hasattr(self, 'class_to_idx_table') and self.class_to_idx_table is not None:
            center_indices = self.class_to_idx_table.lookup(labels_tensor)
        else:
            # 如果哈希表不可用，则使用直接映射作为备选方案
            def get_index(y):
                y_val = int(y.numpy())
                if hasattr(self, 'seen_class_map') and self.seen_class_map is not None and y_val in self.seen_class_map:
                    return tf.constant(self.seen_class_map[y_val], dtype=tf.int32)
                else:
                    return tf.constant(-1, dtype=tf.int32)
                
            center_indices = tf.map_fn(
                lambda x: tf.py_function(get_index, [x], tf.int32),
                labels_tensor,
                dtype=tf.int32
            )
        
        # 从centers变量中取出对应批次样本的中心
        batch_centers = tf.gather(self.centers, center_indices)
        
        # 计算特征与中心之间的欧氏距离平方
        distances = tf.reduce_sum(tf.square(features - batch_centers), axis=-1)
        
        return tf.reduce_mean(distances)  
    
    def classification_loss(self,current_batch_features,y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(
                y_true, pred_attribute)
        
        mi_penalty=0    
        if self.bound == True:    
          mi_penalty = self.mi_penalty_loss(
              current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def cycle_rank_loss(self, original_fake, reconstructed_fake, unsimilar_fake, margin=0.3):
        """
        基于距离的循环秩损失。
        目标：让重构特征(positive)比不相似特征(negative)更靠近原始生成特征(anchor)。
        
        Args:
            original_fake: 原始生成的特征 (anchor)
            reconstructed_fake: 重构的特征 (positive)
            unsimilar_fake: 不相似的特征 (negative)
            margin: CRL专用的margin值，通常比triplet loss的margin更小
        """
        # 计算正样本距离（原始特征与重构特征之间的距离）
        pos_dist = tf.reduce_sum(tf.square(original_fake - reconstructed_fake), axis=-1)
        
        # 计算负样本距离（原始特征与不相似特征之间的距离）
        neg_dist = tf.reduce_sum(tf.square(original_fake - unsimilar_fake), axis=-1)
        
        # 计算基本损失：正样本距离应该小于负样本距离减去margin
        basic_loss = pos_dist - neg_dist + margin
        
        # 取最大值确保损失非负，并计算平均值
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        
        return loss
    
    def train(self, epochs=2000, batch_size=256, log_file=None, test_class_indices=None):
        start_time = datetime.datetime.now()
        
        accuracy_list_1=[]
        accuracy_list_2=[]
        accuracy_list_3=[]
        accuracy_list_4=[]
        
        valid = -np.ones((batch_size,1))
        fake = np.ones((batch_size,1))
        
        # 加载数据
        PATH_train = '/app/data/dataset_train_case1.npz'
        PATH_test = '/app/data/dataset_test_case1.npz'
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        # 使用传入的测试类别，如果没有传入则默认使用E组
        if test_class_indices is None:
            test_class_indices = [9, 13, 15]  # 默认E组 (1-based)
        
        # 根据测试类别，确定训练类别（Seen classes）
        all_class_indices = list(range(1, 16)) # 1-15
        seen_class_indices = [i for i in all_class_indices if i not in test_class_indices]
        
        # === 初始化Center Loss相关变量 ===
        self.seen_class_map = {class_id: i for i, class_id in enumerate(seen_class_indices)}
        num_seen_classes = len(seen_class_indices)
        self.centers = tf.Variable(
            tf.zeros([num_seen_classes, self.feature_dim]), 
            trainable=True,
            name='centers'
        )
        
        # 创建哈希表用于高效查找
        keys_tensor = tf.constant(list(self.seen_class_map.keys()), dtype=tf.int64)
        vals_tensor = tf.constant(list(self.seen_class_map.values()), dtype=tf.int32)
        self.class_to_idx_table = tf.lookup.StaticHashTable(
            tf.lookup.KeyValueTensorInitializer(keys_tensor, vals_tensor),
            default_value=-1  # 如果找不到，返回-1
        )
        
        # ===============================
        
        # This part remains mostly the same, but we will use the labels to find pos/neg samples
        train_X_by_class = {i: train_data[f'training_samples_{i}'] for i in seen_class_indices}
        train_Y_by_class = {i: train_data[f'training_attribute_{i}'] for i in seen_class_indices}

        all_train_X = np.concatenate([v for k, v in train_X_by_class.items()])
        all_train_Y = np.concatenate([v for k, v in train_Y_by_class.items()])
        all_train_labels = np.concatenate([np.full(len(v), k) for k, v in train_X_by_class.items()])

        test_X_by_class = {i: test_data[f'testing_samples_{i}'] for i in test_class_indices}
        test_Y_by_class = {i: test_data[f'testing_attribute_{i}'] for i in test_class_indices}

        test_X = np.concatenate([v for k, v in test_X_by_class.items()])
        test_Y = np.concatenate([v for k, v in test_Y_by_class.items()])
        test_classlabel = np.concatenate([np.full(len(v), k) for k, v in test_X_by_class.items()])

        scaler = MinMaxScaler()
        all_train_X = scaler.fit_transform(all_train_X)
        test_X = scaler.transform(test_X)

        # Re-organize scaled data back into class dictionaries
        current_pos = 0
        for i in seen_class_indices:
            class_len = len(train_X_by_class[i])
            train_X_by_class[i] = all_train_X[current_pos : current_pos + class_len]
            current_pos += class_len

        traindata=all_train_X
        train_attributelabel=all_train_Y
        train_classlabel = all_train_labels
        
        testdata=test_X
        test_attributelabel=test_Y
       
        num_batches=int(traindata.shape[0]/batch_size)
               
        for epoch in range(epochs):
            
            for batch_i in range(num_batches):
                
                start_i =batch_i * batch_size
                end_i=(batch_i + 1) * batch_size
                
                train_x=traindata[start_i:end_i]
                train_y=train_attributelabel[start_i:end_i] 
                train_labels = train_classlabel[start_i:end_i]
                                                                               
                # Autoencoder and Classifier Training (Same as before)
                self.autoencoder.trainable = True
                self.c.trainable = True # Train C together with AE now
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                  feature, output_sample=self.autoencoder(train_x)
                  autoencoder_loss=mean_squared_error(train_x,output_sample)      

                  hidden_ouput_c,predict_attribute_c=self.c(feature)
                  c_loss=self.classification_loss(feature,train_y, hidden_ouput_c, predict_attribute_c)

                  total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c

                # Triplet Loss Metric Learning + Center Loss
                self.autoencoder.trainable = True # Encoder is part of metric learning
                # self.c.trainable = False  # 明确这一点，不在这个步骤更新分类器
                
                # Sample triplets
                anchor_samples = train_x
                positive_samples = []
                negative_samples = []
                for label in train_labels:
                    pos_class_samples = train_X_by_class[label]
                    pos_idx = np.random.choice(len(pos_class_samples))
                    positive_samples.append(pos_class_samples[pos_idx])
                    
                    neg_class = np.random.choice([c for c in seen_class_indices if c != label])
                    neg_class_samples = train_X_by_class[neg_class]
                    neg_idx = np.random.choice(len(neg_class_samples))
                    negative_samples.append(neg_class_samples[neg_idx])

                positive_samples = np.array(positive_samples)
                negative_samples = np.array(negative_samples)

                with tf.GradientTape(persistent=True) as tape_metric:
                    # 获取特征
                    anchor_features = self.encoder(anchor_samples)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    # 1. 计算 Triplet Loss
                    triplet_loss_val = self.triplet_loss(anchor_features, positive_features, negative_features)
                    
                    # 2. 计算 Center Loss
                    center_loss_val = self.center_loss(anchor_features, train_labels)

                    # 3. 组合损失 - 只用来更新encoder
                    total_encoder_loss = triplet_loss_val + self.lambda_center * center_loss_val

                # 更新 Encoder - 使用专门的m_optimizer
                grads_encoder = tape_metric.gradient(total_encoder_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_encoder, self.encoder.trainable_weights))

                # 更新 Centers
                grads_centers = tape_metric.gradient(center_loss_val, [self.centers])
                self.center_optimizer.apply_gradients(zip(grads_centers, [self.centers]))

                del tape_metric

                # Discriminator Training (Same as before)
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False

                for _ in range(self.n_critic):
                  with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                    fake_feature = self.g([noise,train_y])
                    real_feature = self.encoder(train_x)
        
                    real_validity = self.d([real_feature,train_y])
                    fake_validity = self.d([fake_feature,train_y])  
                                           
                    d_loss_real = tf.reduce_mean(tf.nn.relu(1.0 - real_validity))
                    d_loss_fake = tf.reduce_mean(tf.nn.relu(1.0 + fake_validity))
                    d_loss = d_loss_real + d_loss_fake
                  
                  grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                  self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))

                # Generator Training
                self.d.trainable = False               
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                  noise_g = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                  Fake_feature_g = self.g([noise_g,train_y])
                  Fake_validity_g = self.d([Fake_feature_g,train_y])
                  adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
            
                  fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                  classification_loss = self.classification_loss(Fake_feature_g,train_y, fake_hidden_ouput_g, Fake_classification_g)
                  
                  # Triplet loss for Generator
                  g_anchor_features = Fake_feature_g
                  g_positive_features = self.encoder(positive_samples) # Use same positive samples
                  g_negative_features = self.encoder(negative_samples) # Use same negative samples
                  triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                  
                  # === Center Loss for Generator ===
                  # 让生成的特征靠近其对应类别的真实中心
                  center_loss_g = self.center_loss(g_anchor_features, train_labels)
                  
                  # === 新增: 自洽性重构损失 (Self-Consistency Loss) ===
                  # 随机采样一批 unseen 属性
                  unseen_attributes_for_rec = []
                  for _ in range(batch_size):
                      test_class_id = np.random.choice(test_class_indices)
                      unseen_attr = test_Y_by_class[test_class_id][np.random.randint(len(test_Y_by_class[test_class_id]))]
                      unseen_attributes_for_rec.append(unseen_attr)
                  unseen_attributes_for_rec = np.array(unseen_attributes_for_rec)

                  # 1. 生成unseen特征
                  noise_rec = tf.random.normal(shape=(batch_size, self.latent_dim, 1))
                  fake_unseen_feature = self.g([noise_rec, unseen_attributes_for_rec])
                  
                  # 2. 从unseen特征重构属性
                  _, reconstructed_unseen_attributes = self.c(fake_unseen_feature)
                  
                  # 3. 计算重构损失 (MSE)
                  reconstruction_loss = tf.reduce_mean(tf.square(unseen_attributes_for_rec - reconstructed_unseen_attributes))
                  
                  # === 循环秩损失 (CRL) ===
                  cycle_rank_loss_val = 0.0  # 默认值
                  if self.crl == True:
                      # a. 重构特征 (Reconstructed Feature)
                      # 使用分类器预测的、可能带有噪声的属性来重构
                      reconstructed_feature = self.g([noise_g, Fake_classification_g])
                      
                      # b. 不相似特征 (Unsimilar Feature)
                      # 随机采样一个不同的属性来生成
                      shuffled_indices = tf.random.shuffle(tf.range(batch_size))
                      unsimilar_attributes = tf.gather(train_y, shuffled_indices)
                      unsimilar_fake_feature = self.g([noise_g, unsimilar_attributes])
                      
                      # c. 计算CRL
                      cycle_rank_loss_val = self.cycle_rank_loss(
                          original_fake=Fake_feature_g,
                          reconstructed_fake=reconstructed_feature,
                          unsimilar_fake=unsimilar_fake_feature,
                          margin=0.2  # CRL专用的较小margin
                      )
                  
                  # 组合最终的总损失 - 加入重构损失
                  lambda_rec = 10.0  # 重构损失权重
                  total_loss = (adversarial_loss + 
                               self.lambda_cla * classification_loss + 
                               self.lambda_triplet * triplet_loss_g + 
                               self.lambda_center * center_loss_g + 
                               self.lambda_crl * cycle_rank_loss_val +
                               lambda_rec * reconstruction_loss)
                          
                self.c.trainable = True  # 确保C是可训练的
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights + self.c.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights + self.c.trainable_weights))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                print ("[Epoch %d/%d][Batch %d/%d][AE+C loss: %f][Triplet loss: %f][Center loss: %f][D loss: %f][G loss %05f ]time: %s " \
                 % (epoch, epochs,
                   batch_i, num_batches,
                   tf.reduce_mean(total_ac_loss), 
                   triplet_loss_val,
                   center_loss_val,
                   d_loss,
                   tf.reduce_mean(total_loss),                                                                                                              
                   elapsed_time))
        
            if epoch % 1 == 0:
                accuracy_lsvm,accuracy_nrf,accuracy_pnb,accuracy_mlp = feature_generation_and_diagnosis(2000,testdata,test_attributelabel,self.autoencoder,self.g, self.c, test_class_indices)  

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                  %(epoch, epochs,max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)))
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()
            
        best_accuracy = max([max(accuracy_list_1),max(accuracy_list_2),max(accuracy_list_3),max(accuracy_list_4)])
        print('finished! best_acc:{:.4f}'.format(best_accuracy))
        if log_file:
            log_file.write(f'finished! best_acc:{best_accuracy:.4f}\n')
            log_file.flush()
                
if __name__ == '__main__':
    # =============================================================
    # 配置区域：只需要修改这里的TARGET_GROUP即可切换实验组别
    # =============================================================
    TARGET_GROUP = 'B'#可选: 'A', 'B', 'C', 'D', 'E'
    # =============================================================
    
    # 分组配置
    GROUP_CONFIGS = {
        'A': [1, 6, 14],   # 测试类别: [1, 6, 14] 
        'B': [4, 7, 10],   # 测试类别: [4, 7, 10]
        'C': [8, 11, 12],  # 测试类别: [8, 11, 12]
        'D': [2, 3, 5],    # 测试类别: [2, 3, 5]
        'E': [9, 13, 15],  # 测试类别: [9, 13, 15]
    }
    
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + f"_triplet_xunhuanzhi_Group{TARGET_GROUP}.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"开始运行 Group {TARGET_GROUP} 实验，测试类别: {GROUP_CONFIGS[TARGET_GROUP]}")
    print(f"训练开始，日志将被记录到: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 训练日志 (Triplet架构 - Group {TARGET_GROUP})\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**实验组别**: Group {TARGET_GROUP}\n")
        log_file.write(f"**测试类别**: {GROUP_CONFIGS[TARGET_GROUP]}\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = Zero_shot()
        gan.train(epochs=2000, batch_size=256, log_file=log_file, test_class_indices=GROUP_CONFIGS[TARGET_GROUP])

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")

    print(f"训练完成，日志已保存至: {log_filename}") 

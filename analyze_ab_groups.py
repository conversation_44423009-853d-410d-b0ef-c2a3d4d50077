#!/usr/bin/env python3
"""
ASDCGAN A组和B组实验结果分析脚本
分析训练日志、准确率、损失函数收敛情况等关键指标
"""

import re
import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd

def parse_training_log(log_file):
    """解析训练日志文件"""
    data = {
        'epochs': [],
        'g_loss': [],
        'd_loss': [],
        'cycle_loss': [],
        'accuracy': [],
        'quality': []
    }
    
    if not Path(log_file).exists():
        print(f"Warning: {log_file} not found")
        return data
    
    with open(log_file, 'r', encoding='utf-8') as f:
        for line in f:
            # 匹配训练日志行
            match = re.search(r'Epoch (\d+): G_loss=([\d.]+), D_loss=([\d.]+), Cycle=([\d.]+), Best_Accuracy=([\d.]+)%, Quality=([\d.]+)', line)
            if match:
                epoch, g_loss, d_loss, cycle_loss, accuracy, quality = match.groups()
                data['epochs'].append(int(epoch))
                data['g_loss'].append(float(g_loss))
                data['d_loss'].append(float(d_loss))
                data['cycle_loss'].append(float(cycle_loss))
                data['accuracy'].append(float(accuracy))
                data['quality'].append(float(quality))
    
    return data

def analyze_training_stability(data, group_name):
    """分析训练稳定性"""
    if not data['epochs']:
        return {}
    
    g_loss = np.array(data['g_loss'])
    d_loss = np.array(data['d_loss'])
    accuracy = np.array(data['accuracy'])
    
    # 计算损失函数的方差和变化趋势
    g_loss_var = np.var(g_loss)
    d_loss_var = np.var(d_loss)
    
    # 计算最后100轮的平均损失（如果有足够数据）
    window = min(100, len(g_loss))
    if window > 0:
        recent_g_loss = np.mean(g_loss[-window:])
        recent_d_loss = np.mean(d_loss[-window:])
        recent_accuracy = np.mean(accuracy[-window:])
    else:
        recent_g_loss = recent_d_loss = recent_accuracy = 0
    
    # 检测损失函数爆炸
    g_loss_explosion = np.any(g_loss > 1000)
    d_loss_explosion = np.any(d_loss > 1.0)
    
    # 检测收敛性（最后10%轮次的标准差）
    final_portion = max(1, len(g_loss) // 10)
    if final_portion > 0:
        final_g_std = np.std(g_loss[-final_portion:])
        final_d_std = np.std(d_loss[-final_portion:])
        convergence_score = (final_g_std + final_d_std) / 2
    else:
        convergence_score = float('inf')
    
    return {
        'group': group_name,
        'total_epochs': len(data['epochs']),
        'final_accuracy': data['accuracy'][-1] if data['accuracy'] else 0,
        'max_accuracy': max(data['accuracy']) if data['accuracy'] else 0,
        'g_loss_variance': g_loss_var,
        'd_loss_variance': d_loss_var,
        'recent_g_loss': recent_g_loss,
        'recent_d_loss': recent_d_loss,
        'recent_accuracy': recent_accuracy,
        'g_loss_explosion': g_loss_explosion,
        'd_loss_explosion': d_loss_explosion,
        'convergence_score': convergence_score,
        'final_g_loss': data['g_loss'][-1] if data['g_loss'] else 0,
        'final_d_loss': data['d_loss'][-1] if data['d_loss'] else 0
    }

def detect_training_problems(stats):
    """检测训练问题"""
    problems = []
    
    if stats['g_loss_explosion']:
        problems.append("生成器损失爆炸 (>1000)")
    
    if stats['d_loss_explosion']:
        problems.append("判别器损失爆炸 (>1.0)")
    
    if stats['convergence_score'] > 100:
        problems.append("训练不稳定，损失函数振荡严重")
    
    if stats['final_accuracy'] < 40:
        problems.append("准确率过低 (<40%)")
    
    if stats['recent_g_loss'] > 800:
        problems.append("生成器损失持续偏高")
    
    if stats['recent_d_loss'] < 0.01:
        problems.append("判别器过强，可能导致模式崩塌")
    
    if abs(stats['final_accuracy'] - stats['max_accuracy']) > 5:
        problems.append("准确率不稳定，存在退化现象")
    
    return problems

def plot_training_curves(data_a, data_b):
    """绘制训练曲线对比图"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('A组 vs B组 训练曲线对比', fontsize=16, fontweight='bold')
    
    # 生成器损失对比
    axes[0, 0].plot(data_a['epochs'], data_a['g_loss'], label='A组', alpha=0.7, color='blue')
    axes[0, 0].plot(data_b['epochs'], data_b['g_loss'], label='B组', alpha=0.7, color='red')
    axes[0, 0].set_title('生成器损失 (Generator Loss)')
    axes[0, 0].set_xlabel('Epochs')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 判别器损失对比
    axes[0, 1].plot(data_a['epochs'], data_a['d_loss'], label='A组', alpha=0.7, color='blue')
    axes[0, 1].plot(data_b['epochs'], data_b['d_loss'], label='B组', alpha=0.7, color='red')
    axes[0, 1].set_title('判别器损失 (Discriminator Loss)')
    axes[0, 1].set_xlabel('Epochs')
    axes[0, 1].set_ylabel('Loss')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 准确率对比
    axes[1, 0].plot(data_a['epochs'], data_a['accuracy'], label='A组', alpha=0.7, color='blue')
    axes[1, 0].plot(data_b['epochs'], data_b['accuracy'], label='B组', alpha=0.7, color='red')
    axes[1, 0].set_title('分类准确率 (Classification Accuracy)')
    axes[1, 0].set_xlabel('Epochs')
    axes[1, 0].set_ylabel('Accuracy (%)')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 循环一致性损失对比
    axes[1, 1].plot(data_a['epochs'], data_a['cycle_loss'], label='A组', alpha=0.7, color='blue')
    axes[1, 1].plot(data_b['epochs'], data_b['cycle_loss'], label='B组', alpha=0.7, color='red')
    axes[1, 1].set_title('循环一致性损失 (Cycle Consistency Loss)')
    axes[1, 1].set_xlabel('Epochs')
    axes[1, 1].set_ylabel('Loss')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('/home/<USER>/hmt/ACGAN-FG-main/ab_groups_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("📊 训练曲线对比图已保存: ab_groups_comparison.png")

def generate_report(stats_a, stats_b, problems_a, problems_b):
    """生成分析报告"""
    report = """
# ASDCGAN A组和B组实验结果分析报告

## 📊 总体性能对比

| 指标 | A组 | B组 | 差异 |
|------|-----|-----|------|
| 最终准确率 | {:.2f}% | {:.2f}% | {:.2f}% |
| 最高准确率 | {:.2f}% | {:.2f}% | {:.2f}% |
| 训练轮次 | {} | {} | {} |
| 最终生成器损失 | {:.2f} | {:.2f} | {:.2f} |
| 最终判别器损失 | {:.4f} | {:.4f} | {:.4f} |

## 🎯 训练稳定性分析

### A组 (测试类别: [1, 6, 14])
- **收敛评分**: {:.2f} (越低越好)
- **生成器损失方差**: {:.2f}
- **判别器损失方差**: {:.6f}
- **近期平均准确率**: {:.2f}%

### B组 (测试类别: [4, 7, 10])
- **收敛评分**: {:.2f} (越低越好)
- **生成器损失方差**: {:.2f}
- **判别器损失方差**: {:.6f}
- **近期平均准确率**: {:.2f}%

## ⚠️ 发现的问题

### A组问题:
{}

### B组问题:
{}

## 🔍 深度分析

### 1. 准确率分析
- **A组优势**: {}
- **B组优势**: {}

### 2. 训练效率分析
- **收敛速度**: {}
- **训练稳定性**: {}

### 3. 损失函数分析
- **生成器性能**: {}
- **判别器性能**: {}

## 📈 改进建议

### 针对A组:
{}

### 针对B组:
{}

## 📋 结论

{}
""".format(
        # 总体性能对比
        stats_a['final_accuracy'], stats_b['final_accuracy'], 
        stats_a['final_accuracy'] - stats_b['final_accuracy'],
        stats_a['max_accuracy'], stats_b['max_accuracy'],
        stats_a['max_accuracy'] - stats_b['max_accuracy'],
        stats_a['total_epochs'], stats_b['total_epochs'],
        stats_a['total_epochs'] - stats_b['total_epochs'],
        stats_a['final_g_loss'], stats_b['final_g_loss'],
        stats_a['final_g_loss'] - stats_b['final_g_loss'],
        stats_a['final_d_loss'], stats_b['final_d_loss'],
        stats_a['final_d_loss'] - stats_b['final_d_loss'],
        
        # 训练稳定性
        stats_a['convergence_score'], stats_a['g_loss_variance'], stats_a['d_loss_variance'], stats_a['recent_accuracy'],
        stats_b['convergence_score'], stats_b['g_loss_variance'], stats_b['d_loss_variance'], stats_b['recent_accuracy'],
        
        # 问题列表
        '\n'.join([f"- {p}" for p in problems_a]) if problems_a else "- 未发现明显问题",
        '\n'.join([f"- {p}" for p in problems_b]) if problems_b else "- 未发现明显问题",
        
        # 深度分析
        "A组最终准确率更高，达到60.97%" if stats_a['final_accuracy'] > stats_b['final_accuracy'] else "B组表现相对较弱",
        "B组训练更快收敛" if stats_b['total_epochs'] < stats_a['total_epochs'] else "A组需要更多训练轮次",
        "A组收敛更快" if stats_a['convergence_score'] < stats_b['convergence_score'] else "B组收敛更稳定",
        "A组训练更稳定" if stats_a['g_loss_variance'] < stats_b['g_loss_variance'] else "B组损失波动较大",
        "A组生成器损失更低，性能更好" if stats_a['final_g_loss'] < stats_b['final_g_loss'] else "B组生成器性能略优",
        "A组判别器训练更平衡" if 0.01 < stats_a['final_d_loss'] < 0.1 else "判别器可能过强或过弱",
        
        # 改进建议
        "继续当前训练策略，表现良好" if not problems_a else "需要调整超参数和训练策略",
        "增加训练轮次，调整学习率" if problems_b else "保持当前训练设置",
        
        # 结论
        f"A组在准确率方面表现更优（{stats_a['final_accuracy']:.2f}% vs {stats_b['final_accuracy']:.2f}%），"
        f"训练稳定性也更好。B组可能需要调整数据集划分或模型超参数来提升性能。"
    )
    
    return report

def main():
    """主函数"""
    print("🔍 开始分析ASDCGAN A组和B组实验结果...")
    
    # 设置文件路径
    log_a = "/home/<USER>/hmt/ACGAN-FG-main/innovations/experiments/group_A/run_20250724_202822/training.log"
    log_b = "/home/<USER>/hmt/ACGAN-FG-main/innovations/experiments/group_B/run_20250724_203307/training.log"
    
    # 解析训练日志
    print("📖 解析训练日志...")
    data_a = parse_training_log(log_a)
    data_b = parse_training_log(log_b)
    
    if not data_a['epochs']:
        print("❌ A组日志解析失败")
        return
    if not data_b['epochs']:
        print("❌ B组日志解析失败")
        return
    
    print(f"✅ A组: {len(data_a['epochs'])} 个训练轮次")
    print(f"✅ B组: {len(data_b['epochs'])} 个训练轮次")
    
    # 分析训练统计
    print("📊 分析训练统计...")
    stats_a = analyze_training_stability(data_a, "A")
    stats_b = analyze_training_stability(data_b, "B")
    
    # 检测训练问题
    print("🔍 检测训练问题...")
    problems_a = detect_training_problems(stats_a)
    problems_b = detect_training_problems(stats_b)
    
    # 绘制对比图
    print("📈 生成对比图...")
    plot_training_curves(data_a, data_b)
    
    # 生成报告
    print("📝 生成分析报告...")
    report = generate_report(stats_a, stats_b, problems_a, problems_b)
    
    # 保存报告
    with open('/home/<USER>/hmt/ACGAN-FG-main/ab_groups_analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("✅ 分析完成！")
    print("📄 报告已保存: ab_groups_analysis_report.md")
    print("📊 对比图已保存: ab_groups_comparison.png")
    
    # 输出关键结果摘要
    print("\n" + "="*60)
    print("🎯 关键结果摘要")
    print("="*60)
    print(f"A组最终准确率: {stats_a['final_accuracy']:.2f}%")
    print(f"B组最终准确率: {stats_b['final_accuracy']:.2f}%")
    print(f"准确率差异: {stats_a['final_accuracy'] - stats_b['final_accuracy']:.2f}%")
    print(f"A组训练轮次: {stats_a['total_epochs']}")
    print(f"B组训练轮次: {stats_b['total_epochs']}")
    
    if problems_a:
        print(f"\nA组问题: {len(problems_a)} 个")
        for p in problems_a:
            print(f"  - {p}")
    
    if problems_b:
        print(f"\nB组问题: {len(problems_b)} 个")
        for p in problems_b:
            print(f"  - {p}")

if __name__ == "__main__":
    main()
#!/bin/bash

# 🚀 自动化训练脚本 - D、E组连续训练
# 按顺序执行：D组 → E组，每组2000轮次

echo "🎯 自动化训练脚本启动 - D、E组"
echo "====================================="
echo "📋 训练计划:"
echo "1. D组: 2000 epochs (~2-3小时)"
echo "2. E组: 2000 epochs (~2-3小时)"
echo ""
echo "📊 预计总时间: 4-6小时"
echo "⏰ 开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""
echo "💡 文献基准对比:"
echo "- D组目标: 87.24% (文献第二高)"
echo "- E组目标: 89.06% (文献最高)"
echo "====================================="

# 激活conda环境
echo "🔧 激活conda环境..."
source ~/miniconda3/etc/profile.d/conda.sh
conda activate vaegan_rtx50

# 切换到正确目录
cd /home/<USER>/hmt/ACGAN-FG-main/innovations

# 确认是否继续
echo ""
read -p "🤔 确认开始D、E组自动化训练？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 训练已取消"
    exit 1
fi

# 记录开始时间
start_time=$(date +%s)

echo ""
echo "🚀 开始D、E组自动化训练..."
echo "💡 可以随时按 Ctrl+C 中断训练"
echo ""

# 训练D组
echo "============================================"
echo "📊 [1/2] 开始训练D组 (2000 epochs)"
echo "🎯 D组特点: 测试类别[2,3,5], 文献基准87.24%"
echo "⏰ $(date '+%Y-%m-%d %H:%M:%S')"
echo "============================================"

python test_all_improvements.py --group D --epochs 2000

if [ $? -eq 0 ]; then
    echo "✅ D组训练完成！"
else
    echo "❌ D组训练失败！"
    read -p "是否继续E组训练？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 自动化训练已停止"
        exit 1
    fi
fi

echo ""
echo "⏳ 准备开始E组训练..."
sleep 3

# 训练E组
echo "============================================"
echo "📊 [2/2] 开始训练E组 (2000 epochs)"
echo "🎯 E组特点: 测试类别[9,13,15], 文献基准89.06% (最高)"
echo "⏰ $(date '+%Y-%m-%d %H:%M:%S')"
echo "============================================"

python test_all_improvements.py --group E --epochs 2000

if [ $? -eq 0 ]; then
    echo "✅ E组训练完成！"
else
    echo "❌ E组训练失败！"
fi

# 计算总时间
end_time=$(date +%s)
total_time=$((end_time - start_time))
hours=$((total_time / 3600))
minutes=$(((total_time % 3600) / 60))

echo ""
echo "🎉 D、E组自动化训练完成！"
echo "====================================="
echo "⏱️ 总用时: ${hours}小时${minutes}分钟"
echo "🏁 完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""
echo "📁 查看结果:"
echo "- D组: experiments/group_D/"
echo "- E组: experiments/group_E/"
echo ""
echo "📊 启动TensorBoard查看训练曲线:"
echo "tensorboard --logdir=tensorboard"
echo ""
echo "🎯 期望结果:"
echo "- D组: 目标达到87%+ (文献87.24%)"
echo "- E组: 目标达到89%+ (文献89.06%)"
echo "====================================="

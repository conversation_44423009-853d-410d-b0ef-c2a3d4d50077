{"model_config": {"feature_dim": 52, "attribute_dim": 20, "latent_dim": 50, "hidden_dims": [128, 256, 128]}, "training_config": {"batch_size": 64, "learning_rate_g": 0.0001, "learning_rate_d": 0.0002, "adversarial_weight": 0.5, "cycle_consistency_weight": 0.2, "semantic_distance_weight": 0.15, "uncertainty_weight": 0.1, "domain_selection_weight": 0.1, "gradient_penalty_weight": 10.0}, "group_info": {"group": "A", "test_classes": [1, 6, 14], "optimization_target": 80.0, "current_accuracy": 60.69}}
"""
VAEGAN with Attribute Regressor for Zero-Shot Fault Diagnosis
Based on the paper: "Feature Generating Network With Attribute-Consistency for Zero-Shot Fault Diagnosis"
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class Encoder(nn.Module):
    """Encoder network for VAEGAN"""
    
    def __init__(self, input_dim, attribute_dim, hidden_dim=128, latent_dim=50):
        super(Encoder, self).__init__()
        self.input_dim = input_dim
        self.attribute_dim = attribute_dim
        self.latent_dim = latent_dim
        
        # Concatenate feature and attribute
        concat_dim = input_dim + attribute_dim
        
        self.encoder = nn.Sequential(
            nn.Linear(concat_dim, hidden_dim),
            nn.Leaky<PERSON>eLU(0.2),
            nn.LayerNorm(hidden_dim),
            nn.Linear(hidden_dim, hidden_dim),
            nn.LeakyReLU(0.2),
            nn.LayerNorm(hidden_dim),
        )
        
        # Mean and variance for latent variable
        self.fc_mu = nn.Linear(hidden_dim, latent_dim)
        self.fc_var = nn.Linear(hidden_dim, latent_dim)
        
    def reparameterize(self, mu, logvar):
        """Reparameterization trick"""
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
        
    def forward(self, x, a):
        """
        Args:
            x: input features [batch_size, input_dim]
            a: attributes [batch_size, attribute_dim]
        """
        # Concatenate input and attribute
        xa = torch.cat([x, a], dim=1)
        
        # Encode
        h = self.encoder(xa)
        
        # Get mean and variance
        mu = self.fc_mu(h)
        logvar = self.fc_var(h)
        
        # Sample latent variable
        z = self.reparameterize(mu, logvar)
        
        return z, mu, logvar


class Generator(nn.Module):
    """Generator network for VAEGAN - Based on Table I in paper"""

    def __init__(self, attribute_dim, latent_dim=50, output_dim=24):
        super(Generator, self).__init__()
        self.attribute_dim = attribute_dim
        self.latent_dim = latent_dim
        self.output_dim = output_dim

        # Input: concatenated noise and attribute
        input_dim = latent_dim + attribute_dim

        # Based on Table I: Generator architecture (EXACT match)
        self.generator = nn.Sequential(
            nn.Linear(input_dim, 50),      # Fc1
            nn.LeakyReLU(0.2),
            nn.LayerNorm(50),
            nn.Linear(50, 100),            # Fc2
            nn.LeakyReLU(0.2),
            nn.LayerNorm(100),
            nn.Linear(100, 150),           # Fc3
            nn.LeakyReLU(0.2),
            nn.LayerNorm(150),
            nn.Linear(150, output_dim),    # Fc4
            nn.ReLU()
        )
        
    def forward(self, z, a):
        """
        Args:
            z: latent variable [batch_size, latent_dim]
            a: attributes [batch_size, attribute_dim]
        """
        # Concatenate noise and attribute
        za = torch.cat([z, a], dim=1)
        
        # Generate features
        x_gen = self.generator(za)
        
        return x_gen


class Discriminator(nn.Module):
    """Discriminator network for VAEGAN"""
    
    def __init__(self, input_dim, attribute_dim):
        super(Discriminator, self).__init__()
        self.input_dim = input_dim
        self.attribute_dim = attribute_dim
        
        # Input: concatenated feature and attribute
        concat_dim = input_dim + attribute_dim
        
        self.discriminator = nn.Sequential(
            nn.Linear(concat_dim, 200),
            nn.LeakyReLU(0.2),
            nn.LayerNorm(200),
            nn.Linear(200, 100),
            nn.LeakyReLU(0.2),
            nn.LayerNorm(100),
            nn.Linear(100, 1)
        )
        
    def forward(self, x, a):
        """
        Args:
            x: input features [batch_size, input_dim]
            a: attributes [batch_size, attribute_dim]
        """
        # Concatenate feature and attribute
        xa = torch.cat([x, a], dim=1)
        
        # Discriminate
        validity = self.discriminator(xa)
        
        return validity


class AttributeRegressor(nn.Module):
    """Attribute Regressor with hinge rank loss and mutual information constraint"""
    
    def __init__(self, input_dim, attribute_dim):
        super(AttributeRegressor, self).__init__()
        self.input_dim = input_dim
        self.attribute_dim = attribute_dim

        # Based on Table I: EXACT architecture (48 → 24 → attribute_dim)
        self.layer1 = nn.Sequential(
            nn.Linear(input_dim, 48),      # Fc1
            nn.LeakyReLU(0.2)
        )

        self.layer2 = nn.Sequential(
            nn.Linear(48, 24),             # Fc2 (T layer for feature transformation)
            nn.LeakyReLU(0.2)
        )

        self.output_layer = nn.Sequential(
            nn.Linear(24, attribute_dim),  # Fc3
            nn.Sigmoid()
        )
        
    def forward(self, x):
        """
        Args:
            x: input features [batch_size, input_dim]
        Returns:
            predicted attributes and hidden layer output (T from second hidden layer)
        """
        # Forward pass through layers (48 → 24 → attribute_dim)
        h1 = self.layer1(x)        # 48 dim
        h2 = self.layer2(h1)       # 24 dim (T layer for feature transformation)
        a_pred = self.output_layer(h2)  # attribute_dim

        return a_pred, h2  # Return predicted attributes and second hidden layer features T


class VAEGAN_AR(nn.Module):
    """Complete VAEGAN with Attribute Regressor model"""
    
    def __init__(self, input_dim=24, attribute_dim=20, latent_dim=50):
        super(VAEGAN_AR, self).__init__()
        
        self.input_dim = input_dim
        self.attribute_dim = attribute_dim
        self.latent_dim = latent_dim
        
        # Initialize networks
        self.encoder = Encoder(input_dim, attribute_dim, latent_dim=latent_dim)
        self.generator = Generator(attribute_dim, latent_dim, input_dim)
        self.discriminator = Discriminator(input_dim, attribute_dim)
        self.attribute_regressor = AttributeRegressor(input_dim, attribute_dim)
        
    def generate_samples(self, attributes, num_samples=None):
        """Generate samples given attributes"""
        if num_samples is None:
            batch_size = attributes.size(0)
        else:
            batch_size = num_samples
            if attributes.size(0) == 1:
                attributes = attributes.repeat(batch_size, 1)
            
        # Sample random noise
        device = attributes.device
        z = torch.randn(batch_size, self.latent_dim, device=device)
        
        # Generate samples
        with torch.no_grad():
            x_gen = self.generator(z, attributes)
            
        return x_gen
    
    def encode_decode(self, x, a):
        """Encode real samples and decode them (VAE path)"""
        z, mu, logvar = self.encoder(x, a)
        x_recon = self.generator(z, a)
        return x_recon, mu, logvar
    
    def forward(self, x, a, mode='all'):
        """
        Forward pass
        Args:
            x: input features
            a: attributes
            mode: 'encode', 'generate', 'discriminate', 'regress', or 'all'
        """
        outputs = {}
        
        if mode in ['encode', 'all']:
            z, mu, logvar = self.encoder(x, a)
            x_recon = self.generator(z, a)
            outputs['z'] = z
            outputs['mu'] = mu
            outputs['logvar'] = logvar
            outputs['x_recon'] = x_recon
            
        if mode in ['generate', 'all']:
            z_random = torch.randn_like(outputs.get('z', torch.randn(x.size(0), self.latent_dim, device=x.device)))
            x_gen = self.generator(z_random, a)
            outputs['x_gen'] = x_gen
            
        if mode in ['discriminate', 'all']:
            d_real = self.discriminator(x, a)
            outputs['d_real'] = d_real
            if 'x_gen' in outputs:
                d_fake = self.discriminator(outputs['x_gen'], a)
                outputs['d_fake'] = d_fake
                
        if mode in ['regress', 'all']:
            a_pred, hidden_features = self.attribute_regressor(x)
            outputs['a_pred'] = a_pred
            outputs['hidden_features'] = hidden_features
            
        return outputs


class LossFunction:
    """Loss functions for VAEGAN-AR model - Based on paper hyperparameters"""

    def __init__(self, lambda_vae=1.0, lambda_ar=1.0, lambda_gp=10.0, lambda_mi=0.1):
        # Based on paper: λ₁ (VAE factor), λ₂ (AR factor), λ (gradient penalty)
        # Paper uses learning rate 0.0001 and Adam optimizer
        self.lambda_vae = lambda_vae      # λ₁ in Equation (10)
        self.lambda_ar = lambda_ar        # λ₂ in Equation (10)
        self.lambda_gp = lambda_gp        # λ in Equation (2)
        self.lambda_mi = lambda_mi        # λ in Equation (9)
        
    def vae_loss(self, x_recon, x_real, mu, logvar):
        """VAE reconstruction loss + KL divergence"""
        recon_loss = F.mse_loss(x_recon, x_real, reduction='mean')
        kl_div = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp()) / mu.size(0)
        return recon_loss + kl_div
        
    def wasserstein_loss(self, d_real, d_fake):
        """Wasserstein GAN loss"""
        return -torch.mean(d_real) + torch.mean(d_fake)
        
    def gradient_penalty(self, discriminator, real_samples, fake_samples, attributes):
        """Gradient penalty for WGAN-GP"""
        device = real_samples.device
        batch_size = real_samples.size(0)
        
        # Random interpolation
        alpha = torch.rand(batch_size, 1, device=device)
        alpha = alpha.expand_as(real_samples)
        
        interpolated = alpha * real_samples + (1 - alpha) * fake_samples
        interpolated = interpolated.requires_grad_(True)
        
        # Discriminator output on interpolated samples
        d_interpolated = discriminator(interpolated, attributes)
        
        # Compute gradients
        gradients = torch.autograd.grad(
            outputs=d_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(d_interpolated),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]
        
        # Gradient penalty
        gradients = gradients.view(batch_size, -1)
        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()
        
        return gradient_penalty
        
    def hinge_rank_loss(self, a_pred, a_true, all_attributes):
        """
        Simplified Hinge rank loss for better training stability
        """
        batch_size = a_pred.size(0)

        # Simplified approach: use MSE loss with margin
        mse_loss = F.mse_loss(a_pred, a_true, reduction='mean')

        # Add margin-based ranking constraint
        margin = 0.1
        total_margin_loss = 0.0

        for i in range(batch_size):
            pred_i = a_pred[i:i+1]  # [1, attribute_dim]
            true_i = a_true[i:i+1]  # [1, attribute_dim]

            # Distance to true attribute
            dist_true = F.mse_loss(pred_i, true_i, reduction='sum')

            # Sample a few random other attributes for efficiency
            num_samples = min(5, all_attributes.size(0) - 1)
            if num_samples > 0:
                # Random sampling for efficiency
                indices = torch.randperm(all_attributes.size(0))[:num_samples]
                sampled_attrs = all_attributes[indices]

                # Remove true attribute if present
                mask = ~torch.all(sampled_attrs.unsqueeze(1) == true_i.unsqueeze(0), dim=2).any(dim=1)
                if mask.any():
                    sampled_attrs = sampled_attrs[mask]

                    if sampled_attrs.size(0) > 0:
                        # Compute distances to sampled attributes
                        dist_others = F.mse_loss(
                            pred_i.expand(sampled_attrs.size(0), -1),
                            sampled_attrs,
                            reduction='none'
                        ).sum(dim=1)

                        # Margin loss: we want dist_true < dist_others - margin
                        margin_losses = torch.clamp(margin + dist_true - dist_others, min=0)
                        total_margin_loss += margin_losses.mean()

        # Combine MSE and margin loss
        return mse_loss + 0.1 * total_margin_loss / batch_size
        
    def mutual_information_loss(self, hidden_features, original_features, b=1.0):
        """
        Mutual information constraint from Equation (9)
        L_AR += λ{E[D_KL[p_M(t|x) || r(t)]] - b}

        This implements a simplified version using KL divergence approximation
        """
        batch_size = hidden_features.size(0)
        hidden_dim = hidden_features.size(1)

        # Approximate p_M(t|x) as Gaussian distribution
        # Compute mean and variance of hidden features
        hidden_mean = torch.mean(hidden_features, dim=0)  # [hidden_dim]
        hidden_var = torch.var(hidden_features, dim=0) + 1e-8  # [hidden_dim]

        # Approximate r(t) as standard normal distribution
        # KL divergence between N(μ, σ²) and N(0, 1)
        # D_KL = 0.5 * sum(σ² + μ² - 1 - log(σ²))
        kl_div = 0.5 * torch.sum(hidden_var + hidden_mean.pow(2) - 1 - torch.log(hidden_var))

        # Constraint: E[D_KL] - b (we want this to be negative or zero)
        mi_constraint = kl_div - b

        # Return the constraint term (will be minimized)
        return torch.clamp(mi_constraint, min=0)
        
    def compute_total_loss(self, model_outputs, x_real, a_true, all_attributes, mode='generator'):
        """Compute total loss for generator or discriminator"""
        losses = {}
        
        if mode == 'generator':
            # VAE loss
            if 'x_recon' in model_outputs:
                losses['vae'] = self.vae_loss(
                    model_outputs['x_recon'], x_real,
                    model_outputs['mu'], model_outputs['logvar']
                )
            
            # Adversarial loss (generator wants discriminator to output high values for fake samples)
            if 'd_fake' in model_outputs:
                losses['adv'] = -torch.mean(model_outputs['d_fake'])
            
            # Attribute regression loss
            if 'a_pred' in model_outputs:
                losses['ar'] = self.hinge_rank_loss(model_outputs['a_pred'], a_true, all_attributes)
                
                # Mutual information constraint
                if 'hidden_features' in model_outputs:
                    losses['mi'] = self.mutual_information_loss(
                        model_outputs['hidden_features'], x_real
                    )
            
            # Total generator loss
            total_loss = (
                self.lambda_vae * losses.get('vae', 0) +
                losses.get('adv', 0) +
                self.lambda_ar * losses.get('ar', 0) +
                self.lambda_mi * losses.get('mi', 0)
            )
            
        elif mode == 'discriminator':
            # Wasserstein loss
            losses['wasserstein'] = self.wasserstein_loss(
                model_outputs['d_real'], model_outputs['d_fake']
            )
            
            # Gradient penalty
            losses['gp'] = self.gradient_penalty(
                model_outputs['discriminator'], x_real, 
                model_outputs['x_gen'], a_true
            )
            
            # Total discriminator loss
            total_loss = losses['wasserstein'] + self.lambda_gp * losses['gp']
            
        elif mode == 'regressor':
            # Only attribute regression loss
            losses['ar'] = self.hinge_rank_loss(model_outputs['a_pred'], a_true, all_attributes)
            
            # Mutual information constraint
            if 'hidden_features' in model_outputs:
                losses['mi'] = self.mutual_information_loss(
                    model_outputs['hidden_features'], x_real
                )
            
            total_loss = self.lambda_ar * losses['ar'] + self.lambda_mi * losses.get('mi', 0)
            
        losses['total'] = total_loss
        return losses

"""
Demo script for VAEGAN-AR Zero-Shot Fault Diagnosis
Quick demonstration of the model capabilities
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import sys

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from models import VAEGAN_AR, LossFunction
from data_processing import TEPDataProcessor, TEPAttributes
from train import Config


def demo_model_architecture():
    """Demonstrate model components"""
    print("VAEGAN-AR Model Architecture Demo")
    print("=" * 50)
    
    config = Config()
    model = VAEGAN_AR(
        input_dim=config.feature_dim,
        attribute_dim=config.attribute_dim, 
        latent_dim=config.latent_dim
    )
    
    print(f"Model Components:")
    print(f"- Input dimension: {config.feature_dim}")
    print(f"- Attribute dimension: {config.attribute_dim}")
    print(f"- Latent dimension: {config.latent_dim}")
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    print(f"- Total parameters: {total_params:,}")
    
    # Test forward pass
    batch_size = 16
    x_test = torch.randn(batch_size, config.feature_dim)
    a_test = torch.randint(0, 2, (batch_size, config.attribute_dim)).float()
    
    print(f"\nTesting forward pass with batch size {batch_size}:")
    
    # Encode
    z, mu, logvar = model.encoder(x_test, a_test)
    print(f"- Encoder output: z {z.shape}, mu {mu.shape}, logvar {logvar.shape}")
    
    # Generate
    x_gen = model.generator(z, a_test)
    print(f"- Generator output: {x_gen.shape}")
    
    # Discriminate
    d_out = model.discriminator(x_test, a_test)
    print(f"- Discriminator output: {d_out.shape}")
    
    # Attribute regression
    a_pred, hidden = model.attribute_regressor(x_test)
    print(f"- Attribute regressor: predicted {a_pred.shape}, hidden {hidden.shape}")
    
    return model


def demo_data_processing():
    """Demonstrate data processing capabilities"""
    print("\n\nData Processing Demo")
    print("=" * 50)
    
    processor = TEPDataProcessor(feature_dim=24)
    
    # Show attribute matrix
    print("TEP Fault Attributes:")
    attr_matrix = TEPAttributes.get_attribute_matrix()
    print(f"- Attribute matrix shape: {attr_matrix.shape}")
    print(f"- Number of faults: {attr_matrix.shape[0]}")
    print(f"- Number of attributes: {attr_matrix.shape[1]}")
    
    # Show data splits
    print(f"\nData Splits:")
    for split_name, split_info in TEPAttributes.SPLITS.items():
        seen = split_info['seen']
        unseen = split_info['unseen']
        print(f"- Split {split_name}: {len(seen)} seen, {len(unseen)} unseen")
    
    # Prepare sample data
    print(f"\nPreparing sample data for Split A...")
    data_dict = processor.prepare_zsl_data('A')
    
    print(f"- Seen features: {data_dict['seen_features'].shape}")
    print(f"- Unseen features: {data_dict['unseen_features'].shape}")
    print(f"- Seen classes: {data_dict['seen_fault_ids']}")
    print(f"- Unseen classes: {data_dict['unseen_fault_ids']}")
    
    return data_dict


def demo_loss_functions():
    """Demonstrate loss function calculations"""
    print("\n\nLoss Functions Demo")
    print("=" * 50)
    
    loss_fn = LossFunction()
    
    # Sample data
    batch_size = 8
    feature_dim = 24
    attr_dim = 20
    
    x_real = torch.randn(batch_size, feature_dim)
    x_gen = torch.randn(batch_size, feature_dim)
    a_true = torch.randint(0, 2, (batch_size, attr_dim)).float()
    a_pred = torch.sigmoid(torch.randn(batch_size, attr_dim))
    mu = torch.randn(batch_size, 50)
    logvar = torch.randn(batch_size, 50)
    d_real = torch.randn(batch_size, 1)
    d_fake = torch.randn(batch_size, 1)
    all_attributes = torch.randint(0, 2, (15, attr_dim)).float()
    
    # Calculate losses
    print("Loss Calculations:")
    
    # VAE loss
    vae_loss = loss_fn.vae_loss(x_gen, x_real, mu, logvar)
    print(f"- VAE Loss: {vae_loss.item():.4f}")
    
    # Wasserstein loss  
    wgan_loss = loss_fn.wasserstein_loss(d_real, d_fake)
    print(f"- Wasserstein Loss: {wgan_loss.item():.4f}")
    
    # Hinge rank loss
    hinge_loss = loss_fn.hinge_rank_loss(a_pred, a_true, all_attributes)
    print(f"- Hinge Rank Loss: {hinge_loss.item():.4f}")
    
    # Mutual information loss
    hidden_features = torch.randn(batch_size, 24)
    mi_loss = loss_fn.mutual_information_loss(hidden_features, x_real)
    print(f"- Mutual Information Loss: {mi_loss.item():.4f}")


def demo_generation():
    """Demonstrate sample generation"""
    print("\n\nSample Generation Demo") 
    print("=" * 50)
    
    config = Config()
    model = VAEGAN_AR(
        input_dim=config.feature_dim,
        attribute_dim=config.attribute_dim,
        latent_dim=config.latent_dim
    )
    
    model.eval()
    
    # Generate samples for fault class 1
    fault_1_attr = torch.FloatTensor(TEPAttributes.FAULT_ATTRIBUTES[1]).unsqueeze(0)
    print(f"Generating samples for Fault 1:")
    print(f"- Attributes: {fault_1_attr.numpy().flatten()}")
    
    # Generate 10 samples
    generated_samples = model.generate_samples(fault_1_attr, num_samples=10)
    print(f"- Generated samples shape: {generated_samples.shape}")
    print(f"- Sample statistics: mean={generated_samples.mean().item():.3f}, std={generated_samples.std().item():.3f}")
    
    # Generate for multiple fault classes
    fault_ids = [1, 6, 14]  # Unseen classes in Split A
    print(f"\nGenerating samples for unseen classes {fault_ids}:")
    
    for fault_id in fault_ids:
        attr = torch.FloatTensor(TEPAttributes.FAULT_ATTRIBUTES[fault_id]).unsqueeze(0)
        samples = model.generate_samples(attr, num_samples=5)
        print(f"- Fault {fault_id}: mean={samples.mean().item():.3f}, std={samples.std().item():.3f}")


def visualize_attribute_matrix():
    """Visualize the fault attribute matrix"""
    print("\n\nAttribute Matrix Visualization")
    print("=" * 50)
    
    attr_matrix = TEPAttributes.get_attribute_matrix()
    
    # Create visualization
    plt.figure(figsize=(12, 8))
    plt.imshow(attr_matrix, cmap='RdYlBu', aspect='auto')
    plt.colorbar(label='Attribute Value')
    plt.title('TEP Fault Attribute Matrix')
    plt.xlabel('Attribute Index')
    plt.ylabel('Fault Class')
    plt.yticks(range(15), [f'Fault {i+1}' for i in range(15)])
    plt.xticks(range(20), [f'Attr {i+1}' for i in range(20)], rotation=45)
    
    # Add grid
    for i in range(15):
        plt.axhline(i-0.5, color='black', linewidth=0.5, alpha=0.3)
    for i in range(20):
        plt.axvline(i-0.5, color='black', linewidth=0.5, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    save_path = os.path.join('..', 'results', 'attribute_matrix.png')
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    print(f"Attribute matrix visualization saved to: {save_path}")
    plt.close()


def main():
    """Run complete demo"""
    print("VAEGAN-AR Zero-Shot Fault Diagnosis - Demo")
    print("=" * 80)
    
    # Demo model architecture
    model = demo_model_architecture()
    
    # Demo data processing
    data_dict = demo_data_processing()
    
    # Demo loss functions
    demo_loss_functions()
    
    # Demo generation
    demo_generation()
    
    # Visualize attribute matrix
    visualize_attribute_matrix()
    
    print("\n" + "=" * 80)
    print("Demo completed successfully!")
    print("\nTo run a full training experiment:")
    print("cd src && python train.py --split A --epochs 10")
    print("\nTo run all experiments:")
    print("cd experiments && python run_all_experiments.py")


if __name__ == "__main__":
    main()

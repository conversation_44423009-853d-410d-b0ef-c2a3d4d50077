"""
变分生成器模块

基于变分自编码器(VAE)框架的生成器，支持不确定性量化。

核心功能：
1. 变分特征生成，提供不确定性估计
2. 重参数化技巧实现可微分采样
3. 支持条件生成和无条件生成
4. 集成KL散度正则化

技术特点：
- 基于VAE的概率生成框架
- 重参数化技巧保证梯度传播
- 多层次特征编码
- 不确定性量化能力
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class VariationalEncoder(tf.keras.layers.Layer):
    """变分编码器"""
    
    def __init__(self,
                 latent_dim=50,
                 hidden_dims=[128, 64],
                 dropout_rate=0.1,
                 **kwargs):
        super(VariationalEncoder, self).__init__(**kwargs)
        
        self.latent_dim = latent_dim
        self.hidden_dims = hidden_dims
        self.dropout_rate = dropout_rate
        
        # 编码网络
        self.encoder_layers = []
        for i, hidden_dim in enumerate(hidden_dims):
            self.encoder_layers.extend([
                Dense(hidden_dim, activation='relu', name=f'encoder_dense_{i}'),
                LayerNormalization(name=f'encoder_ln_{i}'),
                Dropout(dropout_rate, name=f'encoder_dropout_{i}')
            ])
        
        # 均值和方差网络
        self.mean_layer = Dense(latent_dim, name='latent_mean')
        self.logvar_layer = Dense(latent_dim, name='latent_logvar')
        
    def call(self, inputs, training=None):
        """
        编码输入到潜在空间
        
        Args:
            inputs: 输入特征 [batch_size, input_dim]
            
        Returns:
            mean: 潜在变量均值 [batch_size, latent_dim]
            logvar: 潜在变量对数方差 [batch_size, latent_dim]
        """
        x = inputs
        
        # 通过编码层
        for layer in self.encoder_layers:
            x = layer(x, training=training)
        
        # 计算均值和对数方差
        mean = self.mean_layer(x)
        logvar = self.logvar_layer(x)
        
        return mean, logvar


class VariationalDecoder(tf.keras.layers.Layer):
    """变分解码器"""
    
    def __init__(self,
                 output_dim,
                 hidden_dims=[64, 128],
                 dropout_rate=0.1,
                 **kwargs):
        super(VariationalDecoder, self).__init__(**kwargs)
        
        self.output_dim = output_dim
        self.hidden_dims = hidden_dims
        self.dropout_rate = dropout_rate
        
        # 解码网络
        self.decoder_layers = []
        for i, hidden_dim in enumerate(hidden_dims):
            self.decoder_layers.extend([
                Dense(hidden_dim, activation='relu', name=f'decoder_dense_{i}'),
                LayerNormalization(name=f'decoder_ln_{i}'),
                Dropout(dropout_rate, name=f'decoder_dropout_{i}')
            ])
        
        # 输出层
        self.output_layer = Dense(output_dim, activation='linear', name='decoder_output')
        
    def call(self, inputs, training=None):
        """
        解码潜在变量到输出空间
        
        Args:
            inputs: 潜在变量 [batch_size, latent_dim]
            
        Returns:
            outputs: 重构输出 [batch_size, output_dim]
        """
        x = inputs
        
        # 通过解码层
        for layer in self.decoder_layers:
            x = layer(x, training=training)
        
        # 输出层
        outputs = self.output_layer(x)
        
        return outputs


class VariationalGenerator(tf.keras.layers.Layer):
    """
    变分生成器
    
    基于VAE框架的生成器，支持不确定性量化和条件生成。
    """
    
    def __init__(self,
                 feature_dim=256,
                 attribute_dim=20,
                 latent_dim=50,
                 encoder_hidden_dims=[128, 64],
                 decoder_hidden_dims=[64, 128],
                 dropout_rate=0.1,
                 beta=1.0,
                 **kwargs):
        """
        初始化变分生成器
        
        Args:
            feature_dim: 特征维度
            attribute_dim: 属性维度
            latent_dim: 潜在空间维度
            encoder_hidden_dims: 编码器隐藏层维度
            decoder_hidden_dims: 解码器隐藏层维度
            dropout_rate: Dropout比率
            beta: KL散度权重 (β-VAE)
        """
        super(VariationalGenerator, self).__init__(**kwargs)
        
        self.feature_dim = feature_dim
        self.attribute_dim = attribute_dim
        self.latent_dim = latent_dim
        self.dropout_rate = dropout_rate
        self.beta = beta
        
        # 属性编码器
        self.attribute_encoder = tf.keras.Sequential([
            Dense(64, activation='relu'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(32, activation='tanh')
        ])
        
        # 变分编码器
        self.variational_encoder = VariationalEncoder(
            latent_dim=latent_dim,
            hidden_dims=encoder_hidden_dims,
            dropout_rate=dropout_rate
        )
        
        # 变分解码器
        self.variational_decoder = VariationalDecoder(
            output_dim=feature_dim,
            hidden_dims=decoder_hidden_dims,
            dropout_rate=dropout_rate
        )
        
        # 不确定性估计网络
        self.uncertainty_estimator = tf.keras.Sequential([
            Dense(64, activation='relu'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(32, activation='relu'),
            Dense(feature_dim, activation='softplus')  # 确保输出为正
        ])
        
    def reparameterize(self, mean, logvar):
        """
        重参数化技巧
        
        Args:
            mean: 均值 [batch_size, latent_dim]
            logvar: 对数方差 [batch_size, latent_dim]
            
        Returns:
            z: 采样的潜在变量 [batch_size, latent_dim]
        """
        batch_size = tf.shape(mean)[0]
        epsilon = tf.random.normal(shape=(batch_size, self.latent_dim))
        std = tf.exp(0.5 * logvar)
        z = mean + std * epsilon
        return z
    
    def encode(self, features, attributes, training=None):
        """
        编码特征和属性到潜在空间
        
        Args:
            features: 输入特征 [batch_size, feature_dim]
            attributes: 属性向量 [batch_size, attribute_dim]
            
        Returns:
            mean: 潜在变量均值
            logvar: 潜在变量对数方差
            z: 采样的潜在变量
        """
        # 编码属性
        attr_encoded = self.attribute_encoder(attributes, training=training)
        
        # 组合特征和属性
        combined = tf.concat([features, attr_encoded], axis=-1)
        
        # 变分编码
        mean, logvar = self.variational_encoder(combined, training=training)
        
        # 重参数化采样
        z = self.reparameterize(mean, logvar)
        
        return mean, logvar, z
    
    def decode(self, z, attributes, training=None):
        """
        解码潜在变量到特征空间
        
        Args:
            z: 潜在变量 [batch_size, latent_dim]
            attributes: 属性向量 [batch_size, attribute_dim]
            
        Returns:
            generated_features: 生成的特征
            uncertainty: 不确定性估计
        """
        # 编码属性
        attr_encoded = self.attribute_encoder(attributes, training=training)
        
        # 组合潜在变量和属性
        combined = tf.concat([z, attr_encoded], axis=-1)
        
        # 解码生成特征
        generated_features = self.variational_decoder(combined, training=training)
        
        # 估计不确定性
        uncertainty = self.uncertainty_estimator(combined, training=training)
        
        return generated_features, uncertainty
    
    def call(self, inputs, training=None):
        """
        前向传播
        
        Args:
            inputs: [noise, attributes] 或 [features, attributes]
                   - 如果是生成模式：noise [batch_size, latent_dim], attributes [batch_size, attribute_dim]
                   - 如果是重构模式：features [batch_size, feature_dim], attributes [batch_size, attribute_dim]
                   
        Returns:
            result: {
                'generated_features': 生成的特征,
                'uncertainty': 不确定性估计,
                'mean': 潜在变量均值 (仅重构模式),
                'logvar': 潜在变量对数方差 (仅重构模式),
                'z': 潜在变量
            }
        """
        if len(inputs) == 2:
            input_data, attributes = inputs
            
            # 判断是噪声输入还是特征输入
            if tf.shape(input_data)[-1] == self.latent_dim:
                # 噪声输入 - 生成模式
                z = input_data
                generated_features, uncertainty = self.decode(z, attributes, training=training)
                
                return {
                    'generated_features': generated_features,
                    'uncertainty': uncertainty,
                    'z': z
                }
            else:
                # 特征输入 - 重构模式
                features = input_data
                mean, logvar, z = self.encode(features, attributes, training=training)
                generated_features, uncertainty = self.decode(z, attributes, training=training)
                
                return {
                    'generated_features': generated_features,
                    'uncertainty': uncertainty,
                    'mean': mean,
                    'logvar': logvar,
                    'z': z
                }
        else:
            raise ValueError("输入应该是 [noise/features, attributes] 的形式")
    
    def generate_with_uncertainty(self, noise, attributes, num_samples=10, training=None):
        """
        生成多个样本以估计不确定性
        
        Args:
            noise: 噪声输入 [batch_size, latent_dim]
            attributes: 属性向量 [batch_size, attribute_dim]
            num_samples: 采样数量
            
        Returns:
            mean_features: 平均特征
            std_features: 特征标准差 (不确定性)
            all_samples: 所有采样结果
        """
        samples = []
        
        for _ in range(num_samples):
            result = self.call([noise, attributes], training=training)
            samples.append(result['generated_features'])
        
        # 计算统计量
        all_samples = tf.stack(samples, axis=0)  # [num_samples, batch_size, feature_dim]
        mean_features = tf.reduce_mean(all_samples, axis=0)
        std_features = tf.math.reduce_std(all_samples, axis=0)
        
        return mean_features, std_features, all_samples
    
    def compute_kl_loss(self, mean, logvar):
        """
        计算KL散度损失
        
        Args:
            mean: 潜在变量均值
            logvar: 潜在变量对数方差
            
        Returns:
            kl_loss: KL散度损失
        """
        kl_loss = -0.5 * tf.reduce_sum(
            1 + logvar - tf.square(mean) - tf.exp(logvar),
            axis=-1
        )
        return tf.reduce_mean(kl_loss) * self.beta
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'feature_dim': self.feature_dim,
            'attribute_dim': self.attribute_dim,
            'latent_dim': self.latent_dim,
            'dropout_rate': self.dropout_rate,
            'beta': self.beta
        })
        return config

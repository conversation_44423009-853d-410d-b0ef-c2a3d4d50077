"""
IMPROVED VAEGAN with Attribute Regressor for Zero-Shot Fault Diagnosis
Fixed implementation based on the paper: "Feature Generating Network With Attribute-Consistency for Zero-Shot Fault Diagnosis"

Key improvements:
1. Correct Hinge Rank Loss implementation (Equation 5)
2. Proper Mutual Information constraint (Equations 7-9)
3. Exact network architecture from Table I
4. Algorithm 1 training procedure
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class Encoder(nn.Module):
    """Encoder network for VAEGAN"""
    
    def __init__(self, input_dim, attribute_dim, hidden_dim=128, latent_dim=50):
        super(Encoder, self).__init__()
        self.input_dim = input_dim
        self.attribute_dim = attribute_dim
        self.latent_dim = latent_dim
        
        # Concatenate feature and attribute
        concat_dim = input_dim + attribute_dim
        
        self.encoder = nn.Sequential(
            nn.Linear(concat_dim, hidden_dim),
            nn.<PERSON>kyReL<PERSON>(0.2),
            nn.<PERSON><PERSON><PERSON><PERSON>(hidden_dim),
            nn.<PERSON>(hidden_dim, hidden_dim),
            nn.<PERSON>ky<PERSON>eL<PERSON>(0.2),
            nn.LayerNorm(hidden_dim),
        )
        
        # Mean and variance for latent variable
        self.fc_mu = nn.Linear(hidden_dim, latent_dim)
        self.fc_var = nn.Linear(hidden_dim, latent_dim)
        
    def reparameterize(self, mu, logvar):
        """Reparameterization trick"""
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
        
    def forward(self, x, a):
        """
        Args:
            x: input features [batch_size, input_dim]
            a: attributes [batch_size, attribute_dim]
        """
        # Concatenate input and attribute
        xa = torch.cat([x, a], dim=1)
        
        # Encode
        h = self.encoder(xa)
        
        # Get mean and variance
        mu = self.fc_mu(h)
        logvar = self.fc_var(h)
        
        # Sample latent variable
        z = self.reparameterize(mu, logvar)
        
        return z, mu, logvar


class Generator(nn.Module):
    """Generator network - EXACT Table I implementation"""

    def __init__(self, attribute_dim, latent_dim=50, output_dim=24):
        super(Generator, self).__init__()
        self.attribute_dim = attribute_dim
        self.latent_dim = latent_dim
        self.output_dim = output_dim

        # Input: concatenated noise and attribute
        input_dim = latent_dim + attribute_dim

        # EXACT Table I architecture
        self.generator = nn.Sequential(
            nn.Linear(input_dim, 50),      # Fc1+LeakyReLU(0.2)+LN
            nn.LeakyReLU(0.2),
            nn.LayerNorm(50),
            nn.Linear(50, 100),            # Fc2+LeakyReLU(0.2)+LN
            nn.LeakyReLU(0.2),
            nn.LayerNorm(100),
            nn.Linear(100, 150),           # Fc3+LeakyReLU(0.2)+LN
            nn.LeakyReLU(0.2),
            nn.LayerNorm(150),
            nn.Linear(150, output_dim),    # Fc4+ReLU
            nn.ReLU()                      # Note: ReLU not LeakyReLU for final layer
        )
        
    def forward(self, z, a):
        """
        Args:
            z: latent variable [batch_size, latent_dim]
            a: attributes [batch_size, attribute_dim]
        """
        # Concatenate noise and attribute
        za = torch.cat([z, a], dim=1)
        
        # Generate features
        x_gen = self.generator(za)
        
        return x_gen


class Discriminator(nn.Module):
    """Discriminator network - EXACT Table I implementation"""
    
    def __init__(self, input_dim, attribute_dim):
        super(Discriminator, self).__init__()
        self.input_dim = input_dim
        self.attribute_dim = attribute_dim
        
        # Input: concatenated feature and attribute
        concat_dim = input_dim + attribute_dim
        
        # EXACT Table I architecture
        self.discriminator = nn.Sequential(
            nn.Linear(concat_dim, 200),    # Fc1+LeakyReLU(0.2)+LN
            nn.LeakyReLU(0.2),
            nn.LayerNorm(200),
            nn.Linear(200, 100),           # Fc2+LeakyReLU(0.2)+LN
            nn.LeakyReLU(0.2),
            nn.LayerNorm(100),
            nn.Linear(100, 1)              # Fc3 (no activation)
        )
        
    def forward(self, x, a):
        """
        Args:
            x: input features [batch_size, input_dim]
            a: attributes [batch_size, attribute_dim]
        """
        # Concatenate feature and attribute
        xa = torch.cat([x, a], dim=1)
        
        # Discriminate
        validity = self.discriminator(xa)
        
        return validity


class AttributeRegressor(nn.Module):
    """Attribute Regressor - EXACT Table I implementation"""
    
    def __init__(self, input_dim, attribute_dim):
        super(AttributeRegressor, self).__init__()
        self.input_dim = input_dim
        self.attribute_dim = attribute_dim

        # EXACT Table I architecture: Fc1+LeakyReLU(0.2) -> Fc2+LeakyReLU(0.2) -> Fc3+Sigmoid
        self.layer1 = nn.Sequential(
            nn.Linear(input_dim, 48),      # Fc1+LeakyReLU(0.2)
            nn.LeakyReLU(0.2)
        )

        self.layer2 = nn.Sequential(
            nn.Linear(48, 24),             # Fc2+LeakyReLU(0.2) - T layer for feature transformation
            nn.LeakyReLU(0.2)
        )

        self.output_layer = nn.Sequential(
            nn.Linear(24, attribute_dim),  # Fc3+Sigmoid
            nn.Sigmoid()
        )
        
    def forward(self, x):
        """
        Args:
            x: input features [batch_size, input_dim]
        Returns:
            predicted attributes and hidden layer output T (from second layer)
        """
        h1 = self.layer1(x)        # 48 dim
        h2 = self.layer2(h1)       # 24 dim (T layer for feature transformation)
        a_pred = self.output_layer(h2)  # attribute_dim

        return a_pred, h2  # Return predicted attributes and T layer features


class VAEGAN_AR(nn.Module):
    """Complete VAEGAN with Attribute Regressor model"""
    
    def __init__(self, input_dim=24, attribute_dim=20, latent_dim=50):
        super(VAEGAN_AR, self).__init__()
        
        self.input_dim = input_dim
        self.attribute_dim = attribute_dim
        self.latent_dim = latent_dim
        
        # Initialize networks
        self.encoder = Encoder(input_dim, attribute_dim, latent_dim=latent_dim)
        self.generator = Generator(attribute_dim, latent_dim, input_dim)
        self.discriminator = Discriminator(input_dim, attribute_dim)
        self.attribute_regressor = AttributeRegressor(input_dim, attribute_dim)
        
    def generate_samples(self, attributes, num_samples=None):
        """Generate samples given attributes"""
        if num_samples is None:
            batch_size = attributes.size(0)
        else:
            batch_size = num_samples
            if attributes.size(0) == 1:
                attributes = attributes.repeat(batch_size, 1)
            
        # Sample random noise
        device = attributes.device
        z = torch.randn(batch_size, self.latent_dim, device=device)
        
        # Generate samples
        with torch.no_grad():
            x_gen = self.generator(z, attributes)
            
        return x_gen
    
    def encode_decode(self, x, a):
        """Encode real samples and decode them (VAE path)"""
        z, mu, logvar = self.encoder(x, a)
        x_recon = self.generator(z, a)
        return x_recon, mu, logvar


class ImprovedLossFunction:
    """CORRECTED Loss functions for VAEGAN-AR model - Based on paper equations"""

    def __init__(self, lambda_vae=1.0, lambda_ar=1.0, lambda_gp=10.0, lambda_mi=0.1):
        self.lambda_vae = lambda_vae      # λ₁ in Equation (10)
        self.lambda_ar = lambda_ar        # λ₂ in Equation (10)
        self.lambda_gp = lambda_gp        # λ in Equation (2)
        self.lambda_mi = lambda_mi        # λ in Equation (9)
        
    def vae_loss(self, x_recon, x_real, mu, logvar):
        """VAE reconstruction loss + KL divergence - Equation (1)"""
        recon_loss = F.mse_loss(x_recon, x_real, reduction='mean')
        kl_div = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp()) / mu.size(0)
        return recon_loss + kl_div
        
    def wasserstein_loss(self, d_real, d_fake):
        """Wasserstein GAN loss - Equation (2)"""
        return -torch.mean(d_real) + torch.mean(d_fake)
        
    def gradient_penalty(self, discriminator, real_samples, fake_samples, attributes):
        """Gradient penalty for WGAN-GP - Equation (2)"""
        device = real_samples.device
        batch_size = real_samples.size(0)
        
        # Random interpolation
        alpha = torch.rand(batch_size, 1, device=device)
        alpha = alpha.expand_as(real_samples)
        
        interpolated = alpha * real_samples + (1 - alpha) * fake_samples
        interpolated = interpolated.requires_grad_(True)
        
        # Discriminator output on interpolated samples
        d_interpolated = discriminator(interpolated, attributes)
        
        # Compute gradients
        gradients = torch.autograd.grad(
            outputs=d_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(d_interpolated),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]
        
        # Gradient penalty
        gradients = gradients.view(batch_size, -1)
        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()
        
        return gradient_penalty

    def hinge_rank_loss(self, a_pred, a_true, all_attributes):
        """
        CORRECTED Hinge Rank Loss implementation - Equation (5)
        L_AR = (1/N) * Σ γ_r_Δ(x_i,y_ai) * Σ_{aj≠a_yi} max{0, l(x_i,a_yi,a_j)}
        
        where:
        - l(x_i,a_yi,a_j) = 1 - F(x_i,a_yi;θ_AR) + F(x_i,a_j;θ_AR)
        - F(x_i,a_yi;θ_AR) = ||R(x_i;θ_AR) - a_yi||_2
        - r_Δ(x_i,a_yi) = Σ_{aj≠a_yi} I(l(x_i,a_yi,a_j) > 0)
        - γ_k is a decreasing function of k
        """
        batch_size = a_pred.size(0)
        device = a_pred.device
        total_loss = 0.0
        
        for i in range(batch_size):
            pred_i = a_pred[i]      # [attribute_dim]
            true_i = a_true[i]      # [attribute_dim]
            
            # F(x_i, a_yi; θ_AR) - distance to true attribute
            F_true = torch.norm(pred_i - true_i, p=2)
            
            # Compute distances to all other attributes
            margin_losses = []
            for j in range(all_attributes.size(0)):
                attr_j = all_attributes[j]
                
                # Skip if this is the true attribute
                if torch.allclose(attr_j, true_i, atol=1e-6):
                    continue
                    
                # F(x_i, a_j; θ_AR) - distance to other attribute
                F_other = torch.norm(pred_i - attr_j, p=2)
                
                # l(x_i, a_yi, a_j) = 1 - F_true + F_other
                l_val = 1.0 - F_true + F_other
                
                # max{0, l(x_i, a_yi, a_j)}
                margin_loss = torch.clamp(l_val, min=0)
                if margin_loss > 0:
                    margin_losses.append(margin_loss)
            
            if len(margin_losses) > 0:
                # r_Δ(x_i, a_yi) - count of positive margin losses (rank)
                r_delta = len(margin_losses)
                
                # γ_r_Δ - decreasing function of rank (higher rank = lower weight)
                gamma_r = 1.0 / (1.0 + r_delta * 0.1)  # Decreasing function
                
                # Sum of margin losses for this sample
                sample_loss = sum(margin_losses)
                
                # Weight by γ_r_Δ
                total_loss += gamma_r * sample_loss
        
        # Average over batch
        return total_loss / batch_size

    def mutual_information_loss(self, hidden_features, original_features, b=1.0):
        """
        CORRECTED Mutual Information constraint - Equations (7-9)
        I(T;X) ≤ E_p(x)[D_KL[p_M(t|x) || r(t)]] < b
        
        This implements the variational upper bound from Equation (7)
        """
        batch_size = hidden_features.size(0)
        hidden_dim = hidden_features.size(1)
        
        # Model p_M(t|x) as multivariate Gaussian
        # Compute conditional statistics per sample
        mi_loss = 0.0
        
        for i in range(batch_size):
            t_i = hidden_features[i]  # [hidden_dim]
            
            # Approximate p_M(t|x_i) as Gaussian N(μ_i, σ_i²)
            # For simplicity, assume diagonal covariance
            mu_i = t_i  # Use the hidden representation as mean
            # Estimate variance (could be learned, here we use a simple heuristic)
            sigma_i_sq = torch.ones_like(t_i) * 0.1  # Small variance
            
            # r(t) is standard normal N(0, I)
            # D_KL[N(μ_i, σ_i²) || N(0, I)] = 0.5 * Σ(σ_i² + μ_i² - 1 - log(σ_i²))
            kl_div = 0.5 * torch.sum(sigma_i_sq + mu_i.pow(2) - 1 - torch.log(sigma_i_sq + 1e-8))
            mi_loss += kl_div
        
        # Average over batch
        mi_loss = mi_loss / batch_size
        
        # Constraint: E[D_KL] - b (should be ≤ 0)
        mi_constraint = mi_loss - b
        
        # Return positive part (penalty when constraint is violated)
        return torch.clamp(mi_constraint, min=0)


class ImprovedZeroShotTrainer:
    """Improved trainer following Algorithm 1 from the paper"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize model
        self.model = VAEGAN_AR(
            input_dim=config.feature_dim,
            attribute_dim=config.attribute_dim,
            latent_dim=config.latent_dim
        ).to(self.device)
        
        # Initialize improved loss function
        self.loss_fn = ImprovedLossFunction(
            lambda_vae=config.lambda_vae,
            lambda_ar=config.lambda_ar,
            lambda_gp=config.lambda_gp,
            lambda_mi=config.lambda_mi
        )
        
        # Initialize optimizers with EXACT paper parameters
        self.optim_G = torch.optim.Adam(
            list(self.model.encoder.parameters()) + list(self.model.generator.parameters()),
            lr=0.0001, betas=(0.5, 0.999)  # EXACT paper values
        )
        self.optim_D = torch.optim.Adam(
            self.model.discriminator.parameters(),
            lr=0.0001, betas=(0.5, 0.999)  # EXACT paper values
        )
        self.optim_AR = torch.optim.Adam(
            self.model.attribute_regressor.parameters(),
            lr=0.0001, betas=(0.5, 0.999)  # EXACT paper values
        )
        
        # Training metrics
        self.train_losses = {'G': [], 'D': [], 'AR': []}

    def train_epoch_algorithm1(self, seen_loader, training_attributes, epoch):
        """
        Training following Algorithm 1 from the paper EXACTLY
        """
        self.model.train()
        epoch_losses = {'G': 0.0, 'D': 0.0, 'AR': 0.0}
        n_batches = 0
        
        for batch_idx, (x_real, labels, a_real) in enumerate(seen_loader):
            x_real = x_real.to(self.device)
            a_real = a_real.to(self.device)
            batch_size = x_real.size(0)
            
            # ============ Algorithm 1: Step-by-step implementation ============
            
            for i in range(batch_size):
                # Pick a random data (x_i^s, a_yi^s) - already in batch
                x_i = x_real[i:i+1]  # [1, feature_dim]
                a_yi = a_real[i:i+1]  # [1, attribute_dim]
                
                # ============ Train Attribute Regressor (Lines 6-11) ============
                self.optim_AR.zero_grad()
                
                # â_yi = AR(x_i) - Line 7
                a_pred, hidden_t = self.model.attribute_regressor(x_i)
                
                # L_AR calculation - Lines 8-10
                ar_loss = self.loss_fn.hinge_rank_loss(a_pred, a_yi, training_attributes)
                mi_loss = self.loss_fn.mutual_information_loss(hidden_t, x_i)
                total_ar_loss = ar_loss + self.config.lambda_mi * mi_loss
                
                # θ_AR ← -∇_θ_AR L_AR - Line 11
                total_ar_loss.backward()
                self.optim_AR.step()
                
                # ============ Train Discriminator n times (Lines 12-18) ============
                for critic_iter in range(self.config.n_critic):  # n=5 from paper
                    self.optim_D.zero_grad()
                    
                    # Randomly sample noise z_p ~ N(0,1) - Line 16
                    z_p = torch.randn(1, self.config.latent_dim, device=self.device)
                    
                    # Generate fake sample
                    with torch.no_grad():
                        x_fake = self.model.generator(z_p, a_yi)
                    
                    # L_WGAN calculation - Lines 14-15
                    d_real = self.model.discriminator(x_i, a_yi)
                    d_fake = self.model.discriminator(x_fake, a_yi)
                    
                    wasserstein_loss = torch.mean(d_fake) - torch.mean(d_real)
                    gp = self.loss_fn.gradient_penalty(self.model.discriminator, x_i, x_fake, a_yi)
                    
                    d_loss = wasserstein_loss + self.config.lambda_gp * gp
                    
                    # θ_D ← -∇_θ_D L_WGAN - Line 18
                    d_loss.backward()
                    self.optim_D.step()
            
            # ============ Train Generator/Encoder (Lines 19-28) ============
            self.optim_G.zero_grad()
            
            # VAE path: z = E(x_i, a_yi) - Line 19
            z, mu, logvar = self.model.encoder(x_real, a_real)
            x_hat = self.model.generator(z, a_real)  # x̂ = G(z, a_yi) - Line 20
            
            # L_VAE calculation - Lines 21-22
            vae_loss = self.loss_fn.vae_loss(x_hat, x_real, mu, logvar)
            
            # Generate fake samples - Lines 23-24
            z_p = torch.randn(batch_size, self.config.latent_dim, device=self.device)
            x_tilde = self.model.generator(z_p, a_real)  # x̃ = G(z_p, a_yi) - Line 24
            
            # Attribute prediction for fake samples - Line 25
            a_tilde, hidden_tilde = self.model.attribute_regressor(x_tilde)
            
            # L_AR for fake samples - Lines 26-27
            ar_loss_fake = self.loss_fn.hinge_rank_loss(a_tilde, a_real, training_attributes)
            mi_loss_fake = self.loss_fn.mutual_information_loss(hidden_tilde, x_tilde)
            
            # Adversarial loss
            d_fake = self.model.discriminator(x_tilde, a_real)
            adv_loss = -torch.mean(d_fake)
            
            # Total generator loss - Line 28
            g_loss = (self.config.lambda_vae * vae_loss + 
                     adv_loss + 
                     self.config.lambda_ar * ar_loss_fake +
                     self.config.lambda_mi * mi_loss_fake)
            
            # Update generator and encoder - Lines 29-30
            g_loss.backward()
            self.optim_G.step()
            
            # Record losses
            epoch_losses['G'] += g_loss.item()
            epoch_losses['D'] += d_loss.item()
            epoch_losses['AR'] += total_ar_loss.item()
            n_batches += 1
            
            if batch_idx % 10 == 0:
                print(f"Batch {batch_idx}: G={g_loss.item():.4f}, D={d_loss.item():.4f}, AR={total_ar_loss.item():.4f}")
        
        # Average losses
        for key in epoch_losses:
            epoch_losses[key] /= n_batches
            self.train_losses[key].append(epoch_losses[key])
        
        return epoch_losses


class ImprovedConfig:
    """Improved configuration matching paper exactly"""
    
    def __init__(self):
        # Model parameters - EXACT from Table I
        self.feature_dim = 24
        self.attribute_dim = 20
        self.latent_dim = 50
        
        # Training parameters - EXACT from paper
        self.batch_size = 64
        self.epochs = 300  # Increased for better convergence
        self.lr = 0.0001   # EXACT from paper
        self.n_critic = 5  # CORRECTED: should be 5 not 1 (WGAN-GP standard)

        # Loss weights - optimized based on paper hints
        self.lambda_vae = 1.0    # λ₁ in Equation (10)
        self.lambda_ar = 2.0     # λ₂ in Equation (10) - INCREASED for better attribute consistency
        self.lambda_gp = 10.0    # λ in Equation (2) - WGAN-GP standard
        self.lambda_mi = 0.1     # λ in Equation (9) - mutual information constraint
        
        # Paths
        self.save_dir = './improved_results'
        self.log_dir = './improved_logs'
        
        # Create directories
        import os
        os.makedirs(self.save_dir, exist_ok=True)
        os.makedirs(self.log_dir, exist_ok=True)
# 简化版语义迁移零样本故障诊断
# 摆脱复杂ACGAN架构，直接进行语义到特征的映射学习

import numpy as np
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, LayerNormalization, Dropout
from tensorflow.keras.models import Model
import datetime
import read_data
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import LinearSVC
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score

# GPU配置
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    tf.config.set_visible_devices(gpus[0], 'GPU')
    tf.config.experimental.set_memory_growth(gpus[0], True)

class SimplifiedSemanticTransfer:
    """
    简化版语义迁移方法
    
    核心思路：
    1. 训练一个简单的autoencoder获得特征表示
    2. 训练一个语义描述→特征的直接映射网络
    3. 为目标类别生成虚拟特征样本
    4. 用虚拟样本训练分类器
    """
    
    def __init__(self):
        # 基本参数
        self.data_lenth = 52
        self.sample_shape = (self.data_lenth,)
        self.feature_dim = 256
        self.feature_shape = (256,)
        self.attribute_dim = 20
        
        # 优化器
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.mapper_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        
        # 故障语义描述矩阵（专业设计版本）
        self.fault_descriptions = self.create_professional_tep_descriptions()
        
        # 构建网络
        self.autoencoder = self.build_autoencoder()
        self.semantic_mapper = self.build_semantic_mapper()
        
    def create_professional_tep_descriptions(self):
        """
        基于TEP工艺专业知识的故障语义描述矩阵
        """
        professional_descriptions = np.array([
            # 类别0: 正常操作
            [0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0],
            
            # 类别1: A/C进料比故障 (目标类别) - 进料+控制问题
            [1,1,1,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 1,1,0,0],
            
            # 类别2: B组分故障  
            [0,1,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 0,1,0,0],
            
            # 类别3: D进料温度故障
            [0,0,0,1, 0,0,0,0, 1,1,0,0, 0,0,0,0, 0,0,1,0],
            
            # 类别4: 反应器冷却水故障
            [0,0,0,0, 1,1,0,0, 1,0,1,0, 0,0,0,0, 0,0,0,1],
            
            # 类别5: 冷凝器冷却水故障
            [0,0,0,0, 0,0,1,0, 0,1,1,0, 0,0,0,0, 0,0,0,1],
            
            # 类别6: A进料丢失故障 (目标类别) - 进料丢失
            [1,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 1,0,1,1],
            
            # 类别7: C压头压力丢失故障
            [0,0,1,0, 0,0,0,0, 0,0,0,0, 1,1,1,0, 1,0,0,0],
            
            # 类别8: A/B/C进料组成故障
            [1,1,1,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 0,1,1,0],
            
            # 类别9: D进料温度故障(随机变化)
            [0,0,0,1, 0,0,0,0, 1,0,0,1, 0,0,0,0, 0,0,1,1],
            
            # 类别10: C进料温度故障
            [0,0,1,0, 0,0,0,0, 0,1,0,1, 0,0,0,0, 0,0,1,0],
            
            # 类别11: 反应器冷却水温度故障
            [0,0,0,0, 1,0,1,0, 1,1,0,0, 0,0,0,0, 0,0,0,1],
            
            # 类别12: 冷凝器冷却水温度故障
            [0,0,0,0, 0,1,1,0, 0,0,1,1, 0,0,0,0, 0,0,0,1],
            
            # 类别13: 反应动力学故障
            [0,0,0,0, 1,1,1,1, 0,0,0,0, 0,0,0,0, 0,1,0,0],
            
            # 类别14: 反应器冷却水阀门故障 (目标类别) - 反应器+阀门控制
            [0,0,0,0, 1,1,1,0, 1,0,0,0, 0,0,1,0, 1,0,0,1]
            
        ], dtype=np.float32)
        
        print("✅ 创建简化版TEP故障语义描述矩阵")
        print(f"目标类别验证:")
        for i, idx in enumerate([1, 6, 14]):
            desc = professional_descriptions[idx]
            print(f"  类别{idx}: {desc[:8]}... 非零数:{np.sum(desc > 0)}")
        
        return professional_descriptions
        
    def build_autoencoder(self):
        """
        简单的autoencoder用于特征提取
        """
        sample = Input(shape=self.sample_shape)
        
        # Encoder
        x = Dense(100, activation='relu')(sample)
        x = LayerNormalization()(x)
        x = Dense(200, activation='relu')(x)
        x = LayerNormalization()(x)
        feature = Dense(256, activation='relu')(x)
        
        # Decoder
        x = Dense(200, activation='relu')(feature)
        x = LayerNormalization()(x)
        x = Dense(100, activation='relu')(x)
        x = LayerNormalization()(x)
        output_sample = Dense(52, activation='linear')(x)
        
        autoencoder = Model(sample, [feature, output_sample])
        self.encoder = Model(sample, feature)
        
        print("✅ 构建简化版autoencoder")
        return autoencoder
        
    def build_semantic_mapper(self):
        """
        🔥 核心创新：语义描述→特征的直接映射网络
        """
        semantic_input = Input(shape=(self.attribute_dim,), name='semantic_description')
        
        # 多层感知机进行语义到特征的映射
        x = Dense(64, activation='relu')(semantic_input)
        x = LayerNormalization()(x)
        x = Dropout(0.3)(x)
        
        x = Dense(128, activation='relu')(x)
        x = LayerNormalization()(x)
        x = Dropout(0.3)(x)
        
        x = Dense(256, activation='relu')(x)
        x = LayerNormalization()(x)
        x = Dropout(0.2)(x)
        
        # 输出特征
        mapped_feature = Dense(256, activation='linear', name='mapped_feature')(x)
        
        mapper = Model(semantic_input, mapped_feature)
        
        print("✅ 构建语义映射器")
        return mapper
        
    def train_autoencoder(self, train_data, epochs=100):
        """
        训练autoencoder获得特征表示
        """
        print("🔄 训练autoencoder...")
        
        for epoch in range(epochs):
            with tf.GradientTape() as tape:
                feature, reconstructed = self.autoencoder(train_data)
                reconstruction_loss = tf.reduce_mean(tf.square(train_data - reconstructed))
            
            gradients = tape.gradient(reconstruction_loss, self.autoencoder.trainable_weights)
            self.autoencoder_optimizer.apply_gradients(zip(gradients, self.autoencoder.trainable_weights))
            
            if epoch % 20 == 0:
                print(f"  Epoch {epoch}/{epochs}, 重构损失: {reconstruction_loss:.6f}")
        
        print("✅ Autoencoder训练完成")
        
    def train_semantic_mapper(self, train_data, train_semantic_labels, epochs=200):
        """
        🔥 核心：训练语义描述到特征的映射
        """
        print("🔄 训练语义映射器...")
        
        # 获取真实特征
        real_features = self.encoder.predict(train_data, verbose=0)
        
        batch_size = 120
        num_batches = len(train_data) // batch_size
        
        for epoch in range(epochs):
            epoch_loss = 0
            
            for batch in range(num_batches):
                start_idx = batch * batch_size
                end_idx = (batch + 1) * batch_size
                
                batch_semantic = train_semantic_labels[start_idx:end_idx]
                batch_features = real_features[start_idx:end_idx]
                
                with tf.GradientTape() as tape:
                    # 语义描述映射到特征
                    mapped_features = self.semantic_mapper(batch_semantic)
                    
                    # 映射损失：映射的特征应该接近真实特征
                    mapping_loss = tf.reduce_mean(tf.square(mapped_features - batch_features))
                
                gradients = tape.gradient(mapping_loss, self.semantic_mapper.trainable_weights)
                self.mapper_optimizer.apply_gradients(zip(gradients, self.semantic_mapper.trainable_weights))
                
                epoch_loss += mapping_loss
            
            if epoch % 20 == 0:
                avg_loss = epoch_loss / num_batches
                print(f"  Epoch {epoch}/{epochs}, 映射损失: {avg_loss:.6f}")
        
        print("✅ 语义映射器训练完成")
        
    def generate_virtual_samples(self, target_classes, num_samples_per_class=200):
        """
        🔥 为目标类别生成虚拟特征样本
        """
        print(f"🔄 为目标类别 {target_classes} 生成虚拟样本...")
        
        virtual_features = []
        virtual_labels = []
        
        for class_idx, original_class in enumerate(target_classes):
            # 获取目标类别的语义描述
            semantic_desc = self.fault_descriptions[original_class]
            print(f"  类别{original_class}语义描述: {semantic_desc[:5]}... (非零数: {np.sum(semantic_desc > 0)})")
            
            # 重复语义描述以生成多个样本
            batch_semantics = np.tile(semantic_desc, (num_samples_per_class, 1))
            
            # 使用语义映射器生成特征
            mapped_features = self.semantic_mapper.predict(batch_semantics, verbose=0)
            
            # 添加小量噪声增加多样性
            noise = np.random.normal(0, 0.1, mapped_features.shape)
            augmented_features = mapped_features + noise
            
            virtual_features.append(augmented_features)
            virtual_labels.extend([class_idx] * num_samples_per_class)
            
            # 统计信息
            mean_feat = np.mean(augmented_features)
            std_feat = np.std(augmented_features)
            print(f"  生成特征统计: 均值={mean_feat:.4f}, 标准差={std_feat:.4f}")
        
        combined_features = np.vstack(virtual_features)
        combined_labels = np.array(virtual_labels)
        
        print(f"✅ 虚拟样本生成完成: {combined_features.shape}")
        return combined_features, combined_labels
        
    def zero_shot_test(self, test_data, target_classes):
        """
        零样本测试
        """
        print(f"🧪 开始零样本测试，目标类别: {target_classes}")
        
        # 生成虚拟样本
        virtual_features, virtual_labels = self.generate_virtual_samples(target_classes)
        
        # 训练分类器
        classifiers = {
            'lsvm': LinearSVC(random_state=42, max_iter=2000),
            'rf': RandomForestClassifier(n_estimators=50, random_state=42),
            'nb': GaussianNB(),
            'mlp': MLPClassifier(hidden_layer_sizes=(100,), random_state=42, max_iter=1000)
        }
        
        for name, clf in classifiers.items():
            clf.fit(virtual_features, virtual_labels)
        
        # 测试真实数据
        test_features = self.encoder.predict(test_data, verbose=0)
        
        # 创建真实标签
        samples_per_class = len(test_data) // len(target_classes)
        true_labels = []
        for class_idx in range(len(target_classes)):
            true_labels.extend([class_idx] * samples_per_class)
        true_labels = np.array(true_labels)
        
        # 计算准确率
        accuracies = {}
        for name, clf in classifiers.items():
            predictions = clf.predict(test_features)
            accuracies[name] = accuracy_score(true_labels, predictions)
            print(f"  {name.upper()}: {accuracies[name]:.6f}")
        
        return accuracies['lsvm'], accuracies['rf'], accuracies['nb'], accuracies['mlp']
        
    def train_and_test(self, epochs_ae=100, epochs_mapper=200):
        """
        完整的训练和测试流程
        """
        current_time = datetime.datetime.now().strftime("%Y%m%d%H%M")
        log_file = f"结果/{current_time}_simplified_semantic.md"
        
        # 创建日志文件
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("# 简化版语义迁移零样本故障诊断训练日志\n\n")
            f.write("## 核心创新点\n")
            f.write("1. 摆脱复杂ACGAN架构，直接进行语义到特征映射\n")
            f.write("2. 两阶段训练：autoencoder特征提取 + 语义映射器\n")
            f.write("3. 简单有效的虚拟样本生成机制\n\n")
        
        # 加载数据
        print("📊 加载数据...")
        traindata, trainlabel, train_attributelabel, testdata, testlabel, test_attributelabel, _, _ = read_data.creat_dataset([1, 6, 14])
        
        print(f"训练数据: {traindata.shape}, 测试数据: {testdata.shape}")
        
        # 第一阶段：训练autoencoder
        print("\n🎯 第一阶段：训练autoencoder")
        self.train_autoencoder(traindata, epochs=epochs_ae)
        
        # 第二阶段：训练语义映射器
        print("\n🎯 第二阶段：训练语义映射器")
        self.train_semantic_mapper(traindata, train_attributelabel, epochs=epochs_mapper)
        
        # 零样本测试
        print("\n🧪 零样本测试")
        target_classes = [1, 6, 14]  # 对应原始TEP故障2,7,15
        
        lsvm_acc, rf_acc, nb_acc, mlp_acc = self.zero_shot_test(testdata, target_classes)
        
        # 记录结果
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"## 最终结果\n")
            f.write(f"- LSVM: {lsvm_acc:.6f}\n")
            f.write(f"- RF: {rf_acc:.6f}\n")
            f.write(f"- NB: {nb_acc:.6f}\n")
            f.write(f"- MLP: {mlp_acc:.6f}\n")
            f.write(f"- 最佳: {max(lsvm_acc, rf_acc, nb_acc, mlp_acc):.6f}\n")
        
        print(f"\n📈 最终结果:")
        print(f"LSVM: {lsvm_acc:.6f}")
        print(f"RF: {rf_acc:.6f}")  
        print(f"NB: {nb_acc:.6f}")
        print(f"MLP: {mlp_acc:.6f}")
        print(f"最佳: {max(lsvm_acc, rf_acc, nb_acc, mlp_acc):.6f}")
        
        return max(lsvm_acc, rf_acc, nb_acc, mlp_acc)

if __name__ == '__main__':
    print("开始简化版语义迁移零样本故障诊断...")
    print("=" * 60)
    
    model = SimplifiedSemanticTransfer()
    best_accuracy = model.train_and_test(epochs_ae=50, epochs_mapper=100)
    
    print("=" * 60)
    print(f"训练完成！最佳准确率: {best_accuracy:.6f}")
    
    if best_accuracy > 0.4:  # 明显超过33.33%随机水平
        print("🎉 成功突破随机猜测水平！")
    else:
        print("😞 仍需进一步优化...") 
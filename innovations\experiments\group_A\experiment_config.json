{"model_config": {"feature_dim": 52, "attribute_dim": 20, "latent_dim": 50, "hidden_dims": [128, 256, 128]}, "training_config": {"batch_size": 32, "learning_rate_g": 0.0002, "learning_rate_d": 0.0004, "adversarial_weight": 1.0, "cycle_consistency_weight": 0.1, "semantic_distance_weight": 0.1, "uncertainty_weight": 0.1, "domain_selection_weight": 0.1, "gradient_penalty_weight": 10.0}, "data_info": {"split_group": "A", "test_classes": [1, 6, 14], "train_samples": 5760, "test_samples": 2880, "feature_dim": 52, "attribute_dim": 20, "num_classes": 12}, "final_metrics": {"best_loss": 1108295.5, "final_accuracy": 0.3333333333333333, "total_epochs": 30}}
#!/usr/bin/env python3
"""
🔧 简化版测试 - 专注核心改进

只保留最关键的改进：
1. ✅ 梯度裁剪 (基础版本)
2. ✅ 语义损失修复
3. ❌ 暂时禁用复杂的域转换和属性损失

目标：先让基础改进稳定工作
"""

import os
import sys
import torch
import argparse
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')

from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer


def main():
    parser = argparse.ArgumentParser(description='🔧 简化版测试')
    parser.add_argument('--group', type=str, choices=['A', 'B', 'C', 'D', 'E'], 
                       default='A', help='数据分组选择')
    parser.add_argument('--epochs', type=int, default=50, 
                       help='训练轮次')
    
    args = parser.parse_args()
    
    print(f"""
🔧 简化版测试 - 专注核心改进
=====================================
📊 数据分组: {args.group}
🔄 训练轮次: {args.epochs}
⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 测试策略:
✅ 保留: 梯度裁剪 + 语义损失修复
❌ 禁用: 复杂域转换 + 复杂属性损失
🎯 目标: 先让基础改进稳定工作
=====================================
    """)
    
    try:
        # 初始化训练器
        trainer = EnhancedASDCGANTrainer(
            device='cuda',
            batch_size=32,
            learning_rate_g=0.0002,
            learning_rate_d=0.0004
        )
        
        # 🔧 简化配置：禁用复杂功能
        trainer.use_domain_transfer = False  # 暂时禁用域转换
        trainer.use_attribute_classifier = False  # 暂时禁用属性分类器
        trainer.adaptive_grad_clip = False  # 使用基础梯度裁剪
        trainer.use_triplet_attribute_loss = False  # 禁用三元组损失
        trainer.max_grad_norm = 0.5  # 适中的梯度裁剪阈值
        
        print(f"""
🔧 简化配置:
- 域转换: {trainer.use_domain_transfer} (使用随机噪声)
- 属性分类器: {trainer.use_attribute_classifier}
- 自适应梯度裁剪: {trainer.adaptive_grad_clip}
- 梯度裁剪阈值: {trainer.max_grad_norm}
- 三元组损失: {trainer.use_triplet_attribute_loss}
        """)
        
        # 加载数据
        print(f"📊 加载TEP数据集 (分组 {args.group})...")
        trainer.load_data(split_group=args.group)
        
        # 开始训练
        print(f"🚀 开始简化版训练...")
        trainer.train_enhanced(epochs=args.epochs)
        
        # 获取结果
        best_accuracy = max(trainer.history.get('best_accuracy', [0])) if trainer.history.get('best_accuracy') else 0
        final_g_loss = trainer.history.get('g_loss', [0])[-1] if trainer.history.get('g_loss') else 0
        final_semantic_loss = trainer.history.get('semantic_loss', [0])[-1] if trainer.history.get('semantic_loss') else 0
        
        print(f"""
🎉 简化版测试完成！
=====================================
📊 结果:
- 最佳准确率: {best_accuracy:.2f}%
- 最终生成器损失: {final_g_loss:.1f}
- 最终语义损失: {final_semantic_loss:.4f}

📈 与目标对比:
- 生成器损失: {'✅ 优秀' if final_g_loss < 1000 else '🔧 改善' if final_g_loss < 10000 else '⚠️ 仍高'}
- 语义损失: {'✅ 正常' if final_semantic_loss > 0.01 else '⚠️ 异常'}
- 准确率: {'✅ 良好' if best_accuracy > 65 else '🔧 需要提升'}

💡 下一步建议:
{'如果简化版工作良好，可以逐步启用复杂功能' if final_g_loss < 10000 else '需要进一步调试基础功能'}
=====================================
        """)
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        raise


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
快速改进测试 - 直接使用优化参数测试Group A
目标: 快速验证优化策略是否有效
"""

import os
import sys
import json
import torch
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')

def run_quick_test():
    """运行快速改进测试"""
    print("🚀 ASDCGAN快速改进测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 目标: 使用优化参数快速测试Group A性能提升")
    print("=" * 60)
    
    try:
        # 导入训练器
        from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer
        
        print("✅ 成功导入训练器")
        
        # 使用优化参数创建训练器
        print("\n🔧 创建优化训练器...")
        trainer = EnhancedASDCGANTrainer(
            device='cuda' if torch.cuda.is_available() else 'cpu',
            batch_size=64,  # 优化：增大批次
            learning_rate_g=0.0001,  # 优化：降低生成器学习率
            learning_rate_d=0.0002   # 优化：降低判别器学习率
        )
        
        # 手动设置优化的损失权重
        trainer.adversarial_weight = 0.5  # 降低对抗权重
        trainer.cycle_consistency_weight = 0.2  # 增强循环一致性
        trainer.semantic_distance_weight = 0.15  # 增强语义约束
        
        print(f"   设备: {trainer.device}")
        print(f"   批次大小: {trainer.batch_size}")
        print(f"   生成器学习率: {trainer.learning_rate_g}")
        print(f"   判别器学习率: {trainer.learning_rate_d}")
        print(f"   对抗权重: {trainer.adversarial_weight}")
        print(f"   循环一致性权重: {trainer.cycle_consistency_weight}")
        print(f"   语义距离权重: {trainer.semantic_distance_weight}")
        
        # 设置Group A数据
        print("\n📊 设置Group A数据...")
        test_classes = [1, 6, 14]  # Group A测试类别
        trainer.load_data(split_group='A')  # 使用正确的方法名

        print(f"   测试类别: {test_classes}")
        print(f"   训练样本: {len(trainer.train_loader.dataset) if hasattr(trainer, 'train_loader') else 'N/A'}")
        
        # 快速训练测试 (只训练50个epoch)
        print("\n🚀 开始快速训练测试 (50 epochs)...")
        print("   这将快速验证优化策略是否有效...")

        history = trainer.train_enhanced(epochs=50)
        
        # 分析结果
        print("\n📊 快速测试结果:")
        if history and 'accuracy' in history and history['accuracy']:
            final_acc = history['accuracy'][-1]
            best_acc = max(history['accuracy'])
            initial_acc = history['accuracy'][0] if len(history['accuracy']) > 0 else 0
            
            print(f"   初始准确率: {initial_acc:.2f}%")
            print(f"   最终准确率: {final_acc:.2f}%")
            print(f"   最佳准确率: {best_acc:.2f}%")
            print(f"   提升幅度: {final_acc - initial_acc:+.2f}%")
            
            # 与之前结果对比
            previous_best = 60.69  # Group A之前的最佳结果
            improvement = best_acc - previous_best
            
            print(f"\n📈 与之前对比:")
            print(f"   之前最佳: {previous_best:.2f}%")
            print(f"   当前最佳: {best_acc:.2f}%")
            print(f"   改进效果: {improvement:+.2f}%")
            
            # 评估改进效果
            if improvement > 5:
                print("   ✅ 显著改进！优化策略有效")
                recommendation = "继续使用这些参数进行完整训练"
            elif improvement > 2:
                print("   ⚡ 中等改进，优化策略部分有效")
                recommendation = "微调参数后进行完整训练"
            elif improvement > 0:
                print("   ⚠️ 轻微改进，需要进一步优化")
                recommendation = "调整超参数或尝试其他策略"
            else:
                print("   ❌ 无改进或性能下降")
                recommendation = "重新评估优化策略"
            
            print(f"   🔧 建议: {recommendation}")
            
            # 预测完整训练结果
            if improvement > 0:
                projected_full = best_acc + (improvement * 2)  # 简单预测
                print(f"\n🔮 完整训练预测:")
                print(f"   预计最终准确率: {projected_full:.2f}%")
                if projected_full >= 75:
                    print("   🎯 有望达到发表标准 (75%+)")
                else:
                    print("   ⚠️ 可能仍需进一步优化")
            
            return {
                'success': True,
                'initial_acc': initial_acc,
                'final_acc': final_acc,
                'best_acc': best_acc,
                'improvement': improvement,
                'recommendation': recommendation
            }
        else:
            print("   ❌ 无法获取训练结果")
            return {'success': False, 'error': '无训练历史数据'}
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

def main():
    """主函数"""
    print("开始ASDCGAN快速改进测试...")
    
    result = run_quick_test()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结")
    print("=" * 60)
    
    if result['success']:
        print("✅ 快速测试成功完成")
        print(f"📊 性能改进: {result.get('improvement', 0):+.2f}%")
        print(f"🔧 建议: {result.get('recommendation', '无')}")
        
        if result.get('improvement', 0) > 2:
            print("\n🚀 下一步行动:")
            print("1. 使用相同参数进行完整训练 (500-1000 epochs)")
            print("2. 应用相同优化策略到其他组别")
            print("3. 进行更精细的超参数调优")
        else:
            print("\n🔧 需要进一步优化:")
            print("1. 调整学习率和批次大小")
            print("2. 重新平衡损失函数权重")
            print("3. 考虑架构层面的改进")
    else:
        print("❌ 快速测试失败")
        print(f"错误: {result.get('error', '未知错误')}")
        print("\n🔧 故障排除:")
        print("1. 检查环境配置")
        print("2. 验证数据加载")
        print("3. 检查模型架构")
    
    return result['success']

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

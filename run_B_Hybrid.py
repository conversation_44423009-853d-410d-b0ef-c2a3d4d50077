#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 B-Hybrid实验启动脚本
融合HardTriplet强分离能力与SmartCRL稳定性

使用方法:
1. 直接运行: python run_B_Hybrid.py
2. 在tmux中运行: tmux new-session -d -s B-Hybrid 'python run_B_Hybrid.py'
3. Docker中运行: docker start acgan-container && docker exec -it acgan-container python run_B_Hybrid.py

Created: 2025-07-15
"""

import os
import sys
import datetime

def main():
    print("=" * 80)
    print("🔥 B-Hybrid实验启动")
    print("=" * 80)
    print(f"📅 启动时间: {datetime.datetime.now()}")
    print(f"🎯 实验目标: 融合HardTriplet强分离能力与SmartCRL稳定性")
    print(f"⚡ 核心策略:")
    print(f"   - 保持HardTriplet强权重: lambda_triplet=50, lambda_center=2.5")
    print(f"   - 适度引入SmartCRL约束: lambda_crl=0.05, lambda_semantic=0.05")
    print(f"   - 目标类别: [4, 7, 10] (B组困难类别)")
    print(f"📊 预期目标:")
    print(f"   - 保持55-58%的峰值准确率")
    print(f"   - 显著提升训练稳定性")
    print(f"   - 减少G loss异常峰值")
    print(f"   - 可能突破60%准确率")
    print("=" * 80)
    
    # 检查脚本是否存在
    script_path = "scripts/acgan_triplet_B_Hybrid.py"
    if not os.path.exists(script_path):
        print(f"❌ 错误: 找不到脚本文件 {script_path}")
        return
    
    # 检查依赖
    try:
        import tensorflow as tf
        import numpy as np
        import sklearn
        print(f"✅ 依赖检查通过")
        print(f"   - TensorFlow: {tf.__version__}")
        print(f"   - NumPy: {np.__version__}")
        print(f"   - Scikit-learn: {sklearn.__version__}")
    except ImportError as e:
        print(f"❌ 依赖检查失败: {e}")
        return
    
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    print(f"✅ 日志目录已创建: logs/")
    
    print("\n🚀 开始执行B-Hybrid实验...")
    print("=" * 80)
    
    # 导入并运行实验
    try:
        sys.path.append('scripts')
        from acgan_triplet_B_Hybrid import Zero_shot
        
        # 创建模型实例
        model = Zero_shot()
        
        # 开始训练
        model.train(epochs=2000, batch_size=256)
        
    except Exception as e:
        print(f"❌ 实验执行失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🎉 B-Hybrid实验完成!")
    print("=" * 80)

if __name__ == "__main__":
    main()

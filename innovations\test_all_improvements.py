#!/usr/bin/env python3
"""
🔥 全面改进版ASDCGAN测试脚本

实现的三大关键改进：
1. ✅ 梯度裁剪 - 防止损失爆炸 (解决G_loss~1800问题)
2. ✅ 域转换 - 用真实特征替换随机噪声 (更符合零样本学习本质)
3. ✅ 属性一致性损失 - 完善的属性约束机制

预期改进效果：
- 生成器损失: 1800+ → <100
- 语义损失: 0.0000 → 有意义的非零值
- 训练稳定性: 显著改善
- 准确率: A组 60.97% → 75-85%, B组 39.17% → 60-70%
"""

import os
import sys
import torch
import argparse
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')

from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer


def main():
    parser = argparse.ArgumentParser(description='🔥 全面改进版ASDCGAN训练')
    parser.add_argument('--group', type=str, choices=['A', 'B', 'C', 'D', 'E'], 
                       default='A', help='数据分组选择')
    parser.add_argument('--epochs', type=int, default=2000,
                       help='训练轮次 (默认2000)')
    parser.add_argument('--batch_size', type=int, default=32, 
                       help='批次大小')
    parser.add_argument('--device', type=str, default='cuda', 
                       help='设备选择')
    parser.add_argument('--quick_test', action='store_true', 
                       help='快速测试模式 (50 epochs)')
    parser.add_argument('--disable_domain_transfer', action='store_true',
                       help='禁用域转换 (使用随机噪声)')
    parser.add_argument('--disable_grad_clip', action='store_true',
                       help='禁用梯度裁剪')
    parser.add_argument('--disable_attr_consistency', action='store_true',
                       help='禁用属性一致性损失')
    
    args = parser.parse_args()
    
    # 快速测试模式
    if args.quick_test:
        args.epochs = 50
        print("🚀 快速测试模式：50 epochs")
    
    print(f"""
🔥 全面改进版ASDCGAN训练启动
=====================================
📊 数据分组: {args.group}
🔄 训练轮次: {args.epochs}
📦 批次大小: {args.batch_size}
💻 设备: {args.device}
⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 三大关键改进:
1. 梯度裁剪: {'启用' if not args.disable_grad_clip else '禁用'}
2. 域转换: {'启用' if not args.disable_domain_transfer else '禁用'}
3. 属性一致性: {'启用' if not args.disable_attr_consistency else '禁用'}

🔧 优化配置:
- 循环损失权重: 10.0 → 1.0
- 语义损失权重: 5.0 → 0.5
- 生成器学习率: 0.0001 → 0.0002
- 判别器学习率: 0.0002 → 0.0004
- 梯度裁剪阈值: 1.0
=====================================
    """)
    
    try:
        # 初始化训练器 (使用全面改进的参数)
        trainer = EnhancedASDCGANTrainer(
            device=args.device,
            batch_size=args.batch_size,
            learning_rate_g=0.0002,  # 🔥 优化后的学习率
            learning_rate_d=0.0004   # 🔥 优化后的学习率
        )
        
        # 根据参数调整配置
        if args.disable_domain_transfer:
            trainer.use_domain_transfer = False
            print("⚠️  域转换已禁用，将使用随机噪声")
            
        if args.disable_grad_clip:
            trainer.enable_grad_clip = False
            print("⚠️  梯度裁剪已禁用")
            
        if args.disable_attr_consistency:
            trainer.use_attribute_classifier = False
            print("⚠️  属性一致性损失已禁用")
        
        # 验证配置
        print(f"""
✅ 最终配置验证:
- 对抗损失权重: {trainer.adversarial_weight}
- 循环损失权重: {trainer.cycle_consistency_weight} (优化: 10.0→1.0)
- 语义损失权重: {trainer.semantic_distance_weight} (优化: 5.0→0.5)
- 不确定性权重: {trainer.uncertainty_weight} (优化: 1.0→0.5)
- 属性一致性权重: {trainer.attribute_consistency_weight} (新增)
- 域选择权重: {trainer.domain_selection_weight} (优化: 2.0→1.0)
- 梯度裁剪: {trainer.enable_grad_clip} (阈值: {trainer.max_grad_norm})
- 域转换: {trainer.use_domain_transfer} (噪声比例: {trainer.noise_ratio})
- 属性分类器: {trainer.use_attribute_classifier}
        """)
        
        # 加载数据
        print(f"📊 加载TEP数据集 (分组 {args.group})...")
        trainer.load_data(split_group=args.group)
        
        # 开始训练
        print(f"🚀 开始训练分组 {args.group}...")
        trainer.train_enhanced(epochs=args.epochs)
        
        # 获取最终结果
        best_accuracy = max(trainer.history.get('best_accuracy', [0])) if trainer.history.get('best_accuracy') else 0
        final_g_loss = trainer.history.get('g_loss', [0])[-1] if trainer.history.get('g_loss') else 0
        final_semantic_loss = trainer.history.get('semantic_loss', [0])[-1] if trainer.history.get('semantic_loss') else 0
        
        # 输出结果
        print(f"""
🎉 训练完成！
=====================================
📊 最终结果:
- 最佳准确率: {best_accuracy:.2f}%
- 最终生成器损失: {final_g_loss:.4f}
- 最终语义损失: {final_semantic_loss:.4f}

📈 改进效果对比:
- 生成器损失: 1800+ → {final_g_loss:.1f} ({'✅ 显著改善' if final_g_loss < 200 else '⚠️ 仍需优化'})
- 语义损失: 0.0000 → {final_semantic_loss:.4f} ({'✅ 已修复' if final_semantic_loss > 0.001 else '⚠️ 仍为零'})
- A组准确率目标: 60.97% → 75-85% (当前: {best_accuracy:.2f}%)

📁 结果保存位置:
- 实验目录: {getattr(trainer, 'experiment_dir', 'N/A')}
- 模型文件: {getattr(trainer, 'experiment_dir', 'N/A')}/models/
- 训练日志: {getattr(trainer, 'log_file', 'N/A')}
- TensorBoard: {getattr(trainer, 'tensorboard_dir', 'N/A')}
=====================================
        """)
        
        # 性能分析
        if best_accuracy > 75:
            print("🎯 优秀！准确率超过75%，三大改进效果显著！")
        elif best_accuracy > 65:
            print("✅ 良好！准确率有明显提升，改进方向正确！")
        elif final_g_loss < 200:
            print("🔧 进步！损失已显著降低，继续优化中...")
        else:
            print("⚠️  需要进一步调优，建议检查:")
            print("1. 数据预处理是否正确")
            print("2. 模型架构是否合适")
            print("3. 超参数是否需要调整")
            
        # 改进建议
        print(f"""
💡 下一步建议:
1. 如果损失仍高: 调整梯度裁剪阈值 (当前: {trainer.max_grad_norm})
2. 如果语义损失为0: 检查语义距离计算
3. 如果准确率低: 尝试调整损失权重
4. 运行完整训练: --epochs 1000
5. 测试其他分组: --group B/C/D/E
        """)
            
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        print("💡 建议检查:")
        print("1. GPU内存是否充足")
        print("2. 数据文件是否存在")
        print("3. 依赖包是否正确安装")
        print("4. CUDA版本是否兼容")
        raise


if __name__ == "__main__":
    main()

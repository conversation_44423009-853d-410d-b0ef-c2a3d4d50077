"""
变分生成器模块 (PyTorch版本)

基于VAE框架的生成器，支持不确定性量化和潜在空间操作。

核心功能：
1. 基于属性条件的特征生成
2. 不确定性量化和传播
3. 潜在空间插值和操作
4. 循环一致性支持

技术特点：
- 变分自编码器架构
- 重参数化技巧
- 条件生成
- 不确定性估计
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class VariationalGenerator(nn.Module):
    """
    变分生成器
    
    基于VAE框架的生成器，支持不确定性量化。
    """
    
    def __init__(self, 
                 feature_dim=52,
                 attribute_dim=20,
                 latent_dim=50,
                 hidden_dims=None,
                 dropout_rate=0.1,
                 use_batch_norm=True,
                 activation='leaky_relu'):
        """
        初始化变分生成器
        
        Args:
            feature_dim: 特征维度
            attribute_dim: 属性维度
            latent_dim: 潜在空间维度
            hidden_dims: 隐藏层维度列表
            dropout_rate: Dropout比率
            use_batch_norm: 是否使用批归一化
            activation: 激活函数 ('relu', 'leaky_relu', 'tanh')
        """
        super(VariationalGenerator, self).__init__()
        
        # 保存配置
        self.feature_dim = feature_dim
        self.attribute_dim = attribute_dim
        self.latent_dim = latent_dim
        self.dropout_rate = dropout_rate
        self.use_batch_norm = use_batch_norm
        
        # 设置隐藏层维度
        if hidden_dims is None:
            hidden_dims = [128, 256, 128]
        self.hidden_dims = hidden_dims
        
        # 设置激活函数
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'leaky_relu':
            self.activation = nn.LeakyReLU(0.2)
        elif activation == 'tanh':
            self.activation = nn.Tanh()
        else:
            raise ValueError(f"不支持的激活函数: {activation}")
        
        # 编码器网络
        encoder_layers = []
        
        # 输入层
        input_dim = feature_dim + attribute_dim  # 特征和属性拼接
        
        # 构建编码器隐藏层
        for h_dim in hidden_dims:
            encoder_layers.append(nn.Linear(input_dim, h_dim))
            if use_batch_norm:
                encoder_layers.append(nn.BatchNorm1d(h_dim))
            encoder_layers.append(self.activation)
            encoder_layers.append(nn.Dropout(dropout_rate))
            input_dim = h_dim
        
        self.encoder = nn.Sequential(*encoder_layers)
        
        # 均值和方差预测
        self.fc_mu = nn.Linear(hidden_dims[-1], latent_dim)
        self.fc_var = nn.Linear(hidden_dims[-1], latent_dim)
        
        # 解码器网络
        decoder_layers = []
        
        # 输入层
        input_dim = latent_dim + attribute_dim  # 潜在向量和属性拼接
        
        # 构建解码器隐藏层
        for h_dim in reversed(hidden_dims):
            decoder_layers.append(nn.Linear(input_dim, h_dim))
            if use_batch_norm:
                decoder_layers.append(nn.BatchNorm1d(h_dim))
            decoder_layers.append(self.activation)
            decoder_layers.append(nn.Dropout(dropout_rate))
            input_dim = h_dim
        
        self.decoder = nn.Sequential(*decoder_layers)
        
        # 输出层
        self.final_layer = nn.Linear(hidden_dims[0], feature_dim)
        
        # 不确定性估计层
        self.uncertainty_estimator = nn.Sequential(
            nn.Linear(hidden_dims[0], hidden_dims[0] // 2),
            self.activation,
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dims[0] // 2, feature_dim),
            nn.Softplus()  # 确保不确定性为正
        )
    
    def encode(self, features, attributes):
        """
        编码特征和属性到潜在空间
        
        Args:
            features: 特征向量 [batch_size, feature_dim]
            attributes: 属性向量 [batch_size, attribute_dim]
            
        Returns:
            mu: 均值 [batch_size, latent_dim]
            log_var: 对数方差 [batch_size, latent_dim]
        """
        # 拼接特征和属性
        x = torch.cat([features, attributes], dim=1)
        
        # 编码
        x = self.encoder(x)
        
        # 预测均值和对数方差
        mu = self.fc_mu(x)
        log_var = self.fc_var(x)
        
        return mu, log_var
    
    def reparameterize(self, mu, log_var):
        """
        重参数化技巧
        
        Args:
            mu: 均值 [batch_size, latent_dim]
            log_var: 对数方差 [batch_size, latent_dim]
            
        Returns:
            z: 采样的潜在向量 [batch_size, latent_dim]
        """
        std = torch.exp(0.5 * log_var)
        eps = torch.randn_like(std)
        z = mu + eps * std
        return z
    
    def decode(self, z, attributes):
        """
        从潜在空间解码生成特征
        
        Args:
            z: 潜在向量 [batch_size, latent_dim]
            attributes: 属性向量 [batch_size, attribute_dim]
            
        Returns:
            features: 生成的特征 [batch_size, feature_dim]
            uncertainty: 不确定性估计 [batch_size, feature_dim]
        """
        # 拼接潜在向量和属性
        x = torch.cat([z, attributes], dim=1)
        
        # 解码
        x = self.decoder(x)
        
        # 生成特征
        features = self.final_layer(x)
        
        # 估计不确定性
        uncertainty = self.uncertainty_estimator(x)
        
        return features, uncertainty
    
    def forward(self, noise, attributes):
        """
        前向传播生成特征
        
        Args:
            noise: 噪声向量 [batch_size, latent_dim] 或 None
            attributes: 属性向量 [batch_size, attribute_dim]
            
        Returns:
            result_dict: 包含生成结果的字典
        """
        batch_size = attributes.shape[0]
        
        if noise is None:
            # 如果没有提供噪声，使用标准正态分布采样
            noise = torch.randn(batch_size, self.latent_dim, device=attributes.device)
        
        # 直接使用噪声作为潜在向量
        z = noise
        
        # 解码生成特征
        generated_features, uncertainty = self.decode(z, attributes)
        
        return {
            'generated_features': generated_features,
            'uncertainty': uncertainty,
            'latent_vector': z
        }
    
    def reconstruct(self, features, attributes):
        """
        重构特征
        
        Args:
            features: 特征向量 [batch_size, feature_dim]
            attributes: 属性向量 [batch_size, attribute_dim]
            
        Returns:
            result_dict: 包含重构结果的字典
        """
        # 编码
        mu, log_var = self.encode(features, attributes)
        
        # 采样潜在向量
        z = self.reparameterize(mu, log_var)
        
        # 解码
        reconstructed_features, uncertainty = self.decode(z, attributes)
        
        return {
            'reconstructed_features': reconstructed_features,
            'uncertainty': uncertainty,
            'latent_vector': z,
            'mu': mu,
            'log_var': log_var
        }
    
    def generate_with_uncertainty(self, attributes, num_samples=10):
        """
        生成多个样本以估计不确定性
        
        Args:
            attributes: 属性向量 [batch_size, attribute_dim]
            num_samples: 每个属性生成的样本数
            
        Returns:
            mean_features: 平均特征 [batch_size, feature_dim]
            std_features: 特征标准差 [batch_size, feature_dim]
            all_samples: 所有样本 [num_samples, batch_size, feature_dim]
        """
        batch_size = attributes.shape[0]
        device = attributes.device
        
        # 生成多个样本
        samples = []
        for _ in range(num_samples):
            noise = torch.randn(batch_size, self.latent_dim, device=device)
            result = self.forward(noise, attributes)
            samples.append(result['generated_features'])
        
        # 堆叠所有样本
        all_samples = torch.stack(samples, dim=0)  # [num_samples, batch_size, feature_dim]
        
        # 计算均值和标准差
        mean_features = torch.mean(all_samples, dim=0)  # [batch_size, feature_dim]
        std_features = torch.std(all_samples, dim=0)  # [batch_size, feature_dim]
        
        return mean_features, std_features, all_samples
    
    def interpolate_latent(self, z1, z2, attributes, steps=10):
        """
        在潜在空间中插值
        
        Args:
            z1: 起始潜在向量 [batch_size, latent_dim]
            z2: 结束潜在向量 [batch_size, latent_dim]
            attributes: 属性向量 [batch_size, attribute_dim]
            steps: 插值步数
            
        Returns:
            interpolated_features: 插值特征 [steps, batch_size, feature_dim]
        """
        device = z1.device
        batch_size = z1.shape[0]
        
        # 生成插值系数
        alphas = torch.linspace(0, 1, steps, device=device)
        
        # 插值结果
        interpolated_features = []
        
        for alpha in alphas:
            # 线性插值
            z_interp = z1 * (1 - alpha) + z2 * alpha
            
            # 解码
            features, _ = self.decode(z_interp, attributes)
            interpolated_features.append(features)
        
        return torch.stack(interpolated_features, dim=0)

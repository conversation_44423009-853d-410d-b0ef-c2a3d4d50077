#!/usr/bin/env python3
"""
🎯 简化改进版训练器 - 回归基础 + 核心改进

策略：
1. 保留基础ASDCGAN架构
2. 只添加最关键的改进：
   - ✅ 梯度裁剪 (简单版本)
   - ✅ 语义损失修复
   - ✅ 优化的损失权重
3. 移除所有复杂功能
4. 专注于稳定性和效果

目标：
- 生成器损失 < 1,000
- A组准确率 > 70%
- 训练稳定
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from datetime import datetime
import logging

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')

# 导入基础组件
from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer


class SimpleImprovedTrainer(EnhancedASDCGANTrainer):
    """
    🎯 简化改进版训练器
    
    基于EnhancedASDCGANTrainer，但移除复杂功能，
    只保留最核心的改进
    """
    
    def __init__(self, device='cuda', batch_size=32, 
                 learning_rate_g=0.0002, learning_rate_d=0.0004):
        
        # 调用父类初始化
        super().__init__(device, batch_size, learning_rate_g, learning_rate_d)
        
        # 🎯 简化配置：只保留核心改进
        self.use_domain_transfer = False  # 禁用复杂域转换
        self.use_attribute_classifier = False  # 禁用复杂属性分类器
        self.adaptive_grad_clip = False  # 禁用自适应梯度裁剪
        self.use_triplet_attribute_loss = False  # 禁用三元组损失
        self.use_semantic_guided_interpolation = False  # 禁用语义插值
        
        # 🔥 核心改进：简单但有效的梯度裁剪
        self.enable_grad_clip = True
        self.max_grad_norm = 0.5  # 适中的阈值
        
        # 🔥 优化的损失权重 (基于测试结果调整)
        self.adversarial_weight = 1.0
        self.cycle_consistency_weight = 0.5  # 进一步降低
        self.semantic_distance_weight = 0.3  # 进一步降低
        self.uncertainty_weight = 0.3  # 进一步降低
        self.domain_selection_weight = 0.5  # 进一步降低
        
        print("🎯 简化改进版训练器初始化完成")
        print(f"   梯度裁剪: {self.enable_grad_clip} (阈值: {self.max_grad_norm})")
        print(f"   损失权重: Adv={self.adversarial_weight}, Cycle={self.cycle_consistency_weight}")
        print(f"   复杂功能: 全部禁用，专注核心改进")
    
    def train_step(self, features, attributes, labels):
        """
        🎯 简化的训练步骤
        
        移除所有复杂逻辑，只保留：
        1. 基础对抗训练
        2. 循环一致性
        3. 语义距离 (修复版本)
        4. 简单梯度裁剪
        """
        batch_size = features.shape[0]
        real_features = features.to(self.device)
        real_attributes = attributes.to(self.device)
        
        # ==================== 判别器训练 ====================
        self.optimizer_d.zero_grad()
        
        # 真实样本
        real_inputs = {'features': real_features, 'attributes': real_attributes}
        real_output = self.discriminator(real_inputs)
        real_validity = real_output['final_validity']  # 🔥 修复：使用正确的键名
        
        # 生成假样本 (使用简单随机噪声)
        noise = torch.randn(batch_size, 50).to(self.device)
        with torch.no_grad():
            gen_result = self.generator(noise, real_attributes)
            fake_features = gen_result['generated_features']
        
        # 假样本判别
        fake_inputs = {'features': fake_features.detach(), 'attributes': real_attributes}
        fake_output = self.discriminator(fake_inputs)
        fake_validity = fake_output['final_validity']  # 🔥 修复：使用正确的键名
        
        # 判别器损失 (修复：使用BCEWithLogitsLoss，不需要sigmoid)
        d_loss_real = nn.BCEWithLogitsLoss()(real_validity, torch.ones_like(real_validity))
        d_loss_fake = nn.BCEWithLogitsLoss()(fake_validity, torch.zeros_like(fake_validity))
        d_loss = (d_loss_real + d_loss_fake) / 2
        
        d_loss.backward()
        
        # 🔥 简单梯度裁剪
        if self.enable_grad_clip:
            torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), self.max_grad_norm)
        
        self.optimizer_d.step()
        
        # ==================== 生成器训练 ====================
        self.optimizer_g.zero_grad()
        
        # 重新生成 (需要梯度)
        gen_result = self.generator(noise, real_attributes)
        fake_features = gen_result['generated_features']
        uncertainty = gen_result['uncertainty']
        
        # 对抗损失
        fake_inputs = {'features': fake_features, 'attributes': real_attributes}
        fake_output = self.discriminator(fake_inputs)
        fake_validity = fake_output['final_validity']  # 🔥 修复：使用正确的键名
        adversarial_loss = nn.BCEWithLogitsLoss()(fake_validity, torch.ones_like(fake_validity))  # 🔥 修复：使用BCEWithLogitsLoss
        
        # 循环一致性损失
        cycle_loss = nn.MSELoss()(fake_features, real_features)
        
        # 🔥 修复的语义损失 (简化版本)
        # 创建不同的属性对进行语义距离计算
        shuffled_indices = torch.randperm(batch_size, device=real_attributes.device)
        shuffled_attributes = real_attributes[shuffled_indices]
        
        semantic_distance = self.semantic_distance_calculator(
            real_attributes, shuffled_attributes, fake_features
        )
        
        # 简化的语义损失计算
        semantic_loss = torch.mean(semantic_distance)
        
        # 不确定性损失
        uncertainty_loss = torch.mean(uncertainty)
        
        # 🔥 简化：移除域选择损失，专注核心功能
        domain_loss = torch.tensor(0.0, device=self.device)
        
        # 🔥 总损失 (简化权重，移除域选择)
        g_loss = (self.adversarial_weight * adversarial_loss +
                 self.cycle_consistency_weight * cycle_loss +
                 self.semantic_distance_weight * semantic_loss +
                 self.uncertainty_weight * uncertainty_loss)
        
        g_loss.backward()
        
        # 🔥 生成器梯度裁剪 (简化：只包含核心组件)
        if self.enable_grad_clip:
            generator_params = (
                list(self.generator.parameters()) +
                list(self.semantic_distance_calculator.parameters())
            )
            torch.nn.utils.clip_grad_norm_(generator_params, self.max_grad_norm)
        
        self.optimizer_g.step()
        
        # 返回损失信息 (包含所有期望的键)
        return {
            'g_loss': g_loss.item(),
            'd_loss': d_loss.item(),
            'adversarial_loss': adversarial_loss.item(),
            'cycle_loss': cycle_loss.item(),
            'semantic_loss': semantic_loss.item(),
            'uncertainty_loss': uncertainty_loss.item(),
            'domain_loss': domain_loss.item(),
            'attribute_consistency_loss': 0.0,  # 🔥 简化版本中禁用
            'gradient_penalty': 0.0,  # 🔥 简化版本中禁用
            'domain_entropy': 0.0,  # 🔥 简化版本中禁用
            'grad_norm': self.max_grad_norm  # 🔥 简化版本中使用固定值
        }


def test_simple_improved():
    """测试简化改进版本"""
    print(f"""
🎯 简化改进版测试
=====================================
⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 改进策略:
✅ 保留: 梯度裁剪 + 语义损失修复 + 优化权重
❌ 移除: 所有复杂功能 (域转换、三元组损失等)
🎯 目标: 生成器损失 < 1,000, 准确率 > 70%
=====================================
    """)
    
    try:
        # 初始化简化训练器
        trainer = SimpleImprovedTrainer(
            device='cuda',
            batch_size=32,
            learning_rate_g=0.0002,
            learning_rate_d=0.0004
        )
        
        # 加载数据
        print("📊 加载TEP数据集 (分组 A)...")
        trainer.load_data(split_group='A')
        
        # 开始训练
        print("🚀 开始简化改进训练...")
        trainer.train_enhanced(epochs=50)
        
        # 获取结果
        best_accuracy = max(trainer.history.get('best_accuracy', [0])) if trainer.history.get('best_accuracy') else 0
        final_g_loss = trainer.history.get('g_loss', [0])[-1] if trainer.history.get('g_loss') else 0
        final_semantic_loss = trainer.history.get('semantic_loss', [0])[-1] if trainer.history.get('semantic_loss') else 0
        
        print(f"""
🎉 简化改进版测试完成！
=====================================
📊 结果:
- 最佳准确率: {best_accuracy:.2f}%
- 最终生成器损失: {final_g_loss:.1f}
- 最终语义损失: {final_semantic_loss:.4f}

📈 与复杂版本对比:
- 生成器损失: 59,723 → {final_g_loss:.1f} ({'✅ 显著改善' if final_g_loss < 10000 else '🔧 仍需优化'})
- 准确率: 58.19% → {best_accuracy:.2f}% ({'✅ 有提升' if best_accuracy > 58.19 else '⚠️ 需要改进'})
- 训练稳定性: {'✅ 改善' if final_g_loss < 100000 else '⚠️ 仍不稳定'}

🎯 目标达成情况:
- 生成器损失 < 1,000: {'✅ 达成' if final_g_loss < 1000 else '❌ 未达成'}
- 准确率 > 70%: {'✅ 达成' if best_accuracy > 70 else '❌ 未达成'}
=====================================
        """)
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_simple_improved()

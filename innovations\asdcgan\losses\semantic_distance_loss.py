"""
语义距离损失

基于自适应语义距离的损失函数，确保生成特征在语义空间中的合理分布。

核心功能：
1. 语义距离保持损失 - 确保语义关系的一致性
2. 语义聚类损失 - 促进同类特征聚集
3. 语义分离损失 - 促进异类特征分离
4. 语义平滑损失 - 确保语义空间的平滑性

技术特点：
- 基于自适应语义距离计算
- 支持多种语义约束策略
- 与域选择机制协同工作
"""

import tensorflow as tf
from tensorflow.keras.losses import Loss
import numpy as np


class SemanticDistancePreservationLoss(tf.keras.layers.Layer):
    """语义距离保持损失"""
    
    def __init__(self, margin=1.0, **kwargs):
        super(SemanticDistancePreservationLoss, self).__init__(**kwargs)
        self.margin = margin
        
    def call(self, original_distances, generated_distances):
        """
        计算语义距离保持损失
        
        Args:
            original_distances: 原始语义距离 [batch_size, 1]
            generated_distances: 生成特征的语义距离 [batch_size, 1]
            
        Returns:
            preservation_loss: 距离保持损失
        """
        # 使用平滑L1损失确保距离保持
        distance_diff = tf.abs(original_distances - generated_distances)
        preservation_loss = tf.where(
            distance_diff < self.margin,
            0.5 * tf.square(distance_diff),
            self.margin * distance_diff - 0.5 * tf.square(self.margin)
        )
        
        return tf.reduce_mean(preservation_loss)


class SemanticClusteringLoss(tf.keras.layers.Layer):
    """语义聚类损失"""
    
    def __init__(self, temperature=0.1, **kwargs):
        super(SemanticClusteringLoss, self).__init__(**kwargs)
        self.temperature = temperature
        
    def call(self, features, attributes, semantic_distance_calculator):
        """
        计算语义聚类损失
        
        Args:
            features: 特征向量 [batch_size, feature_dim]
            attributes: 属性向量 [batch_size, attribute_dim]
            semantic_distance_calculator: 语义距离计算器
            
        Returns:
            clustering_loss: 聚类损失
        """
        batch_size = tf.shape(features)[0]
        
        # 计算所有特征对之间的语义距离
        clustering_loss = 0.0
        
        for i in range(batch_size):
            for j in range(i + 1, batch_size):
                # 获取特征对
                feat_i = features[i:i+1]
                feat_j = features[j:j+1]
                attr_i = attributes[i:i+1]
                attr_j = attributes[j:j+1]
                
                # 计算语义距离
                context_i = tf.zeros_like(feat_i)  # 简化的上下文
                semantic_dist = semantic_distance_calculator([attr_i, attr_j, context_i])
                
                # 计算特征距离
                feature_dist = tf.norm(feat_i - feat_j, axis=-1, keepdims=True)
                
                # 如果属性相似，特征也应该相似
                attr_similarity = tf.exp(-tf.norm(attr_i - attr_j, axis=-1, keepdims=True))
                
                # 聚类损失：属性相似时，特征距离应该小
                cluster_loss = attr_similarity * feature_dist
                clustering_loss += cluster_loss
        
        # 归一化
        num_pairs = batch_size * (batch_size - 1) / 2
        clustering_loss = clustering_loss / num_pairs
        
        return tf.reduce_mean(clustering_loss)


class SemanticSeparationLoss(tf.keras.layers.Layer):
    """语义分离损失"""
    
    def __init__(self, margin=2.0, **kwargs):
        super(SemanticSeparationLoss, self).__init__(**kwargs)
        self.margin = margin
        
    def call(self, features, attributes):
        """
        计算语义分离损失
        
        Args:
            features: 特征向量 [batch_size, feature_dim]
            attributes: 属性向量 [batch_size, attribute_dim]
            
        Returns:
            separation_loss: 分离损失
        """
        batch_size = tf.shape(features)[0]
        separation_loss = 0.0
        
        for i in range(batch_size):
            for j in range(i + 1, batch_size):
                # 获取特征对
                feat_i = features[i:i+1]
                feat_j = features[j:j+1]
                attr_i = attributes[i:i+1]
                attr_j = attributes[j:j+1]
                
                # 计算属性差异
                attr_diff = tf.norm(attr_i - attr_j, axis=-1, keepdims=True)
                
                # 计算特征距离
                feature_dist = tf.norm(feat_i - feat_j, axis=-1, keepdims=True)
                
                # 如果属性差异大，特征距离也应该大
                separation_loss += tf.maximum(
                    0.0, 
                    self.margin - feature_dist + attr_diff
                )
        
        # 归一化
        num_pairs = batch_size * (batch_size - 1) / 2
        separation_loss = separation_loss / num_pairs
        
        return tf.reduce_mean(separation_loss)


class SemanticSmoothLoss(tf.keras.layers.Layer):
    """语义平滑损失"""
    
    def __init__(self, **kwargs):
        super(SemanticSmoothLoss, self).__init__(**kwargs)
        
    def call(self, features, attributes):
        """
        计算语义平滑损失
        
        确保语义空间中相邻点的特征变化平滑。
        
        Args:
            features: 特征向量 [batch_size, feature_dim]
            attributes: 属性向量 [batch_size, attribute_dim]
            
        Returns:
            smooth_loss: 平滑损失
        """
        # 计算特征的梯度变化
        feature_grad = tf.gradients(features, attributes)[0]
        
        if feature_grad is not None:
            # 计算梯度的二阶导数作为平滑度量
            smooth_loss = tf.reduce_mean(tf.square(feature_grad))
        else:
            smooth_loss = 0.0
        
        return smooth_loss


class SemanticDistanceLoss(Loss):
    """
    语义距离损失
    
    集成多种语义约束，确保生成特征在语义空间中的合理分布。
    """
    
    def __init__(self,
                 preservation_weight=1.0,
                 clustering_weight=0.5,
                 separation_weight=0.3,
                 smooth_weight=0.1,
                 margin=1.0,
                 temperature=0.1,
                 name='semantic_distance_loss',
                 **kwargs):
        """
        初始化语义距离损失
        
        Args:
            preservation_weight: 距离保持损失权重
            clustering_weight: 聚类损失权重
            separation_weight: 分离损失权重
            smooth_weight: 平滑损失权重
            margin: 分离损失的边界
            temperature: 聚类损失的温度参数
        """
        super().__init__(name=name, **kwargs)
        
        self.preservation_weight = preservation_weight
        self.clustering_weight = clustering_weight
        self.separation_weight = separation_weight
        self.smooth_weight = smooth_weight
        
        # 初始化各组件损失
        self.preservation_loss = SemanticDistancePreservationLoss(margin=margin)
        self.clustering_loss = SemanticClusteringLoss(temperature=temperature)
        self.separation_loss = SemanticSeparationLoss(margin=margin)
        self.smooth_loss = SemanticSmoothLoss()
        
    def call(self, y_true, y_pred):
        """
        计算语义距离损失
        
        Args:
            y_true: 真实数据字典 {
                'original_semantic_distances': 原始语义距离,
                'real_features': 真实特征,
                'real_attributes': 真实属性
            }
            y_pred: 预测数据字典 {
                'generated_semantic_distances': 生成的语义距离,
                'generated_features': 生成特征,
                'semantic_distance_calculator': 语义距离计算器
            }
            
        Returns:
            total_semantic_loss: 总语义距离损失
        """
        # 1. 距离保持损失
        preservation_loss = self.preservation_loss(
            y_true['original_semantic_distances'],
            y_pred['generated_semantic_distances']
        )
        
        # 2. 聚类损失
        clustering_loss = self.clustering_loss(
            y_pred['generated_features'],
            y_true['real_attributes'],
            y_pred['semantic_distance_calculator']
        )
        
        # 3. 分离损失
        separation_loss = self.separation_loss(
            y_pred['generated_features'],
            y_true['real_attributes']
        )
        
        # 4. 平滑损失
        smooth_loss = self.smooth_loss(
            y_pred['generated_features'],
            y_true['real_attributes']
        )
        
        # 5. 计算总损失
        total_semantic_loss = (
            self.preservation_weight * preservation_loss +
            self.clustering_weight * clustering_loss +
            self.separation_weight * separation_loss +
            self.smooth_weight * smooth_loss
        )
        
        return total_semantic_loss
    
    def get_loss_components(self, y_true, y_pred):
        """
        获取各组件损失 (用于监控和调试)
        
        Returns:
            loss_components: 各组件损失字典
        """
        preservation_loss = self.preservation_loss(
            y_true['original_semantic_distances'],
            y_pred['generated_semantic_distances']
        )
        
        clustering_loss = self.clustering_loss(
            y_pred['generated_features'],
            y_true['real_attributes'],
            y_pred['semantic_distance_calculator']
        )
        
        separation_loss = self.separation_loss(
            y_pred['generated_features'],
            y_true['real_attributes']
        )
        
        smooth_loss = self.smooth_loss(
            y_pred['generated_features'],
            y_true['real_attributes']
        )
        
        return {
            'preservation_loss': preservation_loss,
            'clustering_loss': clustering_loss,
            'separation_loss': separation_loss,
            'smooth_loss': smooth_loss,
            'preservation_weight': self.preservation_weight,
            'clustering_weight': self.clustering_weight,
            'separation_weight': self.separation_weight,
            'smooth_weight': self.smooth_weight
        }
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'preservation_weight': self.preservation_weight,
            'clustering_weight': self.clustering_weight,
            'separation_weight': self.separation_weight,
            'smooth_weight': self.smooth_weight
        })
        return config

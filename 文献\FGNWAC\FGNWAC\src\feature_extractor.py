#!/usr/bin/env python3
"""
Feature Extractor Network - 按照论文要求将52维TEP数据降到24维
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader, TensorDataset
import os

class FeatureExtractor(nn.Module):
    """
    特征提取网络：52维 → 24维
    按照论文描述训练一个专门的特征提取器
    """
    
    def __init__(self, input_dim=52, output_dim=24):
        super(FeatureExtractor, self).__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # 设计特征提取网络架构
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, output_dim),
            nn.ReLU()
        )
        
    def forward(self, x):
        return self.feature_extractor(x)

class FeatureExtractorTrainer:
    """特征提取器训练器"""
    
    def __init__(self, input_dim=52, output_dim=24, lr=0.001):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = FeatureExtractor(input_dim, output_dim).to(self.device)
        self.optimizer = optim.Adam(self.model.parameters(), lr=lr)
        self.criterion = nn.MSELoss()
        
    def train_feature_extractor(self, raw_data, labels, epochs=100, batch_size=64):
        """
        训练高质量特征提取器
        重点提高判别性能力
        """
        print("🔧 开始训练高判别性特征提取网络 (52维 → 24维)")
        print(f"   原始数据形状: {raw_data.shape}")
        print(f"   训练轮数: {epochs}")
        print("   重点: 提高特征判别性")

        # 准备数据
        X_train, X_val, y_train, y_val = train_test_split(
            raw_data, labels, test_size=0.2, random_state=42, stratify=labels
        )

        train_dataset = TensorDataset(
            torch.FloatTensor(X_train),
            torch.LongTensor(y_train)
        )
        val_dataset = TensorDataset(
            torch.FloatTensor(X_val),
            torch.LongTensor(y_val)
        )

        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

        # 添加分类头用于监督学习
        num_classes = len(np.unique(labels))
        classifier = nn.Linear(24, num_classes).to(self.device)
        classifier_optimizer = optim.Adam(classifier.parameters(), lr=0.001)
        classification_criterion = nn.CrossEntropyLoss()

        best_val_acc = 0.0

        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            classifier.train()
            train_loss = 0.0
            train_acc = 0.0

            for batch_x, batch_y in train_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)

                # 前向传播
                features = self.model(batch_x)
                logits = classifier(features)

                # 计算损失 - 重点关注分类性能
                # 1. 分类损失（主要目标）
                class_loss = classification_criterion(logits, batch_y - 1)  # 标签从1开始，转为0开始

                # 2. 特征正则化（保持特征有意义）
                feature_reg = torch.mean(torch.norm(features, dim=1))

                # 总损失 - 主要优化分类性能
                total_loss = class_loss + 0.01 * feature_reg

                # 反向传播
                self.optimizer.zero_grad()
                classifier_optimizer.zero_grad()
                total_loss.backward()
                self.optimizer.step()
                classifier_optimizer.step()

                train_loss += total_loss.item()

                # 计算准确率
                _, predicted = torch.max(logits.data, 1)
                train_acc += (predicted == (batch_y - 1)).sum().item()

            # 验证阶段
            self.model.eval()
            classifier.eval()
            val_loss = 0.0
            val_acc = 0.0

            with torch.no_grad():
                for batch_x, batch_y in val_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)

                    features = self.model(batch_x)
                    logits = classifier(features)

                    class_loss = classification_criterion(logits, batch_y - 1)
                    feature_reg = torch.mean(torch.norm(features, dim=1))
                    total_loss = class_loss + 0.01 * feature_reg

                    val_loss += total_loss.item()

                    _, predicted = torch.max(logits.data, 1)
                    val_acc += (predicted == (batch_y - 1)).sum().item()

            # 计算平均损失和准确率
            train_loss /= len(train_loader)
            train_acc = train_acc / len(X_train) * 100
            val_loss /= len(val_loader)
            val_acc = val_acc / len(X_val) * 100

            print(f"Epoch {epoch+1:3d}: Train Loss={train_loss:.4f}, Train Acc={train_acc:.2f}%, "
                  f"Val Loss={val_loss:.4f}, Val Acc={val_acc:.2f}%")

            # 保存最佳模型 - 基于准确率而非损失
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                self.save_model('best_feature_extractor.pth')
                print(f"    ✅ 新的最佳验证准确率: {best_val_acc:.2f}%")

            # 早停条件
            if val_acc > 90:
                print(f"    🎉 达到目标准确率90%，提前停止")
                break

        print(f"✅ 特征提取器训练完成！最佳验证准确率: {best_val_acc:.2f}%")

        if best_val_acc < 70:
            print("⚠️  警告: 特征提取器准确率较低，可能影响后续性能")

        return self.model
    
    def extract_features(self, raw_data):
        """提取24维特征"""
        self.model.eval()
        with torch.no_grad():
            raw_tensor = torch.FloatTensor(raw_data).to(self.device)
            features = self.model(raw_tensor)
            return features.cpu().numpy()
    
    def save_model(self, path):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'input_dim': self.model.input_dim,
            'output_dim': self.model.output_dim
        }, path)
        
    def load_model(self, path):
        """加载模型"""
        if os.path.exists(path):
            checkpoint = torch.load(path, map_location=self.device, weights_only=False)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 已加载特征提取器: {path}")
            return True
        return False

def train_or_load_feature_extractor(raw_data, labels, force_retrain=False):
    """训练或加载特征提取器"""
    
    extractor_path = 'best_feature_extractor.pth'
    trainer = FeatureExtractorTrainer()
    
    if not force_retrain and trainer.load_model(extractor_path):
        print("📁 使用已训练的特征提取器")
    else:
        print("🔧 训练新的特征提取器")
        trainer.train_feature_extractor(raw_data, labels, epochs=50)
    
    return trainer

if __name__ == "__main__":
    # 测试特征提取器
    print("🧪 测试特征提取器")
    
    # 模拟52维TEP数据
    raw_data = np.random.randn(1000, 52)
    labels = np.random.randint(1, 16, 1000)
    
    # 训练特征提取器
    trainer = train_or_load_feature_extractor(raw_data, labels, force_retrain=True)
    
    # 提取特征
    features_24d = trainer.extract_features(raw_data)
    print(f"✅ 特征提取完成: {raw_data.shape} → {features_24d.shape}")

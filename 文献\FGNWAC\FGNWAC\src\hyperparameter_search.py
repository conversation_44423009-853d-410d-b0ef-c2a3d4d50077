"""
VAEGAN-AR超参数搜索系统
实现系统性的两阶段超参数搜索：lambda_ar -> lr
"""

import torch
import pandas as pd
import json
import os
import time
from datetime import datetime
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

from train import ZeroShotTrainer
from search_config import HyperparameterSearchConfig, SearchResultsManager


class NumpyEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理numpy类型"""
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)


def convert_numpy_types(obj):
    """递归转换numpy类型为Python原生类型"""
    if isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    else:
        return obj


class HyperparameterSearcher:
    """超参数搜索器"""
    
    def __init__(self, search_config: HyperparameterSearchConfig):
        self.config = search_config
        self.results_manager = SearchResultsManager(search_config)
        self.search_results = []
        self.best_params = {}
        
        # 创建搜索日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置搜索日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = os.path.join(
            self.config.search_log_dir, 
            f"hyperparameter_search_{timestamp}.log"
        )
        
    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)
        
        if self.config.verbose_logging:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')
    
    def run_single_experiment(self, stage, param_values, split_name, best_params=None):
        """
        运行单个超参数实验
        
        Args:
            stage: 搜索阶段 (1 or 2)
            param_values: 当前参数值字典
            split_name: 数据分组名称
            best_params: 之前阶段的最佳参数
            
        Returns:
            实验结果字典
        """
        # 生成实验配置
        exp_config = self.config.get_experiment_config(stage, param_values, best_params)
        exp_name = self.config.get_experiment_name(stage, param_values, split_name)
        
        self.log_message(f"🚀 开始实验: {exp_name}")
        self.log_message(f"   参数: {param_values}")
        
        # 设置实验特定的保存路径
        exp_config.save_dir = os.path.join(self.config.search_results_dir, exp_name)
        exp_config.log_dir = os.path.join(self.config.search_log_dir, exp_name)
        os.makedirs(exp_config.save_dir, exist_ok=True)
        os.makedirs(exp_config.log_dir, exist_ok=True)
        
        try:
            # 清理GPU缓存
            if self.config.clear_gpu_cache and torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            # 创建训练器
            trainer = ZeroShotTrainer(exp_config)
            
            # 准备数据
            data = trainer.prepare_data(split_name)
            
            # 训练模型（带早停）
            start_time = time.time()
            results = self.train_with_early_stopping(trainer, data, split_name, exp_name)
            end_time = time.time()
            
            # 记录实验结果
            experiment_result = {
                'experiment_name': exp_name,
                'stage': stage,
                'split_name': split_name,
                'parameters': param_values.copy(),
                'best_accuracy': float(results['best_accuracy']),
                'best_epoch': int(results['best_epoch']),
                'final_accuracy': float(results['final_accuracy']),
                'avg_fid': float(results.get('avg_fid', 0)),
                'avg_mmd': float(results.get('avg_mmd', 0)),
                'training_time_minutes': float((end_time - start_time) / 60),
                'total_epochs': int(results['total_epochs']),
                'early_stopped': bool(results['early_stopped']),
                'timestamp': datetime.now().isoformat()
            }

            # 确保所有numpy类型都被转换
            experiment_result = convert_numpy_types(experiment_result)
            
            # 如果有最佳参数，也记录
            if best_params:
                experiment_result['fixed_params'] = best_params.copy()
            
            self.search_results.append(experiment_result)
            
            self.log_message(f"✅ 实验完成: {exp_name}")
            self.log_message(f"   最佳准确率: {results['best_accuracy']:.4f} (第{results['best_epoch']}轮)")
            self.log_message(f"   训练时间: {experiment_result['training_time_minutes']:.1f}分钟")
            
            return experiment_result
            
        except Exception as e:
            self.log_message(f"❌ 实验失败: {exp_name}")
            self.log_message(f"   错误: {str(e)}")
            
            # 记录失败的实验
            failed_result = {
                'experiment_name': exp_name,
                'stage': stage,
                'split_name': split_name,
                'parameters': param_values.copy(),
                'status': 'failed',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.search_results.append(failed_result)
            
            return failed_result
    
    def train_with_early_stopping(self, trainer, data, split_name, exp_name):
        """带早停的训练"""
        best_accuracy = 0.0
        best_epoch = 0
        patience_counter = 0
        early_stopped = False
        
        self.log_message(f"   开始训练，最大轮数: {self.config.search_epochs}")
        
        for epoch in range(self.config.search_epochs):
            # 训练一轮
            epoch_losses = trainer.train_epoch(
                data['seen_loader'],
                data['training_attributes'],
                epoch
            )
            
            # 评估性能
            if epoch % self.config.eval_frequency == 0:
                eval_results = trainer.evaluate(data['data_dict'])
                current_accuracy = eval_results['accuracy']

                # 检查是否有改善
                if current_accuracy > best_accuracy:
                    best_accuracy = current_accuracy
                    best_epoch = epoch
                    patience_counter = 0

                    # 保存最佳模型
                    if not self.config.save_all_models:
                        best_model_path = os.path.join(
                            trainer.config.save_dir,
                            f"best_model_{exp_name}.pth"
                        )
                        trainer.save_model(best_model_path)

                    # 显示准确率（有改善时显示✅）
                    if epoch % self.config.display_frequency == 0:
                        self.log_message(f"   第{epoch}轮: 当前准确率={current_accuracy:.4f}, "
                                       f"最佳准确率={best_accuracy:.4f} ✅, 耐心值={patience_counter}")
                else:
                    patience_counter += 1
                    # 显示准确率（无改善时显示当前状态）
                    if epoch % self.config.display_frequency == 0:
                        self.log_message(f"   第{epoch}轮: 当前准确率={current_accuracy:.4f}, "
                                       f"最佳准确率={best_accuracy:.4f}, 耐心值={patience_counter}")

                # 检查早停条件
                if (epoch >= self.config.min_epochs and
                    patience_counter >= self.config.early_stop_patience):
                    self.log_message(f"   早停触发，第{epoch}轮，连续{patience_counter}轮无改善")
                    early_stopped = True
                    break
        
        # 最终评估
        final_results = trainer.evaluate(data['data_dict'])
        
        return {
            'best_accuracy': best_accuracy,
            'best_epoch': best_epoch,
            'final_accuracy': final_results['accuracy'],
            'avg_fid': final_results.get('avg_fid', 0),
            'avg_mmd': final_results.get('avg_mmd', 0),
            'total_epochs': epoch + 1,
            'early_stopped': early_stopped
        }
    
    def search_lambda_ar(self, split_name):
        """阶段1: 搜索最佳lambda_ar"""
        self.log_message("🔍 开始阶段1: lambda_ar搜索")
        self.log_message(f"   搜索范围: {self.config.lambda_ar_search_range}")
        self.log_message(f"   固定参数: lr={self.config.search_stages[1]['fixed_params']['lr']}")
        
        stage1_results = []
        
        for lambda_ar in tqdm(self.config.lambda_ar_search_range, desc="Lambda_AR搜索"):
            param_values = {'lambda_ar': lambda_ar}
            result = self.run_single_experiment(1, param_values, split_name)
            
            if 'best_accuracy' in result:
                stage1_results.append(result)
        
        # 找到最佳lambda_ar
        if stage1_results:
            best_result = max(stage1_results, key=lambda x: x['best_accuracy'])
            best_lambda_ar = best_result['parameters']['lambda_ar']
            
            self.best_params['lambda_ar'] = best_lambda_ar
            self.log_message(f"✅ 阶段1完成，最佳lambda_ar: {best_lambda_ar}")
            self.log_message(f"   最佳准确率: {best_result['best_accuracy']:.4f}")
            
            # 保存阶段1结果
            self.save_stage_results(1, split_name, stage1_results)
            
            return best_lambda_ar
        else:
            raise RuntimeError("阶段1搜索失败，没有成功的实验")
    
    def search_lr(self, split_name, best_lambda_ar):
        """阶段2: 搜索最佳学习率"""
        self.log_message("🔍 开始阶段2: 学习率搜索")
        self.log_message(f"   搜索范围: {self.config.lr_search_range}")
        self.log_message(f"   固定参数: lambda_ar={best_lambda_ar}")
        
        stage2_results = []
        best_params_stage1 = {'lambda_ar': best_lambda_ar}
        
        for lr in tqdm(self.config.lr_search_range, desc="学习率搜索"):
            param_values = {'lr': lr}
            result = self.run_single_experiment(2, param_values, split_name, best_params_stage1)
            
            if 'best_accuracy' in result:
                stage2_results.append(result)
        
        # 找到最佳lr
        if stage2_results:
            best_result = max(stage2_results, key=lambda x: x['best_accuracy'])
            best_lr = best_result['parameters']['lr']
            
            self.best_params['lr'] = best_lr
            self.log_message(f"✅ 阶段2完成，最佳lr: {best_lr}")
            self.log_message(f"   最佳准确率: {best_result['best_accuracy']:.4f}")
            
            # 保存阶段2结果
            self.save_stage_results(2, split_name, stage2_results)
            
            return best_lr
        else:
            raise RuntimeError("阶段2搜索失败，没有成功的实验")
    
    def save_stage_results(self, stage, split_name, results):
        """保存阶段结果"""
        # 保存CSV格式
        df = pd.DataFrame(results)
        csv_path = self.results_manager.get_results_file_path(stage, split_name, 'csv')
        df.to_csv(csv_path, index=False, encoding='utf-8')
        
        # 保存JSON格式
        json_path = self.results_manager.get_results_file_path(stage, split_name, 'json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)
        
        self.log_message(f"   阶段{stage}结果已保存: {csv_path}")
    
    def run_full_search(self, split_name):
        """运行完整的两阶段搜索"""
        self.log_message("🎯 开始完整的超参数搜索")
        self.log_message(f"   目标分组: {split_name}")
        
        search_summary = self.config.get_search_summary()
        self.log_message(f"   总实验数: {search_summary['total_experiments']}")
        self.log_message(f"   预估时间: {search_summary['estimated_total_time_hours']:.1f}小时")
        
        start_time = time.time()
        
        try:
            # 阶段1: 搜索lambda_ar
            best_lambda_ar = self.search_lambda_ar(split_name)
            
            # 阶段2: 搜索lr
            best_lr = self.search_lr(split_name, best_lambda_ar)
            
            # 保存最终最佳配置
            final_best_config = {
                'split_name': split_name,
                'best_lambda_ar': best_lambda_ar,
                'best_lr': best_lr,
                'search_completed': True,
                'total_search_time_hours': (time.time() - start_time) / 3600,
                'timestamp': datetime.now().isoformat()
            }
            
            best_config_path = self.results_manager.get_best_config_path(split_name)
            with open(best_config_path, 'w', encoding='utf-8') as f:
                json.dump(final_best_config, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)
            
            self.log_message("🎉 超参数搜索完成!")
            self.log_message(f"   最佳配置: lambda_ar={best_lambda_ar}, lr={best_lr}")
            self.log_message(f"   总耗时: {final_best_config['total_search_time_hours']:.2f}小时")
            self.log_message(f"   最佳配置已保存: {best_config_path}")
            
            return final_best_config
            
        except Exception as e:
            self.log_message(f"❌ 搜索过程出错: {str(e)}")
            raise

    def generate_analysis_report(self, split_name):
        """生成分析报告和可视化"""
        self.log_message("📊 生成分析报告")

        plots_dir = self.results_manager.get_analysis_plots_dir(split_name)

        # 读取两个阶段的结果
        stage1_results = self.load_stage_results(1, split_name)
        stage2_results = self.load_stage_results(2, split_name)

        if stage1_results and stage2_results:
            # 生成参数敏感性分析图
            self.plot_parameter_sensitivity(stage1_results, stage2_results, plots_dir)

            # 生成性能对比图
            self.plot_performance_comparison(stage1_results, stage2_results, plots_dir)

            # 生成搜索摘要报告
            self.generate_summary_report(stage1_results, stage2_results, split_name)

    def load_stage_results(self, stage, split_name):
        """加载阶段结果"""
        try:
            json_path = self.results_manager.get_results_file_path(stage, split_name, 'json')
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return None

    def plot_parameter_sensitivity(self, stage1_results, stage2_results, plots_dir):
        """绘制参数敏感性分析图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # Lambda_AR敏感性
        lambda_ar_values = [r['parameters']['lambda_ar'] for r in stage1_results if 'best_accuracy' in r]
        lambda_ar_accuracies = [r['best_accuracy'] for r in stage1_results if 'best_accuracy' in r]

        ax1.plot(lambda_ar_values, lambda_ar_accuracies, 'bo-', linewidth=2, markersize=8)
        ax1.set_xlabel('Lambda_AR')
        ax1.set_ylabel('Best Accuracy')
        ax1.set_title('Lambda_AR Parameter Sensitivity')
        ax1.grid(True, alpha=0.3)

        # 标记最佳点
        if lambda_ar_accuracies:
            best_idx = np.argmax(lambda_ar_accuracies)
            ax1.plot(lambda_ar_values[best_idx], lambda_ar_accuracies[best_idx],
                    'ro', markersize=12, label=f'Best: {lambda_ar_values[best_idx]}')
            ax1.legend()

        # LR敏感性
        lr_values = [r['parameters']['lr'] for r in stage2_results if 'best_accuracy' in r]
        lr_accuracies = [r['best_accuracy'] for r in stage2_results if 'best_accuracy' in r]

        ax2.semilogx(lr_values, lr_accuracies, 'go-', linewidth=2, markersize=8)
        ax2.set_xlabel('Learning Rate')
        ax2.set_ylabel('Best Accuracy')
        ax2.set_title('Learning Rate Parameter Sensitivity')
        ax2.grid(True, alpha=0.3)

        # 标记最佳点
        if lr_accuracies:
            best_idx = np.argmax(lr_accuracies)
            ax2.plot(lr_values[best_idx], lr_accuracies[best_idx],
                    'ro', markersize=12, label=f'Best: {lr_values[best_idx]}')
            ax2.legend()

        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, 'parameter_sensitivity.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def plot_performance_comparison(self, stage1_results, stage2_results, plots_dir):
        """绘制性能对比图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        # 准确率对比
        stage1_acc = [r['best_accuracy'] for r in stage1_results if 'best_accuracy' in r]
        stage2_acc = [r['best_accuracy'] for r in stage2_results if 'best_accuracy' in r]

        ax1.bar(range(len(stage1_acc)), stage1_acc, alpha=0.7, label='Stage 1 (Lambda_AR)')
        ax1.set_xlabel('Experiment Index')
        ax1.set_ylabel('Best Accuracy')
        ax1.set_title('Stage 1: Lambda_AR Search Results')
        ax1.legend()

        ax2.bar(range(len(stage2_acc)), stage2_acc, alpha=0.7, color='orange', label='Stage 2 (LR)')
        ax2.set_xlabel('Experiment Index')
        ax2.set_ylabel('Best Accuracy')
        ax2.set_title('Stage 2: Learning Rate Search Results')
        ax2.legend()

        # 训练时间对比
        stage1_time = [r.get('training_time_minutes', 0) for r in stage1_results if 'best_accuracy' in r]
        stage2_time = [r.get('training_time_minutes', 0) for r in stage2_results if 'best_accuracy' in r]

        ax3.bar(range(len(stage1_time)), stage1_time, alpha=0.7)
        ax3.set_xlabel('Experiment Index')
        ax3.set_ylabel('Training Time (minutes)')
        ax3.set_title('Stage 1: Training Time')

        ax4.bar(range(len(stage2_time)), stage2_time, alpha=0.7, color='orange')
        ax4.set_xlabel('Experiment Index')
        ax4.set_ylabel('Training Time (minutes)')
        ax4.set_title('Stage 2: Training Time')

        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, 'performance_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def generate_summary_report(self, stage1_results, stage2_results, split_name):
        """生成搜索摘要报告"""
        report_path = os.path.join(self.config.search_results_dir, f'search_summary_split_{split_name}.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("VAEGAN-AR 超参数搜索摘要报告\n")
            f.write("=" * 60 + "\n\n")

            f.write(f"数据分组: {split_name}\n")
            f.write(f"搜索时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 阶段1摘要
            f.write("阶段1: Lambda_AR搜索\n")
            f.write("-" * 30 + "\n")
            stage1_successful = [r for r in stage1_results if 'best_accuracy' in r]
            if stage1_successful:
                best_stage1 = max(stage1_successful, key=lambda x: x['best_accuracy'])
                f.write(f"最佳Lambda_AR: {best_stage1['parameters']['lambda_ar']}\n")
                f.write(f"最佳准确率: {best_stage1['best_accuracy']:.4f}\n")
                f.write(f"最佳轮数: {best_stage1['best_epoch']}\n")
                f.write(f"成功实验数: {len(stage1_successful)}/{len(stage1_results)}\n\n")

            # 阶段2摘要
            f.write("阶段2: 学习率搜索\n")
            f.write("-" * 30 + "\n")
            stage2_successful = [r for r in stage2_results if 'best_accuracy' in r]
            if stage2_successful:
                best_stage2 = max(stage2_successful, key=lambda x: x['best_accuracy'])
                f.write(f"最佳学习率: {best_stage2['parameters']['lr']}\n")
                f.write(f"最佳准确率: {best_stage2['best_accuracy']:.4f}\n")
                f.write(f"最佳轮数: {best_stage2['best_epoch']}\n")
                f.write(f"成功实验数: {len(stage2_successful)}/{len(stage2_results)}\n\n")

            # 最终推荐
            f.write("最终推荐配置\n")
            f.write("-" * 30 + "\n")
            if stage1_successful and stage2_successful:
                best_lambda_ar = max(stage1_successful, key=lambda x: x['best_accuracy'])['parameters']['lambda_ar']
                best_lr = max(stage2_successful, key=lambda x: x['best_accuracy'])['parameters']['lr']
                f.write(f"推荐Lambda_AR: {best_lambda_ar}\n")
                f.write(f"推荐学习率: {best_lr}\n")
                f.write(f"预期准确率: {max(stage2_successful, key=lambda x: x['best_accuracy'])['best_accuracy']:.4f}\n")

        self.log_message(f"   搜索摘要报告已保存: {report_path}")


# 使用示例
if __name__ == "__main__":
    # 创建搜索配置
    search_config = HyperparameterSearchConfig()

    # 创建搜索器
    searcher = HyperparameterSearcher(search_config)

    # 运行搜索（示例）
    print("🔍 超参数搜索系统已准备就绪")
    print("使用方法:")
    print("searcher.run_full_search('A')  # 对A组数据进行完整搜索")
    print("searcher.generate_analysis_report('A')  # 生成分析报告")

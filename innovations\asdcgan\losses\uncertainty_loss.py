"""
不确定性损失

基于变分框架的不确定性相关损失函数，支持不确定性量化和校准。

核心功能：
1. KL散度损失 - 变分正则化
2. 不确定性校准损失 - 确保不确定性估计准确
3. 置信度损失 - 优化置信度预测
4. 不确定性一致性损失 - 确保不确定性传播一致

技术特点：
- 基于VAE的理论框架
- 支持多种不确定性类型
- 自适应权重调整
- 与置信度评估协同
"""

import tensorflow as tf
from tensorflow.keras.losses import Loss
import numpy as np


class KLDivergenceLoss(tf.keras.layers.Layer):
    """KL散度损失"""
    
    def __init__(self, beta=1.0, **kwargs):
        super(KLDivergenceLoss, self).__init__(**kwargs)
        self.beta = beta  # β-VAE参数
        
    def call(self, mean, logvar):
        """
        计算KL散度损失
        
        Args:
            mean: 潜在变量均值 [batch_size, latent_dim]
            logvar: 潜在变量对数方差 [batch_size, latent_dim]
            
        Returns:
            kl_loss: KL散度损失
        """
        # KL(q(z|x) || p(z)) where p(z) = N(0, I)
        kl_loss = -0.5 * tf.reduce_sum(
            1 + logvar - tf.square(mean) - tf.exp(logvar),
            axis=-1
        )
        
        return tf.reduce_mean(kl_loss) * self.beta


class UncertaintyCalibrationLoss(tf.keras.layers.Layer):
    """不确定性校准损失"""
    
    def __init__(self, num_bins=10, **kwargs):
        super(UncertaintyCalibrationLoss, self).__init__(**kwargs)
        self.num_bins = num_bins
        
    def call(self, predictions, targets, uncertainties):
        """
        计算不确定性校准损失
        
        基于Expected Calibration Error (ECE)的概念。
        
        Args:
            predictions: 预测结果 [batch_size, num_classes]
            targets: 真实标签 [batch_size, num_classes]
            uncertainties: 不确定性估计 [batch_size, 1]
            
        Returns:
            calibration_loss: 校准损失
        """
        # 计算预测准确性
        predicted_classes = tf.argmax(predictions, axis=-1)
        true_classes = tf.argmax(targets, axis=-1)
        accuracy = tf.cast(tf.equal(predicted_classes, true_classes), tf.float32)
        
        # 将不确定性转换为置信度
        confidence = 1.0 - uncertainties
        confidence = tf.squeeze(confidence, axis=-1)
        
        # 计算ECE
        bin_boundaries = tf.linspace(0.0, 1.0, self.num_bins + 1)
        calibration_loss = 0.0
        
        for i in range(self.num_bins):
            # 定义bin范围
            bin_lower = bin_boundaries[i]
            bin_upper = bin_boundaries[i + 1]
            
            # 找到在当前bin中的样本
            in_bin = tf.logical_and(
                confidence > bin_lower,
                confidence <= bin_upper
            )
            
            # 计算bin中的样本数量
            bin_count = tf.reduce_sum(tf.cast(in_bin, tf.float32))
            
            if bin_count > 0:
                # 计算bin中的平均置信度和准确率
                bin_confidence = tf.reduce_mean(tf.boolean_mask(confidence, in_bin))
                bin_accuracy = tf.reduce_mean(tf.boolean_mask(accuracy, in_bin))
                
                # 计算校准误差
                bin_calibration_error = tf.abs(bin_confidence - bin_accuracy)
                
                # 加权校准误差
                calibration_loss += (bin_count / tf.cast(tf.shape(confidence)[0], tf.float32)) * bin_calibration_error
        
        return calibration_loss


class ConfidenceLoss(tf.keras.layers.Layer):
    """置信度损失"""
    
    def __init__(self, **kwargs):
        super(ConfidenceLoss, self).__init__(**kwargs)
        
    def call(self, predictions, targets, confidence_scores):
        """
        计算置信度损失
        
        Args:
            predictions: 预测结果 [batch_size, num_classes]
            targets: 真实标签 [batch_size, num_classes]
            confidence_scores: 置信度分数 [batch_size, 1]
            
        Returns:
            confidence_loss: 置信度损失
        """
        # 计算预测准确性
        predicted_classes = tf.argmax(predictions, axis=-1)
        true_classes = tf.argmax(targets, axis=-1)
        accuracy = tf.cast(tf.equal(predicted_classes, true_classes), tf.float32)
        accuracy = tf.expand_dims(accuracy, axis=-1)
        
        # 置信度应该与准确性相关
        # 正确预测时置信度应该高，错误预测时置信度应该低
        confidence_loss = tf.keras.losses.binary_crossentropy(
            accuracy, confidence_scores
        )
        
        return tf.reduce_mean(confidence_loss)


class UncertaintyConsistencyLoss(tf.keras.layers.Layer):
    """不确定性一致性损失"""
    
    def __init__(self, **kwargs):
        super(UncertaintyConsistencyLoss, self).__init__(**kwargs)
        
    def call(self, aleatoric_uncertainty, epistemic_uncertainty, total_uncertainty):
        """
        计算不确定性一致性损失
        
        确保总不确定性与各组件不确定性的一致性。
        
        Args:
            aleatoric_uncertainty: 偶然不确定性 [batch_size, 1]
            epistemic_uncertainty: 认知不确定性 [batch_size, 1]
            total_uncertainty: 总不确定性 [batch_size, 1]
            
        Returns:
            consistency_loss: 一致性损失
        """
        # 总不确定性应该等于各组件不确定性之和
        expected_total = aleatoric_uncertainty + epistemic_uncertainty
        consistency_loss = tf.reduce_mean(tf.square(total_uncertainty - expected_total))
        
        return consistency_loss


class UncertaintyLoss(Loss):
    """
    不确定性损失
    
    集成多种不确定性相关损失，确保不确定性估计的准确性和一致性。
    """
    
    def __init__(self,
                 kl_weight=1.0,
                 calibration_weight=0.5,
                 confidence_weight=0.3,
                 consistency_weight=0.2,
                 beta=1.0,
                 num_bins=10,
                 name='uncertainty_loss',
                 **kwargs):
        """
        初始化不确定性损失
        
        Args:
            kl_weight: KL散度损失权重
            calibration_weight: 校准损失权重
            confidence_weight: 置信度损失权重
            consistency_weight: 一致性损失权重
            beta: β-VAE参数
            num_bins: 校准损失的bin数量
        """
        super().__init__(name=name, **kwargs)
        
        self.kl_weight = kl_weight
        self.calibration_weight = calibration_weight
        self.confidence_weight = confidence_weight
        self.consistency_weight = consistency_weight
        
        # 初始化各组件损失
        self.kl_loss = KLDivergenceLoss(beta=beta)
        self.calibration_loss = UncertaintyCalibrationLoss(num_bins=num_bins)
        self.confidence_loss = ConfidenceLoss()
        self.consistency_loss = UncertaintyConsistencyLoss()
        
    def call(self, y_true, y_pred):
        """
        计算不确定性损失
        
        Args:
            y_true: 真实数据字典 {
                'targets': 真实标签,
                'real_features': 真实特征
            }
            y_pred: 预测数据字典 {
                'predictions': 预测结果,
                'mean': 潜在变量均值,
                'logvar': 潜在变量对数方差,
                'aleatoric_uncertainty': 偶然不确定性,
                'epistemic_uncertainty': 认知不确定性,
                'total_uncertainty': 总不确定性,
                'confidence_scores': 置信度分数
            }
            
        Returns:
            total_uncertainty_loss: 总不确定性损失
        """
        total_loss = 0.0
        
        # 1. KL散度损失
        if 'mean' in y_pred and 'logvar' in y_pred:
            kl_loss_value = self.kl_loss(y_pred['mean'], y_pred['logvar'])
            total_loss += self.kl_weight * kl_loss_value
        
        # 2. 不确定性校准损失
        if all(key in y_pred for key in ['predictions', 'total_uncertainty']) and 'targets' in y_true:
            calibration_loss_value = self.calibration_loss(
                y_pred['predictions'],
                y_true['targets'],
                y_pred['total_uncertainty']
            )
            total_loss += self.calibration_weight * calibration_loss_value
        
        # 3. 置信度损失
        if all(key in y_pred for key in ['predictions', 'confidence_scores']) and 'targets' in y_true:
            confidence_loss_value = self.confidence_loss(
                y_pred['predictions'],
                y_true['targets'],
                y_pred['confidence_scores']
            )
            total_loss += self.confidence_weight * confidence_loss_value
        
        # 4. 不确定性一致性损失
        if all(key in y_pred for key in ['aleatoric_uncertainty', 'epistemic_uncertainty', 'total_uncertainty']):
            consistency_loss_value = self.consistency_loss(
                y_pred['aleatoric_uncertainty'],
                y_pred['epistemic_uncertainty'],
                y_pred['total_uncertainty']
            )
            total_loss += self.consistency_weight * consistency_loss_value
        
        return total_loss
    
    def get_loss_components(self, y_true, y_pred):
        """
        获取各组件损失 (用于监控和调试)
        
        Returns:
            loss_components: 各组件损失字典
        """
        components = {}
        
        # KL散度损失
        if 'mean' in y_pred and 'logvar' in y_pred:
            components['kl_loss'] = self.kl_loss(y_pred['mean'], y_pred['logvar'])
        
        # 校准损失
        if all(key in y_pred for key in ['predictions', 'total_uncertainty']) and 'targets' in y_true:
            components['calibration_loss'] = self.calibration_loss(
                y_pred['predictions'],
                y_true['targets'],
                y_pred['total_uncertainty']
            )
        
        # 置信度损失
        if all(key in y_pred for key in ['predictions', 'confidence_scores']) and 'targets' in y_true:
            components['confidence_loss'] = self.confidence_loss(
                y_pred['predictions'],
                y_true['targets'],
                y_pred['confidence_scores']
            )
        
        # 一致性损失
        if all(key in y_pred for key in ['aleatoric_uncertainty', 'epistemic_uncertainty', 'total_uncertainty']):
            components['consistency_loss'] = self.consistency_loss(
                y_pred['aleatoric_uncertainty'],
                y_pred['epistemic_uncertainty'],
                y_pred['total_uncertainty']
            )
        
        # 添加权重信息
        components.update({
            'kl_weight': self.kl_weight,
            'calibration_weight': self.calibration_weight,
            'confidence_weight': self.confidence_weight,
            'consistency_weight': self.consistency_weight
        })
        
        return components
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'kl_weight': self.kl_weight,
            'calibration_weight': self.calibration_weight,
            'confidence_weight': self.confidence_weight,
            'consistency_weight': self.consistency_weight
        })
        return config

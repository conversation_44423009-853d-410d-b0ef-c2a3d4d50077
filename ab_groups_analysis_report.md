
# ASDCGAN A组和B组实验结果分析报告

## 📊 总体性能对比

| 指标 | A组 | B组 | 差异 |
|------|-----|-----|------|
| 最终准确率 | 60.97% | 39.17% | 21.80% |
| 最高准确率 | 60.97% | 39.17% | 21.80% |
| 训练轮次 | 484 | 385 | 99 |
| 最终生成器损失 | 555.58 | 622.16 | -66.58 |
| 最终判别器损失 | 0.0158 | 0.0167 | -0.0009 |

## 🎯 训练稳定性分析

### A组 (测试类别: [1, 6, 14])
- **收敛评分**: 39.37 (越低越好)
- **生成器损失方差**: 63706.17
- **判别器损失方差**: 0.043628
- **近期平均准确率**: 60.97%

### B组 (测试类别: [4, 7, 10])
- **收敛评分**: 44.67 (越低越好)
- **生成器损失方差**: 58164.38
- **判别器损失方差**: 0.066812
- **近期平均准确率**: 39.17%

## ⚠️ 发现的问题

### A组问题:
- 生成器损失爆炸 (>1000)
- 判别器损失爆炸 (>1.0)

### B组问题:
- 生成器损失爆炸 (>1000)
- 判别器损失爆炸 (>1.0)
- 准确率过低 (<40%)

## 🔍 深度分析

### 1. 准确率分析
- **A组优势**: A组最终准确率更高，达到60.97%
- **B组优势**: B组训练更快收敛

### 2. 训练效率分析
- **收敛速度**: A组收敛更快
- **训练稳定性**: B组损失波动较大

### 3. 损失函数分析
- **生成器性能**: A组生成器损失更低，性能更好
- **判别器性能**: A组判别器训练更平衡

## 📈 改进建议

### 针对A组:
需要调整超参数和训练策略

### 针对B组:
增加训练轮次，调整学习率

## 📋 结论

A组在准确率方面表现更优（60.97% vs 39.17%），训练稳定性也更好。B组可能需要调整数据集划分或模型超参数来提升性能。

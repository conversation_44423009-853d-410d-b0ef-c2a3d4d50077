"""
智能域选择器模块 (PyTorch版本)

基于多头注意力机制的自动域选择，替换CycleGAN-SD中的手动域选择过程。

核心功能：
1. 自动选择最优源域进行域转换
2. 基于语义相似度的智能匹配
3. 支持多域并行选择和权重分配
4. 动态适应不同的目标域特征

技术特点：
- 多头注意力机制
- 可学习的域选择策略
- 支持软选择和硬选择
- 梯度友好的可微分实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class DomainSelector(nn.Module):
    """
    智能域选择器
    
    使用多头注意力机制自动选择最优的源域进行域转换，
    替换传统的手动域选择过程。
    """
    
    def __init__(self, 
                 num_domains=5,
                 attention_dim=64,
                 num_heads=4,
                 hidden_dim=128,
                 dropout_rate=0.1,
                 selection_mode='soft',
                 temperature=1.0):
        """
        初始化智能域选择器
        
        Args:
            num_domains: 域的数量
            attention_dim: 注意力机制的维度
            num_heads: 多头注意力的头数
            hidden_dim: 隐藏层维度
            dropout_rate: Dropout比率
            selection_mode: 选择模式 ('soft', 'hard', 'gumbel')
            temperature: Gumbel softmax温度参数
        """
        super(DomainSelector, self).__init__()
        
        self.num_domains = num_domains
        self.attention_dim = attention_dim
        self.num_heads = num_heads
        self.hidden_dim = hidden_dim
        self.dropout_rate = dropout_rate
        self.selection_mode = selection_mode
        self.temperature = temperature
        
        # 目标域编码器
        self.target_encoder = nn.Sequential(
            nn.Linear(attention_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, attention_dim),
            nn.Tanh()
        )
        
        # 源域编码器
        self.source_encoder = nn.Sequential(
            nn.Linear(attention_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, attention_dim),
            nn.Tanh()
        )
        
        # 多头注意力机制
        self.domain_attention = nn.MultiheadAttention(
            embed_dim=attention_dim,
            num_heads=num_heads,
            dropout=dropout_rate,
            batch_first=True
        )
        
        # 选择权重计算网络
        self.selection_weights = nn.Sequential(
            nn.Linear(attention_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 2, num_domains)
        )
        
        # 相似度计算网络
        self.similarity_network = nn.Sequential(
            nn.Linear(attention_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
    
    def forward(self, target_attr, source_attrs):
        """
        前向传播进行域选择
        
        Args:
            target_attr: 目标域属性 [batch_size, attr_dim]
            source_attrs: 源域属性列表 [num_domains, batch_size, attr_dim] 或 list
            
        Returns:
            selection_result: 域选择结果字典
        """
        batch_size = target_attr.shape[0]
        
        # 处理源域属性
        if isinstance(source_attrs, list):
            source_attrs = torch.stack(source_attrs, dim=0)  # [num_domains, batch_size, attr_dim]
        
        # 编码目标域和源域
        target_encoded = self.target_encoder(target_attr)  # [batch_size, attention_dim]
        
        # 编码所有源域
        source_encoded_list = []
        for i in range(self.num_domains):
            if i < source_attrs.shape[0]:
                source_encoded = self.source_encoder(source_attrs[i])  # [batch_size, attention_dim]
                source_encoded_list.append(source_encoded)
            else:
                # 如果源域数量不足，使用零填充
                source_encoded = torch.zeros_like(target_encoded)
                source_encoded_list.append(source_encoded)
        
        source_encoded = torch.stack(source_encoded_list, dim=1)  # [batch_size, num_domains, attention_dim]
        
        # 多头注意力计算
        target_query = target_encoded.unsqueeze(1)  # [batch_size, 1, attention_dim]
        
        attention_output, attention_weights = self.domain_attention(
            target_query, source_encoded, source_encoded
        )  # [batch_size, 1, attention_dim], [batch_size, 1, num_domains]
        
        attention_output = attention_output.squeeze(1)  # [batch_size, attention_dim]
        attention_weights = attention_weights.squeeze(1)  # [batch_size, num_domains]
        
        # 计算选择权重
        selection_logits = self.selection_weights(attention_output)  # [batch_size, num_domains]
        
        # 根据选择模式处理
        if self.selection_mode == 'soft':
            selection_probs = F.softmax(selection_logits, dim=-1)
            selected_domains = selection_probs
        elif self.selection_mode == 'hard':
            # 硬选择：选择概率最大的域
            selected_indices = torch.argmax(selection_logits, dim=-1)
            selection_probs = F.one_hot(selected_indices, num_classes=self.num_domains).float()
            selected_domains = selection_probs
        elif self.selection_mode == 'gumbel':
            # Gumbel-Softmax选择
            selection_probs = F.gumbel_softmax(selection_logits, tau=self.temperature, hard=False)
            selected_domains = selection_probs
        else:
            raise ValueError(f"未知的选择模式: {self.selection_mode}")
        
        # 计算相似度分数
        similarity_scores = []
        for i in range(self.num_domains):
            if i < len(source_encoded_list):
                combined = torch.cat([target_encoded, source_encoded_list[i]], dim=-1)
                similarity = self.similarity_network(combined).squeeze(-1)  # [batch_size]
                similarity_scores.append(similarity)
            else:
                similarity_scores.append(torch.zeros(batch_size, device=target_attr.device))
        
        similarity_scores = torch.stack(similarity_scores, dim=-1)  # [batch_size, num_domains]
        
        return {
            'selection_probs': selection_probs,
            'selected_domains': selected_domains,
            'attention_weights': attention_weights,
            'similarity_scores': similarity_scores,
            'selection_logits': selection_logits,
            'target_encoded': target_encoded,
            'source_encoded': source_encoded
        }
    
    def select_optimal_domains(self, target_attr, source_attrs, top_k=1):
        """
        选择top-k个最优域
        
        Args:
            target_attr: 目标域属性
            source_attrs: 源域属性列表
            top_k: 选择的域数量
            
        Returns:
            top_k_result: top-k选择结果
        """
        selection_result = self.forward(target_attr, source_attrs)
        selection_probs = selection_result['selection_probs']
        
        # 选择top-k个域
        top_k_values, top_k_indices = torch.topk(selection_probs, k=top_k, dim=-1)
        
        return {
            'indices': top_k_indices,
            'weights': top_k_values,
            'full_result': selection_result
        }
    
    def compute_domain_similarity_matrix(self, source_attrs):
        """
        计算域间相似度矩阵
        
        Args:
            source_attrs: 源域属性列表
            
        Returns:
            similarity_matrix: 域间相似度矩阵 [num_domains, num_domains]
        """
        if isinstance(source_attrs, list):
            source_attrs = torch.stack(source_attrs, dim=0)
        
        num_domains = source_attrs.shape[0]
        batch_size = source_attrs.shape[1]
        
        # 编码所有源域
        encoded_domains = []
        for i in range(num_domains):
            encoded = self.source_encoder(source_attrs[i])  # [batch_size, attention_dim]
            encoded_domains.append(encoded.mean(dim=0))  # [attention_dim] - 平均池化
        
        encoded_domains = torch.stack(encoded_domains, dim=0)  # [num_domains, attention_dim]
        
        # 计算相似度矩阵
        similarity_matrix = torch.zeros(num_domains, num_domains, device=source_attrs.device)
        
        for i in range(num_domains):
            for j in range(num_domains):
                if i != j:
                    combined = torch.cat([encoded_domains[i], encoded_domains[j]], dim=-1)
                    similarity = self.similarity_network(combined.unsqueeze(0)).squeeze()
                    similarity_matrix[i, j] = similarity
                else:
                    similarity_matrix[i, j] = 1.0  # 自相似度为1
        
        return similarity_matrix


class DomainSelectionMetrics:
    """域选择度量工具类"""
    
    @staticmethod
    def compute_selection_entropy(selection_probs):
        """计算选择熵"""
        entropy = -torch.sum(selection_probs * torch.log(selection_probs + 1e-8), dim=-1)
        return entropy
    
    @staticmethod
    def compute_selection_confidence(selection_probs):
        """计算选择置信度"""
        max_prob = torch.max(selection_probs, dim=-1)[0]
        return max_prob
    
    @staticmethod
    def compute_diversity_score(selection_probs):
        """计算选择多样性分数"""
        # 使用基尼系数衡量多样性
        sorted_probs, _ = torch.sort(selection_probs, dim=-1, descending=True)
        n = selection_probs.shape[-1]
        index = torch.arange(1, n + 1, device=selection_probs.device).float()
        gini = torch.sum((2 * index - n - 1) * sorted_probs, dim=-1) / (n * torch.sum(sorted_probs, dim=-1))
        diversity = 1 - gini  # 多样性 = 1 - 基尼系数
        return diversity

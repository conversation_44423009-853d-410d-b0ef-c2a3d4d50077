"""
对抗损失

基于Wasserstein GAN和梯度惩罚的稳定对抗训练损失函数。

核心功能：
1. Wasserstein距离损失 - 稳定的对抗训练
2. 梯度惩罚 - 确保Lipschitz约束
3. 多层次对抗损失 - 特征级、属性级、语义级对抗
4. 自适应对抗权重 - 平衡生成器和判别器

技术特点：
- 基于WGAN-GP框架
- 支持多层次判别
- 梯度惩罚稳定训练
- 自适应权重调整
"""

import tensorflow as tf
from tensorflow.keras.losses import Loss
import numpy as np


class WassersteinLoss(tf.keras.layers.Layer):
    """Wasserstein距离损失"""
    
    def __init__(self, **kwargs):
        super(WassersteinLoss, self).__init__(**kwargs)
        
    def discriminator_loss(self, real_output, fake_output):
        """
        判别器的Wasserstein损失
        
        Args:
            real_output: 真实样本的判别输出 [batch_size, 1]
            fake_output: 生成样本的判别输出 [batch_size, 1]
            
        Returns:
            d_loss: 判别器损失
        """
        # Wasserstein距离：E[D(x)] - E[D(G(z))]
        # 判别器要最大化这个距离，所以损失是负的
        d_loss = tf.reduce_mean(fake_output) - tf.reduce_mean(real_output)
        return d_loss
    
    def generator_loss(self, fake_output):
        """
        生成器的Wasserstein损失
        
        Args:
            fake_output: 生成样本的判别输出 [batch_size, 1]
            
        Returns:
            g_loss: 生成器损失
        """
        # 生成器要最小化 -E[D(G(z))]，即最大化 E[D(G(z))]
        g_loss = -tf.reduce_mean(fake_output)
        return g_loss


class GradientPenalty(tf.keras.layers.Layer):
    """梯度惩罚"""
    
    def __init__(self, lambda_gp=10.0, **kwargs):
        super(GradientPenalty, self).__init__(**kwargs)
        self.lambda_gp = lambda_gp
        
    def call(self, real_samples, fake_samples, discriminator):
        """
        计算梯度惩罚
        
        Args:
            real_samples: 真实样本 [batch_size, feature_dim]
            fake_samples: 生成样本 [batch_size, feature_dim]
            discriminator: 判别器模型
            
        Returns:
            gradient_penalty: 梯度惩罚损失
        """
        batch_size = tf.shape(real_samples)[0]
        
        # 随机插值
        epsilon = tf.random.uniform([batch_size, 1], 0.0, 1.0)
        interpolated = epsilon * real_samples + (1 - epsilon) * fake_samples
        
        # 计算插值点的梯度
        with tf.GradientTape() as tape:
            tape.watch(interpolated)
            interpolated_output = discriminator(interpolated, training=True)
        
        gradients = tape.gradient(interpolated_output, interpolated)
        
        # 计算梯度的L2范数
        gradients_norm = tf.sqrt(tf.reduce_sum(tf.square(gradients), axis=1))
        
        # 梯度惩罚：(||∇D(x̂)||₂ - 1)²
        gradient_penalty = tf.reduce_mean(tf.square(gradients_norm - 1.0))
        
        return self.lambda_gp * gradient_penalty


class MultiLevelAdversarialLoss(tf.keras.layers.Layer):
    """多层次对抗损失"""
    
    def __init__(self, 
                 feature_weight=1.0,
                 attribute_weight=0.5,
                 semantic_weight=0.3,
                 **kwargs):
        super(MultiLevelAdversarialLoss, self).__init__(**kwargs)
        
        self.feature_weight = feature_weight
        self.attribute_weight = attribute_weight
        self.semantic_weight = semantic_weight
        
        self.wasserstein_loss = WassersteinLoss()
        
    def call(self, discriminator_outputs, loss_type='discriminator'):
        """
        计算多层次对抗损失
        
        Args:
            discriminator_outputs: 判别器输出字典 {
                'feature_validity': 特征级判别结果,
                'attribute_result': 属性级判别结果,
                'semantic_validity': 语义级判别结果,
                'real_outputs': 真实样本输出 (仅判别器损失),
                'fake_outputs': 生成样本输出
            }
            loss_type: 损失类型 ('discriminator' 或 'generator')
            
        Returns:
            total_adversarial_loss: 总对抗损失
        """
        total_loss = 0.0
        
        if loss_type == 'discriminator':
            # 判别器损失
            if 'real_outputs' in discriminator_outputs and 'fake_outputs' in discriminator_outputs:
                # 特征级对抗损失
                feature_d_loss = self.wasserstein_loss.discriminator_loss(
                    discriminator_outputs['real_outputs']['feature_validity'],
                    discriminator_outputs['fake_outputs']['feature_validity']
                )
                total_loss += self.feature_weight * feature_d_loss
                
                # 属性级对抗损失
                if 'attribute_result' in discriminator_outputs['real_outputs']:
                    attr_d_loss = self.wasserstein_loss.discriminator_loss(
                        discriminator_outputs['real_outputs']['attribute_result']['consistency_score'],
                        discriminator_outputs['fake_outputs']['attribute_result']['consistency_score']
                    )
                    total_loss += self.attribute_weight * attr_d_loss
                
                # 语义级对抗损失
                if ('semantic_validity' in discriminator_outputs['real_outputs'] and
                    'semantic_validity' in discriminator_outputs['fake_outputs']):
                    semantic_d_loss = self.wasserstein_loss.discriminator_loss(
                        discriminator_outputs['real_outputs']['semantic_validity'],
                        discriminator_outputs['fake_outputs']['semantic_validity']
                    )
                    total_loss += self.semantic_weight * semantic_d_loss
        
        elif loss_type == 'generator':
            # 生成器损失
            if 'fake_outputs' in discriminator_outputs:
                # 特征级生成器损失
                feature_g_loss = self.wasserstein_loss.generator_loss(
                    discriminator_outputs['fake_outputs']['feature_validity']
                )
                total_loss += self.feature_weight * feature_g_loss
                
                # 属性级生成器损失
                if 'attribute_result' in discriminator_outputs['fake_outputs']:
                    attr_g_loss = self.wasserstein_loss.generator_loss(
                        discriminator_outputs['fake_outputs']['attribute_result']['consistency_score']
                    )
                    total_loss += self.attribute_weight * attr_g_loss
                
                # 语义级生成器损失
                if 'semantic_validity' in discriminator_outputs['fake_outputs']:
                    semantic_g_loss = self.wasserstein_loss.generator_loss(
                        discriminator_outputs['fake_outputs']['semantic_validity']
                    )
                    total_loss += self.semantic_weight * semantic_g_loss
        
        return total_loss


class AdversarialLoss(Loss):
    """
    对抗损失
    
    集成Wasserstein距离、梯度惩罚和多层次对抗训练的完整对抗损失框架。
    """
    
    def __init__(self,
                 lambda_gp=10.0,
                 feature_weight=1.0,
                 attribute_weight=0.5,
                 semantic_weight=0.3,
                 adaptive_weights=False,
                 name='adversarial_loss',
                 **kwargs):
        """
        初始化对抗损失
        
        Args:
            lambda_gp: 梯度惩罚权重
            feature_weight: 特征级对抗损失权重
            attribute_weight: 属性级对抗损失权重
            semantic_weight: 语义级对抗损失权重
            adaptive_weights: 是否使用自适应权重
        """
        super().__init__(name=name, **kwargs)
        
        self.lambda_gp = lambda_gp
        self.adaptive_weights = adaptive_weights
        
        # 初始化组件
        self.gradient_penalty = GradientPenalty(lambda_gp=lambda_gp)
        self.multi_level_loss = MultiLevelAdversarialLoss(
            feature_weight=feature_weight,
            attribute_weight=attribute_weight,
            semantic_weight=semantic_weight
        )
        
        # 自适应权重历史
        if adaptive_weights:
            self.loss_history = {
                'feature': [],
                'attribute': [],
                'semantic': []
            }
            self.adaptation_rate = 0.01
    
    def compute_discriminator_loss(self, real_samples, fake_samples, discriminator_outputs, discriminator_model):
        """
        计算判别器总损失
        
        Args:
            real_samples: 真实样本
            fake_samples: 生成样本
            discriminator_outputs: 判别器输出
            discriminator_model: 判别器模型
            
        Returns:
            d_loss: 判别器总损失
        """
        # 1. 多层次对抗损失
        adversarial_loss = self.multi_level_loss(discriminator_outputs, loss_type='discriminator')
        
        # 2. 梯度惩罚
        gp_loss = self.gradient_penalty(real_samples, fake_samples, discriminator_model)
        
        # 3. 总判别器损失
        d_loss = adversarial_loss + gp_loss
        
        return d_loss
    
    def compute_generator_loss(self, discriminator_outputs):
        """
        计算生成器总损失
        
        Args:
            discriminator_outputs: 判别器输出
            
        Returns:
            g_loss: 生成器总损失
        """
        # 多层次生成器对抗损失
        g_loss = self.multi_level_loss(discriminator_outputs, loss_type='generator')
        
        return g_loss
    
    def call(self, y_true, y_pred):
        """
        计算对抗损失 (主要用于兼容Keras Loss接口)
        
        Args:
            y_true: 真实数据
            y_pred: 预测数据
            
        Returns:
            loss: 对抗损失
        """
        # 这里主要是为了兼容Keras的Loss接口
        # 实际使用时建议直接调用compute_discriminator_loss或compute_generator_loss
        
        if 'loss_type' in y_pred:
            if y_pred['loss_type'] == 'discriminator':
                return self.compute_discriminator_loss(
                    y_true['real_samples'],
                    y_pred['fake_samples'],
                    y_pred['discriminator_outputs'],
                    y_pred['discriminator_model']
                )
            elif y_pred['loss_type'] == 'generator':
                return self.compute_generator_loss(y_pred['discriminator_outputs'])
        
        # 默认返回生成器损失
        return self.compute_generator_loss(y_pred.get('discriminator_outputs', {}))
    
    def update_adaptive_weights(self, loss_components):
        """
        更新自适应权重
        
        Args:
            loss_components: 各组件损失字典
        """
        if not self.adaptive_weights:
            return
        
        # 记录损失历史
        for key in ['feature', 'attribute', 'semantic']:
            if f'{key}_loss' in loss_components:
                self.loss_history[key].append(loss_components[f'{key}_loss'].numpy())
                
                # 保持历史长度
                if len(self.loss_history[key]) > 100:
                    self.loss_history[key] = self.loss_history[key][-100:]
        
        # 计算自适应权重
        if all(len(self.loss_history[key]) > 10 for key in ['feature', 'attribute', 'semantic']):
            # 计算平均损失
            avg_losses = {}
            for key in ['feature', 'attribute', 'semantic']:
                avg_losses[key] = np.mean(self.loss_history[key][-10:])
            
            # 计算相对权重 (损失越大，权重越小)
            total_loss = sum(avg_losses.values()) + 1e-8
            
            for key in ['feature', 'attribute', 'semantic']:
                new_weight = (total_loss - avg_losses[key]) / total_loss
                current_weight = getattr(self.multi_level_loss, f'{key}_weight')
                
                # 平滑更新
                updated_weight = (
                    (1 - self.adaptation_rate) * current_weight +
                    self.adaptation_rate * new_weight
                )
                
                setattr(self.multi_level_loss, f'{key}_weight', updated_weight)
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'lambda_gp': self.lambda_gp,
            'adaptive_weights': self.adaptive_weights
        })
        return config

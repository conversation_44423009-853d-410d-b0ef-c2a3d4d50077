# 🔥 ACGAN-FG 重构版本使用说明

## 📋 概述

本文件是基于原版 `ACGAN_FG_gb.py` 的重构版本，在保持**核心算法完全不变**的前提下，添加了现代化功能和更好的用户体验。

## ✨ 新增功能

### 1. 🎯 组别选择系统
- **支持A/B/C/D/E组配置**，可灵活选择不同的测试类别组合
- **动态数据加载**，根据组别自动组合训练和测试数据
- **组别验证**，防止无效组别输入

### 2. 📊 TensorBoard实时监控
- **实时可视化**训练过程中的损失变化
- **准确率曲线**跟踪模型性能
- **自动保存**TensorBoard日志文件

### 3. 🖥️ GPU检测与内存管理
- **自动GPU检测**，优先使用GPU加速训练
- **内存增长限制**，避免GPU内存溢出
- **兼容CPU训练**，无GPU环境下自动切换

### 4. 📝 结构化日志系统
- **详细训练日志**，记录每个epoch的损失和准确率
- **时间戳记录**，追踪训练进度和耗时
- **最优准确率跟踪**，自动记录历史最佳性能

## 🎯 组别配置详情

| 组别 | 测试类别 | 训练类别 | 说明 |
|------|----------|----------|------|
| **A组** | [1, 6, 14] | [2,3,4,5,7,8,9,10,11,12,13,15] | 均匀分布 |
| **B组** | [4, 7, 10] | [1,2,3,5,6,8,9,11,12,13,14,15] | 中等难度 |
| **C组** | [8, 11, 12] | [1,2,3,4,5,6,7,9,10,13,14,15] | 连续类别 |
| **D组** | [2, 3, 5] | [1,4,6,7,8,9,10,11,12,13,14,15] | 低编号类别 |
| **E组** | [9, 13, 15] | [1,2,3,4,5,6,7,8,10,11,12,14] | **原版默认** |

## 🚀 使用方法

### 基本使用

```python
from ACGAN_FG_refactored import Zero_shot

# 创建模型实例
gan = Zero_shot()

# 使用E组配置训练（与原版一致）
gan.train(epochs=2000, batch_size=120, group_name='E')
```

### 高级使用

```python
# 选择不同组别
gan.train(epochs=2000, batch_size=120, group_name='A')  # A组
gan.train(epochs=2000, batch_size=120, group_name='B')  # B组
gan.train(epochs=2000, batch_size=120, group_name='C')  # C组
gan.train(epochs=2000, batch_size=120, group_name='D')  # D组

# 自定义日志文件
gan.train(
    epochs=2000, 
    batch_size=120, 
    group_name='E',
    log_file='logs/my_experiment.log'
)
```

## 📊 TensorBoard使用

### 启动TensorBoard

训练完成后，使用以下命令启动TensorBoard：

```bash
tensorboard --logdir=./tensorboard_logs
```

然后在浏览器中访问：`http://localhost:6006`

### 可视化内容

- **损失曲线**：自编码器、生成器、判别器、分类器损失
- **准确率曲线**：LinearSVM、RandomForest、GaussianNB、MLP准确率
- **训练进度**：实时更新的训练状态

## 📁 文件结构

```
scripts/
├── ACGAN_FG_refactored.py          # 重构版本主文件
├── ACGAN_FG_refactored_README.md   # 本说明文档
├── ACGAN_FG_gb.py                  # 原版文件（参考）
├── ACGAN_FG_源码.py                # 原版源码
└── ...

logs/                               # 训练日志目录
├── A_ACGAN_FG_20250721_161234.log
├── B_ACGAN_FG_20250721_161234.log
└── ...

tensorboard_logs/                   # TensorBoard日志目录
├── A_ACGAN_FG_realtime/
├── B_ACGAN_FG_realtime/
└── ...
```

## ⚙️ 参数说明

### train() 方法参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `epochs` | int | 2000 | 训练轮数 |
| `batch_size` | int | 120 | 批次大小 |
| `group_name` | str | 'E' | 组别名称 (A/B/C/D/E) |
| `log_file` | str | None | 日志文件路径（自动生成） |

### 模型超参数（保持原版不变）

| 参数 | 值 | 说明 |
|------|-----|------|
| `data_lenth` | 52 | 输入数据维度 |
| `feature_dim` | 256 | 特征维度 |
| `num_classes` | 15 | 类别数量 |
| `latent_dim` | 50 | 潜在空间维度 |
| `lambda_cla` | 10 | 分类损失权重 |
| `lambda_cms` | 10 | 比较损失权重 |
| `lambda_crl` | 0.01 | 循环排序损失权重 |

## 🔧 核心保证

### 算法一致性
- ✅ **模型架构**：与原版完全一致
- ✅ **损失函数**：保持原版计算逻辑
- ✅ **训练步骤**：完全复制原版训练流程
- ✅ **超参数**：使用原版默认值

### 结果一致性
- ✅ **E组结果**：与原版 `ACGAN_FG_gb.py` 完全一致
- ✅ **测试类别**：E组使用 [9, 13, 15]，与原版相同
- ✅ **训练类别**：自动排除测试类别，逻辑一致

## 🐛 故障排除

### 常见问题

1. **GPU内存不足**
   ```
   解决方案：减小batch_size或使用CPU训练
   ```

2. **TensorBoard无法启动**
   ```
   解决方案：检查tensorboard_logs目录是否存在
   ```

3. **数据文件未找到**
   ```
   解决方案：确保dataset_train_case1.npz和dataset_test_case1.npz在当前目录
   ```

### 调试模式

如果遇到问题，可以在代码中添加调试信息：

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查GPU状态
import tensorflow as tf
print("GPU可用:", tf.config.list_physical_devices('GPU'))
```

## 📈 性能对比

| 功能 | 原版 | 重构版 |
|------|------|--------|
| 组别选择 | ❌ 硬编码E组 | ✅ 支持A/B/C/D/E组 |
| 实时监控 | ❌ 无 | ✅ TensorBoard |
| GPU管理 | ❌ 基础 | ✅ 自动检测+内存管理 |
| 日志系统 | ❌ 简单打印 | ✅ 结构化日志 |
| 代码可读性 | ❌ 一般 | ✅ 详细注释 |
| 核心算法 | ✅ 原版 | ✅ **完全保持** |

## 🎯 最佳实践

1. **首次使用**：建议先用E组验证与原版结果一致
2. **实验对比**：使用不同组别进行对比实验
3. **性能监控**：利用TensorBoard观察训练过程
4. **日志分析**：定期检查日志文件了解训练状态

## 📞 技术支持

如有问题，请检查：
1. 环境依赖是否完整安装
2. 数据文件是否存在
3. GPU驱动是否正常
4. 参考本文档的故障排除部分

---

**重构版本特点**：在保持原版算法完全不变的基础上，提供更好的用户体验和现代化功能。

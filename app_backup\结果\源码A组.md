 DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 1996/2000] [Accuracy_lsvm: 0.853819] [Accuracy_nrf: 0.771181] [Accuracy_pnb: 0.728472][Accuracy_mlp: 0.862847]
[Epoch 1997/2000][Batch 0/48][Autoencoder loss: 24.585817][C loss: 1.095509][M loss: 0.693160][D loss: 17.091553][G loss -4277.991699 ]time: 14:21:22.311386 
[Epoch 1997/2000][Batch 1/48][Autoencoder loss: 37.753941][C loss: 0.070611][M loss: 0.693160][D loss: -307.419037][G loss -4309.945312 ]time: 14:21:22.547410 
[Epoch 1997/2000][Batch 2/48][Autoencoder loss: 28.804939][C loss: 0.043556][M loss: 0.693160][D loss: -342.826569][G loss -4300.504395 ]time: 14:21:22.777606 
[Epoch 1997/2000][Batch 3/48][Autoencoder loss: 54.206875][C loss: 0.017650][M loss: 0.693160][D loss: -282.822144][G loss -4291.239258 ]time: 14:21:23.015110 
[Epoch 1997/2000][Batch 4/48][Autoencoder loss: 106.636917][C loss: 0.669793][M loss: 0.693160][D loss: 167.778381][G loss -4381.954590 ]time: 14:21:23.251824 
[Epoch 1997/2000][Batch 5/48][Autoencoder loss: 105.543068][C loss: 0.527811][M loss: 0.693160][D loss: 213.988968][G loss -4333.862305 ]time: 14:21:23.488777 
[Epoch 1997/2000][Batch 6/48][Autoencoder loss: 188.254059][C loss: 0.462797][M loss: 0.693160][D loss: 243.280136][G loss -4297.912109 ]time: 14:21:23.724803 
[Epoch 1997/2000][Batch 7/48][Autoencoder loss: 13.327023][C loss: 0.406318][M loss: 0.693161][D loss: 222.371536][G loss -4276.151367 ]time: 14:21:23.962574 
[Epoch 1997/2000][Batch 8/48][Autoencoder loss: 170.066650][C loss: 0.369157][M loss: 0.693161][D loss: 312.372040][G loss -4333.634277 ]time: 14:21:24.196531 
[Epoch 1997/2000][Batch 9/48][Autoencoder loss: 57.432453][C loss: 0.386062][M loss: 0.693160][D loss: 283.751007][G loss -4246.638184 ]time: 14:21:24.433145 
[Epoch 1997/2000][Batch 10/48][Autoencoder loss: 61.634121][C loss: 0.392064][M loss: 0.693160][D loss: 292.016815][G loss -4183.609863 ]time: 14:21:24.666354 
[Epoch 1997/2000][Batch 11/48][Autoencoder loss: 121.677391][C loss: 0.372788][M loss: 0.693161][D loss: 271.282104][G loss -4111.687988 ]time: 14:21:24.896714 
[Epoch 1997/2000][Batch 12/48][Autoencoder loss: 29.709965][C loss: 0.457355][M loss: 0.693161][D loss: 139.611450][G loss -3976.413086 ]time: 14:21:25.130120 
[Epoch 1997/2000][Batch 13/48][Autoencoder loss: 178.508118][C loss: 0.354529][M loss: 0.693160][D loss: 428.417267][G loss -3867.995605 ]time: 14:21:25.366855 
[Epoch 1997/2000][Batch 14/48][Autoencoder loss: 38.643711][C loss: 0.262476][M loss: 0.693160][D loss: 283.026947][G loss -3796.040039 ]time: 14:21:25.604586 
[Epoch 1997/2000][Batch 15/48][Autoencoder loss: 62.902809][C loss: 0.241538][M loss: 0.693160][D loss: 320.050751][G loss -3684.104004 ]time: 14:21:25.839451 
[Epoch 1997/2000][Batch 16/48][Autoencoder loss: 165.106186][C loss: 0.627851][M loss: 0.693161][D loss: 413.702972][G loss -3735.940674 ]time: 14:21:26.076562 
[Epoch 1997/2000][Batch 17/48][Autoencoder loss: 29.806475][C loss: 0.774302][M loss: 0.693161][D loss: 604.708191][G loss -3626.320068 ]time: 14:21:26.310046 
[Epoch 1997/2000][Batch 18/48][Autoencoder loss: 83.255852][C loss: 0.655236][M loss: 0.693161][D loss: 419.699829][G loss -3518.253662 ]time: 14:21:26.549338 
[Epoch 1997/2000][Batch 19/48][Autoencoder loss: 40.908573][C loss: 0.633275][M loss: 0.693160][D loss: 434.767944][G loss -3401.789307 ]time: 14:21:26.774570 
[Epoch 1997/2000][Batch 20/48][Autoencoder loss: 74.234383][C loss: 0.894999][M loss: 0.693160][D loss: 306.000854][G loss -3209.847656 ]time: 14:21:27.008865 
[Epoch 1997/2000][Batch 21/48][Autoencoder loss: 100.902855][C loss: 0.895443][M loss: 0.693160][D loss: 383.466797][G loss -3082.411621 ]time: 14:21:27.244935 
[Epoch 1997/2000][Batch 22/48][Autoencoder loss: 19.208973][C loss: 0.716061][M loss: 0.693160][D loss: 257.130157][G loss -2947.441650 ]time: 14:21:27.481586 
[Epoch 1997/2000][Batch 23/48][Autoencoder loss: 76.026146][C loss: 0.760156][M loss: 0.693160][D loss: 284.898865][G loss -2836.313965 ]time: 14:21:27.725031 
[Epoch 1997/2000][Batch 24/48][Autoencoder loss: 34.621521][C loss: 0.430542][M loss: 0.693160][D loss: 116.491722][G loss -2469.961914 ]time: 14:21:27.959051 
[Epoch 1997/2000][Batch 25/48][Autoencoder loss: 42.425091][C loss: 0.421338][M loss: 0.693160][D loss: 122.860458][G loss -2356.056885 ]time: 14:21:28.201424 
[Epoch 1997/2000][Batch 26/48][Autoencoder loss: 63.209785][C loss: 0.411120][M loss: 0.693160][D loss: 143.711884][G loss -2263.191162 ]time: 14:21:28.437410 
[Epoch 1997/2000][Batch 27/48][Autoencoder loss: 10.130283][C loss: 0.403350][M loss: 0.693160][D loss: 148.785599][G loss -2167.752930 ]time: 14:21:28.677230 
[Epoch 1997/2000][Batch 28/48][Autoencoder loss: 41.031235][C loss: 0.319532][M loss: 0.693160][D loss: 125.814178][G loss -2045.132324 ]time: 14:21:28.915868 
[Epoch 1997/2000][Batch 29/48][Autoencoder loss: 22.923208][C loss: 0.304796][M loss: 0.693160][D loss: 101.900040][G loss -1955.693970 ]time: 14:21:29.149398 
[Epoch 1997/2000][Batch 30/48][Autoencoder loss: 30.104908][C loss: 0.295860][M loss: 0.693160][D loss: 220.688675][G loss -1863.514038 ]time: 14:21:29.388454 
[Epoch 1997/2000][Batch 31/48][Autoencoder loss: 23.033062][C loss: 0.278682][M loss: 0.693160][D loss: 51.265232][G loss -1736.158813 ]time: 14:21:29.623081 
[Epoch 1997/2000][Batch 32/48][Autoencoder loss: 15.629118][C loss: 0.453462][M loss: 0.693160][D loss: 106.262932][G loss -1646.189697 ]time: 14:21:29.847914 
[Epoch 1997/2000][Batch 33/48][Autoencoder loss: 23.080847][C loss: 0.451279][M loss: 0.693160][D loss: 105.670357][G loss -1551.514893 ]time: 14:21:30.070898 
[Epoch 1997/2000][Batch 34/48][Autoencoder loss: 6.734160][C loss: 0.438424][M loss: 0.693160][D loss: 110.088196][G loss -1435.822144 ]time: 14:21:30.293626 
[Epoch 1997/2000][Batch 35/48][Autoencoder loss: 13.274107][C loss: 0.413796][M loss: 0.693160][D loss: 120.663864][G loss -1322.068115 ]time: 14:21:30.534849 
[Epoch 1997/2000][Batch 36/48][Autoencoder loss: 17.095299][C loss: 0.459391][M loss: 0.693161][D loss: 129.284058][G loss -1224.372803 ]time: 14:21:30.768673 
[Epoch 1997/2000][Batch 37/48][Autoencoder loss: 20.452599][C loss: 0.476891][M loss: 0.693161][D loss: 153.093887][G loss -1122.145630 ]time: 14:21:31.009862 
[Epoch 1997/2000][Batch 38/48][Autoencoder loss: 25.239702][C loss: 0.469349][M loss: 0.693161][D loss: 173.975128][G loss -1029.929810 ]time: 14:21:31.248161 
[Epoch 1997/2000][Batch 39/48][Autoencoder loss: 9.149356][C loss: 0.342839][M loss: 0.693161][D loss: 131.111603][G loss -937.229553 ]time: 14:21:31.488538 
[Epoch 1997/2000][Batch 40/48][Autoencoder loss: 14.838486][C loss: 0.228962][M loss: 0.693160][D loss: -63.374527][G loss -569.103149 ]time: 14:21:31.730793 
[Epoch 1997/2000][Batch 41/48][Autoencoder loss: 25.686117][C loss: 0.082463][M loss: 0.693161][D loss: 138.560608][G loss -492.290466 ]time: 14:21:31.968566 
[Epoch 1997/2000][Batch 42/48][Autoencoder loss: 13.772607][C loss: 0.372832][M loss: 0.693161][D loss: 31.149483][G loss -436.584564 ]time: 14:21:32.199724 
[Epoch 1997/2000][Batch 43/48][Autoencoder loss: 22.318909][C loss: 0.413023][M loss: 0.693161][D loss: -74.755112][G loss -338.496735 ]time: 14:21:32.429182 
[Epoch 1997/2000][Batch 44/48][Autoencoder loss: 9.960665][C loss: 0.607609][M loss: 0.693161][D loss: -128.887390][G loss -281.793304 ]time: 14:21:32.661925 
[Epoch 1997/2000][Batch 45/48][Autoencoder loss: 11.493991][C loss: 0.582101][M loss: 0.693161][D loss: -122.059364][G loss -164.040817 ]time: 14:21:32.904393 
[Epoch 1997/2000][Batch 46/48][Autoencoder loss: 16.076580][C loss: 0.530317][M loss: 0.693161][D loss: -124.165894][G loss -60.040501 ]time: 14:21:33.143292 
[Epoch 1997/2000][Batch 47/48][Autoencoder loss: 5.645513][C loss: 0.471803][M loss: 0.693161][D loss: -154.125702][G loss 29.920736 ]time: 14:21:33.381305 
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)
63/63 [==============================] - 0s 747us/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)
63/63 [==============================] - 0s 881us/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)
63/63 [==============================] - 0s 801us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 1997/2000] [Accuracy_lsvm: 0.853819] [Accuracy_nrf: 0.771181] [Accuracy_pnb: 0.728472][Accuracy_mlp: 0.862847]
[Epoch 1998/2000][Batch 0/48][Autoencoder loss: 21.639812][C loss: 1.003525][M loss: 0.693161][D loss: 158.771667][G loss -215.869232 ]time: 14:21:43.510513 
[Epoch 1998/2000][Batch 1/48][Autoencoder loss: 32.926296][C loss: 0.081485][M loss: 0.693161][D loss: 23.935726][G loss -120.508820 ]time: 14:21:43.742823 
[Epoch 1998/2000][Batch 2/48][Autoencoder loss: 21.087172][C loss: 0.041224][M loss: 0.693161][D loss: 14.664429][G loss -26.757399 ]time: 14:21:43.983883 
[Epoch 1998/2000][Batch 3/48][Autoencoder loss: 44.388805][C loss: 0.024020][M loss: 0.693161][D loss: 9.514435][G loss 81.783806 ]time: 14:21:44.212266 
[Epoch 1998/2000][Batch 4/48][Autoencoder loss: 93.525108][C loss: 0.646237][M loss: 0.693161][D loss: 222.113022][G loss 152.377777 ]time: 14:21:44.452706 
[Epoch 1998/2000][Batch 5/48][Autoencoder loss: 84.517448][C loss: 0.569805][M loss: 0.693161][D loss: 194.206100][G loss 257.737244 ]time: 14:21:44.690018 
[Epoch 1998/2000][Batch 6/48][Autoencoder loss: 162.949020][C loss: 0.520831][M loss: 0.693161][D loss: 212.606018][G loss 355.178986 ]time: 14:21:44.924981 
[Epoch 1998/2000][Batch 7/48][Autoencoder loss: 12.444377][C loss: 0.453109][M loss: 0.693161][D loss: 246.792358][G loss 452.329865 ]time: 14:21:45.168690 
[Epoch 1998/2000][Batch 8/48][Autoencoder loss: 146.433762][C loss: 0.370333][M loss: 0.693161][D loss: 239.580612][G loss 520.035339 ]time: 14:21:45.405474 
[Epoch 1998/2000][Batch 9/48][Autoencoder loss: 41.854744][C loss: 0.355351][M loss: 0.693161][D loss: 229.182236][G loss 617.396118 ]time: 14:21:45.643759 
[Epoch 1998/2000][Batch 10/48][Autoencoder loss: 47.740891][C loss: 0.350474][M loss: 0.693161][D loss: 230.687881][G loss 724.726379 ]time: 14:21:45.877581 
[Epoch 1998/2000][Batch 11/48][Autoencoder loss: 91.866402][C loss: 0.341373][M loss: 0.693161][D loss: 256.608368][G loss 816.697815 ]time: 14:21:46.115480 
[Epoch 1998/2000][Batch 12/48][Autoencoder loss: 23.569174][C loss: 0.407351][M loss: 0.693161][D loss: 117.010147][G loss 935.397827 ]time: 14:21:46.357349 
[Epoch 1998/2000][Batch 13/48][Autoencoder loss: 133.044174][C loss: 0.321128][M loss: 0.693161][D loss: 264.456726][G loss 1044.216919 ]time: 14:21:46.596551 
[Epoch 1998/2000][Batch 14/48][Autoencoder loss: 28.477367][C loss: 0.226222][M loss: 0.693160][D loss: 166.925812][G loss 1128.453369 ]time: 14:21:46.827922 
[Epoch 1998/2000][Batch 15/48][Autoencoder loss: 44.015247][C loss: 0.196418][M loss: 0.693160][D loss: 144.520248][G loss 1196.851807 ]time: 14:21:47.061540 
[Epoch 1998/2000][Batch 16/48][Autoencoder loss: 121.708115][C loss: 0.866391][M loss: 0.693161][D loss: 168.282684][G loss 1258.389893 ]time: 14:21:47.295266 
[Epoch 1998/2000][Batch 17/48][Autoencoder loss: 20.369635][C loss: 0.961750][M loss: 0.693161][D loss: 276.985016][G loss 1344.991577 ]time: 14:21:47.531533 
[Epoch 1998/2000][Batch 18/48][Autoencoder loss: 60.182884][C loss: 0.690828][M loss: 0.693161][D loss: 179.110123][G loss 1435.682251 ]time: 14:21:47.806847 
[Epoch 1998/2000][Batch 19/48][Autoencoder loss: 36.241253][C loss: 0.585608][M loss: 0.693161][D loss: 178.583725][G loss 1523.425659 ]time: 14:21:48.040476 
[Epoch 1998/2000][Batch 20/48][Autoencoder loss: 59.236095][C loss: 1.057946][M loss: 0.693161][D loss: 66.106934][G loss 1642.263794 ]time: 14:21:48.278124 
[Epoch 1998/2000][Batch 21/48][Autoencoder loss: 65.817505][C loss: 0.959537][M loss: 0.693161][D loss: 182.081772][G loss 1696.034058 ]time: 14:21:48.511559 
[Epoch 1998/2000][Batch 22/48][Autoencoder loss: 13.362812][C loss: 0.785121][M loss: 0.693161][D loss: 41.539032][G loss 1778.187866 ]time: 14:21:48.744711 
[Epoch 1998/2000][Batch 23/48][Autoencoder loss: 51.673893][C loss: 0.795581][M loss: 0.693161][D loss: 91.604431][G loss 1881.333740 ]time: 14:21:48.971925 
[Epoch 1998/2000][Batch 24/48][Autoencoder loss: 30.839729][C loss: 0.541858][M loss: 0.693160][D loss: 13.512467][G loss 2022.749146 ]time: 14:21:49.192904 
[Epoch 1998/2000][Batch 25/48][Autoencoder loss: 27.144268][C loss: 0.510758][M loss: 0.693160][D loss: 6.921883][G loss 2055.616455 ]time: 14:21:49.422597 
[Epoch 1998/2000][Batch 26/48][Autoencoder loss: 36.378235][C loss: 0.477553][M loss: 0.693161][D loss: -2.596447][G loss 2152.251709 ]time: 14:21:49.647826 
[Epoch 1998/2000][Batch 27/48][Autoencoder loss: 8.228487][C loss: 0.449093][M loss: 0.693161][D loss: -21.372265][G loss 2231.403564 ]time: 14:21:49.885633 
[Epoch 1998/2000][Batch 28/48][Autoencoder loss: 24.595713][C loss: 0.386950][M loss: 0.693161][D loss: -37.797497][G loss 2273.947754 ]time: 14:21:50.119675 
[Epoch 1998/2000][Batch 29/48][Autoencoder loss: 14.410480][C loss: 0.369042][M loss: 0.693161][D loss: -33.741425][G loss 2344.168945 ]time: 14:21:50.346740 
[Epoch 1998/2000][Batch 30/48][Autoencoder loss: 20.656092][C loss: 0.372158][M loss: 0.693161][D loss: 49.042931][G loss 2403.257812 ]time: 14:21:50.747699 
[Epoch 1998/2000][Batch 31/48][Autoencoder loss: 11.575515][C loss: 0.329590][M loss: 0.693160][D loss: -82.961914][G loss 2455.322754 ]time: 14:21:51.182522 
[Epoch 1998/2000][Batch 32/48][Autoencoder loss: 10.168924][C loss: 0.361917][M loss: 0.693160][D loss: -56.686378][G loss 2516.988770 ]time: 14:21:51.437142 
[Epoch 1998/2000][Batch 33/48][Autoencoder loss: 10.900362][C loss: 0.361172][M loss: 0.693160][D loss: -34.888409][G loss 2581.039307 ]time: 14:21:51.673929 
[Epoch 1998/2000][Batch 34/48][Autoencoder loss: 5.247300][C loss: 0.345619][M loss: 0.693160][D loss: -101.565216][G loss 2646.013428 ]time: 14:21:51.910800 
[Epoch 1998/2000][Batch 35/48][Autoencoder loss: 9.102944][C loss: 0.330721][M loss: 0.693160][D loss: -82.947067][G loss 2694.607666 ]time: 14:21:52.144274 
[Epoch 1998/2000][Batch 36/48][Autoencoder loss: 12.309389][C loss: 0.509771][M loss: 0.693160][D loss: -86.142654][G loss 2724.649414 ]time: 14:21:52.375526 
[Epoch 1998/2000][Batch 37/48][Autoencoder loss: 16.064734][C loss: 0.356500][M loss: 0.693160][D loss: -81.419823][G loss 2846.189941 ]time: 14:21:52.613803 
[Epoch 1998/2000][Batch 38/48][Autoencoder loss: 16.768604][C loss: 0.349019][M loss: 0.693160][D loss: -79.470734][G loss 2908.511963 ]time: 14:21:52.852385 
[Epoch 1998/2000][Batch 39/48][Autoencoder loss: 10.053894][C loss: 0.246689][M loss: 0.693160][D loss: -14.659636][G loss 2957.736816 ]time: 14:21:53.084126 
[Epoch 1998/2000][Batch 40/48][Autoencoder loss: 14.535297][C loss: 0.273509][M loss: 0.693160][D loss: -83.412170][G loss 3161.925781 ]time: 14:21:53.315490 
[Epoch 1998/2000][Batch 41/48][Autoencoder loss: 20.589756][C loss: 0.092713][M loss: 0.693160][D loss: -28.340885][G loss 3220.587402 ]time: 14:21:53.555205 
[Epoch 1998/2000][Batch 42/48][Autoencoder loss: 15.356708][C loss: 0.392574][M loss: 0.693160][D loss: -167.173523][G loss 3256.940674 ]time: 14:21:53.792252 
[Epoch 1998/2000][Batch 43/48][Autoencoder loss: 18.569120][C loss: 0.358391][M loss: 0.693160][D loss: -331.076874][G loss 3266.078613 ]time: 14:21:54.029935 
[Epoch 1998/2000][Batch 44/48][Autoencoder loss: 8.646731][C loss: 0.635442][M loss: 0.693160][D loss: -241.213699][G loss 3348.945801 ]time: 14:21:54.266916 
[Epoch 1998/2000][Batch 45/48][Autoencoder loss: 9.079688][C loss: 0.587306][M loss: 0.693160][D loss: -261.898010][G loss 3420.831543 ]time: 14:21:54.506228 
[Epoch 1998/2000][Batch 46/48][Autoencoder loss: 15.805739][C loss: 0.501036][M loss: 0.693160][D loss: -266.143280][G loss 3479.711670 ]time: 14:21:54.736551 
[Epoch 1998/2000][Batch 47/48][Autoencoder loss: 7.158787][C loss: 0.373678][M loss: 0.693160][D loss: -235.755997][G loss 3453.518799 ]time: 14:21:55.070373 
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)
63/63 [==============================] - 0s 695us/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)
63/63 [==============================] - 0s 559us/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)
63/63 [==============================] - 0s 565us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 1998/2000] [Accuracy_lsvm: 0.853819] [Accuracy_nrf: 0.771181] [Accuracy_pnb: 0.728472][Accuracy_mlp: 0.862847]
[Epoch 1999/2000][Batch 0/48][Autoencoder loss: 21.972572][C loss: 1.347947][M loss: 0.693160][D loss: -71.278130][G loss 3334.148926 ]time: 14:22:02.953920 
[Epoch 1999/2000][Batch 1/48][Autoencoder loss: 28.841986][C loss: 0.072962][M loss: 0.693160][D loss: -87.393242][G loss 3377.883789 ]time: 14:22:03.193631 
[Epoch 1999/2000][Batch 2/48][Autoencoder loss: 26.825012][C loss: 0.037071][M loss: 0.693160][D loss: -84.040039][G loss 3463.548340 ]time: 14:22:03.442686 
[Epoch 1999/2000][Batch 3/48][Autoencoder loss: 43.818798][C loss: 0.010567][M loss: 0.693160][D loss: -79.528366][G loss 3514.438721 ]time: 14:22:03.680293 
[Epoch 1999/2000][Batch 4/48][Autoencoder loss: 85.807266][C loss: 0.683925][M loss: 0.693160][D loss: 26.016640][G loss 3508.088135 ]time: 14:22:03.914787 
[Epoch 1999/2000][Batch 5/48][Autoencoder loss: 91.085579][C loss: 0.542991][M loss: 0.693160][D loss: -68.544243][G loss 3586.639404 ]time: 14:22:04.149812 
[Epoch 1999/2000][Batch 6/48][Autoencoder loss: 151.185226][C loss: 0.473586][M loss: 0.693160][D loss: -47.362335][G loss 3637.619629 ]time: 14:22:04.377370 
[Epoch 1999/2000][Batch 7/48][Autoencoder loss: 16.308022][C loss: 0.410342][M loss: 0.693160][D loss: 24.465504][G loss 3659.630127 ]time: 14:22:04.612236 
[Epoch 1999/2000][Batch 8/48][Autoencoder loss: 144.292480][C loss: 0.555038][M loss: 0.693160][D loss: -14.953939][G loss 3709.565674 ]time: 14:22:04.847172 
[Epoch 1999/2000][Batch 9/48][Autoencoder loss: 41.084591][C loss: 0.553097][M loss: 0.693160][D loss: -15.449291][G loss 3787.354248 ]time: 14:22:05.081817 
[Epoch 1999/2000][Batch 10/48][Autoencoder loss: 56.513340][C loss: 0.544219][M loss: 0.693160][D loss: 54.938087][G loss 3823.688477 ]time: 14:22:05.317634 
[Epoch 1999/2000][Batch 11/48][Autoencoder loss: 91.753464][C loss: 0.508829][M loss: 0.693160][D loss: 80.891968][G loss 3869.399170 ]time: 14:22:05.558363 
[Epoch 1999/2000][Batch 12/48][Autoencoder loss: 20.458107][C loss: 0.658179][M loss: 0.693160][D loss: -68.061821][G loss 3874.871826 ]time: 14:22:05.793584 
[Epoch 1999/2000][Batch 13/48][Autoencoder loss: 143.441727][C loss: 0.503612][M loss: 0.693160][D loss: 89.585953][G loss 3923.556885 ]time: 14:22:06.032684 
[Epoch 1999/2000][Batch 14/48][Autoencoder loss: 24.057621][C loss: 0.412245][M loss: 0.693160][D loss: -25.295734][G loss 3975.260742 ]time: 14:22:06.267246 
[Epoch 1999/2000][Batch 15/48][Autoencoder loss: 53.749508][C loss: 0.388600][M loss: 0.693160][D loss: -4.310022][G loss 3981.768066 ]time: 14:22:06.502304 
[Epoch 1999/2000][Batch 16/48][Autoencoder loss: 109.539459][C loss: 0.520104][M loss: 0.693160][D loss: -36.387665][G loss 3963.717041 ]time: 14:22:06.740635 
[Epoch 1999/2000][Batch 17/48][Autoencoder loss: 27.306400][C loss: 0.606470][M loss: 0.693160][D loss: 89.089149][G loss 4008.602539 ]time: 14:22:06.976932 
[Epoch 1999/2000][Batch 18/48][Autoencoder loss: 68.167191][C loss: 0.646365][M loss: 0.693160][D loss: -48.419991][G loss 4079.249268 ]time: 14:22:07.212877 
[Epoch 1999/2000][Batch 19/48][Autoencoder loss: 21.236876][C loss: 0.640614][M loss: 0.693160][D loss: -25.734154][G loss 3967.825928 ]time: 14:22:07.446068 
[Epoch 1999/2000][Batch 20/48][Autoencoder loss: 59.594131][C loss: 0.960676][M loss: 0.693160][D loss: -114.042122][G loss 4127.660645 ]time: 14:22:07.681708 
[Epoch 1999/2000][Batch 21/48][Autoencoder loss: 68.919846][C loss: 0.937030][M loss: 0.693160][D loss: 58.841339][G loss 4149.585938 ]time: 14:22:07.912246 
[Epoch 1999/2000][Batch 22/48][Autoencoder loss: 13.464675][C loss: 0.713665][M loss: 0.693160][D loss: -133.695633][G loss 4129.922852 ]time: 14:22:08.139199 
[Epoch 1999/2000][Batch 23/48][Autoencoder loss: 57.909370][C loss: 0.883309][M loss: 0.693160][D loss: -57.691154][G loss 4174.661621 ]time: 14:22:08.367459 
[Epoch 1999/2000][Batch 24/48][Autoencoder loss: 23.224604][C loss: 0.537987][M loss: 0.693160][D loss: -62.339088][G loss 4169.504395 ]time: 14:22:08.593846 
[Epoch 1999/2000][Batch 25/48][Autoencoder loss: 36.529968][C loss: 0.524606][M loss: 0.693160][D loss: -48.908424][G loss 4222.892090 ]time: 14:22:08.829476 
[Epoch 1999/2000][Batch 26/48][Autoencoder loss: 36.761799][C loss: 0.519019][M loss: 0.693161][D loss: -40.396988][G loss 4167.278809 ]time: 14:22:09.057725 
[Epoch 1999/2000][Batch 27/48][Autoencoder loss: 8.264235][C loss: 0.496958][M loss: 0.693161][D loss: -114.694603][G loss 4183.296875 ]time: 14:22:09.289773 
[Epoch 1999/2000][Batch 28/48][Autoencoder loss: 33.443581][C loss: 0.461183][M loss: 0.693160][D loss: -112.822075][G loss 4229.107422 ]time: 14:22:09.524959 
[Epoch 1999/2000][Batch 29/48][Autoencoder loss: 14.256302][C loss: 0.424424][M loss: 0.693160][D loss: -85.475113][G loss 4169.356934 ]time: 14:22:09.755271 
[Epoch 1999/2000][Batch 30/48][Autoencoder loss: 26.785490][C loss: 0.410508][M loss: 0.693160][D loss: 19.413445][G loss 4225.767090 ]time: 14:22:09.988363 
[Epoch 1999/2000][Batch 31/48][Autoencoder loss: 11.645900][C loss: 0.363877][M loss: 0.693160][D loss: -72.635963][G loss 4194.159180 ]time: 14:22:10.214998 
[Epoch 1999/2000][Batch 32/48][Autoencoder loss: 10.470944][C loss: 0.447104][M loss: 0.693160][D loss: -11.408991][G loss 4153.006836 ]time: 14:22:10.447821 
[Epoch 1999/2000][Batch 33/48][Autoencoder loss: 13.637289][C loss: 0.435138][M loss: 0.693160][D loss: -98.344162][G loss 4145.399902 ]time: 14:22:10.679768 
[Epoch 1999/2000][Batch 34/48][Autoencoder loss: 5.489159][C loss: 0.417365][M loss: 0.693161][D loss: -94.811966][G loss 4153.402832 ]time: 14:22:10.915611 
[Epoch 1999/2000][Batch 35/48][Autoencoder loss: 10.003037][C loss: 0.394109][M loss: 0.693161][D loss: -120.794159][G loss 4131.758789 ]time: 14:22:11.144926 
[Epoch 1999/2000][Batch 36/48][Autoencoder loss: 14.317943][C loss: 0.448031][M loss: 0.693160][D loss: -62.612904][G loss 4146.934082 ]time: 14:22:11.374244 
[Epoch 1999/2000][Batch 37/48][Autoencoder loss: 15.072882][C loss: 0.399462][M loss: 0.693160][D loss: -81.806099][G loss 4082.322021 ]time: 14:22:11.610282 
[Epoch 1999/2000][Batch 38/48][Autoencoder loss: 17.524767][C loss: 0.449770][M loss: 0.693160][D loss: -211.831879][G loss 4087.335449 ]time: 14:22:11.845766 
[Epoch 1999/2000][Batch 39/48][Autoencoder loss: 8.040080][C loss: 0.295465][M loss: 0.693160][D loss: 36.539352][G loss 4025.350098 ]time: 14:22:12.088180 
[Epoch 1999/2000][Batch 40/48][Autoencoder loss: 10.579134][C loss: 0.216971][M loss: 0.693160][D loss: -98.472321][G loss 4102.024902 ]time: 14:22:12.327820 
[Epoch 1999/2000][Batch 41/48][Autoencoder loss: 21.508854][C loss: 0.090443][M loss: 0.693161][D loss: -128.707977][G loss 4059.031494 ]time: 14:22:12.562724 
[Epoch 1999/2000][Batch 42/48][Autoencoder loss: 17.811468][C loss: 0.367012][M loss: 0.693161][D loss: -177.695786][G loss 4027.105225 ]time: 14:22:12.798444 
[Epoch 1999/2000][Batch 43/48][Autoencoder loss: 20.933500][C loss: 0.389883][M loss: 0.693161][D loss: -278.651306][G loss 3948.327148 ]time: 14:22:13.037124 
[Epoch 1999/2000][Batch 44/48][Autoencoder loss: 6.730995][C loss: 0.468157][M loss: 0.693160][D loss: -163.464691][G loss 3912.697510 ]time: 14:22:13.266123 
[Epoch 1999/2000][Batch 45/48][Autoencoder loss: 10.518311][C loss: 0.431012][M loss: 0.693160][D loss: -163.494141][G loss 3949.171387 ]time: 14:22:13.505438 
[Epoch 1999/2000][Batch 46/48][Autoencoder loss: 17.035372][C loss: 0.358556][M loss: 0.693160][D loss: -136.719803][G loss 3810.829102 ]time: 14:22:13.745900 
[Epoch 1999/2000][Batch 47/48][Autoencoder loss: 9.725392][C loss: 0.253131][M loss: 0.693161][D loss: -129.792633][G loss 3839.373535 ]time: 14:22:13.984277 
为类别 1 生成 2000 个特征, 属性形状: (2000, 20)
63/63 [==============================] - 0s 852us/step
为类别 6 生成 2000 个特征, 属性形状: (2000, 20)
63/63 [==============================] - 0s 929us/step
为类别 14 生成 2000 个特征, 属性形状: (2000, 20)
63/63 [==============================] - 0s 837us/step
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/base.py:1473: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  return fit_method(estimator, *args, **kwargs)
/usr/local/lib/python3.12/dist-packages/sklearn/utils/validation.py:1339: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
/usr/local/lib/python3.12/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:1105: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().
  y = column_or_1d(y, warn=True)
[Epoch 1999/2000] [Accuracy_lsvm: 0.853819] [Accuracy_nrf: 0.771181] [Accuracy_pnb: 0.728472][Accuracy_mlp: 0.862847]
finished! best_acc:0.8628
root@8325d131374d:/app# ^C
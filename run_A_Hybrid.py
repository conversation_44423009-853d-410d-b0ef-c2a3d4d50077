#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 A-Hybrid实验启动脚本
融合HardTriplet强分离能力与SmartCRL稳定性 (A组版本)
"""

import os
import sys
import datetime

def main():
    print("🔥 A-Hybrid实验启动器")
    print("=" * 80)
    print("📊 实验配置:")
    print("   - 目标组别: A组")
    print("   - 测试类别: [1, 6, 14]")
    print("   - 训练类别: [2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 15]")
    print("   - 融合策略: HardTriplet强权重 + SmartCRL适度约束")
    print("   - 预期目标: 保持55%+准确率，提升训练稳定性")
    print("=" * 80)
    
    # 检查依赖
    try:
        import tensorflow as tf
        import numpy as np
        import sklearn
        print(f"✅ 依赖检查通过")
        print(f"   - TensorFlow: {tf.__version__}")
        print(f"   - NumPy: {np.__version__}")
        print(f"   - Scikit-learn: {sklearn.__version__}")
    except ImportError as e:
        print(f"❌ 依赖检查失败: {e}")
        return
    
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    print(f"✅ 日志目录已创建: logs/")
    
    print("\n🚀 开始执行A-Hybrid实验...")
    print("=" * 80)
    
    # 导入并运行实验
    try:
        sys.path.append('scripts')
        from acgan_triplet_A_Hybrid import Zero_shot
        
        # 创建模型实例
        model = Zero_shot()
        
        # 开始训练
        model.train(epochs=2000, batch_size=256)
        
    except Exception as e:
        print(f"❌ 实验执行失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🎉 A-Hybrid实验完成!")
    print("=" * 80)

if __name__ == "__main__":
    main()
import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
from tensorflow_addons.layers import SpectralNormalization
import datetime
import os
import read_data
from tensorflow.keras.losses import mean_squared_error
from test import feature_generation_and_diagnosis
from sklearn.preprocessing import MinMaxScaler
from data_pipeline import OptimizedDataPipeline, monitor_gpu, check_docker_gpu_config

# 配置GPU显存按需增长
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    tf.config.set_visible_devices(gpus[0], 'GPU')
    tf.config.experimental.set_memory_growth(gpus[0], True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPU")
  except RuntimeError as e:
    print(e)

# 在训练开始前检查GPU配置
print("=" * 60)
print("GPU配置检查和优化")
print("=" * 60)
check_docker_gpu_config()
monitor_gpu()
print("=" * 60)

def residual_block(x, units):
    """一个简单的全连接残差块"""
    shortcut = x
    
    y = Dense(units)(x)
    y = BatchNormalization()(y)
    y = LeakyReLU(alpha=0.2)(y)
    
    y = Dense(units)(y)
    y = BatchNormalization()(y)
    
    # 如果输入和输出维度不同，需要一个线性投影
    if x.shape[-1] != units:
        shortcut = Dense(units)(shortcut)
        
    y = tf.keras.layers.add([shortcut, y])
    y = LeakyReLU(alpha=0.2)(y)
    return y

class SelfAttention(Layer):
    def __init__(self, **kwargs):
        super(SelfAttention, self).__init__(**kwargs)

    def build(self, input_shape):
        self.W_q = self.add_weight(shape=(input_shape[-1], input_shape[-1]),
                                   initializer='glorot_uniform',
                                   trainable=True)
        self.W_k = self.add_weight(shape=(input_shape[-1], input_shape[-1]),
                                   initializer='glorot_uniform',
                                   trainable=True)
        self.W_v = self.add_weight(shape=(input_shape[-1], input_shape[-1]),
                                   initializer='glorot_uniform',
                                   trainable=True)
        super(SelfAttention, self).build(input_shape)

    def call(self, inputs):
        q = tf.matmul(inputs, self.W_q)
        k = tf.matmul(inputs, self.W_k)
        v = tf.matmul(inputs, self.W_v)
        
        # 修复混合精度类型问题
        scale = tf.sqrt(tf.cast(tf.shape(q)[-1], inputs.dtype))
        attention_weights = tf.nn.softmax(tf.matmul(q, k, transpose_b=True) / scale)
        attention_output = tf.matmul(attention_weights, v)
        return attention_output
        
class Zero_shot_Optimized():
    def __init__(self, use_optimized_pipeline=True):
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256  # 保持原始的256维特征
        self.feature_shape=(256,)
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1
        self.crl = True

        self.lambda_cla = 10 
        self.lambda_triplet = 10 
        self.lambda_crl = 0.01 
        
        self.bound = True
        self.mi_weight = 0.001 
        self.mi_bound = 100
        self.triplet_margin = 0.2
        
        # 难负例挖掘参数
        self.use_hard_negative = True
        self.hard_negative_ratio = 0.5  # 50%难负例 + 50%随机负例
        self.top_k_candidates = 3       # 候选数量减少
        
        # 初始化优化数据流水线
        self.use_optimized_pipeline = use_optimized_pipeline
        if self.use_optimized_pipeline:
            self.data_pipeline = OptimizedDataPipeline(use_mixed_precision=True)
            print("已启用优化数据流水线和混合精度训练")
        
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001) # For triplet loss
        
        self.autoencoder= self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c= self.build_classifier()
        
        print("优化版Zero-shot模型初始化完成")

    def build_autoencoder(self):
        sample = Input(shape=self.sample_shape)
        
        e1 = Dense(128)(sample)
        e1 = LeakyReLU(alpha=0.2)(e1)
        e1 = BatchNormalization()(e1)
        
        e2 = residual_block(e1, 256)
        e3 = residual_block(e2, 256)
        
        e3_attention = SelfAttention()(e3)
        
        feature = Dense(256)(e3_attention)
        feature = BatchNormalization()(feature)
        
        d1 = Dense(256, activation='relu')(feature)
        d2 = Dense(128, activation='relu')(d1)
        d3 = Dense(64, activation='relu')(d2)
        output_sample = Dense(self.data_lenth, activation='linear')(d3)
        
        encoder = Model(sample, feature)
        self.encoder = encoder
        
        return Model(sample, [feature, output_sample])

    def build_discriminator(self):
        feature = Input(shape=self.feature_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        feature_embedding = Dense(128)(feature)
        feature_embedding = LeakyReLU(alpha=0.2)(feature_embedding)
        
        attribute_embedding = Dense(128)(attribute)
        attribute_embedding = LeakyReLU(alpha=0.2)(attribute_embedding)
        
        d_input = concatenate([feature_embedding, attribute_embedding])
        
        d1 = Dense(128)(d_input)
        d1 = LeakyReLU(alpha=0.2)(d1)
        d1 = BatchNormalization()(d1)
        
        d2 = residual_block(d1, 128)
        d3 = residual_block(d2, 64)
        
        validity = Dense(1)(d3)
        
        return Model([feature, attribute], validity)

    def build_generator(self):
        noise = Input(shape=self.noise_shape)
        attribute = Input(shape=(20,), dtype='float32')
        
        noise_embedding = Flatten()(noise)
        attribute_embedding = Dense(self.latent_dim)(attribute)
        
        g_input = concatenate([noise_embedding, attribute_embedding])

        g1 = Dense(128)(g_input)
        g1 = LeakyReLU(alpha=0.2)(g1)
        g1 = BatchNormalization()(g1)

        g2 = residual_block(g1, 256)
        g3 = residual_block(g2, 256)
        
        g3_attention = SelfAttention()(g3)
        
        generated_feature = Dense(256)(g3_attention)
        generated_feature = BatchNormalization()(generated_feature)

        return Model([noise, attribute], generated_feature)

    def build_classifier(self):
        sample = Input(shape=self.feature_shape)

        c0 = sample
        c1 = Dense(100)(c0)
        c1 = LeakyReLU(alpha=0.2)(c1)
        
        c2 = Dense(50)(c1)
        c2 = LeakyReLU(alpha=0.2)(c2)
        hidden_ouput = c2
               
        c3 = Dense(20, activation="sigmoid")(c2)
        predict_attribute = c3
        
        return Model(sample, [hidden_ouput, predict_attribute])

    def wasserstein_loss(self, y_true, y_pred):
        """Wasserstein loss function with mixed precision support"""
        # 确保类型一致性，处理混合精度
        y_true = tf.cast(y_true, y_pred.dtype)
        return K.mean(y_true * y_pred)

    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est

    def mi_penalty_loss(self, x, z):
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)

    def classification_loss(self, current_batch_features, y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)
        
        mi_penalty = 0
        if self.bound == True:
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss

    def triplet_loss(self, anchor, positive, negative):
        pos_dist = tf.reduce_sum(tf.square(anchor - positive), axis=-1)
        neg_dist = tf.reduce_sum(tf.square(anchor - negative), axis=-1)
        
        basic_loss = pos_dist - neg_dist + self.triplet_margin
        loss = tf.reduce_mean(tf.maximum(basic_loss, 0.0))
        return loss

    def cycle_rank_loss(self, anchor, positive, negative):
        return self.triplet_loss(anchor, positive, negative)

    def hard_negative_sampling(self, anchor_labels, all_class_attributes, seen_class_indices):
        """
        难负例采样
        为每个anchor样本选择属性相似但类别不同的样本作为负例
        """
        hard_negatives = []
        
        for anchor_label in anchor_labels:
            anchor_attr = all_class_attributes[anchor_label]
            
            # 计算与其他类别的属性相似度
            similarities = []
            available_classes = []
            
            for class_idx in seen_class_indices:
                if class_idx != anchor_label:
                    other_attr = all_class_attributes[class_idx]
                    # 使用余弦相似度
                    similarity = np.dot(anchor_attr, other_attr) / (np.linalg.norm(anchor_attr) * np.linalg.norm(other_attr))
                    similarities.append(similarity)
                    available_classes.append(class_idx)
            
            if not available_classes:
                # 如果没有可用类别，随机选择一个
                hard_negatives.append(seen_class_indices[0])
                continue
                
            similarities = np.array(similarities)
            available_similarities = similarities
            
            # 选择相似度最高的K个类别
            if len(available_classes) > self.top_k_candidates:
                top_k_indices = np.argsort(available_similarities)[-self.top_k_candidates:]
                candidate_classes = [available_classes[idx] for idx in top_k_indices]
            else:
                candidate_classes = available_classes
            
            # 从候选类别中随机选择一个
            hard_negative_class = np.random.choice(candidate_classes)
            hard_negatives.append(hard_negative_class)
            
        return hard_negatives

    def train_optimized(self, epochs, batch_size=512, log_file=None, test_class_indices=None):
        """使用优化数据流水线的训练方法"""
        start_time = datetime.datetime.now()
        
        accuracy_list_1=[]
        accuracy_list_2=[]
        accuracy_list_3=[]
        accuracy_list_4=[]
        
        PATH_train='./dataset_train_case1.npz'
        PATH_test='./dataset_test_case1.npz'
        
        train_data = np.load(PATH_train)
        test_data = np.load(PATH_test)
        
        # 使用传入的测试类别，如果没有传入则默认使用E组
        if test_class_indices is None:
            test_class_indices = [9, 13, 15]  # 默认E组 (1-based)
        
        print(f"开始为测试类别 {test_class_indices} 准备优化数据流水线，批处理大小: {batch_size}")
        
        # 使用优化数据流水线准备数据
        data_info = self.data_pipeline.prepare_data_triplet(
            train_data, test_data, test_class_indices, batch_size=batch_size, shuffle_buffer=20000
        )
        
        train_dataset = data_info['train_dataset']
        train_X_by_class = data_info['train_X_by_class'] 
        train_Y_by_class = data_info['train_Y_by_class']
        seen_class_indices = data_info['seen_classes']
        testdata = data_info['test_X']
        test_attributelabel = data_info['test_Y']
        
        # 获取所有类别的属性向量（用于难负例挖掘）
        all_class_attributes = {}
        for i in seen_class_indices:
            all_class_attributes[i] = train_Y_by_class[i][0]  # 取第一个样本的属性向量
        
        print(f"开始优化版训练，测试类别: {test_class_indices}")
        print(f"批处理大小: {batch_size}")
        print(f"混合精度训练: 已启用")
        print(f"难负例挖掘: {'已启用' if self.use_hard_negative else '已禁用'}")
               
        for epoch in range(epochs):
            epoch_losses = {'ae_c': [], 'm': [], 'd': [], 'g': []}
            batch_count = 0
            
            for batch_data in train_dataset:
                train_x, train_y, train_labels = batch_data
                current_batch_size = tf.shape(train_x)[0]
                batch_count += 1
                
                # 监控GPU使用情况（每100个批次）
                if batch_count % 100 == 0:
                    print(f"Epoch {epoch}, Batch {batch_count} - 监控GPU状态:")
                    monitor_gpu()
                                                                               
                # Autoencoder and Classifier Training (Same as before)
                self.autoencoder.trainable = True
                self.c.trainable = True # Train C together with AE now
                
                with tf.GradientTape(persistent=True) as tape_auto_c:
                  feature, output_sample=self.autoencoder(train_x)
                  autoencoder_loss=mean_squared_error(train_x,output_sample)      

                  hidden_ouput_c,predict_attribute_c=self.c(feature)
                  c_loss=self.classification_loss(feature,train_y, hidden_ouput_c, predict_attribute_c)

                  total_ac_loss = autoencoder_loss + c_loss

                grads_auto_c = tape_auto_c.gradient(total_ac_loss, self.autoencoder.trainable_weights + self.c.trainable_weights)
                self.autoencoder_optimizer.apply_gradients(zip(grads_auto_c, self.autoencoder.trainable_weights + self.c.trainable_weights))

                del tape_auto_c
                epoch_losses['ae_c'].append(float(tf.reduce_mean(total_ac_loss)))

                # Triplet Loss Metric Learning with Hard Negative Mining
                self.autoencoder.trainable = True # Encoder is part of metric learning
                self.c.trainable = True
                
                # Sample triplets with Hard Negative Mining - 优化版采样
                train_labels_np = train_labels.numpy()
                positive_samples = []
                negative_samples = []
                
                for j, label in enumerate(train_labels_np):
                    # Positive sampling (same as original)
                    if label in train_X_by_class and len(train_X_by_class[label]) > 0:
                        pos_class_samples = train_X_by_class[label]
                        pos_idx = np.random.choice(len(pos_class_samples))
                        positive_samples.append(pos_class_samples[pos_idx])
                    else:
                        positive_samples.append(train_x[j].numpy())  # 备用方案
                    
                    # Negative sampling with hard negative mining
                    if self.use_hard_negative and np.random.random() < self.hard_negative_ratio:
                        # 使用难负例挖掘
                        hard_neg_classes = self.hard_negative_sampling([label], all_class_attributes, seen_class_indices)
                        neg_class = hard_neg_classes[0]
                    else:
                        # 传统随机负例采样
                        available_classes = [c for c in seen_class_indices if c != label and c in train_X_by_class]
                        if available_classes:
                            neg_class = np.random.choice(available_classes)
                        else:
                            neg_class = seen_class_indices[0]  # 备用方案
                    
                    if neg_class in train_X_by_class and len(train_X_by_class[neg_class]) > 0:
                        neg_class_samples = train_X_by_class[neg_class]
                        neg_idx = np.random.choice(len(neg_class_samples))
                        negative_samples.append(neg_class_samples[neg_idx])
                    else:
                        negative_samples.append(train_x[j].numpy())  # 备用方案

                positive_samples = tf.constant(np.array(positive_samples), dtype=tf.float32)
                negative_samples = tf.constant(np.array(negative_samples), dtype=tf.float32)

                with tf.GradientTape() as tape_m:
                    anchor_features = self.encoder(train_x)
                    positive_features = self.encoder(positive_samples)
                    negative_features = self.encoder(negative_samples)
                    
                    m_loss = self.triplet_loss(anchor_features, positive_features, negative_features)

                grads_m = tape_m.gradient(m_loss, self.encoder.trainable_weights)
                self.m_optimizer.apply_gradients(zip(grads_m, self.encoder.trainable_weights))
                epoch_losses['m'].append(float(m_loss))

                # Discriminator Training (Same as before)
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = True
                self.g.trainable = False
                
                with tf.GradientTape() as tape_d:
                    noise = tf.random.normal(shape=(current_batch_size, self.latent_dim, 1))
                    Fake_feature = self.g([noise,train_y])
                    Real_validity = self.d([feature,train_y])
                    Fake_validity = self.d([Fake_feature,train_y])
                    
                    valid = -tf.ones((current_batch_size, 1))
                    fake = tf.ones((current_batch_size, 1))
                    
                    d_loss_real = self.wasserstein_loss(valid, Real_validity)
                    d_loss_fake = self.wasserstein_loss(fake, Fake_validity)
                    d_loss = (d_loss_real + d_loss_fake) / 2

                grads_d = tape_d.gradient(d_loss, self.d.trainable_weights)
                self.d_optimizer.apply_gradients(zip(grads_d, self.d.trainable_weights))
                epoch_losses['d'].append(float(d_loss))

                # Generator Training (Same as before)
                self.autoencoder.trainable = False
                self.c.trainable = False
                self.d.trainable = False
                self.g.trainable = True
                
                with tf.GradientTape() as tape_g:
                  noise_g = tf.random.normal(shape=(current_batch_size, self.latent_dim, 1))
                  Fake_feature_g = self.g([noise_g,train_y])
                  Fake_validity_g = self.d([Fake_feature_g,train_y])
                  valid = -tf.ones((current_batch_size, 1))
                  adversarial_loss = self.wasserstein_loss(valid, Fake_validity_g)
            
                  fake_hidden_ouput_g, Fake_classification_g = self.c(Fake_feature_g)
                  classification_loss = self.classification_loss(Fake_feature_g,train_y, fake_hidden_ouput_g, Fake_classification_g)
                  
                  # Triplet loss for Generator
                  g_anchor_features = Fake_feature_g
                  g_positive_features = self.encoder(positive_samples) # Use same positive samples
                  g_negative_features = self.encoder(negative_samples) # Use same negative samples
                  triplet_loss_g = self.triplet_loss(g_anchor_features, g_positive_features, g_negative_features)
                  
                  cycle_rank_loss = 0
                  if self.crl == True:
                    reconstructed_feature = self.g([noise_g, Fake_classification_g])
                    
                    # For cycle rank, the "negative" is a feature from a different class
                    # We can reuse the negative samples from the batch for simplicity
                    batch_size_np = train_labels_np.shape[0]
                    negative_attributes = []
                    for label in train_labels_np:
                        available_classes = [c for c in seen_class_indices if c != label and c in train_Y_by_class]
                        if available_classes:
                            neg_class = np.random.choice(available_classes)
                            negative_attributes.append(train_Y_by_class[neg_class][0])
                        else:
                            negative_attributes.append(train_y[0].numpy())
                    
                    negative_attributes = tf.constant(np.array(negative_attributes), dtype=tf.float32)
                    unsimilar_generated_feature = self.g([noise_g, negative_attributes])

                    cycle_rank_loss = self.cycle_rank_loss(Fake_feature_g, reconstructed_feature, unsimilar_generated_feature)
                           
                  total_loss = adversarial_loss + self.lambda_cla * classification_loss + self.lambda_triplet * triplet_loss_g + self.lambda_crl*cycle_rank_loss
                              
                grads_g = tape_g.gradient(total_loss, self.g.trainable_weights)
                self.g_optimizer.apply_gradients(zip(grads_g, self.g.trainable_weights))
                epoch_losses['g'].append(float(tf.reduce_mean(total_loss)))
                
                elapsed_time = datetime.datetime.now() - start_time
          
                if batch_count % 10 == 0:  # 每10个批次打印一次
                    print("[Epoch %d/%d][Batch %d][AE+C loss: %f][M loss: %f][D loss: %f][G loss %05f] time: %s" \
                         % (epoch, epochs,
                           batch_count,
                           np.mean(epoch_losses['ae_c'][-10:]), 
                           np.mean(epoch_losses['m'][-10:]),
                           np.mean(epoch_losses['d'][-10:]),
                           np.mean(epoch_losses['g'][-10:]),
                           elapsed_time))
        
            # 每个epoch结束后进行评估
            if epoch % 1 == 0:
                accuracy_lsvm, accuracy_nrf, accuracy_pnb, accuracy_mlp = feature_generation_and_diagnosis(
                    2000, testdata, test_attributelabel, self.autoencoder, self.g, self.c, test_class_indices)

                accuracy_list_1.append(accuracy_lsvm) 
                accuracy_list_2.append(accuracy_nrf) 
                accuracy_list_3.append(accuracy_pnb)
                accuracy_list_4.append(accuracy_mlp)

                print("[Epoch %d/%d] [Accuracy_lsvm: %f] [Accuracy_nrf: %f] [Accuracy_pnb: %f][Accuracy_mlp: %f]"\
                      %(epoch, epochs, max(accuracy_list_1), max(accuracy_list_2), max(accuracy_list_3), max(accuracy_list_4)))
                      
                if log_file:
                    log_message = (f"[Epoch {epoch}/{epochs}] "
                                   f"[Accuracy_lsvm: {max(accuracy_list_1):f}] "
                                   f"[Accuracy_nrf: {max(accuracy_list_2):f}] "
                                   f"[Accuracy_pnb: {max(accuracy_list_3):f}]"
                                   f"[Accuracy_mlp: {max(accuracy_list_4):f}]\n")
                    log_file.write(log_message)
                    log_file.flush()

        return {
            'best_accuracy': max(accuracy_list_4) if accuracy_list_4 else 0.0,
            'accuracy_lsvm': max(accuracy_list_1) if accuracy_list_1 else 0.0,
            'accuracy_nrf': max(accuracy_list_2) if accuracy_list_2 else 0.0,
            'accuracy_pnb': max(accuracy_list_3) if accuracy_list_3 else 0.0,
            'accuracy_mlp': max(accuracy_list_4) if accuracy_list_4 else 0.0,
        }


def run_optimized_triplet_experiment(target_group='E', epochs=500, batch_size=512):
    """运行优化版triplet实验"""
    
    # 分组配置
    GROUP_CONFIGS = {
        'A': [1, 6, 14],   # 测试类别: [1, 6, 14] 
        'B': [4, 7, 10],   # 测试类别: [4, 7, 10]
        'C': [8, 11, 12],  # 测试类别: [8, 11, 12]
        'D': [2, 3, 5],    # 测试类别: [2, 3, 5]
        'E': [9, 13, 15],  # 测试类别: [9, 13, 15]
    }
    
    results_dir = "结果"
    os.makedirs(results_dir, exist_ok=True)

    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    start_run_time = datetime.datetime.now(beijing_tz)
    log_filename_base = start_run_time.strftime("%Y%m%d%H%M") + f"_optimized_triplet_Group{target_group}.md"
    log_filename = os.path.join(results_dir, log_filename_base)

    print(f"开始运行优化版 Group {target_group} 实验，测试类别: {GROUP_CONFIGS[target_group]}")
    print(f"优化特性: 数据流水线 + 混合精度训练 + 大批处理 + GPU监控")
    print(f"训练开始，日志将被记录到: {log_filename}")

    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"# 优化版训练日志 (Triplet架构 - Group {target_group})\n\n")
        log_file.write(f"**开始时间**: {start_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**实验组别**: Group {target_group}\n")
        log_file.write(f"**测试类别**: {GROUP_CONFIGS[target_group]}\n")
        log_file.write(f"**优化特性**: 数据流水线优化 + 混合精度 + 增大批处理\n")
        log_file.write(f"**批处理大小**: {batch_size}\n")
        log_file.write(f"**难负例挖掘**: 启用 (50% hard + 50% random)\n\n")
        log_file.write("---\n\n")
        log_file.flush()
        
        gan = Zero_shot_Optimized(use_optimized_pipeline=True)
        results = gan.train_optimized(epochs=epochs, batch_size=batch_size, log_file=log_file, test_class_indices=GROUP_CONFIGS[target_group])

        end_run_time = datetime.datetime.now(beijing_tz)
        log_file.write("\n---\n\n")
        log_file.write(f"**结束时间**: {end_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write(f"**总耗时**: {end_run_time - start_run_time}\n")
        log_file.write(f"**最佳准确率**: {results['best_accuracy']:.4f}\n")

    print(f"优化版训练完成，日志已保存至: {log_filename}")
    print(f"最佳准确率: {results['best_accuracy']:.4f}")
    
    return results

if __name__ == '__main__':
    TARGET_GROUP = 'E'  # 可选: 'A', 'B', 'C', 'D', 'E'
    
    print(f"开始运行 Group {TARGET_GROUP} 的优化版Triplet实验")
    print("优化特性: 数据流水线 + 混合精度训练 + 大批处理 + GPU监控 + 难负例挖掘")
    results = run_optimized_triplet_experiment(target_group=TARGET_GROUP, epochs=500, batch_size=512)
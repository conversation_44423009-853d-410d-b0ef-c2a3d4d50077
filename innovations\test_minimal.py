#!/usr/bin/env python3\n\"\"\"\n最简版ASDCGAN测试 - 回到基础，保证稳定性\n\n策略：\n1. 只保留4个核心损失：adversarial + cycle + semantic + uncertainty\n2. 禁用所有复杂功能：域转换、属性损失、三元组损失等\n3. 使用保守的学习率和权重\n4. 先让准确率超过随机水平（>33%）\n\"\"\"\n\nimport os\nimport sys\nimport argparse\nfrom datetime import datetime\n\n# 添加项目路径\nsys.path.append('/home/<USER>/hmt/ACGAN-FG-main')\n\nfrom enhanced_asdcgan_trainer import EnhancedASDCGANTrainer\nimport torch\n\ndef main():\n    parser = argparse.ArgumentParser(description='最简版ASDCGAN测试')\n    parser.add_argument('--group', type=str, default='A', choices=['A', 'B', 'C', 'D', 'E'],\n                        help='数据分组 (默认: A)')\n    parser.add_argument('--epochs', type=int, default=30,\n                        help='训练轮次 (默认: 30)')\n    \n    args = parser.parse_args()\n    \n    print(\"🎯 最简版ASDCGAN测试 - 回到基础\")\n    print(\"=\" * 50)\n    print(f\"📊 数据分组: {args.group}\")\n    print(f\"🔄 训练轮次: {args.epochs}\")\n    print(f\"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n    print()\n    print(\"🎯 最简策略:\")\n    print(\"✅ 保留: adversarial + cycle + semantic + uncertainty\")\n    print(\"❌ 禁用: 所有复杂损失和功能\")\n    print(\"🎯 目标: 准确率 > 33% (超过随机水平)\")\n    print(\"=\" * 50)\n    \n    # 检查GPU\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    print(f\"🔧 使用设备: {device}\")\n    if torch.cuda.is_available():\n        print(f\"   GPU: {torch.cuda.get_device_name()}\")\n    print()\n    \n    try:\n        # 创建最简版训练器\n        trainer = EnhancedASDCGANTrainer(\n            device=device,\n            batch_size=32,\n            learning_rate_g=0.0001,  # 保守的学习率\n            learning_rate_d=0.0002\n        )\n        \n        # 🔥 最简配置：禁用所有复杂功能\n        trainer.use_domain_transfer = False           # 禁用域转换\n        trainer.use_attribute_classifier = False     # 禁用属性分类器\n        trainer.adaptive_grad_clip = False           # 禁用自适应梯度裁剪\n        trainer.use_triplet_attribute_loss = False   # 禁用三元组损失\n        trainer.enable_grad_clip = True              # 只保留基础梯度裁剪\n        trainer.max_grad_norm = 1.0                  # 保守的裁剪阈值\n        \n        # 🔥 最简权重：所有复杂损失权重设为0\n        trainer.attribute_consistency_weight = 0.0   # 禁用属性损失\n        trainer.domain_cycle_weight = 0.0            # 禁用域循环损失\n        trainer.semantic_similarity_weight = 0.0     # 禁用语义相似性损失\n        \n        # 保守的基础权重\n        trainer.adversarial_weight = 1.0\n        trainer.cycle_consistency_weight = 0.1        # 降低循环损失权重\n        trainer.semantic_distance_weight = 0.1       # 降低语义损失权重\n        trainer.uncertainty_weight = 0.1             # 降低不确定性损失权重\n        trainer.domain_selection_weight = 0.1        # 降低域选择损失权重\n        \n        print(\"🔧 最简配置:\")\n        print(f\"- 域转换: {trainer.use_domain_transfer}\")\n        print(f\"- 属性分类器: {trainer.use_attribute_classifier}\")\n        print(f\"- 自适应梯度裁剪: {trainer.adaptive_grad_clip}\")\n        print(f\"- 基础梯度裁剪: {trainer.enable_grad_clip} (阈值: {trainer.max_grad_norm})\")\n        print(f\"- 三元组损失: {trainer.use_triplet_attribute_loss}\")\n        print()\n        print(\"🔧 损失权重:\")\n        print(f\"- 对抗损失: {trainer.adversarial_weight}\")\n        print(f\"- 循环损失: {trainer.cycle_consistency_weight}\")\n        print(f\"- 语义损失: {trainer.semantic_distance_weight}\")\n        print(f\"- 不确定性损失: {trainer.uncertainty_weight}\")\n        print(f\"- 域选择损失: {trainer.domain_selection_weight}\")\n        print(f\"- 复杂损失: 全部为0\")\n        print()\n        \n        # 加载数据\n        data_info = trainer.load_data(split_group=args.group)\n        \n        print(\"🚀 开始最简版训练...\")\n        print(f\"💡 监控: tensorboard --logdir tensorboard\")\n        print(f\"🌐 访问: http://localhost:6006\")\n        print()\n        \n        # 开始训练\n        history = trainer.train_enhanced(epochs=args.epochs)\n        \n        print()\n        print(\"🎊 最简版ASDCGAN训练完成！\")\n        print(\"=\" * 50)\n        \n        # 分析结果\n        final_accuracy = history['accuracy'][-1]\n        best_accuracy = max(history['accuracy']) if history['accuracy'] else 0.0\n        \n        print(f\"📊 训练结果分析:\")\n        print(f\"   最终准确率: {final_accuracy:.2%}\")\n        print(f\"   最佳准确率: {best_accuracy:.2%}\")\n        print(f\"   是否超过随机水平: {'✅ 是' if best_accuracy > 0.35 else '❌ 否'}\")\n        print(f\"   最终G损失: {history['g_loss'][-1]:.4f}\")\n        print(f\"   最终D损失: {history['d_loss'][-1]:.4f}\")\n        \n        if 'best_accuracies' in trainer.__dict__:\n            print(f\"\\n🏆 最佳分类器准确率:\")\n            for name, acc in trainer.best_accuracies.items():\n                print(f\"   {name.upper()}: {acc*100:.2f}%\")\n        \n        # 训练稳定性分析\n        if len(history['g_loss']) > 5:\n            recent_g_losses = history['g_loss'][-5:]\n            g_loss_std = sum(recent_g_losses) / len(recent_g_losses)\n            print(f\"\\n📈 训练稳定性:\")\n            print(f\"   最后5轮平均G损失: {g_loss_std:.4f}\")\n            print(f\"   损失是否稳定: {'✅ 是' if g_loss_std < 100 else '❌ 否 (可能需要调整学习率)'}\")\n        \n        print(f\"\\n📁 实验结果:\")\n        print(f\"   实验目录: {trainer.experiment_dir}\")\n        print(f\"   训练历史: {trainer.current_run_dir}/training_history.json\")\n        \n        print(f\"\\n🔍 下一步建议:\")\n        if best_accuracy > 0.35:\n            print(\"   ✅ 基础版本工作正常，可以考虑添加简单改进\")\n            print(\"   💡 建议：逐步添加梯度裁剪优化、简化的属性损失\")\n        else:\n            print(\"   ❌ 基础版本需要进一步调试\")\n            print(\"   💡 建议：检查学习率、权重平衡、模型架构\")\n        \n    except Exception as e:\n        print(f\"❌ 训练过程中出现错误: {e}\")\n        import traceback\n        traceback.print_exc()\n        return 1\n    \n    return 0\n\nif __name__ == '__main__':\n    exit_code = main()\n    sys.exit(exit_code)
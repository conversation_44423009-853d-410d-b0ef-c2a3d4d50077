#!/usr/bin/env python3
"""
E组特殊性分析脚本
专门分析E组[9,13,15]为什么准确率偏低的原因
"""

import numpy as np
import pandas as pd
from sklearn.metrics import silhouette_score
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import seaborn as sns

class EGroupAnalyzer:
    def __init__(self, data_dir='/home/<USER>/hmt/ACGAN-FG-main/data/'):
        self.data_dir = data_dir
        self.attribute_matrix = None
        self.class_data = {}
        
        # 组别定义
        self.groups = {
            'A': [1, 6, 14],   # 最容易组 - 77.8%
            'B': [4, 7, 10],   # 最困难组 - 44.4%
            'C': [8, 11, 12],  # 中等组 - 47.8%
            'D': [2, 3, 5],    # 中等偏上组 - 63.9%
            'E': [9, 13, 15]   # 中等偏下组 - 47.6% (问题组)
        }
        
        # 已知的准确率数据
        self.group_accuracy = {
            'A': 77.8,
            'B': 44.4, 
            'C': 47.8,
            'D': 63.9,
            'E': 47.6  # 问题组
        }
        
        # 已知的语义相似度
        self.semantic_similarity = {
            'A': 0.282,
            'B': 0.216,
            'C': 0.368,
            'D': 0.209,
            'E': 0.000  # 零相似度！
        }

    def load_data(self):
        """加载数据"""
        print("📂 加载数据...")
        
        # 加载属性矩阵
        try:
            import pandas as pd
            df = pd.read_excel(f'{self.data_dir}/attribute_matrix.xlsx')
            self.attribute_matrix = df.iloc[:, 1:].values  # 跳过第一列（类别名）
            print(f"✅ 属性矩阵: {self.attribute_matrix.shape}")
        except Exception as e:
            print(f"❌ 加载属性矩阵失败: {e}")
            return False
            
        # 加载各类别数据
        for class_id in range(1, 16):
            try:
                train_data = np.loadtxt(f'{self.data_dir}/d{class_id:02d}.dat')
                test_data = np.loadtxt(f'{self.data_dir}/d{class_id:02d}_te.dat')
                self.class_data[class_id] = {
                    'train': train_data,
                    'test': test_data,
                    'all': np.vstack([train_data, test_data])
                }
                print(f"✅ 类别{class_id}: 训练{len(train_data)}, 测试{len(test_data)}")
            except Exception as e:
                print(f"❌ 加载类别{class_id}失败: {e}")
                
        return True

    def analyze_e_group_problems(self):
        """深度分析E组问题"""
        print("\n" + "="*60)
        print("🔍 E组深度问题分析")
        print("="*60)
        
        e_classes = self.groups['E']  # [9, 13, 15]
        
        print(f"\n📊 E组基本信息:")
        print(f"   类别: {e_classes}")
        print(f"   语义相似度: {self.semantic_similarity['E']} (零相似度)")
        print(f"   准确率: {self.group_accuracy['E']}% (偏低)")
        
        # 1. 属性空间分析
        self._analyze_attribute_space(e_classes)
        
        # 2. 特征分布分析
        self._analyze_feature_distribution(e_classes)
        
        # 3. 类间距离分析
        self._analyze_inter_class_distances(e_classes)
        
        # 4. 与其他组对比
        self._compare_with_other_groups(e_classes)

    def _analyze_attribute_space(self, e_classes):
        """分析E组在属性空间的特性"""
        print(f"\n🎯 E组属性空间分析:")
        print("-" * 40)
        
        if self.attribute_matrix is None:
            print("❌ 属性矩阵未加载")
            return
            
        # 获取E组属性
        e_attributes = self.attribute_matrix[np.array(e_classes) - 1]  # 转换为0-based索引
        
        print(f"E组属性矩阵形状: {e_attributes.shape}")
        
        # 计算类间相似度
        from sklearn.metrics.pairwise import cosine_similarity
        similarity_matrix = cosine_similarity(e_attributes)
        
        print(f"\nE组内部相似度矩阵:")
        for i, class_i in enumerate(e_classes):
            for j, class_j in enumerate(e_classes):
                if i < j:
                    sim = similarity_matrix[i, j]
                    print(f"   类别{class_i} vs 类别{class_j}: {sim:.3f}")
        
        # 平均相似度
        avg_similarity = np.mean(similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)])
        print(f"\nE组平均内部相似度: {avg_similarity:.3f}")
        
        # 属性差异分析
        print(f"\n属性差异分析:")
        for i in range(len(e_classes)):
            for j in range(i+1, len(e_classes)):
                diff = np.abs(e_attributes[i] - e_attributes[j])
                max_diff_idx = np.argmax(diff)
                print(f"   类别{e_classes[i]} vs {e_classes[j]}: 最大差异在属性{max_diff_idx+1} (差异={diff[max_diff_idx]:.3f})")

    def _analyze_feature_distribution(self, e_classes):
        """分析E组特征分布"""
        print(f"\n📈 E组特征分布分析:")
        print("-" * 40)
        
        # 收集E组所有数据
        e_features = []
        e_labels = []
        
        for class_id in e_classes:
            if class_id in self.class_data:
                features = self.class_data[class_id]['all']
                e_features.append(features)
                e_labels.extend([class_id] * len(features))
        
        if not e_features:
            print("❌ E组数据未加载")
            return
            
        X_e = np.vstack(e_features)
        y_e = np.array(e_labels)
        
        print(f"E组总样本数: {len(X_e)}")
        print(f"特征维度: {X_e.shape[1]}")
        
        # 计算轮廓系数
        if len(np.unique(y_e)) > 1:
            silhouette_avg = silhouette_score(X_e, y_e)
            print(f"E组轮廓系数: {silhouette_avg:.3f}")
            
            # 与其他组对比
            print(f"\n轮廓系数对比:")
            for group_name, classes in self.groups.items():
                if group_name == 'E':
                    continue
                group_features = []
                group_labels = []
                for class_id in classes:
                    if class_id in self.class_data:
                        features = self.class_data[class_id]['all']
                        group_features.append(features)
                        group_labels.extend([class_id] * len(features))
                
                if group_features:
                    X_group = np.vstack(group_features)
                    y_group = np.array(group_labels)
                    if len(np.unique(y_group)) > 1:
                        group_silhouette = silhouette_score(X_group, y_group)
                        print(f"   {group_name}组: {group_silhouette:.3f}")

    def _analyze_inter_class_distances(self, e_classes):
        """分析E组类间距离"""
        print(f"\n📏 E组类间距离分析:")
        print("-" * 40)
        
        # 计算各类别中心
        class_centers = {}
        for class_id in e_classes:
            if class_id in self.class_data:
                features = self.class_data[class_id]['all']
                class_centers[class_id] = np.mean(features, axis=0)
        
        # 计算类间距离
        from sklearn.metrics.pairwise import euclidean_distances
        
        print(f"E组类间欧氏距离:")
        for i, class_i in enumerate(e_classes):
            for j, class_j in enumerate(e_classes):
                if i < j and class_i in class_centers and class_j in class_centers:
                    dist = euclidean_distances([class_centers[class_i]], [class_centers[class_j]])[0, 0]
                    print(f"   类别{class_i} vs 类别{class_j}: {dist:.3f}")

    def _compare_with_other_groups(self, e_classes):
        """与其他组对比分析"""
        print(f"\n🔄 E组与其他组对比:")
        print("-" * 40)
        
        print(f"{'组别':<8} {'准确率':<10} {'语义相似度':<12} {'特征'}")
        print("-" * 50)
        
        for group_name, classes in self.groups.items():
            acc = self.group_accuracy[group_name]
            sim = self.semantic_similarity[group_name]
            
            if group_name == 'E':
                feature = "零相似度，分散类别"
            elif group_name == 'A':
                feature = "中等相似度，最高准确率"
            elif group_name == 'D':
                feature = "低相似度，高准确率"
            elif group_name == 'C':
                feature = "最高相似度，中等准确率"
            else:
                feature = "低相似度，低准确率"
                
            print(f"{group_name:<8} {acc:<10.1f}% {sim:<12.3f} {feature}")
        
        print(f"\n🎯 E组问题总结:")
        print("1. 零语义相似度 - 类别间完全不相关")
        print("2. 准确率偏低 - 47.6%，低于D组的63.9%")
        print("3. 特征学习困难 - 缺乏共同语义特征")
        print("4. 模型难以找到有效的判别模式")

def main():
    """主函数"""
    print("🚀 E组特殊性分析开始...")
    
    analyzer = EGroupAnalyzer()
    
    # 加载数据
    if not analyzer.load_data():
        print("❌ 数据加载失败")
        return
    
    # 分析E组问题
    analyzer.analyze_e_group_problems()
    
    print("\n" + "="*60)
    print("📋 E组改进建议")
    print("="*60)
    
    print("\n🎯 针对E组的特殊优化策略:")
    print("1. **增强特征分离**: 大幅提升Triplet Loss权重")
    print("2. **强化中心学习**: 提升Center Loss权重")
    print("3. **独立特征学习**: 为每个类别学习独特特征")
    print("4. **数据增强**: 增加E组类别的训练样本多样性")
    print("5. **架构调整**: 考虑为E组设计专门的分支网络")
    
    print("\n✅ E组分析完成!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
合成特征分析脚本
用于生成合成特征并与真实特征进行对比分析
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
import umap
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import euclidean_distances
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib
matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'Arial Unicode MS', 'SimHei', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
# 如果有中文字体可用，优先使用
try:
    import matplotlib.font_manager as fm
    # 尝试找到系统中的中文字体
    chinese_fonts = [f.name for f in fm.fontManager.ttflist if 'SimHei' in f.name or 'Microsoft YaHei' in f.name or 'WenQuanYi' in f.name]
    if chinese_fonts:
        plt.rcParams['font.sans-serif'] = chinese_fonts + ['DejaVu Sans']
    else:
        # 如果没有中文字体，使用英文标签
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
except:
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']

class SyntheticAnalysis:
    def __init__(self, data_dir='/home/<USER>/hmt/ACGAN-FG-main/data/'):
        self.data_dir = data_dir
        self.groups = {
            'A': [1, 6, 14],   # 最容易组
            'B': [4, 7, 10],   # 最困难组
            'C': [8, 11, 12],  # 中等组
            'D': [2, 3, 5],    # 中等偏上组
            'E': [9, 13, 15]   # 中等偏下组
        }
        self.class_data = {}
        self.test_data = {}
        
    def load_data(self):
        """加载数据"""
        print("📂 加载数据...")
        for class_id in range(1, 16):
            try:
                # 加载训练数据
                train_file = f'{self.data_dir}/d{class_id:02d}.dat'
                train_data = np.loadtxt(train_file)
                self.class_data[class_id] = train_data
                
                # 加载测试数据
                test_file = f'{self.data_dir}/d{class_id:02d}_te.dat'
                test_data = np.loadtxt(test_file)
                self.test_data[class_id] = test_data
                
            except Exception as e:
                print(f"❌ 无法加载类别 {class_id} 的数据: {e}")
    
    def generate_synthetic_features(self, class_id, num_samples=100, method='gaussian'):
        """生成合成特征"""
        if class_id not in self.class_data:
            return None
            
        real_features = self.class_data[class_id]
        
        if method == 'gaussian':
            # 基于高斯分布的简单生成
            mean = np.mean(real_features, axis=0)
            cov = np.cov(real_features.T)
            synthetic = np.random.multivariate_normal(mean, cov, num_samples)
            
        elif method == 'noise':
            # 添加噪声的方法
            indices = np.random.choice(len(real_features), num_samples, replace=True)
            base_samples = real_features[indices]
            noise = np.random.normal(0, 0.1 * np.std(real_features, axis=0), base_samples.shape)
            synthetic = base_samples + noise
            
        elif method == 'interpolation':
            # 插值方法
            synthetic = []
            for _ in range(num_samples):
                idx1, idx2 = np.random.choice(len(real_features), 2, replace=False)
                alpha = np.random.random()
                interpolated = alpha * real_features[idx1] + (1 - alpha) * real_features[idx2]
                synthetic.append(interpolated)
            synthetic = np.array(synthetic)
            
        return synthetic
    
    def compare_real_vs_synthetic(self, group_name, method='gaussian'):
        """对比真实特征与合成特征"""
        print(f"\n🔍 分析组{group_name}的真实vs合成特征对比")
        
        classes = self.groups[group_name]
        
        # 收集数据
        all_features = []
        all_labels = []
        data_types = []
        
        # 训练集数据（背景）
        train_classes = [i for i in range(1, 16) if i not in classes]
        for class_id in train_classes[:5]:  # 只取前5个类别作为背景
            if class_id in self.class_data:
                features = self.class_data[class_id][:50]  # 限制样本数
                all_features.append(features)
                all_labels.extend([class_id] * len(features))
                data_types.extend(['background'] * len(features))
        
        # 测试集真实特征
        for class_id in classes:
            if class_id in self.test_data:
                features = self.test_data[class_id]
                all_features.append(features)
                all_labels.extend([class_id] * len(features))
                data_types.extend(['real'] * len(features))
        
        # 生成合成特征
        for class_id in classes:
            synthetic = self.generate_synthetic_features(class_id, num_samples=50, method=method)
            if synthetic is not None:
                all_features.append(synthetic)
                all_labels.extend([class_id] * len(synthetic))
                data_types.extend(['synthetic'] * len(synthetic))
        
        if not all_features:
            print(f"❌ 组{group_name}没有可用数据")
            return
        
        # 合并数据
        X = np.vstack(all_features)
        y = np.array(all_labels)
        types = np.array(data_types)
        
        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 可视化
        self._visualize_comparison(group_name, X_scaled, y, types, classes, method)
        
        # 统计分析
        self._statistical_comparison(group_name, X_scaled, y, types, classes)
    
    def _visualize_comparison(self, group_name, X_scaled, y, types, classes, method):
        """可视化对比结果"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'组{group_name} 真实vs合成特征对比 ({method}方法)', fontsize=16, fontweight='bold')
        
        # t-SNE降维
        print(f"  🔄 执行t-SNE降维...")
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(X_scaled)//4))
        X_tsne = tsne.fit_transform(X_scaled)
        
        # UMAP降维
        print(f"  🔄 执行UMAP降维...")
        umap_reducer = umap.UMAP(n_components=2, random_state=42)
        X_umap = umap_reducer.fit_transform(X_scaled)
        
        methods_data = [('t-SNE', X_tsne), ('UMAP', X_umap)]
        
        for idx, (method_name, X_reduced) in enumerate(methods_data):
            ax = axes[idx, 0]
            
            # 绘制背景数据
            bg_mask = types == 'background'
            if np.any(bg_mask):
                ax.scatter(X_reduced[bg_mask, 0], X_reduced[bg_mask, 1], 
                          c='lightgray', alpha=0.3, s=10, label='背景类别')
            
            # 绘制真实和合成特征
            colors = ['red', 'blue', 'green']
            markers = ['o', '^']  # 圆圈表示真实，三角表示合成
            
            for i, class_id in enumerate(classes):
                # 真实特征
                real_mask = (y == class_id) & (types == 'real')
                if np.any(real_mask):
                    ax.scatter(X_reduced[real_mask, 0], X_reduced[real_mask, 1],
                              c=colors[i], alpha=0.8, s=60, marker='o',
                              label=f'类别{class_id}(真实)', edgecolors='black', linewidth=1)
                
                # 合成特征
                syn_mask = (y == class_id) & (types == 'synthetic')
                if np.any(syn_mask):
                    ax.scatter(X_reduced[syn_mask, 0], X_reduced[syn_mask, 1],
                              c=colors[i], alpha=0.6, s=40, marker='^',
                              label=f'类别{class_id}(合成)', edgecolors='black', linewidth=1)
            
            ax.set_title(f'{method_name} 降维结果', fontsize=12, fontweight='bold')
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax.grid(True, alpha=0.3)
        
        # 距离分析
        ax = axes[0, 1]
        self._plot_distance_analysis(ax, X_scaled, y, types, classes)
        
        # 分布对比
        ax = axes[1, 1]
        self._plot_distribution_comparison(ax, X_scaled, y, types, classes)
        
        plt.tight_layout()
        plt.savefig(f'/home/<USER>/hmt/ACGAN-FG-main/synthetic_comparison_group_{group_name}_{method}.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def _plot_distance_analysis(self, ax, X_scaled, y, types, classes):
        """绘制距离分析"""
        distances_real = []
        distances_synthetic = []
        
        for class_id in classes:
            real_mask = (y == class_id) & (types == 'real')
            syn_mask = (y == class_id) & (types == 'synthetic')
            
            if np.any(real_mask) and np.any(syn_mask):
                real_features = X_scaled[real_mask]
                syn_features = X_scaled[syn_mask]
                
                # 计算真实特征的中心
                real_center = np.mean(real_features, axis=0)
                
                # 计算合成特征到真实中心的距离
                for syn_feature in syn_features:
                    dist = np.linalg.norm(syn_feature - real_center)
                    distances_synthetic.append(dist)
                
                # 计算真实特征到中心的距离
                for real_feature in real_features:
                    dist = np.linalg.norm(real_feature - real_center)
                    distances_real.append(dist)
        
        # 绘制距离分布
        if distances_real and distances_synthetic:
            ax.hist(distances_real, bins=20, alpha=0.7, label='真实特征', color='blue', density=True)
            ax.hist(distances_synthetic, bins=20, alpha=0.7, label='合成特征', color='red', density=True)
            ax.set_xlabel('到类别中心的距离')
            ax.set_ylabel('密度')
            ax.set_title('特征分布距离对比', fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=ax.transAxes)
    
    def _plot_distribution_comparison(self, ax, X_scaled, y, types, classes):
        """绘制分布对比"""
        # 计算每个类别的统计信息
        stats_data = []
        
        for class_id in classes:
            real_mask = (y == class_id) & (types == 'real')
            syn_mask = (y == class_id) & (types == 'synthetic')
            
            if np.any(real_mask):
                real_features = X_scaled[real_mask]
                real_std = np.mean(np.std(real_features, axis=0))
                stats_data.append(['真实', f'类别{class_id}', real_std])
            
            if np.any(syn_mask):
                syn_features = X_scaled[syn_mask]
                syn_std = np.mean(np.std(syn_features, axis=0))
                stats_data.append(['合成', f'类别{class_id}', syn_std])
        
        if stats_data:
            df = pd.DataFrame(stats_data, columns=['类型', '类别', '标准差'])
            sns.barplot(data=df, x='类别', y='标准差', hue='类型', ax=ax)
            ax.set_title('特征标准差对比', fontweight='bold')
            ax.tick_params(axis='x', rotation=45)
        else:
            ax.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=ax.transAxes)
    
    def _statistical_comparison(self, group_name, X_scaled, y, types, classes):
        """统计对比分析"""
        print(f"\n📊 组{group_name}统计分析结果:")
        print("-" * 40)
        
        from scipy.stats import ks_2samp
        
        for class_id in classes:
            real_mask = (y == class_id) & (types == 'real')
            syn_mask = (y == class_id) & (types == 'synthetic')
            
            if np.any(real_mask) and np.any(syn_mask):
                real_features = X_scaled[real_mask]
                syn_features = X_scaled[syn_mask]
                
                # KS检验
                ks_stats = []
                for dim in range(min(5, X_scaled.shape[1])):  # 只检验前5个维度
                    ks_stat, p_value = ks_2samp(real_features[:, dim], syn_features[:, dim])
                    ks_stats.append(ks_stat)
                
                avg_ks = np.mean(ks_stats)
                
                # 均值和方差对比
                real_mean = np.mean(real_features, axis=0)
                syn_mean = np.mean(syn_features, axis=0)
                mean_diff = np.mean(np.abs(real_mean - syn_mean))
                
                real_std = np.std(real_features, axis=0)
                syn_std = np.std(syn_features, axis=0)
                std_diff = np.mean(np.abs(real_std - syn_std))
                
                print(f"类别{class_id}:")
                print(f"  📈 平均KS统计量: {avg_ks:.3f}")
                print(f"  📊 均值差异: {mean_diff:.3f}")
                print(f"  📏 标准差差异: {std_diff:.3f}")

def main():
    """主函数"""
    print("🚀 开始合成特征分析...")
    
    # 创建分析实例
    analyzer = SyntheticAnalysis()
    
    # 加载数据
    analyzer.load_data()
    
    # 分析不同组的合成特征质量
    methods = ['gaussian', 'noise', 'interpolation']
    focus_groups = ['A', 'B']  # 重点分析最容易和最困难的组
    
    for group in focus_groups:
        for method in methods:
            print(f"\n{'='*60}")
            print(f"分析组{group}使用{method}方法")
            print('='*60)
            analyzer.compare_real_vs_synthetic(group, method)
    
    print("\n✅ 合成特征分析完成!")
    print("📊 生成的文件:")
    for group in focus_groups:
        for method in methods:
            print(f"  - synthetic_comparison_group_{group}_{method}.png")

if __name__ == "__main__":
    import pandas as pd
    main()

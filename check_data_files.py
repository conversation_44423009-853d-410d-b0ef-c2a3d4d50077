#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 数据文件检查脚本
验证ACGAN-FG所需的数据文件是否存在
"""

import os
import numpy as np

def check_data_files():
    """检查数据文件是否存在并显示基本信息"""
    
    print("🔍 检查ACGAN-FG数据文件...")
    print("=" * 50)
    
    # 检查数据文件
    train_file = './data/dataset_train_case1.npz'
    test_file = './data/dataset_test_case1.npz'
    
    files_to_check = [
        ('训练数据', train_file),
        ('测试数据', test_file)
    ]
    
    all_files_exist = True
    
    for name, filepath in files_to_check:
        if os.path.exists(filepath):
            print(f"✅ {name}: {filepath}")
            
            # 显示文件大小
            size_mb = os.path.getsize(filepath) / (1024 * 1024)
            print(f"   文件大小: {size_mb:.2f} MB")
            
            # 尝试加载并显示基本信息
            try:
                data = np.load(filepath)
                print(f"   包含的键: {list(data.keys())[:5]}{'...' if len(data.keys()) > 5 else ''}")
                print(f"   总键数量: {len(data.keys())}")
                
                # 检查是否包含所有15个类别的数据
                train_classes = []
                test_classes = []
                
                for i in range(1, 16):
                    if 'training_samples_' in filepath:
                        if f'training_samples_{i}' in data.keys():
                            train_classes.append(i)
                    else:
                        if f'testing_samples_{i}' in data.keys():
                            test_classes.append(i)
                
                if 'training_samples_' in filepath:
                    print(f"   训练类别: {train_classes}")
                else:
                    print(f"   测试类别: {test_classes}")
                    
            except Exception as e:
                print(f"   ⚠️ 读取文件时出错: {e}")
                
        else:
            print(f"❌ {name}: {filepath} (文件不存在)")
            all_files_exist = False
    
    print("=" * 50)
    
    if all_files_exist:
        print("✅ 所有数据文件检查通过！")
        print("🚀 可以开始运行ACGAN-FG训练")
        
        # 显示运行命令
        print("\n📋 运行命令:")
        print("   python3 scripts/ACGAN_FG_refactored.py")
        print("   python3 run_acgan_group.py A  # A组")
        print("   python3 run_acgan_group.py E  # E组（默认）")
        
    else:
        print("❌ 数据文件检查失败！")
        print("📁 请确保在项目根目录运行此脚本")
        print("📁 当前工作目录:", os.getcwd())
        
        # 检查是否在错误的目录
        if os.path.exists('./scripts/ACGAN_FG_refactored.py'):
            print("✅ 检测到scripts目录，您在正确的项目根目录")
        else:
            print("❌ 未检测到scripts目录，请切换到项目根目录")
            
    return all_files_exist

if __name__ == '__main__':
    check_data_files()

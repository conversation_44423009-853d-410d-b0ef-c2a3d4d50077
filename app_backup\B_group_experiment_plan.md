
# B组损失函数权重再平衡实验计划

## 实验概述
- **目标**: 提升B组困难类别 [4, 7, 10] 的分类性能
- **基线**: 平均准确率 44.38%
- **期望**: 提升至 50%+ (提升5个百分点以上)

## 实验设计

### 实验1: B-HardTriplet (蛮力方法)
- **策略**: 大幅增强Triplet和Center损失权重
- **参数调整**:
  - lambda_triplet: 10 → 50 (5倍增强)
  - lambda_center: 0.5 → 2.5 (5倍增强)
- **假设**: 更强的约束能在拥挤空间中强制分离
- **风险**: 可能导致训练不稳定

### 实验2: B-SmartCRL (智能方法)
- **策略**: 强化语义自洽性约束
- **参数调整**:
  - lambda_crl: 0.01 → 0.1 (10倍增强)
  - 新增语义一致性约束
- **假设**: 语义约束能提高特征区分度
- **风险**: 语义约束可能与特征学习冲突

## 评估指标
1. **分类准确率**: 四种分类器的平均性能
2. **收敛稳定性**: 损失函数收敛情况
3. **特征质量**: t-SNE可视化分析
4. **训练效率**: 达到目标性能所需时间

## 成功标准
- **最低标准**: 平均准确率提升 > 3个百分点
- **良好标准**: 平均准确率提升 > 5个百分点  
- **优秀标准**: 平均准确率提升 > 10个百分点

## 后续计划
基于实验结果，考虑以下改进方向:
1. **组合策略**: 结合两种方法的优点
2. **自适应权重**: 动态调整损失权重
3. **课程学习**: 从易到难的训练策略
4. **注意力机制**: 聚焦判别性特征

---
生成时间: 2025-07-14 14:25:26

#!/bin/bash

# 批量运行所有组别的ACGAN-FG基线训练
# 依次运行A、B、C、D、E组，每组2000轮次

set -e  # 遇到错误时退出

# 配置参数
EPOCHS=2000
BATCH_SIZE=120
GROUPS=("A" "B" "C" "D" "E")

# 创建日志目录
LOG_DIR="logs/batch_training"
mkdir -p "$LOG_DIR"

# 生成时间戳
TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
BATCH_LOG="$LOG_DIR/batch_training_$TIMESTAMP.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$BATCH_LOG"
}

# 开始批量训练
log "🚀 开始批量训练所有组别的ACGAN-FG基线模型"
log "============================================================"
log "📋 训练计划:"
log "   - 组别: A, B, C, D, E"
log "   - 每组轮次: $EPOCHS epochs"
log "   - 批次大小: $BATCH_SIZE"
log "   - 预计总时间: 10-15小时"
log "============================================================"

# 检查脚本是否存在
if [ ! -f "run_enhanced_baseline.py" ]; then
    log "❌ 找不到 run_enhanced_baseline.py 文件!"
    log "请确保在正确的目录下运行此脚本"
    exit 1
fi

# 记录开始时间
START_TIME=$(date +%s)
SUCCESSFUL_GROUPS=0
FAILED_GROUPS=0

# 遍历所有组别
for i in "${!GROUPS[@]}"; do
    GROUP="${GROUPS[$i]}"
    GROUP_NUM=$((i + 1))
    
    log ""
    log "========================================"
    log "📍 进度: $GROUP_NUM/5 - 开始训练 Group $GROUP"
    log "========================================"
    
    # 记录组别开始时间
    GROUP_START_TIME=$(date +%s)
    
    # 运行训练
    log "🚀 执行命令: python run_enhanced_baseline.py --group $GROUP --epochs $EPOCHS --batch_size $BATCH_SIZE"
    
    if python run_enhanced_baseline.py --group "$GROUP" --epochs "$EPOCHS" --batch_size "$BATCH_SIZE" 2>&1 | tee -a "$BATCH_LOG"; then
        # 训练成功
        GROUP_END_TIME=$(date +%s)
        GROUP_DURATION=$((GROUP_END_TIME - GROUP_START_TIME))
        GROUP_HOURS=$(echo "scale=2; $GROUP_DURATION / 3600" | bc -l)
        
        log "✅ Group $GROUP 训练成功完成!"
        log "⏱️  训练时间: ${GROUP_HOURS} 小时"
        
        SUCCESSFUL_GROUPS=$((SUCCESSFUL_GROUPS + 1))
        
        # 尝试提取最佳准确率（从最新的日志文件）
        LATEST_LOG=$(find logs/baseline -name "Group-${GROUP}_*" -type d | sort | tail -1)
        if [ -d "$LATEST_LOG" ] && [ -f "$LATEST_LOG/training.log" ]; then
            BEST_ACC=$(grep "Best MLP Acc:" "$LATEST_LOG/training.log" | tail -1 | sed 's/.*Best MLP Acc: \([0-9.]*\).*/\1/')
            if [ -n "$BEST_ACC" ]; then
                log "📊 Group $GROUP 最佳准确率: $BEST_ACC"
            fi
        fi
        
    else
        # 训练失败
        GROUP_END_TIME=$(date +%s)
        GROUP_DURATION=$((GROUP_END_TIME - GROUP_START_TIME))
        GROUP_HOURS=$(echo "scale=2; $GROUP_DURATION / 3600" | bc -l)
        
        log "❌ Group $GROUP 训练失败!"
        log "⏱️  失败前运行时间: ${GROUP_HOURS} 小时"
        
        FAILED_GROUPS=$((FAILED_GROUPS + 1))
        
        # 询问是否继续
        log "⚠️  Group $GROUP 训练失败，是否继续下一个组别？"
        log "提示: 脚本将在10秒后自动继续，按Ctrl+C可中断"
        
        # 等待10秒，允许用户中断
        for countdown in {10..1}; do
            echo -n "继续倒计时: $countdown 秒... " | tee -a "$BATCH_LOG"
            sleep 1
            echo "" | tee -a "$BATCH_LOG"
        done
        
        log "继续下一个组别的训练..."
    fi
    
    # 显示进度和预计剩余时间
    if [ $GROUP_NUM -lt 5 ]; then
        CURRENT_TIME=$(date +%s)
        ELAPSED_TIME=$((CURRENT_TIME - START_TIME))
        AVG_TIME_PER_GROUP=$((ELAPSED_TIME / GROUP_NUM))
        REMAINING_GROUPS=$((5 - GROUP_NUM))
        ESTIMATED_REMAINING=$((AVG_TIME_PER_GROUP * REMAINING_GROUPS))
        ESTIMATED_HOURS=$(echo "scale=1; $ESTIMATED_REMAINING / 3600" | bc -l)
        
        log "⏱️  预计剩余时间: ${ESTIMATED_HOURS} 小时"
    fi
done

# 计算总时间
END_TIME=$(date +%s)
TOTAL_DURATION=$((END_TIME - START_TIME))
TOTAL_HOURS=$(echo "scale=2; $TOTAL_DURATION / 3600" | bc -l)

# 输出最终总结
log ""
log "============================================================"
log "🎯 批量训练结果总结"
log "============================================================"
log "📊 总体统计:"
log "   总训练时间: ${TOTAL_HOURS} 小时"
log "   成功组别: $SUCCESSFUL_GROUPS/5"
log "   失败组别: $FAILED_GROUPS/5"
log ""

# 列出所有结果
log "📋 详细结果:"
for GROUP in "${GROUPS[@]}"; do
    LATEST_LOG=$(find logs/baseline -name "Group-${GROUP}_*" -type d | sort | tail -1)
    if [ -d "$LATEST_LOG" ] && [ -f "$LATEST_LOG/training.log" ]; then
        BEST_ACC=$(grep "Best MLP Acc:" "$LATEST_LOG/training.log" | tail -1 | sed 's/.*Best MLP Acc: \([0-9.]*\).*/\1/')
        if [ -n "$BEST_ACC" ]; then
            log "   Group $GROUP: ✅ 最佳准确率 $BEST_ACC"
        else
            log "   Group $GROUP: ⚠️  训练完成但无法提取准确率"
        fi
    else
        log "   Group $GROUP: ❌ 训练失败或日志缺失"
    fi
done

log ""
log "💾 详细日志保存在: $BATCH_LOG"
log "📁 TensorBoard日志目录: logs/baseline/"
log ""

if [ $SUCCESSFUL_GROUPS -eq 5 ]; then
    log "🎉 所有组别训练成功完成!"
    exit 0
elif [ $SUCCESSFUL_GROUPS -gt 0 ]; then
    log "⚠️  部分组别训练成功完成 ($SUCCESSFUL_GROUPS/5)"
    exit 0
else
    log "❌ 所有组别训练都失败了"
    exit 1
fi

#!/usr/bin/env python3
"""
🎯 最小可行版本 - 基于您之前60.97%的成功基础

策略：
1. 使用最简单的GAN架构
2. 只保留最核心的功能
3. 添加最基本的改进
4. 确保能稳定运行

目标：
- 先达到之前的60.97%基准
- 再逐步改进到70%+
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from datetime import datetime
import logging
from sklearn.svm import LinearSVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main/innovations')

# 导入数据加载函数
from load_npz_data import load_tep_data_from_npz


class SimpleGenerator(nn.Module):
    """最简单的生成器"""
    def __init__(self, noise_dim=50, attr_dim=20, feature_dim=52):
        super().__init__()
        
        self.model = nn.Sequential(
            nn.Linear(noise_dim + attr_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Linear(256, feature_dim),
            nn.Tanh()
        )
    
    def forward(self, noise, attributes):
        x = torch.cat([noise, attributes], dim=1)
        return self.model(x)


class SimpleDiscriminator(nn.Module):
    """最简单的判别器"""
    def __init__(self, feature_dim=52, attr_dim=20):
        super().__init__()
        
        self.model = nn.Sequential(
            nn.Linear(feature_dim + attr_dim, 256),
            nn.LeakyReLU(0.2),
            nn.Linear(256, 128),
            nn.LeakyReLU(0.2),
            nn.Linear(128, 1)
        )
    
    def forward(self, features, attributes):
        x = torch.cat([features, attributes], dim=1)
        return self.model(x)


class MinimalTrainer:
    """最小可行训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
        # 初始化模型
        self.generator = SimpleGenerator().to(device)
        self.discriminator = SimpleDiscriminator().to(device)
        
        # 优化器
        self.optimizer_g = optim.Adam(self.generator.parameters(), lr=0.0002, betas=(0.5, 0.999))
        self.optimizer_d = optim.Adam(self.discriminator.parameters(), lr=0.0002, betas=(0.5, 0.999))
        
        # 损失函数
        self.criterion = nn.BCEWithLogitsLoss()
        self.mse_loss = nn.MSELoss()
        
        # 训练历史
        self.history = {'g_loss': [], 'd_loss': [], 'accuracy': []}
        
        print("✅ 最小可行训练器初始化完成")
        print(f"   生成器参数: {sum(p.numel() for p in self.generator.parameters()):,}")
        print(f"   判别器参数: {sum(p.numel() for p in self.discriminator.parameters()):,}")
    
    def load_data(self, group='A'):
        """加载数据"""
        print(f"📊 加载TEP数据集 (分组 {group})...")
        
        # 定义测试类别
        test_classes_map = {
            'A': [1, 6, 14],
            'B': [4, 7, 10], 
            'C': [8, 11, 12],
            'D': [2, 3, 5],
            'E': [9, 13, 15]
        }
        
        test_classes = test_classes_map[group]
        
        # 切换到数据目录
        original_cwd = os.getcwd()
        os.chdir('/home/<USER>/hmt/ACGAN-FG-main')
        
        try:
            (traindata, trainlabel, train_attributelabel,
             testdata, testlabel, test_attributelabel,
             test_attribute_matrix, train_attribute_matrix) = load_tep_data_from_npz(test_classes)
        finally:
            os.chdir(original_cwd)
        
        # 转换为tensor
        self.train_features = torch.FloatTensor(traindata).to(self.device)
        self.train_attributes = torch.FloatTensor(train_attributelabel).to(self.device)
        self.test_features = torch.FloatTensor(testdata).to(self.device)
        self.test_attributes = torch.FloatTensor(test_attributelabel).to(self.device)
        self.test_labels = testlabel
        
        print(f"✅ 数据加载完成")
        print(f"   训练样本: {self.train_features.shape[0]}")
        print(f"   测试样本: {self.test_features.shape[0]}")
        print(f"   特征维度: {self.train_features.shape[1]}")
        print(f"   属性维度: {self.train_attributes.shape[1]}")
    
    def train_step(self, batch_size=32):
        """单步训练"""
        # 随机采样
        indices = torch.randperm(self.train_features.shape[0])[:batch_size]
        real_features = self.train_features[indices]
        real_attributes = self.train_attributes[indices]
        
        # ==================== 训练判别器 ====================
        self.optimizer_d.zero_grad()
        
        # 真实样本
        real_output = self.discriminator(real_features, real_attributes)
        real_labels = torch.ones_like(real_output)
        d_loss_real = self.criterion(real_output, real_labels)
        
        # 生成假样本
        noise = torch.randn(batch_size, 50).to(self.device)
        fake_features = self.generator(noise, real_attributes)
        fake_output = self.discriminator(fake_features.detach(), real_attributes)
        fake_labels = torch.zeros_like(fake_output)
        d_loss_fake = self.criterion(fake_output, fake_labels)
        
        d_loss = (d_loss_real + d_loss_fake) / 2
        d_loss.backward()
        
        # 🔥 简单梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), 1.0)
        
        self.optimizer_d.step()
        
        # ==================== 训练生成器 ====================
        self.optimizer_g.zero_grad()
        
        # 生成器损失
        fake_output = self.discriminator(fake_features, real_attributes)
        g_loss = self.criterion(fake_output, torch.ones_like(fake_output))
        
        g_loss.backward()
        
        # 🔥 简单梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.generator.parameters(), 1.0)
        
        self.optimizer_g.step()
        
        return g_loss.item(), d_loss.item()
    
    def evaluate(self):
        """评估模型"""
        self.generator.eval()
        
        # 为每个测试类别生成样本
        unique_labels = np.unique(self.test_labels)
        generated_features = []
        generated_labels = []
        
        with torch.no_grad():
            for label in unique_labels:
                # 获取该类别的属性
                mask = self.test_labels == label
                if np.sum(mask) > 0:
                    attr = self.test_attributes[mask][0:1]  # 取第一个样本的属性
                    
                    # 生成1000个样本
                    for _ in range(10):  # 分批生成
                        noise = torch.randn(100, 50).to(self.device)
                        attr_batch = attr.repeat(100, 1)
                        fake_features = self.generator(noise, attr_batch)
                        
                        generated_features.append(fake_features.cpu().numpy())
                        generated_labels.extend([label] * 100)
        
        if not generated_features:
            return 0.0
        
        # 合并生成的特征
        generated_features = np.vstack(generated_features)
        generated_labels = np.array(generated_labels)
        
        # 使用不同分类器评估
        classifiers = {
            'LinearSVM': LinearSVC(random_state=42, max_iter=1000),
            'RandomForest': RandomForestClassifier(random_state=42, n_estimators=100),
            'GaussianNB': GaussianNB(),
            'MLPClassifier': MLPClassifier(random_state=42, max_iter=500)
        }
        
        accuracies = {}
        for name, clf in classifiers.items():
            try:
                clf.fit(generated_features, generated_labels)
                pred = clf.predict(self.test_features.cpu().numpy())
                acc = accuracy_score(self.test_labels, pred)
                accuracies[name] = acc * 100
            except:
                accuracies[name] = 0.0
        
        self.generator.train()
        return max(accuracies.values())
    
    def train(self, epochs=100, batch_size=32):
        """训练模型"""
        print(f"🚀 开始训练 ({epochs} epochs)")
        
        best_accuracy = 0.0
        
        for epoch in range(epochs):
            # 训练一个epoch
            epoch_g_loss = 0.0
            epoch_d_loss = 0.0
            num_batches = len(self.train_features) // batch_size
            
            for _ in range(num_batches):
                g_loss, d_loss = self.train_step(batch_size)
                epoch_g_loss += g_loss
                epoch_d_loss += d_loss
            
            epoch_g_loss /= num_batches
            epoch_d_loss /= num_batches
            
            # 每10个epoch评估一次
            if (epoch + 1) % 10 == 0:
                accuracy = self.evaluate()
                best_accuracy = max(best_accuracy, accuracy)
                
                print(f"Epoch {epoch+1}/{epochs}: G_loss={epoch_g_loss:.4f}, D_loss={epoch_d_loss:.4f}, Acc={accuracy:.2f}%, Best={best_accuracy:.2f}%")
                
                # 保存历史
                self.history['g_loss'].append(epoch_g_loss)
                self.history['d_loss'].append(epoch_d_loss)
                self.history['accuracy'].append(accuracy)
        
        return best_accuracy


def main():
    print(f"""
🎯 最小可行版本测试
=====================================
⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 策略:
✅ 最简单的GAN架构
✅ 基本的梯度裁剪
✅ 标准的对抗训练
🎯 目标: 先达到60.97%基准
=====================================
    """)
    
    try:
        # 初始化训练器
        trainer = MinimalTrainer(device='cuda')
        
        # 加载数据
        trainer.load_data(group='A')
        
        # 开始训练
        best_accuracy = trainer.train(epochs=100, batch_size=32)
        
        print(f"""
🎉 训练完成！
=====================================
📊 最终结果:
- 最佳准确率: {best_accuracy:.2f}%
- 与之前对比: 60.97% → {best_accuracy:.2f}%
- 状态: {'✅ 达到基准' if best_accuracy > 60 else '🔧 需要改进'}

💡 分析:
{'- 基础架构工作正常，可以进一步优化' if best_accuracy > 50 else '- 需要检查基础实现'}
=====================================
        """)
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

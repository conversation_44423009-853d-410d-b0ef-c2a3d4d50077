#!/usr/bin/env python3
"""
调试脚本 - 检查模块导入和基础功能
"""

import os
import sys

print("🔍 开始调试...")
print(f"当前工作目录: {os.getcwd()}")
print(f"Python路径: {sys.executable}")

# 添加项目路径
sys.path.append('/home/<USER>/hmt/ACGAN-FG-main')
print("✅ 添加项目路径完成")

try:
    print("📦 尝试导入torch...")
    import torch
    print(f"✅ torch导入成功，版本: {torch.__version__}")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"   GPU: {torch.cuda.get_device_name()}")
except Exception as e:
    print(f"❌ torch导入失败: {e}")
    exit(1)

try:
    print("📦 尝试导入数据加载器...")
    from load_npz_data import load_tep_data_from_npz
    print("✅ 数据加载器导入成功")
except Exception as e:
    print(f"❌ 数据加载器导入失败: {e}")
    exit(1)

try:
    print("📦 尝试导入ASDCGAN训练器...")
    from enhanced_asdcgan_trainer import EnhancedASDCGANTrainer
    print("✅ ASDCGAN训练器导入成功")
except Exception as e:
    print(f"❌ ASDCGAN训练器导入失败: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

try:
    print("🏗️ 尝试创建训练器实例...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    trainer = EnhancedASDCGANTrainer(
        device=device,
        batch_size=32,
        learning_rate_g=0.0001,
        learning_rate_d=0.0002
    )
    print("✅ 训练器实例创建成功")
except Exception as e:
    print(f"❌ 训练器实例创建失败: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

try:
    print("📊 尝试加载数据...")
    data_info = trainer.load_data(split_group='A')
    print(f"✅ 数据加载成功: {data_info}")
except Exception as e:
    print(f"❌ 数据加载失败: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("🎉 所有基础功能测试通过！可以开始训练。")
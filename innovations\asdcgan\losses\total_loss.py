"""
总损失管理器

统一管理ASDCGAN的所有损失函数，支持自适应权重调整和损失监控。

核心功能：
1. 损失函数统一管理
2. 自适应权重调整
3. 损失监控和可视化
4. 训练策略协调

技术特点：
- 模块化损失管理
- 自适应权重平衡
- 实时损失监控
- 灵活的训练策略
"""

import tensorflow as tf
from tensorflow.keras.losses import Loss
import numpy as np
from typing import Dict, List, Optional

from .cycle_consistency_loss import CycleConsistencyLoss
from .semantic_distance_loss import SemanticDistanceLoss
from .uncertainty_loss import UncertaintyLoss
from .adversarial_loss import AdversarialLoss


class TotalLossManager:
    """
    总损失管理器
    
    统一管理和协调ASDCGAN的所有损失函数。
    """
    
    def __init__(self,
                 # 损失权重配置
                 adversarial_weight=1.0,
                 cycle_consistency_weight=10.0,
                 semantic_distance_weight=5.0,
                 uncertainty_weight=1.0,
                 
                 # 损失函数配置
                 lambda_gp=10.0,
                 cycle_adaptive_weights=True,
                 uncertainty_beta=1.0,
                 
                 # 自适应权重配置
                 adaptive_weights=True,
                 adaptation_rate=0.01,
                 warmup_epochs=5,
                 
                 **kwargs):
        """
        初始化总损失管理器
        
        Args:
            adversarial_weight: 对抗损失权重
            cycle_consistency_weight: 循环一致性损失权重
            semantic_distance_weight: 语义距离损失权重
            uncertainty_weight: 不确定性损失权重
            lambda_gp: 梯度惩罚权重
            cycle_adaptive_weights: 循环损失是否使用自适应权重
            uncertainty_beta: 不确定性损失的β参数
            adaptive_weights: 是否使用全局自适应权重
            adaptation_rate: 权重适应速率
            warmup_epochs: 预热轮次
        """
        
        # 保存配置
        self.adversarial_weight = adversarial_weight
        self.cycle_consistency_weight = cycle_consistency_weight
        self.semantic_distance_weight = semantic_distance_weight
        self.uncertainty_weight = uncertainty_weight
        
        self.adaptive_weights = adaptive_weights
        self.adaptation_rate = adaptation_rate
        self.warmup_epochs = warmup_epochs
        self.current_epoch = 0
        
        # 初始化各损失函数
        self.adversarial_loss = AdversarialLoss(
            lambda_gp=lambda_gp,
            adaptive_weights=adaptive_weights
        )
        
        self.cycle_consistency_loss = CycleConsistencyLoss(
            adaptive_weights=cycle_adaptive_weights
        )
        
        self.semantic_distance_loss = SemanticDistanceLoss()
        
        self.uncertainty_loss = UncertaintyLoss(
            beta=uncertainty_beta
        )
        
        # 损失历史记录
        self.loss_history = {
            'adversarial': [],
            'cycle_consistency': [],
            'semantic_distance': [],
            'uncertainty': [],
            'total_generator': [],
            'total_discriminator': []
        }
        
        # 权重历史记录
        self.weight_history = {
            'adversarial_weight': [],
            'cycle_consistency_weight': [],
            'semantic_distance_weight': [],
            'uncertainty_weight': []
        }
    
    def set_epoch(self, epoch):
        """设置当前训练轮次"""
        self.current_epoch = epoch
        
        # 如果循环损失支持渐进式训练，设置轮次
        if hasattr(self.cycle_consistency_loss, 'set_epoch'):
            self.cycle_consistency_loss.set_epoch(epoch)
    
    def compute_generator_loss(self, y_true, y_pred):
        """
        计算生成器总损失
        
        Args:
            y_true: 真实数据字典
            y_pred: 预测数据字典
            
        Returns:
            generator_loss_result: {
                'total_loss': 总损失,
                'loss_components': 各组件损失,
                'weights': 当前权重
            }
        """
        loss_components = {}
        total_loss = 0.0
        
        # 1. 对抗损失 (生成器)
        if 'discriminator_outputs' in y_pred:
            adv_loss = self.adversarial_loss.compute_generator_loss(
                y_pred['discriminator_outputs']
            )
            loss_components['adversarial'] = adv_loss
            total_loss += self.adversarial_weight * adv_loss
        
        # 2. 循环一致性损失
        if self._has_cycle_data(y_true, y_pred):
            cycle_loss = self.cycle_consistency_loss(
                self._extract_cycle_data(y_true, y_pred)
            )
            loss_components['cycle_consistency'] = cycle_loss
            total_loss += self.cycle_consistency_weight * cycle_loss
        
        # 3. 语义距离损失
        if self._has_semantic_data(y_true, y_pred):
            semantic_loss = self.semantic_distance_loss(
                self._extract_semantic_data(y_true, y_pred)
            )
            loss_components['semantic_distance'] = semantic_loss
            total_loss += self.semantic_distance_weight * semantic_loss
        
        # 4. 不确定性损失
        if self._has_uncertainty_data(y_true, y_pred):
            uncertainty_loss = self.uncertainty_loss(
                self._extract_uncertainty_data(y_true, y_pred)
            )
            loss_components['uncertainty'] = uncertainty_loss
            total_loss += self.uncertainty_weight * uncertainty_loss
        
        # 5. 记录损失历史
        self._record_loss_history(loss_components, total_loss, 'generator')
        
        # 6. 自适应权重调整
        if self.adaptive_weights and self.current_epoch >= self.warmup_epochs:
            self._update_adaptive_weights(loss_components)
        
        return {
            'total_loss': total_loss,
            'loss_components': loss_components,
            'weights': self._get_current_weights()
        }
    
    def compute_discriminator_loss(self, real_samples, fake_samples, discriminator_outputs, discriminator_model):
        """
        计算判别器总损失
        
        Args:
            real_samples: 真实样本
            fake_samples: 生成样本
            discriminator_outputs: 判别器输出
            discriminator_model: 判别器模型
            
        Returns:
            discriminator_loss_result: {
                'total_loss': 总损失,
                'loss_components': 各组件损失
            }
        """
        # 判别器主要使用对抗损失
        d_loss = self.adversarial_loss.compute_discriminator_loss(
            real_samples, fake_samples, discriminator_outputs, discriminator_model
        )
        
        loss_components = {'adversarial': d_loss}
        
        # 记录损失历史
        self._record_loss_history(loss_components, d_loss, 'discriminator')
        
        return {
            'total_loss': d_loss,
            'loss_components': loss_components
        }
    
    def _has_cycle_data(self, y_true, y_pred):
        """检查是否有循环一致性数据"""
        required_true = ['real_features', 'real_attributes']
        required_pred = ['reconstructed_features', 'predicted_attributes']
        
        return (all(key in y_true for key in required_true) and
                all(key in y_pred for key in required_pred))
    
    def _extract_cycle_data(self, y_true, y_pred):
        """提取循环一致性数据"""
        cycle_true = {
            'real_features': y_true['real_features'],
            'real_attributes': y_true['real_attributes'],
            'original_semantic_distance': y_true.get('original_semantic_distance', 
                                                   tf.zeros((tf.shape(y_true['real_features'])[0], 1)))
        }
        
        cycle_pred = {
            'reconstructed_features': y_pred['reconstructed_features'],
            'predicted_attributes': y_pred['predicted_attributes'],
            'reconstructed_semantic_distance': y_pred.get('reconstructed_semantic_distance',
                                                        tf.zeros((tf.shape(y_pred['reconstructed_features'])[0], 1)))
        }
        
        return cycle_true, cycle_pred
    
    def _has_semantic_data(self, y_true, y_pred):
        """检查是否有语义距离数据"""
        required_true = ['real_attributes']
        required_pred = ['generated_features']
        
        return (all(key in y_true for key in required_true) and
                all(key in y_pred for key in required_pred))
    
    def _extract_semantic_data(self, y_true, y_pred):
        """提取语义距离数据"""
        semantic_true = {
            'original_semantic_distances': y_true.get('original_semantic_distances',
                                                    tf.zeros((tf.shape(y_true['real_attributes'])[0], 1))),
            'real_features': y_true.get('real_features'),
            'real_attributes': y_true['real_attributes']
        }
        
        semantic_pred = {
            'generated_semantic_distances': y_pred.get('generated_semantic_distances',
                                                     tf.zeros((tf.shape(y_pred['generated_features'])[0], 1))),
            'generated_features': y_pred['generated_features'],
            'semantic_distance_calculator': y_pred.get('semantic_distance_calculator')
        }
        
        return semantic_true, semantic_pred
    
    def _has_uncertainty_data(self, y_true, y_pred):
        """检查是否有不确定性数据"""
        return 'uncertainty' in y_pred or any(key in y_pred for key in ['mean', 'logvar'])
    
    def _extract_uncertainty_data(self, y_true, y_pred):
        """提取不确定性数据"""
        uncertainty_true = {
            'targets': y_true.get('targets'),
            'real_features': y_true.get('real_features')
        }
        
        uncertainty_pred = {
            'predictions': y_pred.get('predictions'),
            'mean': y_pred.get('mean'),
            'logvar': y_pred.get('logvar'),
            'aleatoric_uncertainty': y_pred.get('aleatoric_uncertainty'),
            'epistemic_uncertainty': y_pred.get('epistemic_uncertainty'),
            'total_uncertainty': y_pred.get('total_uncertainty'),
            'confidence_scores': y_pred.get('confidence_scores')
        }
        
        return uncertainty_true, uncertainty_pred
    
    def _record_loss_history(self, loss_components, total_loss, loss_type):
        """记录损失历史"""
        for key, value in loss_components.items():
            if key in self.loss_history:
                self.loss_history[key].append(float(value.numpy()))
                
                # 保持历史长度
                if len(self.loss_history[key]) > 1000:
                    self.loss_history[key] = self.loss_history[key][-1000:]
        
        # 记录总损失
        total_key = f'total_{loss_type}'
        if total_key in self.loss_history:
            self.loss_history[total_key].append(float(total_loss.numpy()))
            
            if len(self.loss_history[total_key]) > 1000:
                self.loss_history[total_key] = self.loss_history[total_key][-1000:]
    
    def _update_adaptive_weights(self, loss_components):
        """更新自适应权重"""
        if len(self.loss_history['adversarial']) < 10:
            return
        
        # 计算最近的平均损失
        recent_losses = {}
        for key in ['adversarial', 'cycle_consistency', 'semantic_distance', 'uncertainty']:
            if key in loss_components and key in self.loss_history:
                recent_losses[key] = np.mean(self.loss_history[key][-10:])
        
        if len(recent_losses) < 2:
            return
        
        # 计算相对权重
        total_loss = sum(recent_losses.values()) + 1e-8
        
        # 更新权重 (损失越大，权重越小)
        for key, avg_loss in recent_losses.items():
            weight_attr = f'{key}_weight'
            if hasattr(self, weight_attr):
                current_weight = getattr(self, weight_attr)
                new_weight = (total_loss - avg_loss) / total_loss
                
                # 平滑更新
                updated_weight = (
                    (1 - self.adaptation_rate) * current_weight +
                    self.adaptation_rate * new_weight
                )
                
                # 限制权重范围
                updated_weight = np.clip(updated_weight, 0.1, 10.0)
                
                setattr(self, weight_attr, updated_weight)
        
        # 记录权重历史
        self._record_weight_history()
    
    def _record_weight_history(self):
        """记录权重历史"""
        for key in self.weight_history:
            attr_name = key
            if hasattr(self, attr_name):
                self.weight_history[key].append(getattr(self, attr_name))
                
                # 保持历史长度
                if len(self.weight_history[key]) > 1000:
                    self.weight_history[key] = self.weight_history[key][-1000:]
    
    def _get_current_weights(self):
        """获取当前权重"""
        return {
            'adversarial_weight': self.adversarial_weight,
            'cycle_consistency_weight': self.cycle_consistency_weight,
            'semantic_distance_weight': self.semantic_distance_weight,
            'uncertainty_weight': self.uncertainty_weight
        }
    
    def get_loss_summary(self, window_size=100):
        """
        获取损失摘要统计
        
        Args:
            window_size: 统计窗口大小
            
        Returns:
            loss_summary: 损失统计摘要
        """
        summary = {}
        
        for key, history in self.loss_history.items():
            if len(history) > 0:
                recent_history = history[-window_size:] if len(history) >= window_size else history
                summary[key] = {
                    'mean': np.mean(recent_history),
                    'std': np.std(recent_history),
                    'min': np.min(recent_history),
                    'max': np.max(recent_history),
                    'latest': recent_history[-1] if recent_history else 0.0
                }
        
        return summary
    
    def reset_history(self):
        """重置损失历史"""
        for key in self.loss_history:
            self.loss_history[key] = []
        
        for key in self.weight_history:
            self.weight_history[key] = []

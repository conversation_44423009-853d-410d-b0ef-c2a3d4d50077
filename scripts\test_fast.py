from keras.models import load_model

import tensorflow as tf
import numpy as np
import random
from sklearn import preprocessing
from sklearn.svm import SVR
from sklearn.svm import SVC
from sklearn.metrics import confusion_matrix
from sklearn.metrics import accuracy_score
from sklearn.svm import SVC,LinearSVC
from sklearn.naive_bayes import GaussianNB

def scalar_stand(Train_X, Test_X):
    # 用训练集标准差标准化训练集以及测试集
    scalar_train = preprocessing.StandardScaler().fit(Train_X)
    Train_X = scalar_train.transform(Train_X)
    Test_X = scalar_train.transform(Test_X)
    return Train_X, Test_X

def feature_generation_and_diagnosis_fast(add_quantity, test_x, test_y, autoencoder, generator, classifier, test_class_indices):
    
    print(f"loading data...")
    print(f"test classes: {test_class_indices}")

    # 确定训练中未见的类别
    all_classes = np.arange(1, 16)
    seen_classes = np.setdiff1d(all_classes, test_class_indices)
    print(f"train classes: {seen_classes}")
    
    Labels_train = []
    Labels_test = []
    Generated_feature = []

    samples_per_class = len(test_x) // len(test_class_indices)
    
    for i, class_idx in enumerate(test_class_indices):
        
        # 动态获取属性向量
        attribute_index = i * samples_per_class
        attribute_vector = test_y[attribute_index]
        
        attribute = [attribute_vector for _ in range(add_quantity)]
        attribute = np.array(attribute)
     
        print(f"为类别 {class_idx} 生成 {add_quantity} 个特征, 属性形状: {attribute.shape}")
           
        noise_shape = (add_quantity, 50, 1)
        noise = tf.random.normal(shape=noise_shape)

        generated_feature = generator.predict([noise, attribute])
      
        Generated_feature.append(generated_feature)

        labels_train = np.full((add_quantity, 1), class_idx)
        # 确保测试标签与传入的类别索引匹配
        labels_test = np.full((samples_per_class, 1), class_idx)

        Labels_train.append(labels_train)
        Labels_test.append(labels_test)
    
    Generated_feature=np.array(Generated_feature).reshape(-1, 256)
    Labels_train=np.array(Labels_train).reshape(-1, 1)

    Labels_test=np.array(Labels_test).reshape(-1, 1)  
    test_feature, decoded_test= autoencoder(test_x)

    hidden_ouput_train,predict_attribute_train=classifier(Generated_feature)
    new_feature_train=np.concatenate((Generated_feature, hidden_ouput_train), axis=1)

    hidden_ouput_test,predict_attribute_test=classifier(test_feature)
    new_feature_test=np.concatenate((test_feature, hidden_ouput_test), axis=1)

    train_X=new_feature_train
    train_Y=Labels_train
    
    test_X=new_feature_test
    test_Y=Labels_test

    train_X,test_X=scalar_stand(train_X, test_X)

    print("开始快速评估 (仅使用最快分类器)...")

    # 只使用最快的分类器 - GaussianNB
    classifier_pnb = GaussianNB()
    print("训练GaussianNB...")
    classifier_pnb.fit(train_X, train_Y.ravel())
    Y_pred_pnb = classifier_pnb.predict(test_X)
    accuracy_pnb = accuracy_score(test_Y, Y_pred_pnb)
    print(f"GaussianNB完成，准确率: {accuracy_pnb:.4f}")

    print("快速评估完成！")
    
    # 返回相同格式但只有一个有效值
    return accuracy_pnb, accuracy_pnb, accuracy_pnb, accuracy_pnb
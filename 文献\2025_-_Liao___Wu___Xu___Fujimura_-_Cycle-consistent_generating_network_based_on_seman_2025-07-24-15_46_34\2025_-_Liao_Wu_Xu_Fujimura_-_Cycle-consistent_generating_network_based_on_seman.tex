\documentclass[10pt]{article}
\usepackage[verbose, a4paper, hmargin=2.5cm, vmargin=2.5cm]{geometry}

\usepackage{fontspec}
\usepackage{ctex}
\usepackage{paratype}


\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{esint}

\usepackage{graphicx}
\usepackage[export]{adjustbox}
\usepackage{mdframed}
\usepackage{booktabs,array,multirow}
\usepackage{adjustbox}
\usepackage{tabularx}
\usepackage{hyperref}
\hypersetup{colorlinks=true, linkcolor=blue, filecolor=magenta, urlcolor=cyan,}
\urlstyle{same}
\usepackage[most]{tcolorbox}
\definecolor{mygray}{RGB}{240,240,240}
\tcbset{
  colback=mygray,
  boxrule=0pt,
}
\graphicspath{ {./images/} }
\newcommand{\HRule}{\begin{center}\rule{0.9\linewidth}{0.2mm}\end{center}}
\newcommand{\customfootnote}[1]{
  \let\thefootnote\relax\footnotetext{#1}
}

\begin{document}
\section*{Cycle-Consistent Generating Network Based on Semantic Distance for Zero-Shot Fault Diagnosis}

Wenjie Liao \({}^{ \oplus  }\) , Like Wu \({}^{ \oplus  }\) , Shihui Xu \({}^{ \oplus  }\) , and Shigeru Fujimura \({}^{ \oplus  }\) , Member, IEEE

Abstract- In conventional data-driven fault diagnosis scenarios, it is often challenging to acquire sufficient training instances for every potential fault category. Because many target faults cannot be collected in advance, these gaps can constrain the effectiveness of existing diagnostic models. Zero-shot learning (ZSL) has gained prominence as a viable solution to this problem. However, it often encounters the issue of domain shift and insufficient learning of semantic space. In this study, a novel approach named the cycle-consistent generative adversarial network with semantic distance (CycleGAN-SD) is proposed for zero-shot fault diagnosis. By defining the semantic distance between different fault classes in attribute feature space, CycleGAN-SD facilitates the transformation of fault data from seen to unseen classes. To alleviate the issue that the generated features may deviate from real faults, an attribute regressor is introduced to construct the attribute-consistent loss. Besides, this method also introduces feature concatenation to build new training data and testing data. By merging the synthesized features, a more distinctive representation is achieved, thereby enhancing subsequent fault diagnosis. To validate the effectiveness of the proposed method, three fault diagnosis scenarios were investigated. Furthermore, the experimental results demonstrate that the proposed method surpasses other state-of-the-art zero-shot fault diagnosis techniques.

Index Terms- Attribute-consistency, cycle-consistent generative adversarial network (GAN), domain shift, semantic distance, zero-shot fault diagnosis.

\section*{I. INTRODUCTION}

INDUSTRIAL fault diagnosis, which pinpoints the exact cause of detected deviations from control, represents a long-standing and challenging issue in real-world industrial applications. Over the past few decades, data-driven methods have increasingly gained prominence in fault diagnosis [1], [2], and hence, a substantial body of works demonstrates exceptional performance and remarkable accuracy in this field. Typically, fault diagnosis based on data-driven method is treated as a problem of supervised classification, in which each fault type is categorized distinctly. Provided that there is ample historical data for specific faults, samples from the corresponding fault classes can be precisely diagnosed using typical classification methods, such as support vector machine (SVM) [3], deep neural networks [4], [5], [6], and expert systems [7]. However, in actual industrial processes, gathering adequate fault samples proves challenging due to high costs. Moreover, for some specific failures that could lead to significant damage, it is impermissible to operate an industrial process with faults merely to collect fault data.

The challenge outlined above is prevalent not only in the field of fault diagnosis but also in computer vision (CV). Addressing the issue of unseen data, Lampert et al. [8] introduced a zero-shot learning (ZSL) approach in CV. This method utilizes attributes such as size, color, and shape to identify objects from categories that have not been previously observed. Subsequent to this development, numerous ZSL methods have emerged, achieving notable results in image recognition. For example, Zhang et al. [9] solved the problem of unavailable video about certain activity classes in temporal activity detection using ZSL. Changpinyo et al. [10] proposed a synthesized classifier to learn unseen classes as a mixture of seen class proportions. While ZSL has rapidly advanced in image recognition, industrial faults cannot be characterized by simple attributes such as color and shape. Recently, a groundbreaking study by Feng and Zhao [11] introduced the use of professional knowledge-including the location, cause, and severity of faults-as attributes to bridge the divide between observed and unobserved industrial faults. This methodology, known as zero-shot diagnosis (ZSDs), enables practitioners to effectively diagnose unseen faults by utilizing models trained on known fault data. Following this, a series of methods [12], [13] have been developed to map fault data into semantic space, collectively enhancing the ZSD framework. Chen et al. [14] created an advanced deep attention relation network that leverages data from recognized classes to infer fault knowledge about unseen classes, achieving fault diagnosis across different conditions. In addition, some researchers have enhanced diagnostic accuracy by ensuring alignment between the distributions of fault features and attributes. For instance, Hu et al. [15] formulated a consistency loss function, utilizing the Barlow matrix to improve the performance of classification tasks.

Recently, data augmentation methods grounded in generative models have been extensively investigated, demonstrating promising results. Among the primary generative models, variational autoencoders (VAEs), autoregressive models, and generative adversarial networks (GANs) play a leading role. Notably, GAN architectures [16] have gained considerable attention in deep learning, particularly within CV due to their capability to generate highly realistic images. Since the inception of GAN, numerous derivative models have emerged, finding widespread applications across diverse fields. For example, addressing the mode collapse and convergence challenges often encountered in GAN training, Arjovsky et al. [17] replaced the Jensen-Shannon divergence with the Wasserstein distance, introducing the Wasserstein GAN (WGAN). To accelerate convergence, Gulrajani et al. [18] further refined WGAN by incorporating a gradient penalty (WGAN-GP). In addition, Zhu et al. [19] proposed cycle-consistent adversarial networks (CycleGANs) to facilitate unpaired image-to-image translation. Within the field of ZSL, GANs have also seen increased adoption due to their superior synthetic data generation capabilities [20]. For instance, Xian et al. [21] employed GAN to construct feature generating networks to generate convolutional neural network (CNN) features for unseen label figures. In the area of fault diagnosis, the GAN-based ZSL models have already been explored. The fundamental principle of this technique involves creating samples for unseen fault classes using attributes. Lv et al. [22] employed a conditional adversarial denoising autoencoder to create samples of unseen fault classes, utilizing mixed attributes as the conditioning factors. Zhuo and Ge [23] implemented triplet loss within traditional GAN frameworks to synthesize discriminative fault samples. In various datasets or scenarios, Yan et al. [24] pioneered a differentiable search strategy within GAN frameworks to achieve improved classification outcomes. Other generative models, such as variational autoencoders (VAEs), have also found successful applications in ZSL. For example, Schonfeld et al. [25] proposed a model that learns a shared latent space for image features and class embeddings via modality-specific aligned VAEs. In the field of fault diagnosis, Shao et al. [26] introduced variational autoencoder GAN with attribute regressor (VAEGAN-AR), which integrates VAE and GAN methodologies into an end-to-end framework for both feature extraction and generation.

\customfootnote{

Received 10 December 2024; revised 12 January 2025; accepted 3 February 2025. Date of publication 13 March 2025; date of current version 24 March 2025. The Associate Editor coordinating the review process was Dr. Zhenghua Chen. (Corresponding author: Like Wu.)

The authors are with the Graduate School of Information, Production and Systems, Waseda University, Fukuoka 808-0135, Japan (e-mail: <EMAIL>).

Digital Object Identifier 10.1109/TIM.2025.3545713

}

Overall, ZSL methods have achieved initial success in addressing fault diagnosis in situations where specific fault data for certain classes are lacking. However, several critical issues remain unresolved. A common problem is the bias displayed by models based on attribute embedding for ZSDs. These models often inaccurately predict the attributes of unseen class samples, showing a tendency toward the attributes of previously seen classes. This issue, known as domain shift [27], is also evident in generative-based zero-shot diagnostic models, where there is a noticeable discrepancy between the generated samples and their intended attributes. Moreover, current generative zero-shot fault diagnosis models endeavor to comprehend the distribution of original faulty samples from seen classes and subsequently introduce random noise into the networks to produce synthetic samples of unseen classes for fault diagnosis. However, because these models do not fully exploit the attribute space of different faults, their capability to differentiate between faults of seen and unseen classes is limited. Furthermore, the synthetic fault samples generated often lack authenticity, which can diminish the diagnostic accuracy.

To address the issues outlined above, this article introduces a zero-shot fault diagnosis model based on a cycle-consistent generating network. Specifically, it employs the cycle-consistent generative adversarial network (Cycle-GAN) to develop a novel approach: the cycle-consistent generative adversarial network with semantic distance (CycleGAN-SD). This approach can transfer faults from seen classes into faults of unseen classes. CycleGAN-SD manually selects the nearest fault category based on semantic distance from seen classes and segregates the training sets into source and target domains. Unlike previous GAN-based methods that generate fault samples through random noise, CycleGAN-SD executes fault transformations between these domains. Such transformations ensure that the generated samples not only exhibit characteristics of the unseen classes but also maintain the fundamental distribution of the authentic seen fault samples. Besides, to mitigate domain shift, the generated samples are input into a specially designed regressor that reconstructs the original fault attribute representation in this model. This reconstruction imposes a novel attribute-consistent constraint on the generator, enhancing its ability to learn the projection function from attribute space to sample feature space. Given the subtle differences among various fault attributes, constraints from the regressor help maintain consistency between the generated samples and the attribute representations. In summary, CycleGAN-SD leverages semantic distance to achieve transformations between different domains, producing high-quality fault samples of unseen classes for zero-shot fault diagnosis and mitigating the issue of domain shift commonly encountered in traditional generative zero-shot models. The significant contributions of this article are summarized as follows.

1) In addressing the challenges inherent in zero-shot fault diagnosis, this study proposes a generative model CycleGAN-SD, which can perform data-driven diagnosis tasks effectively, even when data for specific fault categories are absent. To avoid mode collapse and vanishing gradient, Wasserstein distance and gradient penalty are introduced in the proposed model.

2) By defining semantic distance within the attribute feature space, this study enables the transformation of faults between different domains, making generated fault samples of unseen classes more realistic. Furthermore, by incorporating an attribute-consistent constraint from the regressor, the model mitigates the risk of deviation in the features of generated samples of unseen classes from actual faults. Besides, the feature concatenation operation makes generated features more discriminative before fault diagnosis.

3) To enhance the relevance of this study to engineering applications, three different cases are employed to train the generative ZSL model presented herein. The experimental results have shown the effectiveness of the proposed method and its superiority among other state-of-the-art methods.

The structure of this article unfolds as follows. Section II delves into the relevant background of the proposed method. Section III clarifies the suggested model and its implementation process. We conducted experimental studies using the proposed approach recorded in Section IV. Section V offers a recapitulation of the entire study and sheds light on potential future directions.

\begin{center}
\includegraphics[max width=0.5\textwidth]{images/bo_d20u94f7aajc73bfqjhg_2_169_145_684_316_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 1. Comparison between traditional and zero-shot fault diagnosis models. (a) Traditional fault diagnosis. (b) Zero-shot fault diagnosis.

\section*{II. BACKGROUND}

\section*{A. Problem Definition}

For clarity, the definition of zero-shot fault diagnosis is described as follows. There is a subset of labeled fault samples available during training, which is denoted as \({T}_{s} =\)  \(\left\{  {\left( {{x}_{i}^{s},{y}_{i}^{s}}\right) ,i = 1,2,\ldots ,{N}_{s}}\right.\) , where \({x}_{i}^{s}\) represents a fault sample and \({y}_{i}^{s} \in  {Y}_{s} = \{ 1,2,\ldots ,S\) is the corresponding label named as seen classes. During the training phase, certain samples are unavailable, denoted as \({T}_{u} = \left\{  {\left( {{x}_{i}^{u},{y}_{i}^{u}}\right) ,i = 1,2,\ldots ,{N}_{u}}\right.\) , where \({x}_{i}^{u}\) represents unavailable samples and \({y}_{i}^{u}\) corresponds to unseen classes \({Y}_{u} = \{ S + 1,S + 2,\ldots ,S + U\) . It is important to note that the sets of seen classes and unseen classes are disjoint, denoted as \({Y}_{s} \cap  {Y}_{u} = \varnothing\) . Despite the lack of overlap, these two sets are linked through a common semantic space \(C\) , which acts as a knowledge bridge connecting seen and unseen classes. This shared semantic space, also referred to as the attribute space, is constructed using domain expertise. Each fault class \(y \in  {Y}_{s} \cup  {Y}_{u}\) is characterized by a corresponding fault attribute \(c\left( y\right)  \in  C\) . Therefore, the training dataset composed of seen samples can be represented as \({T}_{s} = \left\{  {\left( {{x}_{i}^{s},c\left( {y}_{i}^{s}\right) }\right) ,i = 1,2,\ldots ,{N}_{s}}\right\}\) . The primary objective of zero-shot fault diagnosis is to accurately predict the label \(y \in  {Y}_{u}\) for a given test sample \({D}_{\text{ test }} = {x}_{i}^{u}\) utilizing associated fault attribute. In the context of generative zero-shot fault diagnosis, it becomes essential to generate synthetic samples for unseen classes based on their semantic attributes. For better conceptual clarity, the comparison between traditional and zero-shot fault diagnosis is illustrated in Fig. 1.

\section*{B. Cycle-Consistent GAN}

The Cycle-GAN serves as a powerful mechanism for image transformation. It is distinguished by a cycle-consistency objective function, which enables the model to effortlessly convert images from a source domain to a target domain without the necessity for paired datasets. Essentially, Cycle-GAN consists of a recurrent network made up of two mirror-symmetric GANs, each equipped with its distinct generator and discriminator. There are unpaired datasets in two domains: source domain \(X : x \in  X\) and target domain \(Y : y \in  Y\) . In the beginning, the generator \(G\) transfers the sample \(x\) into \(G\left( x\right)\) in the \(Y\) domain from source domain \(X\) ; next, the discriminator \({D}_{Y}\) differentiates whether the input sample is real or generated by the generator. Ultimately, the generator \(F\) transfers \(G\left( x\right)\) return to sample \(F\left\lbrack  {G\left( x\right) }\right\rbrack\) to domain \(X\) . The mentioned process of the half architecture of Cycle-GAN is illustrated in Fig. 2. The adversarial loss of \(G\) and \({D}_{Y}\) is formulated as follows:

\[
{L}_{\text{ adv }}\left( {G,{D}_{Y}}\right)  = {E}_{y \sim  {P}_{\operatorname{data}\left( y\right) }}\left\lbrack  {\log {D}_{Y}\left( y\right) }\right\rbrack
\]

\[
+ {E}_{x \sim  {P}_{\operatorname{data}\left( x\right) }}\left\lbrack  {\log \left( {1 - {D}_{Y}\left( {G\left( x\right) }\right) }\right) }\right\rbrack   \tag{1}
\]

\begin{center}
\includegraphics[max width=0.5\textwidth]{images/bo_d20u94f7aajc73bfqjhg_2_922_148_726_275_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 2. Half architecture of Cycle-GAN.

where \({D}_{Y}\left( y\right)\) denotes the likelihood that \(y\) originates from the target domain \(Y,{E}_{y \sim  {P}_{\operatorname{data}\left( y\right) }}\) is the expectation of \(y\) in data distribution \({P}_{\operatorname{data}\left( y\right) }\) , and \({E}_{x \sim  {P}_{\operatorname{data}\left( x\right) }}\) is the expectation of \(x\) from the data distribution \({P}_{\text{ data }\left( x\right) }\) .

However, in prior research, notably [19], [28], it was observed that relying solely on adversarial losses does not guarantee the successful transformation of input from the source domain to the target output because all the samples of \(X\) could be transferred to the identical sample in \(Y\) . Therefore, the cycle-consistency loss is imported on the condition of \(F\left\lbrack  {G\left( x\right) }\right\rbrack   \approx  x\) , which is shown as follows:

\[
{L}_{\mathrm{{cyc}}}\left( {G,F}\right)  = {E}_{x \sim  {P}_{\operatorname{data}\left( x\right) }}\left\lbrack  {\parallel F\left( {G\left( x\right) }\right)  - x{\parallel }_{1}}\right\rbrack   \tag{2}
\]

where \(\parallel  \cdot  {\parallel }_{1}\) represents the L1 norm. The described process is the transformation from source domain \(X\) to target domain \(Y\) . The other transferring from \(Y\) to \(X\) is the reverse process of the forward translation.

\section*{C. Domain Shift}

Current ZSL methods generally focus on establishing a mapping between the sample space and the attribute space, facilitating the comparison of both seen and unseen samples within a unified representation. However, such approaches frequently encounter difficulties associated with domain shift [27]. Specifically, seen and unseen classes can be regarded as belonging to distinct domains. While overlapping may occur when attributes are shared across domains, discrepancies often arise as sample features tied to the same attributes may vary substantially due to inherent domain differences. As a result, when projections are learned only from seen classes, they tend to be biased or shifted to unseen classes. Since the model primarily learns to emphasize features of the seen classes, it struggles to generalize to unseen instances. Consequently, during the classification of unseen test samples, there is a high likelihood of misclassification, with unseen samples incorrectly identified as belonging to the seen classes, causing domain shift. In the context of generative zero-shot fault diagnosis, this challenge is even more pronounced. When the generator is trained solely on seen class samples, the synthesized data for unseen classes tend to deviate from the actual samples, as depicted in Fig. 3.

\begin{center}
\includegraphics[max width=0.5\textwidth]{images/bo_d20u94f7aajc73bfqjhg_3_148_150_723_226_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 3. Example of domain shift in generative zero-shot fault diagnosis problem.

\section*{III. METHOD}

This section presents the methodology proposed in this study. An overview of the approach is depicted in Fig. 4, with its specific components and processes outlined in the subsequent three steps.

1) Data Preprocess: After collecting various fault data from industrial settings, the dataset will be segmented into a training set and a testing set, both comprising fault samples and corresponding labels. For fault labels, an analysis is conducted to identify the characteristics of all potential fault classes by examining fault attributes. Once these attributes are obtained, they are input into a pretrained encoder to derive semantic features. Regarding the fault samples, the semantic distances between seen and unseen classes are calculated using semantic features. Subsequently, the nearest fault category is determined, and the training set is divided into source and target domains.

2) Model Training: A cycle-consistent generative model is trained to project samples from the source domain to the target domains along with the input of semantic distance. Concurrently, its mirror-symmetric counterpart projects samples from the target domain back to the source domain, ensuring fidelity and consistency in the bidirectional transformation process. In the training process, a regressor is introduced to ensure attribute consistency in the transformation of fault samples between different domains, as illustrated in Fig. 4.

3) Sample Generation and Feature Concatenation: The trained generator of the model is employed to synthesize fault samples of unseen classes. Subsequently, the generated samples of unseen classes are fed into the trained regressor, from which the outputs of the hidden layer are extracted and combined with the samples to derive the sample features. These new fault features exhibit enhanced discriminability in the feature space, as illustrated in Fig. 4, thereby improving the classification process. These features are then utilized as the new training dataset for classification models to identify faults in unseen classes.

\section*{A. Data Preprocessing}

This step is designed to convert fault labels into attribute representations based on attribute analysis, which serves as the foundation for ZSDs. Next, fault samples are partitioned into the source domain and target domain according to semantic distance calculations, thereby facilitating the subsequent cycle-consistent training. The specific procedure is outlined as follows. After constructing the training set and testing set from various fault samples collected in industrial scenes, attribute analysis is executed based on fault knowledge. In generative zero-shot fault diagnosis, since only fault samples of seen class are available, auxiliary information is needed to generate unseen fault features, i.e., fault attributes \(c = \left\lbrack  {{c}_{1},{c}_{2},\ldots ,{c}_{k}}\right\rbrack\) , \({c}_{i} = 1\) when it indicates the presence of the \(i\) th attribute, whereas \({c}_{i} = 0\) means that the attribute is missing. Then, a VAE is employed to extract the dense semantic representation of faults. The VAE, comprising two components, the encoder \(E\) and the decode \(D\) , is trained on original fault attributes. In the training process, the attribute \(c\) is input into the encoder, which maps it to a mean vector \(u\) and a logarithmic variance \(\log \left( {\sigma }^{2}\right)\) from a Gaussian distribution in the latent space. Utilizing the reparameterization trick, the model randomly samples a vector from a standard normal distribution, which it then scales using the learned mean and variance. Subsequently, the decoder reconstructs the original binary input from the latent representation. Both reconstruction loss and KL divergence loss are considered during the training process. The optimization function for the reconstruction loss is formulated as follows:

\[
\min {L}_{\text{ reconstruction }} = \operatorname{BCE}\left( {{c}_{o},{c}_{r}}\right)  \tag{3}
\]

where \({c}_{o}\) is the original attribute vector and \({c}_{r}\) represents the reconstructed attribute. BCE denotes the binary cross entropy and this function will encourage the VAE to accurately reconstruct the input from the latent space. To ensure that the latent space follows a normal distribution, the KL divergence loss is constructed as follows:

\[
\min {L}_{\mathrm{{KL}}} =  - \frac{1}{2}\sum \left( {1 + \log \left( {\sigma }^{2}\right)  - {u}^{2} - {\sigma }^{2}}\right)  \tag{4}
\]

where \({\sigma }^{2}\) represents the variance and \(u\) is the mean vector. After the training phase, the fault attributes are input into the trained encoder, from which the output mean vector is derived. This vector exactly represents the corresponding semantic features \({f}_{s}\) , which can be obtained by the following formula:

\[
{f}_{s}^{j} = {E}_{{\theta }_{E}}\left( {c}^{j}\right)  \tag{5}
\]

where \({c}^{j}\) represents the fault attribute for category \(j\) . After deriving the semantic features for each fault category, the semantic distances between faults of seen and unseen classes are computed, defined as \({f}_{s}^{1} - {f}_{s}^{2}\) . To facilitate the cycle-consistent training described in Step 2, it is necessary to manually select the nearest fault category in order to segregate the training set into source and target domains. Therefore, the mean semantic gap (MSG) is defined to measure the average distance value between each seen fault class and the unseen classes. The calculation is formulated as follows:

\[
{\mathrm{{MSG}}}_{i} = \frac{1}{N}\mathop{\sum }\limits_{{j = 1}}^{N}\left( {\begin{Vmatrix}{f}_{s}^{i} - {f}_{s}^{j}\end{Vmatrix}}_{1}\right)  \tag{6}
\]

where \({f}_{s}^{i}\) denotes the semantic feature of seen fault class \(i\) and \({f}_{s}^{j}\) is the semantic feature of unseen fault class \(j\) . The difference value is calculated by the L1 norm. Subsequently, the fault class exhibiting the smallest MSG among the seen classes is identified as the nearest fault category. Finally, the fault samples from this nearest category are designated as the source domain, while samples from other categories are classified as target domains for subsequent training.

\begin{center}
\includegraphics[max width=0.6\textwidth]{images/bo_d20u94f7aajc73bfqjhg_4_461_156_875_756_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 4. Flowchart of the proposed method.

For the incorporation of domain-specific knowledge into the training process, the sample features are also extracted by a stacked autoencoder. The real samples from seen classes \({x}_{i}^{s}\) are input into the encoder block, compressed to a feature vector \(\widetilde{x}\) . The optimization function of stacked autoencoder is as follows:

\[
{L}_{\mathrm{{SAE}}} = \operatorname{MSE}\left( {O\left( {E\left( {x}_{i}^{s}\right) }\right) \parallel {x}_{i}^{s}}\right)  \tag{7}
\]

where MSE represents the mean squared error loss, \(E\) is the encoder block of stacked AE, and \(O\) is the decoder block.

\section*{B. Model Training}

The objective of this step is to achieve cycle-consistent training for the proposed model and enable the generator to learn the transformation of fault samples between the source and target domains, thereby facilitating subsequent data augmentation for unseen fault classes. This study proposes a Cycle-GAN-based model tailored for zero-shot fault diagnosis. Similar to Cycle-GAN, the architecture of CycleGAN-SD includes two generators ( \(G\) and \(F\) ) and two discriminators \(\left( {D}_{x}\right.\) and \(\left. {D}_{y}\right)\) . Generator \(G\) is tasked with transferring samples from the source domain to the target domain, whereas \(F\) performs the reverse, converting samples from the target domain back to the source domain. Discriminators \({D}_{x}\) and \({D}_{y}\) serve to differentiate the authenticity of real and synthetic faults. To alleviate the issue of domain shift, this study introduces a regressor into the model. This regressor predicts the attributes of generated samples and imposes an attribute-consistent constraint on the generators, ensuring better alignment between generated samples and semantic features.

Given that CycleGAN-SD consists of two mirror-symmetric networks, the training process for one will be demonstrated as an example. The structure of half of the model is illustrated in Fig. 5. At the beginning of the training, the sample \(x\) from source domain \(S\) is input into generator \(G\) , accompanied by the semantic distance between \(x\) and \(y\) . This process facilitates the generation of the fault sample \({y}_{g}\) in the target domain \(T\) . Then, the generated sample \({y}_{g}\) will be fed into discriminator \({D}_{y}\) together with its corresponding attribute to output validity. Also, the real sample \(y\) from target domain \(T\) is input into \({D}_{y}\) . To reduce the risk of gradient disappearance and model collapse, the Wasserstein distance [18] and gradient penalty [18] are utilized to meet the Lipschitz constraint. The optimization function for the discriminator \({D}_{y}\) has the following form:

\[
\mathop{\min }\limits_{{D}_{y}}{L}_{\mathrm{{WGNA}}}^{{D}_{y}} = {E}_{x \sim  {P}_{S}}\left\lbrack  {{D}_{y}\left( {G\left( {x,{f}_{s}^{y} - {f}_{s}^{x}}\right) ,{c}_{y}}\right) }\right\rbrack
\]

\[
- {E}_{y \sim  {P}_{T}}\left\lbrack  {{D}_{y}\left( {y,{c}_{y}}\right) }\right\rbrack
\]

\[
+ \lambda {E}_{\widehat{s} \sim  {P}_{\widehat{s}}}\left\lbrack  {\left( {\begin{Vmatrix}{\nabla }_{\widehat{s}}\left( D\left( \widehat{s},{c}_{y}\right) \right) \end{Vmatrix}}_{2} - 1\right) }^{2}\right\rbrack   \tag{8}
\]

where \({P}_{S}\) and \({P}_{T}\) are the real data distribution for source domain and target domain, respectively, and \({f}_{s}^{y} - {f}_{s}^{x}\) denotes the semantic distance between sample \(x\) and \(y.{c}_{y}\) represents the corresponding attribute of \(y\) and \(\widehat{s}\) is a random sample that lies between the real sample \(y\) and generated sample \(G\left( {x,{f}_{s}^{y} - {f}_{s}^{x}}\right)\) typically computed as

\[
\widehat{s} = {\varepsilon y} + \left( {1 - \varepsilon }\right) G\left( {x,{f}_{s}^{y} - {f}_{s}^{x}}\right)  \tag{9}
\]

where \(\varepsilon\) is a random value in \(\left\lbrack  {0,1}\right\rbrack  .{\nabla }_{\widehat{s}}\left( {D\left( {\widehat{s},{c}_{y}}\right) }\right)\) denotes the gradient of the output of discriminator with respect to \(\widehat{s}\) and \(\parallel  \cdot  {\parallel }_{2}\) represents the L2 norm. \(\lambda\) is the weight factor to regulate the impact of gradient penalty.

\begin{center}
\includegraphics[max width=0.5\textwidth]{images/bo_d20u94f7aajc73bfqjhg_5_148_148_729_369_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 5. Half-framework of the proposed model.

For the generator, the adversarial loss is determined based on the discriminator's assessment of validity. The loss function is defined by the following equation:

\[
\mathop{\min }\limits_{G}{L}_{\text{ adv }} =  - {E}_{x \sim  {P}_{S}}\left\lbrack  {{D}_{y}\left( {G\left( {x,{f}_{s}^{y} - {f}_{s}^{x}}\right) ,{c}_{y}}\right) }\right\rbrack  . \tag{10}
\]

However, the generated features exhibit unpredictable shifts, mainly because the model has not been trained with samples of unseen classes. To alleviate the issue of domain shift, the generated samples \(G\left( {x,{f}_{s}^{y} - {f}_{s}^{x}}\right)\) is taken as input into an attribute regressor to reconstruct their fault attributes. Subsequently, the attributes predicted by the model are compared with the true attributes, compelling the generator to produce faults that align with the true attributes. The regressor parameterized by \({\theta }_{R}\) , denoted as \(R\left( \right)\) , is constructed using a fully connected network. The attribute-consistent loss function, which enforces this alignment, is formulated as follows:

\[
\mathop{\min }\limits_{G}{L}_{\mathrm{{ac}}} = \frac{1}{N}\mathop{\sum }\limits_{{i = 0}}^{N}\Delta \left( {R\left( {G\left( {x,{f}_{s}^{y} - {f}_{s}^{x}}\right) ;{\theta }_{R}}\right) ,{c}_{y}}\right)  \tag{11}
\]

where \(\Delta\) represents the loss incurred from the difference between the predicted attribute \(R\left( {G\left( {x,{f}_{s}^{y} - {f}_{s}^{x}}\right) ;{\theta }_{R}}\right)\) and the true attribute \({c}_{y}\) .

Meanwhile, the generated sample \(G\left( {x,{f}_{s}^{y} - {f}_{s}^{x}}\right)\) will be inputted into another generator \(F\) to transform it back to the source domain. The generated sample from \(F\) is denoted by \(F\left( {G\left( {x,{f}_{s}^{y} - {f}_{s}^{x}}\right) ,{f}_{s}^{x} - {f}_{s}^{y}}\right)\) , which obey \(F\left( {G\left( {x,{f}_{s}^{y} - }\right. }\right.\)  \(\left. {{f}_{s}^{x}),{f}_{s}^{x} - {f}_{s}^{y}}\right)  \approx  x\) . To improve the harmony between the generated sample and original \(x\) , the loss function for cycle consistency is formulated as

\[
\mathop{\min }\limits_{G}{L}_{\text{ cycle }} = {E}_{x \sim  {P}_{s}}\left\lbrack  {\begin{Vmatrix}F\left( G\left( x,{f}_{s}^{y} - {f}_{s}^{x}\right) ,{f}_{s}^{x} - {f}_{s}^{y}\right)  - x\end{Vmatrix}}_{1}\right\rbrack
\]

(12)

where \({f}_{s}^{x} - {f}_{s}^{y}\) represents the semantic distance between sample \(y\) and \(x\) . In the proposed CycleGAN-SD, the cycle-consistent loss imposes constraints on the shared latent space, ensuring that content from the source domain is preserved during the cycle-based restoration process. Essentially, cycle consistency compels the hidden representations to deviate from their initial distribution throughout the cycle-based restoration mapping, and it serves a crucial role in preventing degeneracy within adversarial learning contexts [29]. Specifically, in generative zero-shot fault diagnosis, it addresses the problem of generating fault samples that are uniformly of the same mode, thereby enhancing the diversity and realism of the synthetic samples.

TABLE I

NETWORK PARAMETERS OF CYCLEGAN-SD

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|}
\hline
Layer & Parameter \\
\cline{1-2}
\multicolumn{2}{|c|}{Variational autoencoder} \\
\cline{1-2}
Input & input \(\dim  =\) attribute \(\dim\) \\
\cline{1-2}
Fc1+LeakyReLU(0.2)+LN & output dim \(= {50}\) \\
\cline{1-2}
Fc2+LeakyReLU(0.2)+LN & output\_dim = mu\_dim, logvar\_dim \\
\cline{1-2}
Reparameterize(mu, logvar) & output dim \(= \mathrm{{mu}}\) dim \\
\cline{1-2}
Fc3+LeakyReLU(0.2)+LN & output dim \(= {50}\) \\
\cline{1-2}
Fc3+Sigmoid & output dim \(=\) attribute dim \\
\cline{1-2}
\multicolumn{2}{|c|}{\(\mathbf{{Discriminator}}\)} \\
\cline{1-2}
Input 1 & input \(\dim  =\) sample feature \(\dim\) \\
\cline{1-2}
Input\_2 & input \(\dim  =\) attribute \(\dim\) \\
\cline{1-2}
Concatenate(   ) & feature \(\mathrm{{dim}} +\) attribute \(\mathrm{{dim}}\) \\
\cline{1-2}
Fcl+LeakyReLU(0.2)+LN & output dim \(= {200}\) \\
\cline{1-2}
Fc2+LeakyReLU(0.2)+LN & output dim \(= {100}\) \\
\cline{1-2}
Fc3 & output\_dim = 1 \\
\cline{1-2}
\multicolumn{2}{|c|}{Generator} \\
\cline{1-2}
Input 1 & input \(\dim  =\) sample feature \(\dim\) \\
\cline{1-2}
Input 2 & input \(\dim  =\) semantic feature \(\dim\) \\
\cline{1-2}
Input 3 & input \(\dim  =\) semantic feature \(\dim\) \\
\cline{1-2}
Concatenate(Input 1, Input 2 - Input 3) & sample\_feature\_dim + semantic\_feature\_dim \\
\cline{1-2}
Fcl+LeakyReLU(0.2)+LN & output dim \(= {300}\) \\
\cline{1-2}
Fc2+LeakyReLU(0.2)+BN & output\_dim = sample\_feature\_dim \\
\cline{1-2}
\multicolumn{2}{|c|}{Regressor} \\
\cline{1-2}
Input & input dim = feature dim \\
\cline{1-2}
Fc1+LeakyReLU(0.2) & output dim \(= {100}\) \\
\cline{1-2}
Fc2+LeakyReLU(0.2) & output dim \(= {50}\) \\
\cline{1-2}
Fc3+Sigmoid & output dim \(=\) attribute dim \\
\cline{1-2}
\hline
\end{tabular}
}
\end{center}

Combined with the adversarial loss and attribute-consistent loss, the optimization function of generator is formulated as follows:

\[
\mathop{\min }\limits_{G}{L}_{\mathrm{{WGAN}}}^{G} = {L}_{\mathrm{{adv}}} + {\lambda }_{1}{L}_{\mathrm{{ac}}} + {\lambda }_{2}{L}_{\text{ cycle }} \tag{13}
\]

where \({\lambda }_{1}\) and \({\lambda }_{2}\) represent the weight factors that reregulate the impact of attribute-consistent loss and cycle-consistent loss. Note that the objective function pertains only to half of the entire architecture; the other half is its mirror representation.

By integrating constraints on adversarial loss, attribute-consistent loss, and cycle consistency loss within the training process, the model effectively transfers signals from the source domain to various unseen fault domains. The resulting generated samples are diverse, reflecting the characteristics of the unseen classes while preserving the fundamental distribution of the authentic seen fault samples. This strategy not only enhances the diversity of the fault samples but also significantly improves the diagnostic accuracy in zero-shot fault diagnosis. Table I displays the structure of each component in the model. "Fc" denotes the fully connected layer. LN and BN denote layer normalization and batch normalization, respectively.

\section*{C. Sample Generation and Feature Concatenation}

The goal of the final step is to generate discriminative fault features for unseen classes through the trained generator, followed by the final fault diagnosis. After the model is trained, samples from the source domain, along with their semantic distances, are fed into the trained generator to synthesize fault samples of unseen classes. To mitigate the issue of domain shift, a feature concatenation operation is performed on the generated samples prior to fault diagnosis. Unlike previous generative zero-shot fault diagnosis studies where attribute information is utilized only during training and often discarded at the classification stage, leading to generated samples inadequately associated with attributes, this study leverages the output of the last hidden layer \(a\) from the well-trained regressor \(R\) . This output, which contains more attribute information and less redundant feature information, is concatenated with the generated fault samples. This method effectively transforms the generated samples of unseen classes into newly discriminative fault features, enhancing the classification process.

\begin{center}
\includegraphics[max width=0.5\textwidth]{images/bo_d20u94f7aajc73bfqjhg_6_149_147_724_166_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 6. Flowchart of feature concatenation.

The concatenation process is illustrated in Fig. 6. Initially, the regressor is extracted from the integrated model, and the synthesized samples of unseen classes are input into it. Subsequently, the output from the last hidden layer \(a\) is extracted and concatenated with the fault samples to synthesize more discriminative fault features. The concatenation process is executed as follows:

\[
{x}_{c}^{u} = {x}^{u} \oplus  a \tag{14}
\]

where \(\oplus\) is the concatenation operation and \({x}_{c}^{u}\) represents the concatenated feature, which are utilized to train the fault diagnosis models. It is important to note that the concatenation operations are conducted after the entire model has been trained and before the classification model is trained. In the test process, the test samples are also transferred in the same way before prediction.

For the fault diagnosis, the linear support vector machine (LSVM), the nonlinear random forest (NRF), the probabilistic naive Bayes (PNB) [11], multilayer perceptron (MLP), and CNN are employed for label inference. All these classification models are implemented using scikit-learn.

\section*{IV. EXPERIMENTS AND RESULTS}

In this section, we assess the effectiveness of the proposed CycleGAN-SD using three distinct datasets. The first one is a toy data model of the three-phase transmission system (TPTS). The second dataset is the Tennessee Eastman process (TEP), and the third one is based on a real hydraulic system. Comparative experiments with other multiple current state-of-the-art methods are also conducted.

\section*{A. Case I: Toy Data Example}

1) Datasets' Description and Preprocess: Electrical faults can lead to significant damage. Accurate identification of the fault category is crucial for the safety and reliability of electrical power systems. However, running a faulty system to gather training data is impractical for power plants. Hence, the proposed zero-shot fault diagnosis method is highly valuable.

TABLE II

STATISTICS OF THE ELECTRICAL FAULT DATA

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|}
\hline
No. & Fault Type & Fault Description \\
\cline{1-3}
1 & Normal & No fault \\
\cline{1-3}
2 & LG & Fault between phase A and ground \\
\cline{1-3}
3 & LL & Fault between phase \(\mathrm{B}\) and \(\mathrm{C}\) \\
\cline{1-3}
4 & LLG & Fault between phase A, phase B and ground \\
\cline{1-3}
5 & LLL & Fault between phase A, phase B and phase C \\
\cline{1-3}
6 & LLLG & Three phase symmetrical fault \\
\cline{1-3}
\hline
\end{tabular}
}
\end{center}

TABLE III

ATTRIBUTES OF THE TPTS

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|}
\hline
No. & Attributes \\
\cline{1-2}
Att\#1 & Related to phase A \\
\cline{1-2}
Att\#2 & Related to phase B \\
\cline{1-2}
Att\#3 & Related to phase C \\
\cline{1-2}
Att\#4 & Related to ground \\
\cline{1-2}
\hline
\end{tabular}
}
\end{center}

\begin{center}
\includegraphics[max width=0.3\textwidth]{images/bo_d20u94f7aajc73bfqjhg_6_1091_861_386_276_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 7. Fault description matrix of TPTS dataset.

The TPTS [30] dataset comprises four \({11} - \mathrm{{kV}}\) generators situated at the terminus of the transmission line, designed to simulate various electrical fault samples. Both true and wrong circuits have been modeled to gather voltage and current measurements at the transmission line terminals. According to [15], the statistics of the electrical fault data are presented in Table II. The dataset includes six classes (five fault classes and one normal class), each with a distinct description detailing the fault attributes.

The descriptions of faults indicate their class information. In line with the " 0/1 " encoding used in ZSL, we use a list that outlines four characteristics of these fault categories to define their attributes. The attributes of the TPTS are detailed in Table III. By integrating the data from Tables II and III, the attribute vectors are formulated to build the semantic knowledge for Case I. Fig. 7 displays these attribute vectors. Each dimension of an attribute vector for a fault is determined by the presence ("1") or absence ("0") of a corresponding attribute. In accordance with ZSDs, where training and testing classes do not overlap, the normal class along with Faults 2, 5, and 6 are used for training, while Faults 3 and 4 are used for testing, with each class containing 1000 samples.

After determining the attribute for each fault class, the MSG for each seen class is calculated, as outlined in Section III. The fault category exhibiting the smallest MSG is selected as the nearest fault category. In this case, Class 5 is identified as the nearest fault category. Therefore, Class 5 is designated as the source domain, while the remaining fault classes are considered the target domain for subsequent model training. For experimental setup, we utilized a server equipped with an NVIDIA GeForce RTX 3080 with 10-GB VRAM, a 1 TB SSD, and running Ubuntu 20.04 LTS as the operating system. The software framework was developed using Python 3.7.

TABLE IV

ACCURACIES OF THE PROPOSED AND RELATED METHODS IN CASE I

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|c|c|c|c|c|}
\hline
\multirow{2}{*}{Classifier} & \multicolumn{7}{|c|}{Accuracy(\%)} \\
\cline{2-8}
 & FDAT[11] & SCE[15] & FAGAN[23] & SRWGAN[31] & VAEGAN- AR[26] & FREE[32] & CycleGAN- SD(ours) \\
\cline{1-8}
LSVM & 67.90 & 94.80 & 81.80 & 83.25 & 85.20 & 88.20 & 93.45 \\
\cline{1-8}
NRF & 69.60 & 90.40 & 86.55 & 79.05 & 79.50 & 81.75 & 88.70 \\
\cline{1-8}
PNB & 68.80 & 95.30 & 86.40 & 77.85 & 87.75 & 90.95 & 96.50 \\
\cline{1-8}
MLP & 65.15 & 87.05 & 82.95 & 71.10 & 81.95 & 78.65 & 89.05 \\
\cline{1-8}
CNN & 54.10 & 89.75 & 77.65 & 70.05 & 72.40 & 74.85 & 94.20 \\
\cline{1-8}
\hline
\end{tabular}
}
\end{center}

2) Model Training: Before model training, a network for feature extraction is trained on the seen class samples. The output vector of the network is used as its features. The data in the training set correspond to the sample's features. The training set has a shape of \(3 \times  {1000} \times  {512}\) . For the hyperparameter setting, a batch size of 64 was selected from the candidate set \(\{ {32},{48},{64},{96},{128}\}\) . The learning rate was set to 0.001, chosen from the range \(\{ {0.0001},{0.1}\}\) , and Adam was employed as the optimizer, given its relative stability and lower sensitivity to variations in learning rate. Then, the training dataset is input into the generative model that is designed, and the training is completed after the loss function of the model converges. After that, the fault samples from the source domain feed the well-trained generative model along with the semantic distance to generate the samples of the unseen classes, and 1000 samples are generated for each unseen class of faults. Next, the generated unseen class samples are used as input for well-trained regressor, and the feature concatenation is conducted according to the method described in Section III. Finally, the classifier is trained with the transformed dataset, and the well-trained classifier is used for fault diagnosis.

3) Fault Diagnosis: Table IV shows the diagnosis results under several different classifiers. It can be seen that the diagnostic accuracy of the proposed method by the five classification models exceeded 85\%, with the highest accuracy reaching \({96.50}\%\) by PNB. This performance significantly surpasses the baseline random guess accuracy of \({50}\%\) , thereby demonstrating the effectiveness of the proposed method in addressing the zero-shot fault diagnosis problem in Case I.

In addition, other current state-of-the-art zero-shot fault diagnosis methods are chosen for comparison, such as fault direct attributes prediction (FDAT) [11], semantic-consistent embedding (SCE) [15], fault auxiliary generative adversarial network (FAGAN) [23], model semantic refinement Wasserstein generative adversarial network (SRWGAN) [31], VAEGAN-AR [26], and feature refinement model for generalized ZSL (FREE) [32] where FDAT is based on attribute prediction, SCE is based on feature-attribute embedding, and the other methods and proposed method are based on generative networks. The experimental results of these methods are also recorded in Table IV. FDAT shows the lowest accuracy of \({69.60}\%\) achieved by NRF, suggesting that DAP-based approaches do not show superior performance in this ZSD case. SCE performed significantly better than FDAT, with an improved accuracy rate of \({95.30}\%\) using PNB. Generative models, such as FAGAN, SRWGAN, and VAEGAN-AR, obtain the highest accuracy of roughly \({85}\%\) . FREE achieved an accuracy rate of \({90.95}\%\) by PNB. Meanwhile, the method proposed in this article achieves a diagnosis accuracy of \({96.50}\%\) , which indicates the superiority of our method among these methods. The results also indicate that the PNB exhibits superior performance in this case. Among all the comparative approaches, SCE demonstrated performance most comparable to that of the proposed model. Notably, while SCE achieved slightly higher diagnostic accuracy than the proposed model when combined with LSVM and NRF, its performance with the remaining three classifiers was inferior. Moreover, the proposed model attained the highest overall accuracy of \({96.50}\%\) using PNB. Compared with other ZSL methods, the proposed model achieves significantly higher accuracy across various classifiers, primarily due to two factors. First, unlike conventional generative models, the fault features generated by the proposed CycleGAN-SD originate from real data, thereby enhancing the authenticity of the synthesized samples. Second, the inclusion of an attribute-consistent loss and the implementation of feature concatenation jointly alleviate domain shift, rendering the generated fault features more discriminative.

\section*{B. Case II: TEP}

1) Datasets' Description and Preprocess: The TEP dataset, developed by Eastman Chemical Company to simulate real-world chemical processes [33], is extensively utilized in fault diagnosis research. It has been a common benchmark in zero-shot fault diagnosis studies for validating methodological effectiveness. The TEP consists of five primary units: a chemical reactor, a condenser, a recycle compressor, a vapor/liquid separator, and a stripper.

The dataset encompasses 21 distinct fault modes, each comprising 480 samples. Every sample includes 52 variables, categorized into 22 continuous process measurements, 19 composition variables, and 11 manipulated variables. In accordance with the data partitioning method outlined in [11], the first 15 fault classes are selected for the zero-shot fault diagnosis experiments. The statistics of the 15 fault classes are shown in Table V and we define the semantic information using 20 specific attributes described in the TEP, as detailed in Table VI. Similar to Case I, the attribute vectors of the TEP are formulated by combining Tables V and VI, as illustrated in Fig. 8. The 15 fault classes are organized into five groups, with a training set comprising 12 classes and a testing set comprising three unseen classes for each group. The group setting is presented in Table VII.

Also, the MSG for each seen class is calculated after determining the attribute for fault classes. The fault category exhibiting the smallest MSG is selected as the source domain class for training. In Case II, different fault classes are identified as the nearest fault category for different groups, as shown in Table VII.

TABLE V

STATISTICS OF THE TEP

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|}
\hline
No. & Fault Type & Fault Description \\
\cline{1-3}
1 & Step & \(\mathrm{A}/\mathrm{C}\) feed ratio, \(\mathrm{B}\) composition constant \\
\cline{1-3}
2 & Step & B composition, A/C ratio constant (pipe 4) \\
\cline{1-3}
3 & Step & D feed temperature (pipe 2) \\
\cline{1-3}
4 & Step & Reactor cooling water inlet temperature \\
\cline{1-3}
5 & Step & Condenser cooling water inlet temperature \\
\cline{1-3}
6 & Step & A feed loss (pipe 1) \\
\cline{1-3}
7 & Step & C header pressure loss (pipe 4) \\
\cline{1-3}
8 & Random variation & \(\mathrm{A},\mathrm{\;B}\) , and \(\mathrm{C}\) feed composition (pipe 4) \\
\cline{1-3}
9 & Random variation & D feed temperature (pipe 2) \\
\cline{1-3}
10 & Random variation & C feed temperature (pipe 4) \\
\cline{1-3}
11 & Random variation & Reactor cooling water inlet temperature \\
\cline{1-3}
12 & Random variation & Condenser cooling water inlet temperature \\
\cline{1-3}
13 & Slow drift & Reaction kinetics \\
\cline{1-3}
14 & Sticking & Reactor cooling water valve \\
\cline{1-3}
15 & Sticking & Condenser cooling water valve \\
\cline{1-3}
\hline
\end{tabular}
}
\end{center}

TABLE VI

ATTRIBUTES OF THE TEP

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|}
\hline
No. & Attributes \\
\cline{1-2}
Att\#1 & A composition is changed \\
\cline{1-2}
Att\#2 & C composition is changed \\
\cline{1-2}
Att\#3 & \(\mathrm{A}/\mathrm{C}\) ratio is changed \\
\cline{1-2}
Att\#4 & B composition is changed \\
\cline{1-2}
Att\#5 & Related with pipe 4 \\
\cline{1-2}
Att\#6 & Temperature of \(\mathrm{D}\) is changed \\
\cline{1-2}
Att\#7 & Related with pipe 2 \\
\cline{1-2}
Att\#8 & Disturbance is step changing \\
\cline{1-2}
Att\#9 & Input is changed \\
\cline{1-2}
Att\#10 & Temperature of input is changed \\
\cline{1-2}
Att\#11 & Related to reactor \\
\cline{1-2}
Att\#12 & Temperature of cooling water is changed \\
\cline{1-2}
Att\#13 & Related to condenser \\
\cline{1-2}
Att\#14 & Related with pipe 1 \\
\cline{1-2}
Att\#15 & Disturbance is random varying \\
\cline{1-2}
Att\#16 & Model parameters are changed \\
\cline{1-2}
Att\#17 & Disturbance is slow drift \\
\cline{1-2}
Att\#18 & Related to cooling water \\
\cline{1-2}
Att\#19 & Related to value \\
\cline{1-2}
Att\#20 & Disturbance is sticking \\
\cline{1-2}
\hline
\end{tabular}
}
\end{center}

TABLE VII

GROUP SETTING OF ZERO-SHOT FAULT DIAGNOSIS FOR TEP

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|c|c|c|}
\hline
\multirow{2}{*}{Group} & \multicolumn{3}{|c|}{Training} & \multicolumn{2}{|c|}{Test} \\
\cline{2-6}
 & Seen classes & Source domain & Total & Unseen classes & Total \\
\cline{1-6}
A & \(2,3,4,5,7 - {13},{15}\) & Class 2 & 5760 & 1, 6, 14 & 1440 \\
\cline{1-6}
B & \(1,2,3,5,6,8,9,{11} - {15}\) & Class 3 & 5760 & 4, 7, 10 & 1440 \\
\cline{1-6}
C & \(1 - 7,9,{10},{13},{14},{15}\) & Class 9 & 5760 & 8, 11, 12 & 1440 \\
\cline{1-6}
D & \(1,4,6 - {15}\) & Class 7 & 5760 & 2,3,5 & 1440 \\
\cline{1-6}
E & 1-8, 10, 11, 12, 14 & Class 12 & 5760 & 9,13,15 & 1440 \\
\cline{1-6}
\hline
\end{tabular}
}
\end{center}

2) Model Training: Similar to Case I, the feature extractor is trained on the seen class samples, and then, the output vector of the network is used as its fault features. Therefore, the training set has a shape of \({12} \times  {480} \times  {256}\) in this case. In this case, the hyperparameters were configured in the same manner as in Case I, with a batch size of 64 and Adam serving as the optimizer at a learning rate of 0.001 . Then, the training dataset is input into the proposed model, and the training is finished until the loss function of the model converges. The value of the loss function of generator is recorded to characterize the performance of the model, as shown in Fig. 9. In Fig. 9, at the beginning, the generator loss decreases dramatically, and then, the loss curves ascend gradually with the increase of iteration. After 2000 iterations, the generator loss converges to around 0, which indicates that the model has been trained well and can be used for fault feature generation.

\begin{center}
\includegraphics[max width=0.4\textwidth]{images/bo_d20u94f7aajc73bfqjhg_8_978_149_614_776_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 8. Fault description matrix for TEP dataset.

\begin{center}
\includegraphics[max width=0.5\textwidth]{images/bo_d20u94f7aajc73bfqjhg_8_961_1004_643_418_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 9. Loss curve of the CycleGAN-SD model on the TEP dataset.

To assess the computational efficiency of the proposed model compared with other methods, actual runtime tests were conducted under the scenarios of Group A settings in this case. The running time of the proposed model and relative models is recorded in Table VIII. From the data presented in Table VIII, during training on the original dataset size, the proposed CycleGAN-SD required the least time, completing in 143.3 min. When the training dataset size was halved, the proposed model also demonstrated the shortest running time of \({83.9}\mathrm{\;{min}}\) . This improvement can be attributed to two main factors. First, incorporating the Wasserstein distance and gradient penalty stabilizes the training process and accelerates model convergence. Second, because the proposed CycleGAN-SD directly transforms fault samples from the source domain into the target domain-rather than generating them from random noise. Therefore, it benefits from a more straightforward and effective sample generation process.

TABLE VIII

RUNNING TIME OF THE PROPOSED AND RELATED METHODS

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|c|c|c|c|c|}
\hline
\multirow{2}{*}{Training size} & \multicolumn{7}{|c|}{Running Time(min)} \\
\cline{2-8}
 & FDAT[11] & SCE[15] & FAGAN[23] & SRWGAN[31] & VAEGAN- AR[26] & FREE[32] & CycleGAN- SD(ours) \\
\cline{1-8}
\({12} \times  {480} \times  {52}\) & 351.2 & 200.9 & 205.5 & 150.7 & 191.5 & 244.1 & 143.3 \\
\cline{1-8}
\({12} \times  {240} \times  {52}\) & 262.3 & 128.8 & 137.3 & 88.4 & 96.3 & 136.3 & 83.9 \\
\cline{1-8}
\hline
\end{tabular}
}
\end{center}

TABLE IX

ANALYSIS OF QUANTITATIVE METRICS FOR THE GENERATIVE-BASED ZERO-SHOT MODELS FOR TEP

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|c|c|c|c|}
\hline
Fault class & Metrics & FAGAN & SRWGAN & VAEGAN-AR & FREE & CycleGAN- SD (ours) \\
\cline{1-7}
\multirow{2}{*}{1} & PCC & 0.6921 & 0.5367 & 0.6075 & 0.5704 & 0.7701 \\
\cline{2-7}
 & CS & 0.7764 & 0.6188 & 0.7949 & 0.7060 & 0.8199 \\
\cline{1-7}
\multirow{2}{*}{6} & PCC & 0.7607 & 0.5212 & 0.6621 & 0.6133 & 0.7574 \\
\cline{2-7}
 & CS & 0.7729 & 0.4843 & 0.8707 & 0.5059 & 0.8847 \\
\cline{1-7}
\multirow{2}{*}{14} & PCC & 0.6551 & 0.6188 & 0.6990 & 0.5871 & 0.7632 \\
\cline{2-7}
 & CS & 0.7434 & 0.7902 & 0.7217 & 0.6470 & 0.8360 \\
\cline{1-7}
\hline
\end{tabular}
}
\end{center}

3) Sample Evaluation and Ablation Study: After model training, the fault samples from the source domain feed the well-trained generative model along with the semantic distance to generate the samples of the unseen classes, and 1000 samples are generated for each unseen class of faults. Next, the generated unseen class samples are used as input for the trained regressor and the feature concatenation is conducted according to the method described in Section III.

To evaluate the quality of the synthetic features, the Pearson correlation coefficient (PCC) and cosine similarity (CS) are utilized to quantitatively measure the resemblance between the generated and original fault features in the scenario of Group A setting. PCC assesses the correlation, with values above 0.5 indicating a significant correlation. CS, on the other hand, determines the similarity in data distribution by computing the cosine of the angle between two sample vectors. Both metrics scale from 0 to 1, where higher values denote greater similarity. According to Table IX, the PCC values of the proposed model exceed 0.75 for all fault classes, and the CS values are all above 0.8, confirming that the distribution of the generated data closely matches that of the original data. In the comparative experiments, the proposed model's generated samples generally outperform those of other generative-based methods in terms of the PCC metric, except in the case of FAGAN, whose PCC value is slightly higher for Class 6. Under the CS metric, the proposed model's generated samples consistently achieve the highest CS values across all fault classes. These findings suggest that the proposed model outperforms other generative-based approaches in producing high-quality fault sample features.

Moreover, the visualization analysis is conducted through the t-distributed stochastic neighbor embedding (t-SNE) based on Group E to demonstrate the comparison between generated features and real ones. Fig. 10(a) illustrates the comparative results, from which we can infer that the generated features closely approximate the real features, and the generated features from different classes are easily distinguished. Then, we remove the feature concatenation part and contrast the newly generated features with the original features illustrated in Fig. 10(b). From Fig. 10(b), it is evident that the divisibility of features between different classes has declined. This reduction in feature distinction complicates the task of the fault diagnosis classifier in differentiating features across various classes. Furthermore, the cycle-consistent loss and attribute-consistent constraint are removed. Afterward, the model is retrained, and the generated features are compared with real features, as depicted in Fig. 10(c). In Fig. 10(c), the significant shift between the generated features and real ones indicates that the model failed to learn the correspondence between the attribute distribution and feature distribution of the unseen classes. This failure is attributed to the domain shift described in the previous section. This observation confirms the conceptual effectiveness of the proposed method.

TABLE X

ACCURACIES OF THE PROPOSED METHODS FOR THE TEP DATASET

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|c|c|c|c|}
\hline
\multirow{2}{*}{Classifier} & \multicolumn{5}{|c|}{Accuracy (\%)} & \multirow{2}{*}{Average (\%)} \\
\cline{2-6}
 & Group A & Group B & Group C & Group D & Group E &  \\
\cline{1-7}
LSVM & 88.94 & 76.52 & 74.03 & 89.72 & 91.10 & 84.06 \\
\cline{1-7}
NRF & 77.50 & 68.82 & 64.51 & 59.10 & 78.82 & 69.75 \\
\cline{1-7}
PNB & 69.93 & 69.51 & 71.11 & 64.72 & 69.10 & 68.87 \\
\cline{1-7}
MLP & 82.43 & 76.60 & 74.44 & 71.04 & 70.76 & 75.05 \\
\cline{1-7}
CNN & 73.33 & 70.07 & 76.46 & 79.72 & 83.47 & 76.61 \\
\cline{1-7}
\hline
\end{tabular}
}
\end{center}

4) Fault Diagnosis: In this section, various classification models are trained with the transformed dataset, and the well-trained classifier is used for fault diagnosis.

a) Accuracy analysis: Table X shows the diagnosis results of the proposed CycleGAN-SD under five different classifiers. The optimal results of CycleGAN-SD are \({88.94}\%\) , \({76.60}\% ,{76.46}\% ,{89.72}\%\) , and 91.10\% using various classification models in Groups A-E, respectively. The highest average accuracy of the five groups achieved \({84.06}\%\) by LSVM. This result is much higher than the random guess accuracy of 33.33\%, proving the effectiveness of the method for zero-shot fault diagnosis in this case. The confusion matrix for the five groups of experiments using LSVM is represented in Fig. 11.

b) Comparison with other methods: In addition, the comparative experiments are executed with other zero-shot fault diagnosis methods. The fault diagnosis is conducted using the same five classifiers as in Case I. However, for clarity, only the results produced by each method's best-performing classifier are summarized in Table XI to facilitate straightforward comparison. Specifically, FDAT and SCE achieve their highest accuracy when employing PNB, SRWGAN utilizes LSVM, FREE uses CNN, and all remaining methods rely on MLP. In Table XI, it is observed that FDAT records the lowest average diagnostic accuracy, achieving \({68.06}\%\) . In contrast, SCE demonstrates markedly superior performance, attaining an average accuracy of 77.40\%. For the generative models, both FAGAN and SRWAN exhibit similar performance of approximately \({74}\%\) accuracy, while FREE achieved an accuracy of \({77.50}\%\) and VAEGAN-AR attained an average accuracy of \({81.61}\%\) . Meanwhile, the method proposed in this article obtained an average accuracy of \({84.06}\%\) . This overall improvement indicates the superiority of the proposed method compared to these existing methods. Upon comparison of the diagnostic accuracy between different groups, Groups A and E consistently recorded the highest accuracy, followed by Groups D and B, whereas Group C recorded the lowest accuracy rate across all the methods.

\begin{center}
\includegraphics[max width=1.0\textwidth]{images/bo_d20u94f7aajc73bfqjhg_10_133_144_1531_488_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 10. Visual analysis of generated features for the TEP dataset. (a) Complete model. (b) Model without feature concatenation. (c) Model without cycle-consistent loss and attribute-consistent constraints.

\begin{center}
\includegraphics[max width=1.0\textwidth]{images/bo_d20u94f7aajc73bfqjhg_10_137_728_1522_283_0.jpg}
\end{center}
\hspace*{3em} 

Fig. 11. Confusion matrix of five groups for the TEP dataset.

TABLE XI

ACCURACIES OF THE PROPOSED AND RELATED METHODS FOR THE TEP DATASET

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|c|c|c|c|}
\hline
\multirow{2}{*}{Method} & \multicolumn{5}{|c|}{Accuracy (\%)} & \multirow{2}{*}{Average (\%)} \\
\cline{2-6}
 & Group A & Group B & Group C & Group D & Group E &  \\
\cline{1-7}
FDAT & 80.28 & 62.50 & 58.96 & 71.11 & 67.43 & 68.06 \\
\cline{1-7}
SCE & 89.58 & 77.92 & 62.50 & 76.11 & 80.90 & 77.40 \\
\cline{1-7}
FAGAN & 83.75 & 79.03 & 63.61 & 72.92 & 70.14 & 73.89 \\
\cline{1-7}
SRWGAN & 81.11 & 71.04 & 65.42 & 75.00 & 79.24 & 74.36 \\
\cline{1-7}
VAEGAN-AR & 85.28 & 76.71 & 70.70 & 93.07 & 82.31 & 81.61 \\
\cline{1-7}
FREE & 83.40 & 76.04 & 73.33 & 80.42 & 74.31 & 77.50 \\
\cline{1-7}
CycleGAN-SD & 88.94 & 76.52 & 74.03 & 89.72 & 91.10 & 84.06 \\
\cline{1-7}
\hline
\end{tabular}
}
\end{center}

\section*{C. Case III: Hydraulic System}

1) Datasets' Description and Preprocess: To further evaluate the performance of the proposed approach, a real-world hydraulic system was employed [34]. This system consists of two primary circuits: an operational circuit responsible for the main hydraulic functions and a secondary circuit dedicated to cooling and filtration, both interconnected via a shared oil reservoir. The operational circuit features a primary pump that experiences cyclical load fluctuations, controlled by a proportional pressure relief valve. Various process sensors are installed to measure key parameters, such as pressure, flow rate, and temperature. In addition to physical sensors, three virtual sensors are utilized to estimate system metrics, including cooling efficiency, cooling power, and overall operational efficiency, derived from the physical sensor data. The sensors in the hydraulic system operate at sampling frequencies ranging from 100 to \(1\mathrm{\;{Hz}}\) . To harmonize these varying rates, a data flattening technique, similar to those described in previous studies [19], [31], was applied. This preprocessing resulted in a dataset comprising 2205 samples, each with 43680 dimensions. The dataset covers 144 distinct fault categories, determined by the combination of four attributes: cooler status (three possible states), valve condition (four states), internal pump leakage (three states), and hydraulic accumulator status (four states). These combinations result from the product of \(3 \times  4 \times  3 \times  4\) . After applying one-hot encoding, the total number of attributes expands to 14. Each fault category corresponds to a unique combination of these attribute values.

To assess the efficacy of the proposed methods within a hydraulic system, we established three distinct scenarios with varying numbers of unseen fault categories: 15, 25, and 30. In the training set, all fault categories other than those designated as unseen are considered seen categories. Also, the MSG for each seen class is calculated and the fault category exhibiting the smallest MSG is selected as the source domain class for training. In Case III, the hyperparameters were set in the same manner as in Cases I and II, with a batch size of 32 and Adam serving as the optimizer at a learning rate of 0.001 .

2) Comparison of Results With Other Methods: Table XII summarizes the performance of various methods employing different classifiers in the scenario with 15 unseen fault categories. From Table XII, it is evident that the proposed method outperforms all other ZSD approaches, regardless of which classifier is employed, and achieves the highest accuracy of 76.41\% using LSVM. In addition, Table XIII presents each method's best-performing classifier in other scenarios, revealing that all methods achieve higher accuracy than random guessing in every group setting. However, as the number of unseen categories increases from 15 to 30, the difficulty of the task rises, leading to a decrease in accuracy for most methods. Our proposed method achieves the best performance across different numbers of unseen categories. Notably, when 30 fault categories are excluded from training, the proposed method improves the average accuracy by \({9.83}\%\) compared to the current best-performing method. These results demonstrate that CycleGAN-SD excels in generating discriminative fault features for unseen fault types, thereby significantly enhancing zero-shot fault diagnosis performance.

TABLE XII

ACCURACIES OF THE PROPOSED AND RELATED METHODS ON 15 UNSEEN CLASSES IN CASE III

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|c|c|c|c|c|}
\hline
\multirow{2}{*}{Classifier} & \multicolumn{7}{|c|}{Accuracy(\%)} \\
\cline{2-8}
 & FDAT & SCE & FAGAN & SRWGAN & VAEGAN- AR & FREE & CycleGAN- SD(ours) \\
\cline{1-8}
LSVM & 48.55 & 68.20 & 40.02 & 44.05 & 63.35 & 70.55 & 76.41 \\
\cline{1-8}
NRF & 57.91 & 60.53 & 57.97 & 62.61 & 54.27 & 64.69 & 69.25 \\
\cline{1-8}
PNB & 52.67 & 58.72 & 52.31 & 57.19 & 66.16 & 50.40 & 68.93 \\
\cline{1-8}
MLP & 42.68 & 71.05 & 45.93 & 56.13 & 67.09 & 67.48 & 74.81 \\
\cline{1-8}
CNN & 43.87 & 51.07 & 56.81 & 69.39 & 58.00 & 63.37 & 72.27 \\
\cline{1-8}
\hline
\end{tabular}
}
\end{center}

TABLE XIII

ACCURACIES OF THE PROPOSED AND RELATED METHODS IN CASE III

\begin{center}
\adjustbox{max width=\textwidth}{
\begin{tabular}{|c|c|c|c|}
\hline
\multirow{2}{*}{Methods} & \multicolumn{3}{|c|}{Average accuracy (\%)} \\
\cline{2-4}
 & 15 & 25 & 30 \\
\cline{1-4}
FDAT & 57.91 & 41.42 & 40.46 \\
\cline{1-4}
SCE & 71.05 & 64.18 & 58.23 \\
\cline{1-4}
FAGAN & 57.97 & 48.08 & 44.44 \\
\cline{1-4}
SRWGAN & 69.39 & 56.60 & 55.28 \\
\cline{1-4}
VAEGAN-AR & 67.09 & 54.63 & 53.47 \\
\cline{1-4}
FREE & 70.55 & 59.74 & 52.81 \\
\cline{1-4}
CycleGAN-SD & 76.41 & 71.59 & 68.06 \\
\cline{1-4}
\hline
\end{tabular}
}
\end{center}

\section*{V. CONCLUSION}

In this article, we introduce a novel method named CycleGAN-SD, designed for data-driven zero-shot fault diagnosis. By defining semantic distances within the attribute feature space, CycleGAN-SD facilitates the transformation of faults across different domains, thereby enhancing the realism of generated fault samples for unseen classes. To mitigate the problem of domain shift, an attribute regressor is incorporated to establish an attribute-consistent loss. This loss ensures that the generator produces fault data that closely align with the true attributes in the feature space. Distinct from other generative methods, the proposed model specifically aims to transform fault samples from the nearest fault category of a seen class to unseen faults. Experimental results from three case studies demonstrate that although other state-of-the-art methods are applicable to zero-shot fault diagnosis, they do not perform as effectively as the proposed CycleGAN-SD, which exhibits reduced discrepancy between generated and real features. Besides, visualization analyses further underscore that the feature concatenation process can make the features more discriminative in feature space. This methodology opens up numerous promising avenues for future research, particularly in exploring semantic features and attribute consistency within generative ZSL. REFERENCES

[1] C. Chen et al., "Pseudo-label guided sparse deep belief network learning method for fault diagnosis of radar critical components," IEEE Trans. Instrum. Meas., vol. 72, pp. 1-12, 2023.

[2] H. Wang, T.-Y. Chai, J.-L. Ding, and M. Brown, "Data driven fault diagnosis and fault tolerant control: Some advances and possible new directions," Acta Autom. Sinica, vol. 35, no. 6, pp. 739-747, Aug. 2009.

[3] Z. Yin and J. Hou, "Recent advances on SVM based fault diagnosis and process monitoring in complicated industrial processes," Neurocomput-ing, vol. 174, pp. 643-650, Jan. 2016.

[4] X. Zhao, M. Jia, and Z. Liu, "Semisupervised deep sparse auto-encoder with local and nonlocal information for intelligent fault diagnosis of rotating machinery," IEEE Trans. Instrum. Meas., vol. 70, pp. 1-13, 2021.

[5] W. Du, P. Hu, H. Wang, X. Gong, and S. Wang, "Fault diagnosis of rotating machinery based on 1D-2D joint convolution neural network," IEEE Trans. Ind. Electron., vol. 70, no. 5, pp. 5277-5285, May 2023.

[6] J. Chen, T. Pan, Z. Zhou, and S. He, "An adversarial learning framework for zero-shot fault recognition of mechanical systems," in Proc. IEEE 17th Int. Conf. Ind. Inform., Jul. 2019, pp. 1275-1278.

[7] Y. Zhi-Ling, W. Bin, D. Xing-Hui, and L. Hao, "Expert system of fault diagnosis for gear box in wind turbine," Syst. Eng. Proc., vol. 4, pp. 189-195, Jul. 2012.

[8] C. H. Lampert, S. Harmeling, and H. Nickisch, "Attribute-based classification for zero-shot visual object categorization," IEEE Trans. Pattern Anal. Mach. Intell., vol. 36, no. 3, pp. 453-465, Mar. 2014.

[9] L. Zhang et al., "TN-ZSTAD: Transferable network for zero-shot temporal activity detection," IEEE Trans. Pattern Anal. Mach. Intell., vol. 45, no. 3, pp. 3848-3861, Mar. 2023.

[10] S. Changpinyo, W.-L. Chao, B. Gong, and F. Sha, "Synthesized classifiers for zero-shot learning," in Proc. IEEE Conf. Comput. Vis. Pattern Recognit. (CVPR), Jun. 2016, pp. 5327-5336.

[11] L. Feng and C. Zhao, "Fault description based attribute transfer for zero-sample industrial fault diagnosis," IEEE Trans. Ind. Informat., vol. 17, no. 3, pp. 1852-1862, Mar. 2021.

[12] X. Sun, J. Gu, M. Wang, Y. Meng, and H. Shi, "Wheel hub defects image recognition base on zero-shot learning," Appl. Sci., vol. 11, no. 4, p. 1529, Feb. 2021.

[13] X. Chen, C. Zhao, and J. Ding, "Pyramid-type zero-shot learning model with multi-granularity hierarchical attributes for industrial fault diagnosis," Rel. Eng. Syst. Saf., vol. 240, Dec. 2023, Art. no. 109591.

[14] Z. Chen, J. Wu, C. Deng, X. Wang, and Y. Wang, "Deep attention relation network: A zero-shot learning method for bearing fault diagnosis under unknown domains," IEEE Trans. Rel., vol. 72, no. 1, pp. 79-89, Mar. 2023.

[15] Z. Hu, H. Zhao, L. Yao, and J. Peng, "Semantic-consistent embedding for zero-shot fault diagnosis," IEEE Trans. Ind. Informat., vol. 19, no. 5, pp. 7022-7031, May 2023.

[16] I. J. Goodfellow et al., "Generative adversarial nets," in Proc. 27th Int. Conf. Neural Inf. Process. Syst. (NIPS), vol. 2, 2014, pp. 2672-2680. [Online]. Available: http://papers.nips.cc/paper/5423- generative-adversarial-nets.pdf

[17] M. Arjovsky, S. Chintala, and L. Bottou, "Wasserstein GAN," 2017, arXiv:1701.07875.

[18] I. Gulrajani, F. Ahmed, M. Arjovsky, V. Dumoulin, and A. Courville, "Improved training of Wasserstein GANs," in Proc. 31st Conf. Neural Inf. Process. Syst. (NIPS), vol. 30, Dec. 2017, pp. 5769-5779.

[19] J. -Y. Zhu, T. Park, P. Isola, and A. A. Efros, "Unpaired image-to-image translation using cycle-consistent adversarial networks," in Proc. IEEE Int. Conf. Comput. Vis. (ICCV). Venice, Italy, Venice, Italy, 2017, pp. 2242-2251.

[20] W. Wang, V. W. Zheng, H. Yu, and C. Miao, "A survey of zero-shot learning: Settings, methods, and applications," ACM Trans. Intell. Syst. Technol., vol. 10, no. 2, pp. 1-37, Mar. 2019.

[21] Y. Xian, T. Lorenz, B. Schiele, and Z. Akata, "Feature generating networks for zero-shot learning," in Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit., Jun. 2018, pp. 5542-5551.
\end{document}
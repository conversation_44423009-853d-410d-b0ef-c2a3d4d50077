
# B组损失函数权重再平衡实验运行脚本
# 使用tmux会话管理，支持断线重连

TIMESTAMP=$(date +%Y%m%d%H%M)
SESSION_NAME="train_B_${TIMESTAMP}"
LOG_DIR="/app/logs"

# 创建日志目录
mkdir -p $LOG_DIR

echo "🚀 启动B组损失函数权重再平衡实验"
echo "📅 时间戳: $TIMESTAMP"
echo "📺 Tmux会话: $SESSION_NAME"
echo "📁 日志目录: $LOG_DIR"

# 创建tmux会话并运行实验
tmux new-session -d -s $SESSION_NAME

# 在tmux会话中运行实验
tmux send-keys -t $SESSION_NAME "cd /app" Enter
tmux send-keys -t $SESSION_NAME "echo '🔥 开始B组实验训练...'" Enter
tmux send-keys -t $SESSION_NAME "python run_B_group_experiments.py --epochs 800 --experiment both 2>&1 | tee $LOG_DIR/acgan_triplet_B_${TIMESTAMP}.log" Enter

echo "✅ 实验已在tmux会话中启动"
echo "📋 查看会话: tmux list-sessions"
echo "🔗 连接会话: tmux attach-session -t $SESSION_NAME"
echo "📊 查看日志: tail -f $LOG_DIR/acgan_triplet_B_${TIMESTAMP}.log"
echo "🛑 停止实验: tmux kill-session -t $SESSION_NAME"


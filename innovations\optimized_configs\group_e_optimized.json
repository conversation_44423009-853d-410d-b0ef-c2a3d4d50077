{"model_config": {"feature_dim": 52, "attribute_dim": 20, "latent_dim": 50, "hidden_dims": [128, 256, 128]}, "training_config": {"batch_size": 56, "learning_rate_g": 0.00012, "learning_rate_d": 0.00025, "adversarial_weight": 0.6, "cycle_consistency_weight": 0.18, "semantic_distance_weight": 0.14, "uncertainty_weight": 0.15, "domain_selection_weight": 0.15, "gradient_penalty_weight": 10.0}, "group_info": {"group": "E", "test_classes": [9, 13, 15], "optimization_target": 75.0, "current_accuracy": 47.67}}